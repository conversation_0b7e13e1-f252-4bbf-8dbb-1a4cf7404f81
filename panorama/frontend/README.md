#### 项目启动

```bash
npm install
npm run dev
```

#### 项目打包

```bash
# 构建开发环境包
npm run build:dev

# 公司内容构建环境包
npm run prod

# 打包的预览
npm run preview

# jz现场构建环境包
npm run local
```

#### 项目环境

```bash
# 开发环境地址
http://192.168.101.228:10010/
```

#### 部署方式

```bash
# 1. 打包项目
npm run prod
# 2. 将打包好的文件放到服务器上
cd /usr/share/nginx/html/
# 3. 修改服务器上的配置文件
修改location下root目录 /usr/local/nginx/conf/nginx.conf
# 4. 重启服务器
进入目录：/usr/local/nginx/sbin
重启：nginx -s reload
```
