FROM node:16.18.0 as builder
# npm --version 8.19.2

WORKDIR /code

COPY . /code

RUN npm config set proxy null && npm config set https-proxy null
RUN npm config set registry https://registry.npm.taobao.org/
RUN npm install --force --loglevel verbose && npm run build:prod

FROM nginx

# Manager Info
MAINTAINER <EMAIL>

COPY --from=builder /code/dist  /usr/local/nginx/html/ 
COPY nginx.conf /etc/nginx/nginx.conf

# init
RUN echo 'ehco init OK'

              