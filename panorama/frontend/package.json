{"name": "lltz", "version": "4.4.0", "description": "全景", "author": "Geeksec", "scripts": {"dev": "vue-cli-service serve --mode dev-traffic", "prod": "vue-cli-service build --mode prod-traffic", "local": "vue-cli-service build --mode local-traffic", "prod:dev": "vue-cli-service build --mode dev-traffic", "preview": "npm run prod:dev && serve -s dist", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit"}, "dependencies": {"axios": "^0.27.2", "core-js": "^3.5.0", "dayjs": "^1.11.0", "element-ui": "^2.15.14", "insert-css": "^2.0.0", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "relation-graph": "2.2.6", "vue": "2.6.14", "vue-cron": "^1.0.9", "vue-echarts": "^6.0.2", "vue-json-viewer": "^2.2.22", "vue-router": "3.0.6", "vuex": "3.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/composition-api": "^1.4.9", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-transform-remove-console": "^6.9.4", "chalk": "2.4.2", "connect": "3.6.6", "eslint": "6.7.2", "eslint-plugin-vue": "6.2.2", "html-webpack-plugin": "3.2.0", "lib-flexible": "^0.3.2", "mockjs": "1.0.1-beta3", "px2rem-loader": "^0.1.9", "runjs": "4.3.2", "sass": "1.26.8", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "skeleton-loader": "^2.0.0", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.14"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "license": "MIT"}