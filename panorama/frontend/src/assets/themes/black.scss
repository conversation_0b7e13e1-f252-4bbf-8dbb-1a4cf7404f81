/* 改变主题色变量 */
$--color-primary: #3977CD;

// 与CSS原生变量映射
#app {
  // 文字颜色
  --TextMostColor: #3977cd; // 主要
  --TextMajorColor: #9999a1; // 重要
  --TextMainColor: #FFFFFF; // 重要
  --TextColor: #fff; // 主题颜色
  // 背景颜色
  --BackgroundMostColor: #2c2c35; // 主要
  --BackgroundMajorColor: #767684; // 重要
  --BackgroundColor: #3977cd; // 主题颜色
  --BackgroundColorRgba: hsl(213, 67%, 25%, 0.5); // 鼠标hover和选中效果
  --BackgroundColorDisabled: #464650; // 禁用
  --BackgroundColorResult: #0F0F13;
  --BackgroundColorEven: #0c1c2f; // 背景偶数行
  --BackgroundColorOdd: #0b2542; // 背景奇数行
  --BackgroundColorRgba: hsl(213, 67%, 25%, 0.5); // 选中颜色
  --BackgroundColorSwitch: #767684;
  --BackgroundColor0: #000; // 按钮背景色
  --BackgroundColorHover: hsl(218.8, 73.5%, 47.2%); // 按钮背景色
  // 边框颜色
  --BorderMostColor: #2c2c35; // 主要
  --BorderMajorColor: #767684; // 重要
  --BorderMiniColor: #0f0f13; // 次要
  --BorderThColor: #0a2c4c; // 表头边框
  --BorderButtonColor: rgba(255, 255, 255, 0.24);
  --BorderInputColor: rgba(255, 255, 255, .1); // 输入框背景色
}