/* 改变主题色变量 */
$--color-primary: #5562c8;

// 与CSS原生变量映射
#app {
  // 文字颜色
  --TextMostColor: rgb(31, 34, 37); // 主要
  --TextMajorColor: rgb(31, 34, 37); // 重要
  --TextMainColor: #FFFFFF; // 重要
  --TextColor: rgb(31, 34, 37); // 主题颜色
  // 背景颜色
  --BackgroundMostColor: #f8f9fd; // 主要
  --BackgroundMajorColor: #767684; // 重要
  --BackgroundColor: #5562c8; // 主题颜色
  --BackgroundColorRgba: rgba(247, 247, 250, .5); // 鼠标hover和选中效果
  --BackgroundColorDisabled: #f2f3f7; // 禁用
  --BackgroundColorResult: #FFFFFF;
  --BackgroundColorEven: rgba(250, 250, 252, 1); // 背景偶数行
  --BackgroundColorOdd: #FFFFFF; // 背景奇数行
  --BackgroundColorRgba: rgba(247, 247, 250, 0.5); // 选中颜色
  --BackgroundColorSwitch: #A1B5CE;
  --BackgroundColor0: #000; // 按钮背景色
  --BackgroundColorHover: hsl(208.4, 93.2, 71.8%); // 按钮背景色
  // 边框颜色
  --BorderMostColor: #2c2c35; // 主要
  --BorderMajorColor: #dee0e7; // 重要
  --BorderMiniColor: #dee0e7; // 次要
  --BorderThColor: rgba(239, 239, 245, 1); // 表头边框
  --BorderButtonColor: rgba(224, 224, 230);
  --BorderInputColor: rgba(255, 255, 255, 1); // 输入框背景色
}