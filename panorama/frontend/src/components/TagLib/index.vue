<template>
  <el-dialog :visible.sync="isShow" width="752px" append-to-body custom-class="tag-lib">
    <div v-loading="fullLoading" class="content">
      <ul v-if="isShowAside" class="content-l">
        <li
          v-for="(item, index) in listArr"
          :key="index"
          class="item"
          :class="{ active: activeIndex === index }"
          @click="itemClick(item, index)"
        >
          <div class="title">{{ item.label }}</div>
          <div class="number">{{ item.num }}</div>
        </li>
      </ul>
      <div class="content-r">
        <!-- 搜索条件 -->
        <div class="form">
          <el-form ref="formRef" :model="form" :inline="true" @submit.native.prevent>
            <el-form-item>
              <el-input
                v-model="form.tag_text"
                size="small"
                suffix-icon="el-icon-search"
                placeholder="搜索标签名"
                @change="handleSearch"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-dropdown
                ref="dropdownRef"
                size="small"
                split-button
                placement="bottom-end"
                @visible-change="changeVisible"
              >
                权重
                <el-dropdown-menu slot="dropdown">
                  <TagFilter v-show="flag" ref="tagFilter" @handleSave="handleSave" />
                </el-dropdown-menu>
              </el-dropdown>
            </el-form-item>
            <el-form-item>
              <el-button plain size="small" @click="handleSearch">
                搜索
              </el-button>
            </el-form-item>
            <!-- <el-form-item>
              <label for="id" class="sort" @click="handleSort">
                智能推荐
                <i
                  class="iconfont"
                  :class="sortDown ? 'icon-a-12_down' : 'icon-a-12_up'"
                ></i>
              </label>
            </el-form-item> -->
          </el-form>
        </div>
        <TagQuery v-if="hasQuery" :query-data="showQuery" />
        <!-- <TagRecommend v-if="!sortDown" title="最近选择" />
        <TagRecommend v-if="!sortDown" title="推荐标签" /> -->
        <!-- 切换 -->
        <div class="toggler">
          <div class="toggler-icon">
            <div
              v-for="(item, index) in tabs"
              :key="item.icon"
              class="iconfont"
              :class="[item.icon, toggleIndex === index ? 'active' : '']"
              @click="toggleType(item, index)"
            ></div>
          </div>
          <div class="page">
            <el-pagination
              v-if="!isList"
              :current-page="page.current_page"
              :page-sizes="sizes"
              :page-size="page.page_size"
              background
              :total="total"
              small
              layout="total,prev, pager, next"
              @current-change="handleCurrentChange"
            >
            </el-pagination>
          </div>
        </div>
        <!-- 标签 -->
        <div v-loading="loading" class="tag-content" :style="{ overflowY: !isList&&form.search_type==='all'?'hidden':'scroll'}">
          <div v-if="!isList&hasQuery" class="table-handle flex">
            <div class="radio">
              <el-radio-group v-model="form.search_type" @input="radioInput">
                <el-radio
                  v-for="(item, index) in radioData"
                  :key="index"
                  :label="item.label"
                >
                  {{ item.name }}
                </el-radio>
              </el-radio-group>
            </div>
          </div>
          <component
            :is="activeCom"
            :tag-list="tagsData"
            :is-active="form.search_type==='all'"
            @handleSelectionChange="handleSelectionChange"
            @tagSelect="tagSelect"
          ></component>
        </div>
        <!-- 操作按钮 -->
        <div slot="footer" class="footer">
          <div>
            <el-button
              size="small"
              icon="el-icon-plus"
              plain
              @click="tagAddVisible = true"
            >
              添加新标签
            </el-button>
          </div>
          <div>
            <el-button size="small" plain @click="isShow = false">
              关闭
            </el-button>
            <el-button size="small" plain @click="handleReset">
              重置
            </el-button>
            <el-button size="small" type="primary" @click="handleConfirm">
              确定
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <TagAdd v-model="tagAddVisible" @getList="handleSearch" />
  </el-dialog>
</template>

<script>
import mixins from "@/mixins";
import TagRecommend from "./component/TagRecommend.vue";
import TagList from "./component/TagList.vue";
import TagTable from "./component/TagTable.vue";
import TagFilter from "./component/TagFilter.vue";
import TagAdd from "./component/TagAdd.vue";
import TagQuery from "./component/TagQuery.vue";
import { getTagSearch } from "@/api/TagList";
import { tagsType, tagsAttribute, TAG_COLOR } from "./constans";

export default {
  name: "TagLib",
  components: {
    TagRecommend,
    TagList,
    TagTable,
    TagFilter,
    TagAdd,
    TagQuery,
  },
  mixins: [mixins],
  props: {
    // 编辑回显的标签
    activetags: {
      type: Array,
      default: () => [],
    },
    // 是否展示侧边栏
    isShowAside:{
      type: Boolean,
      default: true,
    },
    /**
     * 查询类型
     * @description 默认查询全部 9999
     * @params ip:0,域名3,会话6,证书4
     */
    tag_target_type:{
      type:Number,
      default: 9999
    }
  },
  data() {
    return {
      tabs: [
        {
          icon: "icon-a-16_card",
          name: "TagList",
        },
        {
          icon: "icon-a-16_list2",
          name: "TagTable",
        },
      ],
      activeIndex: 0,
      toggleIndex: 0,
      tagAddVisible: false,
      form: {
        tag_text: "", // 查询标签条件（标签名,如果不填传空字符串 可模糊查询）
        tag_target_type: "9999", // 标签类型id
        tag_attribute_id: null, //是否进行细分类查询
        search_type: "all",
        black_weight: [0, 100], // 黑名单权值范围
        white_weight: [0, 100], // 白名单权值范围
      },
      activeCom: "TagList",
      sortDown: true, // 是否智能推荐
      page: {
        current_page: 1,
        page_size: 10,
      },
      sizes: [10, 20, 30, 50],
      total: 0,
      radioData: [
        {
          label: 'list',
          name: "手动选择",
        },
        {
          label: "all",
          name: "全选",
        },
      ],
      showQuery: [], // 点击搜索后展示的条件
      loading: false,
      flag: false,
      targetArr: [],
      attributeArr: [],
      listArr: [],
      tagsData: [],
      fullLoading: false,
      selectTag: [], // 筛选的标签
      tags:[],
      hasQuery:false,
      selectionTags:[]
    };
  },
  computed: {
    // 是否为列表
    isList() {
      return this.activeCom === "TagList";
    },
  },
  watch: {
    isShow(val) {
      if (val) {
        this.getList();
        this.selectTag=JSON.parse(JSON.stringify(this.activetags));
      } else {
        // 关闭弹框
        this.clearReset();
        this.activeCom="TagList";
        this.form.search_type='all';
        this.activeIndex = 0;
        this.toggleIndex=0;
        this.tagsData = [];
      }
    },
  },
  methods: {
    // handleSort() {
    //   this.sortDown = !this.sortDown;
    // },
    // 切换展示方式
    toggleType(item, index) {
      this.toggleIndex = index;
      this.activeCom = item.name;
      this.clearReset();
      // this.selectTag=this.activetags;
      this.form.search_type = this.isList ? "all" : "list";
      this.getList();
    },
    changeVisible(val) {
      this.flag = val;
    },
    // 选择标签
    tagSelect(item) {
      item.effect = item.effect=== "light" ? "dark" : "light";
      if(item.effect==='dark'){
        this.selectTag.push(item);
      }else{
        this.selectTag=this.selectTag.filter(tag=>tag.tag_id!==item.tag_id);
      }
    },
    // 标签合并逻辑处理
    mergeTag(){
      this.tags=Array.from(new Set([...this.selectTag,...this.activetags]));
    },
    // 选择
    handleSelectionChange(val){
      this.selectionTags=val;
    },
    // 切换选项
    radioInput(val) {
      this.form.search_type = val;
      this.page.current_page = 1;
      this.getList();
    },
    itemClick(item, index) {
      if(item.tag_attribute_id===null){
        // 无细分类 传null值
        this.form.tag_attribute_id = null;
        this.form.tag_target_type = item.key||0;
      }else{
        this.form.tag_attribute_id = item.key;
        this.form.tag_target_type = '';
      }
      this.activeIndex = index;
      this.updateList();
    },
    // 更新列表
    updateList() {
      this.getList();
    },
    // 搜索
    handleSearch() {
      const { page, form } = this;
      page.current_page = 1;
      this.performSearch();
    },
    // 执行搜索
    performSearch() {
      this.searchQuery();
      this.isQuery();
      this.getList();
    },
    // 是否展示查询条件
    isQuery() {
      const { form } = this;
      const hasTagText = Boolean(form.tag_text);
      const areWeightsDefault = () => {
        return (
          JSON.stringify(form.black_weight) === JSON.stringify([0, 100]) &&
      JSON.stringify(form.white_weight) === JSON.stringify([0, 100])
        );
      };
      this.hasQuery=(hasTagText || !areWeightsDefault());
    },
    // 重置
    handleReset() {
      this.clearReset();
      this.page.current_page = 1;
      this.selectTag=[]; // 清空手动选择的标签
      this.getList();
    },
    // 清空
    clearReset() {
      Object.assign(this.form, {
        tag_text: "",
        black_weight: [0, 100],
        white_weight: [0, 100],
      });
      this.$refs.tagFilter.reset();
      this.showQuery = [];
      this.selectTag=[];
    },
    // 确定标签
    handleConfirm() {
      if(!this.isList&&this.form.search_type==='all'){
        this.tags=this.tagsData;
      }else{
        if(!this.isList){
          this.tags=[...this.selectionTags,...this.selectTag];
          let obj={};
          this.tags=this.tags.reduce((prev,cur) => {
            if(obj[cur.tag_id] == undefined){
              obj[cur.tag_id] = true && prev.push(cur);
            }
            return prev;
          },[]);
        }else{
          this.tags=this.selectTag;
        }
      }
      this.$emit("getTagValue", this.tags, (val) => val && (this.isShow = false));
    },
    handleCurrentChange(val) {
      this.page.current_page=val;
      this.getList();
    },
    // 获取标签列表数据
    async getList() {
      try {
        this.fullLoading = true;
        this.targetArr = [];
        this.attributeArr = [];
        this.listArr = [];
        const params = { ...this.form };
        if (!this.isList) {
          params.current_page = this.page.current_page;
          params.page_size = this.page.page_size;
        }
        // 详情只展示该类型的标签
        if(!this.isShowAside){
          params.tag_target_type=this.tag_target_type;
          params.tag_attribute_id=null;
        }
        const res = await getTagSearch(params);
        this.tagDetail = res.data;
        if (!this.isList){
          this.total=res.data.total;
        }
        this.handleTarget(res.data.count_map.target, tagsType, "targetArr");
        this.handleTarget(
          res.data.count_map.attribute,
          tagsAttribute,
          "attributeArr"
        );
        this.targetArr.unshift({
          label: "全部",
          key: 9999,
          num: res.data.count_map.ALL,
          tag_attribute_id: null,
        });
        this.listArr = this.targetArr.concat(this.attributeArr);
        res.data.tag_data.forEach((item) => {
          item.level = TAG_COLOR[item.tag_level];
          item.effect = "light";
        });
        this.tagsData = res.data.tag_data.sort((a, b) => a.level - b.level);
        this.activetags.forEach(item=>{
          item.effect="dark";
        });
        this.mergeTag();
        if (this.tags.length) {
          this.tagsData.forEach((item) => {
            this.tags.forEach((x) => {
              if (x.tag_id === item.tag_id) {
                item.effect = "dark";
              }
            });
          });
        }
        // 一次性展示100条
        this.fullLoading = false;
      } catch (error) {
        this.fullLoading = false;
      }
    },
    handleTarget(data, obj, arr) {
      for (const key in data) {
        if(key!=='无细分类'){
          this[arr].push({
            label: key,
            key: obj[key],
            num: data[key],
            tag_attribute_id: arr === "targetArr" ? null : "",
          });
        }
      }
    },
    handleSave(value) {
      if(value){
        let {bStart, bEnd,hStart, hEnd}=value;
        this.form.white_weight = [bStart, bEnd];
        this.form.black_weight = [hStart, hEnd];
      }
      this.flag = false;
    },
    // 查询条件参数
    searchQuery() {
      this.showQuery = [];
      if (this.form.tag_text) {
        this.showQuery.push({
          label: "搜索",
          content: this.form.tag_text,
        });
      }
      if (this.form.black_weight?.length) {
        this.showQuery.push({
          label: "黑名单权重",
          content: this.form.black_weight,
        });
      }
      if (this.form.white_weight?.length) {
        this.showQuery.push({
          label: "白名单权重",
          content: this.form.white_weight,
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./tag.scss";
::v-deep {
  .tag-lib {
    // overflow-y: auto;
    .el-dialog__body,
    .el-dialog__header {
      padding: 0;
      .el-icon-close {
        position: relative;
        z-index: 9999;
      }
    }
  }
  .content {
    height: 480px;
    display: flex;
    padding: 12px 0;
    &-l {
      height: 100%;
      width: 176px;
      background: #f7f8fa;
      padding: 0 8px;
      margin: 0;
      list-style: none;
      overflow-y: auto;
      .item {
        height: 30px;
        display: flex;
        line-height: 30px;
        padding-left: 12px;
        border-radius: 4px;
        .number {
          color: #9999a1;
          margin-left: 4px;
        }
        &:hover {
          cursor: pointer;
        }
      }
      .item.active {
        background: #e7f0fe;
        .title {
          color: #116ef9;
        }
      }
    }
    &-r {
      padding: 0 12px;
      height: 100%;
      position: relative;
      flex: 1;
      display: flex;
      flex-direction: column;
      .form {
        height: 40px;
        .el-form-item{
          margin-right: 8px;
        }
        .sort {
          color: #116ef9;
          font-weight: normal;
        }
      }
      .toggler {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 46px;
        .toggler-icon {
          display: flex;
          width: 64px;
          animation: all 1s;
          border-radius: 4px;
          background: #f2f3f7;
          padding: 4px;
          .iconfont {
            width: 26px;
            height: 26px;
            line-height: 26px;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            &.active {
              color: #116ef9;
              background: #fff;
            }
          }
        }
        .page {
          flex: 1;
          text-align: right;
        }
      }
      .tag-content {
        flex: 1;
        overflow-y: scroll;
        .table-handle {
          margin: 12px 0;
        }
      }
      .footer {
        background: #fff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        height: 56px;
      }
    }
  }
}
</style>
