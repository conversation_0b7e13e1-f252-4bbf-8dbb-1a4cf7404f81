<template>
  <el-table
    ref="multipleTable"
    :data="tagList"
    style="width: 100%"
    stripe
    :class="[isActive?'active':'']"
    :header-cell-style="headerStyle"
    @selection-change="handleSelectionChange"
  >
    <el-table-column align="center" type="selection" width="55"> </el-table-column>
    <el-table-column label="名称" prop="tag_text" show-overflow-tooltip> </el-table-column>
    <el-table-column prop="tag_explain" label="描述" show-overflow-tooltip> </el-table-column>
    <el-table-column prop="black_list" label="黑名单权重"> </el-table-column>
    <el-table-column prop="white_list" label="白名单权重"> </el-table-column>
  </el-table>
</template>

<script>
import mixins from '@/mixins';
export default {
  name: "TagTable",
  mixins: [mixins],
  props: {
    tagList: {
      type: Array,
      default: () => [],
    },
    isActive:{
      type:<PERSON>olean,
      default:false
    }
  },
  data() {
    return {
      multipleSelection: [],
    };
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.$emit('handleSelectionChange',val);
    },
  },
};
</script>

<style lang="scss" scoped>
.active::before {
  background-color: rgba(255, 255, 255, 0.8);
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 300px;
  display: block;
  z-index: 1;
  content: "已选中全部标签";
  line-height: 300px;
  text-align: center;
  font-weight: bold;
  overflow: hidden;
}
</style>
