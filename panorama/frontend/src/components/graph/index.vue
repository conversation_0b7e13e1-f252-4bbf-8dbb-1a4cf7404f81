<template>
  <div class="newgraph">
    <div ref="mainbox" class="graph-box">
      <SeeksRelationGraph
        ref="seeksRelationGraph"
        :options="themeOptions"
        @onLineClick="onLineClick"
      >
        <div slot="node" slot-scope="{ node }" class="slot-code">
          <el-popover placement="top" trigger="hover">
            <div class="title">{{ node.data.label }}</div>
            <div v-if="node.data.infoArr.length" class="content">
              <div
                v-for="(item, index) in node.data.infoArr"
                :key="index"
                class="content-item"
              >
                <div class="content-item-key">
                  {{ item.label }}：
                </div>
                <div class="content-item-value">
                  {{ item.value || "-" }}
                </div>
              </div>
            </div>
            <div slot="reference" class="node">
              <img :src="node.data.img" draggable="false" />
            </div>
            <div slot="reference" class="node-label">
              {{ node.data.label }}
            </div>
          </el-popover>
        </div>
        <template #graph-plug>
          <div class="btn">
            <div>
              <el-button
                size="mini"
                :disabled="currentIndex >= count"
                type="primary"
                @click="handlePreview"
              >
                扩展下一事件
              </el-button>
            </div>
            <div class="num">
              当前结果事件总数量：<span class="color">{{ currentIndex }}/{{ count }}</span>
            </div>
          </div>
          <RGMiniView
            width="320px"
            height="200px"
            position="tr"
            :options="themeOptions"
          />
        </template>
      </SeeksRelationGraph>
    </div>
    <edgeDialog v-model="edgeDialogVisible" :edge-detail="edgeDetail" />
  </div>
</template>

<script>
import SeeksRelationGraph from "relation-graph";
import { RGMiniView } from "relation-graph";
import { formatGraphData } from "./fmtData.js";
import edgeDialog from "./components/edgeDialog.vue";
import { getFirstRunsId, getNextId } from "@/api/manage";
export default {
  components: { SeeksRelationGraph, RGMiniView, edgeDialog },
  props: {
    runId: {
      type: String,
      required: true,
    },
    count: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      graphOptions: {
        // 这里可以参考"Graph 图谱"中的参数进行设置
        backgroundColor: "#0F0F13",
        defaultNodeBorderWidth: 2,
        defaultLineColor: "#9999A1",
        defaultLineShape: 1,
        defaultNodeFontColor: "#F7F8FA",
        defaultJunctionPoint: "border",
        isMoveByParentNode: false,
        hideNodeContentByZoom: false,
        allowShowMiniToolBar: true,
        allowShowDownloadButton: false, // 下载图上按钮
        disableNodeClickEffect: true, // 禁用节点默认的点击效果
        disableLineClickEffect: true, // 禁用线条默认的点击效果
        layout: {
          layoutName: "force",
        },
      },
      graphOptionsLight: {
        // 这里可以参考"Graph 图谱"中的参数进行设置
        backgroundColor: "#ffffff",
        defaultNodeBorderWidth: 2,
        defaultLineColor: "#9999A1",
        defaultLineShape: 1,
        defaultNodeFontColor: "#2C2C35",
        defaultJunctionPoint: "border",
        isMoveByParentNode: false,
        hideNodeContentByZoom: false,
        allowShowMiniToolBar: true,
        allowShowDownloadButton: false, // 下载图上按钮
        disableNodeClickEffect: true, // 禁用节点默认的点击效果
        disableLineClickEffect: true, // 禁用线条默认的点击效果
        layout: {
          layoutName: "force",
        },
      },
      currentIndex: 1,
      currentId: "",
      edgeDialogVisible: false,
      edgeDetail: {
        title: "",
        id: "",
        fromVertexId: "",
        endEventId: "",
        toVertexId: "",
        edgeType: "",
      },
    };
  },
  computed: {
    dicts() {
      return this.$store.state.user.dicts;
    },
    themeOptions() {
      return this.$store.state.user.theme
        ? this.graphOptionsLight
        : this.graphOptions;
    },
  },
  watch: {
    runId: {
      handler(val) {
        if (val) {
          this.init();
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 获取第一个事件
    async init() {
      try {
        const res = await getFirstRunsId(this.runId);
        this.currentId = res.data.id;
        this.$refs.seeksRelationGraph.setJsonData(
          formatGraphData(res.data.eventGraph),
          (val) => {
            // 关系图渲染完成时会调用
            let links = this.$refs.seeksRelationGraph.getLinks();
            for (let i = 0; i < links.length; i++) {
              let arr = [];
              for (let j = 0; j < links[i].relations.length; j++) {
                let t = links[i].relations[j];
                if (
                  arr.find(
                    (k) =>
                      k.to === t.to && k.from === t.from && k.label === t.label
                  )
                ) {
                  continue;
                }
                arr.push(t);
              }
              links[i].relations = arr;
            }
            this.loading = false;
          }
        );
      } catch (error) {
        console.log(error);
      }
    },
    // 显示下一个事件
    async handlePreview() {
      this.currentIndex++;
      const res = await getNextId(this.runId, this.currentId);
      this.currentId=res.data.id;
      this.$refs.seeksRelationGraph.appendJsonData(
        formatGraphData(res.data.eventGraph),
        (val) => {
          // 关系图渲染完成时会调用
          let links = this.$refs.seeksRelationGraph.getLinks();
          for (let i = 0; i < links.length; i++) {
            let arr = [];
            for (let j = 0; j < links[i].relations.length; j++) {
              let t = links[i].relations[j];
              if (
                arr.find(
                  (k) =>
                    k.to === t.to && k.from === t.from && k.label === t.label
                )
              ) {
                continue;
              }
              arr.push(t);
            }
            links[i].relations = arr;
          }
        }
      );
    },
    // 翻译info信息下的字段
    v_info_cn(type, key) {
      let { properties } = this.dicts.vertices[type];
      return properties.find((filed) => filed.key === key)?.name;
    },
    // 关系点击
    onLineClick(lineObject, linkObject, $event) {
      const { label, from, to, type } = lineObject.data;
      this.edgeDetail.id = this.runId;
      this.edgeDetail.title = label;
      this.edgeDetail.fromVertexId = from;
      this.edgeDetail.toVertexId = to;
      this.edgeDetail.edgeType = type;
      this.edgeDetail.endEventId = this.currentId;
      this.edgeDialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.newgraph {
  width: 100%;
  height: 100%;
  position: relative;
  background: #0F0F13;
  border-radius: 8px;

  .graph-box {
    width: 100%;
    height: 100%;
  }
}
::v-deep .rel-miniview {
  border: none;
  // background: var(--BackgroundMostColor);
  backdrop-filter: blur(10px);
  box-shadow: none;
  // position: fixed;
  // bottom: 100px !important;
  canvas {
    background: var(--BackgroundMostColor);
    border: none;
  }
  .rel-mv-visible-area {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid #116ef9;
  }
}
::v-deep .rel-node-shape-0:hover {
  box-shadow: none;
}

::v-deep .rel-node-shape-0 {
  width: auto;
  height: auto;
  padding: 0;
  background-color: transparent !important;

  .node {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #fff;
    border: 1px solid #9999a1;
    display: flex;
    justify-content: center;
    align-items: center;

    > img {
      width: 20px;
      height: 20px;
    }
  }

  .folder-node {
    width: 42px;
    height: 32px;

    > img {
      width: 100%;
      height: 100%;
    }
  }

  .labels-node {
    max-width: 230px;
    min-width: 72px;
    min-height: 36px;
    background: #ffffff;
    border: 1px dashed #dee0e7;
    border-radius: 4px;
    padding: 8px;
    padding-bottom: 4px;
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    cursor: pointer;

    > section {
      height: 20px;
      line-height: 20px;
      background: #eef0f5;
      border-radius: 2px;
      font-size: 12px;
      color: #2c2c35;
      margin-right: 4px;
      margin-bottom: 4px;
      padding: 0 4px;
    }

    .del {
      width: 24px;
      height: 24px;
      line-height: 24px;
      border-radius: 12px;
      background: #ffffff;
      border: 1px solid #dee0e7;
      display: none;
      position: absolute;
      right: -12px;
      top: -12px;

      > i {
        width: 100%;
        height: 100%;
        color: #000;
      }
    }

    .danger {
      background: #fce7e7;
      color: #a41818;
    }

    .warning {
      background: #f9eddf;
      color: #b76f1e;
    }

    .success {
      background: #e7f0fe;
      color: #1b428d;
    }
  }

  .labels-node:hover {
    .del {
      display: inline-block;
    }
  }

  .node-label {
    width: 80px;
    min-height: 20px;
    position: absolute;
    text-align: center;
    left: -10px;
    bottom: -14px;
    font-size: 12px;
    overflow: hidden;
    /*隐藏*/
    white-space: nowrap;
    /*不换行*/
    text-overflow: ellipsis;
    /* 超出部分省略号 */
  }
}
// .slot-code {
//   &:hover {
//     cursor: pointer;
//   }
// }
.btn {
  position: fixed;
  left: 12px;
  top: 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
  z-index: 9999;
  .num {
    color: var(--TextColor);
    margin-left: 12px;
    .color {
      color: var(--TextColor);
    }
  }
}
::v-deep .rel-toolbar {
  color: var(--TextColor);
  .c-current-zoom {
    color: var(--TextColor);
  }
}
.title {
  font-weight: bold;
  margin-bottom: 8px;
}
.content {
  &-item {
    display: flex;
    justify-content: space-between;
    line-height: 1.5rem;
    &-key {
      color: #888282;
      margin-right: 10px;
    }
    &-value {
      color: #000;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}


</style>
<style>
.graph-box .rel-link-peel:hover{
  cursor: pointer !important;
}
</style>
