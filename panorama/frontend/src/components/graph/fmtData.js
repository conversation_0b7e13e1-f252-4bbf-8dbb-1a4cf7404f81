import store from "@/store";

export function formatGraphData(data) {
  let nodes = data.vertex;
  if (data.label_vertex) {
    nodes = nodes.concat(data.label_vertex);
  }
  let links = data.edge || [];
  let contains = data.contains || [];
  // 节点数据匹配
  let tempNodes = [];
  let { vertices, edges } = store.state.user.dicts; // 节点类型
  nodes.forEach((i) => {
    let label = i.label || "N/A";
    let labels = i.labels || "";
    let vid = i.vid || "";
    let img;
    let disableDefaultClickEffect = false;
    img = node_img_fmt(i.type).icon.img;
    disableDefaultClickEffect = false;
    let infoArr = [];
    for (const key in i.v_info) {
      let value = i.v_info[key];
      infoArr.push({
        key,
        value,
        label: vertices[i.type].properties.find((item) => item.key === key)
          ?.name,
      });
    }
    let temp = {
      id: i.id,
      text: i.label,
      styleClass: "raw-node",
      borderWidth: "0",
      width: 40,
      height: 40,
      disableDefaultClickEffect: disableDefaultClickEffect,
      data: {
        id: i.id,
        label: label,
        hideLabel: label,
        type: i.type,
        img: img,
        labels: labels,
        vid: vid,
        identity: i.identity || "",
        infoArr: infoArr.filter((item) => item.label),
      },
    };
    tempNodes.push({
      ...temp,
    });
  });
  // 线条数据匹配
  let tempLinks = [];
  links.forEach((k) => {
    let label = edges[k.type]?.name || "";
    // 由于后端代码中from是关键字，前端需要取消_form字段
    if (!Object.keys(k).includes("from")) {
      k.from = k._from;
    }
    k.label = label;
    // 组装关系属性的数据

    let properties = edges[k.type]?.properties;
    let temp = {
      id: k.id || "",
      from: k.from,
      to: k.to,
      text: label,
      color: "#9999A1",
      fontColor: "#9999A1",
      data: k,
      x: 3,
      type: k.type,
    };
    let headerData = {};
    properties?.forEach((x) => {
      headerData[x.key] = x.name;
    });
    temp.data.headerData = headerData;
    temp.data.table = [{ uri: 2, host: 1 }];
    // console.log(headerData,'1');
    // 先获取关系的所有属性
    // temp.data.table = properties?.map((x) => {
    //   return {
    //     prop: x.key,
    //     value: k.properties?.find((y) => y.key === x.key)?.value,
    //     label: x.name,
    //   };
    // });
    tempLinks.push({
      ...temp,
    });
  });
  contains.forEach((k) => {
    let temp = {
      from: k.from,
      to: k.to,
      isHide: true,
    };
    tempLinks.push({
      ...temp,
    });
  });
  let send = {
    rootId: "a",
    nodes: tempNodes,
    links: tempLinks,
  };
  console.log(send, "send");

  return send;
}

function node_img_fmt(type) {
  switch (type) {
  // IP
  case 1:
    return {
      icon: {
        img: require("../../assets/manage/icon_IP.svg"),
      },
    };
    // 域名
  case 2:
    return {
      icon: {
        img: require("../../assets/manage/icon_DOMAIN.svg"),
      },
    };
    // 证书
  case 3:
    return {
      icon: {
        img: require("../../assets/manage/icon_CERT.svg"),
      },
    };
    // 企业
  case 4:
    return {
      icon: {
        img: require("../../assets/manage/icon_ORG.svg"),
      },
    };
    // UA
  case 5:
    return {
      icon: {
        img: require("../../assets/manage/icon_UA.svg"),
      },
    };
    // 操作设备
  case 6:
    return {
      icon: {
        img: require("../../assets/manage/icon_DEVICE.svg"),
      },
    };
    // 操作系统
  case 7:
    return {
      icon: {
        img: require("../../assets/manage/icon_OS.svg"),
      },
    };
    // APT组织
  case 8:
    return {
      icon: {
        img: require("../../assets/manage/icon_APT_GROUP.svg"),
      },
    };
    // 附带文件
  case 9:
    return {
      icon: {
        img: require("../../assets/manage/icon_ATTACH_FILE.svg"),
      },
    };
    // 攻击信息
  case 10:
    return {
      icon: {
        img: require("../../assets/manage/icon_ATTACK.svg"),
      },
    };
    // MAIL
  case 11:
    return {
      icon: {
        img: require("../../assets/manage/icon_MAIL.svg"),
      },
    };
    // 邮件
  case 12:
    return {
      icon: {
        img: require("../../assets/manage/icon_EMAIL.svg"),
      },
    };
    // 恶意家族
  case 13:
    return {
      icon: {
        img: require("../../assets/manage/icon_ATTACH_FILE.svg"),
      },
    };
  default:
    return {
      icon: {
        img: require("../../assets/manage/icon_EMPTY.svg"),
      },
    };
  }
}
