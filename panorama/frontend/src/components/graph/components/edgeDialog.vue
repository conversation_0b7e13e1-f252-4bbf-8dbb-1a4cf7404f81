<template>
  <el-dialog
    :title="edgeDetail.title"
    :visible.sync="isShow"
    style="width: 100%"
    stripe
    custom-class="custom"
    :width="isAttack ? '980px' : '400px'"
    destroy-on-close
  >
    <el-table
      v-if="isAttack"
      v-loading="loading"
      :header-cell-style="headerCellStyle"
      class="table"
      :data="table"
      style="width: 100%"
    >
      <el-table-column type="index" width="50" label="序号"> </el-table-column>
      <el-table-column
        v-for="(item, index) in columns"
        :key="index"
        show-overflow-tooltip
        :prop="item.key"
        :label="item.name.slice(0, 7)"
      >
        <template #default="{ row }">{{ row[item.key] || "-" }}</template>
      </el-table-column>
    </el-table>
    <div v-else>
      <div v-if="table.length" style="text-align: center; padding: 10px">
        暂无属性
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getEdgesEndEvent } from "@/api/manage";
export default {
  name: "EdgeDialog",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    edgeDetail: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      table: [],
      columns: [],
      isAttack: false,
      loading: false,
    };
  },
  computed: {
    headerCellStyle() {
      return this.$store.state.user.theme
        ? { background: "#e9ecf5", color: "#527ce2" }
        : { background: "#2C2C35", color: "#9999A1" };
    },
    isShow: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    isShow: {
      handler(val) {
        if (val) {
          this.getEdgesEndEvent();
        }
      },
      immediate: true,
    },
  },
  methods: {
    async getEdgesEndEvent() {
      try {
        this.loading = true;
        const { id, endEventId, fromVertexId, toVertexId, edgeType } =
          this.edgeDetail;
        this.isAttack = edgeType === 14;
        const res = await getEdgesEndEvent(
          id,
          endEventId,
          fromVertexId,
          toVertexId,
          edgeType
        );
        this.table = res.data.map((item) => item.edge_info);
        if (this.table?.length) {
          let { type } = res.data[0];
          this.columns = this.$store.state.user.dicts.edges[type].properties;
        }
        this.loading = false;
      } catch (error) {
        console.log(error);
        this.loading = false;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog {
    background: var(--BackgroundMostColor);
    border-radius: 8px;
    .el-dialog__header {
      padding: 16px 12px 0;
      .el-dialog__title {
        color: var(--TextColor);
        font-size: 18px;
        font-weight: 600;
      }
      .el-dialog__headerbtn {
        top: 12px;
      }
    }
    .el-dialog__body {
      padding: 0px 12px 6px;
      .el-loading-mask {
        background: rgba(57, 119, 205, 0.2) !important;
      }
      .el-table {
        margin: 16px 0;
        background: rgba(57, 119, 205, 0) !important;
        &::before {
          height: 0px;
        }
        th.is-leaf {
          border-bottom: 1px solid var(--BorderThColor);
        }
        .el-table__body-wrapper {
          .el-table__row {
            background: rgba(57, 119, 205, 0);
            .el-table__cell {
              border: none;
            }
            .cell {
              color: var(--TextColor);
            }
          }
        }
        .el-table__row--striped {
          td {
            background: var(--BackgroundColorStriped) !important;
          }
        }
        // 当鼠标移入每行不添加背景颜色
        .el-table__body {
          .el-table__row {
            transition: none !important;
          }
          tr:hover > td {
            background: transparent;
          }
        }
      }
    }
  }
}
</style>