<template>
  <div>
    <el-form
      ref="ruleForm"
      :model="ruleForm"
      :rules="formRules"
      label-position="top"
      :label-width="labelWidth ? labelWidth + 'px' : '180px'"
      class="demo-ruleForm"
      :disabled="isCheck"
      style
    >
      <div class="item_content">
        <el-row class="fixed flex" style="display: flex">
          <el-form-item label="执行间隔">
            <el-select
              v-model="ruleForm.interval"
              filterable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in options"
                :key="index"
                :label="item"
                :value="item"
              >
              </el-option>
            </el-select>
            <!-- <el-input-number v-model="ruleForm.interval" :precision="0" style="width:100%" :min="1" :controls="false" placeholder="请输入"></el-input-number> -->
          </el-form-item>
          <el-form-item style="width: 120px" label="执行计划">
            <el-select v-model="ruleForm.unit" @change="changeSelect">
              <el-option
                v-for="(item, index) in Object.keys(dicts.timeUnit)"
                :key="index"
                :label="dicts.timeUnit[item]"
                :value="item"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="fixed">
          <el-form-item label="分类" prop="type">
            <el-select
              v-model="ruleForm.type"
              style="width: 100%"
              placeholder="分类"
            >
              <el-option
                v-for="(item, index) in Object.keys(dicts.classType)"
                :key="index"
                :label="dicts.classType[item]"
                :value="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row class="field">
          <el-form-item label="参数:"></el-form-item>
        </el-row>
        <el-row class="field-prop">
          <el-col
            v-for="(item, index) in formData"
            :key="index"
            :span="item.span ? item.span : 8"
          >
            <!-- 输入框 -->
            <el-form-item
              v-if="item.type == 'input'"
              :label="item.label"
              label-position="left"
              :prop="item.prop"
            >
              <el-input
                :ref="'popoverRef_' + index"
                v-model="ruleForm[item.prop]"
                style="width: 100%"
                :value="item.defaultValue ? item.defaultValue : ''"
                :placeholder="item.placeholder"
                :maxlength="item.maxlength"
                :disabled="item.disabled || false"
                :rows="item.row ? item.row : 1"
              ></el-input>
            </el-form-item>
            <!-- 范围选择 -->
            <el-form-item
              v-if="item.type == 'array'"
              :label="item.label"
              label-position="left"
              :prop="item.prop"
              class="array"
            >
              <el-input
                v-if="ruleForm[item.prop]"
                v-model="ruleForm[item.prop][0]"
                :placeholder="item.placeholder"
                :maxlength="item.maxlength"
                style="width: 200px"
                :disabled="item.disabled || false"
                :rows="item.row ? item.row : 1"
              ></el-input>
              <el-input
                v-if="ruleForm[item.prop]"
                v-model="ruleForm[item.prop][1]"
                style="width: 200px"
                :placeholder="item.placeholder"
                :maxlength="item.maxlength"
                :disabled="item.disabled || false"
                :rows="item.row ? item.row : 1"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <slot name="customItem"></slot>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </div>
</template>
  
<script>
export default {
  name: "DynamicForm",
  props: {
    formData: {
      type: Array,
      default: () => [],
    },
    labelWidth: {
      type: Number,
      default: 140,
    },
    isCheck: {
      type: Boolean,
      default: false,
    },
    editRow: {
      type: Object,
      default: () => {},
    },
    modelDialogVisible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      ruleForm: {
        interval: "",
        unit: "",
      }, // 初始化为空对象，以便动态绑定属性
      formRules: {},
      input: "",
      expression: "",
      showCron: false,
      timeUnitData: {
        1: Array.from({ length: 12 }, (_, i) => (i + 1) * 5),
        2: 60,
        3: 23,
        4: 365,
      },
    };
  },
  computed: {
    dicts() {
      return this.$store.state.user.dicts;
    },
    options() {
      return this.timeUnitData[this.ruleForm.unit];
    },
  },
  watch: {
    editRow: {
      handler(val) {
        if (Object.keys(this.editRow).length) {
          this.ruleForm.interval = val.interval;
          this.ruleForm.unit = String(val.unit);
          this.ruleForm.type = String(val.type);
        }
      },
      deep: true,
      immediate: true,
    },
    modelDialogVisible(val) {
      this.$nextTick(() => {
        if (val && Object.keys(this.editRow).length) {
          this.ruleForm.interval = this.editRow.interval;
          this.ruleForm.unit = String(this.editRow.unit);
          this.ruleForm.type = String(this.editRow.type);
        }
      });
    },
  },
  methods: {
    /**
       考虑到很多内容应会根据select的变化而变化 所以这里添加了一个回调函数 直接在JSON中定义好函数名 
        然后就可以在组件中使用了 
       */
    selectChange(item, event) {
      if (item.cb) {
        item.cb(this.ruleForm[item.prop], event);
      }
      this.$emit("redraw", this.ruleForm[item.prop]);
    },
    initForm() {
      // 初始化表单数据
      const initialData = {};
      this.formData.forEach((item) => {
        if (item.defaultValue) {
          initialData[item.prop] = item.defaultValue;
        }
      });
      this.ruleForm = { ...this.ruleForm, ...initialData };
      // 生成校验规则
      this.generateFormRules();
    },
    generateFormRules() {
      const rules = {};
      this.formData.forEach((item) => {
        if (item.rules && item.rules.length > 0) {
          rules[item.prop] = item.rules.map((rulesItem) => {
            return {
              required: true,
              message: `${item.label}${rulesItem}`,
              trigger: "blur",
            };
          });
        }
      });
      this.formRules = { ...this.formRules, ...rules };
      this.$nextTick(() => {
        // 生成之后需要清空一下 否则会触发校验
        this.$refs["ruleForm"].clearValidate();
      });
    },
    // 确定
    submitForm() {
      let formData = {};
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) {
          return false;
        } else {
          formData = this.ruleForm;
        }
      });
      return formData;
    },
    // 重置
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    // 清空表单验证
    clearRule() {
      this.$refs.ruleForm.clearValidate();
    },
    changeSelect() {
      this.ruleForm.interval = "";
    },
  },
};
</script>
  
  <style scoped lang="scss">
.demo-ruleForm {
  padding: 6px 0 15px 0;
  font-weight: normal;
  ::v-deep label {
    font-weight: 500 !important;
  }
}
.tip_icon {
  position: absolute;
  right: -2%;
  top: -18%;
  background: #fff;
}
.item_btn {
  width: 10%;
  line-height: 40px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
}
::v-deep {
  .el-form-item {
    margin-top: 10px !important;
    margin-bottom: 10px;
  }
}
.array {
  ::v-deep .el-form-item__content {
    display: flex;
    justify-content: space-around;
  }
}
.fixed {
  ::v-deep {
    .el-form-item {
      margin: 0;
      .el-form-item__label {
        color: #2c2c35;
      }
    }
  }
}
.field {
  margin-top: 10px;
  ::v-deep {
    .el-form-item {
      height: 22px;
      margin: 0;
      .el-form-item__label {
        color: #9999a1;
        font-size: 16px;
        font-weight: normal;
      }
    }
  }
}
.field-prop {
  ::v-deep {
    .el-form-item {
      margin: 0;
      .el-form-item__label {
        color: #9999a1;
      }
    }
  }
}
.text {
  color: #3977cd;
  &:hover {
    cursor: pointer;
  }
}
::v-deep {
  .language {
    span {
      display: none;
    }
  }
  .bottom {
    .value {
      display: none;
    }
  }
}
.flex {
  display: flex;
  ::v-deep .el-form-item {
    .el-input__inner {
      text-align: left;
    }
    &:last-child {
      margin-left: 20px;
      .el-form-item__label {
        opacity: 0;
      }
    }
    &:first-child {
      flex: 1;
    }
  }
}
</style>
  
  