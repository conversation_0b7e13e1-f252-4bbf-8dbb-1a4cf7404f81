/**
 * 处理节点颜色和展示
 */
export const NODE_COlORS = {
  1: "233, 12, 12",
  2: "217, 115, 33",
  4: "255, 199, 0",
  5: "7, 212, 171",
  6: "17, 110, 249",
  7: "42, 241, 241",
  8: "203, 17, 249",
  9: "255, 244, 143",
  10: "15, 15, 19",
  11: "255, 202, 153",
  12: "255, 146, 146",
  13: "127, 250, 213",
};
export const formateNodeColor = (nodes) => {
  nodes.forEach((node) => {
    node.borderColor = `rgba(${NODE_COlORS[node.nodeType]}, 1)`;
    node.color = `rgba(${NODE_COlORS[node.nodeType]}, .1)`;
    node.selectColor = `rgba(${NODE_COlORS[node.nodeType]}, .5)`;
    node.backgroundColor = `rgba(${NODE_COlORS[node.nodeType]}, .1)`;
    node.hoverColor = `rgba(${NODE_COlORS[node.nodeType]}, .1)`;
  });
};
