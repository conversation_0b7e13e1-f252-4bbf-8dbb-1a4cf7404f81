<template>
  <div ref="myPage" class="screen" @click="isShowNodeMenuPanel = false">
    <RelationGraph
      v-if="Object.keys(relationData).length"
      ref="graphRef"
      :options="themeOptions"
      :on-contextmenu="showNodeMenus"
    >
      <!-- 工具栏 -->
      <template #graph-plug>
        <MyDemoPanel v-if="isEdit" width="380px" left="90px">
          <!-- <div class="c-title">操作说明：</div> -->
          <ul class="c-content">
            <li>1.左侧拖拽实体节点至画布;</li>
            <li>2.拖拽完毕后点击【关系】设定两个节点之间的关系;</li>
            <li>3.右键点击节点设置属性;</li>
            <li>4.右键点击关系设置属性;</li>
          </ul>
        </MyDemoPanel>
        <DragToCreateToolbar v-if="isEdit" />
        <div class="graph-plug-handle">
          <i v-if="isEdit" class="el-icon-search other" @click="save('query')">&nbsp;&nbsp;查询</i>
          <i v-if="isFefresh" class="el-icon-refresh other" @click="refresh">&nbsp;&nbsp;刷新</i>
          <i v-if="isEdit" class="el-icon-share other" @click="startAddLine">&nbsp;&nbsp;关系</i>
          <i v-if="isEdit" class="el-icon-document-add" @click="save('save')">&nbsp;&nbsp;保存</i>
          <i v-if="isEdit" class="el-icon-close" @click="close">&nbsp;&nbsp;关闭</i>
        </div>
      </template>
      <slot> </slot>
    </RelationGraph>
    <div
      v-show="isShowNodeMenuPanel"
      :style="{
        left: nodeMenuPanelPosition.x + 'px',
        top: nodeMenuPanelPosition.y + 'px',
      }"
      class="node-menu"
    >
      <div
        v-if="currentObjectType === 'node'"
        class="c-node-menu-item"
        @click="onNodeClick"
      >
        设置节点
      </div>
      <div
        v-if="currentObjectType === 'node'"
        class="c-node-menu-item"
        @click="deleteNode"
      >
        删除节点
      </div>
      <div
        v-if="currentObjectType === 'link'"
        class="c-node-menu-item"
        @click="onLineClick"
      >
        设置关系
      </div>
      <div
        v-if="currentObjectType === 'link'"
        class="c-node-menu-item"
        @click="deleteLink"
      >
        删除关系
      </div>
    </div>
    <nodeDialog v-model="nodeDialogVisible" :node-object="nodeObject" />
    <lineDialog
      v-model="lineDialogVisible"
      :line-object="lineObject"
      @EmitLineValidateForm="EmitLineValidateForm"
    />
  </div>
</template>

<script>
import RelationGraph from "relation-graph";
import DragToCreateToolbar from "./component/DragToCreateToolbar";
import MyDemoPanel from "./component/MyDemoPanel.vue";
import nodeDialog from "./component/nodeDialog.vue";
import lineDialog from "./component/lineDialog.vue";
export default {
  name: "GraphR",
  components: {
    RelationGraph,
    DragToCreateToolbar,
    nodeDialog,
    lineDialog,
    MyDemoPanel,
  },
  props: {
    /**
     * 渲染图形的数据

     */
    relationData: {
      type: Object,
      default: () => {},
    },
    id: {
      type: String,
      default: "",
    },
    /**
     * 是否可编辑
     */
    isEdit: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否为新增
     */
    isAdd: {
      type: Boolean,
      default: false,
    },
    /**
     * 是否展示刷新
     */
    isFefresh: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isShowNodeMenuPanel: false,
      nodeMenuPanelPosition: { x: 0, y: 0 },
      currentObjectType: null,
      graphOptions: {
        defaultNodeShape: 1,
        defaultLineColor: "#9999A1",
        defaultNodeFontColor: "#FFFFFF",
        defaultNodeColor: "rgba(66,187,66,1)",
        backgroundColor: "#2C2C35",
        allowShowMiniToolBar: false,
        disableNodeClickEffect: true,
        disableLineClickEffect: true,
        defaultFocusRootNode: false,
        defaultNodeBorderWidth: 1,
        disableDragCanvas: false,
        moveToCenterWhenRefresh: true,
        lineTextMaxLength: 6,
        zoomToFitWhenRefresh: true,
        defaultLineShape: 7,
        layout: {
          layoutName: "center",
          centerOffset_x: 0,
          centerOffset_y: 0,
          distance_coefficient: 0.5,
          fastStart: true,
        },
      },
      graphOptionsLight: {
        defaultNodeShape: 1,
        defaultLineColor: "#9999A1",
        defaultNodeFontColor: "#2C2C35",
        defaultNodeColor: "rgba(66,187,66,1)",
        backgroundColor: "#f8f9fd",
        allowShowMiniToolBar: false,
        disableNodeClickEffect: true,
        disableLineClickEffect: true,
        defaultFocusRootNode: false,
        defaultNodeBorderWidth: 1,
        disableDragCanvas: false,
        moveToCenterWhenRefresh: true,
        lineTextMaxLength: 6,
        zoomToFitWhenRefresh: true,
        defaultLineShape: 7,
        layout: {
          layoutName: "center",
          centerOffset_x: 0,
          centerOffset_y: 0,
          distance_coefficient: 0.5,
          fastStart: true,
        },
      },
      relationGraph: null,
      nodeDialogVisible: false,
      lineDialogVisible: false,
      lineObject: {}, // 线条的属性集合
      nodeObject: {},
    };
  },
  computed: {
    dicts() {
      return this.$store.state.user.dicts;
    },
    themeOptions() {
      return this.$store.state.user.theme
        ? this.graphOptionsLight
        : this.graphOptions;
    },
  },
  watch: {
    id: {
      handler(val) {
        if (val && !this.isAdd) {
          this.$nextTick(() => {
            this.showGraph(this.relationData);
          });
        } else {
          this.$nextTick(() => {
            this.showGraph({
              rootId: "a",
              nodes: [
                {
                  id: "a",
                  text: "A",
                  isHide: false,
                  width: 100,
                  height: 100,
                  opacity: 0,
                },
              ],
              lines: [],
            });
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    showGraph(val) {
      // 当一个节点都没有时，需要初始化一下，把节点背景设置为0
      if (!val.nodes?.length) {
        val = {
          rootId: val.rootId,
          nodes: [
            {
              id: "a",
              text: "A",
              isHide: false,
              width: 1,
              height: 1,
              // opacity: 0,
            },
          ],
          lines: [],
        };
      } else {
        val.rootId = val.nodes[0].id;
      }
      const graphRef = this.$refs.graphRef;
      this.relationGraph = this.$refs.graphRef.getInstance();
      graphRef.setJsonData({ ...val }, (graphInstance) => {
        graphInstance.zoomToFit();
        // 这些写上当图谱初始化完成后需要执行的代码.
      });
    },
    refresh() {
      this.relationGraph.refresh();
    },
    startAddLine(e) {
      if (!this.relationGraph.getNodes().some((node) => node.opacity > 0)) {
        return this.$message({ type: "error", message: "请先创建节点！" });
      }
      this.$message({ type: "success", message: "点击节点开始创建关系！" });
      const newId = this.$generateRandomIndex(8);
      this.relationGraph.startCreatingLinePlot(e, {
        template: {
          lineWidth: 1,
          text: "",
        },
        onCreateLine: (from, to, finalTemplate) => {
          let sourceType = from.data?.type;
          let destinationType = to.data?.type;
          const sourceName = this.dicts.vertices[sourceType]?.name;
          const destinationName = this.dicts.vertices[destinationType]?.name;
          if (!sourceName || !destinationName) {
            return;
          }
          // 1. 判断当前节点与节点之前是否有关系
          let links = this.$refs.graphRef.getLinks();
          if (links.length) {
            for (let x = 0; x < links.length; x++) {
              let { toNode, fromNode } = links[x];
              if (
                (from.id === toNode.id && to.id === fromNode.id) ||
                (from.id === fromNode.id && to.id === toNode.id)
              ) {
                return this.$message.warning(
                  `【${sourceName}】与【${destinationName}】已存在关系`
                );
              }
            }
          }
          // 2. 判断当前节点与节点之前是否存在关系
          let result = this.searchEdges({ sourceType, destinationType }).length;
          if (!result)
            return this.$message.warning(
              `【${sourceName}】与【${destinationName}】间暂无关联关系`
            );
          if (to.id) {
            // 创建的连线的起点一定是节点，但终点可以是空白处，终点没有选择成节点时to不是一个节点，to.id不会有值，这里做了判断，只处理to为节点的情况
            this.relationGraph.addLines([
              Object.assign({}, finalTemplate, {
                from: from.id,
                to: to.id,
                text: "",
                data: {
                  sourceType: from.data.type,
                  destinationType: to.data.type,
                  srcId: from.data.id,
                  dstId: to.data.id,
                  id: "e" + newId,
                },
              }),
            ]);
          }
        },
      });
    },
    save(val) {
      const nodes = this.$refs.graphRef.getInstance().getNodes(); // 获取节点
      const lines = this.$refs.graphRef.getInstance().getLinks(); // 获取线条
      const jsonData = this.$refs.graphRef.getInstance().getGraphJsonData(); // 获取所有数据
      const params = {
        nodes,
        lines,
        jsonData,
      };
      this.$emit("save", params, val);
    },
    close() {
      this.$emit("close");
    },
    // 线条点击
    onLineClick() {
      if (!this.isEdit) return;
      // 初始化数据为空的时候
      if (!this.lineObject.data?.props?.length) {
        this.lineObject.data.props = [
          {
            property: "",
            operator: "",
            value: "",
            id: this.$generateRandomId(),
          },
        ];
      }
      this.lineDialogVisible = true;
      this.isShowNodeMenuPanel = false;
    },
    // 点击线条数据回显
    EmitLineValidateForm(lineValidateForm) {
      this.lineObject.text = this.dicts.edges[lineValidateForm.lx].name;
    },
    // 节点点击
    onNodeClick() {
      if (!this.isEdit) return;
      // 初始化数据为空的时候
      if (!this.nodeObject.data?.fields?.length) {
        let sourceType = this.nodeObject.data.type;
        const sourceName = this.dicts.vertices[sourceType].name;
        this.$message.warning(`【${sourceName}】暂无过滤属性`);
        return;
      }
      if (!this.nodeObject.data?.property?.length) {
        this.nodeObject.data.property = [
          {
            property: "",
            operator: "",
            value: "",
            id: this.$generateRandomId(),
          },
        ];
      }
      this.nodeDialogVisible = true;
      this.isShowNodeMenuPanel = false;
    },
    // 右键点击展开操作项
    showNodeMenus($event, objectType, object) {
      if (!this.isEdit) return;
      if (object) {
        this.currentObjectType = objectType;
        this.currentObject = object;
        if (objectType === "node") {
          this.nodeObject = object;
        } else if (objectType === "link") {
          this.lineObject = object.relations?.[0];
        }
        const _base_position = this.$refs.myPage.getBoundingClientRect();
        this.isShowNodeMenuPanel = true;
        this.nodeMenuPanelPosition.x = $event.clientX - _base_position.x;
        this.nodeMenuPanelPosition.y = $event.clientY - _base_position.y;
        const hideContentMenu = () => {
          this.isShowNodeTipsPanel = false;
          document.body.removeEventListener("click", hideContentMenu);
        };
        document.body.addEventListener("click", hideContentMenu);
      }
    },
    deleteNode() {
      if (!this.isEdit) return;
      const graphInstance = this.$refs.graphRef.getInstance();
      graphInstance.removeNodeById(this.currentObject.id);
      this.isShowNodeMenuPanel = false;
    },
    deleteLink() {
      if (!this.isEdit) return;
      const graphInstance = this.$refs.graphRef.getInstance();
      graphInstance.removeLinkById(this.currentObject.seeks_id);
      this.isShowNodeMenuPanel = false;
    },
    // 根据节点查边
    searchEdges({ sourceType, destinationType }) {
      let panorama_edge_info = [];
      let edges = this.dicts.edges;
      for (const key in edges) {
        if (edges[key]) {
          if (
            edges[key].sourceType == sourceType &&
            edges[key].destinationType == destinationType
          ) {
            panorama_edge_info.push({
              key: key,
              label: edges[key].name,
              src: edges[key].sourceType,
              dst: edges[key].destinationType,
              type: key,
              nodeFiles: edges[key].properties,
              props: [
                {
                  property: "",
                  operator: "",
                  value: "",
                  id: this.$generateRandomId(),
                },
              ],
            });
          }
        }
      }
      return panorama_edge_info;
    },
  },
};
</script>

<style lang="scss" scoped>
.screen {
  width: 100%;
  background: var(--BackgroundMostColor);
  height: 100%;
  overflow: hidden;
}
::v-deep .relation-graph {
  .c-node-text {
    height: 100%;
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    font-size: 10px;
    line-height: 35px;
    cursor: pointer;
    span {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
    }
  }
  .graph-plug-handle {
    top: 16px;
    right: 16px;
    position: absolute;
    width: fit-content;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 22;
    [class^="el-icon-"] {
      padding: 8px;
      width: 62px;
      font-size: 12px;
      color: var(--TextColor);
      background: var(--BackgroundColorResult);
      border: 1px solid transparent;
      border-radius: 4px;
      box-sizing: border-box;
      margin-right: 8px;
      &:hover {
        cursor: pointer;
      }
    }
    .el-icon-document-add {
      color: var(--TextMainColor);
      background: var(--BackgroundColor0) !important;
      border-color: var(--BorderButtonColor) !important;
      &:hover {
        background: var(--BackgroundColorHover) !important;
        color: var(--TextColor) !important;
      }
    }
    .el-icon-share:hover{
       border:1px solid var(--BorderButtonColor) !important;
    }
  }
}
.node-menu {
  z-index: 999;
  padding: 8px 12px;
  background-color: #ffffff;
  border: #eeeeee solid 1px;
  // box-shadow: 0px 0px 8px #cccccc;
  position: absolute;
  border-radius: 4px;
  .c-node-menu-item {
    line-height: 30px;
    // padding-left: 10px;
    cursor: pointer;
    color: #444444;
    font-size: 14px;
    // border-top: #efefef solid 1px;
  }
  .c-node-menu-item:hover {
    background-color: rgba(66, 187, 66, 0.2);
  }
}
.c-title {
  font-weight: bold;
}
.c-content {
  list-style: none;
  padding: 0px;
  margin: 0;
}
</style>
