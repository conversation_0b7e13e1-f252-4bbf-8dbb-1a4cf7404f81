<template>
  <div
    class="c-my-demo-panel"
    :class="[
      closed ? 'c-my-demo-panel-closed' : '',
      right ? 'c-my-demo-panel-r' : '',
    ]"
    :style="{
      '--my-panel-width': this.width,
      '--my-panel-top': this.top,
      left: right ? undefined : left,
      right: right ? right : undefined,
    }"
  >
    <div class="my-footer">
      <div class="c-title">操作说明</div>
      <div v-if="closed" class="my-icon my-icon-open" @click="tooglePanel">
        {{ right ? "↙" : "↘" }}
      </div>
      <div v-else class="my-icon my-icon-close" @click="tooglePanel">
        {{ right ? "➡" : "⬅" }}
      </div>
    </div>
    <div class="my-body">
      <slot />
    </div>
  </div>
</template>
  
<script>
export default {
  name: "MyDemoPanel",
  props: {
    width: {
      mustUseProp: false,
      default: "400px",
      type: String,
    },
    left: {
      mustUseProp: false,
      default: "10px",
      type: String,
    },
    right: {
      mustUseProp: false,
      default: "",
      type: String,
    },
    top: {
      mustUseProp: false,
      default: "10px",
      type: String,
    },
  },
  data() {
    return {
      closed: false,
    };
  },
  mounted() {},
  methods: {
    tooglePanel() {
      this.closed = !this.closed;
    },
  },
};
</script>
  <style lang="scss" scoped>
.c-my-demo-panel {
  position: absolute;
  border-radius: 5px;
  z-index: 800;
  width: var(--my-panel-width);
  top: var(--my-panel-top);
  background: var(--BackgroundColorResult);
//   border: #999999 solid 1px;
  box-shadow: 0 2px 6px rgba(0, 21, 41, 0.3);
  padding: 10px;
  box-sizing: border-box;
  font-size: 14px;
  color: var(--TextColor);
  transition: width 0.3s ease-out;
  .my-footer {
    text-align: right;
    display: flex;
    // place-items: end;
    justify-content: space-between;
    align-items: center;
    .my-icon {
      border-radius: 5px;
      width: 30px;
      height: 30px;
      font-size: 16px;
      color: var(--TextColor);
      background: var(--BackgroundMostColor);
      display: flex;
      place-items: center;
      justify-content: center;
      cursor: pointer;
      &:hover {
        background: var(--BackgroundColorResult);
        color: var(--TextColor);
      }
      svg {
        fill: currentColor;
        width: 100%;
        height: 100%;
      }
    }
    .my-icon-close {
    }
  }
  .c-title {
    color: var(--TextColor);
    font-size: 16px;
    font-weight: bold;
    text-align: left;
    width: 100px;
  }
  .c-content {
    color: var(--TextColor);
    font-size: 14px;
    line-height: 20px;
    padding: 6px;
  }
  .c-button {
    line-height: 18px;
    display: inline-block;
    background-color: #035a8a;
    color: #ffffff;
    font-size: 12px;
    padding: 5px 15px;
    text-align: center;
    cursor: pointer;
    border-radius: 5px;
    &:hover {
      background-color: rgba(3, 90, 138, 0.68);
    }
  }
  .c-link {
    color: #167fb7;
    cursor: pointer;
    padding: 0px 15px;
    &:hover {
      text-decoration: underline #167fb7;
    }
  }
  .c-my-options {
    text-align: center;
    .c-my-option-item {
      text-align: left;
      color: #1da9f5;
      cursor: pointer;
      border-radius: 5px;
      padding-left: 10px;
      margin-top: 5px;
      line-height: 25px;
      &:hover {
        background-color: rgba(29, 169, 245, 0.2);
      }
    }
  }
}
.c-my-demo-panel-closed {
  width: 116px;
  height: 50px;
  .my-body {
    opacity: 0;
    display: none;
  }
}
.c-my-demo-panel-r {
  .my-footer {
    place-items: end;
    justify-content: start;
  }
}
</style>
  