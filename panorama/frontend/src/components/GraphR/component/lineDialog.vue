<template>
  <el-dialog
    title="关系"
    :visible.sync="isShow"
    width="550px"
    :close-on-click-modal="false"
    custom-class="custom"
    append-to-body
  >
    <el-form
      ref="lineFormRef"
      :rules="rules"
      :model="lineForm"
      class="demo-dynamic"
    >
      <el-form-item prop="lx" label="类型">
        <el-select
          v-model="lineForm.lx"
          filterable
          style="width: 100%"
          placeholder="请选择"
          @change="handleChange"
        >
          <el-option
            v-for="item in panorama_edge_info_arr"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <template v-if="lineForm.lx && lineForm.props.length && nodeFiles.length">
        <div class="top">
          <div class="left">筛选</div>
          <div
            class="right el-icon-circle-plus-outline"
            @click="handleAdd"
          ></div>
        </div>
        <div v-for="(item, index) in lineForm.props" :key="item.id">
          <nodeFiled
            v-model="lineForm.props[index]"
            :show-del="lineForm.props.length > 1"
            :options="nodeFiles"
            @handleDel="handleDel"
            @click="handleDel"
          />
        </div>
      </template>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" class="cancel" @click="isShow = false">取 消</el-button>
      <el-button size="small" type="primary" @click="submitForm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Mixins from "@/mixins";
import nodeFiled from "./nodeFiled.vue";
export default {
  name: "LineDialog",
  components: {
    nodeFiled,
  },
  mixins: [Mixins],
  props: {
    lineObject: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      lineForm: {
        lx: "",
        props: [],
      },
      nodeFiles: [],
      panorama_edge_info_arr: [],
      rules: {
        lx: [{ required: true, message: "请选择类型", trigger: "change" }],
      },
    };
  },
  computed: {
    dicts() {
      return this.$store.state.user.dicts.edges;
    },
  },
  watch: {
    isShow(val) {
      if (val) {
        // 获取当前类型的数据
        this.panorama_edge_info_arr = this.searchEdges(this.lineObject.data);
        // 回显关系
        this.lineForm.lx = this.lineObject.data.type
          ? String(this.lineObject.data.type)
          : "";
        this.getNodeFiles(this.lineForm.lx);
        // 回显属性
        if (this.lineForm.lx) {
          this.lineForm.props = JSON.parse(
            JSON.stringify(this.lineObject.data.props)
          );
        }
        this.$nextTick(() => {
          this.$refs.lineFormRef.clearValidate();
        });
      }
    },
  },
  methods: {
    getNodeFiles(val) {
      this.nodeFiles = this.panorama_edge_info_arr
        .find((item) => item.key === val)
        ?.nodeFiles.filter((item) => item.queryable);
      this.lineForm.props = [
        {
          property: "",
          operator: "",
          value: "",
          id: this.$generateRandomId(),
        },
      ];
    },
    handleChange(val) {
      this.getNodeFiles(val);
    },
    submitForm() {
      this.$refs.lineFormRef.validate((valid) => {
        if (!valid) {
          return false;
        }
        this.isShow = false;
        // 只过滤掉有值的清空
        this.lineObject.data.props = this.lineForm.props.filter(
          (item) => item.property
        );
        this.lineObject.data.type = this.lineForm.lx;
        this.$emit("EmitLineValidateForm", this.lineForm);
      });
    },
    handleAdd() {
      this.lineForm.props.push({
        property: "",
        operator: "",
        value: "",
        id: this.$generateRandomId(),
      });
    },
    handleDel(item) {
      this.lineForm.props = this.lineForm.props.filter((x) => x.id !== item.id);
    },
    // 根据节点查边
    searchEdges({ sourceType, destinationType }) {
      let panorama_edge_info = [];
      let edges = this.dicts;
      for (const key in edges) {
        if (edges[key]) {
          if (
            edges[key].sourceType == sourceType &&
            edges[key].destinationType == destinationType
          ) {
            panorama_edge_info.push({
              key: key,
              label: edges[key].name,
              src: edges[key].sourceType,
              dst: edges[key].destinationType,
              type: key,
              nodeFiles: edges[key].properties,
              props: [
                {
                  property: "",
                  operator: "",
                  value: "",
                  id: this.$generateRandomId(),
                },
              ],
            });
          }
        }
      }
      return panorama_edge_info;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./style.scss";
@import "@/styles/common.scss";
</style>