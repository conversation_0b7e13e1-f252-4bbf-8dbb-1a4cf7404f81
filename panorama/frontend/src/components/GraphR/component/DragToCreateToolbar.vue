<template>
  <div class="my-toolbar">
    <div class="my-dragable-items">
      <div
        v-for="template of nodeTemplates"
        :key="template.text"
        class="my-mode-item my-mode-item-node"
        :class="[options.creatingNodePlot ? 'my-mode-item-on' : '']"
        :style="{
          background: template.hoverColor,
          borderColor: template.borderColor,
        }"
        @mouseenter="template.hoverColor = template.selectColor"
        @mouseleave="template.hoverColor = template.backgroundColor"
        @mousedown="startAddNode(template, $event)"
      >
        <div class="c-mb-text">
          {{ template.text }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formateNodeColor } from "../formate";
export default {
  name: "DragToCreateToolbar",
  data() {
    return {
      nodeTemplates: [],
      height: 275,
      newNodeIdIndex: 1,
      panorama_tag_info_arr: [],
    };
  },
  inject: ["graph", "graphInstance"],
  computed: {
    relationGraph() {
      return this.graphInstance();
    },
    options() {
      return this.graph.options;
    },
  },
  mounted() {
    this.init();
    if (this.options.layouts.length > 1) {
      this.height -= 40;
    }
  },
  methods: {
    // 创建节点
    startAddNode(tempNode, e) {
      // this.isExit(tempNode);
      this.relationGraph.startCreatingNodePlot(e, {
        templateText: tempNode.text,
        templateNode: Object.assign({}, JSON.parse(JSON.stringify(tempNode)), {
          width: 70,
          height: 40,
          nodeShape: 1,
          borderWidth: 2,
          borderColor: tempNode.borderColor,
          color: tempNode.backgroundColor,
        }),
        onCreateNode: (x, y) => {
          const nodeSize = {
            width: tempNode.width || 70,
            height: tempNode.height || 40,
          };
          const newId = this.$generateRandomIndex(8);
          this.relationGraph.addNodes([
            Object.assign({}, tempNode, {
              id: "v" + newId,
              x: x - nodeSize.width / 2,
              y: y - nodeSize.height / 2,
              disableDefaultClickEffect: true,
              styleClass: "node-style",
              width: 80,
              height: 40,
              nodeShape: 1,
              borderWidth: 2,
              borderColor: tempNode.borderColor,
              color: tempNode.backgroundColor,
              data: {
                id: "v" + newId, // id
                type: tempNode.nodeType, // type类型
                fields: tempNode.fields, // 字段
                property: [], // 节点下的属性
              },
            }),
          ]);
        },
      });
    },
    // 当前创建节点与画布已有的节点是否都建立了关系
    isExit({nodeType}) {
      let nodes = this.graphInstance().getNodes().filter(item=>item.opacity).map(x=>x.data.type); // 节点的集合
      if(!nodes.length){
        return;
      }
      let edges = Object.values(this.$store.state.user.dicts.edges); // 边的关系集合
      for (let index = 0; index < edges.length; index++) {
        const element = edges[index];
        for (let index = 0; index < nodes; index++) {
          const node = nodes[index];
          console.log((nodeType===String(element.sourceType)&&node===String(element.destinationType)));
          console.log((nodeType===String(element.sourceType)&&node===String(element.destinationType)));
           
          if((nodeType===String(element.sourceType)&&node===String(element.destinationType))||(nodeType===String(element.sourceType)&&node===String(element.destinationType))){
            console.log(element.properties.length);
          }
        }
      }
    },
    init() {
      this.panorama_info();
      formateNodeColor(this.nodeTemplates);
      this.nodeTemplates = { ...this.nodeTemplates };
    },
    panorama_info() {
      let panorama_tag_info = this.$store.state.user.dicts.vertices;
      let panorama_tag_info_arr = [];
      for (const key in panorama_tag_info) {
        if (panorama_tag_info[key]) {
          panorama_tag_info_arr.push({
            text: panorama_tag_info[key].name,
            nodeType: key,
            label: panorama_tag_info[key].name,
            fields: panorama_tag_info[key].properties,
            color: "",
            borderColor: "",
            hoverColor: "",
            selectColor: "",
            backgroundColor: "",
          });
        }
      }
      this.nodeTemplates = panorama_tag_info_arr;
    },
  },
};
</script>

<style lang="scss" scoped>
.my-toolbar {
  position: absolute;
  z-index: 900;
  top: 0;
  left: 0;
  padding: 8px;
  width: 80px;
  box-sizing: border-box;
  height: 100%;
  border-right: var(--BorderMiniColor) solid 1px;
  box-sizing: border-box;
  user-select: none;
  .my-dragable-items {
    display: flex;
    justify-content: center;
    place-items: center;
    gap: 8px;
    flex-wrap: wrap;
  }
  .rg-icon {
    width: 16px;
    height: 16px;
    vertical-align: -3px;
    fill: currentColor;
    overflow: hidden;
  }
  .my-mode-item {
    height: 40px;
    width: 70px;
    padding-top: 5px;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    justify-content: center;
    place-items: center;
    padding: 2px;
    box-sizing: border-box;
    border: 1px solid #cecece;
    .c-mb-text {
      font-size: 10px;
      line-height: 10px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: var(--TextColor)
    }
  }
  .my-mode-item:hover .c-mb-text,
  .c-mb-button-on .c-mb-text {
    color: var(--TextColor)
  }
}
</style>