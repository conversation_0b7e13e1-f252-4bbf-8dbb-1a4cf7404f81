<template>
  <el-dialog
    :title="nodeObject.text"
    :visible.sync="isShow"
    width="550px"
    custom-class="custom"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form ref="nodeFormRef" :model="nodeForm" class="demo-dynamic">
      <div class="top">
        <div class="left">筛选</div>
        <div class="right el-icon-circle-plus-outline" @click="handleAdd"></div>
      </div>
      <div v-for="(item, index) in nodeForm.property" :key="item.id">
        <nodeFiled
          v-model="nodeForm.property[index]"
          :show-del="nodeForm.property.length > 1"
          :options="nodeFiles"
          @handleDel="handleDel"
          @click="handleDel"
        />
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" class="cancel" @click="isShow = false">取 消</el-button>
      <el-button size="small" type="primary" @click="submitForm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Mixins from "@/mixins";
import nodeFiled from "./nodeFiled.vue";
export default {
  name: "NodeDialog",
  components: {
    nodeFiled,
  },
  mixins: [Mixins],
  props: {
    nodeObject: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 初始化
      nodeForm: {
        property: [],
      },
      nodeFiles: [],
    };
  },
  watch: {
    isShow(val) {
      if (val) {
        let { fields } = JSON.parse(JSON.stringify(this.nodeObject.data));
        // 回显属性
        this.nodeForm.property = JSON.parse(JSON.stringify(this.nodeObject.data.property));
        // 查属性字段
        this.nodeFiles = fields.filter(item=>item.queryable);
      }else{
        this.nodeForm.property=[];
      }
    },
  },
  methods: {
    submitForm() {
      this.$refs.nodeFormRef.validate(valid=>{
        if(!valid) return false;
        this.nodeObject.data.property = this.nodeForm.property.filter(item=>item.property);
        this.isShow = false;
      });
    },
    // 新增
    handleAdd() {
      this.nodeForm.property.push({
        property: "",
        operator: "",
        value: "",
        id: this.$generateRandomId(),
      });
    },
    // 删除
    handleDel(item) {
      this.nodeForm.property = this.nodeForm.property.filter(
        (x) => x.id !== item.id
      );
    },
  },
};
</script>

<style lang="scss" scoped>
@import "./style.scss";
@import "@/styles/common.scss";
</style>