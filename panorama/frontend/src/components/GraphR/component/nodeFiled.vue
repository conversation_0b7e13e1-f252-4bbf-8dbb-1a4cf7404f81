<template>
  <div class="filed">
    <el-form-item prop="property" label="字段">
      <el-select
        v-model="form.property"
        style="width: 140px"
        placeholder="请选择"
        clearable
        @change="changeProperty"
      >
        <el-option
          v-for="item in options"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      prop="operator"
      label="操作"
      :rules="[{ validator: validateOperator, trigger: ['blur', 'change'] }]"
    >
      <el-select
        v-model="form.operator"
        :disabled="!form.property"
        style="width: 140px"
        placeholder="请选择"
        @change="changeOperator"
      >
        <el-option
          v-for="item in operators"
          :key="item.key"
          :label="item.name"
          :value="item.key"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      prop="value"
      label="值"
      :rules="[{ validator: validateValue, trigger: ['blur', 'change'] }]"
    >
      <el-select v-if="typeof currentType === 'boolean'">
        <el-option
          v-for="item in [true, false]"
          :key="item"
          :label="item"
          :value="item"
        >
        </el-option>
      </el-select>
      <el-input
        v-else
        v-model="form.value"
        :disabled="!form.operator"
        placeholder="请输入"
        style="width: 140px"
      ></el-input>
    </el-form-item>
    <el-form-item label="操作">
      <!-- <i
        :class="[showDel ? '' : 'delete', 'el-icon-delete']"
        @click="handleDel"
      /> -->
      <i
        :class="['el-icon-delete']"
        @click="handleDel"
      />
    </el-form-item>
  </div>
</template>

<script>
export default {
  name: "NodeFiled",
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    options: {
      type: Array,
      default: () => [],
    },
    showDel: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    var validateOperator = (rule, value, callback) => {
      if (this.form.property) {
        if (!this.form.operator) {
          callback(new Error("请选择操作符"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validateValue = (rule, value, callback) => {
      if (this.form.property && this.form.operator) {
        if (!this.form.value) {
          callback(new Error("请填写值"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      validateValue,
      validateOperator,
      currentType: "",
    };
  },
  computed: {
    form: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
    operator() {
      return this.$store.state.user.dicts.operator;
    },
    operators() {
      let operatorArr = [];
      // 类型
      let type = this.options.find(
        (option) => this.form.property === option.key
      )?.type;
      // 操作的内容
      if (type) {
        for (const key in this.operator) {
          const ele = this.operator[key];
          if (ele.supportedTypes.includes(type)) {
            operatorArr.push({
              key,
              name: ele.name,
            });
          }
        }
      } else {
        operatorArr = [];
      }
      return operatorArr;
    },
  },
  methods: {
    handleDel() {
      this.$emit("handleDel", this.value);
    },
    changeProperty() {
      this.form.operator = "";
      this.form.value = "";
    },
    changeOperator() {
      this.form.value = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.filed {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .el-form-item {
    margin-bottom: 10px;
  }
  .el-icon-delete {
    // color: var(--TextColor);
    // border: 1px solid var(--BorderMajorColor);
    border-radius: 4px;
    padding: 10px;
    &:hover {
      cursor: pointer;
    }
  }
  .delete {
    /* 设置按钮为灰色 */
    color: rgb(222, 217, 217);
    /* 设置禁用状态样式 */
    cursor: not-allowed;
    pointer-events: none;
  }
}
</style>