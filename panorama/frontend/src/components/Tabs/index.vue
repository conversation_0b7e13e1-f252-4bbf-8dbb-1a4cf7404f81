<template>
  <div class="container-box">
    <div class="tabs">
      <div
        v-for="(item, index) in lists"
        :key="index"
        :class="['tabs-item', activeIndex === index ? 'active' : '']"
        @click="clickTab(item,index)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="slot">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "Tabs",
  props: {
    lists: {
      type: Array,
      default: () => [],
    },
    value: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    activeIndex: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    clickTab(item,index) {
      this.activeIndex=index;
      this.$emit('clickTab',{item,index});
    }
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";
.container-box{
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}
.tabs {
  @include flex();
  width: fit-content;
  background: var(--BackgroundMostColor);
  color: var(--TextColor);
  padding: 6px;
  border-radius: 4px;
  &-item {
    padding: 4px 8px;
    border-radius: 4px;
    &:hover{
      cursor: pointer;
    }
  }
  .active {
    background: var(--BackgroundColor0);
    color: var(--TextMainColor);
  }
}
</style>