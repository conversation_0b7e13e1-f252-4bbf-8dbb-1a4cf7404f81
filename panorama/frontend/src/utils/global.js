import dayjs from "dayjs";
// 处理时间 [ processing time ]
export function processingTime(value, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!value) return '-';
  if (String(value).length <= 10) {
    value = value * 1000;
  }
  return dayjs(value).format(format);
}
/**
 * 时间格式化02
 */
export function formatDateOne(value, format = "YYYY-MM-DD HH:mm:ss") {
  return value ? dayjs(value).format(format) : "-";
}

/**
 * 生成随机id
 */
export function generateRandomId() {
  return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>
    (
      c ^
      (window.crypto.getRandomValues(new Uint8Array(1))[0] &
        (15 >> (c / 4)))
    ).toString(16)
  );
}

/**
 * 生成随机id2
 */
export function generateRandomIndex(length) {
  var result = '';
  var characters = '0123456789';
  var charactersLength = characters.length;
  for (var i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}