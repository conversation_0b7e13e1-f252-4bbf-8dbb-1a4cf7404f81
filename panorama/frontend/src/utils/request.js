import axios from "axios";
import { MessageBox, Message } from "element-ui";
import store from "@/store";
let baseURL;
if (process.env.VUE_APP_BASE_API === "") {
  baseURL = `${window.location.origin}/gk-api`;
} else {
  baseURL = process.env.VUE_APP_BASE_API;
}
const service = axios.create({
  baseURL: baseURL,
  timeout: 10000,
});
// 获取token和uid
const getAuthentication = () => {
  const params = new URLSearchParams(window.location.search);
  const ut = params.get("ut");
  const uid = params.get("uid");
  ut && window.sessionStorage.setItem("ut", ut);
  uid && window.sessionStorage.setItem("uid", uid);
};
if (!window.sessionStorage.getItem("ut")) {
  getAuthentication();
}
service.interceptors.request.use(
  (config) => {
    config.headers["ut"] = window.sessionStorage.getItem("ut")||'DHLFNNNNIGDDDEEEFMIKGDFMFDFHQDLQFJDEDNFENIIMMHLQFGMNNQDEMDGGFIHDIHFHIIDGGGGGGGGFGGFGGFGGGGDGGGGGDGGGGGGGIIIIHIGHGG';
    config.headers["uid"] = window.sessionStorage.getItem("uid")||'04H2ZJZZUS0001113';
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
service.interceptors.response.use(
  (response) => {
    const res = response.data;
    if (res.code !== 200) {
      if (res.code === 1002 || res.code === 1001) {
        MessageBox.confirm("是否重新登录", "登录已失效", {
          confirmButtonText: "重新登录",
          cancelButtonText: "取消",
          type: "warning",
        }).then(() => {
          location.reload();
          store.dispatch("user/resetToken").then(() => {});
        });
      } else if (res.code == 40013) {
        Message({
          message: res.msg || "Error:服务器错误",
          type: "info",
          duration: 5 * 1000,
        });
      } else {
        Message({
          message: res.msg || "Error:服务器错误",
          type: "error",
          duration: 5 * 1000,
        });
      }
      return Promise.reject(new Error(res.msg || "Error"));
    } else {
      return res;
    }
  },
  (error) => {
    Message({
      message: error.message || error.msg || "Error:服务器错误",
      type: "error",
      duration: 5 * 1000,
    });
    return Promise.reject(error);
  }
);

export default service;
