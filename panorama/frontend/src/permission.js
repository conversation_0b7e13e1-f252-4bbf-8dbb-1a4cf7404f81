import router from './router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import store from './store';

NProgress.configure({ showSpinner: false });

router.beforeEach(async (to, from, next) => {
  NProgress.start();
  if(!hasDicts()){
    await store.dispatch('user/dict');
  }
  next();
});

router.afterEach(() => {
  NProgress.done();
});

const hasDicts=()=>{
  let dicts=store.state.user.dicts;
  return Object.keys(dicts).length>0;
};
