<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: "App",
  data() {
    return {
      isLight: true,
    };
  },
  created() {
    let GO_DESIGN=window.localStorage.getItem("GO_DESIGN");
    if(GO_DESIGN){
      this.isLight=JSON.parse(GO_DESIGN)?.themeName === "light" ? true : false;
    }
    this.$store.commit("user/SET_THEME", this.isLight);
    document.body.className = this.isLight?'defaultTheme':'blackTheme';
    this.color = getComputedStyle(
      document.getElementById("app")
    ).getPropertyValue("--BackgroundMostColor");
  }
};
</script>

