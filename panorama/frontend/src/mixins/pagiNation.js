export default {
  data() {
    return {
      page: {
        pageSize: 10,
        page: 1,
      },
      sizes: [10, 20, 30, 50],
      total: 0,
      layout: 'total,prev, pager, next,sizes, jumper'
    };
  },
  methods: {
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.page.page = val;
      this.getList();
    }
  }
};
