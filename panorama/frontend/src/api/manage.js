import request from "@/utils/request";
// 查询单次运行记录(右边)
export function getRunsId(id) {
  return request({
    url: `/runs/${id}`,
    method: "get",
  });
}
// 获取第一个事件
export function getFirstRunsId(runId) {
  return request({
    url: `/events/first?runId=${runId}`,
    method: "get",
  });
}
// 获取下一个事件
export function getNextId(runId,currentId) {
  return request({
    url: `/events/next?runId=${runId}&after=${currentId}`,
    method: "get",
  });
}

// 获取两个点之间的截止到某个事件的所有边
export function getEdgesEndEvent(id,endEventId,fromVertexId,toVertexId,edgeType) {
  return request({
    url: `/runs/${id}/edges?endEventId=${endEventId}&fromVertexId=${fromVertexId}&toVertexId=${toVertexId}&edgeType=${edgeType}`,
    method: "get",
  });
}