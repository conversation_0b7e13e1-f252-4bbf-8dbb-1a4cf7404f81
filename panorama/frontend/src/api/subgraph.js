import request from "@/utils/request";
// 图关联事件模型字典
export function getDict() {
  return request({
    url: "/dict",
    method: "get",
  });
}
// 预置模型列表查询
export function modelPresetList(params) {
  return request({
    url: "/models/preset",
    method: "get",
    params
  });
}

// 恢复预置模型
export function modelPresetResume(id) {
  return request({
    url: `/models/preset/${id}/resume`,
    method: "post"
  });
}

// 暂停预置模型
export function modelPresetPause(id) {
  return request({
    url: `/models/preset/${id}/pause`,
    method: "post"
  });
}

// 修改预置模型设置
export function modelPresetEdit(id,data) {
  return request({
    url: `/models/preset/${id}`,
    method: "PATCH",
    data
  });
}

// 预置模式图模式查询
export function presetCustom(id) {
  return request({
    url: `/models/preset/${id}/pattern`,
    method: 'GET'
  });
}
 
// 自定义事件模型列表查询
export function modelGraphList(params) {
  return request({
    url: "/models/custom",
    method: "get",
    params
  });
}

// 自定义事件模型删除
export function modelGraphDelete(id) {
  return request({
    url: "/models/custom/"+id,
    method: "DELETE",
  });
}

// 创建自定义事件图模型
export function createCustom(data) {
  return request({
    url: '/models/custom',
    method: 'POST',
    data
  });
}

// 修改自定义事件图模型
export function editCustom(id,data) {
  return request({
    url: `/models/custom/${id}`,
    method: 'PUT',
    data
  });
}

// 查询自定义模型图模式
export function patternCustom(id) {
  return request({
    url: `/models/custom/${id}/pattern`,
    method: 'GET'
  });
}

// 运行自定义事件图模型
export function modelsCustomRun(id) {
  return request({
    url: `/models/custom/${id}/run`,
    method: 'POST',
  });
}
// 运行自定义事件图模型
export function modelsCustomTry(data) {
  return request({
    url: `/models/custom/try`,
    method: 'POST',
    data
  });
}