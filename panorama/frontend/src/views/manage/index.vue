<template>
  <div class="manage">
    <div class="manage-right">
      <Graph
        v-if="initData.eventCount"
        :run-id="id"
        :count="initData.eventCount"
      />
      <el-empty v-else description="暂无事件结果" class="result"></el-empty>
    </div>
    <div class="manage-left">
      <div class="info">
        <div class="info-item">
          <div class="info-item-label">模型名称</div>
          <div class="info-item-value">{{ initData.modelName||'-' }}</div>
        </div>
        <div class="info-item">
          <div class="info-item-label">等级</div>
          <div class="info-item-value">
            {{ dicts.eventSeverity[initData.severity]||'-' }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-item-label">类型</div>
          <div class="info-item-value">
            {{ dicts.classType[initData.type]||'-' }}
          </div>
        </div>
        <div class="info-item">
          <div class="info-item-label">执行时间</div>
          <div class="info-item-value">
            {{ processingTime(initData.runTime) }}
          </div>
        </div>
      </div>
      <GraphR v-if="Object.keys(relationData).length" :id="id" :relation-data="relationData"> </GraphR>
    </div>
  </div>
</template>

<script>
import GraphR from "@/components/GraphR";
import Graph from "@/components/graph";
import { formateNodeColor } from "@/components/GraphR/formate";
import { processingTime } from "@/utils/global";
import { getRunsId } from "@/api/manage";
export default {
  name: "Manage",
  components: {
    GraphR,
    Graph,
  },
  data() {
    return {
      relationData: {},
      initData: {},
      processingTime,
      id: "",
    };
  },
  computed: {
    dicts() {
      return this.$store.state.user.dicts;
    },
  },
  watch: {
    $route: {
      handler(val) {
        if (val.params?.id) {
          this.id = val.params.id;
          this.getRuns();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    // 模型展示
    init(val) {
      const list = {
        rootId: "",
        nodes: [],
        lines: [],
      };
      let data = val.pattern;
      list.rootId = "a";
      list.nodes = data.vertices.map((item, index) => {
        return {
          id: item.id,
          nodeType: item.type,
          text: this.dicts.vertices[item.type].name,
          data: {
            fields: this.dicts.vertices[item.type].properties,
            id: item.id,
            type: String(item.type), // type类型
            property: item.filters, // 属性
          },
          width: 70,
          height: 40,
        };
      });
      list.lines = data.edges.map((item, index) => {
        return {
          id: item.id,
          nodeType: item.type,
          from: item.srcId,
          to: item.dstId,
          text: this.dicts.edges[item.type].name,
          data: {
            props: item.filters,
            sourceType: item.srcType,
            destinationType: item.dstType,
            srcId: item.srcId,
            dstId: item.dstId,
            id: item.id,
            type: item.type,
          },
        };
      });
      formateNodeColor(list.nodes);
      this.relationData = list;
    },
    // 查询单次运行记录
    async getRuns() {
      try {
        const res = await getRunsId(this.id);
        this.initData = res.data;
        this.init(this.initData);
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.manage {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
  &-left {
    width: 560px;
    height: 100%;
    position: relative;
    background: var(--BackgroundMostColor);
    .info {
      height: 64px;
      padding: 10px 0;
      padding-left: 20px;
      position: absolute;
      background: var(--BackgroundMostColor);
      box-sizing: border-box;
      top: 0px;
      left: 0;
      right: 0;
      z-index: 1;
      display: flex;
      border-bottom: 1px solid var(--BorderMiniColor);
      &-item {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        font-size: 14px;
        &:not(:first-child) {
          margin-left: 30px;
        }
        &-label {
          color: var(--TextMajorColor);
          min-width: 30px;
        }
        &-value {
          color: var(--TextColor);
          width: fit-content;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
  }
  &-right {
    flex: 1;
    background: var(--BackgroundColorResult);
  }
}
// 隐藏刷新按钮
::v-deep .graph-plug-handle {
  display: none !important;
}
.result{
  height: 100%;
  color: #fff;
}
</style>
