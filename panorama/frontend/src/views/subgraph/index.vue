<template>
  <div class="subgraph" :style="{ backgroundImage: `url(${backgroundImage})` }">
    <div class="subgraph-left">
      <Tabs v-model="active" :lists="lists" @clickTab="clickTab">
        <!-- <el-button size="mini"
                   icon="el-icon-refresh"
                   @click="clickTab"
        >
          刷新
        </el-button> -->
        <i class="el-icon-refresh other" @click="clickTab">刷新</i>
        <el-button
          v-show="active"
          icon="el-icon-plus"
          size="mini"
          type="primary"
          style="margin-left: 6px;"
          @click="handleAdd"
        >
          新建模型
        </el-button>
      </Tabs>
      <div v-loading="loading" class="table" style="height:800px">
        <el-table
          :data="tableData"
          style="width: 100%"
          stripe
          :header-cell-style="headerCellStyle"
          @row-click="handleRowClick"
        >
          <el-table-column prop="name" label="模型名称" width="220" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="severityCn" label="等级" width="60">
          </el-table-column>
          <el-table-column prop="typeCn" label="类型" width="150" show-overflow-tooltip> </el-table-column>
          <el-table-column prop="description" label="描述" min-width="250" show-overflow-tooltip>
          </el-table-column>
          <el-table-column v-if="!active" prop="schedule" label="执行间隔" width="150">
            <template #default="{row}">{{ row.interval }}{{ dicts.timeUnit[row.unit] }}</template>
          </el-table-column>
          <el-table-column v-if="!active" prop="statusCn" label="启用" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.active"
                @change="change(row)"
              >
              </el-switch>
            </template>
          </el-table-column>
          <!-- 暂时隐藏，由于没有数据 -->
          <el-table-column v-if="active" prop="ngql" min-width="280" label="查询语句" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="lastRunTime" label="最近运行时间" width="180">
          </el-table-column>
          <!-- <el-table-column v-if="active" prop="running" label="状态" width="80">
            <template #default="{ row }">
              <span :style="{color:row.running?'#DEE0E7':'#767684'}">{{ row.running?'执行中':'未执行' }}</span>
            </template>
          </el-table-column> -->
          <el-table-column prop="address" label="操作" :width="active?160:100">
            <!-- <template v-if="!active" #header>
              <div style="padding-left:20px;padding-right:12px">操作</div>
            </template> -->
            <template #default="{ row,$index }">
              <!-- <el-button v-show="active" type="text" :disabled="row.running" @click="execute(row)">执行</el-button> -->
              <el-button type="text" @click="handleView(row)">查看</el-button>
              <el-button type="text" @click="handleEdit(row,$index)">编辑</el-button>
              <el-button v-show="active" type="text" @click="del(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="page.page"
          :page-size="page.pageSize"
          background
          layout="total, prev, pager, next"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination> 
      </div>
    </div>
    <!-- <div class="subgraph-right">
      <GraphR :id="id" ref="GraphRef" :relation-data="relationData" />
    </div> -->
    <el-dialog
      :visible.sync="delDialogVisible"
      custom-class="custom"
      width="480px"
    >
      <template #title>
        <i class="iconfont icon-a-16_alert-copy"></i>
        <span class="icon-a-16_alert-copy-name">提醒</span>
      </template>
      <div class="el-dialog__content">相关数据将会清除。是否确认删除?</div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" class="cancel" style="margin-right:2px" @click="delDialogVisible = false">取 消</el-button>
        <el-button size="small" type="danger" :loading="delLoading" @click="handleDel">确认删除</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="executeDialogVisible"
      custom-class="custom"
      width="480px"
    >
      <template #title>
        <i class="iconfont icon-a-16_alert-copy"></i>
        <span class="icon-a-16_alert-copy-name">提醒</span>
      </template>
      <div class="el-dialog__content">模型执行过程中不可撤销，是否确认执行？</div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" class="cancel" @click="executeDialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" :loading="executeLoading" @click="handleExecute">确认执行</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="modelDialogTitle"
      :visible.sync="modelDialogVisible"
      custom-class="custom"
      width="480px"
      @close="closeDialog"
      @open="openDialog"
    >
      <DynamicForm ref="product" :model-dialog-visible="modelDialogVisible" :form-data="formData" :edit-row="editRow" />
      <span slot="footer" class="dialog-footer">
        <el-button size="small" class="cancel" @click="modelDialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="handleSave">确 定</el-button>
      </span>
      </DynamicForm>
    </el-dialog>
    <createDialog v-model="createDialogVisible" :graph-form.sync="graphForm" :edit-row="editRow" @emitHandle="emitHandle" />
    <viewDialog v-model="viewDialogVisible" :title="viewData.name" :view-data="viewData" />
    <objectEdit v-if="objectEditVisible" :id="id" v-model="relationData" :is-add="isAdd" :object-edit-visible="objectEditVisible" @save="save" @close="objectEditVisible=false" />
  </div>
</template>

<script>
import Tabs from "@/components/Tabs";
import objectEdit from "./component/objectEdit";
// import GraphR from "@/components/GraphR";
import createDialog from "./component/createDialog.vue";
import viewDialog from "./component/viewDialog.vue";
import { formateNodeColor } from "@/components/GraphR/formate";
import {
  modelPresetList,
  modelPresetEdit,
  modelGraphList,
  modelsCustomRun,
  modelGraphDelete,
  modelPresetResume,
  modelPresetPause,
  createCustom,
  editCustom,
  modelsCustomTry,
} from "@/api/subgraph";
import pagiNation from "@/mixins/pagiNation";
import DynamicForm from "@/components/DynamicForm";
import { processingTime } from "@/utils/global";
export default {
  name: "Subgraph",
  components: {
    Tabs,
    // GraphR,
    viewDialog,
    objectEdit,
    DynamicForm,
    createDialog,
  },
  mixins: [pagiNation],
  data() {
    return {
      lists: [
        { label: "预置模型", key: 1 },
        { label: "自定义模型", key: 2 },
      ],
      modelDialogTitle: "",
      active: 0,
      modelDialogVisible: false,
      objectEditVisible: false,
      createDialogVisible: false,
      delDialogVisible: false,
      executeDialogVisible: false,
      tableData: [],
      relationData: {
        rootId: "a",
        nodes: [
          {
            id: "a",
            text: "A",
            isHide: false,
            width: 1,
            height: 1,
            opacity: 0,
          },
        ],
        lines: [],
      },
      rowIndex: 0,
      loading: false,
      formData: [],
      isShow: false,
      id: "",
      crontab: "",
      editRow: {},
      graphForm: {
        name: "",
        severity: "",
        type: "",
        description: "",
        pattern: {
          vertices: [], // 点
          edges: [], // 边
          limit: 10,
        },
      },
      isAdd: true, // 默认新增
      delId: "",
      delLoading: false,
      executeLoading: false,
      viewDialogVisible: false,
      viewData: {},
    };
  },
  computed: {
    headerCellStyle() {
      return this.$store.state.user.theme
        ? { background: "rgba(250,250,252,1)", color: "#000" }
        : { background: "#0a2c4c", color: "#FFF" };
    },
    backgroundImage() {
      return this.$store.state.user.theme
        ? require(`@/assets/default-theme.png`)
        : require(`@/assets/dark-theme.png`);
    },
    dicts() {
      return this.$store.state.user.dicts;
    },
  },
  mounted() {
    this.getList();
  },
  methods: {
    clickTab() {
      this.page.pageSize = 10;
      this.page.page = 1;
      this.rowIndex = 0;
      this.getList();
    },
    // 确定
    async handleSave() {
      try {
        const data = this.$refs.product.submitForm();
        if (!Object.keys(data).length) {
          return false;
        }
        if (!data.interval) {
          this.$message.warning("请选择执行间隔");
          return false;
        }
        const params = {
          type: Number(data.type),
          interval: Number(data.interval),
          unit: Number(data.unit),
          params: [],
        };
        let notContain = ["type", "interval", "unit"];
        for (const key in data) {
          if (!notContain.includes(key) && data[key]) {
            params.params.push({
              param: key,
              type: key === "range" ? "range" : key,
              value: data[key],
            });
          }
        }
        await modelPresetEdit(this.id, params);
        this.modelDialogVisible = false;
        this.$message.success("修改成功");
        this.getList();
        this.editRow = {};
      } catch (error) {
        console.log(error);
      }
    },
    async getList() {
      try {
        this.loading = true;
        const res = this.active
          ? await modelGraphList({ ...this.page })
          : await modelPresetList({ ...this.page });
        this.tableData = res.data.data;
        this.tableData.forEach((item) => {
          item.severityCn = this.dicts.eventSeverity[item.severity];
          item.typeCn = this.dicts.classType[item.type];
          item.lastRunTime = processingTime(item.lastRunTime);
        });
        if (this.tableData.length) {
          this.id = this.tableData[0].id;
          this.rowIndex = 0;
        } else {
          this.id = "";
        }
        this.total = res.data.total;
        this.init();
        setTimeout(() => {
          this.loading = false;
        }, 500);
      } catch (error) {
        // 由于共用了一个表格，为异常时，需要数据清空和分页总条数为0,id也清空是因为需要更新右边的数据
        this.tableData = [];
        this.id = "";
        this.total = 0;
        this.loading = false;
      }
    },
    init() {
      const list = {
        rootId: "",
        nodes: [],
        lines: [],
      };
      if (this.tableData.length) {
        const data = this.tableData[this.rowIndex].pattern;
        list.rootId = "a";
        list.nodes = data.vertices.map((item, index) => {
          return {
            id: item.id,
            nodeType: item.type,
            text: this.dicts.vertices[item.type].name,
            data: {
              fields: this.dicts.vertices[item.type].properties,
              id: item.id,
              type: String(item.type), // type类型
              property: item.filters.map((x) => {
                return {
                  operator: String(x.operator),
                  property: x.property,
                  value: x.value,
                };
              }), // 属性
            },
            width: 70,
            height: 40,
          };
        });
        list.lines = data.edges.map((item, index) => {
          return {
            id: item.id,
            nodeType: item.type,
            from: item.srcId,
            to: item.dstId,
            text: this.dicts.edges[item.type].name,
            data: {
              props: item.filters.map((x) => {
                return {
                  operator: String(x.operator),
                  property: x.property,
                  value: x.value,
                };
              }),
              sourceType: item.srcType,
              destinationType: item.dstType,
              srcId: item.srcId,
              dstId: item.dstId,
              id: item.id,
              type: item.type,
            },
          };
        });
        formateNodeColor(list.nodes);
        this.relationData = list;
      } else {
        this.relationData = {
          rootId: "a",
          nodes: [
            {
              id: "a",
              text: "A",
              isHide: false,
              width: 1,
              height: 1,
              opacity: 0,
            },
          ],
          lines: [],
        };
      }
      // 当前索引为第一项且是编辑时
      if (!this.rowIndex && !this.isAdd) {
        this.$refs.GraphRef?.showGraph(this.relationData);
      }
    },
    handleEdit(row, index) {
      this.rowIndex = index;
      this.editRow = row;
      this.id = row.id;
      this.init();
      if (this.active) {
        // 自定义模型
        // this.createDialogVisible = true;
        this.objectEditVisible = true;
        this.isAdd = false;
      } else {
        // 预置模型
        this.modelDialogVisible = true;
        this.modelDialogTitle = "模型编辑";
        const params = row.params;
        // 编辑时组装表单数据
        // 现在有这种情况，当type是range时，就是一个数字范围选择组件
        this.formData = params?.map((item) => {
          return {
            prop: item.param,
            label: item.param,
            defaultValue: item.value,
            type: item.type === "range" ? "array" : "input",
            span: 24,
            rules: ["不能为空"],
          };
        });
      }
    },
    // 切换执行状态
    async change(row) {
      try {
        if (row.active) {
          await modelPresetResume(row.id);
        } else {
          await modelPresetPause(row.id);
        }
        this.$message({
          type: "success",
          message: "操作成功!",
        });
        this.getList();
      } catch (error) {
        console.log(error);
        // 失败也需要更新数据，因为状态已经修改过了
        this.getList();
      }
    },
    // 新建模型
    handleAdd() {
      this.editRow = {};
      this.isAdd = true;
      this.objectEditVisible = true;
      // this.createDialogVisible = true;
    },
    del(row) {
      this.delDialogVisible = true;
      this.delId = row.id;
    },
    // 删除
    async handleDel() {
      try {
        this.delLoading = true;
        await modelGraphDelete(this.delId);
        this.$message({
          type: "success",
          message: "删除成功!",
        });
        this.delLoading = false;
        this.delDialogVisible = false;
        this.getList();
      } catch (error) {
        this.delLoading = false;
        console.log(error);
      }
    },
    execute(row) {
      this.delId = row.id;
      this.executeDialogVisible = true;
    },
    // 执行
    async handleExecute() {
      try {
        this.executeLoading = true;
        await modelsCustomRun(this.delId);
        this.$message({
          type: "success",
          message: "执行成功!",
        });
        this.executeLoading = false;
        this.executeDialogVisible = false;
        this.getList();
      } catch (error) {
        this.executeLoading = false;
        console.log(error);
      }
    },
    // 点击行
    handleRowClick(row) {
      // 获取被点击行的索引
      const rowIndex = this.tableData.indexOf(row);
      this.rowIndex = rowIndex;
      this.id = row.id;
      this.init();
    },
    // 可视化编排保存
    save(params, type) {
      try {
        this.graphForm.pattern.vertices = [];
        this.graphForm.pattern.edges = [];
        const nodeIds = [];
        const edgeIds = [];
        let someCondition = false;
        const { lines, nodes } = params;
        // 节点
        nodes.forEach((item) => {
          if (Object.keys(item.data).length) {
            nodeIds.push({
              id: item.data.id,
              type: item.data.type,
            });
            this.graphForm.pattern.vertices.push({
              id: item.data.id,
              type: Number(item.data.type),
              filters:
                item.data?.property
                  .filter((x) => x.property)
                  .map((y) => ({
                    property: y.property,
                    operator: Number(y.operator),
                    value: y.value,
                  })) || [],
            });
          }
        });
        // 边
        lines.forEach((item) => {
          if (Object.keys(item.relations).length) {
            item.relations.forEach((relation) => {
              edgeIds.push(relation.data.srcId);
              edgeIds.push(relation.data.dstId);
              if (!relation.data?.type) {
                const { sourceType, destinationType } = relation.data;
                const sourceName = this.dicts.vertices[sourceType]?.name;
                const destinationName =
                  this.dicts.vertices[destinationType].name;
                this.$message.warning(
                  `节点【${sourceName}】与【${destinationName}】需设置关系`
                );
                someCondition = true;
                return;
              }
              this.graphForm.pattern.edges.push({
                id: relation.data.id,
                type: Number(relation.data.type),
                srcId: relation.data.srcId,
                dstId: relation.data.dstId,
                srcType: Number(relation.data.sourceType),
                dstType: Number(relation.data.destinationType),
                filters:
                  relation.data?.props
                    .filter((x) => x.property)
                    .map((y) => ({
                      property: y.property,
                      operator: Number(y.operator),
                      value: y.value,
                    })) || [],
              });
            });
          }
        });
        if (someCondition) {
          return false;
        }
        if (!edgeIds.length) {
          this.$message.warning("缺少节点关系");
          return;
        }
        if (nodeIds.length <= 1) {
          this.$message.warning("至少需要添加2个节点");
          return;
        }
        let findNode = nodeIds.find((node) => !edgeIds.includes(node.id));
        if (findNode?.type) {
          const name = this.dicts.vertices[findNode.type].name;
          this.$message.warning(`节点【${name}】未设置关系`);
          return;
        }
        if (type === "query") {
          this.graphForm.pattern.limit = 1;
          this.modelsCustomTry();
        } else if (type === "save") {
          this.graphForm.pattern.limit = 10;
          this.createDialogVisible = true;
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 查看
    handleView(row) {
      this.viewData = row;
      this.init();
      this.viewData.relationData = this.relationData;
      this.viewDialogVisible = true;
    },
    // 查询
    async modelsCustomTry() {
      try {
        const res = await modelsCustomTry({ ...this.graphForm.pattern });
        this.$message.success(res.data);
      } catch (error) {
        console.log(error);
      }
    },
    // 最终保存
    async emitHandle() {
      let params = JSON.parse(JSON.stringify(this.graphForm));
      params.severity = Number(params.severity);
      params.type = Number(params.type);
      // 编辑需要传id
      if (!this.isAdd) {
        await editCustom(this.id, params);
        this.$message.success("操作成功");
        this.createDialogVisible = false;
        this.objectEditVisible = false;
        this.getList();
      } else {
        await createCustom(params);
        this.$message.success("操作成功");
        this.createDialogVisible = false;
        this.objectEditVisible = false;
        this.getList();
      }
    },
    // 关闭弹窗
    closeDialog() {
      this.$refs.product.resetForm();
    },
    // 打开弹窗
    openDialog() {
      this.$nextTick(() => {
        this.$refs.product.initForm();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/mixin.scss";
.subgraph {
  height: 100%;
  @include flex();
  // background: url("../../assets/bg.png") no-repeat center;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  overflow: hidden;
  &-left {
    flex: 1;
    padding: $pd16;
    height: 100vh;
    box-sizing: border-box;
    overflow: auto;
  }
  &-right {
    width: 560px;
    height: 100%;
  }
  .el-pagination {
    text-align: right;
  }
}
::v-deep {
  .table {
    .el-loading-mask {
      background: rgba(57, 119, 205, 0.2) !important;
    }
    .el-table {
      margin: 16px 0;
      background: rgba(57, 119, 205, 0) !important;
      &::before {
        height: 0px;
      }
      // th {
      //   border: 1px solid var(--BorderThColor);
      // }
      .el-table__body-wrapper {
        .el-table__row {
          background: rgba(57, 119, 205, 0);
          .el-table__cell {
            border: none;
          }
          .cell {
            color: var(--TextColor);
          }
        }
      }
      th.el-table__cell.is-leaf {
        border-bottom: none;
      }
      .el-table__row:nth-child(odd) {
        td {
          background: var(--BackgroundColorOdd) !important;
        }
      }
      .el-table__row:nth-child(even) {
        td {
          background: var(--BackgroundColorEven) !important;
        }
      }
      // 当鼠标移入添加背景颜色
      .el-table__body {
        tr:hover > td {
          background: var(--BackgroundMostColor) !important;
        }
      }
    }
    .el-pagination {
      .btn-prev,
      .btn-next {
        background: var(--BackgroundMostColor);
      }
      .el-pager {
        .active {
          background: var(--BackgroundColor0) !important;
        }
      }
    }
    .el-button.is-disabled {
      color: var(--TextMostColor);
      opacity: 0.5;
    }
    .el-switch__core {
      background-color: var(--BackgroundColorSwitch);
      border-color: var(--BackgroundColorSwitch);
    }
  }
}
::v-deep {
  .el-dialog__header {
    padding: 16px;
    .el-dialog__title {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .el-dialog__body {
    padding: 0 16px;
  }
}
::v-deep .custom {
  border-radius: 8px;
  background: var(--BackgroundMostColor);
  .el-dialog__header {
    padding-bottom: 0;
    .icon-a-16_alert-copy {
      color: red;
      margin-right: 4px;
      font-size: 16px;
    }
    .el-dialog__title,
    .icon-a-16_alert-copy-name {
      color: var(--TextColor);
      font-size: 16px;
    }
    .el-dialog__headerbtn {
      color: #767684;
    }
  }
  .el-dialog__body {
    .el-form {
      .el-form-item__label {
        font-weight: normal;
        color: var(--TextColor);
        padding: 0;
      }
      .el-form-item__content {
        .el-input.is-disabled {
          .el-input__inner {
            background: var(--BackgroundColorDisabled);
            border-color: var(--BorderMajorColor);
          }
          .el-input-group__append {
            background: #4d4d50;
            border-color: var(--BorderMajorColor);
            span {
              color: var(--TextMostColor);
            }
          }
        }
        .el-input {
          .el-input__inner {
            background: var(--BorderInputColor);
            border: 1px solid var(--BorderInputColor);
            color: var(--TextColor);
          }
        }
        &:hover {
          .el-input.is-disabled {
            .el-input__inner {
              border-color: var(--BorderInputColor);
            }
            .el-input-group__append {
              border-color: var(--BorderInputColor);
            }
          }
          .el-input {
            .el-input__inner {
              border-color: var(--BorderInputColor);
            }
          }
        }
        .el-input__inner:focus {
          border-color: var(--BorderInputColor);
        }
      }
    }
  }
  .el-dialog__content {
    color: var(--TextColor);
    padding: 20px 0;
  }
  .el-dialog__footer {
    border-top: 1px solid var(--BorderMajorColor);
    padding: 16px;
    .cancel {
      background: var(--BackgroundMostColor);
      border-color: var(--BorderMajorColor);
      color: var(--TextColor);
      margin-right: 8px;
    }
  }
}
::v-deep .el-button--primary {
  background: var(--BackgroundColor0) !important;
  border-color: var(--BorderButtonColor) !important;
  &:hover {
    background: var(--BackgroundColorHover) !important;
    color: var(--TextColor) !important;
  }
}
[class^="el-icon-"] {
  padding: 6px;
  height: 27px;
  width: 62px;
  font-size: 12px;
  color: var(--TextColor);
  background: var(--BackgroundColorResult);
  border: 1px solid transparent;
  // border-radius: 4px;
  box-sizing: border-box;
  margin-right: 8px;
  &::before{
    margin-right: 8px;
  }
  &:hover {
    cursor: pointer;
  }
}
</style>
<style>
.el-tooltip__popper {
  max-width: 300px; /* 修改宽度 */
  line-height: 1.2rem;
}
</style>