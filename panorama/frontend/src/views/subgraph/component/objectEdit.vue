<template>
  <el-drawer
    size="68%"
    :with-header="false"
    :wrapper-closable="false"
    :close-on-press-escape="false"
    :visible.sync="objectEditVisible"
    :direction="direction"
  >
    <div class="container">
      <GraphR
        :id="id"
        :relation-data="relationData"
        :is-edit="true"
        :is-add="isAdd"
        @save="save"
        @close="close"
      />
    </div>
  </el-drawer>
</template>
  
<script>
import GraphR from "@/components/GraphR";
export default {
  name: "ObjectEdit",
  components: {
    GraphR,
  },
  props: {
    objectEditVisible: {
      type: Boolean,
      default: false,
    },
    value: {
      type: Object,
      default: () => {},
    },
    id:{
      type:String,
      default:''
    },
    isAdd:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      direction: "rtl",
      isShowNodeTipsPanel: false,
      nodeMenuPanelPosition: { x: 0, y: 0 },
      currentObjectType: null,
      currentObject: "",
      newNodeIdIndex: 1,
      newLineIdIndex: 1,
    };
  },
  computed: {
    relationData: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  methods: {
    // 保存
    save(params,type) {
      this.$emit("save", params,type);
    },
    close() {
      this.$emit("close");
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.container {
// background: #2C2C38;
  height: 100%;
}
</style>