<template>
  <el-dialog
    :title="title"
    :visible.sync="isShow"
    custom-class="custom"
    width="540px"
  >
    <el-form
      ref="formRef"
      :model="form"
      label-position="top"
      label-width="80px"
      :rules="formRules"
    >
      <el-form-item label="模型名称" prop="name">
        <el-input v-model="form.name" placeholder="模型名称"></el-input>
      </el-form-item>
      <el-form-item label="等级" prop="severity">
        <el-select
          v-model="form.severity"
          style="width: 100%"
          placeholder="等级"
        >
          <el-option
            v-for="(item, index) in Object.keys(dicts.eventSeverity)"
            :key="index"
            :label="dicts.eventSeverity[item]"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select
          v-model="form.type"
          style="width: 100%"
          placeholder="类型"
        >
          <el-option
            v-for="(item, index) in Object.keys(dicts.classType)"
            :key="index"
            :label="dicts.classType[item]"
            :value="item"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" placeholder="描述"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" style="text-align: right" class="dialog-footer">
      <el-button size="small" class="cancel" @click="isShow = false">取 消</el-button>
      <el-button size="small" type="primary" @click="handleSave">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>
  
<script>
import mixins from "@/mixins";
export default {
  name: "CreateDialog",
  mixins: [mixins],
  props: {
    editRow: {
      type: Object,
      default: () => {},
    },
    graphForm:{
      type:Object,
      default:()=>{}
    }
  },
  data() {
    return {
      id: "",
      formRules: {
        name: [
          { required: true, message: "请输入模型名称", trigger: "blur" },
        ],
        severity: [
          { required: true, message: "请选择等级", trigger: "change" },
        ],
        type: [
          { required: true, message: "请选择类型", trigger: "change" },
        ],
      },
    };
  },
  computed: {
    dicts() {
      return this.$store.state.user.dicts;
    },
    title() {
      return Object.keys(this.editRow).length ? "编辑模型" : "新建模型";
    },
    form:{
      get(){
        return this.graphForm;
      },
      set(val){
        this.$emit('update:graphForm',val);
      }
    }
  },
  watch: {
    isShow(val) {
      if (val) {
        if (Object.keys(this.editRow).length) {
          this.$nextTick(()=>{
            this.form.name = this.editRow.name;
            this.form.severity = String(this.editRow.severity);
            this.form.description = this.editRow.description;
            this.form.type=String(this.editRow.type);
            this.id = this.editRow.id;
          });
        }else{
          this.$nextTick(()=>{
            this.$refs.formRef.resetFields();
          });
        }
      }
    },
  },
  methods: {
    // 确定
    handleSave() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.$emit("emitHandle");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  }
};
</script>
  
  <style lang="scss" scoped>
::v-deep {
  .el-dialog__header {
    padding: 16px;
    .el-dialog__title {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .el-dialog__body {
    padding: 0 16px;
  }
}
</style>