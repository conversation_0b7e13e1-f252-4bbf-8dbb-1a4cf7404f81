<template>
  <el-dialog
    :title="title"
    :visible.sync="isShow"
    custom-class="custom"
    :show-close="false"
    width="720px"
  >
    <template #title>
      <div class="header">
        <div class="dialog-title">{{ title }}</div>
        <i class="el-icon-refresh other" @click="refresh">&nbsp;&nbsp;刷新</i>
      </div>
    </template>
    <div v-loading="loading" class="content">
      <GraphR :id="viewData.id" ref="GraphRef" :is-fefresh="false" :relation-data="viewData.relationData" />
    </div>
    <div slot="footer" style="text-align: right" class="dialog-footer">
      <el-button size="small" class="cancel" @click="isShow = false">
        关 闭
      </el-button>
    </div>
  </el-dialog>
</template>
  
<script>
import mixins from "@/mixins";
import GraphR from "@/components/GraphR";
export default {
  name: "ViewDialog",
  components: {
    GraphR,
  },
  mixins: [mixins],
  props: {
    editRow: {
      type: Object,
      default: () => {},
    },
    graphForm: {
      type: Object,
      default: () => {},
    },
    title: {
      type: String,
      default: "title",
    },
    viewData:{
      type:Object,
      default:()=>{}
    },
  },
  data() {
    return {
      loading:true
    };
  },
  watch: {
    isShow(val) {
      if(val){
        setTimeout(()=>{
          this.loading=false;
        },1000);
      }
    }
  },
  methods: {
    refresh() {
      this.$refs.GraphRef.relationGraph.refresh();
    },
  },
};
</script>
  
  <style lang="scss" scoped>
::v-deep {
  .custom{
    border-radius:8px;
  }
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .dialog-title {
      color: var(--TextColor);
    }
    [class^="el-icon-"] {
      padding: 8px;
      width: 62px;
      font-size: 12px;
      color: var(--TextColor);
      background: var(--BackgroundColorResult);
      border-radius: 4px;
      box-sizing: border-box;
      margin-right: 8px;
      &:hover {
        cursor: pointer;
        // background: #8abcff;
        // color: #fff;
      }
    }
  }
  .el-dialog__body {
    // padding: 0 16px;
    height: 480px;
    .content{
        height: 100%;
    }
  }
  .el-dialog__footer {
    border-top: 1px solid var(--BorderMajorColor);
    padding: 16px;
    .cancel {
      background: var(--BackgroundMostColor);
      border-color: var(--BorderMajorColor);
      color: var(--TextColor);
      margin-right: 8px;
    }
  }
}
</style>