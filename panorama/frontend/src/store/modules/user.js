import { getDict } from "@/api/subgraph";

const getDefaultState = () => {
  return {
    dicts: {},
    theme: true,
  };
};

const state = getDefaultState();

const mutations = {
  SET_DICT: (state,data) => {
    state.dicts = data;
  },
  SET_THEME: (state, theme) => {
    state.theme = theme;
  }
};

const actions = {
  async dict({ commit }) {
    const res = await getDict();
    commit("SET_DICT", res.data);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
