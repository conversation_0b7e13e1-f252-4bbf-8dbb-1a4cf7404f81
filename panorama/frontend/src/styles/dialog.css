.defaultTheme .custom {
  background: #f8f9fd;
}
.defaultTheme .el-dialog {
  background: #f8f9fd;
}

.defaultTheme .el-dialog__title {
  color: #2c2c35;
}

.defaultTheme .el-dialog__body .el-form-item__label {
  color: #2c2c35;
}

.defaultTheme .el-input .el-input__inner {
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(255, 255, 255, 1);
  color: #2c2c35;
}
.defaultTheme .el-input .el-input__inner:focus {
  border-color: rgba(255, 255, 255, 1);
}

.defaultTheme .el-input.is-disabled .el-input__inner {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(255, 255, 255, 1);
}

.defaultTheme .el-input.is-disabled .el-input-group__append {
  border-color: rgba(255, 255, 255, 1);
}

.defaultTheme .el-input.is-disabled .el-input-group__append span {
  color: #2c2c35;
}

.defaultTheme .el-dialog__footer {
  border-top: 1px solid #dee0e7;
}
.defaultTheme .el-dialog__footer .cancel {
  background: #f8f9fd;
  border-color: #dee0e7;
  color: rgb(31, 34, 37);
}

.defaultTheme .el-button--primary {
  background: #000 !important;
  border-color: rgba(224, 224, 230) !important;
}
.defaultTheme .el-button--primary:hover{
  background: #fff !important;
  color: rgb(31, 34, 37) !important;
}

.defaultTheme .el-icon-delete{
  color: rgb(31, 34, 37);
}
.blackTheme .el-dialog {
  background: #2c2c35;
}
.blackTheme .el-icon-delete{
  color: #fff;
}

.blackTheme .el-dialog__title {
  color: #dee0e7;
}

.blackTheme .el-dialog__body .el-form-item__label {
  color: #dee0e7;
}

.blackTheme .el-input .el-input__inner {
  background: rgba(255, 255, 255, .1);
  border: 1px solid rgba(255, 255, 255, .1);
  color: #dee0e7;
}

.blackTheme .el-input.is-disabled .el-input__inner {
  background: rgba(255, 255, 255, .1);
  border-color: rgba(255, 255, 255, .1);
}
.blackTheme .el-input .el-input__inner:focus {
  border-color: rgba(255, 255, 255, .1);
}
.blackTheme .el-input.is-disabled .el-input-group__append {
  border-color: rgba(255, 255, 255, .1);
}

.blackTheme .el-input.is-disabled .el-input-group__append span {
  color: #dee0e7;
}

.blackTheme .el-dialog__footer {
  border-top: 1px solid #767684;
}
.blackTheme .el-dialog__footer .cancel {
  background: #2c2c35 !important;
  border-color: #767684 !important;
  color: #dee0e7 !important;
}

.blackTheme .custom {
  background: #2c2c35;
}

.blackTheme .el-button--primary {
  background: #000 !important;
  border-color: rgba(255, 255, 255, 0.24) !important;
}
.blackTheme .el-button--primary:hover{
  background: hsl(218.8, 73.5%, 47.2%) !important;
  color: #fff !important;
}