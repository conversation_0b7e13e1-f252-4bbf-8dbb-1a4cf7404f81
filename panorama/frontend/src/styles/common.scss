::v-deep .custom {
  border-radius: 8px;

  .el-dialog__header {
    padding-bottom: 0;

    .el-dialog__title {
      font-size: 16px;
    }

    .el-dialog__headerbtn {
      color: #767684;
    }
  }

  .el-dialog__body {
    .el-form {
      .el-form-item__label {
        padding: 0;
        font-weight: normal;
      }

      .el-form-item__content {
        .el-input.is-disabled {
          .el-input-group__append {
            background: #4D4D50;
          }
        }

        &:hover {
          .el-input.is-disabled {
            .el-input__inner {
              border-color: #3977cd;
            }

            .el-input-group__append {
              border-color: #3977cd;
            }
          }

          .el-input {
            .el-input__inner {
              border-color: #3977cd;
            }
          }
        }

        .el-input__inner:focus {
          border-color: #3977cd;
        }
      }
    }
  }

  .el-dialog__footer {
    padding: 16px;
  }
}