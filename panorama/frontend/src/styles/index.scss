// @import './variables.scss';
// @import './mixin.scss';

@font-face {
  font-family: "alpht";
  src: url('../assets/Alibaba-PuHuiTi-Regular.ttf');
  font-weight: normal;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: "alpht";
  padding-right: 0px !important;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
  overflow-y: hidden;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}


scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

scrollbar-thumb {
  background-color: #e7e7e7;
  border-radius: 3px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

// /* 滑块样式 */
::-webkit-scrollbar-thumb {
  background-color: rgba(44, 44, 53, .3);
  border-radius: 4px;
}

/* 滚动条轨道样式 */
::-webkit-scrollbar-track {
  background-color: #fff;
  border-radius: 4px;
}

.el-table__body-wrapper:hover::-webkit-scrollbar {
  cursor: pointer;
  width: 8px;
  height: 8px;
  background-color: rgba(44, 44, 53, .5);
}

.el-table__body-wrapper:hover::-webkit-scrollbar-thumb {
  // background-color: #666;
  cursor: pointer;
}

.el-table__body-wrapper:hover::-webkit-scrollbar-track {
  // background-color: #000;
  cursor: pointer;
}

.custom {
  border-radius: 8px;
}