@mixin clearfix {
  &:after {
    content: "";
    display: table;
    clear: both;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative { 
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin flex($flex-direction:row,$justify-content:'space-between'){
  display: flex;
  flex-direction: $flex-direction;
  justify-content: $justify-content;
}

$pd16: 16px;
// $color: #F2F3F7;
// $primary-color: #116EF9;
// $bg-color:#0F0F13;
