import Vue from 'vue';
import Env from '@/utils/env';
Vue.use(Env);

import "@/assets/font/iconfont.css";
import 'normalize.css/normalize.css';
import RelationGraph from 'relation-graph';
Vue.use(RelationGraph);
import ElementUI from 'element-ui';
import "@/assets/themes/index.scss";
import '@/styles/index.scss';
import '@/styles/element-variables.scss';
import "@/styles/dialog.css";
import { message } from '@/utils/resetMessage';
import App from './App';
import store from './store';
import router from './router';

import '@/permission';
import * as global from '@/utils/global';
for (const key in global) {
  Vue.prototype[`$${key}`] = global[key];
}
Vue.use(ElementUI);
Vue.config.productionTip = false;
Vue.prototype.$message = message;

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
});