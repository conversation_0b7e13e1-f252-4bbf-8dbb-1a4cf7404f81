import os

from kafka import KafkaProducer

KAFKA_SERVERS = ['***************:9092']
KAFKA_TOPIC = 'agg_judgment_alert_log_subdomain'
pb_dir = '/Users/<USER>/GeekSec/08-测试数据/mock_data/attack_target(sip_vip_atmsg)/**************-**************-URL跳转'

producer = KafkaProducer(bootstrap_servers=KAFKA_SERVERS)
processed_files = set()
#记录处理了的文件
while True:
    for filename in os.listdir(pb_dir):
        if filename.endswith('.pb') and filename not in processed_files:
            file_path = os.path.join(pb_dir, filename)

            # 读取PB文件
            with open(file_path, 'rb') as f:
                pb_data = f.read()

            # 发送PB数据到Kafka
            producer.send(KAFKA_TOPIC, pb_data)
            producer.flush()

            print(f"已发送文件 {filename} 到Kafka")

            # 将文件标记为已处理
            processed_files.add(filename)

