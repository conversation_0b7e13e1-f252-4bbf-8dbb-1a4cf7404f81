import hashlib
import ipaddress
import json
import os
import random
import socket
import struct
import time
from datetime import datetime, timedelta

import faker
from faker import Faker
from kafka import KafkaProducer

import panorama_result_pb2
import protocol_pb2


def get_random_timestamp_last_month():
    # 获取当前时间
    now = datetime.now()

    # 计算一个月前的时间
    one_month_ago = now - timedelta(days=30)

    # 生成一个介于一个月前和现在之间的随机时间
    random_date = one_month_ago + timedelta(seconds=random.randint(0, int((now - one_month_ago).total_seconds())))

    # 转换为毫秒级时间戳
    timestamp_ms = int(random_date.timestamp() * 1000)

    return timestamp_ms

class AlertGenerator:
    KAFKA_SERVERS = ['***************:9092']
    KAFKA_TOPIC = 'agg_judgment_alert_log_subdomain'

    DETECT_TYPES = {
        "IOC_ALERT_INFO": 61,
        "FILE_ALERT_LOG_META": 62,
        "SANDBOX_ALERT_LOG": 63,
        "INC_ALERT_LOG": 64,
        "TELE_ALERT_LOG": 65,
        "EMAIL_ALERT_LOG": 66
    }

    def __init__(self,sip,dip,domain,threat_type,send_email="",rec_email="",file_md5=""):
        """

        :rtype: object
        """
        self.fake = Faker()
        # 保证只有一个PB对象
        self.alert = panorama_result_pb2.ALERT_LOG()
        self.ioc = protocol_pb2.MetaInfo()
        self.producer = KafkaProducer(bootstrap_servers=self.KAFKA_SERVERS)
        self.sip = sip
        self.dip = dip
        self.domain = domain
        self.threat_type = threat_type
        self.send_email = send_email
        self.rec_email = rec_email
        self.file_md5 = file_md5

    def generate_alert(self):
        detect_type = random.choice(list(self.DETECT_TYPES.values()))
        #detect_type = 61
        self._set_base_fields(detect_type)
        # if detect_type == 61:#判断是否为IOC类型
        #     self._set_ioc_fields()
        # if detect_type == 66:
        #     self._set_mail_fields()
        # if detect_type == 62:
        #     self._set_file_fields()
        # if detect_type == 63:
        #     self._set_sandbox_fields()
        # if detect_type == 64:
        #     self._set_inc_fields()
        # if detect_type == 65:
        #     self._set_tele_fields()
        type_method_map = {
            61: self._set_ioc_fields,
            66: self._set_mail_fields,
            62: self._set_file_fields,
            63: self._set_sandbox_fields,
            64: self._set_inc_fields,
            65: self._set_tele_fields
        }
        set_fields_method = type_method_map.get(detect_type)
        if set_fields_method:
            set_fields_method()

    def _set_base_fields(self,detect_type):
        #设置除了IOC报警以外的公共字段
        self.alert.log_guid = int(time.time() * 1000) + self.fake.random_int(min=1, max=50)
        self.alert.time = get_random_timestamp_last_month()
        self.alert.task_id = self.fake.uuid4()
        self.alert.rule_id = self.fake.uuid4()

        self.alert.ioc_tag = random.choice(["malware", "phishing", "c2", "exfiltration", "ransomware",
        "trojan", "botnet", "spyware", "exploit", "ddos",
        "cryptomining", "backdoor", "apt", "keylogger", "rootkit"])

        self.alert.iob_tag = random.choice(["malware", "phishing", "c2", "exfiltration", "ransomware",
        "trojan", "botnet", "spyware", "exploit", "ddos",
        "cryptomining", "backdoor", "apt", "keylogger", "rootkit"])

        self.alert.ioa_tag = random.choice(["malware", "phishing", "c2", "exfiltration", "ransomware",
        "trojan", "botnet", "spyware", "exploit", "ddos",
        "cryptomining", "backdoor", "apt", "keylogger", "rootkit"])

        self.alert.user_tag = random.choice(["malware", "phishing", "c2", "exfiltration", "ransomware",
        "trojan", "botnet", "spyware", "exploit", "ddos",
        "cryptomining", "backdoor", "apt", "keylogger", "rootkit"])

        self.alert.sip_info.ip = self.sip
        self.alert.sip_info.port = self.fake.random_int(min=1,max=65535)
        self.alert.sip_info.ip_country = self.fake.country()
        #根据不同国家生成对应的信息
        if self.alert.sip_info.ip_country == 'United States':
            self.alert.sip_info.ip_stat = self.fake.state()
            self.alert.sip_info.ip_city = self.fake.city()
        if self.alert.sip_info.ip_country == 'China':
            self.alert.sip_info.ip_stat = self.fake.province()
            self.alert.sip_info.ip_city = self.fake.city()
        else:
            self.alert.sip_info.ip_stat = self.fake.state()
            self.alert.sip_info.ip_city = self.fake.city()
        self.alert.sip_info.ip_org = random.choice(['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Group', 'Association', 'Foundation'])
        self.alert.sip_info.ip_longitude = self.fake.longitude()
        self.alert.sip_info.ip_latitude = self.fake.latitude()

        self.alert.sip_info.ip_isp = random.choice(["Comcast", "AT&T", "Verizon", "Spectrum", "CenturyLink",
            "Cox Communications", "Charter", "Frontier", "Windstream",
            "Mediacom", "Optimum", "Suddenlink", "EarthLink", "RCN",
            "Deutsche Telekom", "Orange", "Telefónica", "Vodafone",
            "China Mobile", "China Telecom", "NTT", "KDDI", "SoftBank"])
        self.alert.sip_info.ip_asn = 'test'

        self.alert.dip_info.ip = self.dip
        self.alert.dip_info.port = self.fake.random_int(min=1, max=65535)
        self.alert.dip_info.ip_country = self.fake.country()
        # 根据不同国家生成对应的信息
        if self.alert.dip_info.ip_country == 'United States':
            self.alert.dip_info.ip_stat = self.fake.state()
            self.alert.dip_info.ip_city = self.fake.city()
        if self.alert.dip_info.ip_country == 'China':
            self.alert.dip_info.ip_stat = self.fake.province()
            self.alert.dip_info.ip_city = self.fake.city()
        else:
            self.alert.dip_info.ip_stat = self.fake.state()
            self.alert.dip_info.ip_city = self.fake.city()
        self.alert.dip_info.ip_org = random.choice(
            ['Inc.', 'LLC', 'Corp.', 'Ltd.', 'Group', 'Association', 'Foundation'])
        self.alert.dip_info.ip_longitude = self.fake.longitude()
        self.alert.dip_info.ip_latitude = self.fake.latitude()

        self.alert.dip_info.ip_isp = random.choice(["Comcast", "AT&T", "Verizon", "Spectrum", "CenturyLink",
                                                    "Cox Communications", "Charter", "Frontier", "Windstream",
                                                    "Mediacom", "Optimum", "Suddenlink", "EarthLink", "RCN",
                                                    "Deutsche Telekom", "Orange", "Telefónica", "Vodafone",
                                                    "China Mobile", "China Telecom", "NTT", "KDDI", "SoftBank"])
        self.alert.dip_info.ip_asn = 'test'

        self.alert.aip_info.ip = self.alert.sip_info.ip
        self.alert.aip_info.port = self.alert.sip_info.port
        self.alert.aip_info.ip_country = self.alert.sip_info.ip_country
        self.alert.aip_info.ip_stat = self.alert.sip_info.ip_stat
        self.alert.aip_info.ip_city = self.alert.sip_info.ip_city
        self.alert.aip_info.ip_org = self.alert.sip_info.ip_org
        self.alert.aip_info.ip_longitude = self.alert.sip_info.ip_longitude
        self.alert.aip_info.ip_latitude = self.alert.sip_info.ip_latitude
        self.alert.aip_info.ip_isp = self.alert.sip_info.ip_isp
        self.alert.aip_info.ip_asn = self.alert.sip_info.ip_asn

        self.alert.vip_info.ip = self.alert.dip_info.ip
        self.alert.vip_info.port = self.alert.dip_info.port
        self.alert.vip_info.ip_country = self.alert.dip_info.ip_country
        self.alert.vip_info.ip_stat = self.alert.dip_info.ip_country
        self.alert.vip_info.ip_city = self.alert.dip_info.ip_city
        self.alert.vip_info.ip_org = self.alert.dip_info.ip_org
        self.alert.vip_info.ip_longitude = self.alert.dip_info.ip_longitude
        self.alert.vip_info.ip_latitude = self.alert.dip_info.ip_latitude
        self.alert.vip_info.ip_isp = self.alert.dip_info.ip_isp
        self.alert.vip_info.ip_asn = self.alert.dip_info.ip_asn

        self.alert.LR_aggregate_value = 'test'
        self.alert.LR_first_alert_date = self.alert.time
        self.alert.LR_last_alert_date = self.alert.time
        self.alert.LR_alert_time = int(self.fake.unix_time())
        self.alert.detect_type = detect_type
        # self.alert.threat_type = random.choice([1,2,3,4,5])
        self.alert.threat_type = threat_type#SQL注入

        self.alert.severity = random.choice([1,2,3,4,5])
        self.alert.confidence = 80
        self.alert.white_list = 80
        self.alert.black_list = 80
        self.alert.trans_proto = random.choice([6,17])
        self.alert.meta_data = b'test'
        self.alert.raw_data_path = self.fake.file_path()
        self.alert.alert_data_path = self.fake.file_path()
        self.alert.sensor_ip = self.fake.ipv4()
        self.alert.vendor_id = random.choice(['Bosch','Honeywell','Omron','Siemens','Amphenol','Panasonic'])

    # def _set_ioc_fields(self):
    #     self.alert.ioc_alert_info.id = str(self.fake.uuid4())
    #     self.alert.ioc_alert_info.malicious_type = self.fake.word()
    #     self.alert.ioc_alert_info.campaign_info.campaign = self.fake.word()
    #     self.alert.ioc_alert_info.campaign_info.first_detection_date = str(self.fake.date())
    #     self.alert.ioc_alert_info.campaign_info.attack_method = random.choice(["SQL Injection", "Cross-Site Scripting", "Cross-Site Request Forgery",
    #         "Buffer Overflow", "Brute Force", "Dictionary Attack",
    #         "Man-in-the-Middle", "Denial of Service", "Distributed Denial of Service",
    #         "Password Sniffing", "Phishing", "Spear Phishing", "Watering Hole Attack",
    #         "Session Hijacking", "DNS Cache Poisoning", "ARP Spoofing",
    #         "Zero-day Exploit", "Social Engineering", "Keylogging",
    #         "Driver Manipulation", "Remote Code Execution", "File Inclusion Exploit",
    #         "Command Injection", "Directory Traversal", "Side-Channel Attack",
    #         "Replay Attack", "Clickjacking", "Integer Overflow",
    #         "Format String Attack", "Privilege Escalation"])
    #     self.alert.ioc_alert_info.campaign_info.rat = 'test'
    #     self.alert.ioc_alert_info.campaign_info.actor = 'test'
    #     self.alert.ioc_alert_info.campaign_info.responsible_country = self.fake.country()
    #     self.alert.ioc_alert_info.campaign_info.alias = self.fake.word()
    #     self.alert.ioc_alert_info.campaign_info.report_date = str(self.fake.date())
    #     self.alert.ioc_alert_info.campaign_info.create_time = str(self.fake.date())
    #     self.alert.ioc_alert_info.campaign_info.primary_name = self.fake.last_name()
    #     self.alert.ioc_alert_info.campaign_info.affected_sector = 'test'
    #     self.alert.ioc_alert_info.campaign_info.front_description = ''.join(self.fake.words())
    #     self.alert.ioc_alert_info.campaign_info.reference_link = self.fake.url()
    #     self.alert.ioc_alert_info.campaign_info.report_vendor = 'test'
    #     self.alert.ioc_alert_info.campaign_info.reference_document = 'test'
    #     self.alert.ioc_alert_info.campaign_info.affected_system = random.choice(['Windows','Mac','IOS','Andriod'])
    #     self.alert.ioc_alert_info.campaign_info.related_vulnerability = 'test'
    #     self.alert.ioc_alert_info.malicious_family_info.malicious_family = 'test'
    #     self.alert.ioc_alert_info.malicious_family_info.risk = 'test'
    #     self.alert.ioc_alert_info.malicious_family_info.platform = random.choice(['Windows','Mac','IOS','Andriod'])
    #     self.alert.ioc_alert_info.malicious_family_info.description = ''.join(self.fake.words())
    #     self.alert.ioc_alert_info.malicious_family_info.reference = 'test'
    #     self.alert.ioc_alert_info.current_status = 'test'
    #     self.alert.ioc_alert_info.confidence = '80'
    #     self.alert.ioc_alert_info.hazard_level = 'test'
    #     self.alert.ioc_alert_info.tag = 'test'
    #     self.alert.ioc_alert_info.kill_chain = random.choice(["Reconnaissance",
    #         "Weaponization",
    #         "Delivery",
    #         "Exploitation",
    #         "Installation",
    #         "Command and Control",
    #         "Actions on Objectives"])
    #     self.alert.ioc_alert_info.targeted = random.choice([True,False])
    #     self.alert.ioc_alert_info.protocol = random.choice(['HTTP','DNS'])
    #     print(f"协议类型：{self.alert.ioc_alert_info.protocol}")
    #     if self.alert.ioc_alert_info.protocol == 'HTTP':
    #         self.alert.ioc_alert_info.meta_info = json.dumps(self._ioc_http_meta()).encode('UTF-8')
    #         self.alert.ioc_alert_info.meta_info_type = 11
    #     if self.alert.ioc_alert_info.protocol == 'DNS':
    #         self.alert.ioc_alert_info.meta_info = json.dumps(self._ioc_dns_meta()).encode('UTF-8')
    #         self.alert.ioc_alert_info.meta_info_type = 28


    def _set_ioc_fields(self):
        self.alert.ioc_alert_info.id = str(self.fake.uuid4())
        self.alert.ioc_alert_info.malicious_type = self.fake.word()
        self.alert.ioc_alert_info.campaign_info.campaign = self.fake.word()
        self.alert.ioc_alert_info.campaign_info.first_detection_date = str(self.fake.date())
        self.alert.ioc_alert_info.campaign_info.attack_method = random.choice(["SQL Injection", "Cross-Site Scripting", "Cross-Site Request Forgery",
                "Buffer Overflow", "Brute Force", "Dictionary Attack",
                "Man-in-the-Middle", "Denial of Service", "Distributed Denial of Service",
                "Password Sniffing", "Phishing", "Spear Phishing", "Watering Hole Attack",
                "Session Hijacking", "DNS Cache Poisoning", "ARP Spoofing",
                "Zero-day Exploit", "Social Engineering", "Keylogging",
                "Driver Manipulation", "Remote Code Execution", "File Inclusion Exploit",
                "Command Injection", "Directory Traversal", "Side-Channel Attack",
                "Replay Attack", "Clickjacking", "Integer Overflow",
                "Format String Attack", "Privilege Escalation"])
        self.alert.ioc_alert_info.campaign_info.rat = 'test'
        self.alert.ioc_alert_info.campaign_info.actor = 'test'
        self.alert.ioc_alert_info.campaign_info.responsible_country = self.fake.country()
        self.alert.ioc_alert_info.campaign_info.alias = self.fake.word()
        self.alert.ioc_alert_info.campaign_info.report_date = str(self.fake.date())
        self.alert.ioc_alert_info.campaign_info.create_time = str(self.fake.date())
        self.alert.ioc_alert_info.campaign_info.primary_name = self.fake.last_name()
        self.alert.ioc_alert_info.campaign_info.affected_sector = 'test'
        self.alert.ioc_alert_info.campaign_info.front_description = ''.join(self.fake.words())
        self.alert.ioc_alert_info.campaign_info.reference_link = self.fake.url()
        self.alert.ioc_alert_info.campaign_info.report_vendor = 'test'
        self.alert.ioc_alert_info.campaign_info.reference_document = 'test'
        self.alert.ioc_alert_info.campaign_info.affected_system = random.choice(['Windows','Mac','IOS','Andriod'])
        self.alert.ioc_alert_info.campaign_info.related_vulnerability = 'test'
        self.alert.ioc_alert_info.malicious_family_info.malicious_family = 'test'
        self.alert.ioc_alert_info.malicious_family_info.risk = 'test'
        self.alert.ioc_alert_info.malicious_family_info.platform = random.choice(['Windows','Mac','IOS','Andriod'])
        self.alert.ioc_alert_info.malicious_family_info.description = ''.join(self.fake.words())
        self.alert.ioc_alert_info.malicious_family_info.reference = 'test'
        self.alert.ioc_alert_info.current_status = 'test'
        self.alert.ioc_alert_info.confidence = '80'
        self.alert.ioc_alert_info.hazard_level = 'test'
        self.alert.ioc_alert_info.tag = 'test'
        self.alert.ioc_alert_info.kill_chain = random.choice(["Reconnaissance",
                "Weaponization",
                "Delivery",
                "Exploitation",
                "Installation",
                "Command and Control",
                "Actions on Objectives"])
        self.alert.ioc_alert_info.targeted = random.choice([True,False])
        self.alert.ioc_alert_info.protocol = random.choice(['HTTP','DNS','X509'])
        #self.alert.ioc_alert_info.protocol = 'DNS'
        print(f"协议类型：{self.alert.ioc_alert_info.protocol}")
        if self.alert.ioc_alert_info.protocol == 'HTTP':
            self.alert.ioc_alert_info.meta_info = self._ioc_http_meta().SerializeToString()
            self.alert.ioc_alert_info.meta_info_type = 11
        if self.alert.ioc_alert_info.protocol == 'DNS':
           self.alert.ioc_alert_info.meta_info = self._ioc_dns_meta().SerializeToString()
           self.alert.ioc_alert_info.meta_info_type = 28
        if self.alert.ioc_alert_info.protocol == 'X509':
            self.alert.ioc_alert_info.meta_info = self._ioc_cert_meta().SerializeToString()
            self.alert.ioc_alert_info.meta_info_type = 31


    def _ioc_http_meta(self):
        # self._http_meta = {"meta_info": {
        #     "proto_types": 11,
        #     "basic_info": {
        #         "basic_struct": {
        #             "flow_id": (b'123').decode('utf-8'),
        #             "src_ip": self.alert.sip_info.ip,
        #             "dst_ip": self.alert.dip_info.ip,
        #             "src_port": self.alert.sip_info.port,
        #             "dst_port": self.alert.dip_info.port,
        #
        #         }
        #     },
        #     "http_info": {
        #         "method": random.choice(["GET", "POST", "PUT"]),
        #         "uri": self.fake.uri(),
        #     }
        # }
        # }
        # return self._http_meta
        self.ioc.proto_types.append(11)
        self.ioc.basic_info.basic_struct.flow_id = b'123'
        # self.ioc.basic_info.basic_struct.src_ip = struct.unpack("!I",socket.inet_aton(self.alert.sip_info.ip))[0]
        # self.ioc.basic_info.basic_struct.dst_ip = struct.unpack("!I",socket.inet_aton(self.alert.dip_info.ip))[0]
        self.ioc.basic_info.basic_struct.src_ip = 123
        self.ioc.basic_info.basic_struct.dst_ip = 123
        self.ioc.basic_info.basic_struct.src_port = (self.alert.sip_info.port)
        self.ioc.basic_info.basic_struct.dst_port = (self.alert.dip_info.port)
        http_info = self.ioc.http_info.add()
        http_info.method = random.choice(["GET", "POST", "PUT"])
        http_info.uri = self.fake.uri()
        http_info.uri_key = self.fake.uuid4()
        http_info.uri_path = self.fake.word()
        http_info.version_src = random.choice(["HTTP/1.0", "HTTP/1.1", "HTTP/2", "HTTP/3"])
        http_info.host = self.domain
        http_info.user_agent = self.fake.user_agent()
        http_info.referer = self.fake.word()
        http_info.accept_encoding = random.choice(["gzip",
        "deflate",
        "br",
        "identity",
        "UTF-8",
        "ISO-8859-1",
        "ASCII",
        "UTF-16",
        "Windows-1252",
        "EUC-JP",
        "Shift_JIS",
        "GBK",
        "Big5"])
        http_info.accept_language = self.fake.language_name()
        http_info.location = f"{self.fake.city()}, {self.fake.country()}"


        return self.ioc



    def _ioc_dns_meta(self):
        self.ioc.proto_types.append(28)
        self.ioc.basic_info.basic_struct.flow_id = b'123'
        # self.ioc.basic_info.basic_struct.src_ip = struct.unpack("!I",socket.inet_aton(self.alert.sip_info.ip))[0]#转换成IP格式
        # self.ioc.basic_info.basic_struct.dst_ip = struct.unpack("!I",socket.inet_aton(self.alert.dip_info.ip))[0]
        self.ioc.basic_info.basic_struct.src_port = (self.alert.sip_info.port)
        self.ioc.basic_info.basic_struct.dst_port = (self.alert.dip_info.port)
        dns_info = self.ioc.dns_info.add()
        dns_info.query = self.domain
        dns_info.answer_types = random.choice(["A","AAAA","CNAME","MX", "NS",])
        dns_info.flags = random.choice([1,2,3,4,5,6,7])
        dns_info.aip_country = self.fake.country()
        dns_info.cname = ''
        dns_info.cname_cnt = 0
        #dns_info.aip = ipaddress.IPv4Address(self.dip)
        dns_info.aip = struct.unpack("!I",socket.inet_aton(self.dip))[0]
        dns_info.ns_host = self.fake.hostname()
        dns_info.ns_country = self.fake.country()
        dns_info.mx_host = self.fake.hostname()
        dns_info.mx_country = self.fake.country()
        # self.ioc.dns_info.query = self.domain
        # self.ioc.dns_info.answer_types = "www.baidu.com"

        return self.ioc

    def _ioc_cert_meta(self):
        self.ioc.proto_types.append(31)
        self.ioc.basic_info.basic_struct.flow_id = b'123'
        self.ioc.basic_info.basic_struct.src_port = (self.alert.sip_info.port)
        self.ioc.basic_info.basic_struct.dst_port = (self.alert.dip_info.port)
        x509_info = self.ioc.x509_info.add()
        x509_info.issuer_on = self.fake.domain_name()
        x509_info.subject_on = self.fake.company()
        x509_info.fingerprint = hashlib.sha1(os.urandom(1024)).digest()

        return self.ioc



    def _set_mail_fields(self):
        self.alert.email_alert_info.mail_sender = self.send_email
        self.alert.email_alert_info.mail_receiver = self.rec_email


    # def _ioc_dns_meta(self):
    #     self._dns_meta = {
    #         "meta_info": {
    #             "proto_types": 28,
    #             "basic_info": {
    #                 "basic_struct": {
    #                     "flow_id": (b'123').decode('utf-8'),
    #                     "src_ip": self.alert.sip_info.ip,
    #                     "dst_ip": self.alert.dip_info.ip,
    #                     "src_port": self.alert.sip_info.port,
    #                     "dst_port": self.alert.dip_info.port,
    #
    #                 }
    #             },
    #             "dns_info": {
    #                 "query": "123",
    #                 "answer_types": "www.baidu.com",
    #             }
    #         }
    #     }
    #     return self._dns_meta
    def send_to_kafka(self):
        serialized_data = self.alert.SerializeToString()
        self.producer.send(self.KAFKA_TOPIC, serialized_data)
        self.producer.flush()

    def print_alert_info(self):
        print(f"源IP: {self.alert.sip_info.ip}")
        print(f"目的IP：{self.alert.dip_info.ip}")
        print(f"类型为： {self.alert.detect_type}")
        print(f"发送邮件为：{self.alert.email_alert_info.mail_sender}")
        print(f"接收邮件为：{self.alert.email_alert_info.mail_receiver}")
        print(f"时间为：{self.alert.time}")

    def _set_file_fields(self):
        self.alert.file_alert_info.file_md5 = self.file_md5
        self.alert.file_alert_info.file_type = "txt"
        self.alert.file_alert_info.malicious = True
        self.alert.file_alert_info.targeted = True
        self.alert.file_alert_info.associated_value = "test"

    def _set_sandbox_fields(self):
        self.alert.sandbox_alert_info.file_name = 'test.txt'
        self.alert.sandbox_alert_info.file_size = 5
        self.alert.sandbox_alert_info.file_md5 = self.file_md5
        self.alert.sandbox_alert_info.file_type = 'txt'
        self.alert.sandbox_alert_info.file_path = self.fake.file_path()
        self.alert.sandbox_alert_info.associated_value = 'test'
        self.alert.sandbox_alert_info.meta_info = b'test'
        self.alert.sandbox_alert_info.mail_sender = self.send_email
        self.alert.sandbox_alert_info.mail_receiver = self.rec_email
        self.alert.sandbox_alert_info.mail_subject = 'abc'

    def _set_inc_fields(self):
        self.alert.industry_iot_alert_info.pkt =b'test'
        self.alert.industry_iot_alert_info.protocol = 'TCP'
        self.alert.industry_iot_alert_info.protocol_type = 1
        self.alert.industry_iot_alert_info.count = 1
        self.alert.industry_iot_alert_info.meta = '{"test":"test"}'
        self.alert.industry_iot_alert_info.action = 'test'
        self.alert.industry_iot_alert_info.err_type = 1
        self.alert.industry_iot_alert_info.is_key = True
        self.alert.industry_iot_alert_info.is_attack = True
        self.alert.industry_iot_alert_info.is_vul = True
        self.alert.industry_iot_alert_info.event_type = 'APT'
        self.alert.industry_iot_alert_info.event_desc = 'test'
        self.alert.industry_iot_alert_info.event_level = 1

    def _set_tele_fields(self):
        self.alert.tele_alert_info.rule_type = 1
        self.alert.tele_alert_info.alarm_operation_code = 2
        self.alert.tele_alert_info.alarm_source = 'TELE'
        self.alert.tele_alert_info.datetimes = '2024'
        self.alert.tele_alert_info.alarm_id = '123'
        self.alert.tele_alert_info.alarm_level = 1
        self.alert.tele_alert_info.threat_source_node = 'test'
        self.alert.tele_alert_info.threat_source_country = 'China'
        self.alert.tele_alert_info.threat_source_operator = 'CNC'
        self.alert.tele_alert_info.threatned_node = 'test'
        self.alert.tele_alert_info.threatned_country = 'China'
        self.alert.tele_alert_info.threatned_operator = 'CNC'
        self.alert.tele_alert_info.alarm_reson = 'attack'
        self.alert.tele_alert_info.threat_source_id = 123
        self.alert.tele_alert_info.imsi = 'test'
        self.alert.tele_alert_info.msisdn = 'test'
        self.alert.tele_alert_info.target_country = 'China'
        self.alert.tele_alert_info.target_operator = 'CNC'
        self.alert.tele_alert_info.alarm_effect = 2

    def save_pb(self):
        data = self.alert.SerializeToString()
        return data

if __name__ == '__main__':
    def process_alert(ip_src, ip_dst, domain, send_email,receive_email,threat_type, file_md5):
        alert = AlertGenerator(ip_src, ip_dst, domain,threat_type=threat_type, send_email=send_email,rec_email=receive_email,  file_md5=file_md5)
        alert.generate_alert()
        alert.print_alert_info()
        data = alert.save_pb()
        #return data
        alert.send_to_kafka()


    for j in range(200):
        try:
            ips = [faker.Faker().ipv4() for _ in range(4)]
            domain = faker.Faker().domain_name()
            threat_type = random.randint(1024, 1054)
            send_email =f'{faker.Faker().name()} <{faker.Faker().email()}>'
            receive_email = f'{faker.Faker().name()} <{faker.Faker().email()}>'
            emails = [f"{faker.Faker().name()} <{faker.Faker().email()}>" for _ in range(4)]
            # ip_src = '**************'
            # ip_dst = '**************'
            # threat_type = 1036
            # domain = 'wangxiaobao.com'
            # <AUTHOR> <EMAIL>'
            # <AUTHOR> <EMAIL>'
            # file_md5 = '00f415d8475545be5ba114f208b0ff'
            for i in range(3):
                pb_data = process_alert(ips[i],ips[i+1],domain,emails[i],emails[i+1],threat_type,file_md5='00f415d8475545be5ba114f208b0ff')
            #pb_data = process_alert(ip_src=ip_src,ip_dst=ip_dst,threat_type=threat_type,domain=domain,send_email=send_email,receive_email=receive_email,file_md5=file_md5)
            # with open(f'./testdata_{ip_src}_{ip_dst}.pb','wb') as file:
            #         file.write(pb_data)
            # print(f'攻击链为：{ips[0]}-{ips[1]}-{ips[2]}-{ips[3]}-{domain},发送邮件为：{send_email},接收邮件为：{receive_email}')
        except Exception as e:
            print(e)
    # 构建完整攻击链2

    # for i in range(500):
    #     try:




