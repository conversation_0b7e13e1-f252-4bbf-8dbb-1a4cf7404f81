# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: panorama_result.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'panorama_result.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x15panorama_result.proto\"\x94\x07\n\tALERT_LOG\x12\x10\n\x08log_guid\x18\x01 \x02(\x04\x12\x0c\n\x04time\x18\x02 \x02(\x04\x12\x11\n\tline_info\x18\x03 \x03(\t\x12\x0f\n\x07task_id\x18\x04 \x02(\t\x12\x0f\n\x07rule_id\x18\x05 \x02(\t\x12\x0f\n\x07ioc_tag\x18\x06 \x02(\t\x12\x0f\n\x07iob_tag\x18\x07 \x02(\t\x12\x0f\n\x07ioa_tag\x18\x08 \x02(\t\x12\x10\n\x08user_tag\x18\t \x02(\t\x12\x1a\n\x08sip_info\x18\n \x02(\x0b\x32\x08.IP_INFO\x12\x1a\n\x08\x64ip_info\x18\x0b \x02(\x0b\x32\x08.IP_INFO\x12\x1a\n\x08\x61ip_info\x18\x0c \x02(\x0b\x32\x08.IP_INFO\x12\x1a\n\x08vip_info\x18\r \x02(\x0b\x32\x08.IP_INFO\x12\x1a\n\x12LR_aggregate_value\x18\x0e \x02(\t\x12\x1b\n\x13LR_first_alert_date\x18\x0f \x02(\x04\x12\x1a\n\x12LR_last_alert_date\x18\x10 \x02(\x04\x12\x15\n\rLR_alert_time\x18\x11 \x02(\r\x12\x13\n\x0b\x64\x65tect_type\x18\x12 \x02(\r\x12\x13\n\x0bthreat_type\x18\x13 \x02(\r\x12\x10\n\x08severity\x18\x14 \x02(\r\x12\x12\n\nconfidence\x18\x15 \x02(\x05\x12\x12\n\nwhite_list\x18\x16 \x02(\r\x12\x12\n\nblack_list\x18\x17 \x02(\r\x12\x13\n\x0btrans_proto\x18\x18 \x02(\r\x12\x11\n\tapp_proto\x18\x19 \x01(\r\x12\x11\n\tmeta_data\x18\x1a \x02(\x0c\x12\x15\n\rraw_data_path\x18\x1b \x02(\t\x12\x17\n\x0f\x61lert_data_path\x18\x1c \x02(\t\x12\x11\n\tsensor_ip\x18\x1d \x02(\t\x12\x11\n\tvendor_id\x18\x1e \x02(\t\x12\'\n\x0eioc_alert_info\x18= \x01(\x0b\x32\x0f.IOC_ALERT_INFO\x12(\n\x0f\x66ile_alert_info\x18> \x01(\x0b\x32\x0f.FILE_ALERT_LOG\x12.\n\x12sandbox_alert_info\x18? \x01(\x0b\x32\x12.SANDBOX_ALERT_LOG\x12/\n\x17industry_iot_alert_info\x18@ \x01(\x0b\x32\x0e.INC_ALERT_LOG\x12(\n\x0ftele_alert_info\x18\x41 \x01(\x0b\x32\x0f.TELE_ALERT_LOG\x12*\n\x10\x65mail_alert_info\x18\x42 \x01(\x0b\x32\x10.EMAIL_ALERT_LOG\"\xb9\x01\n\x0f\x45MAIL_ALERT_LOG\x12\x13\n\x0bmail_sender\x18\x01 \x01(\t\x12\x15\n\rmail_receiver\x18\x02 \x01(\t\x12\x14\n\x0cmail_subject\x18\x03 \x01(\t\x12\x14\n\x0cmail_content\x18\x04 \x01(\t\x12\x10\n\x08\x66ile_md5\x18\x05 \x01(\t\x12\x10\n\x08industry\x18\x06 \x01(\t\x12\x14\n\x0c\x61nomaly_tags\x18\x07 \x03(\t\x12\x14\n\x0c\x61lert_reason\x18\x08 \x01(\t\"\xcf\x03\n\x0eTELE_ALERT_LOG\x12\x11\n\trule_type\x18\x01 \x02(\r\x12\x1c\n\x14\x61larm_operation_code\x18\x02 \x02(\r\x12\x14\n\x0c\x61larm_source\x18\x03 \x02(\t\x12\x11\n\tdatetimes\x18\x04 \x02(\t\x12\x10\n\x08\x61larm_id\x18\x05 \x02(\t\x12\x13\n\x0b\x61larm_level\x18\x06 \x02(\r\x12\x1a\n\x12threat_source_node\x18\x07 \x02(\t\x12\x1d\n\x15threat_source_country\x18\x08 \x02(\t\x12\x1e\n\x16threat_source_operator\x18\t \x02(\t\x12\x16\n\x0ethreatned_node\x18\n \x02(\t\x12\x19\n\x11threatned_country\x18\x0b \x02(\t\x12\x1a\n\x12threatned_operator\x18\x0c \x02(\t\x12\x13\n\x0b\x61larm_reson\x18\r \x02(\t\x12\x18\n\x10threat_source_id\x18\x0e \x02(\r\x12\x0c\n\x04imsi\x18\x0f \x02(\t\x12\x0e\n\x06msisdn\x18\x10 \x02(\t\x12\x16\n\x0etarget_country\x18\x11 \x02(\t\x12\x17\n\x0ftarget_operator\x18\x12 \x02(\t\x12\x14\n\x0c\x61larm_effect\x18\x13 \x02(\r\"\xf4\x01\n\rINC_ALERT_LOG\x12\x0b\n\x03pkt\x18\x01 \x02(\x0c\x12\x10\n\x08protocol\x18\x02 \x02(\t\x12\x15\n\rprotocol_type\x18\x03 \x02(\r\x12\r\n\x05\x63ount\x18\x04 \x02(\r\x12\x0c\n\x04meta\x18\x06 \x02(\t\x12\x0e\n\x06\x61\x63tion\x18\x07 \x02(\t\x12\x10\n\x08\x65rr_type\x18\x08 \x02(\r\x12\x0e\n\x06is_key\x18\t \x02(\x08\x12\x11\n\tis_attack\x18\n \x02(\x08\x12\x0e\n\x06is_vul\x18\x0b \x02(\x08\x12\x12\n\nevent_type\x18\x0c \x02(\t\x12\x12\n\nevent_desc\x18\r \x02(\t\x12\x13\n\x0b\x65vent_level\x18\x0e \x02(\r\"\xcd\x02\n\x0e\x46ILE_ALERT_LOG\x12\x11\n\tfile_name\x18\x01 \x01(\t\x12\x11\n\tfile_type\x18\x02 \x02(\t\x12\x11\n\tfile_size\x18\x03 \x01(\r\x12\x10\n\x08\x66ile_md5\x18\x04 \x02(\t\x12\x12\n\nfirst_seen\x18\x05 \x01(\x04\x12\x18\n\x10network_behavior\x18\x06 \x01(\t\x12\x15\n\rassociated_ip\x18\x07 \x01(\t\x12\x11\n\tmalicious\x18\x08 \x02(\x08\x12\x18\n\x10malicious_family\x18\t \x01(\t\x12\x10\n\x08targeted\x18\n \x02(\x08\x12\x10\n\x08\x63\x61mpaign\x18\x0b \x01(\t\x12\x18\n\x10\x61ssociated_value\x18\x0c \x02(\t\x12\x13\n\x0bmail_sender\x18\r \x01(\t\x12\x15\n\rmail_receiver\x18\x0e \x01(\t\x12\x14\n\x0cmail_subject\x18\x0f \x01(\t\"\xf4\x01\n\x11SANDBOX_ALERT_LOG\x12\x11\n\tfile_name\x18\x01 \x02(\t\x12\x11\n\tfile_size\x18\x02 \x02(\x05\x12\x10\n\x08\x66ile_md5\x18\x03 \x02(\t\x12\x11\n\tfile_type\x18\x04 \x02(\t\x12\x11\n\tfile_path\x18\x05 \x02(\t\x12\x18\n\x10\x61ssociated_value\x18\x06 \x02(\t\x12\x13\n\x0bmail_sender\x18\x07 \x01(\t\x12\x15\n\rmail_receiver\x18\x08 \x01(\t\x12\x14\n\x0cmail_subject\x18\t \x01(\t\x12\x11\n\tmeta_info\x18\n \x02(\x0c\x12\x12\n\nreport_uri\x18\x0b \x01(\t\"\xe3\x02\n\x0eIOC_ALERT_INFO\x12\n\n\x02id\x18\x01 \x02(\t\x12\x16\n\x0emalicious_type\x18\x02 \x02(\t\x12%\n\rcampaign_info\x18\x03 \x02(\x0b\x32\x0e.CAMPAIGN_INFO\x12\x35\n\x15malicious_family_info\x18\x04 \x02(\x0b\x32\x16.MALICIOUS_FAMILY_INFO\x12\x16\n\x0e\x63urrent_status\x18\x05 \x02(\t\x12\x12\n\nconfidence\x18\x06 \x02(\t\x12\x14\n\x0chazard_level\x18\x07 \x02(\t\x12\x0b\n\x03tag\x18\x08 \x02(\t\x12\x12\n\nkill_chain\x18\t \x02(\t\x12\x10\n\x08targeted\x18\n \x02(\x08\x12\x10\n\x08protocol\x18\x0b \x02(\t\x12\x1d\n\tpacp_info\x18\x0c \x01(\x0b\x32\n.PCAP_INFO\x12\x11\n\tmeta_info\x18P \x01(\x0c\x12\x16\n\x0emeta_info_type\x18Q \x01(\r\"\x90\x02\n\tPCAP_HTTP\x12\x0e\n\x06method\x18\x01 \x01(\t\x12\x0c\n\x04host\x18\x02 \x01(\t\x12\x0b\n\x03uri\x18\x03 \x01(\x0c\x12\x10\n\x08referrer\x18\x04 \x01(\t\x12\r\n\x05\x61gent\x18\x05 \x01(\t\x12\x0e\n\x06\x63ookie\x18\x06 \x01(\t\x12\x11\n\tparameter\x18\x07 \x01(\t\x12\x12\n\nreq_header\x18\x08 \x01(\t\x12\x10\n\x08req_body\x18\t \x01(\x0c\x12\x12\n\nrsp_status\x18\n \x01(\r\x12\x1a\n\x12rsp_content_length\x18\x0b \x01(\r\x12\x18\n\x10rsp_content_type\x18\x0c \x01(\t\x12\x12\n\nrsp_header\x18\r \x01(\t\x12\x10\n\x08rsp_body\x18\x0e \x01(\x0c\"6\n\nPCAP_OTHER\x12\x13\n\x0bpacket_size\x18\x01 \x01(\r\x12\x13\n\x0bpacket_data\x18\x02 \x01(\x0c\"\x83\x02\n\tPCAP_INFO\x12\x11\n\ttask_name\x18\x01 \x01(\t\x12\x0f\n\x07task_id\x18\x02 \x01(\r\x12\x17\n\x0frule_group_name\x18\x03 \x01(\t\x12\x15\n\rrule_group_id\x18\x04 \x01(\r\x12\x11\n\trule_name\x18\x05 \x01(\t\x12\x0f\n\x07rule_id\x18\x06 \x01(\r\x12\x0e\n\x06\x64\x65tail\x18\x07 \x01(\t\x12\x11\n\tpcap_path\x18\x08 \x01(\t\x12\x15\n\rori_pcap_path\x18\t \x01(\t\x12 \n\x0chttp_payload\x18\n \x01(\x0b\x32\n.PCAP_HTTP\x12\"\n\rother_payload\x18\x0b \x01(\x0b\x32\x0b.PCAP_OTHER\"\x95\x03\n\rCAMPAIGN_INFO\x12\x10\n\x08\x63\x61mpaign\x18\x01 \x02(\t\x12\x1c\n\x14\x66irst_detection_date\x18\x02 \x02(\t\x12\x15\n\rattack_method\x18\x03 \x02(\t\x12\x0b\n\x03rat\x18\x04 \x02(\t\x12\r\n\x05\x61\x63tor\x18\x05 \x02(\t\x12\x1b\n\x13responsible_country\x18\x06 \x02(\t\x12\r\n\x05\x61lias\x18\x07 \x02(\t\x12\x13\n\x0breport_date\x18\x08 \x02(\t\x12\x13\n\x0b\x63reate_time\x18\t \x02(\t\x12\x14\n\x0cprimary_name\x18\n \x02(\t\x12\x17\n\x0f\x61\x66\x66\x65\x63ted_sector\x18\x0b \x02(\t\x12\x19\n\x11\x66ront_description\x18\x0c \x02(\t\x12\x16\n\x0ereference_link\x18\r \x02(\t\x12\x15\n\rreport_vendor\x18\x0e \x02(\t\x12\x1a\n\x12reference_document\x18\x0f \x02(\t\x12\x17\n\x0f\x61\x66\x66\x65\x63ted_system\x18\x10 \x02(\t\x12\x1d\n\x15related_vulnerability\x18\x11 \x02(\t\"y\n\x15MALICIOUS_FAMILY_INFO\x12\x18\n\x10malicious_family\x18\x01 \x02(\t\x12\x0c\n\x04risk\x18\x02 \x02(\t\x12\x10\n\x08platform\x18\x03 \x02(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x02(\t\x12\x11\n\treference\x18\x05 \x02(\t\"\xc4\x01\n\x07IP_INFO\x12\n\n\x02ip\x18\x01 \x02(\t\x12\x0c\n\x04port\x18\x02 \x02(\r\x12\x12\n\nip_country\x18\x03 \x02(\t\x12\x0f\n\x07ip_stat\x18\x04 \x02(\t\x12\x0f\n\x07ip_city\x18\x05 \x02(\t\x12\x0e\n\x06ip_org\x18\x06 \x02(\t\x12\x14\n\x0cip_longitude\x18\x07 \x02(\x01\x12\x13\n\x0bip_latitude\x18\x08 \x02(\x01\x12\x0e\n\x06ip_isp\x18\t \x02(\t\x12\x0e\n\x06ip_asn\x18\n \x02(\t\x12\x0e\n\x06ip_tag\x18\x0b \x01(\tB\x0c\n\ncom.entity')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'panorama_result_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\ncom.entity'
  _globals['_ALERT_LOG']._serialized_start=26
  _globals['_ALERT_LOG']._serialized_end=942
  _globals['_EMAIL_ALERT_LOG']._serialized_start=945
  _globals['_EMAIL_ALERT_LOG']._serialized_end=1130
  _globals['_TELE_ALERT_LOG']._serialized_start=1133
  _globals['_TELE_ALERT_LOG']._serialized_end=1596
  _globals['_INC_ALERT_LOG']._serialized_start=1599
  _globals['_INC_ALERT_LOG']._serialized_end=1843
  _globals['_FILE_ALERT_LOG']._serialized_start=1846
  _globals['_FILE_ALERT_LOG']._serialized_end=2179
  _globals['_SANDBOX_ALERT_LOG']._serialized_start=2182
  _globals['_SANDBOX_ALERT_LOG']._serialized_end=2426
  _globals['_IOC_ALERT_INFO']._serialized_start=2429
  _globals['_IOC_ALERT_INFO']._serialized_end=2784
  _globals['_PCAP_HTTP']._serialized_start=2787
  _globals['_PCAP_HTTP']._serialized_end=3059
  _globals['_PCAP_OTHER']._serialized_start=3061
  _globals['_PCAP_OTHER']._serialized_end=3115
  _globals['_PCAP_INFO']._serialized_start=3118
  _globals['_PCAP_INFO']._serialized_end=3377
  _globals['_CAMPAIGN_INFO']._serialized_start=3380
  _globals['_CAMPAIGN_INFO']._serialized_end=3785
  _globals['_MALICIOUS_FAMILY_INFO']._serialized_start=3787
  _globals['_MALICIOUS_FAMILY_INFO']._serialized_end=3908
  _globals['_IP_INFO']._serialized_start=3911
  _globals['_IP_INFO']._serialized_end=4107
# @@protoc_insertion_point(module_scope)
