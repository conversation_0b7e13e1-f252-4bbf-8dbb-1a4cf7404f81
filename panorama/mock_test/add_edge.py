"""
    随机插入部分边关系
"""


from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config
from nebula3.data.ResultSet import ResultSet
import random


nebula_server_address = "***************"
# Nebula Graph 连接配置
config = Config()
config.max_connection_pool_size = 10
connection_pool = ConnectionPool()

# 替换为您的 Nebula Graph 服务器地址和端口
connection_pool.init([(nebula_server_address, 9669)], config)

# 图空间名称
space_name = "panorama_analysis_graph"

# 随机抽取节点并插入边的函数
def insert_random_edges(session,tag_type_1,tag_type_2,edge_type, num_edges, tag_limit=100):
    try:
        # 获取所有 IP 节点
        result1 = session.execute(f'USE {space_name}; MATCH (v:{tag_type_1}) RETURN id(v) as vid limit {tag_limit}')
        list1 = [record.values()[0].as_string() for record in result1]

        # 获取所有 DOMAIN 节点
        result2 = session.execute(f'USE {space_name}; MATCH (v:{tag_type_2}) RETURN id(v) as vid limit {tag_limit}')
        list2 = [record.values()[0].as_string() for record in result2]

        # 随机插入边
        for _ in range(num_edges):
            a = random.choice(list1)
            b = random.choice(list2)
            # b = "************"
            
            # 插入边
            query = f'''
            INSERT EDGE {edge_type}() VALUES "{a}" -> "{b}": ()
            '''
            # print(query)
            result = session.execute(query)
            
            if result.is_succeeded():
                print(f"成功插入边: {a} -> {b}")
            else:
                print(f"插入边失败: {query}")

    except Exception as e:
        print(f"发生错误: {e}")

# 主函数
def main():
    # 创建会话
    with connection_pool.session_context('root', 'nebula') as session:
        # 插入 100 条随机边（您可以调整这个数字）
        insert_random_edges(session,"IP","IP","make_attack_to" ,100)

if __name__ == "__main__":
    main()

# 关闭连接池
connection_pool.close()
