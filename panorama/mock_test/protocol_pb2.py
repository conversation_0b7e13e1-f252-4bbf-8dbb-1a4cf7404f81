# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: protocol.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'protocol.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0eprotocol.proto\x12\x11\x63om.geeksec.proto\"\x85\r\n\x08MetaInfo\x12\x30\n\nbasic_info\x18\x01 \x01(\x0b\x32\x1c.com.geeksec.proto.BasicInfo\x12\x13\n\x0bproto_types\x18\x02 \x03(\r\x12,\n\x08stp_info\x18\x03 \x03(\x0b\x32\x1a.com.geeksec.proto.StpInfo\x12,\n\x08\x61rp_info\x18\x04 \x03(\x0b\x32\x1a.com.geeksec.proto.ArpInfo\x12,\n\x08\x63\x64p_info\x18\x05 \x03(\x0b\x32\x1a.com.geeksec.proto.CdpInfo\x12.\n\ticmp_info\x18\x06 \x03(\x0b\x32\x1b.com.geeksec.proto.IcmpInfo\x12,\n\x08gre_info\x18\x07 \x03(\x0b\x32\x1a.com.geeksec.proto.GreInfo\x12,\n\x08ssl_info\x18\x08 \x03(\x0b\x32\x1a.com.geeksec.proto.SslInfo\x12.\n\tkrb5_info\x18\t \x03(\x0b\x32\x1b.com.geeksec.proto.Krb5Info\x12\x34\n\x0cunknown_info\x18\n \x03(\x0b\x32\x1e.com.geeksec.proto.UnknownInfo\x12.\n\thttp_info\x18\x0b \x03(\x0b\x32\x1b.com.geeksec.proto.HttpInfo\x12,\n\x08smb_info\x18\x0c \x03(\x0b\x32\x1a.com.geeksec.proto.SmbInfo\x12,\n\x08\x66tp_info\x18\r \x03(\x0b\x32\x1a.com.geeksec.proto.FtpInfo\x12\x32\n\x0btelnet_info\x18\x0e \x03(\x0b\x32\x1d.com.geeksec.proto.TelnetInfo\x12,\n\x08gtp_info\x18\x0f \x03(\x0b\x32\x1a.com.geeksec.proto.GtpInfo\x12\x30\n\nsocks_info\x18\x10 \x03(\x0b\x32\x1c.com.geeksec.proto.SocksInfo\x12*\n\x07\x64\x62_info\x18\x11 \x03(\x0b\x32\x19.com.geeksec.proto.DbInfo\x12,\n\x08vnc_info\x18\x12 \x03(\x0b\x32\x1a.com.geeksec.proto.VncInfo\x12.\n\ttftp_info\x18\x13 \x03(\x0b\x32\x1b.com.geeksec.proto.TftpInfo\x12.\n\tdhcp_info\x18\x14 \x03(\x0b\x32\x1b.com.geeksec.proto.DhcpInfo\x12,\n\x08rdp_info\x18\x15 \x03(\x0b\x32\x1a.com.geeksec.proto.RdpInfo\x12.\n\tmail_info\x18\x16 \x03(\x0b\x32\x1b.com.geeksec.proto.MailInfo\x12.\n\tsnmp_info\x18\x17 \x03(\x0b\x32\x1b.com.geeksec.proto.SnmpInfo\x12.\n\tdnp3_info\x18\x18 \x03(\x0b\x32\x1b.com.geeksec.proto.Dnp3Info\x12\x36\n\rdiameter_info\x18\x19 \x03(\x0b\x32\x1f.com.geeksec.proto.DiameterInfo\x12,\n\x08ssh_info\x18\x1a \x03(\x0b\x32\x1a.com.geeksec.proto.SshInfo\x12.\n\tntlm_info\x18\x1b \x03(\x0b\x32\x1b.com.geeksec.proto.NtlmInfo\x12,\n\x08\x64ns_info\x18\x1c \x03(\x0b\x32\x1a.com.geeksec.proto.DnsInfo\x12,\n\x08\x62gp_info\x18\x1d \x03(\x0b\x32\x1a.com.geeksec.proto.BgpInfo\x12\x32\n\x0bmodbus_info\x18\x1e \x03(\x0b\x32\x1d.com.geeksec.proto.ModbusInfo\x12.\n\tx509_info\x18\x1f \x03(\x0b\x32\x1b.com.geeksec.proto.X509Info\x12\x32\n\x0b\x62rowse_info\x18  \x03(\x0b\x32\x1d.com.geeksec.proto.BrowseInfo\x12\x32\n\x0bsyslog_info\x18! \x03(\x0b\x32\x1d.com.geeksec.proto.SyslogInfo\x12.\n\tuser_info\x18\" \x03(\x0b\x32\x1b.com.geeksec.proto.UserInfo\x12\x30\n\nmevil_info\x18# \x03(\x0b\x32\x1c.com.geeksec.proto.MevilInfo\"\xeb\x1a\n\tBasicInfo\x12\x34\n\x0c\x62\x61sic_struct\x18\x01 \x01(\x0b\x32\x1e.com.geeksec.proto.BasicStruct\x12\x13\n\x0bstr_task_id\x18\x02 \x01(\t\x12\x13\n\x0bstr_rule_id\x18\x03 \x01(\t\x12\x11\n\tstr_etags\x18\x04 \x01(\t\x12\x11\n\tstr_ttags\x18\x05 \x01(\t\x12\x11\n\tstr_utags\x18\x06 \x01(\t\x12\x11\n\tstr_atags\x18\x07 \x01(\t\x12\x18\n\x10u64_capture_time\x18\x08 \x01(\x04\x12\x15\n\rstr_line_name\x18\t \x01(\t\x12\x11\n\tu8_ip_ver\x18\n \x01(\r\x12\x14\n\x0cu32_src_addr\x18\x0b \x01(\r\x12\x16\n\x0estr_src_addrv6\x18\x0c \x01(\t\x12\x14\n\x0cu32_dst_addr\x18\r \x01(\r\x12\x16\n\x0estr_dst_addrv6\x18\x0e \x01(\t\x12\x14\n\x0cu16_src_port\x18\x0f \x01(\r\x12\x14\n\x0cu16_dst_port\x18\x10 \x01(\r\x12\x16\n\x0eu8_ip_protocol\x18\x11 \x01(\r\x12\x17\n\x0fstr_src_country\x18\x12 \x01(\t\x12\x15\n\rstr_src_state\x18\x13 \x01(\t\x12\x14\n\x0cstr_src_city\x18\x14 \x01(\t\x12\x19\n\x11\x66\x36\x34_src_longitude\x18\x15 \x01(\x01\x12\x18\n\x10\x66\x36\x34_src_latitude\x18\x16 \x01(\x01\x12\x13\n\x0bstr_src_isp\x18\x17 \x01(\t\x12\x13\n\x0bstr_src_asn\x18\x18 \x01(\t\x12\x17\n\x0fstr_dst_country\x18\x19 \x01(\t\x12\x15\n\rstr_dst_state\x18\x1a \x01(\t\x12\x14\n\x0cstr_dst_city\x18\x1b \x01(\t\x12\x19\n\x11\x66\x36\x34_dst_longitude\x18\x1c \x01(\x01\x12\x18\n\x10\x66\x36\x34_dst_latitude\x18\x1d \x01(\x01\x12\x13\n\x0bstr_dst_isp\x18\x1e \x01(\t\x12\x13\n\x0bstr_dst_asn\x18\x1f \x01(\t\x12\x18\n\x10u8_out_addr_type\x18  \x01(\r\x12\x18\n\x10u32_out_src_addr\x18! \x01(\r\x12\x1a\n\x12str_out_src_addrv6\x18\" \x01(\t\x12\x18\n\x10u32_out_dst_addr\x18# \x01(\r\x12\x1a\n\x12str_out_dst_addrv6\x18$ \x01(\t\x12\x18\n\x10u16_out_src_port\x18% \x01(\r\x12\x18\n\x10u16_out_dst_port\x18& \x01(\r\x12\x14\n\x0cu8_out_proto\x18\' \x01(\r\x12\x12\n\nu8_ip_flag\x18( \x01(\r\x12\x12\n\nu16_ip_len\x18) \x01(\r\x12\x15\n\rbin_ip_header\x18* \x01(\x0c\x12\x11\n\tu8_ip_ttl\x18+ \x01(\r\x12\x16\n\x0e\x62in_ip_content\x18, \x01(\x0c\x12\x1a\n\x12u32_ip_content_len\x18- \x01(\r\x12\x15\n\rstr_prot_info\x18. \x01(\t\x12\x15\n\rstr_prot_type\x18/ \x01(\t\x12\x15\n\rstr_prot_name\x18\x30 \x01(\t\x12\x18\n\x10u8_app_direction\x18\x31 \x01(\r\x12\x16\n\x0eu64_begin_time\x18\x32 \x01(\x04\x12\x14\n\x0cu64_end_time\x18\x33 \x01(\x04\x12\x14\n\x0cu32_time_len\x18\x34 \x01(\r\x12\x12\n\nu8_ttl_src\x18\x35 \x01(\r\x12\x12\n\nu8_ttl_dst\x18\x36 \x01(\r\x12\x13\n\x0bu32_pkt_num\x18\x37 \x01(\r\x12\x1b\n\x13u32_up_link_pkt_num\x18\x38 \x01(\r\x12\x1d\n\x15u32_down_link_pkt_num\x18\x39 \x01(\r\x12\x15\n\ru64_ses_bytes\x18: \x01(\x04\x12\x18\n\x10u64_up_ses_bytes\x18; \x01(\x04\x12\x1a\n\x12u64_down_ses_bytes\x18< \x01(\x04\x12\x1b\n\x13\x66\x33\x32_ses_bytes_ratio\x18= \x01(\x02\x12\x13\n\x0bu64_pay_len\x18> \x01(\x04\x12\x16\n\x0eu64_up_pay_len\x18? \x01(\x04\x12\x18\n\x10u64_down_pay_len\x18@ \x01(\x04\x12\x19\n\x11\x66\x33\x32_pay_len_ratio\x18\x41 \x01(\x02\x12\x1a\n\x12u64_link_des_bytes\x18\x42 \x01(\x04\x12\x1d\n\x15u64_up_link_des_bytes\x18\x43 \x01(\x04\x12\x1f\n\x17u64_down_link_des_bytes\x18\x44 \x01(\x04\x12\x15\n\rbin_ip_stream\x18\x45 \x01(\x0c\x12\x19\n\x11\x62in_ip_stream_src\x18\x46 \x01(\x0c\x12\x19\n\x11\x62in_ip_stream_dst\x18G \x01(\x0c\x12!\n\x19str_up_link_trans_pay_hex\x18H \x01(\t\x12#\n\x1bstr_down_link_trans_pay_hex\x18I \x01(\t\x12\x1f\n\x17str_up_link_pay_len_set\x18J \x01(\t\x12!\n\x19str_down_link_pay_len_set\x18K \x01(\t\x12\x1f\n\x17u32_up_link_max_pkt_len\x18L \x01(\r\x12!\n\x19u32_down_link_max_pkt_len\x18M \x01(\r\x12\x1f\n\x17u32_up_link_min_pkt_len\x18N \x01(\r\x12!\n\x19u32_down_link_min_pkt_len\x18O \x01(\r\x12!\n\x19u32_up_link_hfreq_pkt_len\x18P \x01(\r\x12#\n\x1bu32_down_link_hfreq_pkt_len\x18Q \x01(\r\x12\x1f\n\x17u32_up_link_max_pkt_int\x18R \x01(\r\x12!\n\x19u32_down_link_max_pkt_int\x18S \x01(\r\x12\x1f\n\x17u32_up_link_min_pkt_int\x18T \x01(\r\x12!\n\x19u32_down_link_min_pkt_int\x18U \x01(\r\x12\x13\n\x0b\x62in_tcp_hdr\x18V \x01(\x0c\x12\x16\n\x0eu8_tcp_hdr_len\x18W \x01(\r\x12\x14\n\x0cu16_tcp_flag\x18X \x01(\r\x12\x17\n\x0fu8_tcp_flag_fin\x18Y \x01(\r\x12\x17\n\x0fu8_tcp_flag_syn\x18Z \x01(\r\x12\x17\n\x0fu8_tcp_flag_rst\x18[ \x01(\r\x12\x17\n\x0fu8_tcp_flag_psh\x18\\ \x01(\r\x12\x17\n\x0fu8_tcp_flag_ack\x18] \x01(\r\x12\x17\n\x0fu8_tcp_flag_urg\x18^ \x01(\r\x12\x17\n\x0fu8_tcp_flag_ece\x18_ \x01(\r\x12\x17\n\x0fu8_tcp_flag_cwr\x18` \x01(\r\x12\x16\n\x0eu8_tcp_flag_ns\x18\x61 \x01(\r\x12\x1a\n\x12u32_tcp_windowsize\x18\x62 \x01(\r\x12\x17\n\x0f\x62in_tcp_payload\x18\x63 \x01(\x0c\x12\x1b\n\x13u32_tcp_payload_len\x18\x64 \x01(\r\x12\x1f\n\x17u32_up_link_syn_seq_num\x18\x65 \x01(\r\x12!\n\x19u32_down_link_syn_seq_num\x18\x66 \x01(\r\x12 \n\x18u16_up_link_syn_tcp_wins\x18g \x01(\r\x12\"\n\x1au16_down_link_syn_tcp_wins\x18h \x01(\r\x12\x1c\n\x14str_up_link_tcp_opts\x18i \x01(\t\x12\x1e\n\x16str_down_link_tcp_opts\x18j \x01(\t\x12\x19\n\x11str_tcp_src_flags\x18k \x01(\t\x12\x19\n\x11str_tcp_dst_flags\x18l \x01(\t\x12\x1d\n\x15u32_tcp_flags_fin_cnt\x18m \x01(\r\x12\x1d\n\x15u32_tcp_flags_syn_cnt\x18n \x01(\r\x12\x1d\n\x15u32_tcp_flags_rst_cnt\x18o \x01(\r\x12\x1d\n\x15u32_tcp_flags_psh_cnt\x18p \x01(\r\x12\x1d\n\x15u32_tcp_flags_ack_cnt\x18q \x01(\r\x12\x1d\n\x15u32_tcp_flags_urg_cnt\x18r \x01(\r\x12\x1d\n\x15u32_tcp_flags_ece_cnt\x18s \x01(\r\x12\x1d\n\x15u32_tcp_flags_cwr_cnt\x18t \x01(\r\x12\x1c\n\x14u32_tcp_flags_ns_cnt\x18u \x01(\r\x12!\n\x19u32_tcp_flags_syn_ack_cnt\x18v \x01(\r\x12\x1a\n\x12u8_tcp_established\x18w \x01(\r\x12\x16\n\x0e\x62in_udp_header\x18x \x01(\x0c\x12\x17\n\x0f\x62in_udp_payload\x18y \x01(\x0c\x12\x1b\n\x13u32_udp_payload_len\x18z \x01(\r\x12\x17\n\x0f\x62in_sctp_header\x18{ \x01(\x0c\x12\x1a\n\x12u8_sctp_chunk_type\x18| \x01(\r\x12\x1a\n\x12u16_sctp_stream_id\x18} \x01(\r\x12\x19\n\x11u32_sctp_proto_id\x18~ \x01(\r\x12\x18\n\x10\x62in_sctp_payload\x18\x7f \x01(\x0c\x12\x1d\n\x14u32_sctp_payload_len\x18\x80\x01 \x01(\r\"\xfe\x02\n\x0b\x42\x61sicStruct\x12\x0f\n\x07\x66low_id\x18\x01 \x01(\x0c\x12\x0e\n\x06src_ip\x18\x02 \x01(\r\x12\x0e\n\x06\x64st_ip\x18\x03 \x01(\r\x12\x10\n\x08src_port\x18\x04 \x01(\r\x12\x10\n\x08\x64st_port\x18\x05 \x01(\r\x12\x10\n\x08protocol\x18\x06 \x01(\r\x12\x10\n\x08ip_class\x18\x07 \x01(\r\x12\x10\n\x08pro_type\x18\x08 \x01(\r\x12\x10\n\x08log_type\x18\t \x01(\r\x12\x11\n\tapp_class\x18\n \x01(\r\x12\x0e\n\x06\x61pp_id\x18\x0b \x01(\r\x12\x13\n\x0bpackets_src\x18\x0c \x01(\r\x12\x13\n\x0bpackets_dst\x18\r \x01(\r\x12\x11\n\tbytes_src\x18\x0e \x01(\x04\x12\x11\n\tbytes_dst\x18\x0f \x01(\x04\x12\x13\n\x0b\x63reate_time\x18\x10 \x01(\r\x12\x12\n\nstart_time\x18\x11 \x01(\x04\x12\x16\n\x0esession_length\x18\x12 \x01(\x04\x12\x0e\n\x06srcmac\x18\x13 \x01(\x0c\x12\x0e\n\x06\x64stmac\x18\x14 \x01(\x0c\"(\n\x07StpInfo\x12\x10\n\x08root_mac\x18\x01 \x01(\x0c\x12\x0b\n\x03mac\x18\x02 \x01(\x0c\"\x7f\n\x07\x41rpInfo\x12\x10\n\x08hardware\x18\x01 \x01(\t\x12\x0f\n\x07mac_src\x18\x02 \x01(\x0c\x12\x0e\n\x06ip_src\x18\x03 \x01(\r\x12\x0f\n\x07mac_dst\x18\x04 \x01(\x0c\x12\x0e\n\x06ip_dst\x18\x05 \x01(\r\x12\x0e\n\x06opcode\x18\x06 \x01(\t\x12\x10\n\x08protocol\x18\x07 \x01(\t\"\xa3\x01\n\x07\x43\x64pInfo\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\x12\n\nip_address\x18\x02 \x01(\r\x12\x11\n\tdevice_id\x18\x03 \x01(\t\x12\x18\n\x10software_version\x18\x04 \x01(\t\x12\x0f\n\x07port_id\x18\x05 \x01(\t\x12\x19\n\x11vtp_manage_domain\x18\x06 \x01(\t\x12\x1a\n\x12management_address\x18\x07 \x01(\r\"\xc5\x01\n\x08IcmpInfo\x12\x0c\n\x04type\x18\x01 \x01(\x05\x12\x0c\n\x04\x63ode\x18\x02 \x01(\x05\x12\x0b\n\x03seq\x18\x03 \x01(\x05\x12\x15\n\rresponse_time\x18\x04 \x01(\x04\x12\x13\n\x0bpayload_hex\x18\x05 \x01(\x0c\x12\x17\n\x0funreachable_src\x18\x06 \x01(\t\x12\x17\n\x0funreachable_dst\x18\x07 \x01(\t\x12\x19\n\x11unreachable_proto\x18\x08 \x01(\x05\x12\x17\n\x0funreachable_ttl\x18\t \x01(\x05\"X\n\x07GreInfo\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\x0f\n\x07\x63\x61ll_id\x18\x02 \x01(\x05\x12\x0b\n\x03key\x18\x03 \x01(\r\x12\x0e\n\x06ip_src\x18\x04 \x01(\r\x12\x0e\n\x06ip_dst\x18\x05 \x01(\r\"\xf1\x05\n\x07SslInfo\x12\x11\n\tversion_c\x18\x01 \x01(\t\x12\x0b\n\x03sni\x18\x02 \x01(\t\x12\x11\n\tgmttime_c\x18\x03 \x01(\x04\x12\x17\n\x0fhandshake_len_c\x18\x04 \x01(\r\x12\x10\n\x08random_c\x18\x05 \x01(\x0c\x12\x13\n\x0bsessionid_c\x18\x06 \x01(\x0c\x12\x14\n\x0c\x63iphersuites\x18\x07 \x01(\t\x12\x18\n\x10\x63iphersuites_cnt\x18\x08 \x01(\r\x12\x14\n\x0c\x63ompmethod_c\x18\t \x01(\t\x12\x17\n\x0fsessionticket_c\x18\n \x01(\x0c\x12\x19\n\x11\x65xt_ecpointformat\x18\x0b \x01(\t\x12\x17\n\x0f\x65xt_ec_groups_c\x18\x0c \x01(\t\x12\x11\n\text_cnt_c\x18\r \x01(\r\x12\x13\n\x0b\x65xt_types_c\x18\x0e \x01(\t\x12\x12\n\next_grease\x18\x0f \x01(\x05\x12\x12\n\ncert_cnt_c\x18\x10 \x01(\r\x12\x12\n\ncert_len_c\x18\x11 \x01(\r\x12\x15\n\rcert_hashes_c\x18\x12 \x01(\t\x12\x11\n\tversion_s\x18\x13 \x01(\t\x12\x17\n\x0fhandshake_len_s\x18\x14 \x01(\r\x12\x10\n\x08random_s\x18\x15 \x01(\x0c\x12\x11\n\tgmttime_s\x18\x16 \x01(\r\x12\x14\n\x0c\x63ompmethod_s\x18\x17 \x01(\t\x12\x0e\n\x06\x63ipher\x18\x18 \x01(\x0c\x12\x13\n\x0bsessionid_s\x18\x19 \x01(\x0c\x12\x13\n\x0b\x65xt_types_s\x18\x1a \x01(\t\x12\x17\n\x0fsessionticket_s\x18\x1b \x01(\x0c\x12\x11\n\text_cnt_s\x18\x1c \x01(\r\x12\x17\n\x0f\x65xt_ec_groups_s\x18\x1d \x01(\t\x12\n\n\x02\x65\x63\x18\x1e \x01(\x0c\x12\x12\n\ncert_cnt_s\x18\x1f \x01(\r\x12\x12\n\ncert_len_s\x18  \x01(\r\x12\x15\n\rcert_hashes_s\x18! \x01(\t\x12\x0b\n\x03ja3\x18\" \x01(\x0c\x12\x0c\n\x04ja3s\x18# \x01(\x0c\x12\x0b\n\x03joy\x18$ \x01(\x0c\x12\x0c\n\x04joys\x18% \x01(\x0c\"]\n\x08Krb5Info\x12\x10\n\x08msg_type\x18\x01 \x01(\x05\x12\r\n\x05\x63name\x18\x02 \x01(\t\x12\r\n\x05sname\x18\x03 \x01(\t\x12\r\n\x05realm\x18\x04 \x01(\t\x12\x12\n\nerror_code\x18\x05 \x01(\x05\" \n\x0bUnknownInfo\x12\x11\n\tencrypted\x18\x01 \x01(\r\"\xe0\x07\n\x08HttpInfo\x12\x0e\n\x06method\x18\x01 \x01(\t\x12\x0b\n\x03uri\x18\x02 \x01(\t\x12\x0f\n\x07uri_key\x18\x03 \x01(\t\x12\x10\n\x08uri_path\x18\x04 \x01(\t\x12\x13\n\x0bversion_src\x18\x05 \x01(\t\x12\x0c\n\x04host\x18\x06 \x01(\t\x12\x12\n\nuser_agent\x18\x07 \x01(\t\x12\x0f\n\x07referer\x18\x08 \x01(\t\x12\x17\n\x0f\x61\x63\x63\x65pt_encoding\x18\t \x01(\t\x12\x17\n\x0f\x61\x63\x63\x65pt_language\x18\n \x01(\t\x12\x15\n\rauthorization\x18\x0b \x01(\t\x12\x18\n\x10\x63ontent_type_req\x18\x0c \x01(\t\x12\x0e\n\x06xff_ip\x18\r \x01(\t\x12\x0c\n\x04\x66rom\x18\x0e \x01(\t\x12\x1a\n\x12\x63ontent_length_req\x18\x0f \x01(\r\x12\x0e\n\x06\x63ookie\x18\x10 \x01(\t\x12\x12\n\ncookie_key\x18\x11 \x01(\t\x12\x12\n\nproxy_auth\x18\x12 \x01(\t\x12\x10\n\x08\x61\x63\x63\x65pt_c\x18\x13 \x01(\t\x12\x0f\n\x07range_c\x18\x14 \x01(\t\x12\x0c\n\x04user\x18\x15 \x01(\t\x12\x10\n\x08password\x18\x16 \x01(\t\x12\x14\n\x0chead_key_req\x18\x17 \x01(\t\x12\x17\n\x0f\x63ontent_md5_req\x18\x18 \x01(\x0c\x12\x0f\n\x07reqbody\x18\x19 \x01(\x0c\x12\x12\n\nstatuscode\x18\x1a \x01(\r\x12\x13\n\x0bversion_dst\x18\x1b \x01(\t\x12\x10\n\x08location\x18\x1c \x01(\t\x12\x0e\n\x06server\x18\x1d \x01(\t\x12\x0b\n\x03via\x18\x1e \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x1f \x01(\r\x12\x0f\n\x07\x61uth_ok\x18  \x01(\x05\x12\x19\n\x11transfer_encoding\x18! \x01(\t\x12\x19\n\x11\x63ontent_type_resp\x18\" \x01(\t\x12\x18\n\x10\x63ontent_language\x18# \x01(\t\x12\x1b\n\x13\x63ontent_length_resp\x18$ \x01(\r\x12\x14\n\x0cx_powered_by\x18% \x01(\t\x12\x0c\n\x04vary\x18& \x01(\t\x12\x10\n\x08sinkhole\x18\' \x01(\t\x12\x1d\n\x15\x63ontent_encoding_resp\x18( \x01(\t\x12\x15\n\rhead_key_resp\x18) \x01(\t\x12\x16\n\x0eset_cookie_key\x18* \x01(\t\x12\x16\n\x0eset_cookie_val\x18+ \x01(\t\x12\x1b\n\x13\x63ontent_disposition\x18, \x01(\t\x12\x13\n\x0b\x61ttach_name\x18- \x01(\t\x12\x16\n\x0e\x61ttach_content\x18. \x01(\t\x12\x18\n\x10\x63ontent_md5_resp\x18/ \x01(\x0c\x12\x10\n\x08respbody\x18\x30 \x01(\x0c\"\xcd\x01\n\x07SmbInfo\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x10\n\x08\x66ilename\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\t\x12\x10\n\x08\x66ile_len\x18\x04 \x01(\x04\x12\x0b\n\x03\x64ir\x18\x05 \x01(\t\x12\x0e\n\x06\x64omain\x18\x06 \x01(\t\x12\x0c\n\x04host\x18\x07 \x01(\t\x12\n\n\x02os\x18\x08 \x01(\t\x12\x0f\n\x07version\x18\t \x01(\t\x12\x11\n\tauth_type\x18\n \x01(\t\x12\r\n\x05share\x18\x0b \x01(\t\x12\x16\n\x0e\x61ttach_content\x18\x0c \x01(\t\"\x9c\x02\n\x07\x46tpInfo\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x10\n\x08\x66ilename\x18\x03 \x01(\t\x12\x12\n\noperations\x18\x04 \x01(\t\x12\x14\n\x0c\x66ile_content\x18\x05 \x01(\t\x12\x14\n\x0c\x63ontent_type\x18\x06 \x01(\t\x12\x10\n\x08serverip\x18\x07 \x01(\r\x12\x10\n\x08\x64\x61taport\x18\x08 \x01(\r\x12\x10\n\x08\x66ilesize\x18\t \x01(\r\x12\x13\n\x0brequest_cmd\x18\n \x01(\t\x12\x13\n\x0brequest_arg\x18\x0b \x01(\t\x12\x15\n\rresponse_code\x18\x0c \x01(\t\x12\x14\n\x0cresponse_arg\x18\r \x01(\t\x12\x12\n\nlogin_flag\x18\x0e \x01(\t\"\x8d\x01\n\nTelnetInfo\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x14\n\x0cterminaltype\x18\x03 \x01(\t\x12\x0f\n\x07\x63md_req\x18\x04 \x01(\t\x12\x10\n\x08\x63md_resp\x18\x05 \x01(\t\x12\x12\n\nlogin_flag\x18\x06 \x01(\t\x12\x12\n\ntn_all_cmd\x18\x07 \x01(\t\"\x88\x01\n\x07GtpInfo\x12\x0c\n\x04teid\x18\x01 \x01(\r\x12\x0f\n\x07teid_up\x18\x02 \x01(\r\x12\x11\n\tteid_down\x18\x03 \x01(\r\x12\x0c\n\x04imsi\x18\x04 \x01(\t\x12\x0e\n\x06msisdn\x18\x05 \x01(\t\x12\x0c\n\x04imei\x18\x06 \x01(\t\x12\x0c\n\x04\x65\x63gi\x18\x07 \x01(\t\x12\x11\n\tenodeb_id\x18\x08 \x01(\r\"\xaa\x01\n\tSocksInfo\x12\x12\n\nagent_type\x18\x01 \x01(\x05\x12\x0c\n\x04user\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\x12\x13\n\x0breal_ip_src\x18\x04 \x01(\r\x12\x14\n\x0crealport_src\x18\x05 \x01(\x05\x12\x14\n\x0crealport_dst\x18\x06 \x01(\x05\x12\x13\n\x0breal_domain\x18\x07 \x01(\t\x12\x13\n\x0breal_ip_dst\x18\x08 \x01(\r\"\x95\x01\n\x06\x44\x62Info\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x0e\n\x06passwd\x18\x02 \x01(\x0c\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0b\n\x03sql\x18\x04 \x01(\t\x12\n\n\x02ip\x18\x05 \x01(\t\x12\x0c\n\x04port\x18\x06 \x01(\x05\x12\x0c\n\x04type\x18\x07 \x01(\t\x12\x15\n\rmysql_version\x18\x08 \x01(\t\x12\x13\n\x0boracle_host\x18\t \x01(\t\"\x87\x01\n\x07VncInfo\x12\x0c\n\x04\x66ile\x18\x01 \x01(\t\x12\x16\n\x0eversion_server\x18\x02 \x01(\t\x12\x16\n\x0eversion_client\x18\x03 \x01(\t\x12\x18\n\x10\x63hallenge_random\x18\x04 \x01(\x0c\x12\x16\n\x0e\x63hallengs_auth\x18\x05 \x01(\x0c\x12\x0c\n\x04\x64\x65sk\x18\x06 \x01(\t\"\x9c\x01\n\x08TftpInfo\x12\x0c\n\x04mode\x18\x01 \x01(\t\x12\x10\n\x08\x66ilename\x18\x02 \x01(\t\x12\x10\n\x08\x66ilesize\x18\x03 \x01(\x05\x12\x12\n\noption_key\x18\x04 \x01(\t\x12\x14\n\x0coption_value\x18\x05 \x01(\t\x12\x13\n\x0b\x66ilecontent\x18\x06 \x01(\t\x12\x0e\n\x06option\x18\x07 \x01(\t\x12\x0f\n\x07\x66ilemd5\x18\x08 \x01(\x0c\"m\n\x08\x44hcpInfo\x12\x16\n\x0e\x63urrentAddress\x18\x01 \x01(\r\x12\x16\n\x0eserviceAddress\x18\x02 \x01(\r\x12\x18\n\x10\x63lientMacAddress\x18\x03 \x01(\x0c\x12\x17\n\x0fserviceHostName\x18\x04 \x01(\t\"}\n\x07RdpInfo\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\x12\x0b\n\x03\x61pp\x18\x04 \x01(\t\x12\x13\n\x0bvchannel_id\x18\x05 \x01(\t\x12\x0e\n\x06\x64omain\x18\x06 \x01(\t\x12\x13\n\x0b\x61\x63\x63\x65ss_path\x18\x07 \x01(\t\"\xb6\x06\n\x08MailInfo\x12\x0c\n\x04user\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t\x12\x0c\n\x04\x66rom\x18\x03 \x01(\t\x12\x12\n\nfrom_alias\x18\x04 \x01(\t\x12\n\n\x02to\x18\x05 \x01(\t\x12\x10\n\x08to_alias\x18\x06 \x01(\t\x12\n\n\x02\x63\x63\x18\x07 \x01(\t\x12\x10\n\x08\x63\x63_alias\x18\x08 \x01(\t\x12\x0b\n\x03\x62\x63\x63\x18\t \x01(\t\x12\x10\n\x08reply_to\x18\n \x01(\t\x12\x0f\n\x07subject\x18\x0b \x01(\t\x12\x0c\n\x04\x64\x61te\x18\x0c \x01(\r\x12\x10\n\x08x_mailer\x18\r \x01(\t\x12\x13\n\x0b\x66rom_domain\x18\x0e \x01(\t\x12\x0f\n\x07\x66rom_ip\x18\x0f \x01(\r\x12\x10\n\x08\x66rom_asn\x18\x10 \x01(\r\x12\x14\n\x0c\x66rom_country\x18\x11 \x01(\t\x12\x0c\n\x04with\x18\x12 \x01(\t\x12\x11\n\tby_domain\x18\x13 \x01(\t\x12\r\n\x05\x62y_ip\x18\x14 \x01(\r\x12\x0e\n\x06\x62y_asn\x18\x15 \x01(\r\x12\x12\n\nby_country\x18\x16 \x01(\t\x12\x11\n\tuseragent\x18\x17 \x01(\t\x12\x10\n\x08\x64\x61te_dst\x18\x18 \x01(\r\x12\x15\n\rsrv_agent_dst\x18\x19 \x01(\t\x12\x14\n\x0cmime_version\x18\x1a \x01(\t\x12\x11\n\tlogin_srv\x18\x1b \x01(\t\x12\x10\n\x08smtp_srv\x18\x1c \x01(\t\x12\x16\n\x0esmtp_srv_agent\x18\x1d \x01(\t\x12\x12\n\nproto_type\x18\x1e \x01(\t\x12\x12\n\nmessage_id\x18\x1f \x01(\t\x12\x14\n\x0c\x63ontent_type\x18  \x01(\t\x12\x14\n\x0ctext_charset\x18! \x01(\t\x12\x16\n\x0etrans_encoding\x18\" \x01(\t\x12\x11\n\tbody_type\x18# \x01(\t\x12\r\n\x05index\x18$ \x01(\t\x12\x0e\n\x06header\x18% \x01(\t\x12\x0c\n\x04\x62ody\x18& \x01(\t\x12\x10\n\x08\x66ilename\x18\' \x01(\t\x12\x19\n\x11\x66ile_content_type\x18( \x01(\t\x12\x10\n\x08\x66ile_len\x18) \x01(\r\x12\x0b\n\x03md5\x18* \x01(\x0c\x12\x16\n\x0e\x61ttach_content\x18+ \x01(\t\x12\x1b\n\x13login_auth_identify\x18, \x01(\t\"\xb5\x01\n\x08SnmpInfo\x12\x0f\n\x07version\x18\x01 \x01(\x05\x12\x0f\n\x07pdutype\x18\x02 \x01(\x05\x12\x11\n\tcommunity\x18\x03 \x01(\t\x12\x12\n\nrequest_id\x18\x04 \x01(\r\x12\x17\n\x0ftrap_enterprise\x18\x05 \x01(\t\x12\x0b\n\x03oid\x18\x06 \x01(\t\x12\x14\n\x0cobject_value\x18\x07 \x01(\t\x12\x12\n\nobject_syn\x18\x08 \x01(\t\x12\x10\n\x08sec_mode\x18\t \x01(\x05\"\xfb\x01\n\x08\x44np3Info\x12\r\n\x05proto\x18\x01 \x01(\x05\x12\x12\n\nlink_flags\x18\x02 \x01(\x05\x12\x10\n\x08link_src\x18\x03 \x01(\x05\x12\x10\n\x08link_dst\x18\x04 \x01(\x05\x12\x0b\n\x03len\x18\x05 \x01(\x05\x12\x10\n\x08link_crc\x18\x06 \x01(\x05\x12\x13\n\x0btrans_flags\x18\x07 \x01(\x05\x12\x11\n\tapp_flags\x18\x08 \x01(\x05\x12\x10\n\x08\x61pp_func\x18\t \x01(\t\x12\x0f\n\x07\x61pp_obj\x18\n \x01(\x05\x12\x15\n\rapp_qualifier\x18\x0b \x01(\x05\x12\x11\n\tapp_range\x18\x0c \x01(\x05\x12\x14\n\x0c\x61pp_obj_args\x18\r \x01(\x05\"\xb3\x02\n\x0c\x44iameterInfo\x12\x10\n\x08\x63md_code\x18\x01 \x01(\x05\x12\x16\n\x0e\x61pplication_id\x18\x02 \x01(\r\x12\x10\n\x08username\x18\x03 \x01(\t\x12\x11\n\taccess_ip\x18\x04 \x01(\r\x12\x13\n\x0b\x61\x63\x63\x65ss_port\x18\x05 \x01(\r\x12\x0e\n\x06nas_id\x18\x06 \x01(\t\x12\x15\n\rnas_port_type\x18\x07 \x01(\r\x12\x11\n\tcalled_id\x18\x08 \x01(\t\x12\x12\n\ncalling_id\x18\t \x01(\t\x12\x10\n\x08\x64st_host\x18\n \x01(\t\x12\x11\n\tauth_type\x18\x0b \x01(\r\x12\x10\n\x08src_host\x18\x0c \x01(\t\x12\x12\n\nsession_id\x18\r \x01(\t\x12\x12\n\nhop_by_hop\x18\x0e \x01(\r\x12\x12\n\nend_to_end\x18\x0f \x01(\r\"\xe8\x04\n\x07SshInfo\x12\x0e\n\x06\x63liVer\x18\x01 \x01(\t\x12\x11\n\tcliCookie\x18\x02 \x01(\x0c\x12\x1a\n\x12\x63liKeyExcAndAutMet\x18\x03 \x01(\t\x12\x15\n\rcliHostKeyAlg\x18\x04 \x01(\t\x12\x13\n\x0b\x63liEncryAlg\x18\x05 \x01(\t\x12\x19\n\x11\x63liMsgAuthCodeAlg\x18\x06 \x01(\t\x12\x13\n\x0b\x63liComprAlg\x18\x07 \x01(\t\x12\x13\n\x0b\x63liDHPubKey\x18\x08 \x01(\x0c\x12\x0e\n\x06srvVer\x18\t \x01(\t\x12\x11\n\tsrvCookie\x18\n \x01(\x0c\x12\x1b\n\x13srvKeyExcAndAuthMet\x18\x0b \x01(\t\x12\x15\n\rsrvHostKeyAlg\x18\x0c \x01(\t\x12\x13\n\x0bsrvEncryAlg\x18\r \x01(\t\x12\x19\n\x11srvMsgAuthCodeAlg\x18\x0e \x01(\t\x12\x13\n\x0bsrvComprAlg\x18\x0f \x01(\t\x12\x13\n\x0bsrvDHPubKey\x18\x10 \x01(\x0c\x12\x1a\n\x12\x65xpNumBySrvHostKey\x18\x11 \x01(\x0c\x12\x17\n\x0fmodBySrvHostKey\x18\x12 \x01(\x0c\x12\x15\n\rpBySrvHostKey\x18\x13 \x01(\x0c\x12\x15\n\rqBySrvHostKey\x18\x14 \x01(\x0c\x12\x15\n\rgBySrvHostKey\x18\x15 \x01(\x0c\x12\x15\n\ryBySrvHostKey\x18\x16 \x01(\x0c\x12\x13\n\x0bsigOfSrvKey\x18\x17 \x01(\x0c\x12\r\n\x05\x44HGen\x18\x18 \x01(\x0c\x12\r\n\x05\x44HMod\x18\x19 \x01(\x0c\x12\x17\n\x0fsrvhostkeyfp256\x18\x1a \x01(\x0c\x12\r\n\x05HASSH\x18\x1b \x01(\x0c\x12\x10\n\x08SrvHASSH\x18\x1c \x01(\x0c\"\x1b\n\x08NtlmInfo\x12\x0f\n\x07netname\x18\x01 \x01(\t\"\xc0\x03\n\x07\x44nsInfo\x12\r\n\x05query\x18\x01 \x01(\t\x12\x14\n\x0c\x61nswer_types\x18\x02 \x01(\t\x12\r\n\x05\x63name\x18\x03 \x01(\t\x12\x11\n\tcname_cnt\x18\x04 \x01(\r\x12\x12\n\nanswer_cnt\x18\x05 \x01(\r\x12\x10\n\x08\x61uth_cnt\x18\x06 \x01(\r\x12\x0f\n\x07\x61\x64\x64_cnt\x18\x07 \x01(\r\x12\r\n\x05\x66lags\x18\x08 \x01(\r\x12\x0c\n\x04ipv6\x18\t \x01(\t\x12\x0b\n\x03\x61ip\x18\n \x01(\r\x12\x0f\n\x07\x61ip_cnt\x18\x0b \x01(\r\x12\x0f\n\x07\x61ip_asn\x18\x0c \x01(\r\x12\x13\n\x0b\x61ip_country\x18\r \x01(\t\x12\x0f\n\x07ns_host\x18\x0e \x01(\t\x12\x13\n\x0bns_host_cnt\x18\x0f \x01(\r\x12\r\n\x05ns_ip\x18\x10 \x01(\r\x12\x11\n\tns_ip_cnt\x18\x11 \x01(\r\x12\x0e\n\x06ns_asn\x18\x12 \x01(\r\x12\x12\n\nns_country\x18\x13 \x01(\t\x12\x0f\n\x07mx_host\x18\x14 \x01(\t\x12\x13\n\x0bmx_host_cnt\x18\x15 \x01(\r\x12\r\n\x05mx_ip\x18\x16 \x01(\r\x12\x11\n\tmx_ip_cnt\x18\x17 \x01(\r\x12\x0e\n\x06mx_asn\x18\x18 \x01(\r\x12\x12\n\nmx_country\x18\x19 \x01(\t\"\xe1\x03\n\x07\x42gpInfo\x12\x0e\n\x06marker\x18\x01 \x01(\x0c\x12\x10\n\x08msg_type\x18\x02 \x01(\t\x12\x0f\n\x07version\x18\x03 \x01(\x05\x12\x0b\n\x03\x61sn\x18\x04 \x01(\x05\x12\x10\n\x08holdtime\x18\x05 \x01(\x05\x12\n\n\x02id\x18\x06 \x01(\r\x12\x11\n\tauth_code\x18\x07 \x01(\x05\x12\x11\n\tauth_data\x18\x08 \x01(\x0c\x12\x0c\n\x04nlri\x18\t \x01(\t\x12\x0e\n\x06origin\x18\n \x01(\x05\x12\x0f\n\x07\x61s_path\x18\x0b \x01(\t\x12\x10\n\x08next_hop\x18\x0c \x01(\r\x12\x0b\n\x03med\x18\r \x01(\x04\x12\x11\n\tlocalpref\x18\x0e \x01(\x04\x12\x0c\n\x04\x63omm\x18\x0f \x01(\t\x12\x10\n\x08\x65xt_comm\x18\x10 \x01(\t\x12\x11\n\torigin_id\x18\x11 \x01(\r\x12\x0f\n\x07\x63luster\x18\x12 \x01(\t\x12\x10\n\x08mp_reach\x18\x13 \x01(\t\x12\x12\n\nmp_unreach\x18\x14 \x01(\t\x12\x0f\n\x07\x61ggr_as\x18\x15 \x01(\t\x12\x11\n\taggr_addr\x18\x16 \x01(\t\x12\x13\n\x0b\x65rr_subcode\x18\x17 \x01(\x05\x12\x10\n\x08\x65rr_data\x18\x18 \x01(\t\x12\x11\n\twithdrawn\x18\x19 \x01(\t\x12\x0b\n\x03\x61\x66i\x18\x1a \x01(\x05\x12\x0b\n\x03res\x18\x1b \x01(\x05\x12\x0f\n\x07sub_afi\x18\x1c \x01(\x05\"\xe9\x01\n\nModbusInfo\x12\x0f\n\x07tran_id\x18\x01 \x01(\x05\x12\x10\n\x08proto_id\x18\x02 \x01(\x05\x12\x0b\n\x03len\x18\x03 \x01(\x05\x12\x0f\n\x07unit_id\x18\x04 \x01(\x05\x12\x0c\n\x04\x66unc\x18\x05 \x01(\x05\x12\r\n\x05opera\x18\x06 \x01(\t\x12\x11\n\texception\x18\x07 \x01(\x05\x12\x15\n\rexception_exp\x18\x08 \x01(\t\x12\r\n\x05\x64\x65lta\x18\t \x01(\x05\x12\x11\n\tnew_value\x18\n \x01(\x05\x12\x11\n\told_value\x18\x0b \x01(\x05\x12\x10\n\x08register\x18\x0c \x01(\x05\x12\x0c\n\x04\x61\x64\x64r\x18\r \x01(\x05\"\xfa\x04\n\x08X509Info\x12\x0f\n\x07version\x18\x01 \x01(\r\x12\x0e\n\x06serial\x18\x02 \x01(\t\x12\x14\n\x0c\x61lgorithm_id\x18\x03 \x01(\t\x12\x12\n\nowner_flag\x18\x04 \x01(\x05\x12\x11\n\tnotbefore\x18\x05 \x01(\r\x12\x10\n\x08notafter\x18\x06 \x01(\r\x12\x0c\n\x04\x64\x61ys\x18\x07 \x01(\r\x12\x16\n\x0e\x64\x61ys_remaining\x18\x08 \x01(\r\x12\x11\n\tissuer_on\x18\t \x01(\t\x12\x11\n\tissuer_cn\x18\n \x01(\t\x12\x12\n\nsubject_on\x18\x0b \x01(\t\x12\x12\n\nsubject_cn\x18\x0c \x01(\t\x12\x0e\n\x06issuer\x18\r \x01(\t\x12\x0f\n\x07subject\x18\x0e \x01(\t\x12\x10\n\x08keyusage\x18\x0f \x01(\t\x12\x12\n\nauth_keyid\x18\x10 \x01(\x0c\x12\x15\n\rsubject_keyid\x18\x11 \x01(\x0c\x12\x17\n\x0fkey_purpose_ids\x18\x12 \x01(\t\x12\x17\n\x0f\x63rl_dist_points\x18\x13 \x01(\t\x12\x12\n\npolicy_ids\x18\x14 \x01(\t\x12\x0e\n\x06\x61lt_ip\x18\x15 \x01(\t\x12\x12\n\nalt_domain\x18\x16 \x01(\t\x12\x16\n\x0e\x61lt_domain_cnt\x18\x17 \x01(\r\x12\x10\n\x08\x61uthinfo\x18\x18 \x01(\t\x12\x10\n\x08\x62\x61sic_ca\x18\x19 \x01(\x05\x12\x15\n\rbasic_pathlen\x18\x1a \x01(\r\x12\x0f\n\x07\x65xt_set\x18\x1b \x01(\t\x12\x0f\n\x07\x65xt_cnt\x18\x1c \x01(\r\x12\x17\n\x0f\x66ingerprint_alg\x18\x1d \x01(\t\x12\x13\n\x0b\x66ingerprint\x18\x1e \x01(\x0c\x12\x12\n\npublic_key\x18\x1f \x01(\x0c\x12\x0b\n\x03raw\x18  \x01(\x0c\"!\n\nBrowseInfo\x12\x13\n\x0bwin_version\x18\x01 \x01(\t\"\x86\x01\n\nSyslogInfo\x12\x10\n\x08\x66\x61\x63ility\x18\x01 \x01(\x05\x12\r\n\x05level\x18\x02 \x01(\x05\x12\x11\n\ttimestamp\x18\x03 \x01(\t\x12\x10\n\x08hostname\x18\x04 \x01(\t\x12\x0f\n\x07message\x18\x05 \x01(\t\x12\r\n\x05\x65vent\x18\x06 \x01(\t\x12\x12\n\nevent_info\x18\x07 \x01(\t\"\xac\x01\n\x08UserInfo\x12\x0f\n\x07string1\x18\x01 \x01(\t\x12\x0f\n\x07string2\x18\x02 \x01(\t\x12\x0f\n\x07string3\x18\x03 \x01(\t\x12\x0f\n\x07string4\x18\x04 \x01(\t\x12\x0c\n\x04num1\x18\x05 \x01(\r\x12\x0c\n\x04num2\x18\x06 \x01(\r\x12\x0c\n\x04num3\x18\x07 \x01(\r\x12\x0c\n\x04num4\x18\x08 \x01(\r\x12\x11\n\tprotocols\x18\t \x01(\t\x12\x11\n\tenodeb_id\x18\n \x01(\r\"D\n\tMevilInfo\x12\x37\n\x0emevil_hit_info\x18\x01 \x03(\x0b\x32\x1f.com.geeksec.proto.MevilHitInfo\"q\n\x0cMevilHitInfo\x12\x0f\n\x07task_id\x18\x01 \x01(\r\x12\x10\n\x08group_id\x18\x02 \x01(\r\x12\x0f\n\x07rule_id\x18\x03 \x01(\r\x12\r\n\x05\x62g_id\x18\x04 \x01(\r\x12\x0c\n\x04\x66ile\x18\x05 \x01(\t\x12\x10\n\x08pcap_idx\x18\x06 \x01(\x0c')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'protocol_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  DESCRIPTOR._loaded_options = None
  _globals['_METAINFO']._serialized_start=38
  _globals['_METAINFO']._serialized_end=1707
  _globals['_BASICINFO']._serialized_start=1710
  _globals['_BASICINFO']._serialized_end=5145
  _globals['_BASICSTRUCT']._serialized_start=5148
  _globals['_BASICSTRUCT']._serialized_end=5530
  _globals['_STPINFO']._serialized_start=5532
  _globals['_STPINFO']._serialized_end=5572
  _globals['_ARPINFO']._serialized_start=5574
  _globals['_ARPINFO']._serialized_end=5701
  _globals['_CDPINFO']._serialized_start=5704
  _globals['_CDPINFO']._serialized_end=5867
  _globals['_ICMPINFO']._serialized_start=5870
  _globals['_ICMPINFO']._serialized_end=6067
  _globals['_GREINFO']._serialized_start=6069
  _globals['_GREINFO']._serialized_end=6157
  _globals['_SSLINFO']._serialized_start=6160
  _globals['_SSLINFO']._serialized_end=6913
  _globals['_KRB5INFO']._serialized_start=6915
  _globals['_KRB5INFO']._serialized_end=7008
  _globals['_UNKNOWNINFO']._serialized_start=7010
  _globals['_UNKNOWNINFO']._serialized_end=7042
  _globals['_HTTPINFO']._serialized_start=7045
  _globals['_HTTPINFO']._serialized_end=8037
  _globals['_SMBINFO']._serialized_start=8040
  _globals['_SMBINFO']._serialized_end=8245
  _globals['_FTPINFO']._serialized_start=8248
  _globals['_FTPINFO']._serialized_end=8532
  _globals['_TELNETINFO']._serialized_start=8535
  _globals['_TELNETINFO']._serialized_end=8676
  _globals['_GTPINFO']._serialized_start=8679
  _globals['_GTPINFO']._serialized_end=8815
  _globals['_SOCKSINFO']._serialized_start=8818
  _globals['_SOCKSINFO']._serialized_end=8988
  _globals['_DBINFO']._serialized_start=8991
  _globals['_DBINFO']._serialized_end=9140
  _globals['_VNCINFO']._serialized_start=9143
  _globals['_VNCINFO']._serialized_end=9278
  _globals['_TFTPINFO']._serialized_start=9281
  _globals['_TFTPINFO']._serialized_end=9437
  _globals['_DHCPINFO']._serialized_start=9439
  _globals['_DHCPINFO']._serialized_end=9548
  _globals['_RDPINFO']._serialized_start=9550
  _globals['_RDPINFO']._serialized_end=9675
  _globals['_MAILINFO']._serialized_start=9678
  _globals['_MAILINFO']._serialized_end=10500
  _globals['_SNMPINFO']._serialized_start=10503
  _globals['_SNMPINFO']._serialized_end=10684
  _globals['_DNP3INFO']._serialized_start=10687
  _globals['_DNP3INFO']._serialized_end=10938
  _globals['_DIAMETERINFO']._serialized_start=10941
  _globals['_DIAMETERINFO']._serialized_end=11248
  _globals['_SSHINFO']._serialized_start=11251
  _globals['_SSHINFO']._serialized_end=11867
  _globals['_NTLMINFO']._serialized_start=11869
  _globals['_NTLMINFO']._serialized_end=11896
  _globals['_DNSINFO']._serialized_start=11899
  _globals['_DNSINFO']._serialized_end=12347
  _globals['_BGPINFO']._serialized_start=12350
  _globals['_BGPINFO']._serialized_end=12831
  _globals['_MODBUSINFO']._serialized_start=12834
  _globals['_MODBUSINFO']._serialized_end=13067
  _globals['_X509INFO']._serialized_start=13070
  _globals['_X509INFO']._serialized_end=13704
  _globals['_BROWSEINFO']._serialized_start=13706
  _globals['_BROWSEINFO']._serialized_end=13739
  _globals['_SYSLOGINFO']._serialized_start=13742
  _globals['_SYSLOGINFO']._serialized_end=13876
  _globals['_USERINFO']._serialized_start=13879
  _globals['_USERINFO']._serialized_end=14051
  _globals['_MEVILINFO']._serialized_start=14053
  _globals['_MEVILINFO']._serialized_end=14121
  _globals['_MEVILHITINFO']._serialized_start=14123
  _globals['_MEVILHITINFO']._serialized_end=14236
# @@protoc_insertion_point(module_scope)
