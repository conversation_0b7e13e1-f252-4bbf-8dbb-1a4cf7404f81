package com.geeksec.common.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import java.util.List;
import java.util.regex.Pattern;

/**
 * Ip转换工具
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019/11/9 13:12
 */
public class IpConvertUtil {
    /**
     * ip正则表达式
     */
    public static final String IP_REGEX = "(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)\\.(25[0-5]|2[0-4]\\d|[0-1]\\d{2}|[1-9]?\\d)";

    private static final Pattern IP_PATTERN = Pattern.compile(IP_REGEX);

    private static final String IP_SLICE = "(25[0-5]|2[0-4]\\d)|(1(\\d{0,2}))|(0)|(2\\d?)";

    /**
     * 验证ip格式是否正确
     *
     * @return 正确 true 错误 false
     */
    public static boolean checkIpStyle(String ip) {
        if (StringUtils.isEmpty(ip)) {
            return false;
        }
        return IP_PATTERN.matcher(ip).matches();
    }

    /**
     * 比较两个IP的大小, 1表示startIp大于endIp, -1表示startIp小于endIp ,0表示startIp等于endIp
     *
     * @param startIp
     * @param endIp
     * @return
     */
    public static int compareIp(String startIp, String endIp) {
        if (StringUtils.isEmpty(startIp) || StringUtils.isEmpty(endIp)) {
            throw new RuntimeException("ip不能为空");
        }
        long beginIp = convertIp(startIp);
        long finishIp = convertIp(endIp);
        if (beginIp > finishIp) {
            return 1;
        } else if (beginIp < finishIp) {
            return -1;
        } else {
            return 0;
        }
    }

    /**
     * 将ip转换十进制的long
     *
     * @param originIp
     * @return
     */
    public static Long convertIp(String originIp) {
        String[] originIpArray = originIp.split("\\.");
        int result = 0;
        for (int i = 0; i < originIpArray.length; i++) {
            result += Integer.parseInt(originIpArray[i]) << (i * 8);
        }
        return Long.parseLong(result + "");
    }

    /**
     * @param * @param numbers
     * @return java.lang.String
     * @author: xuemeng
     * @date: 12:15 2020/5/28
     * @since poly-platform
     */
    public static String convertToIp(List<Integer> ips) {
        Integer[] ints = ips.toArray(new Integer[0]);

        if (ints.length == 0 || ints.length > 4) {
            return " ";
        }
        if (ints.length == 1) {
            return convertToIp(ints[0]);
        }
        StringBuilder s = new StringBuilder(32);
        for (Integer num : ints) {
            for (int j = 0; j < 2; j++) {
                int current = num & 0xFFFF;
                s.append(Long.toString(current, 16)).append(":");
                num >>= 16;
            }
        }
        return s.substring(0, s.length() - 1);
    }

    /**
     * 将long类型的ip转换成ipv4格式的String
     *
     * @param originIp
     * @return
     * @deprecated
     */
    public static String convertLongToIp(Long originIp) {
        if (originIp == null || originIp.longValue() == 0L) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        if (originIp > Integer.MAX_VALUE || originIp < Integer.MIN_VALUE) {
            result.append((originIp & 0x000000FF));
            result.append(".");
            result.append((originIp & 0x0000FFFF) >>> 8);
            result.append(".");
            result.append((originIp & 0x00FFFFFF) >>> 16);
            result.append(".");
            result.append((originIp >>> 24));
        } else {
            Integer strInt = Integer.parseInt(originIp + "");
            result.append((strInt & 0x000000FF));
            result.append(".");
            result.append((strInt & 0x0000FFFF) >>> 8);
            result.append(".");
            result.append((strInt & 0x00FFFFFF) >>> 16);
            result.append(".");
            result.append((strInt >>> 24));

        }
        return result.toString();
    }

    /**
     * 将int类型的ip转换成ipv4格式的String
     *
     * @param i
     * @return
     * @deprecated
     */
    public static String int2ip(int i) {
        return convertLongToIp(Long.parseLong(i + ""));
    }

    /**
     * 将String类型的ip转换成ipv4格式的int
     *
     * @param ipAddress
     * @return
     * @deprecated
     */
    public static int ipToInt(String ipAddress) {

        int result = 0;

        String[] ipAddressInArray = ipAddress.split("\\.");

        for (int i = 3; i >= 0; i--) {

            int ip = Integer.parseInt(ipAddressInArray[3 - i]);

            // left shifting 24,16,8,0 and bitwise OR

            // 1. 192 << 24
            // 1. 168 << 16
            // 1. 1 << 8
            // 1. 2 << 0
            result |= ip << (i * 8);

        }

        return result;
    }

    /**
     * 判断Long形的ip是否是同一子网段
     *
     * @param originIp
     * @param oldIp
     * @return
     */
    public static Boolean isSameSubNet(Long originIp, Long oldIp) {
        Boolean result;
        String[] originIps = convertLongToIp(originIp).split("\\.");
        String[] oldIps = convertLongToIp(oldIp).split("\\.");
        if (originIps[0].equals(oldIps[0]) && originIps[1].equals(oldIps[1]) && originIps[2].equals(oldIps[2])) {
            result = true;
        } else {
            result = false;
        }
        return result;
    }

    /**
     * @Author: xuemeng
     * @Description: 根据Ip前缀获取最大ip的long值
     * @Date: 17:50 2019/11/18
     */
    public static Long getLongMinIpSub(String ipSub) {
        if (!regexSubIp(ipSub)) {
            return null;
        }
        if (StringUtils.endsWith(ipSub, ".")) {
            ipSub += "0";
        }
        String[] strArr = ipSub.split("\\.");
        String ipMax = "";
        int len = strArr.length;
        for (int i = 0; i < 4; i++) {
            ipMax += "." + (len > i ? strArr[i] : 0);
        }
        System.out.println(ipMax.substring(1));
        return convertIp(ipMax.substring(1));
    }

    /**
     * @Author: xuemeng
     * @Description: 根据Ip前缀获取最小的ip 的 Long 值
     * @Date: 17:50 2019/11/18
     */
    public static Long getLongMaxIpSub(String ipSub) {
        if (StringUtils.endsWith(ipSub, ".")) {
            ipSub += "2";
        }
        if (!regexSubIp(ipSub)) {
            return null;
        }
        String[] strArr = ipSub.split("\\.");
        String ipMax = "";
        int len = strArr.length;
        for (int i = 0; i < 4; i++) {
            ipMax += ("." + (len - 1 > i ? strArr[i] : len - 1 == i ? getMax(NumberUtils.createInteger(strArr[i])) : "255"));
        }
        System.out.println(ipMax.substring(1));
        return convertIp(ipMax.substring(1));
    }

    private static int getMax(int a) {
        return a < 10 ? a == 0 ? 0 : a == 1 ? a * 100 + 99 : a == 2 ? a * 100 + 55 : a * 10 + 9 : a < 25 ? a * 10 + 9 : a == 25 ? 255 : a;
    }

    /**
     * @Author: xuemeng
     * @Description: ip前缀校验
     * @Date: 17:53 2019/11/18
     */
    private static boolean regexSubIp(String ipSub) {
        // 校验
        if (StringUtils.isBlank(ipSub)) {
            return false;
        }
        if (StringUtils.startsWith(ipSub, ".")) {
            return false;
        }
        String[] strArr = ipSub.split("\\.");
        if (strArr == null || strArr.length == 0 || StringUtils.equals(strArr[0], "0") || strArr.length > 4) {
            return false;
        }
        // 校验
        for (String s : strArr) {
            if (!Pattern.matches(IP_SLICE, s)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 将long类型的ip转换成ipv4格式的String
     *
     * @param originIp
     * @return
     */
    public static String convertToIp(int originIp) {
        if (originIp == 0) {
            return "0.0.0.0";
        }
        StringBuilder result = new StringBuilder();
        Integer strInt = Integer.parseInt(originIp + "");
        result.append((strInt & 0x000000FF));
        result.append(".");
        result.append((strInt & 0x0000FFFF) >>> 8);
        result.append(".");
        result.append((strInt & 0x00FFFFFF) >>> 16);
        result.append(".");
        result.append((strInt >>> 24));
        return result.toString();
    }

    /**
     * 将ipv4格式的String转换成long类型的ip
     *
     * @param originIp
     * @return
     */
    public static int convertToIp(String originIp) {
        int result = 0;

        String[] ipAddressInArray = originIp.split("\\.");

        for (int i = 0; i < 4; i++) {

            int ip = Integer.parseInt(ipAddressInArray[i]);

            // left shifting 24,16,8,0 and bitwise OR

            // 1. 192 << 24
            // 1. 168 << 16
            // 1. 1 << 8
            // 1. 2 << 0
            result |= ip << (i * 8);

        }

        return result;
    }

    /**
     * ip地址转成long型数字
     * 将IP地址转化成整数的方法如下：
     * 1、通过String的split方法按.分隔得到4个长度的数组
     * 2、通过左移位操作（<<）给每一段的数字加权，第一段的权为2的24次方，第二段的权为2的16次方，第三段的权为2的8次方，最后一段的权为1
     *
     * @param strIp
     * @return
     */
    public static long ipToLong(String strIp) {
        String[] ip = strIp.split("\\.");
        return (Long.parseLong(ip[3]) << 24) + (Long.parseLong(ip[2]) << 16) + (Long.parseLong(ip[1]) << 8) + Long.parseLong(ip[0]);
    }

    /**
     * 推荐使用
     * 将十进制整数形式转换成127.0.0.1形式的ip地址
     * 将整数形式的IP地址转化成字符串的方法如下：
     * 1、将整数值进行右移位操作（>>>），右移24位，右移时高位补0，得到的数字即为第一段IP。
     * 2、通过与操作符（&）将整数值的高8位设为0，再右移16位，得到的数字即为第二段IP。
     * 3、通过与操作符吧整数值的高16位设为0，再右移8位，得到的数字即为第三段IP。
     * 4、通过与操作符吧整数值的高24位设为0，得到的数字即为第四段IP。
     *
     * @param longIp
     * @return
     */
    public static String longToIp(long longIp) {
        StringBuffer sb = new StringBuffer("");
        // 将高24位置0
        sb.append(String.valueOf((longIp & 0x000000FF)));
        sb.append(".");
        // 将高16位置0，然后右移8位
        sb.append(String.valueOf((longIp & 0x0000FFFF) >>> 8));
        sb.append(".");
        // 将高8位置0，然后右移16位
        sb.append(String.valueOf((longIp & 0x00FFFFFF) >>> 16));
        sb.append(".");
        // 直接右移24位
        sb.append(String.valueOf((longIp >>> 24)));
        return sb.toString();
    }

}
