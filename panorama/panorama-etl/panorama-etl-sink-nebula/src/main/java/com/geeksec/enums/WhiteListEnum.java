package com.geeksec.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 白名单属性
 * <p>
 *     文档注释:
 *     >0:在白名单内
 *     此处的取值范围是0x0001-0x0015 使用的是位运算存值（即排列组合）
 * </p>
 *
 */
public enum WhiteListEnum {

    NO_WHITE(0, "非白名单"),
    CDN(0x0001, "CDN"),
    WEB_CRAWLER(0x0002, "网络爬虫"),
    DOMAIN_NAME_SERVER(0x0004, "域名服务器"),
    OTHER(0x0008, "其它");

    /**
     * 值
     */
    private Integer code;

    /**
     * 类型
     */
    private String msg;

    WhiteListEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, WhiteListEnum> codeEnumMap = new HashMap<>();

    static {
        for (WhiteListEnum e : WhiteListEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        WhiteListEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }

}
