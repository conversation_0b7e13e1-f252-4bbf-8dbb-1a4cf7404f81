package com.geeksec.transfer.function.process.protocol;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.AttackAlertTrans;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.AlertLog;
import com.geeksec.transfer.handle.IPTransHandler;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertAttackProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {

    private static final Logger logger = LoggerFactory.getLogger(AlertAttackProcessFunction.class);

    /**
     * 需要提取的点边关系
     * 点：
     * IP
     * ORG
     * ATTACK
     * 边：
     * ip_belong_to_org
     * make_attack
     * attack_to
     * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, Row>.Context context, Collector<Row> collector) throws Exception {
        try {
            IPTrans sip = IPTransHandler.transIp(alertLog.getSip());
            IPTrans dip = IPTransHandler.transIp(alertLog.getDip());

            List<Row> rows = new ArrayList<>(IPTransHandler.getAllIpRows(sip, dip));

            AttackAlertTrans attackAlertTrans = AlertTools.createAttackAlertTrans(alertLog);

            if (attackAlertTrans != null) {
                rows.addAll(attackAlertTrans.getAllRows());
            }

            for (Row row : rows) {
                collector.collect(row);
            }
        } catch (Exception e) {
            logger.error("Error processing file Attack log: {}", alertLog.getGuid(), e);
        }
    }
}
