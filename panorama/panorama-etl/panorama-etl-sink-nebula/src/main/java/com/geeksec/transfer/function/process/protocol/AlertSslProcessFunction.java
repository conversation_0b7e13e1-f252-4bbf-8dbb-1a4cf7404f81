package com.geeksec.transfer.function.process.protocol;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.SslAlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertSslProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {


    private static final Logger logger = LoggerFactory.getLogger(AlertSslProcessFunction.class);

    /**
     * 需要提取的点边关系
     * 点：
     * APP
     * 边：
     * app_connect_cert
     * client_use_app
     * app_belong_to_server
     * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, Row>.Context context, Collector<Row> collector) throws Exception {
        try {
            // 提取X509证书告警日志元数据
            SslAlertTrans sslAlertTrans = AlertTools.createSslAlertTrans(alertLog);
            if (sslAlertTrans != null) {
                List<Row> rows = sslAlertTrans.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing Ssl alert log: {}", alertLog.getGuid(), e);
        }
    }
}
