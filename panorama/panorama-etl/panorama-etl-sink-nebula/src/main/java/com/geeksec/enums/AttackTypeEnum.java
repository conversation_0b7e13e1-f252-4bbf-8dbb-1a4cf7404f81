package com.geeksec.enums;

import org.apache.commons.lang.math.NumberUtils;

import java.util.*;

/**
 * 威胁类型（attack_type）
 */
public enum AttackTypeEnum {


    /**
     * 1 APT事件
     */
    APT(0x0100, -1, "APT事件"),
    APT_EVENT(0x0101, 0x0100, "APT攻击"),
    APT_CUSTOM(0x01fe, 0x0100, "APT事件用户自定义"),
    APT_OTHER(0x01ff, 0x0100, "其他APT类型"),

    /**
     * 2 侦察
     */
    RECONNAISSANCE(0x0200, -1, "侦察"),
    PORT_SCAN(0x0201, 0x0200, "端口扫描"),
    NETWORK_SCAN(0x0202, 0x0200, "网络扫描"),
    CUSTOM(0x02fe, 0x0200, "侦察用户自定义"),
    RECONNAISSANCE_OTHER(0x02ff, 0x0200, "其他侦察类型"),

    /**
     * 3 拒绝服务
     */
    DENIAL_SERVICE(0x0300, -1, "拒绝服务"),
    SYN_FLOOD(0x0301, 0x0300, "SYN Flood"),
    ACK_FLOOD(0x0302, 0x0300, "ACK Flood"),
    HTTP_FLOOD(0x0303, 0x0300, "HTTP Flood"),
    UDP_FLOOD(0x0304, 0x0300, "UDP Flood"),
    DNS_FLOOD(0x0305, 0x0300, "DNS Flood"),
    DENIAL_SERVICE_CUSTOM(0x03fe, 0x0300, "拒绝服务用户自定义"),
    OTHER_DENIAL_OF_SERVICE(0x03ff, 0x0300, "其他拒绝服务"),

    /**
     * 4 攻击利用
     */
    ATTACK_UTILIZATION(0x0400, -1, "攻击利用"),
    SQL_INJECTION(0x0401, 0x0400, "SQL注入"),
    URL_JUMP(0x0402, 0x0400, "URL跳转"),
    CODE_EXECUTION(0x0403, 0x0400, "代码执行"),
    UNAUTHORIZED_ACCESS(0x0404, 0x0400, "非授权访问/权限绕过"),
    XSS(0x0405, 0x0400, "跨站脚本攻击XSS"),
    CSRF(0x0406, 0x0400, "跨站请求伪造CSRF"),
    LOGIC_ERROR(0x0407, 0x0400, "逻辑/设计错误"),
    COMMAND_EXECUTION(0x0408, 0x0400, "命令执行"),
    DIRECTORY_TRAVERSAL(0x0409, 0x0400, "目录遍历"),
    CONFIGURATION_ERROR(0x040a, 0x0400, "配置不当/错误"),
    WEAK_PASSWORD(0x040b, 0x0400, "弱口令"),
    FILE_CONTAINS(0x040c, 0x0400, "文件包含"),
    FILE_READ(0x040d, 0x0400, "文件读取"),
    FILE_UPLOAD(0x040e, 0x0400, "文件上传"),
    DOCUMENT_DOWNLOAD(0x040f, 0x0400, "文件下载"),
    FILE_WRITE(0x0410, 0x0400, "文件写入"),
    OVERFLOW_ATTACK(0x0411, 0x0400, "溢出攻击"),
    INFORMATION_DISCLOSURE(0x0412, 0x0400, "信息泄露"),
    BROWSER_HIJACKING(0x0413, 0x0400, "浏览器劫持"),
    VIOLENT_GUESS(0x0414, 0x0400, "暴力猜解"),
    PHISHING(0x0415, 0x0400, "网络钓鱼"),
    WEBSHELL_UPLOAD(0x0416, 0x0400, "webshell上传"),
    BACK_DOOR(0x0417, 0x0400, "后门程序"),
    CONCEALED_TUNNEL(0x0418, 0x0400, "隐蔽隧道"),
    PROXY_TOOL(0x0419, 0x0400, "代理工具"),
    SUSPICIOUS_FILE_NAME(0x041a, 0x0400, "可疑文件名"),
    ABNORMAL_LOGIN(0x041b, 0x0400, "异常登录"),
    OS_COMMAND_INJECTION(0x041c, 0x0400, "OS命令注入"),
    WEBSHELL_UTILIZATION(0x041d, 0x0400, "WebShell利用"),
    PROTOCOL_EXCEPTION(0x041e, 0x0400, "协议异常"),
    ATTACK_UTILIZATION_CUSTOM(0x04fe, 0x0400, "攻击利用用户自定义"),
    ATTACK_UTILIZATION_OTHER(0x04ff, 0x0400, "其他攻击利用"),


    /**
     * 恶意域名
     */
    DNS_ALERT(0x0500, -1, "恶意域名"),
    DNS_DGA_DOMAIN(0x0501, 0x0500, "DGA静态域名"),
    DNS_DGA_RULE(0x0502, 0x0500, "DGA动态规则"),
    DNS_FAST_FLUX(0x0503, 0x0500, "FastFlux"),
    DNS_REBIND(0x0504, 0x0500, "DNS重绑定"),
    DNS_REFLECT(0x0505, 0x0500, "DNS反射放大攻击"),
    DNS_IDN(0x0506, 0x0500, "IDN可疑域名"),
    DNS_SINKHOLE(0x0507, 0x0500, "Sinkhole"),
    DNS_RING_ADDRESS(0x0508, 0x0500, "DNS隐蔽隧道"),
    DNS_SUS_DYN_DOMAIN(0x0509, 0x0500, "环路地址"),
    DNS_SUSPICIOUS_DYNAMIC_DOMAIN(0x050a, 0x0500, "可疑动态域名"),
    DNS_HEARTBEAT_DOMAIN(0x050b, 0x0500, "心跳域名"),
    DNS_HEART_BEAT_DOMAIN(0x05fe, 0x0500, "恶意域名用户自定义"),
    DNS_OTHER(0x05ff, 0x0500, "其他恶意域名"),

    /**
     * 元数据-恶意软件
     */
    METADATA_MALICIOUS(0x0600, -1, "威胁情报"),
    METADATA_BACKDOOR_PROGRAM(0x0601, 0x0600, "漏洞利用程序"),
    METADATA_BOTNET(0x0602, 0x0600, "僵尸网络"),
    METADATA_TROJAN(0x0603, 0x0600, "特洛伊木马"),
    METADATA_VIRUS(0x0604, 0x0600, "电脑病毒"),
    METADATA_SPYWARE(0x0605, 0x0600, "元数据-间谍软件"),
    METADATA_MALICIOUS_ADVERTISING(0x0606, 0x0600, "恶意广告"),
    METADATA_REMOTE_CONTROL_TROJAN(0x0607, 0x0600, "远控木马"),
    METADATA_KEY_LOGGER(0x0608, 0x0600, "键盘记录"),
    METADATA_STEALING_TROJAN(0x0609, 0x0600, "窃密木马"),
    METADATA_NETWORK_WORM(0x060a, 0x0600, "网络蠕虫"),
    METADATA_RANSOMWARE(0x060b, 0x0600, "勒索软件"),
    METADATA_BLACK_MARKET_TOOL(0x060c, 0x0600, "黑市工具"),
    METADATA_ROGUE_PROMOTION(0x060d, 0x0600, "流氓推广"),
    METADATA_MALICIOUS_DOWNLOAD(0x060e, 0x0600, "恶意下载"),
    METADATA_INFECTIOUS_VIRUS(0x060f, 0x0600, "感染型病毒"),
    METADATA_MINING_CUSTOM(0x0610, 0x0600, "挖矿病毒"),
    METADATA_CUSTOM(0x06fe, 0x0600, "恶意软件用户自定义"),
    METADATA_MALICIOUS_OTHER(0x06ff, 0x0600, "其他恶意软件"),
    /**
     * 文件-恶意软件
     */
    MALICIOUS(0x0700, -1, "恶意文件"),
    BACKDOOR_PROGRAM(0x0701, 0x0700, "后门程序"),
    KEY_LOGGER(0x0702, 0x0700, "键盘记录"),
    TROJAN(0x0703, 0x0700, "特洛伊木马"),
    REMOTE_CONTROL_TROJAN(0x0704, 0x0700, "远控木马"),
    STEALING_TROJAN(0x0705, 0x0700, "窃密木马"),
    VIRUS(0x0706, 0x0700, "电脑病毒"),
    BOTNET(0x0707, 0x0700, "僵尸网络"),
    NETWORK_WORM(0x0708, 0x0700, "网络蠕虫"),
    SPYWARE(0x0709, 0x0700, "文件-间谍软件"),
    MALICIOUS_ADVERTISING(0x070a, 0x0700, "恶意广告"),
    ROGUE_PROMOTION(0x070b, 0x0700, "流氓推广"),
    RANSOMWARE(0x070c, 0x0700, "勒索软件"),
    BLACK_MARKET_TOOL(0x070d, 0x0700, "黑市工具"),
    MALICIOUS_DOWNLOAD(0x070e, 0x0700, "攻击利用套件"),
    INFECTIOUS_VIRUS(0x070f, 0x0700, "漏洞利用程序"),
    SUSPECT_MALICIOUS(0x07fd, 0x0700, "疑似恶意软件"),
    MALICIOUS_CUSTOM(0x07fe, 0x0700, "恶意软件用户自定义"),
    MALICIOUS_OTHER(0x07ff, 0x0700, "其他恶意软件");

    private static Map<Integer, String> mapChild2Parent;

    private static Map<Integer, AttackTypeEnum> codeEnumMap = new HashMap<>();

    /**
     * key: 父节点 value: 所有的子节点
     */
    private static Map<Integer, List<Integer>> parentToChildrenMap = new HashMap<>(16);
    /**
     * 类型编号
     */
    private Integer code;
    /**
     * 父级类型
     */
    private Integer parent;
    /**
     * 名称
     */
    private String msg;

    AttackTypeEnum(Integer code, Integer parent, String msg) {
        this.code = code;
        this.parent = parent;
        this.msg = msg;
    }


    static {
        for (AttackTypeEnum e : AttackTypeEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    static {
        // 所有父节点信息
        List<Integer> parentCode = new ArrayList<>(16);

        for (AttackTypeEnum e : AttackTypeEnum.values()) {
            if (e.getParent() == -1) {
                parentCode.add(e.getCode());
            }
        }

        // 提前将 children 引用放入到 parentToChildrenMap
        for (Integer parent : parentCode) {
            List<Integer> children = new ArrayList<>(16);
            parentToChildrenMap.put(parent, children);
        }

        // 再次遍历，填充子节点
        for (AttackTypeEnum e : AttackTypeEnum.values()) {
            boolean contains = parentCode.contains(e.getParent());
            if (contains) {
                parentToChildrenMap.get(e.getParent()).add(e.getCode());
            }
        }
    }

    public static Map<Integer, List<Integer>> getParentToChildrenMap() {
        return parentToChildrenMap;
    }

    public static String getMsgByCode(Integer code) {
        AttackTypeEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }

    public static AttackTypeEnum get(Integer code) {
        return codeEnumMap.get(code);
    }

    public static String getMsgByCode(Set<String> codeSet, String delimiter) {
        Set<String> set = new HashSet<>();
        for (String str : codeSet) {
            Integer code = NumberUtils.createInteger(str);
            set.add(getMsgByCode(code));
        }
        return String.join(delimiter, set);
    }

    public static String getParentFull(Set<String> codeSet, String delimiter) {
        Set<String> set = new HashSet<>();
        for (String str : codeSet) {
            Integer code = NumberUtils.createInteger(str);
            set.add(getParentFull(code));
        }
        return String.join(delimiter, set);
    }

    public static String getParentCodes(Set<String> codeSet, String delimiter) {
        Set<String> set = new HashSet<>();
        for (String str : codeSet) {

            AttackTypeEnum attackTypeEnum = AttackTypeEnum.get(Integer.valueOf(str));
            if (attackTypeEnum == null) {
                continue;
            }
            if (attackTypeEnum.getParent() != -1) {
                set.add(String.valueOf(attackTypeEnum.getParent()));
            } else {
                set.add(str);
            }
        }
        return String.join(delimiter, set);
    }

    public static String getParent(Integer child) {
        if (mapChild2Parent == null) {
            synchronized (AttackTypeEnum.class) {
                if (mapChild2Parent == null) {
                    mapChild2Parent = new HashMap<>(64);
                    for (AttackTypeEnum e : AttackTypeEnum.values()) {
                        Integer pa = e.getParent();
                        if (pa == -1) {
                            mapChild2Parent.put(e.code, "");
                            continue;
                        }
                        for (AttackTypeEnum e1 : AttackTypeEnum.values()) {
                            if (e1.getCode().equals(pa)) {
                                mapChild2Parent.put(e.code, e1.msg);
                            }
                        }
                    }
                }
            }
        }
        return mapChild2Parent.get(child);
    }

    public static String getParentFull(Integer child) {
        if (mapChild2Parent == null) {
            synchronized (AttackTypeEnum.class) {
                if (mapChild2Parent == null) {
                    mapChild2Parent = new HashMap<>(64);
                    for (AttackTypeEnum e : AttackTypeEnum.values()) {
                        Integer pa = e.getParent();
                        if (pa == -1) {
                            mapChild2Parent.put(e.code, e.msg);
                            continue;
                        }
                        for (AttackTypeEnum e1 : AttackTypeEnum.values()) {
                            if (e1.getCode().equals(pa)) {
                                mapChild2Parent.put(e.code, e1.msg);
                            }
                        }
                    }
                }
            }
        }
        return mapChild2Parent.get(child);
    }

    public static List<Integer> getSecondLevel(Integer code) {
        List<Integer> secondLevel = new ArrayList<>();
        secondLevel.add(code);
        for (AttackTypeEnum e : AttackTypeEnum.values()) {
            if (e.getParent().equals(code)) {
                secondLevel.add(e.getCode());
            }
        }
        return secondLevel;
    }

    /**
     * 枚举查询 根据 code 查询message信息
     *
     * @param code code值
     * @return message
     */
    public static String getEnumMessage(Integer code) {
        AttackTypeEnum attackTypeEnum = AttackTypeEnum.get(code);
        return Objects.isNull(attackTypeEnum) ? null : attackTypeEnum.getMsg();
    }

    /**
     * 枚举查询 根据 code 查询父类的message信息
     * 如果该节点已经是1级节点（威胁类型），那么就直接返回该节点信息
     * 如果该节点是二级节点（威胁事件），那么返回它父节点威胁类型的信息
     *
     * @param code code值
     * @return 父类的message信息
     */
    public static String getEnumParentMsg(Integer code) {
        AttackTypeEnum attackTypeEnum = AttackTypeEnum.get(code);
        if (Objects.nonNull(attackTypeEnum)) {
            Integer parent = attackTypeEnum.getParent();
            if (parent == -1) {
                return attackTypeEnum.getMsg();
            }
            AttackTypeEnum attackTypeEnumParent = AttackTypeEnum.get(parent);
            return Objects.isNull(attackTypeEnumParent) ? null : attackTypeEnumParent.getMsg();
        }
        return null;
    }


    public static String getMsgFull(Integer code, String delimiter) {
        AttackTypeEnum attackTypeEnum = AttackTypeEnum.get(code);
        if (attackTypeEnum == null) {
            return "";
        }
        if (attackTypeEnum.getParent() == -1) {
            return attackTypeEnum.getMsg();
        }

        AttackTypeEnum parent = AttackTypeEnum.get(attackTypeEnum.getParent());
        return String.join(delimiter, parent.getMsg(), attackTypeEnum.getMsg());
    }

    public static String getCodeFull(Integer code, String delimiter) {
        AttackTypeEnum attackTypeEnum = AttackTypeEnum.get(code);
        if (attackTypeEnum == null) {
            return "";
        }
        if (attackTypeEnum.getParent() == -1) {
            return attackTypeEnum.getCode().toString();
        }

        AttackTypeEnum parent = AttackTypeEnum.get(attackTypeEnum.getParent());
        return String.join(delimiter, parent.getCode().toString(), attackTypeEnum.getCode().toString());
    }

    public static List<Integer> getDnsCodes() {
        List<Integer> codes = new ArrayList<>();
        for (AttackTypeEnum value : AttackTypeEnum.values()) {
            if (value.toString().startsWith("DNS_")) {
                codes.add(value.getCode());
            }
        }
        return codes;
    }

    /**
     * 获取所有的一级code
     *
     * @return
     */
    public static List<Integer> getFirstLevelCodes() {
        List<Integer> codes = new ArrayList<>();
        for (AttackTypeEnum value : AttackTypeEnum.values()) {
            if (value.getParent() == -1) {
                codes.add(value.getCode());
            }
        }
        return codes;
    }

    public Integer getCode() {
        return code;
    }

    public Integer getParent() {
        return parent;
    }

    public String getMsg() {
        return msg;
    }

}
