package com.geeksec.task;

import cn.hutool.core.date.DateUtil;
import com.geeksec.common.constant.DetectTypeConstant;
import com.geeksec.common.constant.PanoramaOutPutTagConstant;
import com.geeksec.common.deserializer.AlertLogDeserializer;
import com.geeksec.nebula.option.NebulaSinkOptionHandler;
import com.geeksec.nebula.sideout.RowSideOutHandler;
import com.geeksec.proto.AlertLog;
import com.geeksec.proto.message.*;
import com.geeksec.transfer.function.process.alarm.*;
import com.geeksec.transfer.function.process.protocol.*;
import com.twitter.chill.protobuf.ProtobufSerializer;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.nebula.sink.NebulaSinkFunction;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class PanoramaAlertKafka2Nebula {

    private static final Logger logger = LoggerFactory.getLogger(PanoramaAlertKafka2Nebula.class);

    // kafka地址
    public static String BROKER_LIST;

    // kafka消费组
    public static String GROUP_ID;

    // kafka主题(告警日志)
    public static String KAFKA_CONSUMER_TOPIC;

    public static final int PA1 = 1;
    public static final int PA2 = 2;
    public static final int PA4 = 4;
    public static final int PA8 = 8;
    public static final int PA16 = 16;

    public static final String HTTP_MSG = "http";
    public static final String DNS_MSG = "dns";
    public static final String SSL_MSG = "ssl";
    public static final String TLS_MSG = "tls";
    public static final String X509_MSG = "x509";

    public static void main(String[] args) throws Exception {
        logger.info("Starting init aggregation node alert info into nebula...");

        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 通过外部配置文件进行配置设置
        ParameterTool parameterTool = ParameterTool.fromArgs(args);

        String configPath = parameterTool.get("config_path");

        // 从配置文件加载配置
        ParameterTool config = ParameterTool.fromPropertiesFile(configPath);
        BROKER_LIST = config.get("kafka.bootstrap.server.filter");
        GROUP_ID = config.get("kafka.group.id.nebula");
        KAFKA_CONSUMER_TOPIC = config.get("kafka.topic.filtered");

        // 注册对应PB解析器(新版日志)
        env.getConfig().registerTypeWithKryoSerializer(AlertLog.ALERT_LOG.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IocAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IobAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IoaAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IiotAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(FileAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(CryptoAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(CertAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(MailAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(MobileAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(ProtoAlertInfo.class,ProtobufSerializer.class);


        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                //最大失败次数
                5,
                // 衡量失败次数的是时间段
                Time.of(2, TimeUnit.SECONDS),
                // 间隔
                Time.of(5, TimeUnit.SECONDS)
        ));
        // 设置链式任务和重置时间点
        env.disableOperatorChaining();

        // 1.创建Kafka自定义 Source
        Properties properties = new Properties();
        properties.put("bootstrap.servers", BROKER_LIST);
        properties.setProperty("group.id", GROUP_ID);

        // 2.开始获取Kafka中的 Alert_log的信息
        KafkaSource<AlertLog.ALERT_LOG> kafkaSource = KafkaSource.<AlertLog.ALERT_LOG>builder()
                .setBootstrapServers(BROKER_LIST)
                .setTopics(KAFKA_CONSUMER_TOPIC)
                .setGroupId(GROUP_ID)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(new AlertLogDeserializer())
                .build();
        DataStream<AlertLog.ALERT_LOG> sourceStream = env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka Source").setParallelism(PA2).name("读取Kafka中告警日志信息");
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> detectTypeSplitStream = sourceStream.process(new ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>() {
            @Override
            public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context ctx, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
                // 获取日志类型
                int detectType = alertLog.getDetectType();
                // 根据日志类型进分流
                switch (detectType) {
                    case DetectTypeConstant.IOC_ALERT_INFO:
                        // 规则告警信息
                        ctx.output(PanoramaOutPutTagConstant.IOC_ALERT_OUTPUT_TAG, alertLog);
                        break;
                    case DetectTypeConstant.IOA_ALERT_INFO:
                        ctx.output(PanoramaOutPutTagConstant.IOA_ALERT_OUTPUT_TAG, alertLog);
                        break;
                    case DetectTypeConstant.FILE_ALERT_INFO:
                        ctx.output(PanoramaOutPutTagConstant.SANDBOX_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    case DetectTypeConstant.CRYPTO_ALERT_INFO:
                        ctx.output(PanoramaOutPutTagConstant.CRYPTO_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    case DetectTypeConstant.MAIL_ALERT_INFO:
                        ctx.output(PanoramaOutPutTagConstant.EMAIL_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    case DetectTypeConstant.IIOT_ALERT_INFO:
                        ctx.output(PanoramaOutPutTagConstant.IOT_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    default:
                        break;
                }
            }
        }).setParallelism(PA8);


        SingleOutputStreamOperator<AlertLog.ALERT_LOG> appProtoTypeSplitStream = sourceStream.process(new ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>() {
            @Override
            public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context ctx, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
                // 获取日志类型
                String appProto = alertLog.getAppProto().toLowerCase();

                // 根据日志类型进分流
                switch (appProto) {
                    case HTTP_MSG:
                        // 规则告警信息
                        ctx.output(PanoramaOutPutTagConstant.HTTP_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    case DNS_MSG:
                        ctx.output(PanoramaOutPutTagConstant.DNS_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    case SSL_MSG:
                    case TLS_MSG:
                        ctx.output(PanoramaOutPutTagConstant.SSL_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    case X509_MSG:
                        ctx.output(PanoramaOutPutTagConstant.X509_ALERT_INFO_OUTPUT_TAG, alertLog);
                        break;
                    default:
                        break;
                }
            }
        }).setParallelism(PA8);

        // 3.开始分批处理对应的日志
        DataStream<AlertLog.ALERT_LOG> alarmIocStream = detectTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.IOC_ALERT_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmIoaStream = detectTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.IOA_ALERT_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmEmailStream = detectTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.EMAIL_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmFileStream = detectTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.SANDBOX_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmCryptoStream = detectTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.CRYPTO_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmIotStream = detectTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.IOT_ALERT_INFO_OUTPUT_TAG);

        DataStream<AlertLog.ALERT_LOG> appHttpStream = appProtoTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.HTTP_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> appDnsStream = appProtoTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.DNS_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> appSslStream = appProtoTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.SSL_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> appX509Stream = appProtoTypeSplitStream.getSideOutput(PanoramaOutPutTagConstant.X509_ALERT_INFO_OUTPUT_TAG);


        // 4.1 开始处理抽取和处理Http协议数据
        DataStream<Row> parsedHttpStream = appHttpStream
                .process(new AlertHttpProcessFunction())
                .name("HTTP协议告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 4.2 开始抽取和处理DNS协议数据
        SingleOutputStreamOperator<Row> parsedDnsStream = appDnsStream
                .process(new AlertDnsProcessFunction())
                .name("DNS协议告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 4.3 开始抽取X509证书协议数据
        SingleOutputStreamOperator<Row> parsedX509Stream = appX509Stream
                .process(new AlertX509ProcessFunction())
                .name("X509协议告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 4.4 开始抽取ssl协议元数据
        SingleOutputStreamOperator<Row> parsedSslStream = appSslStream
                .process(new AlertSslProcessFunction())
                .name("SSL协议告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 4.5 IP关系
        SingleOutputStreamOperator<Row> parsedAttackStream = sourceStream
                .process(new AlertAttackProcessFunction())
                .name("IP 图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 5.1 开始处理 FILE_ALERT 文件数据流
        SingleOutputStreamOperator<Row> parsedFileStream = alarmFileStream
                .process(new AlertFileProcessFunction())
                .name("FILE 告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 5.2 开始处理 IOC_ALERT 文件数据流
        SingleOutputStreamOperator<Row> parsedIocStream = alarmIocStream
                .process(new AlertIocProcessFunction())
                .name("IOC 告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 5.3 开始处理 CRYPTO_ALERT 文件数据流
        SingleOutputStreamOperator<Row> parsedCryptoStream = alarmCryptoStream
                .process(new AlertCryptoProcessFunction())
                .name("CRYPTO 告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 5.4 开始处理 IOA_ALERT 文件数据流
        SingleOutputStreamOperator<Row> parsedIoaStream = alarmIoaStream
                .process(new AlertIoaProcessFunction())
                .name("IOA 告警图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 5.5 开始处理 MAIL_ALERT 邮件告警检测日志流
        SingleOutputStreamOperator<Row> parsedEmailStream = alarmEmailStream
                .process(new AlertEmailProcessFunction())
                .name("MAIL 检测图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // TODO IOT关系抽取
        // 5.6 开始处理 IIOT_ALERT 工控告警检测日志流
        SingleOutputStreamOperator<Row> parsedIotStream = alarmIotStream
                .process(new AlertIotProcessFunction())
                .name("IOT 检测图数据节点&关联关系抽取")
                .setParallelism(PA4);

        // 将准备好的所有待入Nebula的Row流合并
        DataStream<Row> allParsedAlertStream = parsedHttpStream.union(parsedDnsStream, parsedX509Stream,
                parsedSslStream, parsedAttackStream, parsedFileStream, parsedIocStream, parsedCryptoStream,
                parsedIoaStream, parsedEmailStream, parsedIotStream);

        SingleOutputStreamOperator<Row> alertInfoRowSideOutStream = RowSideOutHandler.handleRowSideOutPutTag(allParsedAlertStream);

        // 开始将抽取后的图数据库数据sink入nebula
        graphAlertSink2Nebula(alertInfoRowSideOutStream, config);
        logger.info("Aggregation node alert info into nebule task start! now ->{} ", DateUtil.now());
        env.execute("ETL-ALERT-KAFKA-TO-NEBULA-TASK");
    }

    private static void graphAlertSink2Nebula(SingleOutputStreamOperator<Row> alertInfoRowSideOutStream, ParameterTool config) {

        // 处理之前初始化图数据库设定
        NebulaSinkOptionHandler.init(config);

        /*
        Tag部分
        */
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_IP)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_IP"))).setParallelism(PA1).name("IP实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_DOMAIN)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_DOMAIN"))).setParallelism(PA1).name("域名实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_CERT)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_CERT"))).setParallelism(PA1).name("证书实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_ATTACK)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_ATTACK"))).setParallelism(PA1).name("攻击行为节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_URL)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_URL"))).setParallelism(PA1).name("URL实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_ORG)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_ORG"))).setParallelism(PA1).name("机构实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_UA)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_UA"))).setParallelism(PA1).name("UA实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_APT_GROUP)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_APT_GROUP"))).setParallelism(PA1).name("APT组织实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_EMAIL)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_EMAIL"))).setParallelism(PA1).name("邮箱实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_MAIL)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_MAIL"))).setParallelism(PA1).name("邮件实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_FILE)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_FILE"))).setParallelism(PA1).name("文件实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_SSLFINGER)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_SSLFINGER"))).setParallelism(PA1).name("操作系统实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_APP)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_APP"))).setParallelism(PA1).name("APP实体节点");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_DEVICE)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleVertexFormat("TAG_DEVICE"))).setParallelism(PA1).name("设备实体节点");

        /*
          Edge部分
          */
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_make_attack)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_make_attack"))).setParallelism(PA1).name("make_attack 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_attack_to)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_attack_to"))).setParallelism(PA1).name("attack_to 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_ip_belong_to_apt)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_ip_belong_to_apt"))).setParallelism(PA1).name("ip_belong_to_apt 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_domain_belong_to_apt)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_domain_belong_to_apt"))).setParallelism(PA1).name("domain_belong_to_apt 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_ip_belong_to_org)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_ip_belong_to_org"))).setParallelism(PA1).name("ip_belong_to_org 边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_http_connect)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_http_connect"))).setParallelism(PA1).name("http_connect 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_ua)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_use_ua"))).setParallelism(PA1).name("client_use_ua 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_ua_connect_domain)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_ua_connect_domain"))).setParallelism(PA1).name("ua_connect_domain 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_http_connect_domain)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_http_connect_domain"))).setParallelism(PA1).name("client_http_connect_domain 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_server_http_connect_domain)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_server_http_connect_domain"))).setParallelism(PA1).name("server_http_connect_domain 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_http_connect_url)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_http_connect_url"))).setParallelism(PA1).name("client_http_connect_url 边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_query_domain)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_query_domain"))).setParallelism(PA1).name("client_query_domain 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_query_dns_server)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_query_dns_server"))).setParallelism(PA1).name("client_query_dns_server 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_dns_server_resolves_domain)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_dns_server_resolves_domain"))).setParallelism(PA1).name("dns_server_resolves_domain 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_parse_to)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_parse_to"))).setParallelism(PA1).name("parse_to边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_cert)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_use_cert"))).setParallelism(PA1).name("client_use_cert 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_server_use_cert)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_server_use_cert"))).setParallelism(PA1).name("server_use_cert 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_cert_belong_to_org)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_cert_belong_to_org"))).setParallelism(PA1).name("cert_belong_to_org 边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_cert_connect_sni)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_cert_connect_sni"))).setParallelism(PA1).name("cert_connect_sni 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_cert_connect_sslfinger)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_cert_connect_sslfinger"))).setParallelism(PA1).name("cert_connect_sslfinger 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_sslfinger)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_use_sslfinger"))).setParallelism(PA1).name("client_use_sslfinger 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_server_use_sslfinger)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_server_use_sslfinger"))).setParallelism(PA1).name("server_use_sslfinger 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_sslfinger_connect_sni)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_sslfinger_connect_sni"))).setParallelism(PA1).name("sslfinger_connect_sni 边");


        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_send_mail)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_send_mail"))).setParallelism(PA1).name("send_mail 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_receive_mail)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_receive_mail"))).setParallelism(PA1).name("receive_mail 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_include_file)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_include_file"))).setParallelism(PA1).name("include_file 边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_send_file)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_send_file"))).setParallelism(PA1).name("send_file 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_receive_file)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_receive_file"))).setParallelism(PA1).name("receive_file 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_file_connect_ip)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_file_connect_ip"))).setParallelism(PA1).name("file_connect_ip 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_file_connect_domain)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_file_connect_domain"))).setParallelism(PA1).name("file_connect_domain 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_file_connect_url)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_file_connect_url"))).setParallelism(PA1).name("file_connect_url 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_sender_send_file)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_sender_send_file"))).setParallelism(PA1).name("sender_send_file 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_receiver_receive_file)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_receiver_receive_file"))).setParallelism(PA1).name("receiver_receive_file 边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_attack_belong_to_apt_group)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_attack_belong_to_apt_group"))).setParallelism(PA1).name("attack_belong_to_apt_group 边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_app_connect_cert)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_app_connect_cert"))).setParallelism(PA1).name("app_connect_cert 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_app)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_client_use_app"))).setParallelism(PA1).name("client_use_app 边");
        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_app_belong_to_server)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_app_belong_to_server"))).setParallelism(PA1).name("app_belong_to_server 边");

        alertInfoRowSideOutStream.getSideOutput(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_attack_to_device)
                .addSink(new NebulaSinkFunction<Row>(NebulaSinkOptionHandler.handleEdgeFormat("EDGE_attack_to_device"))).setParallelism(PA1).name("attack_to_device 边");
    }
}