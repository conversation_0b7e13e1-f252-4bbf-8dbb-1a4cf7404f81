package com.geeksec.entity.pojo;

import cn.hutool.core.util.StrUtil;
import com.geeksec.config.util.MD5;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.entity.trans.X509CertTrans;
import lombok.Data;
import org.apache.flink.types.Row;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.geeksec.transfer.function.process.protocol.AlertX509ProcessFunction.COUNTRY_COMPANY_SUFFIX_MAP;


/**
 * <AUTHOR>
 */
@Data
public class X509AlertTrans {
    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private List<X509CertTrans> x509Cert;

    public List<Row> getAllRows() {
        List<Row> rows = new ArrayList<>();

        for(X509CertTrans x509CertTrans:x509Cert){
            String fingerPrint = x509CertTrans.getFingerPrint();
            if (StrUtil.isNotEmpty(fingerPrint)) {
                rows.addAll(getCertRelatedRows(x509CertTrans));
            }
        }
        return rows;
    }

    private List<Row> getCertRelatedRows(X509CertTrans x509Cert) {
        List<Row> certRelatedRows = new ArrayList<>();
        String fingerPrint = x509Cert.getFingerPrint();
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedDateTimeAfter = sdf.format(new Date(x509Cert.getNotAfter()));
        String formattedDateTimeBefore = sdf.format(new Date(x509Cert.getNotBefore()));

        Row certRow = new Row(9);
        certRow.setField(0, "TAG_CERT");
        certRow.setField(1, fingerPrint);
        certRow.setField(2, x509Cert.getIssuer());
        certRow.setField(3, x509Cert.getSubject());
        certRow.setField(4, x509Cert.getAlgorithmId());
        certRow.setField(5, x509Cert.getFpAlg());
        certRow.setField(6, formattedDateTimeBefore);
        certRow.setField(7, formattedDateTimeAfter);
        certRow.setField(8, x509Cert.getVersion());
        certRelatedRows.add(certRow);

        // 暂不需要判断org的公司属性
        String subjectO = x509Cert.getSubjectOn();
        String subjectOu = x509Cert.getSubjectOu();
        String issuerO = x509Cert.getIssuerOn();
        String issuerOu = x509Cert.getIssuerOu();
        Set<String> companyLegalCountrySubO = getCompanyLegalCountry(subjectO);
        Set<String> companyLegalCountrySubOu = getCompanyLegalCountry(subjectOu);
        Set<String> companyLegalCountryIsuO = getCompanyLegalCountry(issuerO);
        Set<String> companyLegalCountryIsuOu = getCompanyLegalCountry(issuerOu);

        Row issuerOrgRow = new Row(3);
        issuerOrgRow.setField(0, "TAG_ORG");
        issuerOrgRow.setField(1, MD5.getMd5(issuerO));
        issuerOrgRow.setField(2, issuerO);
        certRelatedRows.add(issuerOrgRow);

        Row subjectOrgRow = new Row(3);
        subjectOrgRow.setField(0, "TAG_ORG");
        subjectOrgRow.setField(1, MD5.getMd5(subjectO));
        subjectOrgRow.setField(2, subjectO);
        certRelatedRows.add(subjectOrgRow);

        // client_use_cert 客户端使用证书
        Row cucRow = new Row(4);
        cucRow.setField(0, "EDGE_client_use_cert");
        cucRow.setField(1, sip.getIPAddr());
        cucRow.setField(2, fingerPrint);
        cucRow.setField(3, 0);
        certRelatedRows.add(cucRow);

        // server_use_cert 服务端部署证书
        Row sucRow = new Row(4);
        sucRow.setField(0, "EDGE_server_use_cert");
        sucRow.setField(1, dip.getIPAddr());
        sucRow.setField(2, fingerPrint);
        sucRow.setField(3, 0);
        certRelatedRows.add(sucRow);

        // cert_belong_to_org 证书关联ORG
        Row cbtoRow = new Row(4);
        sucRow.setField(0, "EDGE_cert_belong_to_org");
        sucRow.setField(1, fingerPrint);
        sucRow.setField(2, MD5.getMd5(subjectO));
        sucRow.setField(3, 0);
        certRelatedRows.add(cbtoRow);

        return certRelatedRows;
    }

    private Set<String> getCompanyLegalCountry(String name) {

        Set<String> legalCountries = new HashSet<>();

        for (String country:COUNTRY_COMPANY_SUFFIX_MAP.keySet()){
            List<String> suffixList = COUNTRY_COMPANY_SUFFIX_MAP.get(country);
            for (String suffix:suffixList){
                if(name.contains(suffix)){
                    legalCountries.add(country);
                    break;
                }
            }
        }
        return legalCountries;
    }
}
