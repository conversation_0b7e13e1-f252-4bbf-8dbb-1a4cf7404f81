package com.geeksec.transfer.handle;

import com.geeksec.config.util.IpNetUtils;
import com.geeksec.config.util.MD5;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.base.IpInfo;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;

public class IPTransHandler {

    /**
     * IP实体转义器方法，所有的IP实体通过这个方法进行获取
     *
     * @param ipInfo 输入IP数据
     * @return ipTrans IP数据转化
     */
    public static IPTrans transIp(IpInfo.IP_INFO ipInfo) {
        IPTrans ipTrans = new IPTrans();
        ipTrans.setIPAddr(ipInfo.getIp());
        ipTrans.setCity(ipInfo.getIpCity());
        ipTrans.setCountry(ipInfo.getIpCountry());
        ipTrans.setOrg(ipInfo.getIpOrg());
        ipTrans.setLatitude(ipInfo.getIpLatitude());
        ipTrans.setLongitude(ipInfo.getIpLongitude());
        ipTrans.setISP(ipInfo.getIpIsp());
        ipTrans.setAS(ipInfo.getIpAsn());
        ipTrans.setSTAT(ipInfo.getIpStat());
        ipTrans.setPort(ipInfo.getPort());
        return ipTrans;
    }

    /**
     * 获取所有的IP相关联的Row信息
     *
     * @param sIp 源IP
     * @param dIp 目的IP
     * @return 生成点边
     */
    public static List<Row> getAllIpRows(IPTrans sIp, IPTrans dIp) {
        List<Row> rows = new ArrayList<>();

        updateIpRow(sIp, rows);

        updateIpRow(dIp, rows);

        Row sIpOrgRow = new Row(3);
        String sIpOrg = sIp.getOrg();
        sIpOrgRow.setField(0, "TAG_ORG");
        sIpOrgRow.setField(1, MD5.getMd5(sIpOrg));
        sIpOrgRow.setField(2, sIpOrg);
        rows.add(sIpOrgRow);

        Row sIpBelongToRow = new Row(4);
        sIpBelongToRow.setField(0, "EDGE_ip_belong_to_org");
        sIpBelongToRow.setField(1, sIp.getIPAddr());
        sIpBelongToRow.setField(2, MD5.getMd5(sIp.getOrg()));
        sIpBelongToRow.setField(3, 0);
        rows.add(sIpBelongToRow);

        Row dIpOrgRow = new Row(3);
        String dIpOrg = dIp.getOrg();
        dIpOrgRow.setField(0, "TAG_ORG");
        dIpOrgRow.setField(1, MD5.getMd5(dIpOrg));
        dIpOrgRow.setField(2, dIpOrg);
        rows.add(dIpOrgRow);

        Row dIpBelongToRow = new Row(4);
        dIpBelongToRow.setField(0, "EDGE_ip_belong_to_org");
        dIpBelongToRow.setField(1, dIp.getIPAddr());
        dIpBelongToRow.setField(2, MD5.getMd5(dIp.getOrg()));
        dIpBelongToRow.setField(3, 0);
        rows.add(dIpBelongToRow);

        return rows;
    }

    private static void updateIpRow(IPTrans sIp, List<Row> rows) {
        Row sIpRow = new Row(11);
        sIpRow.setField(0, "TAG_IP");
        sIpRow.setField(1, sIp.getIPAddr());
        if (Boolean.TRUE.equals(IpNetUtils.isValidIPV4(sIp.getIPAddr()))) {
            sIpRow.setField(2, "V4");
        } else {
            sIpRow.setField(2, "V6");
        }
        sIpRow.setField(3, sIp.getCity());
        sIpRow.setField(4, sIp.getCountry());
        sIpRow.setField(5, sIp.getSTAT());
        sIpRow.setField(6, sIp.getLongitude());
        sIpRow.setField(7, sIp.getLatitude());
        sIpRow.setField(8, sIp.getISP());
        sIpRow.setField(9, sIp.getAS());
        sIpRow.setField(10, sIp.getOrg());
        rows.add(sIpRow);
    }
}