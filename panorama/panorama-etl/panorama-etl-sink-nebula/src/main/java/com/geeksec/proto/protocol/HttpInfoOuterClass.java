package com.geeksec.proto.protocol;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: HttpInfo.proto
// Protobuf Java Version: 4.29.4

public final class HttpInfoOuterClass {
  private HttpInfoOuterClass() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      HttpInfoOuterClass.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface HttpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:HttpInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes host = 1;</code>
     * @return Whether the host field is set.
     */
    boolean hasHost();
    /**
     * <code>optional bytes host = 1;</code>
     * @return The host.
     */
    com.google.protobuf.ByteString getHost();

    /**
     * <code>optional bytes uri = 2;</code>
     * @return Whether the uri field is set.
     */
    boolean hasUri();
    /**
     * <code>optional bytes uri = 2;</code>
     * @return The uri.
     */
    com.google.protobuf.ByteString getUri();

    /**
     * <code>optional bytes varConEnc = 3;</code>
     * @return Whether the varConEnc field is set.
     */
    boolean hasVarConEnc();
    /**
     * <code>optional bytes varConEnc = 3;</code>
     * @return The varConEnc.
     */
    com.google.protobuf.ByteString getVarConEnc();

    /**
     * <code>optional bytes authInfo = 4;</code>
     * @return Whether the authInfo field is set.
     */
    boolean hasAuthInfo();
    /**
     * <code>optional bytes authInfo = 4;</code>
     * @return The authInfo.
     */
    com.google.protobuf.ByteString getAuthInfo();

    /**
     * <code>optional bytes conEncByCli = 5;</code>
     * @return Whether the conEncByCli field is set.
     */
    boolean hasConEncByCli();
    /**
     * <code>optional bytes conEncByCli = 5;</code>
     * @return The conEncByCli.
     */
    com.google.protobuf.ByteString getConEncByCli();

    /**
     * <code>optional bytes conLan = 6;</code>
     * @return Whether the conLan field is set.
     */
    boolean hasConLan();
    /**
     * <code>optional bytes conLan = 6;</code>
     * @return The conLan.
     */
    com.google.protobuf.ByteString getConLan();

    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     * @return Whether the conLenByCli field is set.
     */
    boolean hasConLenByCli();
    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     * @return The conLenByCli.
     */
    int getConLenByCli();

    /**
     * <code>optional bytes conURL = 8;</code>
     * @return Whether the conURL field is set.
     */
    boolean hasConURL();
    /**
     * <code>optional bytes conURL = 8;</code>
     * @return The conURL.
     */
    com.google.protobuf.ByteString getConURL();

    /**
     * <code>optional bytes conMD5 = 9;</code>
     * @return Whether the conMD5 field is set.
     */
    boolean hasConMD5();
    /**
     * <code>optional bytes conMD5 = 9;</code>
     * @return The conMD5.
     */
    com.google.protobuf.ByteString getConMD5();

    /**
     * <code>optional bytes conType = 10;</code>
     * @return Whether the conType field is set.
     */
    boolean hasConType();
    /**
     * <code>optional bytes conType = 10;</code>
     * @return The conType.
     */
    com.google.protobuf.ByteString getConType();

    /**
     * <code>optional bytes cookie = 11;</code>
     * @return Whether the cookie field is set.
     */
    boolean hasCookie();
    /**
     * <code>optional bytes cookie = 11;</code>
     * @return The cookie.
     */
    com.google.protobuf.ByteString getCookie();

    /**
     * <code>optional bytes cookie2 = 12;</code>
     * @return Whether the cookie2 field is set.
     */
    boolean hasCookie2();
    /**
     * <code>optional bytes cookie2 = 12;</code>
     * @return The cookie2.
     */
    com.google.protobuf.ByteString getCookie2();

    /**
     * <code>optional bytes date = 13;</code>
     * @return Whether the date field is set.
     */
    boolean hasDate();
    /**
     * <code>optional bytes date = 13;</code>
     * @return The date.
     */
    com.google.protobuf.ByteString getDate();

    /**
     * <code>optional bytes from = 14;</code>
     * @return Whether the from field is set.
     */
    boolean hasFrom();
    /**
     * <code>optional bytes from = 14;</code>
     * @return The from.
     */
    com.google.protobuf.ByteString getFrom();

    /**
     * <code>optional bytes loc = 15;</code>
     * @return Whether the loc field is set.
     */
    boolean hasLoc();
    /**
     * <code>optional bytes loc = 15;</code>
     * @return The loc.
     */
    com.google.protobuf.ByteString getLoc();

    /**
     * <code>optional bytes proAuthen = 16;</code>
     * @return Whether the proAuthen field is set.
     */
    boolean hasProAuthen();
    /**
     * <code>optional bytes proAuthen = 16;</code>
     * @return The proAuthen.
     */
    com.google.protobuf.ByteString getProAuthen();

    /**
     * <code>optional bytes proAuthor = 17;</code>
     * @return Whether the proAuthor field is set.
     */
    boolean hasProAuthor();
    /**
     * <code>optional bytes proAuthor = 17;</code>
     * @return The proAuthor.
     */
    com.google.protobuf.ByteString getProAuthor();

    /**
     * <code>optional bytes refURL = 18;</code>
     * @return Whether the refURL field is set.
     */
    boolean hasRefURL();
    /**
     * <code>optional bytes refURL = 18;</code>
     * @return The refURL.
     */
    com.google.protobuf.ByteString getRefURL();

    /**
     * <code>optional bytes srv = 19;</code>
     * @return Whether the srv field is set.
     */
    boolean hasSrv();
    /**
     * <code>optional bytes srv = 19;</code>
     * @return The srv.
     */
    com.google.protobuf.ByteString getSrv();

    /**
     * <code>optional uint32 srvCnt = 20;</code>
     * @return Whether the srvCnt field is set.
     */
    boolean hasSrvCnt();
    /**
     * <code>optional uint32 srvCnt = 20;</code>
     * @return The srvCnt.
     */
    int getSrvCnt();

    /**
     * <code>optional bytes setCookieKey = 21;</code>
     * @return Whether the setCookieKey field is set.
     */
    boolean hasSetCookieKey();
    /**
     * <code>optional bytes setCookieKey = 21;</code>
     * @return The setCookieKey.
     */
    com.google.protobuf.ByteString getSetCookieKey();

    /**
     * <code>optional bytes setCookieVal = 22;</code>
     * @return Whether the setCookieVal field is set.
     */
    boolean hasSetCookieVal();
    /**
     * <code>optional bytes setCookieVal = 22;</code>
     * @return The setCookieVal.
     */
    com.google.protobuf.ByteString getSetCookieVal();

    /**
     * <code>optional bytes traEnc = 23;</code>
     * @return Whether the traEnc field is set.
     */
    boolean hasTraEnc();
    /**
     * <code>optional bytes traEnc = 23;</code>
     * @return The traEnc.
     */
    com.google.protobuf.ByteString getTraEnc();

    /**
     * <code>optional bytes usrAge = 24;</code>
     * @return Whether the usrAge field is set.
     */
    boolean hasUsrAge();
    /**
     * <code>optional bytes usrAge = 24;</code>
     * @return The usrAge.
     */
    com.google.protobuf.ByteString getUsrAge();

    /**
     * <code>optional bytes via = 25;</code>
     * @return Whether the via field is set.
     */
    boolean hasVia();
    /**
     * <code>optional bytes via = 25;</code>
     * @return The via.
     */
    com.google.protobuf.ByteString getVia();

    /**
     * <code>optional bytes xForFor = 26;</code>
     * @return Whether the xForFor field is set.
     */
    boolean hasXForFor();
    /**
     * <code>optional bytes xForFor = 26;</code>
     * @return The xForFor.
     */
    com.google.protobuf.ByteString getXForFor();

    /**
     * <code>optional uint32 statCode = 27;</code>
     * @return Whether the statCode field is set.
     */
    boolean hasStatCode();
    /**
     * <code>optional uint32 statCode = 27;</code>
     * @return The statCode.
     */
    int getStatCode();

    /**
     * <code>optional bytes met = 28;</code>
     * @return Whether the met field is set.
     */
    boolean hasMet();
    /**
     * <code>optional bytes met = 28;</code>
     * @return The met.
     */
    com.google.protobuf.ByteString getMet();

    /**
     * <code>optional bytes srvAge = 29;</code>
     * @return Whether the srvAge field is set.
     */
    boolean hasSrvAge();
    /**
     * <code>optional bytes srvAge = 29;</code>
     * @return The srvAge.
     */
    com.google.protobuf.ByteString getSrvAge();

    /**
     * <code>optional bytes proAuth = 30;</code>
     * @return Whether the proAuth field is set.
     */
    boolean hasProAuth();
    /**
     * <code>optional bytes proAuth = 30;</code>
     * @return The proAuth.
     */
    com.google.protobuf.ByteString getProAuth();

    /**
     * <code>optional bytes xPowBy = 31;</code>
     * @return Whether the xPowBy field is set.
     */
    boolean hasXPowBy();
    /**
     * <code>optional bytes xPowBy = 31;</code>
     * @return The xPowBy.
     */
    com.google.protobuf.ByteString getXPowBy();

    /**
     * <code>optional bytes extHdrs = 32;</code>
     * @return Whether the extHdrs field is set.
     */
    boolean hasExtHdrs();
    /**
     * <code>optional bytes extHdrs = 32;</code>
     * @return The extHdrs.
     */
    com.google.protobuf.ByteString getExtHdrs();

    /**
     * <code>optional bytes rangeofCli = 33;</code>
     * @return Whether the rangeofCli field is set.
     */
    boolean hasRangeofCli();
    /**
     * <code>optional bytes rangeofCli = 33;</code>
     * @return The rangeofCli.
     */
    com.google.protobuf.ByteString getRangeofCli();

    /**
     * <code>optional uint32 viaCnt = 34;</code>
     * @return Whether the viaCnt field is set.
     */
    boolean hasViaCnt();
    /**
     * <code>optional uint32 viaCnt = 34;</code>
     * @return The viaCnt.
     */
    int getViaCnt();

    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     * @return Whether the statCodeCnt field is set.
     */
    boolean hasStatCodeCnt();
    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     * @return The statCodeCnt.
     */
    int getStatCodeCnt();

    /**
     * <code>optional bytes reqVer = 36;</code>
     * @return Whether the reqVer field is set.
     */
    boolean hasReqVer();
    /**
     * <code>optional bytes reqVer = 36;</code>
     * @return The reqVer.
     */
    com.google.protobuf.ByteString getReqVer();

    /**
     * <code>optional bytes reqHead = 37;</code>
     * @return Whether the reqHead field is set.
     */
    boolean hasReqHead();
    /**
     * <code>optional bytes reqHead = 37;</code>
     * @return The reqHead.
     */
    com.google.protobuf.ByteString getReqHead();

    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     * @return Whether the reqHeadMd5 field is set.
     */
    boolean hasReqHeadMd5();
    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     * @return The reqHeadMd5.
     */
    int getReqHeadMd5();

    /**
     * <code>optional bytes cacConUp = 39;</code>
     * @return Whether the cacConUp field is set.
     */
    boolean hasCacConUp();
    /**
     * <code>optional bytes cacConUp = 39;</code>
     * @return The cacConUp.
     */
    com.google.protobuf.ByteString getCacConUp();

    /**
     * <code>optional bytes conUp = 40;</code>
     * @return Whether the conUp field is set.
     */
    boolean hasConUp();
    /**
     * <code>optional bytes conUp = 40;</code>
     * @return The conUp.
     */
    com.google.protobuf.ByteString getConUp();

    /**
     * <code>optional bytes praUp = 41;</code>
     * @return Whether the praUp field is set.
     */
    boolean hasPraUp();
    /**
     * <code>optional bytes praUp = 41;</code>
     * @return The praUp.
     */
    com.google.protobuf.ByteString getPraUp();

    /**
     * <code>optional bytes upg = 42;</code>
     * @return Whether the upg field is set.
     */
    boolean hasUpg();
    /**
     * <code>optional bytes upg = 42;</code>
     * @return The upg.
     */
    com.google.protobuf.ByteString getUpg();

    /**
     * <code>optional bytes accChaUp = 43;</code>
     * @return Whether the accChaUp field is set.
     */
    boolean hasAccChaUp();
    /**
     * <code>optional bytes accChaUp = 43;</code>
     * @return The accChaUp.
     */
    com.google.protobuf.ByteString getAccChaUp();

    /**
     * <code>optional bytes acctRanUp = 44;</code>
     * @return Whether the acctRanUp field is set.
     */
    boolean hasAcctRanUp();
    /**
     * <code>optional bytes acctRanUp = 44;</code>
     * @return The acctRanUp.
     */
    com.google.protobuf.ByteString getAcctRanUp();

    /**
     * <code>optional bytes ifMat = 45;</code>
     * @return Whether the ifMat field is set.
     */
    boolean hasIfMat();
    /**
     * <code>optional bytes ifMat = 45;</code>
     * @return The ifMat.
     */
    com.google.protobuf.ByteString getIfMat();

    /**
     * <code>optional bytes ifModSin = 46;</code>
     * @return Whether the ifModSin field is set.
     */
    boolean hasIfModSin();
    /**
     * <code>optional bytes ifModSin = 46;</code>
     * @return The ifModSin.
     */
    com.google.protobuf.ByteString getIfModSin();

    /**
     * <code>optional bytes ifNonMat = 47;</code>
     * @return Whether the ifNonMat field is set.
     */
    boolean hasIfNonMat();
    /**
     * <code>optional bytes ifNonMat = 47;</code>
     * @return The ifNonMat.
     */
    com.google.protobuf.ByteString getIfNonMat();

    /**
     * <code>optional bytes ifRan = 48;</code>
     * @return Whether the ifRan field is set.
     */
    boolean hasIfRan();
    /**
     * <code>optional bytes ifRan = 48;</code>
     * @return The ifRan.
     */
    com.google.protobuf.ByteString getIfRan();

    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     * @return Whether the ifUnModSin field is set.
     */
    boolean hasIfUnModSin();
    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     * @return The ifUnModSin.
     */
    long getIfUnModSin();

    /**
     * <code>optional uint32 maxFor = 50;</code>
     * @return Whether the maxFor field is set.
     */
    boolean hasMaxFor();
    /**
     * <code>optional uint32 maxFor = 50;</code>
     * @return The maxFor.
     */
    int getMaxFor();

    /**
     * <code>optional bytes te = 51;</code>
     * @return Whether the te field is set.
     */
    boolean hasTe();
    /**
     * <code>optional bytes te = 51;</code>
     * @return The te.
     */
    com.google.protobuf.ByteString getTe();

    /**
     * <code>optional bytes cacConDown = 52;</code>
     * @return Whether the cacConDown field is set.
     */
    boolean hasCacConDown();
    /**
     * <code>optional bytes cacConDown = 52;</code>
     * @return The cacConDown.
     */
    com.google.protobuf.ByteString getCacConDown();

    /**
     * <code>optional bytes conDown = 53;</code>
     * @return Whether the conDown field is set.
     */
    boolean hasConDown();
    /**
     * <code>optional bytes conDown = 53;</code>
     * @return The conDown.
     */
    com.google.protobuf.ByteString getConDown();

    /**
     * <code>optional bytes praDown = 54;</code>
     * @return Whether the praDown field is set.
     */
    boolean hasPraDown();
    /**
     * <code>optional bytes praDown = 54;</code>
     * @return The praDown.
     */
    com.google.protobuf.ByteString getPraDown();

    /**
     * <code>optional bytes trail = 55;</code>
     * @return Whether the trail field is set.
     */
    boolean hasTrail();
    /**
     * <code>optional bytes trail = 55;</code>
     * @return The trail.
     */
    com.google.protobuf.ByteString getTrail();

    /**
     * <code>optional bytes accRanDown = 56;</code>
     * @return Whether the accRanDown field is set.
     */
    boolean hasAccRanDown();
    /**
     * <code>optional bytes accRanDown = 56;</code>
     * @return The accRanDown.
     */
    com.google.protobuf.ByteString getAccRanDown();

    /**
     * <code>optional bytes eTag = 57;</code>
     * @return Whether the eTag field is set.
     */
    boolean hasETag();
    /**
     * <code>optional bytes eTag = 57;</code>
     * @return The eTag.
     */
    com.google.protobuf.ByteString getETag();

    /**
     * <code>optional bytes retAft = 58;</code>
     * @return Whether the retAft field is set.
     */
    boolean hasRetAft();
    /**
     * <code>optional bytes retAft = 58;</code>
     * @return The retAft.
     */
    com.google.protobuf.ByteString getRetAft();

    /**
     * <code>optional bytes wwwAuth = 59;</code>
     * @return Whether the wwwAuth field is set.
     */
    boolean hasWwwAuth();
    /**
     * <code>optional bytes wwwAuth = 59;</code>
     * @return The wwwAuth.
     */
    com.google.protobuf.ByteString getWwwAuth();

    /**
     * <code>optional bytes refresh = 60;</code>
     * @return Whether the refresh field is set.
     */
    boolean hasRefresh();
    /**
     * <code>optional bytes refresh = 60;</code>
     * @return The refresh.
     */
    com.google.protobuf.ByteString getRefresh();

    /**
     * <code>optional bytes conTypDown = 61;</code>
     * @return Whether the conTypDown field is set.
     */
    boolean hasConTypDown();
    /**
     * <code>optional bytes conTypDown = 61;</code>
     * @return The conTypDown.
     */
    com.google.protobuf.ByteString getConTypDown();

    /**
     * <code>optional bytes allow = 62;</code>
     * @return Whether the allow field is set.
     */
    boolean hasAllow();
    /**
     * <code>optional bytes allow = 62;</code>
     * @return The allow.
     */
    com.google.protobuf.ByteString getAllow();

    /**
     * <code>optional uint64 expires = 63;</code>
     * @return Whether the expires field is set.
     */
    boolean hasExpires();
    /**
     * <code>optional uint64 expires = 63;</code>
     * @return The expires.
     */
    long getExpires();

    /**
     * <code>optional uint64 lasMod = 64;</code>
     * @return Whether the lasMod field is set.
     */
    boolean hasLasMod();
    /**
     * <code>optional uint64 lasMod = 64;</code>
     * @return The lasMod.
     */
    long getLasMod();

    /**
     * <code>optional bytes accChaDown = 65;</code>
     * @return Whether the accChaDown field is set.
     */
    boolean hasAccChaDown();
    /**
     * <code>optional bytes accChaDown = 65;</code>
     * @return The accChaDown.
     */
    com.google.protobuf.ByteString getAccChaDown();

    /**
     * <code>optional bytes httpRelKey = 66;</code>
     * @return Whether the httpRelKey field is set.
     */
    boolean hasHttpRelKey();
    /**
     * <code>optional bytes httpRelKey = 66;</code>
     * @return The httpRelKey.
     */
    com.google.protobuf.ByteString getHttpRelKey();

    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     * @return Whether the httpEmbPro field is set.
     */
    boolean hasHttpEmbPro();
    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     * @return The httpEmbPro.
     */
    com.google.protobuf.ByteString getHttpEmbPro();

    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     * @return Whether the fullTextHeader field is set.
     */
    boolean hasFullTextHeader();
    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     * @return The fullTextHeader.
     */
    com.google.protobuf.ByteString getFullTextHeader();

    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     * @return Whether the fullTextLen field is set.
     */
    boolean hasFullTextLen();
    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     * @return The fullTextLen.
     */
    int getFullTextLen();

    /**
     * <code>optional bytes fileName = 70;</code>
     * @return Whether the fileName field is set.
     */
    boolean hasFileName();
    /**
     * <code>optional bytes fileName = 70;</code>
     * @return The fileName.
     */
    com.google.protobuf.ByteString getFileName();

    /**
     * <code>optional bytes contDown = 71;</code>
     * @return Whether the contDown field is set.
     */
    boolean hasContDown();
    /**
     * <code>optional bytes contDown = 71;</code>
     * @return The contDown.
     */
    com.google.protobuf.ByteString getContDown();

    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     * @return Whether the reqVerCnt field is set.
     */
    boolean hasReqVerCnt();
    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     * @return The reqVerCnt.
     */
    int getReqVerCnt();

    /**
     * <code>optional uint32 metCnt = 73;</code>
     * @return Whether the metCnt field is set.
     */
    boolean hasMetCnt();
    /**
     * <code>optional uint32 metCnt = 73;</code>
     * @return The metCnt.
     */
    int getMetCnt();

    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     * @return Whether the reqHeadCnt field is set.
     */
    boolean hasReqHeadCnt();
    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     * @return The reqHeadCnt.
     */
    int getReqHeadCnt();

    /**
     * <code>optional bytes accByCli = 75;</code>
     * @return Whether the accByCli field is set.
     */
    boolean hasAccByCli();
    /**
     * <code>optional bytes accByCli = 75;</code>
     * @return The accByCli.
     */
    com.google.protobuf.ByteString getAccByCli();

    /**
     * <code>optional bytes accLanByCli = 76;</code>
     * @return Whether the accLanByCli field is set.
     */
    boolean hasAccLanByCli();
    /**
     * <code>optional bytes accLanByCli = 76;</code>
     * @return The accLanByCli.
     */
    com.google.protobuf.ByteString getAccLanByCli();

    /**
     * <code>optional bytes accEncByCli = 77;</code>
     * @return Whether the accEncByCli field is set.
     */
    boolean hasAccEncByCli();
    /**
     * <code>optional bytes accEncByCli = 77;</code>
     * @return The accEncByCli.
     */
    com.google.protobuf.ByteString getAccEncByCli();

    /**
     * <code>optional uint32 authCnt = 78;</code>
     * @return Whether the authCnt field is set.
     */
    boolean hasAuthCnt();
    /**
     * <code>optional uint32 authCnt = 78;</code>
     * @return The authCnt.
     */
    int getAuthCnt();

    /**
     * <code>optional uint32 hostCnt = 79;</code>
     * @return Whether the hostCnt field is set.
     */
    boolean hasHostCnt();
    /**
     * <code>optional uint32 hostCnt = 79;</code>
     * @return The hostCnt.
     */
    int getHostCnt();

    /**
     * <code>optional uint32 uriCnt = 80;</code>
     * @return Whether the uriCnt field is set.
     */
    boolean hasUriCnt();
    /**
     * <code>optional uint32 uriCnt = 80;</code>
     * @return The uriCnt.
     */
    int getUriCnt();

    /**
     * <code>optional bytes uriPath = 81;</code>
     * @return Whether the uriPath field is set.
     */
    boolean hasUriPath();
    /**
     * <code>optional bytes uriPath = 81;</code>
     * @return The uriPath.
     */
    com.google.protobuf.ByteString getUriPath();

    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     * @return Whether the uriPathCnt field is set.
     */
    boolean hasUriPathCnt();
    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     * @return The uriPathCnt.
     */
    int getUriPathCnt();

    /**
     * <code>repeated bytes uriKey = 83;</code>
     * @return A list containing the uriKey.
     */
    java.util.List<com.google.protobuf.ByteString> getUriKeyList();
    /**
     * <code>repeated bytes uriKey = 83;</code>
     * @return The count of uriKey.
     */
    int getUriKeyCount();
    /**
     * <code>repeated bytes uriKey = 83;</code>
     * @param index The index of the element to return.
     * @return The uriKey at the given index.
     */
    com.google.protobuf.ByteString getUriKey(int index);

    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     * @return Whether the uriKeyCnt field is set.
     */
    boolean hasUriKeyCnt();
    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     * @return The uriKeyCnt.
     */
    int getUriKeyCnt();

    /**
     * <code>optional bytes uriSearch = 85;</code>
     * @return Whether the uriSearch field is set.
     */
    boolean hasUriSearch();
    /**
     * <code>optional bytes uriSearch = 85;</code>
     * @return The uriSearch.
     */
    com.google.protobuf.ByteString getUriSearch();

    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     * @return Whether the usrAgeCnt field is set.
     */
    boolean hasUsrAgeCnt();
    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     * @return The usrAgeCnt.
     */
    int getUsrAgeCnt();

    /**
     * <code>optional bytes user = 87;</code>
     * @return Whether the user field is set.
     */
    boolean hasUser();
    /**
     * <code>optional bytes user = 87;</code>
     * @return The user.
     */
    com.google.protobuf.ByteString getUser();

    /**
     * <code>optional uint32 userCnt = 88;</code>
     * @return Whether the userCnt field is set.
     */
    boolean hasUserCnt();
    /**
     * <code>optional uint32 userCnt = 88;</code>
     * @return The userCnt.
     */
    int getUserCnt();

    /**
     * <code>optional bytes reqBody = 89;</code>
     * @return Whether the reqBody field is set.
     */
    boolean hasReqBody();
    /**
     * <code>optional bytes reqBody = 89;</code>
     * @return The reqBody.
     */
    com.google.protobuf.ByteString getReqBody();

    /**
     * <code>optional bytes reqBodyN = 90;</code>
     * @return Whether the reqBodyN field is set.
     */
    boolean hasReqBodyN();
    /**
     * <code>optional bytes reqBodyN = 90;</code>
     * @return The reqBodyN.
     */
    com.google.protobuf.ByteString getReqBodyN();

    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     * @return Whether the conMD5ByCli field is set.
     */
    boolean hasConMD5ByCli();
    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     * @return The conMD5ByCli.
     */
    com.google.protobuf.ByteString getConMD5ByCli();

    /**
     * <code>repeated bytes cookieKey = 92;</code>
     * @return A list containing the cookieKey.
     */
    java.util.List<com.google.protobuf.ByteString> getCookieKeyList();
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     * @return The count of cookieKey.
     */
    int getCookieKeyCount();
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     * @param index The index of the element to return.
     * @return The cookieKey at the given index.
     */
    com.google.protobuf.ByteString getCookieKey(int index);

    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     * @return Whether the cookieKeyCnt field is set.
     */
    boolean hasCookieKeyCnt();
    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     * @return The cookieKeyCnt.
     */
    int getCookieKeyCnt();

    /**
     * <code>optional bytes imei = 94;</code>
     * @return Whether the imei field is set.
     */
    boolean hasImei();
    /**
     * <code>optional bytes imei = 94;</code>
     * @return The imei.
     */
    com.google.protobuf.ByteString getImei();

    /**
     * <code>optional bytes imsi = 95;</code>
     * @return Whether the imsi field is set.
     */
    boolean hasImsi();
    /**
     * <code>optional bytes imsi = 95;</code>
     * @return The imsi.
     */
    com.google.protobuf.ByteString getImsi();

    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     * @return Whether the xForForCnt field is set.
     */
    boolean hasXForForCnt();
    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     * @return The xForForCnt.
     */
    int getXForForCnt();

    /**
     * <code>optional bytes respVer = 97;</code>
     * @return Whether the respVer field is set.
     */
    boolean hasRespVer();
    /**
     * <code>optional bytes respVer = 97;</code>
     * @return The respVer.
     */
    com.google.protobuf.ByteString getRespVer();

    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     * @return Whether the respVerCnt field is set.
     */
    boolean hasRespVerCnt();
    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     * @return The respVerCnt.
     */
    int getRespVerCnt();

    /**
     * <code>optional bytes respHead = 99;</code>
     * @return Whether the respHead field is set.
     */
    boolean hasRespHead();
    /**
     * <code>optional bytes respHead = 99;</code>
     * @return The respHead.
     */
    com.google.protobuf.ByteString getRespHead();

    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     * @return Whether the respHeadMd5 field is set.
     */
    boolean hasRespHeadMd5();
    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     * @return The respHeadMd5.
     */
    com.google.protobuf.ByteString getRespHeadMd5();

    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     * @return Whether the respHeadCnt field is set.
     */
    boolean hasRespHeadCnt();
    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     * @return The respHeadCnt.
     */
    int getRespHeadCnt();

    /**
     * <code>optional bytes respBody = 102;</code>
     * @return Whether the respBody field is set.
     */
    boolean hasRespBody();
    /**
     * <code>optional bytes respBody = 102;</code>
     * @return The respBody.
     */
    com.google.protobuf.ByteString getRespBody();

    /**
     * <code>optional bytes respBodyN = 103;</code>
     * @return Whether the respBodyN field is set.
     */
    boolean hasRespBodyN();
    /**
     * <code>optional bytes respBodyN = 103;</code>
     * @return The respBodyN.
     */
    com.google.protobuf.ByteString getRespBodyN();

    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     * @return Whether the conMD5BySrv field is set.
     */
    boolean hasConMD5BySrv();
    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     * @return The conMD5BySrv.
     */
    com.google.protobuf.ByteString getConMD5BySrv();

    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     * @return Whether the conEncBySrv field is set.
     */
    boolean hasConEncBySrv();
    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     * @return The conEncBySrv.
     */
    int getConEncBySrv();

    /**
     * <code>optional bytes Location = 106;</code>
     * @return Whether the location field is set.
     */
    boolean hasLocation();
    /**
     * <code>optional bytes Location = 106;</code>
     * @return The location.
     */
    com.google.protobuf.ByteString getLocation();

    /**
     * <code>optional bytes xSinHol = 107;</code>
     * @return Whether the xSinHol field is set.
     */
    boolean hasXSinHol();
    /**
     * <code>optional bytes xSinHol = 107;</code>
     * @return The xSinHol.
     */
    com.google.protobuf.ByteString getXSinHol();

    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     * @return Whether the conEncBySrvCnt field is set.
     */
    boolean hasConEncBySrvCnt();
    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     * @return The conEncBySrvCnt.
     */
    int getConEncBySrvCnt();

    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     * @return Whether the conLenSrv field is set.
     */
    boolean hasConLenSrv();
    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     * @return The conLenSrv.
     */
    int getConLenSrv();

    /**
     * <code>optional bytes conDispUp = 110;</code>
     * @return Whether the conDispUp field is set.
     */
    boolean hasConDispUp();
    /**
     * <code>optional bytes conDispUp = 110;</code>
     * @return The conDispUp.
     */
    com.google.protobuf.ByteString getConDispUp();

    /**
     * <code>optional bytes conDispDown = 111;</code>
     * @return Whether the conDispDown field is set.
     */
    boolean hasConDispDown();
    /**
     * <code>optional bytes conDispDown = 111;</code>
     * @return The conDispDown.
     */
    com.google.protobuf.ByteString getConDispDown();

    /**
     * <code>optional bytes authUser = 112;</code>
     * @return Whether the authUser field is set.
     */
    boolean hasAuthUser();
    /**
     * <code>optional bytes authUser = 112;</code>
     * @return The authUser.
     */
    com.google.protobuf.ByteString getAuthUser();

    /**
     * <code>optional uint32 authUserCount = 113;</code>
     * @return Whether the authUserCount field is set.
     */
    boolean hasAuthUserCount();
    /**
     * <code>optional uint32 authUserCount = 113;</code>
     * @return The authUserCount.
     */
    int getAuthUserCount();

    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     * @return Whether the bodyServerMd5Count field is set.
     */
    boolean hasBodyServerMd5Count();
    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     * @return The bodyServerMd5Count.
     */
    int getBodyServerMd5Count();

    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     * @return Whether the contentDispositionClient field is set.
     */
    boolean hasContentDispositionClient();
    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     * @return The contentDispositionClient.
     */
    com.google.protobuf.ByteString getContentDispositionClient();

    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     * @return Whether the contentDispositionServer field is set.
     */
    boolean hasContentDispositionServer();
    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     * @return The contentDispositionServer.
     */
    com.google.protobuf.ByteString getContentDispositionServer();

    /**
     * <code>optional bytes filePath = 117;</code>
     * @return Whether the filePath field is set.
     */
    boolean hasFilePath();
    /**
     * <code>optional bytes filePath = 117;</code>
     * @return The filePath.
     */
    com.google.protobuf.ByteString getFilePath();

    /**
     * <code>optional bytes setCookie = 118;</code>
     * @return Whether the setCookie field is set.
     */
    boolean hasSetCookie();
    /**
     * <code>optional bytes setCookie = 118;</code>
     * @return The setCookie.
     */
    com.google.protobuf.ByteString getSetCookie();

    /**
     * <code>optional bytes title = 119;</code>
     * @return Whether the title field is set.
     */
    boolean hasTitle();
    /**
     * <code>optional bytes title = 119;</code>
     * @return The title.
     */
    com.google.protobuf.ByteString getTitle();
  }
  /**
   * Protobuf type {@code HttpInfo}
   */
  public static final class HttpInfo extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:HttpInfo)
      HttpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        HttpInfo.class.getName());
    }
    // Use HttpInfo.newBuilder() to construct.
    private HttpInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private HttpInfo() {
      host_ = com.google.protobuf.ByteString.EMPTY;
      uri_ = com.google.protobuf.ByteString.EMPTY;
      varConEnc_ = com.google.protobuf.ByteString.EMPTY;
      authInfo_ = com.google.protobuf.ByteString.EMPTY;
      conEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      conLan_ = com.google.protobuf.ByteString.EMPTY;
      conURL_ = com.google.protobuf.ByteString.EMPTY;
      conMD5_ = com.google.protobuf.ByteString.EMPTY;
      conType_ = com.google.protobuf.ByteString.EMPTY;
      cookie_ = com.google.protobuf.ByteString.EMPTY;
      cookie2_ = com.google.protobuf.ByteString.EMPTY;
      date_ = com.google.protobuf.ByteString.EMPTY;
      from_ = com.google.protobuf.ByteString.EMPTY;
      loc_ = com.google.protobuf.ByteString.EMPTY;
      proAuthen_ = com.google.protobuf.ByteString.EMPTY;
      proAuthor_ = com.google.protobuf.ByteString.EMPTY;
      refURL_ = com.google.protobuf.ByteString.EMPTY;
      srv_ = com.google.protobuf.ByteString.EMPTY;
      setCookieKey_ = com.google.protobuf.ByteString.EMPTY;
      setCookieVal_ = com.google.protobuf.ByteString.EMPTY;
      traEnc_ = com.google.protobuf.ByteString.EMPTY;
      usrAge_ = com.google.protobuf.ByteString.EMPTY;
      via_ = com.google.protobuf.ByteString.EMPTY;
      xForFor_ = com.google.protobuf.ByteString.EMPTY;
      met_ = com.google.protobuf.ByteString.EMPTY;
      srvAge_ = com.google.protobuf.ByteString.EMPTY;
      proAuth_ = com.google.protobuf.ByteString.EMPTY;
      xPowBy_ = com.google.protobuf.ByteString.EMPTY;
      extHdrs_ = com.google.protobuf.ByteString.EMPTY;
      rangeofCli_ = com.google.protobuf.ByteString.EMPTY;
      reqVer_ = com.google.protobuf.ByteString.EMPTY;
      reqHead_ = com.google.protobuf.ByteString.EMPTY;
      cacConUp_ = com.google.protobuf.ByteString.EMPTY;
      conUp_ = com.google.protobuf.ByteString.EMPTY;
      praUp_ = com.google.protobuf.ByteString.EMPTY;
      upg_ = com.google.protobuf.ByteString.EMPTY;
      accChaUp_ = com.google.protobuf.ByteString.EMPTY;
      acctRanUp_ = com.google.protobuf.ByteString.EMPTY;
      ifMat_ = com.google.protobuf.ByteString.EMPTY;
      ifModSin_ = com.google.protobuf.ByteString.EMPTY;
      ifNonMat_ = com.google.protobuf.ByteString.EMPTY;
      ifRan_ = com.google.protobuf.ByteString.EMPTY;
      te_ = com.google.protobuf.ByteString.EMPTY;
      cacConDown_ = com.google.protobuf.ByteString.EMPTY;
      conDown_ = com.google.protobuf.ByteString.EMPTY;
      praDown_ = com.google.protobuf.ByteString.EMPTY;
      trail_ = com.google.protobuf.ByteString.EMPTY;
      accRanDown_ = com.google.protobuf.ByteString.EMPTY;
      eTag_ = com.google.protobuf.ByteString.EMPTY;
      retAft_ = com.google.protobuf.ByteString.EMPTY;
      wwwAuth_ = com.google.protobuf.ByteString.EMPTY;
      refresh_ = com.google.protobuf.ByteString.EMPTY;
      conTypDown_ = com.google.protobuf.ByteString.EMPTY;
      allow_ = com.google.protobuf.ByteString.EMPTY;
      accChaDown_ = com.google.protobuf.ByteString.EMPTY;
      httpRelKey_ = com.google.protobuf.ByteString.EMPTY;
      httpEmbPro_ = com.google.protobuf.ByteString.EMPTY;
      fullTextHeader_ = com.google.protobuf.ByteString.EMPTY;
      fileName_ = com.google.protobuf.ByteString.EMPTY;
      contDown_ = com.google.protobuf.ByteString.EMPTY;
      accByCli_ = com.google.protobuf.ByteString.EMPTY;
      accLanByCli_ = com.google.protobuf.ByteString.EMPTY;
      accEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      uriPath_ = com.google.protobuf.ByteString.EMPTY;
      uriKey_ = emptyList(com.google.protobuf.ByteString.class);
      uriSearch_ = com.google.protobuf.ByteString.EMPTY;
      user_ = com.google.protobuf.ByteString.EMPTY;
      reqBody_ = com.google.protobuf.ByteString.EMPTY;
      reqBodyN_ = com.google.protobuf.ByteString.EMPTY;
      conMD5ByCli_ = com.google.protobuf.ByteString.EMPTY;
      cookieKey_ = emptyList(com.google.protobuf.ByteString.class);
      imei_ = com.google.protobuf.ByteString.EMPTY;
      imsi_ = com.google.protobuf.ByteString.EMPTY;
      respVer_ = com.google.protobuf.ByteString.EMPTY;
      respHead_ = com.google.protobuf.ByteString.EMPTY;
      respHeadMd5_ = com.google.protobuf.ByteString.EMPTY;
      respBody_ = com.google.protobuf.ByteString.EMPTY;
      respBodyN_ = com.google.protobuf.ByteString.EMPTY;
      conMD5BySrv_ = com.google.protobuf.ByteString.EMPTY;
      location_ = com.google.protobuf.ByteString.EMPTY;
      xSinHol_ = com.google.protobuf.ByteString.EMPTY;
      conDispUp_ = com.google.protobuf.ByteString.EMPTY;
      conDispDown_ = com.google.protobuf.ByteString.EMPTY;
      authUser_ = com.google.protobuf.ByteString.EMPTY;
      contentDispositionClient_ = com.google.protobuf.ByteString.EMPTY;
      contentDispositionServer_ = com.google.protobuf.ByteString.EMPTY;
      filePath_ = com.google.protobuf.ByteString.EMPTY;
      setCookie_ = com.google.protobuf.ByteString.EMPTY;
      title_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return HttpInfoOuterClass.internal_static_HttpInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return HttpInfoOuterClass.internal_static_HttpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              HttpInfoOuterClass.HttpInfo.class, HttpInfoOuterClass.HttpInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    private int bitField3_;
    public static final int HOST_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString host_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes host = 1;</code>
     * @return Whether the host field is set.
     */
    @java.lang.Override
    public boolean hasHost() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes host = 1;</code>
     * @return The host.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getHost() {
      return host_;
    }

    public static final int URI_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString uri_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes uri = 2;</code>
     * @return Whether the uri field is set.
     */
    @java.lang.Override
    public boolean hasUri() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes uri = 2;</code>
     * @return The uri.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUri() {
      return uri_;
    }

    public static final int VARCONENC_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString varConEnc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes varConEnc = 3;</code>
     * @return Whether the varConEnc field is set.
     */
    @java.lang.Override
    public boolean hasVarConEnc() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes varConEnc = 3;</code>
     * @return The varConEnc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getVarConEnc() {
      return varConEnc_;
    }

    public static final int AUTHINFO_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString authInfo_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes authInfo = 4;</code>
     * @return Whether the authInfo field is set.
     */
    @java.lang.Override
    public boolean hasAuthInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes authInfo = 4;</code>
     * @return The authInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAuthInfo() {
      return authInfo_;
    }

    public static final int CONENCBYCLI_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString conEncByCli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conEncByCli = 5;</code>
     * @return Whether the conEncByCli field is set.
     */
    @java.lang.Override
    public boolean hasConEncByCli() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes conEncByCli = 5;</code>
     * @return The conEncByCli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConEncByCli() {
      return conEncByCli_;
    }

    public static final int CONLAN_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString conLan_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conLan = 6;</code>
     * @return Whether the conLan field is set.
     */
    @java.lang.Override
    public boolean hasConLan() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes conLan = 6;</code>
     * @return The conLan.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConLan() {
      return conLan_;
    }

    public static final int CONLENBYCLI_FIELD_NUMBER = 7;
    private int conLenByCli_ = 0;
    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     * @return Whether the conLenByCli field is set.
     */
    @java.lang.Override
    public boolean hasConLenByCli() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 conLenByCli = 7;</code>
     * @return The conLenByCli.
     */
    @java.lang.Override
    public int getConLenByCli() {
      return conLenByCli_;
    }

    public static final int CONURL_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString conURL_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conURL = 8;</code>
     * @return Whether the conURL field is set.
     */
    @java.lang.Override
    public boolean hasConURL() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes conURL = 8;</code>
     * @return The conURL.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConURL() {
      return conURL_;
    }

    public static final int CONMD5_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString conMD5_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conMD5 = 9;</code>
     * @return Whether the conMD5 field is set.
     */
    @java.lang.Override
    public boolean hasConMD5() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes conMD5 = 9;</code>
     * @return The conMD5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConMD5() {
      return conMD5_;
    }

    public static final int CONTYPE_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString conType_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conType = 10;</code>
     * @return Whether the conType field is set.
     */
    @java.lang.Override
    public boolean hasConType() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes conType = 10;</code>
     * @return The conType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConType() {
      return conType_;
    }

    public static final int COOKIE_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString cookie_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cookie = 11;</code>
     * @return Whether the cookie field is set.
     */
    @java.lang.Override
    public boolean hasCookie() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes cookie = 11;</code>
     * @return The cookie.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCookie() {
      return cookie_;
    }

    public static final int COOKIE2_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString cookie2_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cookie2 = 12;</code>
     * @return Whether the cookie2 field is set.
     */
    @java.lang.Override
    public boolean hasCookie2() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes cookie2 = 12;</code>
     * @return The cookie2.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCookie2() {
      return cookie2_;
    }

    public static final int DATE_FIELD_NUMBER = 13;
    private com.google.protobuf.ByteString date_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes date = 13;</code>
     * @return Whether the date field is set.
     */
    @java.lang.Override
    public boolean hasDate() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes date = 13;</code>
     * @return The date.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDate() {
      return date_;
    }

    public static final int FROM_FIELD_NUMBER = 14;
    private com.google.protobuf.ByteString from_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes from = 14;</code>
     * @return Whether the from field is set.
     */
    @java.lang.Override
    public boolean hasFrom() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes from = 14;</code>
     * @return The from.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getFrom() {
      return from_;
    }

    public static final int LOC_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString loc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes loc = 15;</code>
     * @return Whether the loc field is set.
     */
    @java.lang.Override
    public boolean hasLoc() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes loc = 15;</code>
     * @return The loc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getLoc() {
      return loc_;
    }

    public static final int PROAUTHEN_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString proAuthen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes proAuthen = 16;</code>
     * @return Whether the proAuthen field is set.
     */
    @java.lang.Override
    public boolean hasProAuthen() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes proAuthen = 16;</code>
     * @return The proAuthen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getProAuthen() {
      return proAuthen_;
    }

    public static final int PROAUTHOR_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString proAuthor_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes proAuthor = 17;</code>
     * @return Whether the proAuthor field is set.
     */
    @java.lang.Override
    public boolean hasProAuthor() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes proAuthor = 17;</code>
     * @return The proAuthor.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getProAuthor() {
      return proAuthor_;
    }

    public static final int REFURL_FIELD_NUMBER = 18;
    private com.google.protobuf.ByteString refURL_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes refURL = 18;</code>
     * @return Whether the refURL field is set.
     */
    @java.lang.Override
    public boolean hasRefURL() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes refURL = 18;</code>
     * @return The refURL.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRefURL() {
      return refURL_;
    }

    public static final int SRV_FIELD_NUMBER = 19;
    private com.google.protobuf.ByteString srv_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srv = 19;</code>
     * @return Whether the srv field is set.
     */
    @java.lang.Override
    public boolean hasSrv() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes srv = 19;</code>
     * @return The srv.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrv() {
      return srv_;
    }

    public static final int SRVCNT_FIELD_NUMBER = 20;
    private int srvCnt_ = 0;
    /**
     * <code>optional uint32 srvCnt = 20;</code>
     * @return Whether the srvCnt field is set.
     */
    @java.lang.Override
    public boolean hasSrvCnt() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint32 srvCnt = 20;</code>
     * @return The srvCnt.
     */
    @java.lang.Override
    public int getSrvCnt() {
      return srvCnt_;
    }

    public static final int SETCOOKIEKEY_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString setCookieKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes setCookieKey = 21;</code>
     * @return Whether the setCookieKey field is set.
     */
    @java.lang.Override
    public boolean hasSetCookieKey() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes setCookieKey = 21;</code>
     * @return The setCookieKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSetCookieKey() {
      return setCookieKey_;
    }

    public static final int SETCOOKIEVAL_FIELD_NUMBER = 22;
    private com.google.protobuf.ByteString setCookieVal_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes setCookieVal = 22;</code>
     * @return Whether the setCookieVal field is set.
     */
    @java.lang.Override
    public boolean hasSetCookieVal() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional bytes setCookieVal = 22;</code>
     * @return The setCookieVal.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSetCookieVal() {
      return setCookieVal_;
    }

    public static final int TRAENC_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString traEnc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes traEnc = 23;</code>
     * @return Whether the traEnc field is set.
     */
    @java.lang.Override
    public boolean hasTraEnc() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes traEnc = 23;</code>
     * @return The traEnc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTraEnc() {
      return traEnc_;
    }

    public static final int USRAGE_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString usrAge_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes usrAge = 24;</code>
     * @return Whether the usrAge field is set.
     */
    @java.lang.Override
    public boolean hasUsrAge() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes usrAge = 24;</code>
     * @return The usrAge.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUsrAge() {
      return usrAge_;
    }

    public static final int VIA_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString via_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes via = 25;</code>
     * @return Whether the via field is set.
     */
    @java.lang.Override
    public boolean hasVia() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes via = 25;</code>
     * @return The via.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getVia() {
      return via_;
    }

    public static final int XFORFOR_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString xForFor_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes xForFor = 26;</code>
     * @return Whether the xForFor field is set.
     */
    @java.lang.Override
    public boolean hasXForFor() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes xForFor = 26;</code>
     * @return The xForFor.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getXForFor() {
      return xForFor_;
    }

    public static final int STATCODE_FIELD_NUMBER = 27;
    private int statCode_ = 0;
    /**
     * <code>optional uint32 statCode = 27;</code>
     * @return Whether the statCode field is set.
     */
    @java.lang.Override
    public boolean hasStatCode() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional uint32 statCode = 27;</code>
     * @return The statCode.
     */
    @java.lang.Override
    public int getStatCode() {
      return statCode_;
    }

    public static final int MET_FIELD_NUMBER = 28;
    private com.google.protobuf.ByteString met_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes met = 28;</code>
     * @return Whether the met field is set.
     */
    @java.lang.Override
    public boolean hasMet() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes met = 28;</code>
     * @return The met.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMet() {
      return met_;
    }

    public static final int SRVAGE_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString srvAge_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvAge = 29;</code>
     * @return Whether the srvAge field is set.
     */
    @java.lang.Override
    public boolean hasSrvAge() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes srvAge = 29;</code>
     * @return The srvAge.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvAge() {
      return srvAge_;
    }

    public static final int PROAUTH_FIELD_NUMBER = 30;
    private com.google.protobuf.ByteString proAuth_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes proAuth = 30;</code>
     * @return Whether the proAuth field is set.
     */
    @java.lang.Override
    public boolean hasProAuth() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes proAuth = 30;</code>
     * @return The proAuth.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getProAuth() {
      return proAuth_;
    }

    public static final int XPOWBY_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString xPowBy_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes xPowBy = 31;</code>
     * @return Whether the xPowBy field is set.
     */
    @java.lang.Override
    public boolean hasXPowBy() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes xPowBy = 31;</code>
     * @return The xPowBy.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getXPowBy() {
      return xPowBy_;
    }

    public static final int EXTHDRS_FIELD_NUMBER = 32;
    private com.google.protobuf.ByteString extHdrs_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes extHdrs = 32;</code>
     * @return Whether the extHdrs field is set.
     */
    @java.lang.Override
    public boolean hasExtHdrs() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <code>optional bytes extHdrs = 32;</code>
     * @return The extHdrs.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getExtHdrs() {
      return extHdrs_;
    }

    public static final int RANGEOFCLI_FIELD_NUMBER = 33;
    private com.google.protobuf.ByteString rangeofCli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes rangeofCli = 33;</code>
     * @return Whether the rangeofCli field is set.
     */
    @java.lang.Override
    public boolean hasRangeofCli() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes rangeofCli = 33;</code>
     * @return The rangeofCli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRangeofCli() {
      return rangeofCli_;
    }

    public static final int VIACNT_FIELD_NUMBER = 34;
    private int viaCnt_ = 0;
    /**
     * <code>optional uint32 viaCnt = 34;</code>
     * @return Whether the viaCnt field is set.
     */
    @java.lang.Override
    public boolean hasViaCnt() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 viaCnt = 34;</code>
     * @return The viaCnt.
     */
    @java.lang.Override
    public int getViaCnt() {
      return viaCnt_;
    }

    public static final int STATCODECNT_FIELD_NUMBER = 35;
    private int statCodeCnt_ = 0;
    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     * @return Whether the statCodeCnt field is set.
     */
    @java.lang.Override
    public boolean hasStatCodeCnt() {
      return ((bitField1_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 statCodeCnt = 35;</code>
     * @return The statCodeCnt.
     */
    @java.lang.Override
    public int getStatCodeCnt() {
      return statCodeCnt_;
    }

    public static final int REQVER_FIELD_NUMBER = 36;
    private com.google.protobuf.ByteString reqVer_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes reqVer = 36;</code>
     * @return Whether the reqVer field is set.
     */
    @java.lang.Override
    public boolean hasReqVer() {
      return ((bitField1_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes reqVer = 36;</code>
     * @return The reqVer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getReqVer() {
      return reqVer_;
    }

    public static final int REQHEAD_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString reqHead_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes reqHead = 37;</code>
     * @return Whether the reqHead field is set.
     */
    @java.lang.Override
    public boolean hasReqHead() {
      return ((bitField1_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes reqHead = 37;</code>
     * @return The reqHead.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getReqHead() {
      return reqHead_;
    }

    public static final int REQHEADMD5_FIELD_NUMBER = 38;
    private int reqHeadMd5_ = 0;
    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     * @return Whether the reqHeadMd5 field is set.
     */
    @java.lang.Override
    public boolean hasReqHeadMd5() {
      return ((bitField1_ & 0x00000020) != 0);
    }
    /**
     * <code>optional uint32 reqHeadMd5 = 38;</code>
     * @return The reqHeadMd5.
     */
    @java.lang.Override
    public int getReqHeadMd5() {
      return reqHeadMd5_;
    }

    public static final int CACCONUP_FIELD_NUMBER = 39;
    private com.google.protobuf.ByteString cacConUp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cacConUp = 39;</code>
     * @return Whether the cacConUp field is set.
     */
    @java.lang.Override
    public boolean hasCacConUp() {
      return ((bitField1_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes cacConUp = 39;</code>
     * @return The cacConUp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCacConUp() {
      return cacConUp_;
    }

    public static final int CONUP_FIELD_NUMBER = 40;
    private com.google.protobuf.ByteString conUp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conUp = 40;</code>
     * @return Whether the conUp field is set.
     */
    @java.lang.Override
    public boolean hasConUp() {
      return ((bitField1_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes conUp = 40;</code>
     * @return The conUp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConUp() {
      return conUp_;
    }

    public static final int PRAUP_FIELD_NUMBER = 41;
    private com.google.protobuf.ByteString praUp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes praUp = 41;</code>
     * @return Whether the praUp field is set.
     */
    @java.lang.Override
    public boolean hasPraUp() {
      return ((bitField1_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes praUp = 41;</code>
     * @return The praUp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPraUp() {
      return praUp_;
    }

    public static final int UPG_FIELD_NUMBER = 42;
    private com.google.protobuf.ByteString upg_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes upg = 42;</code>
     * @return Whether the upg field is set.
     */
    @java.lang.Override
    public boolean hasUpg() {
      return ((bitField1_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes upg = 42;</code>
     * @return The upg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUpg() {
      return upg_;
    }

    public static final int ACCCHAUP_FIELD_NUMBER = 43;
    private com.google.protobuf.ByteString accChaUp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes accChaUp = 43;</code>
     * @return Whether the accChaUp field is set.
     */
    @java.lang.Override
    public boolean hasAccChaUp() {
      return ((bitField1_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes accChaUp = 43;</code>
     * @return The accChaUp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAccChaUp() {
      return accChaUp_;
    }

    public static final int ACCTRANUP_FIELD_NUMBER = 44;
    private com.google.protobuf.ByteString acctRanUp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes acctRanUp = 44;</code>
     * @return Whether the acctRanUp field is set.
     */
    @java.lang.Override
    public boolean hasAcctRanUp() {
      return ((bitField1_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes acctRanUp = 44;</code>
     * @return The acctRanUp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAcctRanUp() {
      return acctRanUp_;
    }

    public static final int IFMAT_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString ifMat_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ifMat = 45;</code>
     * @return Whether the ifMat field is set.
     */
    @java.lang.Override
    public boolean hasIfMat() {
      return ((bitField1_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes ifMat = 45;</code>
     * @return The ifMat.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIfMat() {
      return ifMat_;
    }

    public static final int IFMODSIN_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString ifModSin_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ifModSin = 46;</code>
     * @return Whether the ifModSin field is set.
     */
    @java.lang.Override
    public boolean hasIfModSin() {
      return ((bitField1_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes ifModSin = 46;</code>
     * @return The ifModSin.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIfModSin() {
      return ifModSin_;
    }

    public static final int IFNONMAT_FIELD_NUMBER = 47;
    private com.google.protobuf.ByteString ifNonMat_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ifNonMat = 47;</code>
     * @return Whether the ifNonMat field is set.
     */
    @java.lang.Override
    public boolean hasIfNonMat() {
      return ((bitField1_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes ifNonMat = 47;</code>
     * @return The ifNonMat.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIfNonMat() {
      return ifNonMat_;
    }

    public static final int IFRAN_FIELD_NUMBER = 48;
    private com.google.protobuf.ByteString ifRan_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ifRan = 48;</code>
     * @return Whether the ifRan field is set.
     */
    @java.lang.Override
    public boolean hasIfRan() {
      return ((bitField1_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes ifRan = 48;</code>
     * @return The ifRan.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIfRan() {
      return ifRan_;
    }

    public static final int IFUNMODSIN_FIELD_NUMBER = 49;
    private long ifUnModSin_ = 0L;
    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     * @return Whether the ifUnModSin field is set.
     */
    @java.lang.Override
    public boolean hasIfUnModSin() {
      return ((bitField1_ & 0x00010000) != 0);
    }
    /**
     * <code>optional uint64 ifUnModSin = 49;</code>
     * @return The ifUnModSin.
     */
    @java.lang.Override
    public long getIfUnModSin() {
      return ifUnModSin_;
    }

    public static final int MAXFOR_FIELD_NUMBER = 50;
    private int maxFor_ = 0;
    /**
     * <code>optional uint32 maxFor = 50;</code>
     * @return Whether the maxFor field is set.
     */
    @java.lang.Override
    public boolean hasMaxFor() {
      return ((bitField1_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 maxFor = 50;</code>
     * @return The maxFor.
     */
    @java.lang.Override
    public int getMaxFor() {
      return maxFor_;
    }

    public static final int TE_FIELD_NUMBER = 51;
    private com.google.protobuf.ByteString te_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes te = 51;</code>
     * @return Whether the te field is set.
     */
    @java.lang.Override
    public boolean hasTe() {
      return ((bitField1_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes te = 51;</code>
     * @return The te.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTe() {
      return te_;
    }

    public static final int CACCONDOWN_FIELD_NUMBER = 52;
    private com.google.protobuf.ByteString cacConDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cacConDown = 52;</code>
     * @return Whether the cacConDown field is set.
     */
    @java.lang.Override
    public boolean hasCacConDown() {
      return ((bitField1_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes cacConDown = 52;</code>
     * @return The cacConDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCacConDown() {
      return cacConDown_;
    }

    public static final int CONDOWN_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString conDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conDown = 53;</code>
     * @return Whether the conDown field is set.
     */
    @java.lang.Override
    public boolean hasConDown() {
      return ((bitField1_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes conDown = 53;</code>
     * @return The conDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConDown() {
      return conDown_;
    }

    public static final int PRADOWN_FIELD_NUMBER = 54;
    private com.google.protobuf.ByteString praDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes praDown = 54;</code>
     * @return Whether the praDown field is set.
     */
    @java.lang.Override
    public boolean hasPraDown() {
      return ((bitField1_ & 0x00200000) != 0);
    }
    /**
     * <code>optional bytes praDown = 54;</code>
     * @return The praDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPraDown() {
      return praDown_;
    }

    public static final int TRAIL_FIELD_NUMBER = 55;
    private com.google.protobuf.ByteString trail_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes trail = 55;</code>
     * @return Whether the trail field is set.
     */
    @java.lang.Override
    public boolean hasTrail() {
      return ((bitField1_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes trail = 55;</code>
     * @return The trail.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTrail() {
      return trail_;
    }

    public static final int ACCRANDOWN_FIELD_NUMBER = 56;
    private com.google.protobuf.ByteString accRanDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes accRanDown = 56;</code>
     * @return Whether the accRanDown field is set.
     */
    @java.lang.Override
    public boolean hasAccRanDown() {
      return ((bitField1_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes accRanDown = 56;</code>
     * @return The accRanDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAccRanDown() {
      return accRanDown_;
    }

    public static final int ETAG_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString eTag_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes eTag = 57;</code>
     * @return Whether the eTag field is set.
     */
    @java.lang.Override
    public boolean hasETag() {
      return ((bitField1_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes eTag = 57;</code>
     * @return The eTag.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getETag() {
      return eTag_;
    }

    public static final int RETAFT_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString retAft_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes retAft = 58;</code>
     * @return Whether the retAft field is set.
     */
    @java.lang.Override
    public boolean hasRetAft() {
      return ((bitField1_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes retAft = 58;</code>
     * @return The retAft.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRetAft() {
      return retAft_;
    }

    public static final int WWWAUTH_FIELD_NUMBER = 59;
    private com.google.protobuf.ByteString wwwAuth_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes wwwAuth = 59;</code>
     * @return Whether the wwwAuth field is set.
     */
    @java.lang.Override
    public boolean hasWwwAuth() {
      return ((bitField1_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes wwwAuth = 59;</code>
     * @return The wwwAuth.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getWwwAuth() {
      return wwwAuth_;
    }

    public static final int REFRESH_FIELD_NUMBER = 60;
    private com.google.protobuf.ByteString refresh_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes refresh = 60;</code>
     * @return Whether the refresh field is set.
     */
    @java.lang.Override
    public boolean hasRefresh() {
      return ((bitField1_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes refresh = 60;</code>
     * @return The refresh.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRefresh() {
      return refresh_;
    }

    public static final int CONTYPDOWN_FIELD_NUMBER = 61;
    private com.google.protobuf.ByteString conTypDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conTypDown = 61;</code>
     * @return Whether the conTypDown field is set.
     */
    @java.lang.Override
    public boolean hasConTypDown() {
      return ((bitField1_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes conTypDown = 61;</code>
     * @return The conTypDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConTypDown() {
      return conTypDown_;
    }

    public static final int ALLOW_FIELD_NUMBER = 62;
    private com.google.protobuf.ByteString allow_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes allow = 62;</code>
     * @return Whether the allow field is set.
     */
    @java.lang.Override
    public boolean hasAllow() {
      return ((bitField1_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes allow = 62;</code>
     * @return The allow.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAllow() {
      return allow_;
    }

    public static final int EXPIRES_FIELD_NUMBER = 63;
    private long expires_ = 0L;
    /**
     * <code>optional uint64 expires = 63;</code>
     * @return Whether the expires field is set.
     */
    @java.lang.Override
    public boolean hasExpires() {
      return ((bitField1_ & 0x40000000) != 0);
    }
    /**
     * <code>optional uint64 expires = 63;</code>
     * @return The expires.
     */
    @java.lang.Override
    public long getExpires() {
      return expires_;
    }

    public static final int LASMOD_FIELD_NUMBER = 64;
    private long lasMod_ = 0L;
    /**
     * <code>optional uint64 lasMod = 64;</code>
     * @return Whether the lasMod field is set.
     */
    @java.lang.Override
    public boolean hasLasMod() {
      return ((bitField1_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint64 lasMod = 64;</code>
     * @return The lasMod.
     */
    @java.lang.Override
    public long getLasMod() {
      return lasMod_;
    }

    public static final int ACCCHADOWN_FIELD_NUMBER = 65;
    private com.google.protobuf.ByteString accChaDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes accChaDown = 65;</code>
     * @return Whether the accChaDown field is set.
     */
    @java.lang.Override
    public boolean hasAccChaDown() {
      return ((bitField2_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes accChaDown = 65;</code>
     * @return The accChaDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAccChaDown() {
      return accChaDown_;
    }

    public static final int HTTPRELKEY_FIELD_NUMBER = 66;
    private com.google.protobuf.ByteString httpRelKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes httpRelKey = 66;</code>
     * @return Whether the httpRelKey field is set.
     */
    @java.lang.Override
    public boolean hasHttpRelKey() {
      return ((bitField2_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes httpRelKey = 66;</code>
     * @return The httpRelKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getHttpRelKey() {
      return httpRelKey_;
    }

    public static final int HTTPEMBPRO_FIELD_NUMBER = 67;
    private com.google.protobuf.ByteString httpEmbPro_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     * @return Whether the httpEmbPro field is set.
     */
    @java.lang.Override
    public boolean hasHttpEmbPro() {
      return ((bitField2_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes httpEmbPro = 67;</code>
     * @return The httpEmbPro.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getHttpEmbPro() {
      return httpEmbPro_;
    }

    public static final int FULLTEXTHEADER_FIELD_NUMBER = 68;
    private com.google.protobuf.ByteString fullTextHeader_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     * @return Whether the fullTextHeader field is set.
     */
    @java.lang.Override
    public boolean hasFullTextHeader() {
      return ((bitField2_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes fullTextHeader = 68;</code>
     * @return The fullTextHeader.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getFullTextHeader() {
      return fullTextHeader_;
    }

    public static final int FULLTEXTLEN_FIELD_NUMBER = 69;
    private int fullTextLen_ = 0;
    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     * @return Whether the fullTextLen field is set.
     */
    @java.lang.Override
    public boolean hasFullTextLen() {
      return ((bitField2_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 fullTextLen = 69;</code>
     * @return The fullTextLen.
     */
    @java.lang.Override
    public int getFullTextLen() {
      return fullTextLen_;
    }

    public static final int FILENAME_FIELD_NUMBER = 70;
    private com.google.protobuf.ByteString fileName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes fileName = 70;</code>
     * @return Whether the fileName field is set.
     */
    @java.lang.Override
    public boolean hasFileName() {
      return ((bitField2_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes fileName = 70;</code>
     * @return The fileName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getFileName() {
      return fileName_;
    }

    public static final int CONTDOWN_FIELD_NUMBER = 71;
    private com.google.protobuf.ByteString contDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes contDown = 71;</code>
     * @return Whether the contDown field is set.
     */
    @java.lang.Override
    public boolean hasContDown() {
      return ((bitField2_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes contDown = 71;</code>
     * @return The contDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getContDown() {
      return contDown_;
    }

    public static final int REQVERCNT_FIELD_NUMBER = 72;
    private int reqVerCnt_ = 0;
    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     * @return Whether the reqVerCnt field is set.
     */
    @java.lang.Override
    public boolean hasReqVerCnt() {
      return ((bitField2_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 reqVerCnt = 72;</code>
     * @return The reqVerCnt.
     */
    @java.lang.Override
    public int getReqVerCnt() {
      return reqVerCnt_;
    }

    public static final int METCNT_FIELD_NUMBER = 73;
    private int metCnt_ = 0;
    /**
     * <code>optional uint32 metCnt = 73;</code>
     * @return Whether the metCnt field is set.
     */
    @java.lang.Override
    public boolean hasMetCnt() {
      return ((bitField2_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint32 metCnt = 73;</code>
     * @return The metCnt.
     */
    @java.lang.Override
    public int getMetCnt() {
      return metCnt_;
    }

    public static final int REQHEADCNT_FIELD_NUMBER = 74;
    private int reqHeadCnt_ = 0;
    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     * @return Whether the reqHeadCnt field is set.
     */
    @java.lang.Override
    public boolean hasReqHeadCnt() {
      return ((bitField2_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint32 reqHeadCnt = 74;</code>
     * @return The reqHeadCnt.
     */
    @java.lang.Override
    public int getReqHeadCnt() {
      return reqHeadCnt_;
    }

    public static final int ACCBYCLI_FIELD_NUMBER = 75;
    private com.google.protobuf.ByteString accByCli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes accByCli = 75;</code>
     * @return Whether the accByCli field is set.
     */
    @java.lang.Override
    public boolean hasAccByCli() {
      return ((bitField2_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes accByCli = 75;</code>
     * @return The accByCli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAccByCli() {
      return accByCli_;
    }

    public static final int ACCLANBYCLI_FIELD_NUMBER = 76;
    private com.google.protobuf.ByteString accLanByCli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes accLanByCli = 76;</code>
     * @return Whether the accLanByCli field is set.
     */
    @java.lang.Override
    public boolean hasAccLanByCli() {
      return ((bitField2_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes accLanByCli = 76;</code>
     * @return The accLanByCli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAccLanByCli() {
      return accLanByCli_;
    }

    public static final int ACCENCBYCLI_FIELD_NUMBER = 77;
    private com.google.protobuf.ByteString accEncByCli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes accEncByCli = 77;</code>
     * @return Whether the accEncByCli field is set.
     */
    @java.lang.Override
    public boolean hasAccEncByCli() {
      return ((bitField2_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes accEncByCli = 77;</code>
     * @return The accEncByCli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAccEncByCli() {
      return accEncByCli_;
    }

    public static final int AUTHCNT_FIELD_NUMBER = 78;
    private int authCnt_ = 0;
    /**
     * <code>optional uint32 authCnt = 78;</code>
     * @return Whether the authCnt field is set.
     */
    @java.lang.Override
    public boolean hasAuthCnt() {
      return ((bitField2_ & 0x00002000) != 0);
    }
    /**
     * <code>optional uint32 authCnt = 78;</code>
     * @return The authCnt.
     */
    @java.lang.Override
    public int getAuthCnt() {
      return authCnt_;
    }

    public static final int HOSTCNT_FIELD_NUMBER = 79;
    private int hostCnt_ = 0;
    /**
     * <code>optional uint32 hostCnt = 79;</code>
     * @return Whether the hostCnt field is set.
     */
    @java.lang.Override
    public boolean hasHostCnt() {
      return ((bitField2_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 hostCnt = 79;</code>
     * @return The hostCnt.
     */
    @java.lang.Override
    public int getHostCnt() {
      return hostCnt_;
    }

    public static final int URICNT_FIELD_NUMBER = 80;
    private int uriCnt_ = 0;
    /**
     * <code>optional uint32 uriCnt = 80;</code>
     * @return Whether the uriCnt field is set.
     */
    @java.lang.Override
    public boolean hasUriCnt() {
      return ((bitField2_ & 0x00008000) != 0);
    }
    /**
     * <code>optional uint32 uriCnt = 80;</code>
     * @return The uriCnt.
     */
    @java.lang.Override
    public int getUriCnt() {
      return uriCnt_;
    }

    public static final int URIPATH_FIELD_NUMBER = 81;
    private com.google.protobuf.ByteString uriPath_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes uriPath = 81;</code>
     * @return Whether the uriPath field is set.
     */
    @java.lang.Override
    public boolean hasUriPath() {
      return ((bitField2_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes uriPath = 81;</code>
     * @return The uriPath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUriPath() {
      return uriPath_;
    }

    public static final int URIPATHCNT_FIELD_NUMBER = 82;
    private int uriPathCnt_ = 0;
    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     * @return Whether the uriPathCnt field is set.
     */
    @java.lang.Override
    public boolean hasUriPathCnt() {
      return ((bitField2_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 uriPathCnt = 82;</code>
     * @return The uriPathCnt.
     */
    @java.lang.Override
    public int getUriPathCnt() {
      return uriPathCnt_;
    }

    public static final int URIKEY_FIELD_NUMBER = 83;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> uriKey_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes uriKey = 83;</code>
     * @return A list containing the uriKey.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getUriKeyList() {
      return uriKey_;
    }
    /**
     * <code>repeated bytes uriKey = 83;</code>
     * @return The count of uriKey.
     */
    public int getUriKeyCount() {
      return uriKey_.size();
    }
    /**
     * <code>repeated bytes uriKey = 83;</code>
     * @param index The index of the element to return.
     * @return The uriKey at the given index.
     */
    public com.google.protobuf.ByteString getUriKey(int index) {
      return uriKey_.get(index);
    }

    public static final int URIKEYCNT_FIELD_NUMBER = 84;
    private int uriKeyCnt_ = 0;
    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     * @return Whether the uriKeyCnt field is set.
     */
    @java.lang.Override
    public boolean hasUriKeyCnt() {
      return ((bitField2_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 uriKeyCnt = 84;</code>
     * @return The uriKeyCnt.
     */
    @java.lang.Override
    public int getUriKeyCnt() {
      return uriKeyCnt_;
    }

    public static final int URISEARCH_FIELD_NUMBER = 85;
    private com.google.protobuf.ByteString uriSearch_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes uriSearch = 85;</code>
     * @return Whether the uriSearch field is set.
     */
    @java.lang.Override
    public boolean hasUriSearch() {
      return ((bitField2_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes uriSearch = 85;</code>
     * @return The uriSearch.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUriSearch() {
      return uriSearch_;
    }

    public static final int USRAGECNT_FIELD_NUMBER = 86;
    private int usrAgeCnt_ = 0;
    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     * @return Whether the usrAgeCnt field is set.
     */
    @java.lang.Override
    public boolean hasUsrAgeCnt() {
      return ((bitField2_ & 0x00100000) != 0);
    }
    /**
     * <code>optional uint32 usrAgeCnt = 86;</code>
     * @return The usrAgeCnt.
     */
    @java.lang.Override
    public int getUsrAgeCnt() {
      return usrAgeCnt_;
    }

    public static final int USER_FIELD_NUMBER = 87;
    private com.google.protobuf.ByteString user_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes user = 87;</code>
     * @return Whether the user field is set.
     */
    @java.lang.Override
    public boolean hasUser() {
      return ((bitField2_ & 0x00200000) != 0);
    }
    /**
     * <code>optional bytes user = 87;</code>
     * @return The user.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUser() {
      return user_;
    }

    public static final int USERCNT_FIELD_NUMBER = 88;
    private int userCnt_ = 0;
    /**
     * <code>optional uint32 userCnt = 88;</code>
     * @return Whether the userCnt field is set.
     */
    @java.lang.Override
    public boolean hasUserCnt() {
      return ((bitField2_ & 0x00400000) != 0);
    }
    /**
     * <code>optional uint32 userCnt = 88;</code>
     * @return The userCnt.
     */
    @java.lang.Override
    public int getUserCnt() {
      return userCnt_;
    }

    public static final int REQBODY_FIELD_NUMBER = 89;
    private com.google.protobuf.ByteString reqBody_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes reqBody = 89;</code>
     * @return Whether the reqBody field is set.
     */
    @java.lang.Override
    public boolean hasReqBody() {
      return ((bitField2_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes reqBody = 89;</code>
     * @return The reqBody.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getReqBody() {
      return reqBody_;
    }

    public static final int REQBODYN_FIELD_NUMBER = 90;
    private com.google.protobuf.ByteString reqBodyN_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes reqBodyN = 90;</code>
     * @return Whether the reqBodyN field is set.
     */
    @java.lang.Override
    public boolean hasReqBodyN() {
      return ((bitField2_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes reqBodyN = 90;</code>
     * @return The reqBodyN.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getReqBodyN() {
      return reqBodyN_;
    }

    public static final int CONMD5BYCLI_FIELD_NUMBER = 91;
    private com.google.protobuf.ByteString conMD5ByCli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     * @return Whether the conMD5ByCli field is set.
     */
    @java.lang.Override
    public boolean hasConMD5ByCli() {
      return ((bitField2_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes conMD5ByCli = 91;</code>
     * @return The conMD5ByCli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConMD5ByCli() {
      return conMD5ByCli_;
    }

    public static final int COOKIEKEY_FIELD_NUMBER = 92;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> cookieKey_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     * @return A list containing the cookieKey.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getCookieKeyList() {
      return cookieKey_;
    }
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     * @return The count of cookieKey.
     */
    public int getCookieKeyCount() {
      return cookieKey_.size();
    }
    /**
     * <code>repeated bytes cookieKey = 92;</code>
     * @param index The index of the element to return.
     * @return The cookieKey at the given index.
     */
    public com.google.protobuf.ByteString getCookieKey(int index) {
      return cookieKey_.get(index);
    }

    public static final int COOKIEKEYCNT_FIELD_NUMBER = 93;
    private int cookieKeyCnt_ = 0;
    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     * @return Whether the cookieKeyCnt field is set.
     */
    @java.lang.Override
    public boolean hasCookieKeyCnt() {
      return ((bitField2_ & 0x04000000) != 0);
    }
    /**
     * <code>optional uint32 cookieKeyCnt = 93;</code>
     * @return The cookieKeyCnt.
     */
    @java.lang.Override
    public int getCookieKeyCnt() {
      return cookieKeyCnt_;
    }

    public static final int IMEI_FIELD_NUMBER = 94;
    private com.google.protobuf.ByteString imei_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes imei = 94;</code>
     * @return Whether the imei field is set.
     */
    @java.lang.Override
    public boolean hasImei() {
      return ((bitField2_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes imei = 94;</code>
     * @return The imei.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getImei() {
      return imei_;
    }

    public static final int IMSI_FIELD_NUMBER = 95;
    private com.google.protobuf.ByteString imsi_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes imsi = 95;</code>
     * @return Whether the imsi field is set.
     */
    @java.lang.Override
    public boolean hasImsi() {
      return ((bitField2_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes imsi = 95;</code>
     * @return The imsi.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getImsi() {
      return imsi_;
    }

    public static final int XFORFORCNT_FIELD_NUMBER = 96;
    private int xForForCnt_ = 0;
    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     * @return Whether the xForForCnt field is set.
     */
    @java.lang.Override
    public boolean hasXForForCnt() {
      return ((bitField2_ & 0x20000000) != 0);
    }
    /**
     * <code>optional uint32 xForForCnt = 96;</code>
     * @return The xForForCnt.
     */
    @java.lang.Override
    public int getXForForCnt() {
      return xForForCnt_;
    }

    public static final int RESPVER_FIELD_NUMBER = 97;
    private com.google.protobuf.ByteString respVer_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes respVer = 97;</code>
     * @return Whether the respVer field is set.
     */
    @java.lang.Override
    public boolean hasRespVer() {
      return ((bitField2_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes respVer = 97;</code>
     * @return The respVer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRespVer() {
      return respVer_;
    }

    public static final int RESPVERCNT_FIELD_NUMBER = 98;
    private int respVerCnt_ = 0;
    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     * @return Whether the respVerCnt field is set.
     */
    @java.lang.Override
    public boolean hasRespVerCnt() {
      return ((bitField2_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint32 respVerCnt = 98;</code>
     * @return The respVerCnt.
     */
    @java.lang.Override
    public int getRespVerCnt() {
      return respVerCnt_;
    }

    public static final int RESPHEAD_FIELD_NUMBER = 99;
    private com.google.protobuf.ByteString respHead_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes respHead = 99;</code>
     * @return Whether the respHead field is set.
     */
    @java.lang.Override
    public boolean hasRespHead() {
      return ((bitField3_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes respHead = 99;</code>
     * @return The respHead.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRespHead() {
      return respHead_;
    }

    public static final int RESPHEADMD5_FIELD_NUMBER = 100;
    private com.google.protobuf.ByteString respHeadMd5_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     * @return Whether the respHeadMd5 field is set.
     */
    @java.lang.Override
    public boolean hasRespHeadMd5() {
      return ((bitField3_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes respHeadMd5 = 100;</code>
     * @return The respHeadMd5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRespHeadMd5() {
      return respHeadMd5_;
    }

    public static final int RESPHEADCNT_FIELD_NUMBER = 101;
    private int respHeadCnt_ = 0;
    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     * @return Whether the respHeadCnt field is set.
     */
    @java.lang.Override
    public boolean hasRespHeadCnt() {
      return ((bitField3_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 respHeadCnt = 101;</code>
     * @return The respHeadCnt.
     */
    @java.lang.Override
    public int getRespHeadCnt() {
      return respHeadCnt_;
    }

    public static final int RESPBODY_FIELD_NUMBER = 102;
    private com.google.protobuf.ByteString respBody_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes respBody = 102;</code>
     * @return Whether the respBody field is set.
     */
    @java.lang.Override
    public boolean hasRespBody() {
      return ((bitField3_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes respBody = 102;</code>
     * @return The respBody.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRespBody() {
      return respBody_;
    }

    public static final int RESPBODYN_FIELD_NUMBER = 103;
    private com.google.protobuf.ByteString respBodyN_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes respBodyN = 103;</code>
     * @return Whether the respBodyN field is set.
     */
    @java.lang.Override
    public boolean hasRespBodyN() {
      return ((bitField3_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes respBodyN = 103;</code>
     * @return The respBodyN.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRespBodyN() {
      return respBodyN_;
    }

    public static final int CONMD5BYSRV_FIELD_NUMBER = 104;
    private com.google.protobuf.ByteString conMD5BySrv_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     * @return Whether the conMD5BySrv field is set.
     */
    @java.lang.Override
    public boolean hasConMD5BySrv() {
      return ((bitField3_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes conMD5BySrv = 104;</code>
     * @return The conMD5BySrv.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConMD5BySrv() {
      return conMD5BySrv_;
    }

    public static final int CONENCBYSRV_FIELD_NUMBER = 105;
    private int conEncBySrv_ = 0;
    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     * @return Whether the conEncBySrv field is set.
     */
    @java.lang.Override
    public boolean hasConEncBySrv() {
      return ((bitField3_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 conEncBySrv = 105;</code>
     * @return The conEncBySrv.
     */
    @java.lang.Override
    public int getConEncBySrv() {
      return conEncBySrv_;
    }

    public static final int LOCATION_FIELD_NUMBER = 106;
    private com.google.protobuf.ByteString location_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes Location = 106;</code>
     * @return Whether the location field is set.
     */
    @java.lang.Override
    public boolean hasLocation() {
      return ((bitField3_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes Location = 106;</code>
     * @return The location.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getLocation() {
      return location_;
    }

    public static final int XSINHOL_FIELD_NUMBER = 107;
    private com.google.protobuf.ByteString xSinHol_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes xSinHol = 107;</code>
     * @return Whether the xSinHol field is set.
     */
    @java.lang.Override
    public boolean hasXSinHol() {
      return ((bitField3_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes xSinHol = 107;</code>
     * @return The xSinHol.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getXSinHol() {
      return xSinHol_;
    }

    public static final int CONENCBYSRVCNT_FIELD_NUMBER = 108;
    private int conEncBySrvCnt_ = 0;
    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     * @return Whether the conEncBySrvCnt field is set.
     */
    @java.lang.Override
    public boolean hasConEncBySrvCnt() {
      return ((bitField3_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint32 conEncBySrvCnt = 108;</code>
     * @return The conEncBySrvCnt.
     */
    @java.lang.Override
    public int getConEncBySrvCnt() {
      return conEncBySrvCnt_;
    }

    public static final int CONLENSRV_FIELD_NUMBER = 109;
    private int conLenSrv_ = 0;
    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     * @return Whether the conLenSrv field is set.
     */
    @java.lang.Override
    public boolean hasConLenSrv() {
      return ((bitField3_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint32 conLenSrv = 109;</code>
     * @return The conLenSrv.
     */
    @java.lang.Override
    public int getConLenSrv() {
      return conLenSrv_;
    }

    public static final int CONDISPUP_FIELD_NUMBER = 110;
    private com.google.protobuf.ByteString conDispUp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conDispUp = 110;</code>
     * @return Whether the conDispUp field is set.
     */
    @java.lang.Override
    public boolean hasConDispUp() {
      return ((bitField3_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes conDispUp = 110;</code>
     * @return The conDispUp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConDispUp() {
      return conDispUp_;
    }

    public static final int CONDISPDOWN_FIELD_NUMBER = 111;
    private com.google.protobuf.ByteString conDispDown_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conDispDown = 111;</code>
     * @return Whether the conDispDown field is set.
     */
    @java.lang.Override
    public boolean hasConDispDown() {
      return ((bitField3_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes conDispDown = 111;</code>
     * @return The conDispDown.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConDispDown() {
      return conDispDown_;
    }

    public static final int AUTHUSER_FIELD_NUMBER = 112;
    private com.google.protobuf.ByteString authUser_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes authUser = 112;</code>
     * @return Whether the authUser field is set.
     */
    @java.lang.Override
    public boolean hasAuthUser() {
      return ((bitField3_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes authUser = 112;</code>
     * @return The authUser.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAuthUser() {
      return authUser_;
    }

    public static final int AUTHUSERCOUNT_FIELD_NUMBER = 113;
    private int authUserCount_ = 0;
    /**
     * <code>optional uint32 authUserCount = 113;</code>
     * @return Whether the authUserCount field is set.
     */
    @java.lang.Override
    public boolean hasAuthUserCount() {
      return ((bitField3_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 authUserCount = 113;</code>
     * @return The authUserCount.
     */
    @java.lang.Override
    public int getAuthUserCount() {
      return authUserCount_;
    }

    public static final int BODYSERVERMD5COUNT_FIELD_NUMBER = 114;
    private int bodyServerMd5Count_ = 0;
    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     * @return Whether the bodyServerMd5Count field is set.
     */
    @java.lang.Override
    public boolean hasBodyServerMd5Count() {
      return ((bitField3_ & 0x00008000) != 0);
    }
    /**
     * <code>optional uint32 bodyServerMd5Count = 114;</code>
     * @return The bodyServerMd5Count.
     */
    @java.lang.Override
    public int getBodyServerMd5Count() {
      return bodyServerMd5Count_;
    }

    public static final int CONTENTDISPOSITIONCLIENT_FIELD_NUMBER = 115;
    private com.google.protobuf.ByteString contentDispositionClient_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     * @return Whether the contentDispositionClient field is set.
     */
    @java.lang.Override
    public boolean hasContentDispositionClient() {
      return ((bitField3_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes contentDispositionClient = 115;</code>
     * @return The contentDispositionClient.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getContentDispositionClient() {
      return contentDispositionClient_;
    }

    public static final int CONTENTDISPOSITIONSERVER_FIELD_NUMBER = 116;
    private com.google.protobuf.ByteString contentDispositionServer_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     * @return Whether the contentDispositionServer field is set.
     */
    @java.lang.Override
    public boolean hasContentDispositionServer() {
      return ((bitField3_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes contentDispositionServer = 116;</code>
     * @return The contentDispositionServer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getContentDispositionServer() {
      return contentDispositionServer_;
    }

    public static final int FILEPATH_FIELD_NUMBER = 117;
    private com.google.protobuf.ByteString filePath_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes filePath = 117;</code>
     * @return Whether the filePath field is set.
     */
    @java.lang.Override
    public boolean hasFilePath() {
      return ((bitField3_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes filePath = 117;</code>
     * @return The filePath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getFilePath() {
      return filePath_;
    }

    public static final int SETCOOKIE_FIELD_NUMBER = 118;
    private com.google.protobuf.ByteString setCookie_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes setCookie = 118;</code>
     * @return Whether the setCookie field is set.
     */
    @java.lang.Override
    public boolean hasSetCookie() {
      return ((bitField3_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes setCookie = 118;</code>
     * @return The setCookie.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSetCookie() {
      return setCookie_;
    }

    public static final int TITLE_FIELD_NUMBER = 119;
    private com.google.protobuf.ByteString title_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes title = 119;</code>
     * @return Whether the title field is set.
     */
    @java.lang.Override
    public boolean hasTitle() {
      return ((bitField3_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes title = 119;</code>
     * @return The title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTitle() {
      return title_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, host_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, uri_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBytes(3, varConEnc_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBytes(4, authInfo_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBytes(5, conEncByCli_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(6, conLan_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeUInt32(7, conLenByCli_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeBytes(8, conURL_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeBytes(9, conMD5_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeBytes(10, conType_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeBytes(11, cookie_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeBytes(12, cookie2_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeBytes(13, date_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeBytes(14, from_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeBytes(15, loc_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeBytes(16, proAuthen_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(17, proAuthor_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeBytes(18, refURL_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeBytes(19, srv_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeUInt32(20, srvCnt_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeBytes(21, setCookieKey_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeBytes(22, setCookieVal_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeBytes(23, traEnc_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeBytes(24, usrAge_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeBytes(25, via_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeBytes(26, xForFor_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeUInt32(27, statCode_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeBytes(28, met_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeBytes(29, srvAge_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeBytes(30, proAuth_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeBytes(31, xPowBy_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeBytes(32, extHdrs_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeBytes(33, rangeofCli_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeUInt32(34, viaCnt_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        output.writeUInt32(35, statCodeCnt_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        output.writeBytes(36, reqVer_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        output.writeBytes(37, reqHead_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        output.writeUInt32(38, reqHeadMd5_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        output.writeBytes(39, cacConUp_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        output.writeBytes(40, conUp_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        output.writeBytes(41, praUp_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        output.writeBytes(42, upg_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        output.writeBytes(43, accChaUp_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        output.writeBytes(44, acctRanUp_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        output.writeBytes(45, ifMat_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        output.writeBytes(46, ifModSin_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        output.writeBytes(47, ifNonMat_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        output.writeBytes(48, ifRan_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        output.writeUInt64(49, ifUnModSin_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        output.writeUInt32(50, maxFor_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        output.writeBytes(51, te_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        output.writeBytes(52, cacConDown_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        output.writeBytes(53, conDown_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        output.writeBytes(54, praDown_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        output.writeBytes(55, trail_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        output.writeBytes(56, accRanDown_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        output.writeBytes(57, eTag_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        output.writeBytes(58, retAft_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        output.writeBytes(59, wwwAuth_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        output.writeBytes(60, refresh_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        output.writeBytes(61, conTypDown_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        output.writeBytes(62, allow_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        output.writeUInt64(63, expires_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        output.writeUInt64(64, lasMod_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        output.writeBytes(65, accChaDown_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        output.writeBytes(66, httpRelKey_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        output.writeBytes(67, httpEmbPro_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        output.writeBytes(68, fullTextHeader_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        output.writeUInt32(69, fullTextLen_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        output.writeBytes(70, fileName_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        output.writeBytes(71, contDown_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        output.writeUInt32(72, reqVerCnt_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        output.writeUInt32(73, metCnt_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        output.writeUInt32(74, reqHeadCnt_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        output.writeBytes(75, accByCli_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        output.writeBytes(76, accLanByCli_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        output.writeBytes(77, accEncByCli_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        output.writeUInt32(78, authCnt_);
      }
      if (((bitField2_ & 0x00004000) != 0)) {
        output.writeUInt32(79, hostCnt_);
      }
      if (((bitField2_ & 0x00008000) != 0)) {
        output.writeUInt32(80, uriCnt_);
      }
      if (((bitField2_ & 0x00010000) != 0)) {
        output.writeBytes(81, uriPath_);
      }
      if (((bitField2_ & 0x00020000) != 0)) {
        output.writeUInt32(82, uriPathCnt_);
      }
      for (int i = 0; i < uriKey_.size(); i++) {
        output.writeBytes(83, uriKey_.get(i));
      }
      if (((bitField2_ & 0x00040000) != 0)) {
        output.writeUInt32(84, uriKeyCnt_);
      }
      if (((bitField2_ & 0x00080000) != 0)) {
        output.writeBytes(85, uriSearch_);
      }
      if (((bitField2_ & 0x00100000) != 0)) {
        output.writeUInt32(86, usrAgeCnt_);
      }
      if (((bitField2_ & 0x00200000) != 0)) {
        output.writeBytes(87, user_);
      }
      if (((bitField2_ & 0x00400000) != 0)) {
        output.writeUInt32(88, userCnt_);
      }
      if (((bitField2_ & 0x00800000) != 0)) {
        output.writeBytes(89, reqBody_);
      }
      if (((bitField2_ & 0x01000000) != 0)) {
        output.writeBytes(90, reqBodyN_);
      }
      if (((bitField2_ & 0x02000000) != 0)) {
        output.writeBytes(91, conMD5ByCli_);
      }
      for (int i = 0; i < cookieKey_.size(); i++) {
        output.writeBytes(92, cookieKey_.get(i));
      }
      if (((bitField2_ & 0x04000000) != 0)) {
        output.writeUInt32(93, cookieKeyCnt_);
      }
      if (((bitField2_ & 0x08000000) != 0)) {
        output.writeBytes(94, imei_);
      }
      if (((bitField2_ & 0x10000000) != 0)) {
        output.writeBytes(95, imsi_);
      }
      if (((bitField2_ & 0x20000000) != 0)) {
        output.writeUInt32(96, xForForCnt_);
      }
      if (((bitField2_ & 0x40000000) != 0)) {
        output.writeBytes(97, respVer_);
      }
      if (((bitField2_ & 0x80000000) != 0)) {
        output.writeUInt32(98, respVerCnt_);
      }
      if (((bitField3_ & 0x00000001) != 0)) {
        output.writeBytes(99, respHead_);
      }
      if (((bitField3_ & 0x00000002) != 0)) {
        output.writeBytes(100, respHeadMd5_);
      }
      if (((bitField3_ & 0x00000004) != 0)) {
        output.writeUInt32(101, respHeadCnt_);
      }
      if (((bitField3_ & 0x00000008) != 0)) {
        output.writeBytes(102, respBody_);
      }
      if (((bitField3_ & 0x00000010) != 0)) {
        output.writeBytes(103, respBodyN_);
      }
      if (((bitField3_ & 0x00000020) != 0)) {
        output.writeBytes(104, conMD5BySrv_);
      }
      if (((bitField3_ & 0x00000040) != 0)) {
        output.writeUInt32(105, conEncBySrv_);
      }
      if (((bitField3_ & 0x00000080) != 0)) {
        output.writeBytes(106, location_);
      }
      if (((bitField3_ & 0x00000100) != 0)) {
        output.writeBytes(107, xSinHol_);
      }
      if (((bitField3_ & 0x00000200) != 0)) {
        output.writeUInt32(108, conEncBySrvCnt_);
      }
      if (((bitField3_ & 0x00000400) != 0)) {
        output.writeUInt32(109, conLenSrv_);
      }
      if (((bitField3_ & 0x00000800) != 0)) {
        output.writeBytes(110, conDispUp_);
      }
      if (((bitField3_ & 0x00001000) != 0)) {
        output.writeBytes(111, conDispDown_);
      }
      if (((bitField3_ & 0x00002000) != 0)) {
        output.writeBytes(112, authUser_);
      }
      if (((bitField3_ & 0x00004000) != 0)) {
        output.writeUInt32(113, authUserCount_);
      }
      if (((bitField3_ & 0x00008000) != 0)) {
        output.writeUInt32(114, bodyServerMd5Count_);
      }
      if (((bitField3_ & 0x00010000) != 0)) {
        output.writeBytes(115, contentDispositionClient_);
      }
      if (((bitField3_ & 0x00020000) != 0)) {
        output.writeBytes(116, contentDispositionServer_);
      }
      if (((bitField3_ & 0x00040000) != 0)) {
        output.writeBytes(117, filePath_);
      }
      if (((bitField3_ & 0x00080000) != 0)) {
        output.writeBytes(118, setCookie_);
      }
      if (((bitField3_ & 0x00100000) != 0)) {
        output.writeBytes(119, title_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, host_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, uri_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, varConEnc_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, authInfo_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, conEncByCli_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, conLan_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, conLenByCli_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, conURL_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, conMD5_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, conType_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, cookie_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, cookie2_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(13, date_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(14, from_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, loc_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, proAuthen_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, proAuthor_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(18, refURL_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(19, srv_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, srvCnt_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, setCookieKey_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(22, setCookieVal_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, traEnc_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, usrAge_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, via_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, xForFor_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(27, statCode_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(28, met_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, srvAge_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(30, proAuth_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, xPowBy_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(32, extHdrs_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(33, rangeofCli_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(34, viaCnt_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(35, statCodeCnt_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(36, reqVer_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, reqHead_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, reqHeadMd5_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(39, cacConUp_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(40, conUp_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(41, praUp_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(42, upg_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(43, accChaUp_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(44, acctRanUp_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, ifMat_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, ifModSin_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(47, ifNonMat_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(48, ifRan_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(49, ifUnModSin_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(50, maxFor_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(51, te_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(52, cacConDown_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, conDown_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(54, praDown_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(55, trail_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(56, accRanDown_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, eTag_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, retAft_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(59, wwwAuth_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(60, refresh_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(61, conTypDown_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(62, allow_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(63, expires_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(64, lasMod_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(65, accChaDown_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(66, httpRelKey_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(67, httpEmbPro_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(68, fullTextHeader_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(69, fullTextLen_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(70, fileName_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(71, contDown_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(72, reqVerCnt_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(73, metCnt_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(74, reqHeadCnt_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(75, accByCli_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(76, accLanByCli_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(77, accEncByCli_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(78, authCnt_);
      }
      if (((bitField2_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(79, hostCnt_);
      }
      if (((bitField2_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(80, uriCnt_);
      }
      if (((bitField2_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(81, uriPath_);
      }
      if (((bitField2_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(82, uriPathCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < uriKey_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(uriKey_.get(i));
        }
        size += dataSize;
        size += 2 * getUriKeyList().size();
      }
      if (((bitField2_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(84, uriKeyCnt_);
      }
      if (((bitField2_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(85, uriSearch_);
      }
      if (((bitField2_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(86, usrAgeCnt_);
      }
      if (((bitField2_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(87, user_);
      }
      if (((bitField2_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(88, userCnt_);
      }
      if (((bitField2_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(89, reqBody_);
      }
      if (((bitField2_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(90, reqBodyN_);
      }
      if (((bitField2_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(91, conMD5ByCli_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < cookieKey_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(cookieKey_.get(i));
        }
        size += dataSize;
        size += 2 * getCookieKeyList().size();
      }
      if (((bitField2_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(93, cookieKeyCnt_);
      }
      if (((bitField2_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(94, imei_);
      }
      if (((bitField2_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(95, imsi_);
      }
      if (((bitField2_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(96, xForForCnt_);
      }
      if (((bitField2_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(97, respVer_);
      }
      if (((bitField2_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(98, respVerCnt_);
      }
      if (((bitField3_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(99, respHead_);
      }
      if (((bitField3_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(100, respHeadMd5_);
      }
      if (((bitField3_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(101, respHeadCnt_);
      }
      if (((bitField3_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(102, respBody_);
      }
      if (((bitField3_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(103, respBodyN_);
      }
      if (((bitField3_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(104, conMD5BySrv_);
      }
      if (((bitField3_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(105, conEncBySrv_);
      }
      if (((bitField3_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(106, location_);
      }
      if (((bitField3_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(107, xSinHol_);
      }
      if (((bitField3_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(108, conEncBySrvCnt_);
      }
      if (((bitField3_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(109, conLenSrv_);
      }
      if (((bitField3_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(110, conDispUp_);
      }
      if (((bitField3_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(111, conDispDown_);
      }
      if (((bitField3_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(112, authUser_);
      }
      if (((bitField3_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(113, authUserCount_);
      }
      if (((bitField3_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(114, bodyServerMd5Count_);
      }
      if (((bitField3_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(115, contentDispositionClient_);
      }
      if (((bitField3_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(116, contentDispositionServer_);
      }
      if (((bitField3_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(117, filePath_);
      }
      if (((bitField3_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(118, setCookie_);
      }
      if (((bitField3_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(119, title_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof HttpInfoOuterClass.HttpInfo)) {
        return super.equals(obj);
      }
      HttpInfoOuterClass.HttpInfo other = (HttpInfoOuterClass.HttpInfo) obj;

      if (hasHost() != other.hasHost()) return false;
      if (hasHost()) {
        if (!getHost()
            .equals(other.getHost())) return false;
      }
      if (hasUri() != other.hasUri()) return false;
      if (hasUri()) {
        if (!getUri()
            .equals(other.getUri())) return false;
      }
      if (hasVarConEnc() != other.hasVarConEnc()) return false;
      if (hasVarConEnc()) {
        if (!getVarConEnc()
            .equals(other.getVarConEnc())) return false;
      }
      if (hasAuthInfo() != other.hasAuthInfo()) return false;
      if (hasAuthInfo()) {
        if (!getAuthInfo()
            .equals(other.getAuthInfo())) return false;
      }
      if (hasConEncByCli() != other.hasConEncByCli()) return false;
      if (hasConEncByCli()) {
        if (!getConEncByCli()
            .equals(other.getConEncByCli())) return false;
      }
      if (hasConLan() != other.hasConLan()) return false;
      if (hasConLan()) {
        if (!getConLan()
            .equals(other.getConLan())) return false;
      }
      if (hasConLenByCli() != other.hasConLenByCli()) return false;
      if (hasConLenByCli()) {
        if (getConLenByCli()
            != other.getConLenByCli()) return false;
      }
      if (hasConURL() != other.hasConURL()) return false;
      if (hasConURL()) {
        if (!getConURL()
            .equals(other.getConURL())) return false;
      }
      if (hasConMD5() != other.hasConMD5()) return false;
      if (hasConMD5()) {
        if (!getConMD5()
            .equals(other.getConMD5())) return false;
      }
      if (hasConType() != other.hasConType()) return false;
      if (hasConType()) {
        if (!getConType()
            .equals(other.getConType())) return false;
      }
      if (hasCookie() != other.hasCookie()) return false;
      if (hasCookie()) {
        if (!getCookie()
            .equals(other.getCookie())) return false;
      }
      if (hasCookie2() != other.hasCookie2()) return false;
      if (hasCookie2()) {
        if (!getCookie2()
            .equals(other.getCookie2())) return false;
      }
      if (hasDate() != other.hasDate()) return false;
      if (hasDate()) {
        if (!getDate()
            .equals(other.getDate())) return false;
      }
      if (hasFrom() != other.hasFrom()) return false;
      if (hasFrom()) {
        if (!getFrom()
            .equals(other.getFrom())) return false;
      }
      if (hasLoc() != other.hasLoc()) return false;
      if (hasLoc()) {
        if (!getLoc()
            .equals(other.getLoc())) return false;
      }
      if (hasProAuthen() != other.hasProAuthen()) return false;
      if (hasProAuthen()) {
        if (!getProAuthen()
            .equals(other.getProAuthen())) return false;
      }
      if (hasProAuthor() != other.hasProAuthor()) return false;
      if (hasProAuthor()) {
        if (!getProAuthor()
            .equals(other.getProAuthor())) return false;
      }
      if (hasRefURL() != other.hasRefURL()) return false;
      if (hasRefURL()) {
        if (!getRefURL()
            .equals(other.getRefURL())) return false;
      }
      if (hasSrv() != other.hasSrv()) return false;
      if (hasSrv()) {
        if (!getSrv()
            .equals(other.getSrv())) return false;
      }
      if (hasSrvCnt() != other.hasSrvCnt()) return false;
      if (hasSrvCnt()) {
        if (getSrvCnt()
            != other.getSrvCnt()) return false;
      }
      if (hasSetCookieKey() != other.hasSetCookieKey()) return false;
      if (hasSetCookieKey()) {
        if (!getSetCookieKey()
            .equals(other.getSetCookieKey())) return false;
      }
      if (hasSetCookieVal() != other.hasSetCookieVal()) return false;
      if (hasSetCookieVal()) {
        if (!getSetCookieVal()
            .equals(other.getSetCookieVal())) return false;
      }
      if (hasTraEnc() != other.hasTraEnc()) return false;
      if (hasTraEnc()) {
        if (!getTraEnc()
            .equals(other.getTraEnc())) return false;
      }
      if (hasUsrAge() != other.hasUsrAge()) return false;
      if (hasUsrAge()) {
        if (!getUsrAge()
            .equals(other.getUsrAge())) return false;
      }
      if (hasVia() != other.hasVia()) return false;
      if (hasVia()) {
        if (!getVia()
            .equals(other.getVia())) return false;
      }
      if (hasXForFor() != other.hasXForFor()) return false;
      if (hasXForFor()) {
        if (!getXForFor()
            .equals(other.getXForFor())) return false;
      }
      if (hasStatCode() != other.hasStatCode()) return false;
      if (hasStatCode()) {
        if (getStatCode()
            != other.getStatCode()) return false;
      }
      if (hasMet() != other.hasMet()) return false;
      if (hasMet()) {
        if (!getMet()
            .equals(other.getMet())) return false;
      }
      if (hasSrvAge() != other.hasSrvAge()) return false;
      if (hasSrvAge()) {
        if (!getSrvAge()
            .equals(other.getSrvAge())) return false;
      }
      if (hasProAuth() != other.hasProAuth()) return false;
      if (hasProAuth()) {
        if (!getProAuth()
            .equals(other.getProAuth())) return false;
      }
      if (hasXPowBy() != other.hasXPowBy()) return false;
      if (hasXPowBy()) {
        if (!getXPowBy()
            .equals(other.getXPowBy())) return false;
      }
      if (hasExtHdrs() != other.hasExtHdrs()) return false;
      if (hasExtHdrs()) {
        if (!getExtHdrs()
            .equals(other.getExtHdrs())) return false;
      }
      if (hasRangeofCli() != other.hasRangeofCli()) return false;
      if (hasRangeofCli()) {
        if (!getRangeofCli()
            .equals(other.getRangeofCli())) return false;
      }
      if (hasViaCnt() != other.hasViaCnt()) return false;
      if (hasViaCnt()) {
        if (getViaCnt()
            != other.getViaCnt()) return false;
      }
      if (hasStatCodeCnt() != other.hasStatCodeCnt()) return false;
      if (hasStatCodeCnt()) {
        if (getStatCodeCnt()
            != other.getStatCodeCnt()) return false;
      }
      if (hasReqVer() != other.hasReqVer()) return false;
      if (hasReqVer()) {
        if (!getReqVer()
            .equals(other.getReqVer())) return false;
      }
      if (hasReqHead() != other.hasReqHead()) return false;
      if (hasReqHead()) {
        if (!getReqHead()
            .equals(other.getReqHead())) return false;
      }
      if (hasReqHeadMd5() != other.hasReqHeadMd5()) return false;
      if (hasReqHeadMd5()) {
        if (getReqHeadMd5()
            != other.getReqHeadMd5()) return false;
      }
      if (hasCacConUp() != other.hasCacConUp()) return false;
      if (hasCacConUp()) {
        if (!getCacConUp()
            .equals(other.getCacConUp())) return false;
      }
      if (hasConUp() != other.hasConUp()) return false;
      if (hasConUp()) {
        if (!getConUp()
            .equals(other.getConUp())) return false;
      }
      if (hasPraUp() != other.hasPraUp()) return false;
      if (hasPraUp()) {
        if (!getPraUp()
            .equals(other.getPraUp())) return false;
      }
      if (hasUpg() != other.hasUpg()) return false;
      if (hasUpg()) {
        if (!getUpg()
            .equals(other.getUpg())) return false;
      }
      if (hasAccChaUp() != other.hasAccChaUp()) return false;
      if (hasAccChaUp()) {
        if (!getAccChaUp()
            .equals(other.getAccChaUp())) return false;
      }
      if (hasAcctRanUp() != other.hasAcctRanUp()) return false;
      if (hasAcctRanUp()) {
        if (!getAcctRanUp()
            .equals(other.getAcctRanUp())) return false;
      }
      if (hasIfMat() != other.hasIfMat()) return false;
      if (hasIfMat()) {
        if (!getIfMat()
            .equals(other.getIfMat())) return false;
      }
      if (hasIfModSin() != other.hasIfModSin()) return false;
      if (hasIfModSin()) {
        if (!getIfModSin()
            .equals(other.getIfModSin())) return false;
      }
      if (hasIfNonMat() != other.hasIfNonMat()) return false;
      if (hasIfNonMat()) {
        if (!getIfNonMat()
            .equals(other.getIfNonMat())) return false;
      }
      if (hasIfRan() != other.hasIfRan()) return false;
      if (hasIfRan()) {
        if (!getIfRan()
            .equals(other.getIfRan())) return false;
      }
      if (hasIfUnModSin() != other.hasIfUnModSin()) return false;
      if (hasIfUnModSin()) {
        if (getIfUnModSin()
            != other.getIfUnModSin()) return false;
      }
      if (hasMaxFor() != other.hasMaxFor()) return false;
      if (hasMaxFor()) {
        if (getMaxFor()
            != other.getMaxFor()) return false;
      }
      if (hasTe() != other.hasTe()) return false;
      if (hasTe()) {
        if (!getTe()
            .equals(other.getTe())) return false;
      }
      if (hasCacConDown() != other.hasCacConDown()) return false;
      if (hasCacConDown()) {
        if (!getCacConDown()
            .equals(other.getCacConDown())) return false;
      }
      if (hasConDown() != other.hasConDown()) return false;
      if (hasConDown()) {
        if (!getConDown()
            .equals(other.getConDown())) return false;
      }
      if (hasPraDown() != other.hasPraDown()) return false;
      if (hasPraDown()) {
        if (!getPraDown()
            .equals(other.getPraDown())) return false;
      }
      if (hasTrail() != other.hasTrail()) return false;
      if (hasTrail()) {
        if (!getTrail()
            .equals(other.getTrail())) return false;
      }
      if (hasAccRanDown() != other.hasAccRanDown()) return false;
      if (hasAccRanDown()) {
        if (!getAccRanDown()
            .equals(other.getAccRanDown())) return false;
      }
      if (hasETag() != other.hasETag()) return false;
      if (hasETag()) {
        if (!getETag()
            .equals(other.getETag())) return false;
      }
      if (hasRetAft() != other.hasRetAft()) return false;
      if (hasRetAft()) {
        if (!getRetAft()
            .equals(other.getRetAft())) return false;
      }
      if (hasWwwAuth() != other.hasWwwAuth()) return false;
      if (hasWwwAuth()) {
        if (!getWwwAuth()
            .equals(other.getWwwAuth())) return false;
      }
      if (hasRefresh() != other.hasRefresh()) return false;
      if (hasRefresh()) {
        if (!getRefresh()
            .equals(other.getRefresh())) return false;
      }
      if (hasConTypDown() != other.hasConTypDown()) return false;
      if (hasConTypDown()) {
        if (!getConTypDown()
            .equals(other.getConTypDown())) return false;
      }
      if (hasAllow() != other.hasAllow()) return false;
      if (hasAllow()) {
        if (!getAllow()
            .equals(other.getAllow())) return false;
      }
      if (hasExpires() != other.hasExpires()) return false;
      if (hasExpires()) {
        if (getExpires()
            != other.getExpires()) return false;
      }
      if (hasLasMod() != other.hasLasMod()) return false;
      if (hasLasMod()) {
        if (getLasMod()
            != other.getLasMod()) return false;
      }
      if (hasAccChaDown() != other.hasAccChaDown()) return false;
      if (hasAccChaDown()) {
        if (!getAccChaDown()
            .equals(other.getAccChaDown())) return false;
      }
      if (hasHttpRelKey() != other.hasHttpRelKey()) return false;
      if (hasHttpRelKey()) {
        if (!getHttpRelKey()
            .equals(other.getHttpRelKey())) return false;
      }
      if (hasHttpEmbPro() != other.hasHttpEmbPro()) return false;
      if (hasHttpEmbPro()) {
        if (!getHttpEmbPro()
            .equals(other.getHttpEmbPro())) return false;
      }
      if (hasFullTextHeader() != other.hasFullTextHeader()) return false;
      if (hasFullTextHeader()) {
        if (!getFullTextHeader()
            .equals(other.getFullTextHeader())) return false;
      }
      if (hasFullTextLen() != other.hasFullTextLen()) return false;
      if (hasFullTextLen()) {
        if (getFullTextLen()
            != other.getFullTextLen()) return false;
      }
      if (hasFileName() != other.hasFileName()) return false;
      if (hasFileName()) {
        if (!getFileName()
            .equals(other.getFileName())) return false;
      }
      if (hasContDown() != other.hasContDown()) return false;
      if (hasContDown()) {
        if (!getContDown()
            .equals(other.getContDown())) return false;
      }
      if (hasReqVerCnt() != other.hasReqVerCnt()) return false;
      if (hasReqVerCnt()) {
        if (getReqVerCnt()
            != other.getReqVerCnt()) return false;
      }
      if (hasMetCnt() != other.hasMetCnt()) return false;
      if (hasMetCnt()) {
        if (getMetCnt()
            != other.getMetCnt()) return false;
      }
      if (hasReqHeadCnt() != other.hasReqHeadCnt()) return false;
      if (hasReqHeadCnt()) {
        if (getReqHeadCnt()
            != other.getReqHeadCnt()) return false;
      }
      if (hasAccByCli() != other.hasAccByCli()) return false;
      if (hasAccByCli()) {
        if (!getAccByCli()
            .equals(other.getAccByCli())) return false;
      }
      if (hasAccLanByCli() != other.hasAccLanByCli()) return false;
      if (hasAccLanByCli()) {
        if (!getAccLanByCli()
            .equals(other.getAccLanByCli())) return false;
      }
      if (hasAccEncByCli() != other.hasAccEncByCli()) return false;
      if (hasAccEncByCli()) {
        if (!getAccEncByCli()
            .equals(other.getAccEncByCli())) return false;
      }
      if (hasAuthCnt() != other.hasAuthCnt()) return false;
      if (hasAuthCnt()) {
        if (getAuthCnt()
            != other.getAuthCnt()) return false;
      }
      if (hasHostCnt() != other.hasHostCnt()) return false;
      if (hasHostCnt()) {
        if (getHostCnt()
            != other.getHostCnt()) return false;
      }
      if (hasUriCnt() != other.hasUriCnt()) return false;
      if (hasUriCnt()) {
        if (getUriCnt()
            != other.getUriCnt()) return false;
      }
      if (hasUriPath() != other.hasUriPath()) return false;
      if (hasUriPath()) {
        if (!getUriPath()
            .equals(other.getUriPath())) return false;
      }
      if (hasUriPathCnt() != other.hasUriPathCnt()) return false;
      if (hasUriPathCnt()) {
        if (getUriPathCnt()
            != other.getUriPathCnt()) return false;
      }
      if (!getUriKeyList()
          .equals(other.getUriKeyList())) return false;
      if (hasUriKeyCnt() != other.hasUriKeyCnt()) return false;
      if (hasUriKeyCnt()) {
        if (getUriKeyCnt()
            != other.getUriKeyCnt()) return false;
      }
      if (hasUriSearch() != other.hasUriSearch()) return false;
      if (hasUriSearch()) {
        if (!getUriSearch()
            .equals(other.getUriSearch())) return false;
      }
      if (hasUsrAgeCnt() != other.hasUsrAgeCnt()) return false;
      if (hasUsrAgeCnt()) {
        if (getUsrAgeCnt()
            != other.getUsrAgeCnt()) return false;
      }
      if (hasUser() != other.hasUser()) return false;
      if (hasUser()) {
        if (!getUser()
            .equals(other.getUser())) return false;
      }
      if (hasUserCnt() != other.hasUserCnt()) return false;
      if (hasUserCnt()) {
        if (getUserCnt()
            != other.getUserCnt()) return false;
      }
      if (hasReqBody() != other.hasReqBody()) return false;
      if (hasReqBody()) {
        if (!getReqBody()
            .equals(other.getReqBody())) return false;
      }
      if (hasReqBodyN() != other.hasReqBodyN()) return false;
      if (hasReqBodyN()) {
        if (!getReqBodyN()
            .equals(other.getReqBodyN())) return false;
      }
      if (hasConMD5ByCli() != other.hasConMD5ByCli()) return false;
      if (hasConMD5ByCli()) {
        if (!getConMD5ByCli()
            .equals(other.getConMD5ByCli())) return false;
      }
      if (!getCookieKeyList()
          .equals(other.getCookieKeyList())) return false;
      if (hasCookieKeyCnt() != other.hasCookieKeyCnt()) return false;
      if (hasCookieKeyCnt()) {
        if (getCookieKeyCnt()
            != other.getCookieKeyCnt()) return false;
      }
      if (hasImei() != other.hasImei()) return false;
      if (hasImei()) {
        if (!getImei()
            .equals(other.getImei())) return false;
      }
      if (hasImsi() != other.hasImsi()) return false;
      if (hasImsi()) {
        if (!getImsi()
            .equals(other.getImsi())) return false;
      }
      if (hasXForForCnt() != other.hasXForForCnt()) return false;
      if (hasXForForCnt()) {
        if (getXForForCnt()
            != other.getXForForCnt()) return false;
      }
      if (hasRespVer() != other.hasRespVer()) return false;
      if (hasRespVer()) {
        if (!getRespVer()
            .equals(other.getRespVer())) return false;
      }
      if (hasRespVerCnt() != other.hasRespVerCnt()) return false;
      if (hasRespVerCnt()) {
        if (getRespVerCnt()
            != other.getRespVerCnt()) return false;
      }
      if (hasRespHead() != other.hasRespHead()) return false;
      if (hasRespHead()) {
        if (!getRespHead()
            .equals(other.getRespHead())) return false;
      }
      if (hasRespHeadMd5() != other.hasRespHeadMd5()) return false;
      if (hasRespHeadMd5()) {
        if (!getRespHeadMd5()
            .equals(other.getRespHeadMd5())) return false;
      }
      if (hasRespHeadCnt() != other.hasRespHeadCnt()) return false;
      if (hasRespHeadCnt()) {
        if (getRespHeadCnt()
            != other.getRespHeadCnt()) return false;
      }
      if (hasRespBody() != other.hasRespBody()) return false;
      if (hasRespBody()) {
        if (!getRespBody()
            .equals(other.getRespBody())) return false;
      }
      if (hasRespBodyN() != other.hasRespBodyN()) return false;
      if (hasRespBodyN()) {
        if (!getRespBodyN()
            .equals(other.getRespBodyN())) return false;
      }
      if (hasConMD5BySrv() != other.hasConMD5BySrv()) return false;
      if (hasConMD5BySrv()) {
        if (!getConMD5BySrv()
            .equals(other.getConMD5BySrv())) return false;
      }
      if (hasConEncBySrv() != other.hasConEncBySrv()) return false;
      if (hasConEncBySrv()) {
        if (getConEncBySrv()
            != other.getConEncBySrv()) return false;
      }
      if (hasLocation() != other.hasLocation()) return false;
      if (hasLocation()) {
        if (!getLocation()
            .equals(other.getLocation())) return false;
      }
      if (hasXSinHol() != other.hasXSinHol()) return false;
      if (hasXSinHol()) {
        if (!getXSinHol()
            .equals(other.getXSinHol())) return false;
      }
      if (hasConEncBySrvCnt() != other.hasConEncBySrvCnt()) return false;
      if (hasConEncBySrvCnt()) {
        if (getConEncBySrvCnt()
            != other.getConEncBySrvCnt()) return false;
      }
      if (hasConLenSrv() != other.hasConLenSrv()) return false;
      if (hasConLenSrv()) {
        if (getConLenSrv()
            != other.getConLenSrv()) return false;
      }
      if (hasConDispUp() != other.hasConDispUp()) return false;
      if (hasConDispUp()) {
        if (!getConDispUp()
            .equals(other.getConDispUp())) return false;
      }
      if (hasConDispDown() != other.hasConDispDown()) return false;
      if (hasConDispDown()) {
        if (!getConDispDown()
            .equals(other.getConDispDown())) return false;
      }
      if (hasAuthUser() != other.hasAuthUser()) return false;
      if (hasAuthUser()) {
        if (!getAuthUser()
            .equals(other.getAuthUser())) return false;
      }
      if (hasAuthUserCount() != other.hasAuthUserCount()) return false;
      if (hasAuthUserCount()) {
        if (getAuthUserCount()
            != other.getAuthUserCount()) return false;
      }
      if (hasBodyServerMd5Count() != other.hasBodyServerMd5Count()) return false;
      if (hasBodyServerMd5Count()) {
        if (getBodyServerMd5Count()
            != other.getBodyServerMd5Count()) return false;
      }
      if (hasContentDispositionClient() != other.hasContentDispositionClient()) return false;
      if (hasContentDispositionClient()) {
        if (!getContentDispositionClient()
            .equals(other.getContentDispositionClient())) return false;
      }
      if (hasContentDispositionServer() != other.hasContentDispositionServer()) return false;
      if (hasContentDispositionServer()) {
        if (!getContentDispositionServer()
            .equals(other.getContentDispositionServer())) return false;
      }
      if (hasFilePath() != other.hasFilePath()) return false;
      if (hasFilePath()) {
        if (!getFilePath()
            .equals(other.getFilePath())) return false;
      }
      if (hasSetCookie() != other.hasSetCookie()) return false;
      if (hasSetCookie()) {
        if (!getSetCookie()
            .equals(other.getSetCookie())) return false;
      }
      if (hasTitle() != other.hasTitle()) return false;
      if (hasTitle()) {
        if (!getTitle()
            .equals(other.getTitle())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHost()) {
        hash = (37 * hash) + HOST_FIELD_NUMBER;
        hash = (53 * hash) + getHost().hashCode();
      }
      if (hasUri()) {
        hash = (37 * hash) + URI_FIELD_NUMBER;
        hash = (53 * hash) + getUri().hashCode();
      }
      if (hasVarConEnc()) {
        hash = (37 * hash) + VARCONENC_FIELD_NUMBER;
        hash = (53 * hash) + getVarConEnc().hashCode();
      }
      if (hasAuthInfo()) {
        hash = (37 * hash) + AUTHINFO_FIELD_NUMBER;
        hash = (53 * hash) + getAuthInfo().hashCode();
      }
      if (hasConEncByCli()) {
        hash = (37 * hash) + CONENCBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getConEncByCli().hashCode();
      }
      if (hasConLan()) {
        hash = (37 * hash) + CONLAN_FIELD_NUMBER;
        hash = (53 * hash) + getConLan().hashCode();
      }
      if (hasConLenByCli()) {
        hash = (37 * hash) + CONLENBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getConLenByCli();
      }
      if (hasConURL()) {
        hash = (37 * hash) + CONURL_FIELD_NUMBER;
        hash = (53 * hash) + getConURL().hashCode();
      }
      if (hasConMD5()) {
        hash = (37 * hash) + CONMD5_FIELD_NUMBER;
        hash = (53 * hash) + getConMD5().hashCode();
      }
      if (hasConType()) {
        hash = (37 * hash) + CONTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getConType().hashCode();
      }
      if (hasCookie()) {
        hash = (37 * hash) + COOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getCookie().hashCode();
      }
      if (hasCookie2()) {
        hash = (37 * hash) + COOKIE2_FIELD_NUMBER;
        hash = (53 * hash) + getCookie2().hashCode();
      }
      if (hasDate()) {
        hash = (37 * hash) + DATE_FIELD_NUMBER;
        hash = (53 * hash) + getDate().hashCode();
      }
      if (hasFrom()) {
        hash = (37 * hash) + FROM_FIELD_NUMBER;
        hash = (53 * hash) + getFrom().hashCode();
      }
      if (hasLoc()) {
        hash = (37 * hash) + LOC_FIELD_NUMBER;
        hash = (53 * hash) + getLoc().hashCode();
      }
      if (hasProAuthen()) {
        hash = (37 * hash) + PROAUTHEN_FIELD_NUMBER;
        hash = (53 * hash) + getProAuthen().hashCode();
      }
      if (hasProAuthor()) {
        hash = (37 * hash) + PROAUTHOR_FIELD_NUMBER;
        hash = (53 * hash) + getProAuthor().hashCode();
      }
      if (hasRefURL()) {
        hash = (37 * hash) + REFURL_FIELD_NUMBER;
        hash = (53 * hash) + getRefURL().hashCode();
      }
      if (hasSrv()) {
        hash = (37 * hash) + SRV_FIELD_NUMBER;
        hash = (53 * hash) + getSrv().hashCode();
      }
      if (hasSrvCnt()) {
        hash = (37 * hash) + SRVCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCnt();
      }
      if (hasSetCookieKey()) {
        hash = (37 * hash) + SETCOOKIEKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSetCookieKey().hashCode();
      }
      if (hasSetCookieVal()) {
        hash = (37 * hash) + SETCOOKIEVAL_FIELD_NUMBER;
        hash = (53 * hash) + getSetCookieVal().hashCode();
      }
      if (hasTraEnc()) {
        hash = (37 * hash) + TRAENC_FIELD_NUMBER;
        hash = (53 * hash) + getTraEnc().hashCode();
      }
      if (hasUsrAge()) {
        hash = (37 * hash) + USRAGE_FIELD_NUMBER;
        hash = (53 * hash) + getUsrAge().hashCode();
      }
      if (hasVia()) {
        hash = (37 * hash) + VIA_FIELD_NUMBER;
        hash = (53 * hash) + getVia().hashCode();
      }
      if (hasXForFor()) {
        hash = (37 * hash) + XFORFOR_FIELD_NUMBER;
        hash = (53 * hash) + getXForFor().hashCode();
      }
      if (hasStatCode()) {
        hash = (37 * hash) + STATCODE_FIELD_NUMBER;
        hash = (53 * hash) + getStatCode();
      }
      if (hasMet()) {
        hash = (37 * hash) + MET_FIELD_NUMBER;
        hash = (53 * hash) + getMet().hashCode();
      }
      if (hasSrvAge()) {
        hash = (37 * hash) + SRVAGE_FIELD_NUMBER;
        hash = (53 * hash) + getSrvAge().hashCode();
      }
      if (hasProAuth()) {
        hash = (37 * hash) + PROAUTH_FIELD_NUMBER;
        hash = (53 * hash) + getProAuth().hashCode();
      }
      if (hasXPowBy()) {
        hash = (37 * hash) + XPOWBY_FIELD_NUMBER;
        hash = (53 * hash) + getXPowBy().hashCode();
      }
      if (hasExtHdrs()) {
        hash = (37 * hash) + EXTHDRS_FIELD_NUMBER;
        hash = (53 * hash) + getExtHdrs().hashCode();
      }
      if (hasRangeofCli()) {
        hash = (37 * hash) + RANGEOFCLI_FIELD_NUMBER;
        hash = (53 * hash) + getRangeofCli().hashCode();
      }
      if (hasViaCnt()) {
        hash = (37 * hash) + VIACNT_FIELD_NUMBER;
        hash = (53 * hash) + getViaCnt();
      }
      if (hasStatCodeCnt()) {
        hash = (37 * hash) + STATCODECNT_FIELD_NUMBER;
        hash = (53 * hash) + getStatCodeCnt();
      }
      if (hasReqVer()) {
        hash = (37 * hash) + REQVER_FIELD_NUMBER;
        hash = (53 * hash) + getReqVer().hashCode();
      }
      if (hasReqHead()) {
        hash = (37 * hash) + REQHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getReqHead().hashCode();
      }
      if (hasReqHeadMd5()) {
        hash = (37 * hash) + REQHEADMD5_FIELD_NUMBER;
        hash = (53 * hash) + getReqHeadMd5();
      }
      if (hasCacConUp()) {
        hash = (37 * hash) + CACCONUP_FIELD_NUMBER;
        hash = (53 * hash) + getCacConUp().hashCode();
      }
      if (hasConUp()) {
        hash = (37 * hash) + CONUP_FIELD_NUMBER;
        hash = (53 * hash) + getConUp().hashCode();
      }
      if (hasPraUp()) {
        hash = (37 * hash) + PRAUP_FIELD_NUMBER;
        hash = (53 * hash) + getPraUp().hashCode();
      }
      if (hasUpg()) {
        hash = (37 * hash) + UPG_FIELD_NUMBER;
        hash = (53 * hash) + getUpg().hashCode();
      }
      if (hasAccChaUp()) {
        hash = (37 * hash) + ACCCHAUP_FIELD_NUMBER;
        hash = (53 * hash) + getAccChaUp().hashCode();
      }
      if (hasAcctRanUp()) {
        hash = (37 * hash) + ACCTRANUP_FIELD_NUMBER;
        hash = (53 * hash) + getAcctRanUp().hashCode();
      }
      if (hasIfMat()) {
        hash = (37 * hash) + IFMAT_FIELD_NUMBER;
        hash = (53 * hash) + getIfMat().hashCode();
      }
      if (hasIfModSin()) {
        hash = (37 * hash) + IFMODSIN_FIELD_NUMBER;
        hash = (53 * hash) + getIfModSin().hashCode();
      }
      if (hasIfNonMat()) {
        hash = (37 * hash) + IFNONMAT_FIELD_NUMBER;
        hash = (53 * hash) + getIfNonMat().hashCode();
      }
      if (hasIfRan()) {
        hash = (37 * hash) + IFRAN_FIELD_NUMBER;
        hash = (53 * hash) + getIfRan().hashCode();
      }
      if (hasIfUnModSin()) {
        hash = (37 * hash) + IFUNMODSIN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIfUnModSin());
      }
      if (hasMaxFor()) {
        hash = (37 * hash) + MAXFOR_FIELD_NUMBER;
        hash = (53 * hash) + getMaxFor();
      }
      if (hasTe()) {
        hash = (37 * hash) + TE_FIELD_NUMBER;
        hash = (53 * hash) + getTe().hashCode();
      }
      if (hasCacConDown()) {
        hash = (37 * hash) + CACCONDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getCacConDown().hashCode();
      }
      if (hasConDown()) {
        hash = (37 * hash) + CONDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getConDown().hashCode();
      }
      if (hasPraDown()) {
        hash = (37 * hash) + PRADOWN_FIELD_NUMBER;
        hash = (53 * hash) + getPraDown().hashCode();
      }
      if (hasTrail()) {
        hash = (37 * hash) + TRAIL_FIELD_NUMBER;
        hash = (53 * hash) + getTrail().hashCode();
      }
      if (hasAccRanDown()) {
        hash = (37 * hash) + ACCRANDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getAccRanDown().hashCode();
      }
      if (hasETag()) {
        hash = (37 * hash) + ETAG_FIELD_NUMBER;
        hash = (53 * hash) + getETag().hashCode();
      }
      if (hasRetAft()) {
        hash = (37 * hash) + RETAFT_FIELD_NUMBER;
        hash = (53 * hash) + getRetAft().hashCode();
      }
      if (hasWwwAuth()) {
        hash = (37 * hash) + WWWAUTH_FIELD_NUMBER;
        hash = (53 * hash) + getWwwAuth().hashCode();
      }
      if (hasRefresh()) {
        hash = (37 * hash) + REFRESH_FIELD_NUMBER;
        hash = (53 * hash) + getRefresh().hashCode();
      }
      if (hasConTypDown()) {
        hash = (37 * hash) + CONTYPDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getConTypDown().hashCode();
      }
      if (hasAllow()) {
        hash = (37 * hash) + ALLOW_FIELD_NUMBER;
        hash = (53 * hash) + getAllow().hashCode();
      }
      if (hasExpires()) {
        hash = (37 * hash) + EXPIRES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getExpires());
      }
      if (hasLasMod()) {
        hash = (37 * hash) + LASMOD_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLasMod());
      }
      if (hasAccChaDown()) {
        hash = (37 * hash) + ACCCHADOWN_FIELD_NUMBER;
        hash = (53 * hash) + getAccChaDown().hashCode();
      }
      if (hasHttpRelKey()) {
        hash = (37 * hash) + HTTPRELKEY_FIELD_NUMBER;
        hash = (53 * hash) + getHttpRelKey().hashCode();
      }
      if (hasHttpEmbPro()) {
        hash = (37 * hash) + HTTPEMBPRO_FIELD_NUMBER;
        hash = (53 * hash) + getHttpEmbPro().hashCode();
      }
      if (hasFullTextHeader()) {
        hash = (37 * hash) + FULLTEXTHEADER_FIELD_NUMBER;
        hash = (53 * hash) + getFullTextHeader().hashCode();
      }
      if (hasFullTextLen()) {
        hash = (37 * hash) + FULLTEXTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getFullTextLen();
      }
      if (hasFileName()) {
        hash = (37 * hash) + FILENAME_FIELD_NUMBER;
        hash = (53 * hash) + getFileName().hashCode();
      }
      if (hasContDown()) {
        hash = (37 * hash) + CONTDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getContDown().hashCode();
      }
      if (hasReqVerCnt()) {
        hash = (37 * hash) + REQVERCNT_FIELD_NUMBER;
        hash = (53 * hash) + getReqVerCnt();
      }
      if (hasMetCnt()) {
        hash = (37 * hash) + METCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMetCnt();
      }
      if (hasReqHeadCnt()) {
        hash = (37 * hash) + REQHEADCNT_FIELD_NUMBER;
        hash = (53 * hash) + getReqHeadCnt();
      }
      if (hasAccByCli()) {
        hash = (37 * hash) + ACCBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getAccByCli().hashCode();
      }
      if (hasAccLanByCli()) {
        hash = (37 * hash) + ACCLANBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getAccLanByCli().hashCode();
      }
      if (hasAccEncByCli()) {
        hash = (37 * hash) + ACCENCBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getAccEncByCli().hashCode();
      }
      if (hasAuthCnt()) {
        hash = (37 * hash) + AUTHCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAuthCnt();
      }
      if (hasHostCnt()) {
        hash = (37 * hash) + HOSTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getHostCnt();
      }
      if (hasUriCnt()) {
        hash = (37 * hash) + URICNT_FIELD_NUMBER;
        hash = (53 * hash) + getUriCnt();
      }
      if (hasUriPath()) {
        hash = (37 * hash) + URIPATH_FIELD_NUMBER;
        hash = (53 * hash) + getUriPath().hashCode();
      }
      if (hasUriPathCnt()) {
        hash = (37 * hash) + URIPATHCNT_FIELD_NUMBER;
        hash = (53 * hash) + getUriPathCnt();
      }
      if (getUriKeyCount() > 0) {
        hash = (37 * hash) + URIKEY_FIELD_NUMBER;
        hash = (53 * hash) + getUriKeyList().hashCode();
      }
      if (hasUriKeyCnt()) {
        hash = (37 * hash) + URIKEYCNT_FIELD_NUMBER;
        hash = (53 * hash) + getUriKeyCnt();
      }
      if (hasUriSearch()) {
        hash = (37 * hash) + URISEARCH_FIELD_NUMBER;
        hash = (53 * hash) + getUriSearch().hashCode();
      }
      if (hasUsrAgeCnt()) {
        hash = (37 * hash) + USRAGECNT_FIELD_NUMBER;
        hash = (53 * hash) + getUsrAgeCnt();
      }
      if (hasUser()) {
        hash = (37 * hash) + USER_FIELD_NUMBER;
        hash = (53 * hash) + getUser().hashCode();
      }
      if (hasUserCnt()) {
        hash = (37 * hash) + USERCNT_FIELD_NUMBER;
        hash = (53 * hash) + getUserCnt();
      }
      if (hasReqBody()) {
        hash = (37 * hash) + REQBODY_FIELD_NUMBER;
        hash = (53 * hash) + getReqBody().hashCode();
      }
      if (hasReqBodyN()) {
        hash = (37 * hash) + REQBODYN_FIELD_NUMBER;
        hash = (53 * hash) + getReqBodyN().hashCode();
      }
      if (hasConMD5ByCli()) {
        hash = (37 * hash) + CONMD5BYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getConMD5ByCli().hashCode();
      }
      if (getCookieKeyCount() > 0) {
        hash = (37 * hash) + COOKIEKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCookieKeyList().hashCode();
      }
      if (hasCookieKeyCnt()) {
        hash = (37 * hash) + COOKIEKEYCNT_FIELD_NUMBER;
        hash = (53 * hash) + getCookieKeyCnt();
      }
      if (hasImei()) {
        hash = (37 * hash) + IMEI_FIELD_NUMBER;
        hash = (53 * hash) + getImei().hashCode();
      }
      if (hasImsi()) {
        hash = (37 * hash) + IMSI_FIELD_NUMBER;
        hash = (53 * hash) + getImsi().hashCode();
      }
      if (hasXForForCnt()) {
        hash = (37 * hash) + XFORFORCNT_FIELD_NUMBER;
        hash = (53 * hash) + getXForForCnt();
      }
      if (hasRespVer()) {
        hash = (37 * hash) + RESPVER_FIELD_NUMBER;
        hash = (53 * hash) + getRespVer().hashCode();
      }
      if (hasRespVerCnt()) {
        hash = (37 * hash) + RESPVERCNT_FIELD_NUMBER;
        hash = (53 * hash) + getRespVerCnt();
      }
      if (hasRespHead()) {
        hash = (37 * hash) + RESPHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getRespHead().hashCode();
      }
      if (hasRespHeadMd5()) {
        hash = (37 * hash) + RESPHEADMD5_FIELD_NUMBER;
        hash = (53 * hash) + getRespHeadMd5().hashCode();
      }
      if (hasRespHeadCnt()) {
        hash = (37 * hash) + RESPHEADCNT_FIELD_NUMBER;
        hash = (53 * hash) + getRespHeadCnt();
      }
      if (hasRespBody()) {
        hash = (37 * hash) + RESPBODY_FIELD_NUMBER;
        hash = (53 * hash) + getRespBody().hashCode();
      }
      if (hasRespBodyN()) {
        hash = (37 * hash) + RESPBODYN_FIELD_NUMBER;
        hash = (53 * hash) + getRespBodyN().hashCode();
      }
      if (hasConMD5BySrv()) {
        hash = (37 * hash) + CONMD5BYSRV_FIELD_NUMBER;
        hash = (53 * hash) + getConMD5BySrv().hashCode();
      }
      if (hasConEncBySrv()) {
        hash = (37 * hash) + CONENCBYSRV_FIELD_NUMBER;
        hash = (53 * hash) + getConEncBySrv();
      }
      if (hasLocation()) {
        hash = (37 * hash) + LOCATION_FIELD_NUMBER;
        hash = (53 * hash) + getLocation().hashCode();
      }
      if (hasXSinHol()) {
        hash = (37 * hash) + XSINHOL_FIELD_NUMBER;
        hash = (53 * hash) + getXSinHol().hashCode();
      }
      if (hasConEncBySrvCnt()) {
        hash = (37 * hash) + CONENCBYSRVCNT_FIELD_NUMBER;
        hash = (53 * hash) + getConEncBySrvCnt();
      }
      if (hasConLenSrv()) {
        hash = (37 * hash) + CONLENSRV_FIELD_NUMBER;
        hash = (53 * hash) + getConLenSrv();
      }
      if (hasConDispUp()) {
        hash = (37 * hash) + CONDISPUP_FIELD_NUMBER;
        hash = (53 * hash) + getConDispUp().hashCode();
      }
      if (hasConDispDown()) {
        hash = (37 * hash) + CONDISPDOWN_FIELD_NUMBER;
        hash = (53 * hash) + getConDispDown().hashCode();
      }
      if (hasAuthUser()) {
        hash = (37 * hash) + AUTHUSER_FIELD_NUMBER;
        hash = (53 * hash) + getAuthUser().hashCode();
      }
      if (hasAuthUserCount()) {
        hash = (37 * hash) + AUTHUSERCOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getAuthUserCount();
      }
      if (hasBodyServerMd5Count()) {
        hash = (37 * hash) + BODYSERVERMD5COUNT_FIELD_NUMBER;
        hash = (53 * hash) + getBodyServerMd5Count();
      }
      if (hasContentDispositionClient()) {
        hash = (37 * hash) + CONTENTDISPOSITIONCLIENT_FIELD_NUMBER;
        hash = (53 * hash) + getContentDispositionClient().hashCode();
      }
      if (hasContentDispositionServer()) {
        hash = (37 * hash) + CONTENTDISPOSITIONSERVER_FIELD_NUMBER;
        hash = (53 * hash) + getContentDispositionServer().hashCode();
      }
      if (hasFilePath()) {
        hash = (37 * hash) + FILEPATH_FIELD_NUMBER;
        hash = (53 * hash) + getFilePath().hashCode();
      }
      if (hasSetCookie()) {
        hash = (37 * hash) + SETCOOKIE_FIELD_NUMBER;
        hash = (53 * hash) + getSetCookie().hashCode();
      }
      if (hasTitle()) {
        hash = (37 * hash) + TITLE_FIELD_NUMBER;
        hash = (53 * hash) + getTitle().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static HttpInfoOuterClass.HttpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static HttpInfoOuterClass.HttpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static HttpInfoOuterClass.HttpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static HttpInfoOuterClass.HttpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(HttpInfoOuterClass.HttpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code HttpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:HttpInfo)
        HttpInfoOuterClass.HttpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return HttpInfoOuterClass.internal_static_HttpInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return HttpInfoOuterClass.internal_static_HttpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                HttpInfoOuterClass.HttpInfo.class, HttpInfoOuterClass.HttpInfo.Builder.class);
      }

      // Construct using HttpInfoOuterClass.HttpInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        bitField2_ = 0;
        bitField3_ = 0;
        host_ = com.google.protobuf.ByteString.EMPTY;
        uri_ = com.google.protobuf.ByteString.EMPTY;
        varConEnc_ = com.google.protobuf.ByteString.EMPTY;
        authInfo_ = com.google.protobuf.ByteString.EMPTY;
        conEncByCli_ = com.google.protobuf.ByteString.EMPTY;
        conLan_ = com.google.protobuf.ByteString.EMPTY;
        conLenByCli_ = 0;
        conURL_ = com.google.protobuf.ByteString.EMPTY;
        conMD5_ = com.google.protobuf.ByteString.EMPTY;
        conType_ = com.google.protobuf.ByteString.EMPTY;
        cookie_ = com.google.protobuf.ByteString.EMPTY;
        cookie2_ = com.google.protobuf.ByteString.EMPTY;
        date_ = com.google.protobuf.ByteString.EMPTY;
        from_ = com.google.protobuf.ByteString.EMPTY;
        loc_ = com.google.protobuf.ByteString.EMPTY;
        proAuthen_ = com.google.protobuf.ByteString.EMPTY;
        proAuthor_ = com.google.protobuf.ByteString.EMPTY;
        refURL_ = com.google.protobuf.ByteString.EMPTY;
        srv_ = com.google.protobuf.ByteString.EMPTY;
        srvCnt_ = 0;
        setCookieKey_ = com.google.protobuf.ByteString.EMPTY;
        setCookieVal_ = com.google.protobuf.ByteString.EMPTY;
        traEnc_ = com.google.protobuf.ByteString.EMPTY;
        usrAge_ = com.google.protobuf.ByteString.EMPTY;
        via_ = com.google.protobuf.ByteString.EMPTY;
        xForFor_ = com.google.protobuf.ByteString.EMPTY;
        statCode_ = 0;
        met_ = com.google.protobuf.ByteString.EMPTY;
        srvAge_ = com.google.protobuf.ByteString.EMPTY;
        proAuth_ = com.google.protobuf.ByteString.EMPTY;
        xPowBy_ = com.google.protobuf.ByteString.EMPTY;
        extHdrs_ = com.google.protobuf.ByteString.EMPTY;
        rangeofCli_ = com.google.protobuf.ByteString.EMPTY;
        viaCnt_ = 0;
        statCodeCnt_ = 0;
        reqVer_ = com.google.protobuf.ByteString.EMPTY;
        reqHead_ = com.google.protobuf.ByteString.EMPTY;
        reqHeadMd5_ = 0;
        cacConUp_ = com.google.protobuf.ByteString.EMPTY;
        conUp_ = com.google.protobuf.ByteString.EMPTY;
        praUp_ = com.google.protobuf.ByteString.EMPTY;
        upg_ = com.google.protobuf.ByteString.EMPTY;
        accChaUp_ = com.google.protobuf.ByteString.EMPTY;
        acctRanUp_ = com.google.protobuf.ByteString.EMPTY;
        ifMat_ = com.google.protobuf.ByteString.EMPTY;
        ifModSin_ = com.google.protobuf.ByteString.EMPTY;
        ifNonMat_ = com.google.protobuf.ByteString.EMPTY;
        ifRan_ = com.google.protobuf.ByteString.EMPTY;
        ifUnModSin_ = 0L;
        maxFor_ = 0;
        te_ = com.google.protobuf.ByteString.EMPTY;
        cacConDown_ = com.google.protobuf.ByteString.EMPTY;
        conDown_ = com.google.protobuf.ByteString.EMPTY;
        praDown_ = com.google.protobuf.ByteString.EMPTY;
        trail_ = com.google.protobuf.ByteString.EMPTY;
        accRanDown_ = com.google.protobuf.ByteString.EMPTY;
        eTag_ = com.google.protobuf.ByteString.EMPTY;
        retAft_ = com.google.protobuf.ByteString.EMPTY;
        wwwAuth_ = com.google.protobuf.ByteString.EMPTY;
        refresh_ = com.google.protobuf.ByteString.EMPTY;
        conTypDown_ = com.google.protobuf.ByteString.EMPTY;
        allow_ = com.google.protobuf.ByteString.EMPTY;
        expires_ = 0L;
        lasMod_ = 0L;
        accChaDown_ = com.google.protobuf.ByteString.EMPTY;
        httpRelKey_ = com.google.protobuf.ByteString.EMPTY;
        httpEmbPro_ = com.google.protobuf.ByteString.EMPTY;
        fullTextHeader_ = com.google.protobuf.ByteString.EMPTY;
        fullTextLen_ = 0;
        fileName_ = com.google.protobuf.ByteString.EMPTY;
        contDown_ = com.google.protobuf.ByteString.EMPTY;
        reqVerCnt_ = 0;
        metCnt_ = 0;
        reqHeadCnt_ = 0;
        accByCli_ = com.google.protobuf.ByteString.EMPTY;
        accLanByCli_ = com.google.protobuf.ByteString.EMPTY;
        accEncByCli_ = com.google.protobuf.ByteString.EMPTY;
        authCnt_ = 0;
        hostCnt_ = 0;
        uriCnt_ = 0;
        uriPath_ = com.google.protobuf.ByteString.EMPTY;
        uriPathCnt_ = 0;
        uriKey_ = emptyList(com.google.protobuf.ByteString.class);
        uriKeyCnt_ = 0;
        uriSearch_ = com.google.protobuf.ByteString.EMPTY;
        usrAgeCnt_ = 0;
        user_ = com.google.protobuf.ByteString.EMPTY;
        userCnt_ = 0;
        reqBody_ = com.google.protobuf.ByteString.EMPTY;
        reqBodyN_ = com.google.protobuf.ByteString.EMPTY;
        conMD5ByCli_ = com.google.protobuf.ByteString.EMPTY;
        cookieKey_ = emptyList(com.google.protobuf.ByteString.class);
        cookieKeyCnt_ = 0;
        imei_ = com.google.protobuf.ByteString.EMPTY;
        imsi_ = com.google.protobuf.ByteString.EMPTY;
        xForForCnt_ = 0;
        respVer_ = com.google.protobuf.ByteString.EMPTY;
        respVerCnt_ = 0;
        respHead_ = com.google.protobuf.ByteString.EMPTY;
        respHeadMd5_ = com.google.protobuf.ByteString.EMPTY;
        respHeadCnt_ = 0;
        respBody_ = com.google.protobuf.ByteString.EMPTY;
        respBodyN_ = com.google.protobuf.ByteString.EMPTY;
        conMD5BySrv_ = com.google.protobuf.ByteString.EMPTY;
        conEncBySrv_ = 0;
        location_ = com.google.protobuf.ByteString.EMPTY;
        xSinHol_ = com.google.protobuf.ByteString.EMPTY;
        conEncBySrvCnt_ = 0;
        conLenSrv_ = 0;
        conDispUp_ = com.google.protobuf.ByteString.EMPTY;
        conDispDown_ = com.google.protobuf.ByteString.EMPTY;
        authUser_ = com.google.protobuf.ByteString.EMPTY;
        authUserCount_ = 0;
        bodyServerMd5Count_ = 0;
        contentDispositionClient_ = com.google.protobuf.ByteString.EMPTY;
        contentDispositionServer_ = com.google.protobuf.ByteString.EMPTY;
        filePath_ = com.google.protobuf.ByteString.EMPTY;
        setCookie_ = com.google.protobuf.ByteString.EMPTY;
        title_ = com.google.protobuf.ByteString.EMPTY;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return HttpInfoOuterClass.internal_static_HttpInfo_descriptor;
      }

      @java.lang.Override
      public HttpInfoOuterClass.HttpInfo getDefaultInstanceForType() {
        return HttpInfoOuterClass.HttpInfo.getDefaultInstance();
      }

      @java.lang.Override
      public HttpInfoOuterClass.HttpInfo build() {
        HttpInfoOuterClass.HttpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public HttpInfoOuterClass.HttpInfo buildPartial() {
        HttpInfoOuterClass.HttpInfo result = new HttpInfoOuterClass.HttpInfo(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        if (bitField2_ != 0) { buildPartial2(result); }
        if (bitField3_ != 0) { buildPartial3(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(HttpInfoOuterClass.HttpInfo result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.host_ = host_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.uri_ = uri_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.varConEnc_ = varConEnc_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.authInfo_ = authInfo_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.conEncByCli_ = conEncByCli_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.conLan_ = conLan_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.conLenByCli_ = conLenByCli_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.conURL_ = conURL_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.conMD5_ = conMD5_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.conType_ = conType_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.cookie_ = cookie_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.cookie2_ = cookie2_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.date_ = date_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.from_ = from_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.loc_ = loc_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.proAuthen_ = proAuthen_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.proAuthor_ = proAuthor_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.refURL_ = refURL_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.srv_ = srv_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.srvCnt_ = srvCnt_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.setCookieKey_ = setCookieKey_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.setCookieVal_ = setCookieVal_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.traEnc_ = traEnc_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.usrAge_ = usrAge_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.via_ = via_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.xForFor_ = xForFor_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.statCode_ = statCode_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.met_ = met_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.srvAge_ = srvAge_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.proAuth_ = proAuth_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.xPowBy_ = xPowBy_;
          to_bitField0_ |= 0x40000000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.extHdrs_ = extHdrs_;
          to_bitField0_ |= 0x80000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(HttpInfoOuterClass.HttpInfo result) {
        int from_bitField1_ = bitField1_;
        int to_bitField1_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.rangeofCli_ = rangeofCli_;
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.viaCnt_ = viaCnt_;
          to_bitField1_ |= 0x00000002;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.statCodeCnt_ = statCodeCnt_;
          to_bitField1_ |= 0x00000004;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.reqVer_ = reqVer_;
          to_bitField1_ |= 0x00000008;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.reqHead_ = reqHead_;
          to_bitField1_ |= 0x00000010;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.reqHeadMd5_ = reqHeadMd5_;
          to_bitField1_ |= 0x00000020;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.cacConUp_ = cacConUp_;
          to_bitField1_ |= 0x00000040;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          result.conUp_ = conUp_;
          to_bitField1_ |= 0x00000080;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.praUp_ = praUp_;
          to_bitField1_ |= 0x00000100;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          result.upg_ = upg_;
          to_bitField1_ |= 0x00000200;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          result.accChaUp_ = accChaUp_;
          to_bitField1_ |= 0x00000400;
        }
        if (((from_bitField1_ & 0x00000800) != 0)) {
          result.acctRanUp_ = acctRanUp_;
          to_bitField1_ |= 0x00000800;
        }
        if (((from_bitField1_ & 0x00001000) != 0)) {
          result.ifMat_ = ifMat_;
          to_bitField1_ |= 0x00001000;
        }
        if (((from_bitField1_ & 0x00002000) != 0)) {
          result.ifModSin_ = ifModSin_;
          to_bitField1_ |= 0x00002000;
        }
        if (((from_bitField1_ & 0x00004000) != 0)) {
          result.ifNonMat_ = ifNonMat_;
          to_bitField1_ |= 0x00004000;
        }
        if (((from_bitField1_ & 0x00008000) != 0)) {
          result.ifRan_ = ifRan_;
          to_bitField1_ |= 0x00008000;
        }
        if (((from_bitField1_ & 0x00010000) != 0)) {
          result.ifUnModSin_ = ifUnModSin_;
          to_bitField1_ |= 0x00010000;
        }
        if (((from_bitField1_ & 0x00020000) != 0)) {
          result.maxFor_ = maxFor_;
          to_bitField1_ |= 0x00020000;
        }
        if (((from_bitField1_ & 0x00040000) != 0)) {
          result.te_ = te_;
          to_bitField1_ |= 0x00040000;
        }
        if (((from_bitField1_ & 0x00080000) != 0)) {
          result.cacConDown_ = cacConDown_;
          to_bitField1_ |= 0x00080000;
        }
        if (((from_bitField1_ & 0x00100000) != 0)) {
          result.conDown_ = conDown_;
          to_bitField1_ |= 0x00100000;
        }
        if (((from_bitField1_ & 0x00200000) != 0)) {
          result.praDown_ = praDown_;
          to_bitField1_ |= 0x00200000;
        }
        if (((from_bitField1_ & 0x00400000) != 0)) {
          result.trail_ = trail_;
          to_bitField1_ |= 0x00400000;
        }
        if (((from_bitField1_ & 0x00800000) != 0)) {
          result.accRanDown_ = accRanDown_;
          to_bitField1_ |= 0x00800000;
        }
        if (((from_bitField1_ & 0x01000000) != 0)) {
          result.eTag_ = eTag_;
          to_bitField1_ |= 0x01000000;
        }
        if (((from_bitField1_ & 0x02000000) != 0)) {
          result.retAft_ = retAft_;
          to_bitField1_ |= 0x02000000;
        }
        if (((from_bitField1_ & 0x04000000) != 0)) {
          result.wwwAuth_ = wwwAuth_;
          to_bitField1_ |= 0x04000000;
        }
        if (((from_bitField1_ & 0x08000000) != 0)) {
          result.refresh_ = refresh_;
          to_bitField1_ |= 0x08000000;
        }
        if (((from_bitField1_ & 0x10000000) != 0)) {
          result.conTypDown_ = conTypDown_;
          to_bitField1_ |= 0x10000000;
        }
        if (((from_bitField1_ & 0x20000000) != 0)) {
          result.allow_ = allow_;
          to_bitField1_ |= 0x20000000;
        }
        if (((from_bitField1_ & 0x40000000) != 0)) {
          result.expires_ = expires_;
          to_bitField1_ |= 0x40000000;
        }
        if (((from_bitField1_ & 0x80000000) != 0)) {
          result.lasMod_ = lasMod_;
          to_bitField1_ |= 0x80000000;
        }
        result.bitField1_ |= to_bitField1_;
      }

      private void buildPartial2(HttpInfoOuterClass.HttpInfo result) {
        int from_bitField2_ = bitField2_;
        int to_bitField2_ = 0;
        if (((from_bitField2_ & 0x00000001) != 0)) {
          result.accChaDown_ = accChaDown_;
          to_bitField2_ |= 0x00000001;
        }
        if (((from_bitField2_ & 0x00000002) != 0)) {
          result.httpRelKey_ = httpRelKey_;
          to_bitField2_ |= 0x00000002;
        }
        if (((from_bitField2_ & 0x00000004) != 0)) {
          result.httpEmbPro_ = httpEmbPro_;
          to_bitField2_ |= 0x00000004;
        }
        if (((from_bitField2_ & 0x00000008) != 0)) {
          result.fullTextHeader_ = fullTextHeader_;
          to_bitField2_ |= 0x00000008;
        }
        if (((from_bitField2_ & 0x00000010) != 0)) {
          result.fullTextLen_ = fullTextLen_;
          to_bitField2_ |= 0x00000010;
        }
        if (((from_bitField2_ & 0x00000020) != 0)) {
          result.fileName_ = fileName_;
          to_bitField2_ |= 0x00000020;
        }
        if (((from_bitField2_ & 0x00000040) != 0)) {
          result.contDown_ = contDown_;
          to_bitField2_ |= 0x00000040;
        }
        if (((from_bitField2_ & 0x00000080) != 0)) {
          result.reqVerCnt_ = reqVerCnt_;
          to_bitField2_ |= 0x00000080;
        }
        if (((from_bitField2_ & 0x00000100) != 0)) {
          result.metCnt_ = metCnt_;
          to_bitField2_ |= 0x00000100;
        }
        if (((from_bitField2_ & 0x00000200) != 0)) {
          result.reqHeadCnt_ = reqHeadCnt_;
          to_bitField2_ |= 0x00000200;
        }
        if (((from_bitField2_ & 0x00000400) != 0)) {
          result.accByCli_ = accByCli_;
          to_bitField2_ |= 0x00000400;
        }
        if (((from_bitField2_ & 0x00000800) != 0)) {
          result.accLanByCli_ = accLanByCli_;
          to_bitField2_ |= 0x00000800;
        }
        if (((from_bitField2_ & 0x00001000) != 0)) {
          result.accEncByCli_ = accEncByCli_;
          to_bitField2_ |= 0x00001000;
        }
        if (((from_bitField2_ & 0x00002000) != 0)) {
          result.authCnt_ = authCnt_;
          to_bitField2_ |= 0x00002000;
        }
        if (((from_bitField2_ & 0x00004000) != 0)) {
          result.hostCnt_ = hostCnt_;
          to_bitField2_ |= 0x00004000;
        }
        if (((from_bitField2_ & 0x00008000) != 0)) {
          result.uriCnt_ = uriCnt_;
          to_bitField2_ |= 0x00008000;
        }
        if (((from_bitField2_ & 0x00010000) != 0)) {
          result.uriPath_ = uriPath_;
          to_bitField2_ |= 0x00010000;
        }
        if (((from_bitField2_ & 0x00020000) != 0)) {
          result.uriPathCnt_ = uriPathCnt_;
          to_bitField2_ |= 0x00020000;
        }
        if (((from_bitField2_ & 0x00040000) != 0)) {
          uriKey_.makeImmutable();
          result.uriKey_ = uriKey_;
        }
        if (((from_bitField2_ & 0x00080000) != 0)) {
          result.uriKeyCnt_ = uriKeyCnt_;
          to_bitField2_ |= 0x00040000;
        }
        if (((from_bitField2_ & 0x00100000) != 0)) {
          result.uriSearch_ = uriSearch_;
          to_bitField2_ |= 0x00080000;
        }
        if (((from_bitField2_ & 0x00200000) != 0)) {
          result.usrAgeCnt_ = usrAgeCnt_;
          to_bitField2_ |= 0x00100000;
        }
        if (((from_bitField2_ & 0x00400000) != 0)) {
          result.user_ = user_;
          to_bitField2_ |= 0x00200000;
        }
        if (((from_bitField2_ & 0x00800000) != 0)) {
          result.userCnt_ = userCnt_;
          to_bitField2_ |= 0x00400000;
        }
        if (((from_bitField2_ & 0x01000000) != 0)) {
          result.reqBody_ = reqBody_;
          to_bitField2_ |= 0x00800000;
        }
        if (((from_bitField2_ & 0x02000000) != 0)) {
          result.reqBodyN_ = reqBodyN_;
          to_bitField2_ |= 0x01000000;
        }
        if (((from_bitField2_ & 0x04000000) != 0)) {
          result.conMD5ByCli_ = conMD5ByCli_;
          to_bitField2_ |= 0x02000000;
        }
        if (((from_bitField2_ & 0x08000000) != 0)) {
          cookieKey_.makeImmutable();
          result.cookieKey_ = cookieKey_;
        }
        if (((from_bitField2_ & 0x10000000) != 0)) {
          result.cookieKeyCnt_ = cookieKeyCnt_;
          to_bitField2_ |= 0x04000000;
        }
        if (((from_bitField2_ & 0x20000000) != 0)) {
          result.imei_ = imei_;
          to_bitField2_ |= 0x08000000;
        }
        if (((from_bitField2_ & 0x40000000) != 0)) {
          result.imsi_ = imsi_;
          to_bitField2_ |= 0x10000000;
        }
        if (((from_bitField2_ & 0x80000000) != 0)) {
          result.xForForCnt_ = xForForCnt_;
          to_bitField2_ |= 0x20000000;
        }
        result.bitField2_ |= to_bitField2_;
      }

      private void buildPartial3(HttpInfoOuterClass.HttpInfo result) {
        int from_bitField3_ = bitField3_;
        int to_bitField2_ = 0;
        if (((from_bitField3_ & 0x00000001) != 0)) {
          result.respVer_ = respVer_;
          to_bitField2_ |= 0x40000000;
        }
        if (((from_bitField3_ & 0x00000002) != 0)) {
          result.respVerCnt_ = respVerCnt_;
          to_bitField2_ |= 0x80000000;
        }
        int to_bitField3_ = 0;
        if (((from_bitField3_ & 0x00000004) != 0)) {
          result.respHead_ = respHead_;
          to_bitField3_ |= 0x00000001;
        }
        if (((from_bitField3_ & 0x00000008) != 0)) {
          result.respHeadMd5_ = respHeadMd5_;
          to_bitField3_ |= 0x00000002;
        }
        if (((from_bitField3_ & 0x00000010) != 0)) {
          result.respHeadCnt_ = respHeadCnt_;
          to_bitField3_ |= 0x00000004;
        }
        if (((from_bitField3_ & 0x00000020) != 0)) {
          result.respBody_ = respBody_;
          to_bitField3_ |= 0x00000008;
        }
        if (((from_bitField3_ & 0x00000040) != 0)) {
          result.respBodyN_ = respBodyN_;
          to_bitField3_ |= 0x00000010;
        }
        if (((from_bitField3_ & 0x00000080) != 0)) {
          result.conMD5BySrv_ = conMD5BySrv_;
          to_bitField3_ |= 0x00000020;
        }
        if (((from_bitField3_ & 0x00000100) != 0)) {
          result.conEncBySrv_ = conEncBySrv_;
          to_bitField3_ |= 0x00000040;
        }
        if (((from_bitField3_ & 0x00000200) != 0)) {
          result.location_ = location_;
          to_bitField3_ |= 0x00000080;
        }
        if (((from_bitField3_ & 0x00000400) != 0)) {
          result.xSinHol_ = xSinHol_;
          to_bitField3_ |= 0x00000100;
        }
        if (((from_bitField3_ & 0x00000800) != 0)) {
          result.conEncBySrvCnt_ = conEncBySrvCnt_;
          to_bitField3_ |= 0x00000200;
        }
        if (((from_bitField3_ & 0x00001000) != 0)) {
          result.conLenSrv_ = conLenSrv_;
          to_bitField3_ |= 0x00000400;
        }
        if (((from_bitField3_ & 0x00002000) != 0)) {
          result.conDispUp_ = conDispUp_;
          to_bitField3_ |= 0x00000800;
        }
        if (((from_bitField3_ & 0x00004000) != 0)) {
          result.conDispDown_ = conDispDown_;
          to_bitField3_ |= 0x00001000;
        }
        if (((from_bitField3_ & 0x00008000) != 0)) {
          result.authUser_ = authUser_;
          to_bitField3_ |= 0x00002000;
        }
        if (((from_bitField3_ & 0x00010000) != 0)) {
          result.authUserCount_ = authUserCount_;
          to_bitField3_ |= 0x00004000;
        }
        if (((from_bitField3_ & 0x00020000) != 0)) {
          result.bodyServerMd5Count_ = bodyServerMd5Count_;
          to_bitField3_ |= 0x00008000;
        }
        if (((from_bitField3_ & 0x00040000) != 0)) {
          result.contentDispositionClient_ = contentDispositionClient_;
          to_bitField3_ |= 0x00010000;
        }
        if (((from_bitField3_ & 0x00080000) != 0)) {
          result.contentDispositionServer_ = contentDispositionServer_;
          to_bitField3_ |= 0x00020000;
        }
        if (((from_bitField3_ & 0x00100000) != 0)) {
          result.filePath_ = filePath_;
          to_bitField3_ |= 0x00040000;
        }
        if (((from_bitField3_ & 0x00200000) != 0)) {
          result.setCookie_ = setCookie_;
          to_bitField3_ |= 0x00080000;
        }
        if (((from_bitField3_ & 0x00400000) != 0)) {
          result.title_ = title_;
          to_bitField3_ |= 0x00100000;
        }
        result.bitField2_ |= to_bitField2_;
        result.bitField3_ |= to_bitField3_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof HttpInfoOuterClass.HttpInfo) {
          return mergeFrom((HttpInfoOuterClass.HttpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(HttpInfoOuterClass.HttpInfo other) {
        if (other == HttpInfoOuterClass.HttpInfo.getDefaultInstance()) return this;
        if (other.hasHost()) {
          setHost(other.getHost());
        }
        if (other.hasUri()) {
          setUri(other.getUri());
        }
        if (other.hasVarConEnc()) {
          setVarConEnc(other.getVarConEnc());
        }
        if (other.hasAuthInfo()) {
          setAuthInfo(other.getAuthInfo());
        }
        if (other.hasConEncByCli()) {
          setConEncByCli(other.getConEncByCli());
        }
        if (other.hasConLan()) {
          setConLan(other.getConLan());
        }
        if (other.hasConLenByCli()) {
          setConLenByCli(other.getConLenByCli());
        }
        if (other.hasConURL()) {
          setConURL(other.getConURL());
        }
        if (other.hasConMD5()) {
          setConMD5(other.getConMD5());
        }
        if (other.hasConType()) {
          setConType(other.getConType());
        }
        if (other.hasCookie()) {
          setCookie(other.getCookie());
        }
        if (other.hasCookie2()) {
          setCookie2(other.getCookie2());
        }
        if (other.hasDate()) {
          setDate(other.getDate());
        }
        if (other.hasFrom()) {
          setFrom(other.getFrom());
        }
        if (other.hasLoc()) {
          setLoc(other.getLoc());
        }
        if (other.hasProAuthen()) {
          setProAuthen(other.getProAuthen());
        }
        if (other.hasProAuthor()) {
          setProAuthor(other.getProAuthor());
        }
        if (other.hasRefURL()) {
          setRefURL(other.getRefURL());
        }
        if (other.hasSrv()) {
          setSrv(other.getSrv());
        }
        if (other.hasSrvCnt()) {
          setSrvCnt(other.getSrvCnt());
        }
        if (other.hasSetCookieKey()) {
          setSetCookieKey(other.getSetCookieKey());
        }
        if (other.hasSetCookieVal()) {
          setSetCookieVal(other.getSetCookieVal());
        }
        if (other.hasTraEnc()) {
          setTraEnc(other.getTraEnc());
        }
        if (other.hasUsrAge()) {
          setUsrAge(other.getUsrAge());
        }
        if (other.hasVia()) {
          setVia(other.getVia());
        }
        if (other.hasXForFor()) {
          setXForFor(other.getXForFor());
        }
        if (other.hasStatCode()) {
          setStatCode(other.getStatCode());
        }
        if (other.hasMet()) {
          setMet(other.getMet());
        }
        if (other.hasSrvAge()) {
          setSrvAge(other.getSrvAge());
        }
        if (other.hasProAuth()) {
          setProAuth(other.getProAuth());
        }
        if (other.hasXPowBy()) {
          setXPowBy(other.getXPowBy());
        }
        if (other.hasExtHdrs()) {
          setExtHdrs(other.getExtHdrs());
        }
        if (other.hasRangeofCli()) {
          setRangeofCli(other.getRangeofCli());
        }
        if (other.hasViaCnt()) {
          setViaCnt(other.getViaCnt());
        }
        if (other.hasStatCodeCnt()) {
          setStatCodeCnt(other.getStatCodeCnt());
        }
        if (other.hasReqVer()) {
          setReqVer(other.getReqVer());
        }
        if (other.hasReqHead()) {
          setReqHead(other.getReqHead());
        }
        if (other.hasReqHeadMd5()) {
          setReqHeadMd5(other.getReqHeadMd5());
        }
        if (other.hasCacConUp()) {
          setCacConUp(other.getCacConUp());
        }
        if (other.hasConUp()) {
          setConUp(other.getConUp());
        }
        if (other.hasPraUp()) {
          setPraUp(other.getPraUp());
        }
        if (other.hasUpg()) {
          setUpg(other.getUpg());
        }
        if (other.hasAccChaUp()) {
          setAccChaUp(other.getAccChaUp());
        }
        if (other.hasAcctRanUp()) {
          setAcctRanUp(other.getAcctRanUp());
        }
        if (other.hasIfMat()) {
          setIfMat(other.getIfMat());
        }
        if (other.hasIfModSin()) {
          setIfModSin(other.getIfModSin());
        }
        if (other.hasIfNonMat()) {
          setIfNonMat(other.getIfNonMat());
        }
        if (other.hasIfRan()) {
          setIfRan(other.getIfRan());
        }
        if (other.hasIfUnModSin()) {
          setIfUnModSin(other.getIfUnModSin());
        }
        if (other.hasMaxFor()) {
          setMaxFor(other.getMaxFor());
        }
        if (other.hasTe()) {
          setTe(other.getTe());
        }
        if (other.hasCacConDown()) {
          setCacConDown(other.getCacConDown());
        }
        if (other.hasConDown()) {
          setConDown(other.getConDown());
        }
        if (other.hasPraDown()) {
          setPraDown(other.getPraDown());
        }
        if (other.hasTrail()) {
          setTrail(other.getTrail());
        }
        if (other.hasAccRanDown()) {
          setAccRanDown(other.getAccRanDown());
        }
        if (other.hasETag()) {
          setETag(other.getETag());
        }
        if (other.hasRetAft()) {
          setRetAft(other.getRetAft());
        }
        if (other.hasWwwAuth()) {
          setWwwAuth(other.getWwwAuth());
        }
        if (other.hasRefresh()) {
          setRefresh(other.getRefresh());
        }
        if (other.hasConTypDown()) {
          setConTypDown(other.getConTypDown());
        }
        if (other.hasAllow()) {
          setAllow(other.getAllow());
        }
        if (other.hasExpires()) {
          setExpires(other.getExpires());
        }
        if (other.hasLasMod()) {
          setLasMod(other.getLasMod());
        }
        if (other.hasAccChaDown()) {
          setAccChaDown(other.getAccChaDown());
        }
        if (other.hasHttpRelKey()) {
          setHttpRelKey(other.getHttpRelKey());
        }
        if (other.hasHttpEmbPro()) {
          setHttpEmbPro(other.getHttpEmbPro());
        }
        if (other.hasFullTextHeader()) {
          setFullTextHeader(other.getFullTextHeader());
        }
        if (other.hasFullTextLen()) {
          setFullTextLen(other.getFullTextLen());
        }
        if (other.hasFileName()) {
          setFileName(other.getFileName());
        }
        if (other.hasContDown()) {
          setContDown(other.getContDown());
        }
        if (other.hasReqVerCnt()) {
          setReqVerCnt(other.getReqVerCnt());
        }
        if (other.hasMetCnt()) {
          setMetCnt(other.getMetCnt());
        }
        if (other.hasReqHeadCnt()) {
          setReqHeadCnt(other.getReqHeadCnt());
        }
        if (other.hasAccByCli()) {
          setAccByCli(other.getAccByCli());
        }
        if (other.hasAccLanByCli()) {
          setAccLanByCli(other.getAccLanByCli());
        }
        if (other.hasAccEncByCli()) {
          setAccEncByCli(other.getAccEncByCli());
        }
        if (other.hasAuthCnt()) {
          setAuthCnt(other.getAuthCnt());
        }
        if (other.hasHostCnt()) {
          setHostCnt(other.getHostCnt());
        }
        if (other.hasUriCnt()) {
          setUriCnt(other.getUriCnt());
        }
        if (other.hasUriPath()) {
          setUriPath(other.getUriPath());
        }
        if (other.hasUriPathCnt()) {
          setUriPathCnt(other.getUriPathCnt());
        }
        if (!other.uriKey_.isEmpty()) {
          if (uriKey_.isEmpty()) {
            uriKey_ = other.uriKey_;
            uriKey_.makeImmutable();
            bitField2_ |= 0x00040000;
          } else {
            ensureUriKeyIsMutable();
            uriKey_.addAll(other.uriKey_);
          }
          onChanged();
        }
        if (other.hasUriKeyCnt()) {
          setUriKeyCnt(other.getUriKeyCnt());
        }
        if (other.hasUriSearch()) {
          setUriSearch(other.getUriSearch());
        }
        if (other.hasUsrAgeCnt()) {
          setUsrAgeCnt(other.getUsrAgeCnt());
        }
        if (other.hasUser()) {
          setUser(other.getUser());
        }
        if (other.hasUserCnt()) {
          setUserCnt(other.getUserCnt());
        }
        if (other.hasReqBody()) {
          setReqBody(other.getReqBody());
        }
        if (other.hasReqBodyN()) {
          setReqBodyN(other.getReqBodyN());
        }
        if (other.hasConMD5ByCli()) {
          setConMD5ByCli(other.getConMD5ByCli());
        }
        if (!other.cookieKey_.isEmpty()) {
          if (cookieKey_.isEmpty()) {
            cookieKey_ = other.cookieKey_;
            cookieKey_.makeImmutable();
            bitField2_ |= 0x08000000;
          } else {
            ensureCookieKeyIsMutable();
            cookieKey_.addAll(other.cookieKey_);
          }
          onChanged();
        }
        if (other.hasCookieKeyCnt()) {
          setCookieKeyCnt(other.getCookieKeyCnt());
        }
        if (other.hasImei()) {
          setImei(other.getImei());
        }
        if (other.hasImsi()) {
          setImsi(other.getImsi());
        }
        if (other.hasXForForCnt()) {
          setXForForCnt(other.getXForForCnt());
        }
        if (other.hasRespVer()) {
          setRespVer(other.getRespVer());
        }
        if (other.hasRespVerCnt()) {
          setRespVerCnt(other.getRespVerCnt());
        }
        if (other.hasRespHead()) {
          setRespHead(other.getRespHead());
        }
        if (other.hasRespHeadMd5()) {
          setRespHeadMd5(other.getRespHeadMd5());
        }
        if (other.hasRespHeadCnt()) {
          setRespHeadCnt(other.getRespHeadCnt());
        }
        if (other.hasRespBody()) {
          setRespBody(other.getRespBody());
        }
        if (other.hasRespBodyN()) {
          setRespBodyN(other.getRespBodyN());
        }
        if (other.hasConMD5BySrv()) {
          setConMD5BySrv(other.getConMD5BySrv());
        }
        if (other.hasConEncBySrv()) {
          setConEncBySrv(other.getConEncBySrv());
        }
        if (other.hasLocation()) {
          setLocation(other.getLocation());
        }
        if (other.hasXSinHol()) {
          setXSinHol(other.getXSinHol());
        }
        if (other.hasConEncBySrvCnt()) {
          setConEncBySrvCnt(other.getConEncBySrvCnt());
        }
        if (other.hasConLenSrv()) {
          setConLenSrv(other.getConLenSrv());
        }
        if (other.hasConDispUp()) {
          setConDispUp(other.getConDispUp());
        }
        if (other.hasConDispDown()) {
          setConDispDown(other.getConDispDown());
        }
        if (other.hasAuthUser()) {
          setAuthUser(other.getAuthUser());
        }
        if (other.hasAuthUserCount()) {
          setAuthUserCount(other.getAuthUserCount());
        }
        if (other.hasBodyServerMd5Count()) {
          setBodyServerMd5Count(other.getBodyServerMd5Count());
        }
        if (other.hasContentDispositionClient()) {
          setContentDispositionClient(other.getContentDispositionClient());
        }
        if (other.hasContentDispositionServer()) {
          setContentDispositionServer(other.getContentDispositionServer());
        }
        if (other.hasFilePath()) {
          setFilePath(other.getFilePath());
        }
        if (other.hasSetCookie()) {
          setSetCookie(other.getSetCookie());
        }
        if (other.hasTitle()) {
          setTitle(other.getTitle());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                host_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                uri_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                varConEnc_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                authInfo_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                conEncByCli_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                conLan_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 56: {
                conLenByCli_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 66: {
                conURL_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                conMD5_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                conType_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                cookie_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                cookie2_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                date_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                from_ = input.readBytes();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              case 122: {
                loc_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                proAuthen_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                proAuthor_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                refURL_ = input.readBytes();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                srv_ = input.readBytes();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 160: {
                srvCnt_ = input.readUInt32();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              case 170: {
                setCookieKey_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 178: {
                setCookieVal_ = input.readBytes();
                bitField0_ |= 0x00200000;
                break;
              } // case 178
              case 186: {
                traEnc_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                usrAge_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                via_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                xForFor_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 216: {
                statCode_ = input.readUInt32();
                bitField0_ |= 0x04000000;
                break;
              } // case 216
              case 226: {
                met_ = input.readBytes();
                bitField0_ |= 0x08000000;
                break;
              } // case 226
              case 234: {
                srvAge_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              case 242: {
                proAuth_ = input.readBytes();
                bitField0_ |= 0x20000000;
                break;
              } // case 242
              case 250: {
                xPowBy_ = input.readBytes();
                bitField0_ |= 0x40000000;
                break;
              } // case 250
              case 258: {
                extHdrs_ = input.readBytes();
                bitField0_ |= 0x80000000;
                break;
              } // case 258
              case 266: {
                rangeofCli_ = input.readBytes();
                bitField1_ |= 0x00000001;
                break;
              } // case 266
              case 272: {
                viaCnt_ = input.readUInt32();
                bitField1_ |= 0x00000002;
                break;
              } // case 272
              case 280: {
                statCodeCnt_ = input.readUInt32();
                bitField1_ |= 0x00000004;
                break;
              } // case 280
              case 290: {
                reqVer_ = input.readBytes();
                bitField1_ |= 0x00000008;
                break;
              } // case 290
              case 298: {
                reqHead_ = input.readBytes();
                bitField1_ |= 0x00000010;
                break;
              } // case 298
              case 304: {
                reqHeadMd5_ = input.readUInt32();
                bitField1_ |= 0x00000020;
                break;
              } // case 304
              case 314: {
                cacConUp_ = input.readBytes();
                bitField1_ |= 0x00000040;
                break;
              } // case 314
              case 322: {
                conUp_ = input.readBytes();
                bitField1_ |= 0x00000080;
                break;
              } // case 322
              case 330: {
                praUp_ = input.readBytes();
                bitField1_ |= 0x00000100;
                break;
              } // case 330
              case 338: {
                upg_ = input.readBytes();
                bitField1_ |= 0x00000200;
                break;
              } // case 338
              case 346: {
                accChaUp_ = input.readBytes();
                bitField1_ |= 0x00000400;
                break;
              } // case 346
              case 354: {
                acctRanUp_ = input.readBytes();
                bitField1_ |= 0x00000800;
                break;
              } // case 354
              case 362: {
                ifMat_ = input.readBytes();
                bitField1_ |= 0x00001000;
                break;
              } // case 362
              case 370: {
                ifModSin_ = input.readBytes();
                bitField1_ |= 0x00002000;
                break;
              } // case 370
              case 378: {
                ifNonMat_ = input.readBytes();
                bitField1_ |= 0x00004000;
                break;
              } // case 378
              case 386: {
                ifRan_ = input.readBytes();
                bitField1_ |= 0x00008000;
                break;
              } // case 386
              case 392: {
                ifUnModSin_ = input.readUInt64();
                bitField1_ |= 0x00010000;
                break;
              } // case 392
              case 400: {
                maxFor_ = input.readUInt32();
                bitField1_ |= 0x00020000;
                break;
              } // case 400
              case 410: {
                te_ = input.readBytes();
                bitField1_ |= 0x00040000;
                break;
              } // case 410
              case 418: {
                cacConDown_ = input.readBytes();
                bitField1_ |= 0x00080000;
                break;
              } // case 418
              case 426: {
                conDown_ = input.readBytes();
                bitField1_ |= 0x00100000;
                break;
              } // case 426
              case 434: {
                praDown_ = input.readBytes();
                bitField1_ |= 0x00200000;
                break;
              } // case 434
              case 442: {
                trail_ = input.readBytes();
                bitField1_ |= 0x00400000;
                break;
              } // case 442
              case 450: {
                accRanDown_ = input.readBytes();
                bitField1_ |= 0x00800000;
                break;
              } // case 450
              case 458: {
                eTag_ = input.readBytes();
                bitField1_ |= 0x01000000;
                break;
              } // case 458
              case 466: {
                retAft_ = input.readBytes();
                bitField1_ |= 0x02000000;
                break;
              } // case 466
              case 474: {
                wwwAuth_ = input.readBytes();
                bitField1_ |= 0x04000000;
                break;
              } // case 474
              case 482: {
                refresh_ = input.readBytes();
                bitField1_ |= 0x08000000;
                break;
              } // case 482
              case 490: {
                conTypDown_ = input.readBytes();
                bitField1_ |= 0x10000000;
                break;
              } // case 490
              case 498: {
                allow_ = input.readBytes();
                bitField1_ |= 0x20000000;
                break;
              } // case 498
              case 504: {
                expires_ = input.readUInt64();
                bitField1_ |= 0x40000000;
                break;
              } // case 504
              case 512: {
                lasMod_ = input.readUInt64();
                bitField1_ |= 0x80000000;
                break;
              } // case 512
              case 522: {
                accChaDown_ = input.readBytes();
                bitField2_ |= 0x00000001;
                break;
              } // case 522
              case 530: {
                httpRelKey_ = input.readBytes();
                bitField2_ |= 0x00000002;
                break;
              } // case 530
              case 538: {
                httpEmbPro_ = input.readBytes();
                bitField2_ |= 0x00000004;
                break;
              } // case 538
              case 546: {
                fullTextHeader_ = input.readBytes();
                bitField2_ |= 0x00000008;
                break;
              } // case 546
              case 552: {
                fullTextLen_ = input.readUInt32();
                bitField2_ |= 0x00000010;
                break;
              } // case 552
              case 562: {
                fileName_ = input.readBytes();
                bitField2_ |= 0x00000020;
                break;
              } // case 562
              case 570: {
                contDown_ = input.readBytes();
                bitField2_ |= 0x00000040;
                break;
              } // case 570
              case 576: {
                reqVerCnt_ = input.readUInt32();
                bitField2_ |= 0x00000080;
                break;
              } // case 576
              case 584: {
                metCnt_ = input.readUInt32();
                bitField2_ |= 0x00000100;
                break;
              } // case 584
              case 592: {
                reqHeadCnt_ = input.readUInt32();
                bitField2_ |= 0x00000200;
                break;
              } // case 592
              case 602: {
                accByCli_ = input.readBytes();
                bitField2_ |= 0x00000400;
                break;
              } // case 602
              case 610: {
                accLanByCli_ = input.readBytes();
                bitField2_ |= 0x00000800;
                break;
              } // case 610
              case 618: {
                accEncByCli_ = input.readBytes();
                bitField2_ |= 0x00001000;
                break;
              } // case 618
              case 624: {
                authCnt_ = input.readUInt32();
                bitField2_ |= 0x00002000;
                break;
              } // case 624
              case 632: {
                hostCnt_ = input.readUInt32();
                bitField2_ |= 0x00004000;
                break;
              } // case 632
              case 640: {
                uriCnt_ = input.readUInt32();
                bitField2_ |= 0x00008000;
                break;
              } // case 640
              case 650: {
                uriPath_ = input.readBytes();
                bitField2_ |= 0x00010000;
                break;
              } // case 650
              case 656: {
                uriPathCnt_ = input.readUInt32();
                bitField2_ |= 0x00020000;
                break;
              } // case 656
              case 666: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureUriKeyIsMutable();
                uriKey_.add(v);
                break;
              } // case 666
              case 672: {
                uriKeyCnt_ = input.readUInt32();
                bitField2_ |= 0x00080000;
                break;
              } // case 672
              case 682: {
                uriSearch_ = input.readBytes();
                bitField2_ |= 0x00100000;
                break;
              } // case 682
              case 688: {
                usrAgeCnt_ = input.readUInt32();
                bitField2_ |= 0x00200000;
                break;
              } // case 688
              case 698: {
                user_ = input.readBytes();
                bitField2_ |= 0x00400000;
                break;
              } // case 698
              case 704: {
                userCnt_ = input.readUInt32();
                bitField2_ |= 0x00800000;
                break;
              } // case 704
              case 714: {
                reqBody_ = input.readBytes();
                bitField2_ |= 0x01000000;
                break;
              } // case 714
              case 722: {
                reqBodyN_ = input.readBytes();
                bitField2_ |= 0x02000000;
                break;
              } // case 722
              case 730: {
                conMD5ByCli_ = input.readBytes();
                bitField2_ |= 0x04000000;
                break;
              } // case 730
              case 738: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureCookieKeyIsMutable();
                cookieKey_.add(v);
                break;
              } // case 738
              case 744: {
                cookieKeyCnt_ = input.readUInt32();
                bitField2_ |= 0x10000000;
                break;
              } // case 744
              case 754: {
                imei_ = input.readBytes();
                bitField2_ |= 0x20000000;
                break;
              } // case 754
              case 762: {
                imsi_ = input.readBytes();
                bitField2_ |= 0x40000000;
                break;
              } // case 762
              case 768: {
                xForForCnt_ = input.readUInt32();
                bitField2_ |= 0x80000000;
                break;
              } // case 768
              case 778: {
                respVer_ = input.readBytes();
                bitField3_ |= 0x00000001;
                break;
              } // case 778
              case 784: {
                respVerCnt_ = input.readUInt32();
                bitField3_ |= 0x00000002;
                break;
              } // case 784
              case 794: {
                respHead_ = input.readBytes();
                bitField3_ |= 0x00000004;
                break;
              } // case 794
              case 802: {
                respHeadMd5_ = input.readBytes();
                bitField3_ |= 0x00000008;
                break;
              } // case 802
              case 808: {
                respHeadCnt_ = input.readUInt32();
                bitField3_ |= 0x00000010;
                break;
              } // case 808
              case 818: {
                respBody_ = input.readBytes();
                bitField3_ |= 0x00000020;
                break;
              } // case 818
              case 826: {
                respBodyN_ = input.readBytes();
                bitField3_ |= 0x00000040;
                break;
              } // case 826
              case 834: {
                conMD5BySrv_ = input.readBytes();
                bitField3_ |= 0x00000080;
                break;
              } // case 834
              case 840: {
                conEncBySrv_ = input.readUInt32();
                bitField3_ |= 0x00000100;
                break;
              } // case 840
              case 850: {
                location_ = input.readBytes();
                bitField3_ |= 0x00000200;
                break;
              } // case 850
              case 858: {
                xSinHol_ = input.readBytes();
                bitField3_ |= 0x00000400;
                break;
              } // case 858
              case 864: {
                conEncBySrvCnt_ = input.readUInt32();
                bitField3_ |= 0x00000800;
                break;
              } // case 864
              case 872: {
                conLenSrv_ = input.readUInt32();
                bitField3_ |= 0x00001000;
                break;
              } // case 872
              case 882: {
                conDispUp_ = input.readBytes();
                bitField3_ |= 0x00002000;
                break;
              } // case 882
              case 890: {
                conDispDown_ = input.readBytes();
                bitField3_ |= 0x00004000;
                break;
              } // case 890
              case 898: {
                authUser_ = input.readBytes();
                bitField3_ |= 0x00008000;
                break;
              } // case 898
              case 904: {
                authUserCount_ = input.readUInt32();
                bitField3_ |= 0x00010000;
                break;
              } // case 904
              case 912: {
                bodyServerMd5Count_ = input.readUInt32();
                bitField3_ |= 0x00020000;
                break;
              } // case 912
              case 922: {
                contentDispositionClient_ = input.readBytes();
                bitField3_ |= 0x00040000;
                break;
              } // case 922
              case 930: {
                contentDispositionServer_ = input.readBytes();
                bitField3_ |= 0x00080000;
                break;
              } // case 930
              case 938: {
                filePath_ = input.readBytes();
                bitField3_ |= 0x00100000;
                break;
              } // case 938
              case 946: {
                setCookie_ = input.readBytes();
                bitField3_ |= 0x00200000;
                break;
              } // case 946
              case 954: {
                title_ = input.readBytes();
                bitField3_ |= 0x00400000;
                break;
              } // case 954
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;
      private int bitField3_;

      private com.google.protobuf.ByteString host_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes host = 1;</code>
       * @return Whether the host field is set.
       */
      @java.lang.Override
      public boolean hasHost() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes host = 1;</code>
       * @return The host.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getHost() {
        return host_;
      }
      /**
       * <code>optional bytes host = 1;</code>
       * @param value The host to set.
       * @return This builder for chaining.
       */
      public Builder setHost(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        host_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes host = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHost() {
        bitField0_ = (bitField0_ & ~0x00000001);
        host_ = getDefaultInstance().getHost();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString uri_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes uri = 2;</code>
       * @return Whether the uri field is set.
       */
      @java.lang.Override
      public boolean hasUri() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes uri = 2;</code>
       * @return The uri.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUri() {
        return uri_;
      }
      /**
       * <code>optional bytes uri = 2;</code>
       * @param value The uri to set.
       * @return This builder for chaining.
       */
      public Builder setUri(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        uri_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes uri = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUri() {
        bitField0_ = (bitField0_ & ~0x00000002);
        uri_ = getDefaultInstance().getUri();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString varConEnc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes varConEnc = 3;</code>
       * @return Whether the varConEnc field is set.
       */
      @java.lang.Override
      public boolean hasVarConEnc() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes varConEnc = 3;</code>
       * @return The varConEnc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getVarConEnc() {
        return varConEnc_;
      }
      /**
       * <code>optional bytes varConEnc = 3;</code>
       * @param value The varConEnc to set.
       * @return This builder for chaining.
       */
      public Builder setVarConEnc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        varConEnc_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes varConEnc = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearVarConEnc() {
        bitField0_ = (bitField0_ & ~0x00000004);
        varConEnc_ = getDefaultInstance().getVarConEnc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authInfo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authInfo = 4;</code>
       * @return Whether the authInfo field is set.
       */
      @java.lang.Override
      public boolean hasAuthInfo() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes authInfo = 4;</code>
       * @return The authInfo.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAuthInfo() {
        return authInfo_;
      }
      /**
       * <code>optional bytes authInfo = 4;</code>
       * @param value The authInfo to set.
       * @return This builder for chaining.
       */
      public Builder setAuthInfo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        authInfo_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authInfo = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthInfo() {
        bitField0_ = (bitField0_ & ~0x00000008);
        authInfo_ = getDefaultInstance().getAuthInfo();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       * @return Whether the conEncByCli field is set.
       */
      @java.lang.Override
      public boolean hasConEncByCli() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       * @return The conEncByCli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConEncByCli() {
        return conEncByCli_;
      }
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       * @param value The conEncByCli to set.
       * @return This builder for chaining.
       */
      public Builder setConEncByCli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conEncByCli_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conEncByCli = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearConEncByCli() {
        bitField0_ = (bitField0_ & ~0x00000010);
        conEncByCli_ = getDefaultInstance().getConEncByCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conLan_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conLan = 6;</code>
       * @return Whether the conLan field is set.
       */
      @java.lang.Override
      public boolean hasConLan() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes conLan = 6;</code>
       * @return The conLan.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConLan() {
        return conLan_;
      }
      /**
       * <code>optional bytes conLan = 6;</code>
       * @param value The conLan to set.
       * @return This builder for chaining.
       */
      public Builder setConLan(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conLan_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conLan = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearConLan() {
        bitField0_ = (bitField0_ & ~0x00000020);
        conLan_ = getDefaultInstance().getConLan();
        onChanged();
        return this;
      }

      private int conLenByCli_ ;
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       * @return Whether the conLenByCli field is set.
       */
      @java.lang.Override
      public boolean hasConLenByCli() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       * @return The conLenByCli.
       */
      @java.lang.Override
      public int getConLenByCli() {
        return conLenByCli_;
      }
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       * @param value The conLenByCli to set.
       * @return This builder for chaining.
       */
      public Builder setConLenByCli(int value) {

        conLenByCli_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conLenByCli = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearConLenByCli() {
        bitField0_ = (bitField0_ & ~0x00000040);
        conLenByCli_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conURL_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conURL = 8;</code>
       * @return Whether the conURL field is set.
       */
      @java.lang.Override
      public boolean hasConURL() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes conURL = 8;</code>
       * @return The conURL.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConURL() {
        return conURL_;
      }
      /**
       * <code>optional bytes conURL = 8;</code>
       * @param value The conURL to set.
       * @return This builder for chaining.
       */
      public Builder setConURL(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conURL_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conURL = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearConURL() {
        bitField0_ = (bitField0_ & ~0x00000080);
        conURL_ = getDefaultInstance().getConURL();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conMD5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conMD5 = 9;</code>
       * @return Whether the conMD5 field is set.
       */
      @java.lang.Override
      public boolean hasConMD5() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes conMD5 = 9;</code>
       * @return The conMD5.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConMD5() {
        return conMD5_;
      }
      /**
       * <code>optional bytes conMD5 = 9;</code>
       * @param value The conMD5 to set.
       * @return This builder for chaining.
       */
      public Builder setConMD5(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conMD5_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conMD5 = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearConMD5() {
        bitField0_ = (bitField0_ & ~0x00000100);
        conMD5_ = getDefaultInstance().getConMD5();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conType = 10;</code>
       * @return Whether the conType field is set.
       */
      @java.lang.Override
      public boolean hasConType() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes conType = 10;</code>
       * @return The conType.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConType() {
        return conType_;
      }
      /**
       * <code>optional bytes conType = 10;</code>
       * @param value The conType to set.
       * @return This builder for chaining.
       */
      public Builder setConType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conType_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conType = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearConType() {
        bitField0_ = (bitField0_ & ~0x00000200);
        conType_ = getDefaultInstance().getConType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cookie = 11;</code>
       * @return Whether the cookie field is set.
       */
      @java.lang.Override
      public boolean hasCookie() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes cookie = 11;</code>
       * @return The cookie.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCookie() {
        return cookie_;
      }
      /**
       * <code>optional bytes cookie = 11;</code>
       * @param value The cookie to set.
       * @return This builder for chaining.
       */
      public Builder setCookie(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cookie_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cookie = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearCookie() {
        bitField0_ = (bitField0_ & ~0x00000400);
        cookie_ = getDefaultInstance().getCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cookie2_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cookie2 = 12;</code>
       * @return Whether the cookie2 field is set.
       */
      @java.lang.Override
      public boolean hasCookie2() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes cookie2 = 12;</code>
       * @return The cookie2.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCookie2() {
        return cookie2_;
      }
      /**
       * <code>optional bytes cookie2 = 12;</code>
       * @param value The cookie2 to set.
       * @return This builder for chaining.
       */
      public Builder setCookie2(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cookie2_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cookie2 = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearCookie2() {
        bitField0_ = (bitField0_ & ~0x00000800);
        cookie2_ = getDefaultInstance().getCookie2();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString date_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes date = 13;</code>
       * @return Whether the date field is set.
       */
      @java.lang.Override
      public boolean hasDate() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes date = 13;</code>
       * @return The date.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDate() {
        return date_;
      }
      /**
       * <code>optional bytes date = 13;</code>
       * @param value The date to set.
       * @return This builder for chaining.
       */
      public Builder setDate(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        date_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes date = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearDate() {
        bitField0_ = (bitField0_ & ~0x00001000);
        date_ = getDefaultInstance().getDate();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString from_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes from = 14;</code>
       * @return Whether the from field is set.
       */
      @java.lang.Override
      public boolean hasFrom() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes from = 14;</code>
       * @return The from.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFrom() {
        return from_;
      }
      /**
       * <code>optional bytes from = 14;</code>
       * @param value The from to set.
       * @return This builder for chaining.
       */
      public Builder setFrom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        from_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes from = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearFrom() {
        bitField0_ = (bitField0_ & ~0x00002000);
        from_ = getDefaultInstance().getFrom();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString loc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes loc = 15;</code>
       * @return Whether the loc field is set.
       */
      @java.lang.Override
      public boolean hasLoc() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes loc = 15;</code>
       * @return The loc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getLoc() {
        return loc_;
      }
      /**
       * <code>optional bytes loc = 15;</code>
       * @param value The loc to set.
       * @return This builder for chaining.
       */
      public Builder setLoc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        loc_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes loc = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoc() {
        bitField0_ = (bitField0_ & ~0x00004000);
        loc_ = getDefaultInstance().getLoc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString proAuthen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes proAuthen = 16;</code>
       * @return Whether the proAuthen field is set.
       */
      @java.lang.Override
      public boolean hasProAuthen() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes proAuthen = 16;</code>
       * @return The proAuthen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getProAuthen() {
        return proAuthen_;
      }
      /**
       * <code>optional bytes proAuthen = 16;</code>
       * @param value The proAuthen to set.
       * @return This builder for chaining.
       */
      public Builder setProAuthen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        proAuthen_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes proAuthen = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearProAuthen() {
        bitField0_ = (bitField0_ & ~0x00008000);
        proAuthen_ = getDefaultInstance().getProAuthen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString proAuthor_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes proAuthor = 17;</code>
       * @return Whether the proAuthor field is set.
       */
      @java.lang.Override
      public boolean hasProAuthor() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes proAuthor = 17;</code>
       * @return The proAuthor.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getProAuthor() {
        return proAuthor_;
      }
      /**
       * <code>optional bytes proAuthor = 17;</code>
       * @param value The proAuthor to set.
       * @return This builder for chaining.
       */
      public Builder setProAuthor(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        proAuthor_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes proAuthor = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearProAuthor() {
        bitField0_ = (bitField0_ & ~0x00010000);
        proAuthor_ = getDefaultInstance().getProAuthor();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString refURL_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes refURL = 18;</code>
       * @return Whether the refURL field is set.
       */
      @java.lang.Override
      public boolean hasRefURL() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes refURL = 18;</code>
       * @return The refURL.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRefURL() {
        return refURL_;
      }
      /**
       * <code>optional bytes refURL = 18;</code>
       * @param value The refURL to set.
       * @return This builder for chaining.
       */
      public Builder setRefURL(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        refURL_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes refURL = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefURL() {
        bitField0_ = (bitField0_ & ~0x00020000);
        refURL_ = getDefaultInstance().getRefURL();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srv_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srv = 19;</code>
       * @return Whether the srv field is set.
       */
      @java.lang.Override
      public boolean hasSrv() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes srv = 19;</code>
       * @return The srv.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrv() {
        return srv_;
      }
      /**
       * <code>optional bytes srv = 19;</code>
       * @param value The srv to set.
       * @return This builder for chaining.
       */
      public Builder setSrv(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srv_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srv = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrv() {
        bitField0_ = (bitField0_ & ~0x00040000);
        srv_ = getDefaultInstance().getSrv();
        onChanged();
        return this;
      }

      private int srvCnt_ ;
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       * @return Whether the srvCnt field is set.
       */
      @java.lang.Override
      public boolean hasSrvCnt() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       * @return The srvCnt.
       */
      @java.lang.Override
      public int getSrvCnt() {
        return srvCnt_;
      }
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       * @param value The srvCnt to set.
       * @return This builder for chaining.
       */
      public Builder setSrvCnt(int value) {

        srvCnt_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCnt = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvCnt() {
        bitField0_ = (bitField0_ & ~0x00080000);
        srvCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString setCookieKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       * @return Whether the setCookieKey field is set.
       */
      @java.lang.Override
      public boolean hasSetCookieKey() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       * @return The setCookieKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSetCookieKey() {
        return setCookieKey_;
      }
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       * @param value The setCookieKey to set.
       * @return This builder for chaining.
       */
      public Builder setSetCookieKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        setCookieKey_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes setCookieKey = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearSetCookieKey() {
        bitField0_ = (bitField0_ & ~0x00100000);
        setCookieKey_ = getDefaultInstance().getSetCookieKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString setCookieVal_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       * @return Whether the setCookieVal field is set.
       */
      @java.lang.Override
      public boolean hasSetCookieVal() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       * @return The setCookieVal.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSetCookieVal() {
        return setCookieVal_;
      }
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       * @param value The setCookieVal to set.
       * @return This builder for chaining.
       */
      public Builder setSetCookieVal(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        setCookieVal_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes setCookieVal = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearSetCookieVal() {
        bitField0_ = (bitField0_ & ~0x00200000);
        setCookieVal_ = getDefaultInstance().getSetCookieVal();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString traEnc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes traEnc = 23;</code>
       * @return Whether the traEnc field is set.
       */
      @java.lang.Override
      public boolean hasTraEnc() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes traEnc = 23;</code>
       * @return The traEnc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTraEnc() {
        return traEnc_;
      }
      /**
       * <code>optional bytes traEnc = 23;</code>
       * @param value The traEnc to set.
       * @return This builder for chaining.
       */
      public Builder setTraEnc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        traEnc_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes traEnc = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearTraEnc() {
        bitField0_ = (bitField0_ & ~0x00400000);
        traEnc_ = getDefaultInstance().getTraEnc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString usrAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes usrAge = 24;</code>
       * @return Whether the usrAge field is set.
       */
      @java.lang.Override
      public boolean hasUsrAge() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes usrAge = 24;</code>
       * @return The usrAge.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUsrAge() {
        return usrAge_;
      }
      /**
       * <code>optional bytes usrAge = 24;</code>
       * @param value The usrAge to set.
       * @return This builder for chaining.
       */
      public Builder setUsrAge(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        usrAge_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes usrAge = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsrAge() {
        bitField0_ = (bitField0_ & ~0x00800000);
        usrAge_ = getDefaultInstance().getUsrAge();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString via_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes via = 25;</code>
       * @return Whether the via field is set.
       */
      @java.lang.Override
      public boolean hasVia() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes via = 25;</code>
       * @return The via.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getVia() {
        return via_;
      }
      /**
       * <code>optional bytes via = 25;</code>
       * @param value The via to set.
       * @return This builder for chaining.
       */
      public Builder setVia(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        via_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes via = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearVia() {
        bitField0_ = (bitField0_ & ~0x01000000);
        via_ = getDefaultInstance().getVia();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString xForFor_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes xForFor = 26;</code>
       * @return Whether the xForFor field is set.
       */
      @java.lang.Override
      public boolean hasXForFor() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes xForFor = 26;</code>
       * @return The xForFor.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getXForFor() {
        return xForFor_;
      }
      /**
       * <code>optional bytes xForFor = 26;</code>
       * @param value The xForFor to set.
       * @return This builder for chaining.
       */
      public Builder setXForFor(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        xForFor_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes xForFor = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearXForFor() {
        bitField0_ = (bitField0_ & ~0x02000000);
        xForFor_ = getDefaultInstance().getXForFor();
        onChanged();
        return this;
      }

      private int statCode_ ;
      /**
       * <code>optional uint32 statCode = 27;</code>
       * @return Whether the statCode field is set.
       */
      @java.lang.Override
      public boolean hasStatCode() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional uint32 statCode = 27;</code>
       * @return The statCode.
       */
      @java.lang.Override
      public int getStatCode() {
        return statCode_;
      }
      /**
       * <code>optional uint32 statCode = 27;</code>
       * @param value The statCode to set.
       * @return This builder for chaining.
       */
      public Builder setStatCode(int value) {

        statCode_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 statCode = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatCode() {
        bitField0_ = (bitField0_ & ~0x04000000);
        statCode_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString met_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes met = 28;</code>
       * @return Whether the met field is set.
       */
      @java.lang.Override
      public boolean hasMet() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes met = 28;</code>
       * @return The met.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMet() {
        return met_;
      }
      /**
       * <code>optional bytes met = 28;</code>
       * @param value The met to set.
       * @return This builder for chaining.
       */
      public Builder setMet(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        met_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes met = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearMet() {
        bitField0_ = (bitField0_ & ~0x08000000);
        met_ = getDefaultInstance().getMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvAge = 29;</code>
       * @return Whether the srvAge field is set.
       */
      @java.lang.Override
      public boolean hasSrvAge() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes srvAge = 29;</code>
       * @return The srvAge.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvAge() {
        return srvAge_;
      }
      /**
       * <code>optional bytes srvAge = 29;</code>
       * @param value The srvAge to set.
       * @return This builder for chaining.
       */
      public Builder setSrvAge(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvAge_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvAge = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvAge() {
        bitField0_ = (bitField0_ & ~0x10000000);
        srvAge_ = getDefaultInstance().getSrvAge();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString proAuth_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes proAuth = 30;</code>
       * @return Whether the proAuth field is set.
       */
      @java.lang.Override
      public boolean hasProAuth() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes proAuth = 30;</code>
       * @return The proAuth.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getProAuth() {
        return proAuth_;
      }
      /**
       * <code>optional bytes proAuth = 30;</code>
       * @param value The proAuth to set.
       * @return This builder for chaining.
       */
      public Builder setProAuth(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        proAuth_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes proAuth = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearProAuth() {
        bitField0_ = (bitField0_ & ~0x20000000);
        proAuth_ = getDefaultInstance().getProAuth();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString xPowBy_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes xPowBy = 31;</code>
       * @return Whether the xPowBy field is set.
       */
      @java.lang.Override
      public boolean hasXPowBy() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes xPowBy = 31;</code>
       * @return The xPowBy.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getXPowBy() {
        return xPowBy_;
      }
      /**
       * <code>optional bytes xPowBy = 31;</code>
       * @param value The xPowBy to set.
       * @return This builder for chaining.
       */
      public Builder setXPowBy(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        xPowBy_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes xPowBy = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearXPowBy() {
        bitField0_ = (bitField0_ & ~0x40000000);
        xPowBy_ = getDefaultInstance().getXPowBy();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extHdrs_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extHdrs = 32;</code>
       * @return Whether the extHdrs field is set.
       */
      @java.lang.Override
      public boolean hasExtHdrs() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes extHdrs = 32;</code>
       * @return The extHdrs.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getExtHdrs() {
        return extHdrs_;
      }
      /**
       * <code>optional bytes extHdrs = 32;</code>
       * @param value The extHdrs to set.
       * @return This builder for chaining.
       */
      public Builder setExtHdrs(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        extHdrs_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extHdrs = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtHdrs() {
        bitField0_ = (bitField0_ & ~0x80000000);
        extHdrs_ = getDefaultInstance().getExtHdrs();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rangeofCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       * @return Whether the rangeofCli field is set.
       */
      @java.lang.Override
      public boolean hasRangeofCli() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       * @return The rangeofCli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRangeofCli() {
        return rangeofCli_;
      }
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       * @param value The rangeofCli to set.
       * @return This builder for chaining.
       */
      public Builder setRangeofCli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rangeofCli_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes rangeofCli = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearRangeofCli() {
        bitField1_ = (bitField1_ & ~0x00000001);
        rangeofCli_ = getDefaultInstance().getRangeofCli();
        onChanged();
        return this;
      }

      private int viaCnt_ ;
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       * @return Whether the viaCnt field is set.
       */
      @java.lang.Override
      public boolean hasViaCnt() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       * @return The viaCnt.
       */
      @java.lang.Override
      public int getViaCnt() {
        return viaCnt_;
      }
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       * @param value The viaCnt to set.
       * @return This builder for chaining.
       */
      public Builder setViaCnt(int value) {

        viaCnt_ = value;
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 viaCnt = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearViaCnt() {
        bitField1_ = (bitField1_ & ~0x00000002);
        viaCnt_ = 0;
        onChanged();
        return this;
      }

      private int statCodeCnt_ ;
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       * @return Whether the statCodeCnt field is set.
       */
      @java.lang.Override
      public boolean hasStatCodeCnt() {
        return ((bitField1_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       * @return The statCodeCnt.
       */
      @java.lang.Override
      public int getStatCodeCnt() {
        return statCodeCnt_;
      }
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       * @param value The statCodeCnt to set.
       * @return This builder for chaining.
       */
      public Builder setStatCodeCnt(int value) {

        statCodeCnt_ = value;
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 statCodeCnt = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatCodeCnt() {
        bitField1_ = (bitField1_ & ~0x00000004);
        statCodeCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqVer = 36;</code>
       * @return Whether the reqVer field is set.
       */
      @java.lang.Override
      public boolean hasReqVer() {
        return ((bitField1_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes reqVer = 36;</code>
       * @return The reqVer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getReqVer() {
        return reqVer_;
      }
      /**
       * <code>optional bytes reqVer = 36;</code>
       * @param value The reqVer to set.
       * @return This builder for chaining.
       */
      public Builder setReqVer(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        reqVer_ = value;
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqVer = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqVer() {
        bitField1_ = (bitField1_ & ~0x00000008);
        reqVer_ = getDefaultInstance().getReqVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqHead_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqHead = 37;</code>
       * @return Whether the reqHead field is set.
       */
      @java.lang.Override
      public boolean hasReqHead() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes reqHead = 37;</code>
       * @return The reqHead.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getReqHead() {
        return reqHead_;
      }
      /**
       * <code>optional bytes reqHead = 37;</code>
       * @param value The reqHead to set.
       * @return This builder for chaining.
       */
      public Builder setReqHead(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        reqHead_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqHead = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqHead() {
        bitField1_ = (bitField1_ & ~0x00000010);
        reqHead_ = getDefaultInstance().getReqHead();
        onChanged();
        return this;
      }

      private int reqHeadMd5_ ;
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       * @return Whether the reqHeadMd5 field is set.
       */
      @java.lang.Override
      public boolean hasReqHeadMd5() {
        return ((bitField1_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       * @return The reqHeadMd5.
       */
      @java.lang.Override
      public int getReqHeadMd5() {
        return reqHeadMd5_;
      }
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       * @param value The reqHeadMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setReqHeadMd5(int value) {

        reqHeadMd5_ = value;
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 reqHeadMd5 = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqHeadMd5() {
        bitField1_ = (bitField1_ & ~0x00000020);
        reqHeadMd5_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cacConUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cacConUp = 39;</code>
       * @return Whether the cacConUp field is set.
       */
      @java.lang.Override
      public boolean hasCacConUp() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes cacConUp = 39;</code>
       * @return The cacConUp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCacConUp() {
        return cacConUp_;
      }
      /**
       * <code>optional bytes cacConUp = 39;</code>
       * @param value The cacConUp to set.
       * @return This builder for chaining.
       */
      public Builder setCacConUp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cacConUp_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cacConUp = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearCacConUp() {
        bitField1_ = (bitField1_ & ~0x00000040);
        cacConUp_ = getDefaultInstance().getCacConUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conUp = 40;</code>
       * @return Whether the conUp field is set.
       */
      @java.lang.Override
      public boolean hasConUp() {
        return ((bitField1_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes conUp = 40;</code>
       * @return The conUp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConUp() {
        return conUp_;
      }
      /**
       * <code>optional bytes conUp = 40;</code>
       * @param value The conUp to set.
       * @return This builder for chaining.
       */
      public Builder setConUp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conUp_ = value;
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conUp = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearConUp() {
        bitField1_ = (bitField1_ & ~0x00000080);
        conUp_ = getDefaultInstance().getConUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString praUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes praUp = 41;</code>
       * @return Whether the praUp field is set.
       */
      @java.lang.Override
      public boolean hasPraUp() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes praUp = 41;</code>
       * @return The praUp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPraUp() {
        return praUp_;
      }
      /**
       * <code>optional bytes praUp = 41;</code>
       * @param value The praUp to set.
       * @return This builder for chaining.
       */
      public Builder setPraUp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        praUp_ = value;
        bitField1_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes praUp = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearPraUp() {
        bitField1_ = (bitField1_ & ~0x00000100);
        praUp_ = getDefaultInstance().getPraUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString upg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes upg = 42;</code>
       * @return Whether the upg field is set.
       */
      @java.lang.Override
      public boolean hasUpg() {
        return ((bitField1_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes upg = 42;</code>
       * @return The upg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUpg() {
        return upg_;
      }
      /**
       * <code>optional bytes upg = 42;</code>
       * @param value The upg to set.
       * @return This builder for chaining.
       */
      public Builder setUpg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        upg_ = value;
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes upg = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpg() {
        bitField1_ = (bitField1_ & ~0x00000200);
        upg_ = getDefaultInstance().getUpg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accChaUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accChaUp = 43;</code>
       * @return Whether the accChaUp field is set.
       */
      @java.lang.Override
      public boolean hasAccChaUp() {
        return ((bitField1_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes accChaUp = 43;</code>
       * @return The accChaUp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAccChaUp() {
        return accChaUp_;
      }
      /**
       * <code>optional bytes accChaUp = 43;</code>
       * @param value The accChaUp to set.
       * @return This builder for chaining.
       */
      public Builder setAccChaUp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        accChaUp_ = value;
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accChaUp = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccChaUp() {
        bitField1_ = (bitField1_ & ~0x00000400);
        accChaUp_ = getDefaultInstance().getAccChaUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString acctRanUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       * @return Whether the acctRanUp field is set.
       */
      @java.lang.Override
      public boolean hasAcctRanUp() {
        return ((bitField1_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       * @return The acctRanUp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAcctRanUp() {
        return acctRanUp_;
      }
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       * @param value The acctRanUp to set.
       * @return This builder for chaining.
       */
      public Builder setAcctRanUp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        acctRanUp_ = value;
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes acctRanUp = 44;</code>
       * @return This builder for chaining.
       */
      public Builder clearAcctRanUp() {
        bitField1_ = (bitField1_ & ~0x00000800);
        acctRanUp_ = getDefaultInstance().getAcctRanUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifMat_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifMat = 45;</code>
       * @return Whether the ifMat field is set.
       */
      @java.lang.Override
      public boolean hasIfMat() {
        return ((bitField1_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes ifMat = 45;</code>
       * @return The ifMat.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIfMat() {
        return ifMat_;
      }
      /**
       * <code>optional bytes ifMat = 45;</code>
       * @param value The ifMat to set.
       * @return This builder for chaining.
       */
      public Builder setIfMat(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ifMat_ = value;
        bitField1_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifMat = 45;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfMat() {
        bitField1_ = (bitField1_ & ~0x00001000);
        ifMat_ = getDefaultInstance().getIfMat();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifModSin_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifModSin = 46;</code>
       * @return Whether the ifModSin field is set.
       */
      @java.lang.Override
      public boolean hasIfModSin() {
        return ((bitField1_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes ifModSin = 46;</code>
       * @return The ifModSin.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIfModSin() {
        return ifModSin_;
      }
      /**
       * <code>optional bytes ifModSin = 46;</code>
       * @param value The ifModSin to set.
       * @return This builder for chaining.
       */
      public Builder setIfModSin(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ifModSin_ = value;
        bitField1_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifModSin = 46;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfModSin() {
        bitField1_ = (bitField1_ & ~0x00002000);
        ifModSin_ = getDefaultInstance().getIfModSin();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifNonMat_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       * @return Whether the ifNonMat field is set.
       */
      @java.lang.Override
      public boolean hasIfNonMat() {
        return ((bitField1_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       * @return The ifNonMat.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIfNonMat() {
        return ifNonMat_;
      }
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       * @param value The ifNonMat to set.
       * @return This builder for chaining.
       */
      public Builder setIfNonMat(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ifNonMat_ = value;
        bitField1_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifNonMat = 47;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfNonMat() {
        bitField1_ = (bitField1_ & ~0x00004000);
        ifNonMat_ = getDefaultInstance().getIfNonMat();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ifRan_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ifRan = 48;</code>
       * @return Whether the ifRan field is set.
       */
      @java.lang.Override
      public boolean hasIfRan() {
        return ((bitField1_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes ifRan = 48;</code>
       * @return The ifRan.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIfRan() {
        return ifRan_;
      }
      /**
       * <code>optional bytes ifRan = 48;</code>
       * @param value The ifRan to set.
       * @return This builder for chaining.
       */
      public Builder setIfRan(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ifRan_ = value;
        bitField1_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ifRan = 48;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfRan() {
        bitField1_ = (bitField1_ & ~0x00008000);
        ifRan_ = getDefaultInstance().getIfRan();
        onChanged();
        return this;
      }

      private long ifUnModSin_ ;
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       * @return Whether the ifUnModSin field is set.
       */
      @java.lang.Override
      public boolean hasIfUnModSin() {
        return ((bitField1_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       * @return The ifUnModSin.
       */
      @java.lang.Override
      public long getIfUnModSin() {
        return ifUnModSin_;
      }
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       * @param value The ifUnModSin to set.
       * @return This builder for chaining.
       */
      public Builder setIfUnModSin(long value) {

        ifUnModSin_ = value;
        bitField1_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ifUnModSin = 49;</code>
       * @return This builder for chaining.
       */
      public Builder clearIfUnModSin() {
        bitField1_ = (bitField1_ & ~0x00010000);
        ifUnModSin_ = 0L;
        onChanged();
        return this;
      }

      private int maxFor_ ;
      /**
       * <code>optional uint32 maxFor = 50;</code>
       * @return Whether the maxFor field is set.
       */
      @java.lang.Override
      public boolean hasMaxFor() {
        return ((bitField1_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 maxFor = 50;</code>
       * @return The maxFor.
       */
      @java.lang.Override
      public int getMaxFor() {
        return maxFor_;
      }
      /**
       * <code>optional uint32 maxFor = 50;</code>
       * @param value The maxFor to set.
       * @return This builder for chaining.
       */
      public Builder setMaxFor(int value) {

        maxFor_ = value;
        bitField1_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 maxFor = 50;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxFor() {
        bitField1_ = (bitField1_ & ~0x00020000);
        maxFor_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString te_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes te = 51;</code>
       * @return Whether the te field is set.
       */
      @java.lang.Override
      public boolean hasTe() {
        return ((bitField1_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes te = 51;</code>
       * @return The te.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTe() {
        return te_;
      }
      /**
       * <code>optional bytes te = 51;</code>
       * @param value The te to set.
       * @return This builder for chaining.
       */
      public Builder setTe(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        te_ = value;
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes te = 51;</code>
       * @return This builder for chaining.
       */
      public Builder clearTe() {
        bitField1_ = (bitField1_ & ~0x00040000);
        te_ = getDefaultInstance().getTe();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cacConDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cacConDown = 52;</code>
       * @return Whether the cacConDown field is set.
       */
      @java.lang.Override
      public boolean hasCacConDown() {
        return ((bitField1_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes cacConDown = 52;</code>
       * @return The cacConDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCacConDown() {
        return cacConDown_;
      }
      /**
       * <code>optional bytes cacConDown = 52;</code>
       * @param value The cacConDown to set.
       * @return This builder for chaining.
       */
      public Builder setCacConDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cacConDown_ = value;
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cacConDown = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearCacConDown() {
        bitField1_ = (bitField1_ & ~0x00080000);
        cacConDown_ = getDefaultInstance().getCacConDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conDown = 53;</code>
       * @return Whether the conDown field is set.
       */
      @java.lang.Override
      public boolean hasConDown() {
        return ((bitField1_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes conDown = 53;</code>
       * @return The conDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConDown() {
        return conDown_;
      }
      /**
       * <code>optional bytes conDown = 53;</code>
       * @param value The conDown to set.
       * @return This builder for chaining.
       */
      public Builder setConDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conDown_ = value;
        bitField1_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conDown = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearConDown() {
        bitField1_ = (bitField1_ & ~0x00100000);
        conDown_ = getDefaultInstance().getConDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString praDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes praDown = 54;</code>
       * @return Whether the praDown field is set.
       */
      @java.lang.Override
      public boolean hasPraDown() {
        return ((bitField1_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes praDown = 54;</code>
       * @return The praDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPraDown() {
        return praDown_;
      }
      /**
       * <code>optional bytes praDown = 54;</code>
       * @param value The praDown to set.
       * @return This builder for chaining.
       */
      public Builder setPraDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        praDown_ = value;
        bitField1_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes praDown = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearPraDown() {
        bitField1_ = (bitField1_ & ~0x00200000);
        praDown_ = getDefaultInstance().getPraDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString trail_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes trail = 55;</code>
       * @return Whether the trail field is set.
       */
      @java.lang.Override
      public boolean hasTrail() {
        return ((bitField1_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes trail = 55;</code>
       * @return The trail.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTrail() {
        return trail_;
      }
      /**
       * <code>optional bytes trail = 55;</code>
       * @param value The trail to set.
       * @return This builder for chaining.
       */
      public Builder setTrail(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        trail_ = value;
        bitField1_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes trail = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearTrail() {
        bitField1_ = (bitField1_ & ~0x00400000);
        trail_ = getDefaultInstance().getTrail();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accRanDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accRanDown = 56;</code>
       * @return Whether the accRanDown field is set.
       */
      @java.lang.Override
      public boolean hasAccRanDown() {
        return ((bitField1_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes accRanDown = 56;</code>
       * @return The accRanDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAccRanDown() {
        return accRanDown_;
      }
      /**
       * <code>optional bytes accRanDown = 56;</code>
       * @param value The accRanDown to set.
       * @return This builder for chaining.
       */
      public Builder setAccRanDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        accRanDown_ = value;
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accRanDown = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccRanDown() {
        bitField1_ = (bitField1_ & ~0x00800000);
        accRanDown_ = getDefaultInstance().getAccRanDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eTag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes eTag = 57;</code>
       * @return Whether the eTag field is set.
       */
      @java.lang.Override
      public boolean hasETag() {
        return ((bitField1_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes eTag = 57;</code>
       * @return The eTag.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getETag() {
        return eTag_;
      }
      /**
       * <code>optional bytes eTag = 57;</code>
       * @param value The eTag to set.
       * @return This builder for chaining.
       */
      public Builder setETag(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        eTag_ = value;
        bitField1_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes eTag = 57;</code>
       * @return This builder for chaining.
       */
      public Builder clearETag() {
        bitField1_ = (bitField1_ & ~0x01000000);
        eTag_ = getDefaultInstance().getETag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString retAft_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes retAft = 58;</code>
       * @return Whether the retAft field is set.
       */
      @java.lang.Override
      public boolean hasRetAft() {
        return ((bitField1_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes retAft = 58;</code>
       * @return The retAft.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRetAft() {
        return retAft_;
      }
      /**
       * <code>optional bytes retAft = 58;</code>
       * @param value The retAft to set.
       * @return This builder for chaining.
       */
      public Builder setRetAft(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        retAft_ = value;
        bitField1_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes retAft = 58;</code>
       * @return This builder for chaining.
       */
      public Builder clearRetAft() {
        bitField1_ = (bitField1_ & ~0x02000000);
        retAft_ = getDefaultInstance().getRetAft();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString wwwAuth_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       * @return Whether the wwwAuth field is set.
       */
      @java.lang.Override
      public boolean hasWwwAuth() {
        return ((bitField1_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       * @return The wwwAuth.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getWwwAuth() {
        return wwwAuth_;
      }
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       * @param value The wwwAuth to set.
       * @return This builder for chaining.
       */
      public Builder setWwwAuth(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        wwwAuth_ = value;
        bitField1_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes wwwAuth = 59;</code>
       * @return This builder for chaining.
       */
      public Builder clearWwwAuth() {
        bitField1_ = (bitField1_ & ~0x04000000);
        wwwAuth_ = getDefaultInstance().getWwwAuth();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString refresh_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes refresh = 60;</code>
       * @return Whether the refresh field is set.
       */
      @java.lang.Override
      public boolean hasRefresh() {
        return ((bitField1_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes refresh = 60;</code>
       * @return The refresh.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRefresh() {
        return refresh_;
      }
      /**
       * <code>optional bytes refresh = 60;</code>
       * @param value The refresh to set.
       * @return This builder for chaining.
       */
      public Builder setRefresh(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        refresh_ = value;
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes refresh = 60;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefresh() {
        bitField1_ = (bitField1_ & ~0x08000000);
        refresh_ = getDefaultInstance().getRefresh();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conTypDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conTypDown = 61;</code>
       * @return Whether the conTypDown field is set.
       */
      @java.lang.Override
      public boolean hasConTypDown() {
        return ((bitField1_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes conTypDown = 61;</code>
       * @return The conTypDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConTypDown() {
        return conTypDown_;
      }
      /**
       * <code>optional bytes conTypDown = 61;</code>
       * @param value The conTypDown to set.
       * @return This builder for chaining.
       */
      public Builder setConTypDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conTypDown_ = value;
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conTypDown = 61;</code>
       * @return This builder for chaining.
       */
      public Builder clearConTypDown() {
        bitField1_ = (bitField1_ & ~0x10000000);
        conTypDown_ = getDefaultInstance().getConTypDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString allow_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes allow = 62;</code>
       * @return Whether the allow field is set.
       */
      @java.lang.Override
      public boolean hasAllow() {
        return ((bitField1_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes allow = 62;</code>
       * @return The allow.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAllow() {
        return allow_;
      }
      /**
       * <code>optional bytes allow = 62;</code>
       * @param value The allow to set.
       * @return This builder for chaining.
       */
      public Builder setAllow(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        allow_ = value;
        bitField1_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes allow = 62;</code>
       * @return This builder for chaining.
       */
      public Builder clearAllow() {
        bitField1_ = (bitField1_ & ~0x20000000);
        allow_ = getDefaultInstance().getAllow();
        onChanged();
        return this;
      }

      private long expires_ ;
      /**
       * <code>optional uint64 expires = 63;</code>
       * @return Whether the expires field is set.
       */
      @java.lang.Override
      public boolean hasExpires() {
        return ((bitField1_ & 0x40000000) != 0);
      }
      /**
       * <code>optional uint64 expires = 63;</code>
       * @return The expires.
       */
      @java.lang.Override
      public long getExpires() {
        return expires_;
      }
      /**
       * <code>optional uint64 expires = 63;</code>
       * @param value The expires to set.
       * @return This builder for chaining.
       */
      public Builder setExpires(long value) {

        expires_ = value;
        bitField1_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 expires = 63;</code>
       * @return This builder for chaining.
       */
      public Builder clearExpires() {
        bitField1_ = (bitField1_ & ~0x40000000);
        expires_ = 0L;
        onChanged();
        return this;
      }

      private long lasMod_ ;
      /**
       * <code>optional uint64 lasMod = 64;</code>
       * @return Whether the lasMod field is set.
       */
      @java.lang.Override
      public boolean hasLasMod() {
        return ((bitField1_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint64 lasMod = 64;</code>
       * @return The lasMod.
       */
      @java.lang.Override
      public long getLasMod() {
        return lasMod_;
      }
      /**
       * <code>optional uint64 lasMod = 64;</code>
       * @param value The lasMod to set.
       * @return This builder for chaining.
       */
      public Builder setLasMod(long value) {

        lasMod_ = value;
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 lasMod = 64;</code>
       * @return This builder for chaining.
       */
      public Builder clearLasMod() {
        bitField1_ = (bitField1_ & ~0x80000000);
        lasMod_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accChaDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accChaDown = 65;</code>
       * @return Whether the accChaDown field is set.
       */
      @java.lang.Override
      public boolean hasAccChaDown() {
        return ((bitField2_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes accChaDown = 65;</code>
       * @return The accChaDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAccChaDown() {
        return accChaDown_;
      }
      /**
       * <code>optional bytes accChaDown = 65;</code>
       * @param value The accChaDown to set.
       * @return This builder for chaining.
       */
      public Builder setAccChaDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        accChaDown_ = value;
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accChaDown = 65;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccChaDown() {
        bitField2_ = (bitField2_ & ~0x00000001);
        accChaDown_ = getDefaultInstance().getAccChaDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString httpRelKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       * @return Whether the httpRelKey field is set.
       */
      @java.lang.Override
      public boolean hasHttpRelKey() {
        return ((bitField2_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       * @return The httpRelKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getHttpRelKey() {
        return httpRelKey_;
      }
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       * @param value The httpRelKey to set.
       * @return This builder for chaining.
       */
      public Builder setHttpRelKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        httpRelKey_ = value;
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes httpRelKey = 66;</code>
       * @return This builder for chaining.
       */
      public Builder clearHttpRelKey() {
        bitField2_ = (bitField2_ & ~0x00000002);
        httpRelKey_ = getDefaultInstance().getHttpRelKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString httpEmbPro_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       * @return Whether the httpEmbPro field is set.
       */
      @java.lang.Override
      public boolean hasHttpEmbPro() {
        return ((bitField2_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       * @return The httpEmbPro.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getHttpEmbPro() {
        return httpEmbPro_;
      }
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       * @param value The httpEmbPro to set.
       * @return This builder for chaining.
       */
      public Builder setHttpEmbPro(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        httpEmbPro_ = value;
        bitField2_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes httpEmbPro = 67;</code>
       * @return This builder for chaining.
       */
      public Builder clearHttpEmbPro() {
        bitField2_ = (bitField2_ & ~0x00000004);
        httpEmbPro_ = getDefaultInstance().getHttpEmbPro();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fullTextHeader_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       * @return Whether the fullTextHeader field is set.
       */
      @java.lang.Override
      public boolean hasFullTextHeader() {
        return ((bitField2_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       * @return The fullTextHeader.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFullTextHeader() {
        return fullTextHeader_;
      }
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       * @param value The fullTextHeader to set.
       * @return This builder for chaining.
       */
      public Builder setFullTextHeader(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fullTextHeader_ = value;
        bitField2_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fullTextHeader = 68;</code>
       * @return This builder for chaining.
       */
      public Builder clearFullTextHeader() {
        bitField2_ = (bitField2_ & ~0x00000008);
        fullTextHeader_ = getDefaultInstance().getFullTextHeader();
        onChanged();
        return this;
      }

      private int fullTextLen_ ;
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       * @return Whether the fullTextLen field is set.
       */
      @java.lang.Override
      public boolean hasFullTextLen() {
        return ((bitField2_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       * @return The fullTextLen.
       */
      @java.lang.Override
      public int getFullTextLen() {
        return fullTextLen_;
      }
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       * @param value The fullTextLen to set.
       * @return This builder for chaining.
       */
      public Builder setFullTextLen(int value) {

        fullTextLen_ = value;
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 fullTextLen = 69;</code>
       * @return This builder for chaining.
       */
      public Builder clearFullTextLen() {
        bitField2_ = (bitField2_ & ~0x00000010);
        fullTextLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fileName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fileName = 70;</code>
       * @return Whether the fileName field is set.
       */
      @java.lang.Override
      public boolean hasFileName() {
        return ((bitField2_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes fileName = 70;</code>
       * @return The fileName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFileName() {
        return fileName_;
      }
      /**
       * <code>optional bytes fileName = 70;</code>
       * @param value The fileName to set.
       * @return This builder for chaining.
       */
      public Builder setFileName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileName_ = value;
        bitField2_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fileName = 70;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileName() {
        bitField2_ = (bitField2_ & ~0x00000020);
        fileName_ = getDefaultInstance().getFileName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString contDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes contDown = 71;</code>
       * @return Whether the contDown field is set.
       */
      @java.lang.Override
      public boolean hasContDown() {
        return ((bitField2_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes contDown = 71;</code>
       * @return The contDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getContDown() {
        return contDown_;
      }
      /**
       * <code>optional bytes contDown = 71;</code>
       * @param value The contDown to set.
       * @return This builder for chaining.
       */
      public Builder setContDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        contDown_ = value;
        bitField2_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes contDown = 71;</code>
       * @return This builder for chaining.
       */
      public Builder clearContDown() {
        bitField2_ = (bitField2_ & ~0x00000040);
        contDown_ = getDefaultInstance().getContDown();
        onChanged();
        return this;
      }

      private int reqVerCnt_ ;
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       * @return Whether the reqVerCnt field is set.
       */
      @java.lang.Override
      public boolean hasReqVerCnt() {
        return ((bitField2_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       * @return The reqVerCnt.
       */
      @java.lang.Override
      public int getReqVerCnt() {
        return reqVerCnt_;
      }
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       * @param value The reqVerCnt to set.
       * @return This builder for chaining.
       */
      public Builder setReqVerCnt(int value) {

        reqVerCnt_ = value;
        bitField2_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 reqVerCnt = 72;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqVerCnt() {
        bitField2_ = (bitField2_ & ~0x00000080);
        reqVerCnt_ = 0;
        onChanged();
        return this;
      }

      private int metCnt_ ;
      /**
       * <code>optional uint32 metCnt = 73;</code>
       * @return Whether the metCnt field is set.
       */
      @java.lang.Override
      public boolean hasMetCnt() {
        return ((bitField2_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 metCnt = 73;</code>
       * @return The metCnt.
       */
      @java.lang.Override
      public int getMetCnt() {
        return metCnt_;
      }
      /**
       * <code>optional uint32 metCnt = 73;</code>
       * @param value The metCnt to set.
       * @return This builder for chaining.
       */
      public Builder setMetCnt(int value) {

        metCnt_ = value;
        bitField2_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 metCnt = 73;</code>
       * @return This builder for chaining.
       */
      public Builder clearMetCnt() {
        bitField2_ = (bitField2_ & ~0x00000100);
        metCnt_ = 0;
        onChanged();
        return this;
      }

      private int reqHeadCnt_ ;
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       * @return Whether the reqHeadCnt field is set.
       */
      @java.lang.Override
      public boolean hasReqHeadCnt() {
        return ((bitField2_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       * @return The reqHeadCnt.
       */
      @java.lang.Override
      public int getReqHeadCnt() {
        return reqHeadCnt_;
      }
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       * @param value The reqHeadCnt to set.
       * @return This builder for chaining.
       */
      public Builder setReqHeadCnt(int value) {

        reqHeadCnt_ = value;
        bitField2_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 reqHeadCnt = 74;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqHeadCnt() {
        bitField2_ = (bitField2_ & ~0x00000200);
        reqHeadCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accByCli = 75;</code>
       * @return Whether the accByCli field is set.
       */
      @java.lang.Override
      public boolean hasAccByCli() {
        return ((bitField2_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes accByCli = 75;</code>
       * @return The accByCli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAccByCli() {
        return accByCli_;
      }
      /**
       * <code>optional bytes accByCli = 75;</code>
       * @param value The accByCli to set.
       * @return This builder for chaining.
       */
      public Builder setAccByCli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        accByCli_ = value;
        bitField2_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accByCli = 75;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccByCli() {
        bitField2_ = (bitField2_ & ~0x00000400);
        accByCli_ = getDefaultInstance().getAccByCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accLanByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       * @return Whether the accLanByCli field is set.
       */
      @java.lang.Override
      public boolean hasAccLanByCli() {
        return ((bitField2_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       * @return The accLanByCli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAccLanByCli() {
        return accLanByCli_;
      }
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       * @param value The accLanByCli to set.
       * @return This builder for chaining.
       */
      public Builder setAccLanByCli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        accLanByCli_ = value;
        bitField2_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accLanByCli = 76;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccLanByCli() {
        bitField2_ = (bitField2_ & ~0x00000800);
        accLanByCli_ = getDefaultInstance().getAccLanByCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString accEncByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       * @return Whether the accEncByCli field is set.
       */
      @java.lang.Override
      public boolean hasAccEncByCli() {
        return ((bitField2_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       * @return The accEncByCli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAccEncByCli() {
        return accEncByCli_;
      }
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       * @param value The accEncByCli to set.
       * @return This builder for chaining.
       */
      public Builder setAccEncByCli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        accEncByCli_ = value;
        bitField2_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes accEncByCli = 77;</code>
       * @return This builder for chaining.
       */
      public Builder clearAccEncByCli() {
        bitField2_ = (bitField2_ & ~0x00001000);
        accEncByCli_ = getDefaultInstance().getAccEncByCli();
        onChanged();
        return this;
      }

      private int authCnt_ ;
      /**
       * <code>optional uint32 authCnt = 78;</code>
       * @return Whether the authCnt field is set.
       */
      @java.lang.Override
      public boolean hasAuthCnt() {
        return ((bitField2_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint32 authCnt = 78;</code>
       * @return The authCnt.
       */
      @java.lang.Override
      public int getAuthCnt() {
        return authCnt_;
      }
      /**
       * <code>optional uint32 authCnt = 78;</code>
       * @param value The authCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAuthCnt(int value) {

        authCnt_ = value;
        bitField2_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 authCnt = 78;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthCnt() {
        bitField2_ = (bitField2_ & ~0x00002000);
        authCnt_ = 0;
        onChanged();
        return this;
      }

      private int hostCnt_ ;
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       * @return Whether the hostCnt field is set.
       */
      @java.lang.Override
      public boolean hasHostCnt() {
        return ((bitField2_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       * @return The hostCnt.
       */
      @java.lang.Override
      public int getHostCnt() {
        return hostCnt_;
      }
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       * @param value The hostCnt to set.
       * @return This builder for chaining.
       */
      public Builder setHostCnt(int value) {

        hostCnt_ = value;
        bitField2_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 hostCnt = 79;</code>
       * @return This builder for chaining.
       */
      public Builder clearHostCnt() {
        bitField2_ = (bitField2_ & ~0x00004000);
        hostCnt_ = 0;
        onChanged();
        return this;
      }

      private int uriCnt_ ;
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       * @return Whether the uriCnt field is set.
       */
      @java.lang.Override
      public boolean hasUriCnt() {
        return ((bitField2_ & 0x00008000) != 0);
      }
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       * @return The uriCnt.
       */
      @java.lang.Override
      public int getUriCnt() {
        return uriCnt_;
      }
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       * @param value The uriCnt to set.
       * @return This builder for chaining.
       */
      public Builder setUriCnt(int value) {

        uriCnt_ = value;
        bitField2_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uriCnt = 80;</code>
       * @return This builder for chaining.
       */
      public Builder clearUriCnt() {
        bitField2_ = (bitField2_ & ~0x00008000);
        uriCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString uriPath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes uriPath = 81;</code>
       * @return Whether the uriPath field is set.
       */
      @java.lang.Override
      public boolean hasUriPath() {
        return ((bitField2_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes uriPath = 81;</code>
       * @return The uriPath.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUriPath() {
        return uriPath_;
      }
      /**
       * <code>optional bytes uriPath = 81;</code>
       * @param value The uriPath to set.
       * @return This builder for chaining.
       */
      public Builder setUriPath(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        uriPath_ = value;
        bitField2_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes uriPath = 81;</code>
       * @return This builder for chaining.
       */
      public Builder clearUriPath() {
        bitField2_ = (bitField2_ & ~0x00010000);
        uriPath_ = getDefaultInstance().getUriPath();
        onChanged();
        return this;
      }

      private int uriPathCnt_ ;
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       * @return Whether the uriPathCnt field is set.
       */
      @java.lang.Override
      public boolean hasUriPathCnt() {
        return ((bitField2_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       * @return The uriPathCnt.
       */
      @java.lang.Override
      public int getUriPathCnt() {
        return uriPathCnt_;
      }
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       * @param value The uriPathCnt to set.
       * @return This builder for chaining.
       */
      public Builder setUriPathCnt(int value) {

        uriPathCnt_ = value;
        bitField2_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uriPathCnt = 82;</code>
       * @return This builder for chaining.
       */
      public Builder clearUriPathCnt() {
        bitField2_ = (bitField2_ & ~0x00020000);
        uriPathCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> uriKey_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureUriKeyIsMutable() {
        if (!uriKey_.isModifiable()) {
          uriKey_ = makeMutableCopy(uriKey_);
        }
        bitField2_ |= 0x00040000;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       * @return A list containing the uriKey.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getUriKeyList() {
        uriKey_.makeImmutable();
        return uriKey_;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       * @return The count of uriKey.
       */
      public int getUriKeyCount() {
        return uriKey_.size();
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       * @param index The index of the element to return.
       * @return The uriKey at the given index.
       */
      public com.google.protobuf.ByteString getUriKey(int index) {
        return uriKey_.get(index);
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       * @param index The index to set the value at.
       * @param value The uriKey to set.
       * @return This builder for chaining.
       */
      public Builder setUriKey(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureUriKeyIsMutable();
        uriKey_.set(index, value);
        bitField2_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       * @param value The uriKey to add.
       * @return This builder for chaining.
       */
      public Builder addUriKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureUriKeyIsMutable();
        uriKey_.add(value);
        bitField2_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       * @param values The uriKey to add.
       * @return This builder for chaining.
       */
      public Builder addAllUriKey(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureUriKeyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, uriKey_);
        bitField2_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes uriKey = 83;</code>
       * @return This builder for chaining.
       */
      public Builder clearUriKey() {
        uriKey_ = emptyList(com.google.protobuf.ByteString.class);
        bitField2_ = (bitField2_ & ~0x00040000);
        onChanged();
        return this;
      }

      private int uriKeyCnt_ ;
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       * @return Whether the uriKeyCnt field is set.
       */
      @java.lang.Override
      public boolean hasUriKeyCnt() {
        return ((bitField2_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       * @return The uriKeyCnt.
       */
      @java.lang.Override
      public int getUriKeyCnt() {
        return uriKeyCnt_;
      }
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       * @param value The uriKeyCnt to set.
       * @return This builder for chaining.
       */
      public Builder setUriKeyCnt(int value) {

        uriKeyCnt_ = value;
        bitField2_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 uriKeyCnt = 84;</code>
       * @return This builder for chaining.
       */
      public Builder clearUriKeyCnt() {
        bitField2_ = (bitField2_ & ~0x00080000);
        uriKeyCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString uriSearch_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes uriSearch = 85;</code>
       * @return Whether the uriSearch field is set.
       */
      @java.lang.Override
      public boolean hasUriSearch() {
        return ((bitField2_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes uriSearch = 85;</code>
       * @return The uriSearch.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUriSearch() {
        return uriSearch_;
      }
      /**
       * <code>optional bytes uriSearch = 85;</code>
       * @param value The uriSearch to set.
       * @return This builder for chaining.
       */
      public Builder setUriSearch(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        uriSearch_ = value;
        bitField2_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes uriSearch = 85;</code>
       * @return This builder for chaining.
       */
      public Builder clearUriSearch() {
        bitField2_ = (bitField2_ & ~0x00100000);
        uriSearch_ = getDefaultInstance().getUriSearch();
        onChanged();
        return this;
      }

      private int usrAgeCnt_ ;
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       * @return Whether the usrAgeCnt field is set.
       */
      @java.lang.Override
      public boolean hasUsrAgeCnt() {
        return ((bitField2_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       * @return The usrAgeCnt.
       */
      @java.lang.Override
      public int getUsrAgeCnt() {
        return usrAgeCnt_;
      }
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       * @param value The usrAgeCnt to set.
       * @return This builder for chaining.
       */
      public Builder setUsrAgeCnt(int value) {

        usrAgeCnt_ = value;
        bitField2_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 usrAgeCnt = 86;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsrAgeCnt() {
        bitField2_ = (bitField2_ & ~0x00200000);
        usrAgeCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString user_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes user = 87;</code>
       * @return Whether the user field is set.
       */
      @java.lang.Override
      public boolean hasUser() {
        return ((bitField2_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes user = 87;</code>
       * @return The user.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUser() {
        return user_;
      }
      /**
       * <code>optional bytes user = 87;</code>
       * @param value The user to set.
       * @return This builder for chaining.
       */
      public Builder setUser(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        user_ = value;
        bitField2_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes user = 87;</code>
       * @return This builder for chaining.
       */
      public Builder clearUser() {
        bitField2_ = (bitField2_ & ~0x00400000);
        user_ = getDefaultInstance().getUser();
        onChanged();
        return this;
      }

      private int userCnt_ ;
      /**
       * <code>optional uint32 userCnt = 88;</code>
       * @return Whether the userCnt field is set.
       */
      @java.lang.Override
      public boolean hasUserCnt() {
        return ((bitField2_ & 0x00800000) != 0);
      }
      /**
       * <code>optional uint32 userCnt = 88;</code>
       * @return The userCnt.
       */
      @java.lang.Override
      public int getUserCnt() {
        return userCnt_;
      }
      /**
       * <code>optional uint32 userCnt = 88;</code>
       * @param value The userCnt to set.
       * @return This builder for chaining.
       */
      public Builder setUserCnt(int value) {

        userCnt_ = value;
        bitField2_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 userCnt = 88;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserCnt() {
        bitField2_ = (bitField2_ & ~0x00800000);
        userCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqBody_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqBody = 89;</code>
       * @return Whether the reqBody field is set.
       */
      @java.lang.Override
      public boolean hasReqBody() {
        return ((bitField2_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes reqBody = 89;</code>
       * @return The reqBody.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getReqBody() {
        return reqBody_;
      }
      /**
       * <code>optional bytes reqBody = 89;</code>
       * @param value The reqBody to set.
       * @return This builder for chaining.
       */
      public Builder setReqBody(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        reqBody_ = value;
        bitField2_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqBody = 89;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqBody() {
        bitField2_ = (bitField2_ & ~0x01000000);
        reqBody_ = getDefaultInstance().getReqBody();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString reqBodyN_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       * @return Whether the reqBodyN field is set.
       */
      @java.lang.Override
      public boolean hasReqBodyN() {
        return ((bitField2_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       * @return The reqBodyN.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getReqBodyN() {
        return reqBodyN_;
      }
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       * @param value The reqBodyN to set.
       * @return This builder for chaining.
       */
      public Builder setReqBodyN(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        reqBodyN_ = value;
        bitField2_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes reqBodyN = 90;</code>
       * @return This builder for chaining.
       */
      public Builder clearReqBodyN() {
        bitField2_ = (bitField2_ & ~0x02000000);
        reqBodyN_ = getDefaultInstance().getReqBodyN();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conMD5ByCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       * @return Whether the conMD5ByCli field is set.
       */
      @java.lang.Override
      public boolean hasConMD5ByCli() {
        return ((bitField2_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       * @return The conMD5ByCli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConMD5ByCli() {
        return conMD5ByCli_;
      }
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       * @param value The conMD5ByCli to set.
       * @return This builder for chaining.
       */
      public Builder setConMD5ByCli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conMD5ByCli_ = value;
        bitField2_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conMD5ByCli = 91;</code>
       * @return This builder for chaining.
       */
      public Builder clearConMD5ByCli() {
        bitField2_ = (bitField2_ & ~0x04000000);
        conMD5ByCli_ = getDefaultInstance().getConMD5ByCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> cookieKey_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureCookieKeyIsMutable() {
        if (!cookieKey_.isModifiable()) {
          cookieKey_ = makeMutableCopy(cookieKey_);
        }
        bitField2_ |= 0x08000000;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       * @return A list containing the cookieKey.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getCookieKeyList() {
        cookieKey_.makeImmutable();
        return cookieKey_;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       * @return The count of cookieKey.
       */
      public int getCookieKeyCount() {
        return cookieKey_.size();
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       * @param index The index of the element to return.
       * @return The cookieKey at the given index.
       */
      public com.google.protobuf.ByteString getCookieKey(int index) {
        return cookieKey_.get(index);
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       * @param index The index to set the value at.
       * @param value The cookieKey to set.
       * @return This builder for chaining.
       */
      public Builder setCookieKey(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCookieKeyIsMutable();
        cookieKey_.set(index, value);
        bitField2_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       * @param value The cookieKey to add.
       * @return This builder for chaining.
       */
      public Builder addCookieKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCookieKeyIsMutable();
        cookieKey_.add(value);
        bitField2_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       * @param values The cookieKey to add.
       * @return This builder for chaining.
       */
      public Builder addAllCookieKey(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureCookieKeyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cookieKey_);
        bitField2_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes cookieKey = 92;</code>
       * @return This builder for chaining.
       */
      public Builder clearCookieKey() {
        cookieKey_ = emptyList(com.google.protobuf.ByteString.class);
        bitField2_ = (bitField2_ & ~0x08000000);
        onChanged();
        return this;
      }

      private int cookieKeyCnt_ ;
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       * @return Whether the cookieKeyCnt field is set.
       */
      @java.lang.Override
      public boolean hasCookieKeyCnt() {
        return ((bitField2_ & 0x10000000) != 0);
      }
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       * @return The cookieKeyCnt.
       */
      @java.lang.Override
      public int getCookieKeyCnt() {
        return cookieKeyCnt_;
      }
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       * @param value The cookieKeyCnt to set.
       * @return This builder for chaining.
       */
      public Builder setCookieKeyCnt(int value) {

        cookieKeyCnt_ = value;
        bitField2_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cookieKeyCnt = 93;</code>
       * @return This builder for chaining.
       */
      public Builder clearCookieKeyCnt() {
        bitField2_ = (bitField2_ & ~0x10000000);
        cookieKeyCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString imei_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes imei = 94;</code>
       * @return Whether the imei field is set.
       */
      @java.lang.Override
      public boolean hasImei() {
        return ((bitField2_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes imei = 94;</code>
       * @return The imei.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getImei() {
        return imei_;
      }
      /**
       * <code>optional bytes imei = 94;</code>
       * @param value The imei to set.
       * @return This builder for chaining.
       */
      public Builder setImei(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        imei_ = value;
        bitField2_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes imei = 94;</code>
       * @return This builder for chaining.
       */
      public Builder clearImei() {
        bitField2_ = (bitField2_ & ~0x20000000);
        imei_ = getDefaultInstance().getImei();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString imsi_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes imsi = 95;</code>
       * @return Whether the imsi field is set.
       */
      @java.lang.Override
      public boolean hasImsi() {
        return ((bitField2_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes imsi = 95;</code>
       * @return The imsi.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getImsi() {
        return imsi_;
      }
      /**
       * <code>optional bytes imsi = 95;</code>
       * @param value The imsi to set.
       * @return This builder for chaining.
       */
      public Builder setImsi(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        imsi_ = value;
        bitField2_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes imsi = 95;</code>
       * @return This builder for chaining.
       */
      public Builder clearImsi() {
        bitField2_ = (bitField2_ & ~0x40000000);
        imsi_ = getDefaultInstance().getImsi();
        onChanged();
        return this;
      }

      private int xForForCnt_ ;
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       * @return Whether the xForForCnt field is set.
       */
      @java.lang.Override
      public boolean hasXForForCnt() {
        return ((bitField2_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       * @return The xForForCnt.
       */
      @java.lang.Override
      public int getXForForCnt() {
        return xForForCnt_;
      }
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       * @param value The xForForCnt to set.
       * @return This builder for chaining.
       */
      public Builder setXForForCnt(int value) {

        xForForCnt_ = value;
        bitField2_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 xForForCnt = 96;</code>
       * @return This builder for chaining.
       */
      public Builder clearXForForCnt() {
        bitField2_ = (bitField2_ & ~0x80000000);
        xForForCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respVer = 97;</code>
       * @return Whether the respVer field is set.
       */
      @java.lang.Override
      public boolean hasRespVer() {
        return ((bitField3_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes respVer = 97;</code>
       * @return The respVer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRespVer() {
        return respVer_;
      }
      /**
       * <code>optional bytes respVer = 97;</code>
       * @param value The respVer to set.
       * @return This builder for chaining.
       */
      public Builder setRespVer(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        respVer_ = value;
        bitField3_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respVer = 97;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespVer() {
        bitField3_ = (bitField3_ & ~0x00000001);
        respVer_ = getDefaultInstance().getRespVer();
        onChanged();
        return this;
      }

      private int respVerCnt_ ;
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       * @return Whether the respVerCnt field is set.
       */
      @java.lang.Override
      public boolean hasRespVerCnt() {
        return ((bitField3_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       * @return The respVerCnt.
       */
      @java.lang.Override
      public int getRespVerCnt() {
        return respVerCnt_;
      }
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       * @param value The respVerCnt to set.
       * @return This builder for chaining.
       */
      public Builder setRespVerCnt(int value) {

        respVerCnt_ = value;
        bitField3_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 respVerCnt = 98;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespVerCnt() {
        bitField3_ = (bitField3_ & ~0x00000002);
        respVerCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respHead_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respHead = 99;</code>
       * @return Whether the respHead field is set.
       */
      @java.lang.Override
      public boolean hasRespHead() {
        return ((bitField3_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes respHead = 99;</code>
       * @return The respHead.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRespHead() {
        return respHead_;
      }
      /**
       * <code>optional bytes respHead = 99;</code>
       * @param value The respHead to set.
       * @return This builder for chaining.
       */
      public Builder setRespHead(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        respHead_ = value;
        bitField3_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respHead = 99;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespHead() {
        bitField3_ = (bitField3_ & ~0x00000004);
        respHead_ = getDefaultInstance().getRespHead();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respHeadMd5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       * @return Whether the respHeadMd5 field is set.
       */
      @java.lang.Override
      public boolean hasRespHeadMd5() {
        return ((bitField3_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       * @return The respHeadMd5.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRespHeadMd5() {
        return respHeadMd5_;
      }
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       * @param value The respHeadMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setRespHeadMd5(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        respHeadMd5_ = value;
        bitField3_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respHeadMd5 = 100;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespHeadMd5() {
        bitField3_ = (bitField3_ & ~0x00000008);
        respHeadMd5_ = getDefaultInstance().getRespHeadMd5();
        onChanged();
        return this;
      }

      private int respHeadCnt_ ;
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       * @return Whether the respHeadCnt field is set.
       */
      @java.lang.Override
      public boolean hasRespHeadCnt() {
        return ((bitField3_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       * @return The respHeadCnt.
       */
      @java.lang.Override
      public int getRespHeadCnt() {
        return respHeadCnt_;
      }
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       * @param value The respHeadCnt to set.
       * @return This builder for chaining.
       */
      public Builder setRespHeadCnt(int value) {

        respHeadCnt_ = value;
        bitField3_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 respHeadCnt = 101;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespHeadCnt() {
        bitField3_ = (bitField3_ & ~0x00000010);
        respHeadCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respBody_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respBody = 102;</code>
       * @return Whether the respBody field is set.
       */
      @java.lang.Override
      public boolean hasRespBody() {
        return ((bitField3_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes respBody = 102;</code>
       * @return The respBody.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRespBody() {
        return respBody_;
      }
      /**
       * <code>optional bytes respBody = 102;</code>
       * @param value The respBody to set.
       * @return This builder for chaining.
       */
      public Builder setRespBody(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        respBody_ = value;
        bitField3_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respBody = 102;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespBody() {
        bitField3_ = (bitField3_ & ~0x00000020);
        respBody_ = getDefaultInstance().getRespBody();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString respBodyN_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes respBodyN = 103;</code>
       * @return Whether the respBodyN field is set.
       */
      @java.lang.Override
      public boolean hasRespBodyN() {
        return ((bitField3_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes respBodyN = 103;</code>
       * @return The respBodyN.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRespBodyN() {
        return respBodyN_;
      }
      /**
       * <code>optional bytes respBodyN = 103;</code>
       * @param value The respBodyN to set.
       * @return This builder for chaining.
       */
      public Builder setRespBodyN(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        respBodyN_ = value;
        bitField3_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes respBodyN = 103;</code>
       * @return This builder for chaining.
       */
      public Builder clearRespBodyN() {
        bitField3_ = (bitField3_ & ~0x00000040);
        respBodyN_ = getDefaultInstance().getRespBodyN();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conMD5BySrv_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       * @return Whether the conMD5BySrv field is set.
       */
      @java.lang.Override
      public boolean hasConMD5BySrv() {
        return ((bitField3_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       * @return The conMD5BySrv.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConMD5BySrv() {
        return conMD5BySrv_;
      }
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       * @param value The conMD5BySrv to set.
       * @return This builder for chaining.
       */
      public Builder setConMD5BySrv(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conMD5BySrv_ = value;
        bitField3_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conMD5BySrv = 104;</code>
       * @return This builder for chaining.
       */
      public Builder clearConMD5BySrv() {
        bitField3_ = (bitField3_ & ~0x00000080);
        conMD5BySrv_ = getDefaultInstance().getConMD5BySrv();
        onChanged();
        return this;
      }

      private int conEncBySrv_ ;
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       * @return Whether the conEncBySrv field is set.
       */
      @java.lang.Override
      public boolean hasConEncBySrv() {
        return ((bitField3_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       * @return The conEncBySrv.
       */
      @java.lang.Override
      public int getConEncBySrv() {
        return conEncBySrv_;
      }
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       * @param value The conEncBySrv to set.
       * @return This builder for chaining.
       */
      public Builder setConEncBySrv(int value) {

        conEncBySrv_ = value;
        bitField3_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conEncBySrv = 105;</code>
       * @return This builder for chaining.
       */
      public Builder clearConEncBySrv() {
        bitField3_ = (bitField3_ & ~0x00000100);
        conEncBySrv_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString location_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes Location = 106;</code>
       * @return Whether the location field is set.
       */
      @java.lang.Override
      public boolean hasLocation() {
        return ((bitField3_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes Location = 106;</code>
       * @return The location.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getLocation() {
        return location_;
      }
      /**
       * <code>optional bytes Location = 106;</code>
       * @param value The location to set.
       * @return This builder for chaining.
       */
      public Builder setLocation(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        location_ = value;
        bitField3_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes Location = 106;</code>
       * @return This builder for chaining.
       */
      public Builder clearLocation() {
        bitField3_ = (bitField3_ & ~0x00000200);
        location_ = getDefaultInstance().getLocation();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString xSinHol_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes xSinHol = 107;</code>
       * @return Whether the xSinHol field is set.
       */
      @java.lang.Override
      public boolean hasXSinHol() {
        return ((bitField3_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes xSinHol = 107;</code>
       * @return The xSinHol.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getXSinHol() {
        return xSinHol_;
      }
      /**
       * <code>optional bytes xSinHol = 107;</code>
       * @param value The xSinHol to set.
       * @return This builder for chaining.
       */
      public Builder setXSinHol(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        xSinHol_ = value;
        bitField3_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes xSinHol = 107;</code>
       * @return This builder for chaining.
       */
      public Builder clearXSinHol() {
        bitField3_ = (bitField3_ & ~0x00000400);
        xSinHol_ = getDefaultInstance().getXSinHol();
        onChanged();
        return this;
      }

      private int conEncBySrvCnt_ ;
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       * @return Whether the conEncBySrvCnt field is set.
       */
      @java.lang.Override
      public boolean hasConEncBySrvCnt() {
        return ((bitField3_ & 0x00000800) != 0);
      }
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       * @return The conEncBySrvCnt.
       */
      @java.lang.Override
      public int getConEncBySrvCnt() {
        return conEncBySrvCnt_;
      }
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       * @param value The conEncBySrvCnt to set.
       * @return This builder for chaining.
       */
      public Builder setConEncBySrvCnt(int value) {

        conEncBySrvCnt_ = value;
        bitField3_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conEncBySrvCnt = 108;</code>
       * @return This builder for chaining.
       */
      public Builder clearConEncBySrvCnt() {
        bitField3_ = (bitField3_ & ~0x00000800);
        conEncBySrvCnt_ = 0;
        onChanged();
        return this;
      }

      private int conLenSrv_ ;
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       * @return Whether the conLenSrv field is set.
       */
      @java.lang.Override
      public boolean hasConLenSrv() {
        return ((bitField3_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       * @return The conLenSrv.
       */
      @java.lang.Override
      public int getConLenSrv() {
        return conLenSrv_;
      }
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       * @param value The conLenSrv to set.
       * @return This builder for chaining.
       */
      public Builder setConLenSrv(int value) {

        conLenSrv_ = value;
        bitField3_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conLenSrv = 109;</code>
       * @return This builder for chaining.
       */
      public Builder clearConLenSrv() {
        bitField3_ = (bitField3_ & ~0x00001000);
        conLenSrv_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conDispUp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conDispUp = 110;</code>
       * @return Whether the conDispUp field is set.
       */
      @java.lang.Override
      public boolean hasConDispUp() {
        return ((bitField3_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes conDispUp = 110;</code>
       * @return The conDispUp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConDispUp() {
        return conDispUp_;
      }
      /**
       * <code>optional bytes conDispUp = 110;</code>
       * @param value The conDispUp to set.
       * @return This builder for chaining.
       */
      public Builder setConDispUp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conDispUp_ = value;
        bitField3_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conDispUp = 110;</code>
       * @return This builder for chaining.
       */
      public Builder clearConDispUp() {
        bitField3_ = (bitField3_ & ~0x00002000);
        conDispUp_ = getDefaultInstance().getConDispUp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conDispDown_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conDispDown = 111;</code>
       * @return Whether the conDispDown field is set.
       */
      @java.lang.Override
      public boolean hasConDispDown() {
        return ((bitField3_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes conDispDown = 111;</code>
       * @return The conDispDown.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConDispDown() {
        return conDispDown_;
      }
      /**
       * <code>optional bytes conDispDown = 111;</code>
       * @param value The conDispDown to set.
       * @return This builder for chaining.
       */
      public Builder setConDispDown(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conDispDown_ = value;
        bitField3_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conDispDown = 111;</code>
       * @return This builder for chaining.
       */
      public Builder clearConDispDown() {
        bitField3_ = (bitField3_ & ~0x00004000);
        conDispDown_ = getDefaultInstance().getConDispDown();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authUser_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authUser = 112;</code>
       * @return Whether the authUser field is set.
       */
      @java.lang.Override
      public boolean hasAuthUser() {
        return ((bitField3_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes authUser = 112;</code>
       * @return The authUser.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAuthUser() {
        return authUser_;
      }
      /**
       * <code>optional bytes authUser = 112;</code>
       * @param value The authUser to set.
       * @return This builder for chaining.
       */
      public Builder setAuthUser(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        authUser_ = value;
        bitField3_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authUser = 112;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthUser() {
        bitField3_ = (bitField3_ & ~0x00008000);
        authUser_ = getDefaultInstance().getAuthUser();
        onChanged();
        return this;
      }

      private int authUserCount_ ;
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       * @return Whether the authUserCount field is set.
       */
      @java.lang.Override
      public boolean hasAuthUserCount() {
        return ((bitField3_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       * @return The authUserCount.
       */
      @java.lang.Override
      public int getAuthUserCount() {
        return authUserCount_;
      }
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       * @param value The authUserCount to set.
       * @return This builder for chaining.
       */
      public Builder setAuthUserCount(int value) {

        authUserCount_ = value;
        bitField3_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 authUserCount = 113;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthUserCount() {
        bitField3_ = (bitField3_ & ~0x00010000);
        authUserCount_ = 0;
        onChanged();
        return this;
      }

      private int bodyServerMd5Count_ ;
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       * @return Whether the bodyServerMd5Count field is set.
       */
      @java.lang.Override
      public boolean hasBodyServerMd5Count() {
        return ((bitField3_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       * @return The bodyServerMd5Count.
       */
      @java.lang.Override
      public int getBodyServerMd5Count() {
        return bodyServerMd5Count_;
      }
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       * @param value The bodyServerMd5Count to set.
       * @return This builder for chaining.
       */
      public Builder setBodyServerMd5Count(int value) {

        bodyServerMd5Count_ = value;
        bitField3_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 bodyServerMd5Count = 114;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyServerMd5Count() {
        bitField3_ = (bitField3_ & ~0x00020000);
        bodyServerMd5Count_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString contentDispositionClient_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       * @return Whether the contentDispositionClient field is set.
       */
      @java.lang.Override
      public boolean hasContentDispositionClient() {
        return ((bitField3_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       * @return The contentDispositionClient.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getContentDispositionClient() {
        return contentDispositionClient_;
      }
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       * @param value The contentDispositionClient to set.
       * @return This builder for chaining.
       */
      public Builder setContentDispositionClient(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        contentDispositionClient_ = value;
        bitField3_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes contentDispositionClient = 115;</code>
       * @return This builder for chaining.
       */
      public Builder clearContentDispositionClient() {
        bitField3_ = (bitField3_ & ~0x00040000);
        contentDispositionClient_ = getDefaultInstance().getContentDispositionClient();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString contentDispositionServer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       * @return Whether the contentDispositionServer field is set.
       */
      @java.lang.Override
      public boolean hasContentDispositionServer() {
        return ((bitField3_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       * @return The contentDispositionServer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getContentDispositionServer() {
        return contentDispositionServer_;
      }
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       * @param value The contentDispositionServer to set.
       * @return This builder for chaining.
       */
      public Builder setContentDispositionServer(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        contentDispositionServer_ = value;
        bitField3_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes contentDispositionServer = 116;</code>
       * @return This builder for chaining.
       */
      public Builder clearContentDispositionServer() {
        bitField3_ = (bitField3_ & ~0x00080000);
        contentDispositionServer_ = getDefaultInstance().getContentDispositionServer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString filePath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes filePath = 117;</code>
       * @return Whether the filePath field is set.
       */
      @java.lang.Override
      public boolean hasFilePath() {
        return ((bitField3_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes filePath = 117;</code>
       * @return The filePath.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFilePath() {
        return filePath_;
      }
      /**
       * <code>optional bytes filePath = 117;</code>
       * @param value The filePath to set.
       * @return This builder for chaining.
       */
      public Builder setFilePath(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        filePath_ = value;
        bitField3_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes filePath = 117;</code>
       * @return This builder for chaining.
       */
      public Builder clearFilePath() {
        bitField3_ = (bitField3_ & ~0x00100000);
        filePath_ = getDefaultInstance().getFilePath();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString setCookie_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes setCookie = 118;</code>
       * @return Whether the setCookie field is set.
       */
      @java.lang.Override
      public boolean hasSetCookie() {
        return ((bitField3_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes setCookie = 118;</code>
       * @return The setCookie.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSetCookie() {
        return setCookie_;
      }
      /**
       * <code>optional bytes setCookie = 118;</code>
       * @param value The setCookie to set.
       * @return This builder for chaining.
       */
      public Builder setSetCookie(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        setCookie_ = value;
        bitField3_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes setCookie = 118;</code>
       * @return This builder for chaining.
       */
      public Builder clearSetCookie() {
        bitField3_ = (bitField3_ & ~0x00200000);
        setCookie_ = getDefaultInstance().getSetCookie();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString title_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes title = 119;</code>
       * @return Whether the title field is set.
       */
      @java.lang.Override
      public boolean hasTitle() {
        return ((bitField3_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes title = 119;</code>
       * @return The title.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTitle() {
        return title_;
      }
      /**
       * <code>optional bytes title = 119;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        title_ = value;
        bitField3_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes title = 119;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        bitField3_ = (bitField3_ & ~0x00400000);
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:HttpInfo)
    }

    // @@protoc_insertion_point(class_scope:HttpInfo)
    private static final HttpInfoOuterClass.HttpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new HttpInfoOuterClass.HttpInfo();
    }

    public static HttpInfoOuterClass.HttpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<HttpInfo>
        PARSER = new com.google.protobuf.AbstractParser<HttpInfo>() {
      @java.lang.Override
      public HttpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<HttpInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<HttpInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public HttpInfoOuterClass.HttpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_HttpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_HttpInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016HttpInfo.proto\"\357\020\n\010HttpInfo\022\014\n\004host\030\001 " +
      "\001(\014\022\013\n\003uri\030\002 \001(\014\022\021\n\tvarConEnc\030\003 \001(\014\022\020\n\010a" +
      "uthInfo\030\004 \001(\014\022\023\n\013conEncByCli\030\005 \001(\014\022\016\n\006co" +
      "nLan\030\006 \001(\014\022\023\n\013conLenByCli\030\007 \001(\r\022\016\n\006conUR" +
      "L\030\010 \001(\014\022\016\n\006conMD5\030\t \001(\014\022\017\n\007conType\030\n \001(\014" +
      "\022\016\n\006cookie\030\013 \001(\014\022\017\n\007cookie2\030\014 \001(\014\022\014\n\004dat" +
      "e\030\r \001(\014\022\014\n\004from\030\016 \001(\014\022\013\n\003loc\030\017 \001(\014\022\021\n\tpr" +
      "oAuthen\030\020 \001(\014\022\021\n\tproAuthor\030\021 \001(\014\022\016\n\006refU" +
      "RL\030\022 \001(\014\022\013\n\003srv\030\023 \001(\014\022\016\n\006srvCnt\030\024 \001(\r\022\024\n" +
      "\014setCookieKey\030\025 \001(\014\022\024\n\014setCookieVal\030\026 \001(" +
      "\014\022\016\n\006traEnc\030\027 \001(\014\022\016\n\006usrAge\030\030 \001(\014\022\013\n\003via" +
      "\030\031 \001(\014\022\017\n\007xForFor\030\032 \001(\014\022\020\n\010statCode\030\033 \001(" +
      "\r\022\013\n\003met\030\034 \001(\014\022\016\n\006srvAge\030\035 \001(\014\022\017\n\007proAut" +
      "h\030\036 \001(\014\022\016\n\006xPowBy\030\037 \001(\014\022\017\n\007extHdrs\030  \001(\014" +
      "\022\022\n\nrangeofCli\030! \001(\014\022\016\n\006viaCnt\030\" \001(\r\022\023\n\013" +
      "statCodeCnt\030# \001(\r\022\016\n\006reqVer\030$ \001(\014\022\017\n\007req" +
      "Head\030% \001(\014\022\022\n\nreqHeadMd5\030& \001(\r\022\020\n\010cacCon" +
      "Up\030\' \001(\014\022\r\n\005conUp\030( \001(\014\022\r\n\005praUp\030) \001(\014\022\013" +
      "\n\003upg\030* \001(\014\022\020\n\010accChaUp\030+ \001(\014\022\021\n\tacctRan" +
      "Up\030, \001(\014\022\r\n\005ifMat\030- \001(\014\022\020\n\010ifModSin\030. \001(" +
      "\014\022\020\n\010ifNonMat\030/ \001(\014\022\r\n\005ifRan\0300 \001(\014\022\022\n\nif" +
      "UnModSin\0301 \001(\004\022\016\n\006maxFor\0302 \001(\r\022\n\n\002te\0303 \001" +
      "(\014\022\022\n\ncacConDown\0304 \001(\014\022\017\n\007conDown\0305 \001(\014\022" +
      "\017\n\007praDown\0306 \001(\014\022\r\n\005trail\0307 \001(\014\022\022\n\naccRa" +
      "nDown\0308 \001(\014\022\014\n\004eTag\0309 \001(\014\022\016\n\006retAft\030: \001(" +
      "\014\022\017\n\007wwwAuth\030; \001(\014\022\017\n\007refresh\030< \001(\014\022\022\n\nc" +
      "onTypDown\030= \001(\014\022\r\n\005allow\030> \001(\014\022\017\n\007expire" +
      "s\030? \001(\004\022\016\n\006lasMod\030@ \001(\004\022\022\n\naccChaDown\030A " +
      "\001(\014\022\022\n\nhttpRelKey\030B \001(\014\022\022\n\nhttpEmbPro\030C " +
      "\001(\014\022\026\n\016fullTextHeader\030D \001(\014\022\023\n\013fullTextL" +
      "en\030E \001(\r\022\020\n\010fileName\030F \001(\014\022\020\n\010contDown\030G" +
      " \001(\014\022\021\n\treqVerCnt\030H \001(\r\022\016\n\006metCnt\030I \001(\r\022" +
      "\022\n\nreqHeadCnt\030J \001(\r\022\020\n\010accByCli\030K \001(\014\022\023\n" +
      "\013accLanByCli\030L \001(\014\022\023\n\013accEncByCli\030M \001(\014\022" +
      "\017\n\007authCnt\030N \001(\r\022\017\n\007hostCnt\030O \001(\r\022\016\n\006uri" +
      "Cnt\030P \001(\r\022\017\n\007uriPath\030Q \001(\014\022\022\n\nuriPathCnt" +
      "\030R \001(\r\022\016\n\006uriKey\030S \003(\014\022\021\n\turiKeyCnt\030T \001(" +
      "\r\022\021\n\turiSearch\030U \001(\014\022\021\n\tusrAgeCnt\030V \001(\r\022" +
      "\014\n\004user\030W \001(\014\022\017\n\007userCnt\030X \001(\r\022\017\n\007reqBod" +
      "y\030Y \001(\014\022\020\n\010reqBodyN\030Z \001(\014\022\023\n\013conMD5ByCli" +
      "\030[ \001(\014\022\021\n\tcookieKey\030\\ \003(\014\022\024\n\014cookieKeyCn" +
      "t\030] \001(\r\022\014\n\004imei\030^ \001(\014\022\014\n\004imsi\030_ \001(\014\022\022\n\nx" +
      "ForForCnt\030` \001(\r\022\017\n\007respVer\030a \001(\014\022\022\n\nresp" +
      "VerCnt\030b \001(\r\022\020\n\010respHead\030c \001(\014\022\023\n\013respHe" +
      "adMd5\030d \001(\014\022\023\n\013respHeadCnt\030e \001(\r\022\020\n\010resp" +
      "Body\030f \001(\014\022\021\n\trespBodyN\030g \001(\014\022\023\n\013conMD5B" +
      "ySrv\030h \001(\014\022\023\n\013conEncBySrv\030i \001(\r\022\020\n\010Locat" +
      "ion\030j \001(\014\022\017\n\007xSinHol\030k \001(\014\022\026\n\016conEncBySr" +
      "vCnt\030l \001(\r\022\021\n\tconLenSrv\030m \001(\r\022\021\n\tconDisp" +
      "Up\030n \001(\014\022\023\n\013conDispDown\030o \001(\014\022\020\n\010authUse" +
      "r\030p \001(\014\022\025\n\rauthUserCount\030q \001(\r\022\032\n\022bodySe" +
      "rverMd5Count\030r \001(\r\022 \n\030contentDisposition" +
      "Client\030s \001(\014\022 \n\030contentDispositionServer" +
      "\030t \001(\014\022\020\n\010filePath\030u \001(\014\022\021\n\tsetCookie\030v " +
      "\001(\014\022\r\n\005title\030w \001(\014"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_HttpInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_HttpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_HttpInfo_descriptor,
        new java.lang.String[] { "Host", "Uri", "VarConEnc", "AuthInfo", "ConEncByCli", "ConLan", "ConLenByCli", "ConURL", "ConMD5", "ConType", "Cookie", "Cookie2", "Date", "From", "Loc", "ProAuthen", "ProAuthor", "RefURL", "Srv", "SrvCnt", "SetCookieKey", "SetCookieVal", "TraEnc", "UsrAge", "Via", "XForFor", "StatCode", "Met", "SrvAge", "ProAuth", "XPowBy", "ExtHdrs", "RangeofCli", "ViaCnt", "StatCodeCnt", "ReqVer", "ReqHead", "ReqHeadMd5", "CacConUp", "ConUp", "PraUp", "Upg", "AccChaUp", "AcctRanUp", "IfMat", "IfModSin", "IfNonMat", "IfRan", "IfUnModSin", "MaxFor", "Te", "CacConDown", "ConDown", "PraDown", "Trail", "AccRanDown", "ETag", "RetAft", "WwwAuth", "Refresh", "ConTypDown", "Allow", "Expires", "LasMod", "AccChaDown", "HttpRelKey", "HttpEmbPro", "FullTextHeader", "FullTextLen", "FileName", "ContDown", "ReqVerCnt", "MetCnt", "ReqHeadCnt", "AccByCli", "AccLanByCli", "AccEncByCli", "AuthCnt", "HostCnt", "UriCnt", "UriPath", "UriPathCnt", "UriKey", "UriKeyCnt", "UriSearch", "UsrAgeCnt", "User", "UserCnt", "ReqBody", "ReqBodyN", "ConMD5ByCli", "CookieKey", "CookieKeyCnt", "Imei", "Imsi", "XForForCnt", "RespVer", "RespVerCnt", "RespHead", "RespHeadMd5", "RespHeadCnt", "RespBody", "RespBodyN", "ConMD5BySrv", "ConEncBySrv", "Location", "XSinHol", "ConEncBySrvCnt", "ConLenSrv", "ConDispUp", "ConDispDown", "AuthUser", "AuthUserCount", "BodyServerMd5Count", "ContentDispositionClient", "ContentDispositionServer", "FilePath", "SetCookie", "Title", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
