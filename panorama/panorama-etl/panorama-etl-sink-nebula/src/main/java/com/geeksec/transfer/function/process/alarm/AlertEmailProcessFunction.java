package com.geeksec.transfer.function.process.alarm;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.EmailAlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertEmailProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {

    private static final Logger logger = LoggerFactory.getLogger(AlertEmailProcessFunction.class);

    /**
     * 需要提取的点边关系
     * 点：
     * MAIL
     * EMAIL
     * 边：
     * send_mail
     * receive_mail
     * include_file
     * sender_send_file
     * receiver_receive_file
     * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, Context ctx, Collector<Row> collector) {
        try {
            EmailAlertTrans emailAlert = AlertTools.createEmailAlertTrans(alertLog);
            if (emailAlert != null) {
                List<Row> rows = emailAlert.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing Email alert log: {}", alertLog.getGuid(), e);
        }
    }
}