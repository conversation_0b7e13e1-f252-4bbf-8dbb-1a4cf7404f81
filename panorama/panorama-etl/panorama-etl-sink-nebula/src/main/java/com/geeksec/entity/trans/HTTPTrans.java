package com.geeksec.entity.trans;

import com.google.protobuf.ByteString;
import lombok.Data;

import java.util.List;

@Data
public class HTTPTrans {
    private String setCookie;
    private String agent;
    private String hostMd5;
    private String uriMd5;
    private String contentType;
    private long status;

    // HTTP 方法（如 GET, POST, PUT, DELETE）
    private String method;

    // 完整的请求 URI
    private String uri;

    // 请求 URI 的键部分
    private List<String> uriKey;

    // 请求 URI 的路径部分
    private String uriPath;

    // 源请求的 HTTP 版本（如 HTTP/1.1）
    private String versionSrc;

    // 请求的主机名
    private String host;

    // 客户端的用户代理字符串
    private String userAgent;

    // 来源地址
    private String referer;

    // 客户端支持的内容编码
    private String acceptEncoding;

    // 客户端支持的语言类型
    private String acceptLanguage;

    // 用于身份验证的凭据
    private String authorization;

    // 请求体的内容类型
    private String contentTypeReq;

    // X-Forwarded-For 头部，原始客户端的 IP 地址
    private String xffIp;

    // 请求的发起者
    private String from;

    // 请求体的长度
    private Integer contentLengthReq;

    // 发送的 Cookie 字符串
    private String cookie;

    // Cookie 的键
    private String cookieKey;

    // 代理服务器的身份验证信息
    private String proxyAuth;

    // 客户端支持的内容类型
    private String acceptC;

    // 请求的字节范围
    private String rangeC;

    // 用户名
    private String user;

    // 密码
    private String password;

    // 请求头的键
    private String headKeyReq;

    // 请求体的 MD5 摘要
    private byte[] contentMd5Req;

    // 请求体的内容
    private String reqbody;

    // 响应的 HTTP 状态码
    private Integer statusCode;

    // 目标响应的 HTTP 版本
    private String versionDst;

    // 重定向地址
    private String location;

    // 服务器信息
    private String server;

    // 代理链信息
    private String via;

    // 请求发起的时间戳
    private Long date;

    // 身份验证是否成功的标志
    private Integer authOk;

    // 响应的传输编码
    private String transferEncoding;

    // 响应体的内容类型
    private String contentTypeResp;

    // 响应体的内容语言
    private String contentLanguage;

    // 响应体的长度
    private Integer contentLengthResp;

    // 服务器所使用的技术或框架信息
    private String xPoweredBy;

    // 指示响应的可变性
    private String vary;

    // 用于捕获和处理请求的机制
    private String sinkhole;

    // 响应体的内容编码
    private String contentEncodingResp;

    // 响应头的键
    private String headKeyResp;

    // 设置的 Cookie 的键
    private String setCookieKey;

    // 设置的 Cookie 的值
    private String setCookieVal;

    // 指示如何处理响应内容的头部
    private String contentDisposition;

    // 附件的名称
    private String attachName;

    // 附件的内容
    private byte[] attachContent;

    // 响应体的 MD5 摘要
    private byte[] contentMd5Resp;

    // 响应体的内容
    private String respbody;
}
