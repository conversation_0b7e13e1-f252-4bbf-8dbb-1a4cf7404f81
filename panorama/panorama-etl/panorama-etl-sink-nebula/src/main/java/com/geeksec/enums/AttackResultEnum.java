package com.geeksec.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 攻击结果
 *
 */
public enum AttackResultEnum {
    ATTEMPT(1, "企图"),
    SUCCESS(2, "成功"),
    FALL(3, "失陷"),
    FAILURE(4, "失败");

    /**
     * 攻击结果
     */
    private Integer code;

    /**
     * 取值
     */
    private String msg;

    AttackResultEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, AttackResultEnum> codeEnumMap = new HashMap<>();

    static {
        for (AttackResultEnum e : AttackResultEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        AttackResultEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }
}
