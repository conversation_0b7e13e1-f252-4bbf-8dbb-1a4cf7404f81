package com.geeksec.entity.pojo;

import com.geeksec.config.util.MD5;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.entity.trans.SslTrans;
import lombok.Data;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SslAlertTrans {
    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private List<SslTrans> ssl;

    private String aptName;

    public List<Row> getAllRows() {
        List<Row> rows = new ArrayList<>();

        for(SslTrans sslTrans:ssl){
            rows.addAll(getSslRelatedRows(sslTrans));
        }
        return rows;
    }

    private Collection<Row> getSslRelatedRows(SslTrans sslTrans) {

        String sni = sslTrans.getSrvName();
        String clientCert = sslTrans.getCliCertHashes();
        String serverCert = sslTrans.getSrvCertHashes();

        String clientFinger = sslTrans.getCliJa3();
        String serverFinger = sslTrans.getSrvJa3();

        List<Row> sslRelatedRows = new ArrayList<>();

        // DOMAIN TAG
        Row domainRow = new Row(3);
        domainRow.setField(0, "TAG_DOMAIN");
        domainRow.setField(1, MD5.getMd5(sni));
        domainRow.setField(2, sni);
        sslRelatedRows.add(domainRow);

        // SSLFINGER TAG
        Row clientFingerRow = new Row(2);
        clientFingerRow.setField(0, "TAG_SSLFINGER");
        clientFingerRow.setField(1, serverFinger);
        sslRelatedRows.add(clientFingerRow);
        Row serverFingerRow = new Row(2);
        serverFingerRow.setField(0, "TAG_SSLFINGER");
        serverFingerRow.setField(1, serverFinger);
        sslRelatedRows.add(serverFingerRow);

        // domain_belong_to_apt edge
        if(aptName != null && !aptName.isEmpty()){
            Row row = new Row(4);
            row.setField(0, "EDGE_domain_belong_to_apt");
            row.setField(1, MD5.getMd5(sni));
            row.setField(2, aptName);
            row.setField(3, 0);
            sslRelatedRows.add(row);
        }

        // cert_connect_sni
        Row certConnectSniRow = new Row(4);
        certConnectSniRow.setField(0, "EDGE_cert_connect_sni");
        certConnectSniRow.setField(1, serverCert);
        certConnectSniRow.setField(2, MD5.getMd5(sni));
        certConnectSniRow.setField(3, 0);
        sslRelatedRows.add(certConnectSniRow);

        // cert_connect_sslfinger
        Row certConnectSslfingerRow = new Row(4);
        certConnectSslfingerRow.setField(0, "EDGE_cert_connect_sslfinger");
        certConnectSslfingerRow.setField(1, serverCert);
        certConnectSslfingerRow.setField(2, serverFinger);
        certConnectSslfingerRow.setField(3, 0);
        sslRelatedRows.add(certConnectSslfingerRow);

        // client_use_sslfinger
        Row clientUseSslfingerRow = new Row(4);
        clientUseSslfingerRow.setField(0, "EDGE_client_use_sslfinger");
        clientUseSslfingerRow.setField(1, sip.getIPAddr());
        clientUseSslfingerRow.setField(2, clientFinger);
        clientUseSslfingerRow.setField(3, 0);
        sslRelatedRows.add(clientUseSslfingerRow);

        // server_use_sslfinger
        Row serverUseSslfingerRow = new Row(4);
        serverUseSslfingerRow.setField(0, "EDGE_server_use_sslfinger");
        serverUseSslfingerRow.setField(1, dip.getIPAddr());
        serverUseSslfingerRow.setField(2, serverFinger);
        serverUseSslfingerRow.setField(3, 0);
        sslRelatedRows.add(serverUseSslfingerRow);

        // sslfinger_connect_sni
        Row sslfingerConnectSniRow = new Row(4);
        sslfingerConnectSniRow.setField(0, "EDGE_sslfinger_connect_sni");
        sslfingerConnectSniRow.setField(1, serverFinger);
        sslfingerConnectSniRow.setField(2, MD5.getMd5(sni));
        sslfingerConnectSniRow.setField(3, 0);
        sslRelatedRows.add(sslfingerConnectSniRow);

        return sslRelatedRows;
    }


}
