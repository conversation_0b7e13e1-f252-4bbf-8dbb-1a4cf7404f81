package com.geeksec.transfer.function.process.protocol;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.DNSAlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertDnsProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {

    private static final Logger logger = LoggerFactory.getLogger(AlertDnsProcessFunction.class);


    /**
     * 需要提取的点边关系
     * 点：
     * DOMAIN: domain_addr,STRING,域名地址
     * 边：
     * parse_to: Client_IP
     * client_query_dns_server
     * dns_server_resolves_domain
     * client_query_domain
     * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, Context ctx, Collector<Row> collector) {
        try {
            DNSAlertTrans dnsAlert = AlertTools.createDnsAlertTrans(alertLog);
            if (dnsAlert != null) {
                List<Row> rows = dnsAlert.getAllRows();
                for (Row row : rows){
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing DNS alert log: {}", alertLog.getGuid(), e);
        }
    }
}