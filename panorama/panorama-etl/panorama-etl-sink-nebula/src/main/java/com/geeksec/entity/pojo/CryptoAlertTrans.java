package com.geeksec.entity.pojo;

import com.geeksec.config.util.MD5;
import com.geeksec.entity.trans.IPTrans;
import lombok.Data;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CryptoAlertTrans {

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private String certHash;

    private String appName;
    private int appTypeId;
    private String appType;
    private int appClassId;
    private String appClass;

    private boolean isEncrypted ;



    public List<Row> getAllRows() {

        // 统一录入IP相关节点&边信息
        List<Row> rows = new ArrayList<>();

        // APP TAG
        Row aptTagRow = new Row(8);
        aptTagRow.setField(0, "TAG_APP");
        aptTagRow.setField(1, MD5.getMd5(appName));
        aptTagRow.setField(2, appName);
        aptTagRow.setField(3, appTypeId);
        aptTagRow.setField(4, appType);
        aptTagRow.setField(5, appClassId);
        aptTagRow.setField(6, appClass);
        aptTagRow.setField(7, isEncrypted);
        rows.add(aptTagRow);

        // app_connect_cert EDGE
        Row appConnectCertRow = new Row(4);
        appConnectCertRow.setField(0, "EDGE_app_connect_cert");
        appConnectCertRow.setField(1, MD5.getMd5(appName));
        appConnectCertRow.setField(2, certHash);
        appConnectCertRow.setField(3, 0);
        rows.add(appConnectCertRow);

        // client_use_app EDGE
        Row clientUseAppRow = new Row(4);
        clientUseAppRow.setField(0, "EDGE_client_use_app");
        clientUseAppRow.setField(1, sip.getIPAddr());
        clientUseAppRow.setField(2, MD5.getMd5(appName));
        clientUseAppRow.setField(3, 0);
        rows.add(clientUseAppRow);

        // app_belong_to_server EDGE
        Row appBelongToServerRow = new Row(4);
        appBelongToServerRow.setField(0, "EDGE_app_belong_to_server");
        appBelongToServerRow.setField(1, MD5.getMd5(appName));
        appBelongToServerRow.setField(2, dip.getIPAddr());
        appBelongToServerRow.setField(3, 0);
        rows.add(appBelongToServerRow);

        return rows;
    }
}
