package com.geeksec.proto.protocol;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: CommonInfo.proto
// Protobuf Java Version: 4.29.4

public final class CommonInfoOuterClass {
  private CommonInfoOuterClass() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      CommonInfoOuterClass.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CommonInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CommonInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes srcMacOui = 1;</code>
     * @return Whether the srcMacOui field is set.
     */
    boolean hasSrcMacOui();
    /**
     * <code>optional bytes srcMacOui = 1;</code>
     * @return The srcMacOui.
     */
    com.google.protobuf.ByteString getSrcMacOui();

    /**
     * <code>optional bytes dstMacOui = 2;</code>
     * @return Whether the dstMacOui field is set.
     */
    boolean hasDstMacOui();
    /**
     * <code>optional bytes dstMacOui = 2;</code>
     * @return The dstMacOui.
     */
    com.google.protobuf.ByteString getDstMacOui();

    /**
     * <code>optional bytes lineName1 = 3;</code>
     * @return Whether the lineName1 field is set.
     */
    boolean hasLineName1();
    /**
     * <code>optional bytes lineName1 = 3;</code>
     * @return The lineName1.
     */
    com.google.protobuf.ByteString getLineName1();

    /**
     * <code>optional bytes lineName2 = 4;</code>
     * @return Whether the lineName2 field is set.
     */
    boolean hasLineName2();
    /**
     * <code>optional bytes lineName2 = 4;</code>
     * @return The lineName2.
     */
    com.google.protobuf.ByteString getLineName2();

    /**
     * <code>optional uint64 begTime = 5;</code>
     * @return Whether the begTime field is set.
     */
    boolean hasBegTime();
    /**
     * <code>optional uint64 begTime = 5;</code>
     * @return The begTime.
     */
    long getBegTime();

    /**
     * <code>optional bytes tagsRule = 6;</code>
     * @return Whether the tagsRule field is set.
     */
    boolean hasTagsRule();
    /**
     * <code>optional bytes tagsRule = 6;</code>
     * @return The tagsRule.
     */
    com.google.protobuf.ByteString getTagsRule();

    /**
     * <code>optional uint64 endTime = 7;</code>
     * @return Whether the endTime field is set.
     */
    boolean hasEndTime();
    /**
     * <code>optional uint64 endTime = 7;</code>
     * @return The endTime.
     */
    long getEndTime();

    /**
     * <code>optional uint32 comDur = 8;</code>
     * @return Whether the comDur field is set.
     */
    boolean hasComDur();
    /**
     * <code>optional uint32 comDur = 8;</code>
     * @return The comDur.
     */
    int getComDur();

    /**
     * <code>optional bytes meanID = 9;</code>
     * @return Whether the meanID field is set.
     */
    boolean hasMeanID();
    /**
     * <code>optional bytes meanID = 9;</code>
     * @return The meanID.
     */
    com.google.protobuf.ByteString getMeanID();

    /**
     * <code>optional bytes siteID = 10;</code>
     * @return Whether the siteID field is set.
     */
    boolean hasSiteID();
    /**
     * <code>optional bytes siteID = 10;</code>
     * @return The siteID.
     */
    com.google.protobuf.ByteString getSiteID();

    /**
     * <code>optional bytes unitID = 11;</code>
     * @return Whether the unitID field is set.
     */
    boolean hasUnitID();
    /**
     * <code>optional bytes unitID = 11;</code>
     * @return The unitID.
     */
    com.google.protobuf.ByteString getUnitID();

    /**
     * <code>optional bytes taskID = 12;</code>
     * @return Whether the taskID field is set.
     */
    boolean hasTaskID();
    /**
     * <code>optional bytes taskID = 12;</code>
     * @return The taskID.
     */
    com.google.protobuf.ByteString getTaskID();

    /**
     * <code>optional uint64 guid = 13;</code>
     * @return Whether the guid field is set.
     */
    boolean hasGuid();
    /**
     * <code>optional uint64 guid = 13;</code>
     * @return The guid.
     */
    long getGuid();

    /**
     * <code>optional uint64 stortime = 14;</code>
     * @return Whether the stortime field is set.
     */
    boolean hasStortime();
    /**
     * <code>optional uint64 stortime = 14;</code>
     * @return The stortime.
     */
    long getStortime();

    /**
     * <code>optional bytes mdsecdeg = 15;</code>
     * @return Whether the mdsecdeg field is set.
     */
    boolean hasMdsecdeg();
    /**
     * <code>optional bytes mdsecdeg = 15;</code>
     * @return The mdsecdeg.
     */
    com.google.protobuf.ByteString getMdsecdeg();

    /**
     * <code>optional bytes filesecdeg = 16;</code>
     * @return Whether the filesecdeg field is set.
     */
    boolean hasFilesecdeg();
    /**
     * <code>optional bytes filesecdeg = 16;</code>
     * @return The filesecdeg.
     */
    com.google.protobuf.ByteString getFilesecdeg();

    /**
     * <code>optional bytes secdegpro = 17;</code>
     * @return Whether the secdegpro field is set.
     */
    boolean hasSecdegpro();
    /**
     * <code>optional bytes secdegpro = 17;</code>
     * @return The secdegpro.
     */
    com.google.protobuf.ByteString getSecdegpro();

    /**
     * <code>optional uint32 commipVer = 18;</code>
     * @return Whether the commipVer field is set.
     */
    boolean hasCommipVer();
    /**
     * <code>optional uint32 commipVer = 18;</code>
     * @return The commipVer.
     */
    int getCommipVer();

    /**
     * <code>optional uint64 commsrcAddr = 19;</code>
     * @return Whether the commsrcAddr field is set.
     */
    boolean hasCommsrcAddr();
    /**
     * <code>optional uint64 commsrcAddr = 19;</code>
     * @return The commsrcAddr.
     */
    long getCommsrcAddr();

    /**
     * <code>optional uint64 commdstAddr = 20;</code>
     * @return Whether the commdstAddr field is set.
     */
    boolean hasCommdstAddr();
    /**
     * <code>optional uint64 commdstAddr = 20;</code>
     * @return The commdstAddr.
     */
    long getCommdstAddr();

    /**
     * <code>optional uint32 commsrcPort = 21;</code>
     * @return Whether the commsrcPort field is set.
     */
    boolean hasCommsrcPort();
    /**
     * <code>optional uint32 commsrcPort = 21;</code>
     * @return The commsrcPort.
     */
    int getCommsrcPort();

    /**
     * <code>optional uint32 commdstPort = 22;</code>
     * @return Whether the commdstPort field is set.
     */
    boolean hasCommdstPort();
    /**
     * <code>optional uint32 commdstPort = 22;</code>
     * @return The commdstPort.
     */
    int getCommdstPort();

    /**
     * <code>optional uint32 commprotNum = 23;</code>
     * @return Whether the commprotNum field is set.
     */
    boolean hasCommprotNum();
    /**
     * <code>optional uint32 commprotNum = 23;</code>
     * @return The commprotNum.
     */
    int getCommprotNum();

    /**
     * <code>optional bytes commsrcAddrV6 = 24;</code>
     * @return Whether the commsrcAddrV6 field is set.
     */
    boolean hasCommsrcAddrV6();
    /**
     * <code>optional bytes commsrcAddrV6 = 24;</code>
     * @return The commsrcAddrV6.
     */
    com.google.protobuf.ByteString getCommsrcAddrV6();

    /**
     * <code>optional bytes commdstAddrV6 = 25;</code>
     * @return Whether the commdstAddrV6 field is set.
     */
    boolean hasCommdstAddrV6();
    /**
     * <code>optional bytes commdstAddrV6 = 25;</code>
     * @return The commdstAddrV6.
     */
    com.google.protobuf.ByteString getCommdstAddrV6();

    /**
     * <code>optional bytes commprotInfo = 26;</code>
     * @return Whether the commprotInfo field is set.
     */
    boolean hasCommprotInfo();
    /**
     * <code>optional bytes commprotInfo = 26;</code>
     * @return The commprotInfo.
     */
    com.google.protobuf.ByteString getCommprotInfo();

    /**
     * <code>optional bytes commprotType = 27;</code>
     * @return Whether the commprotType field is set.
     */
    boolean hasCommprotType();
    /**
     * <code>optional bytes commprotType = 27;</code>
     * @return The commprotType.
     */
    com.google.protobuf.ByteString getCommprotType();

    /**
     * <code>optional bytes commprotName = 28;</code>
     * @return Whether the commprotName field is set.
     */
    boolean hasCommprotName();
    /**
     * <code>optional bytes commprotName = 28;</code>
     * @return The commprotName.
     */
    com.google.protobuf.ByteString getCommprotName();

    /**
     * <code>optional uint32 commmulRouFlag = 29;</code>
     * @return Whether the commmulRouFlag field is set.
     */
    boolean hasCommmulRouFlag();
    /**
     * <code>optional uint32 commmulRouFlag = 29;</code>
     * @return The commmulRouFlag.
     */
    int getCommmulRouFlag();

    /**
     * <code>optional uint32 commintFlag = 30;</code>
     * @return Whether the commintFlag field is set.
     */
    boolean hasCommintFlag();
    /**
     * <code>optional uint32 commintFlag = 30;</code>
     * @return The commintFlag.
     */
    int getCommintFlag();

    /**
     * <code>optional uint32 commstrDirec = 31;</code>
     * @return Whether the commstrDirec field is set.
     */
    boolean hasCommstrDirec();
    /**
     * <code>optional uint32 commstrDirec = 31;</code>
     * @return The commstrDirec.
     */
    int getCommstrDirec();

    /**
     * <code>optional uint32 commpktNum = 32;</code>
     * @return Whether the commpktNum field is set.
     */
    boolean hasCommpktNum();
    /**
     * <code>optional uint32 commpktNum = 32;</code>
     * @return The commpktNum.
     */
    int getCommpktNum();

    /**
     * <code>optional uint64 commpayLen = 33;</code>
     * @return Whether the commpayLen field is set.
     */
    boolean hasCommpayLen();
    /**
     * <code>optional uint64 commpayLen = 33;</code>
     * @return The commpayLen.
     */
    long getCommpayLen();

    /**
     * <code>optional uint64 commstreamId = 34;</code>
     * @return Whether the commstreamId field is set.
     */
    boolean hasCommstreamId();
    /**
     * <code>optional uint64 commstreamId = 34;</code>
     * @return The commstreamId.
     */
    long getCommstreamId();

    /**
     * <code>optional bytes commetags = 35;</code>
     * @return Whether the commetags field is set.
     */
    boolean hasCommetags();
    /**
     * <code>optional bytes commetags = 35;</code>
     * @return The commetags.
     */
    com.google.protobuf.ByteString getCommetags();

    /**
     * <code>optional bytes commttags = 36;</code>
     * @return Whether the commttags field is set.
     */
    boolean hasCommttags();
    /**
     * <code>optional bytes commttags = 36;</code>
     * @return The commttags.
     */
    com.google.protobuf.ByteString getCommttags();

    /**
     * <code>optional bytes commatags = 37;</code>
     * @return Whether the commatags field is set.
     */
    boolean hasCommatags();
    /**
     * <code>optional bytes commatags = 37;</code>
     * @return The commatags.
     */
    com.google.protobuf.ByteString getCommatags();

    /**
     * <code>optional bytes commutags = 38;</code>
     * @return Whether the commutags field is set.
     */
    boolean hasCommutags();
    /**
     * <code>optional bytes commutags = 38;</code>
     * @return The commutags.
     */
    com.google.protobuf.ByteString getCommutags();

    /**
     * <code>optional uint32 commlable1 = 39;</code>
     * @return Whether the commlable1 field is set.
     */
    boolean hasCommlable1();
    /**
     * <code>optional uint32 commlable1 = 39;</code>
     * @return The commlable1.
     */
    int getCommlable1();

    /**
     * <code>optional uint32 commlable2 = 40;</code>
     * @return Whether the commlable2 field is set.
     */
    boolean hasCommlable2();
    /**
     * <code>optional uint32 commlable2 = 40;</code>
     * @return The commlable2.
     */
    int getCommlable2();

    /**
     * <code>optional uint32 commlable3 = 41;</code>
     * @return Whether the commlable3 field is set.
     */
    boolean hasCommlable3();
    /**
     * <code>optional uint32 commlable3 = 41;</code>
     * @return The commlable3.
     */
    int getCommlable3();

    /**
     * <code>optional uint32 commlable4 = 42;</code>
     * @return Whether the commlable4 field is set.
     */
    boolean hasCommlable4();
    /**
     * <code>optional uint32 commlable4 = 42;</code>
     * @return The commlable4.
     */
    int getCommlable4();

    /**
     * <code>optional uint32 commvlanID1 = 43;</code>
     * @return Whether the commvlanID1 field is set.
     */
    boolean hasCommvlanID1();
    /**
     * <code>optional uint32 commvlanID1 = 43;</code>
     * @return The commvlanID1.
     */
    int getCommvlanID1();

    /**
     * <code>optional uint32 commvlanID2 = 44;</code>
     * @return Whether the commvlanID2 field is set.
     */
    boolean hasCommvlanID2();
    /**
     * <code>optional uint32 commvlanID2 = 44;</code>
     * @return The commvlanID2.
     */
    int getCommvlanID2();

    /**
     * <code>optional bytes commsrcMac = 45;</code>
     * @return Whether the commsrcMac field is set.
     */
    boolean hasCommsrcMac();
    /**
     * <code>optional bytes commsrcMac = 45;</code>
     * @return The commsrcMac.
     */
    com.google.protobuf.ByteString getCommsrcMac();

    /**
     * <code>optional bytes commdstMac = 46;</code>
     * @return Whether the commdstMac field is set.
     */
    boolean hasCommdstMac();
    /**
     * <code>optional bytes commdstMac = 46;</code>
     * @return The commdstMac.
     */
    com.google.protobuf.ByteString getCommdstMac();

    /**
     * <code>optional uint32 commtunnelID = 47;</code>
     * @return Whether the commtunnelID field is set.
     */
    boolean hasCommtunnelID();
    /**
     * <code>optional uint32 commtunnelID = 47;</code>
     * @return The commtunnelID.
     */
    int getCommtunnelID();

    /**
     * <code>optional bytes commsrcCountry = 48;</code>
     * @return Whether the commsrcCountry field is set.
     */
    boolean hasCommsrcCountry();
    /**
     * <code>optional bytes commsrcCountry = 48;</code>
     * @return The commsrcCountry.
     */
    com.google.protobuf.ByteString getCommsrcCountry();

    /**
     * <code>optional bytes commsrcState = 49;</code>
     * @return Whether the commsrcState field is set.
     */
    boolean hasCommsrcState();
    /**
     * <code>optional bytes commsrcState = 49;</code>
     * @return The commsrcState.
     */
    com.google.protobuf.ByteString getCommsrcState();

    /**
     * <code>optional bytes commsrcCity = 50;</code>
     * @return Whether the commsrcCity field is set.
     */
    boolean hasCommsrcCity();
    /**
     * <code>optional bytes commsrcCity = 50;</code>
     * @return The commsrcCity.
     */
    com.google.protobuf.ByteString getCommsrcCity();

    /**
     * <code>optional float commsrcLongitude = 51;</code>
     * @return Whether the commsrcLongitude field is set.
     */
    boolean hasCommsrcLongitude();
    /**
     * <code>optional float commsrcLongitude = 51;</code>
     * @return The commsrcLongitude.
     */
    float getCommsrcLongitude();

    /**
     * <code>optional float commsrcLatitude = 52;</code>
     * @return Whether the commsrcLatitude field is set.
     */
    boolean hasCommsrcLatitude();
    /**
     * <code>optional float commsrcLatitude = 52;</code>
     * @return The commsrcLatitude.
     */
    float getCommsrcLatitude();

    /**
     * <code>optional bytes commsrcISP = 53;</code>
     * @return Whether the commsrcISP field is set.
     */
    boolean hasCommsrcISP();
    /**
     * <code>optional bytes commsrcISP = 53;</code>
     * @return The commsrcISP.
     */
    com.google.protobuf.ByteString getCommsrcISP();

    /**
     * <code>optional uint32 commsrcASN = 54;</code>
     * @return Whether the commsrcASN field is set.
     */
    boolean hasCommsrcASN();
    /**
     * <code>optional uint32 commsrcASN = 54;</code>
     * @return The commsrcASN.
     */
    int getCommsrcASN();

    /**
     * <code>optional bytes commdstCountry = 55;</code>
     * @return Whether the commdstCountry field is set.
     */
    boolean hasCommdstCountry();
    /**
     * <code>optional bytes commdstCountry = 55;</code>
     * @return The commdstCountry.
     */
    com.google.protobuf.ByteString getCommdstCountry();

    /**
     * <code>optional bytes commdstState = 56;</code>
     * @return Whether the commdstState field is set.
     */
    boolean hasCommdstState();
    /**
     * <code>optional bytes commdstState = 56;</code>
     * @return The commdstState.
     */
    com.google.protobuf.ByteString getCommdstState();

    /**
     * <code>optional bytes commdstCity = 57;</code>
     * @return Whether the commdstCity field is set.
     */
    boolean hasCommdstCity();
    /**
     * <code>optional bytes commdstCity = 57;</code>
     * @return The commdstCity.
     */
    com.google.protobuf.ByteString getCommdstCity();

    /**
     * <code>optional float commdstLongitude = 58;</code>
     * @return Whether the commdstLongitude field is set.
     */
    boolean hasCommdstLongitude();
    /**
     * <code>optional float commdstLongitude = 58;</code>
     * @return The commdstLongitude.
     */
    float getCommdstLongitude();

    /**
     * <code>optional float commdstLatitude = 59;</code>
     * @return Whether the commdstLatitude field is set.
     */
    boolean hasCommdstLatitude();
    /**
     * <code>optional float commdstLatitude = 59;</code>
     * @return The commdstLatitude.
     */
    float getCommdstLatitude();

    /**
     * <code>optional bytes commdstISP = 60;</code>
     * @return Whether the commdstISP field is set.
     */
    boolean hasCommdstISP();
    /**
     * <code>optional bytes commdstISP = 60;</code>
     * @return The commdstISP.
     */
    com.google.protobuf.ByteString getCommdstISP();

    /**
     * <code>optional uint32 commdstASN = 61;</code>
     * @return Whether the commdstASN field is set.
     */
    boolean hasCommdstASN();
    /**
     * <code>optional uint32 commdstASN = 61;</code>
     * @return The commdstASN.
     */
    int getCommdstASN();

    /**
     * <code>optional uint32 outAddrType = 62;</code>
     * @return Whether the outAddrType field is set.
     */
    boolean hasOutAddrType();
    /**
     * <code>optional uint32 outAddrType = 62;</code>
     * @return The outAddrType.
     */
    int getOutAddrType();

    /**
     * <code>optional uint32 outSrcAddr = 63;</code>
     * @return Whether the outSrcAddr field is set.
     */
    boolean hasOutSrcAddr();
    /**
     * <code>optional uint32 outSrcAddr = 63;</code>
     * @return The outSrcAddr.
     */
    int getOutSrcAddr();

    /**
     * <code>optional uint32 outDstAddr = 64;</code>
     * @return Whether the outDstAddr field is set.
     */
    boolean hasOutDstAddr();
    /**
     * <code>optional uint32 outDstAddr = 64;</code>
     * @return The outDstAddr.
     */
    int getOutDstAddr();

    /**
     * <code>optional bytes outer_ipv6_src = 65;</code>
     * @return Whether the outerIpv6Src field is set.
     */
    boolean hasOuterIpv6Src();
    /**
     * <code>optional bytes outer_ipv6_src = 65;</code>
     * @return The outerIpv6Src.
     */
    com.google.protobuf.ByteString getOuterIpv6Src();

    /**
     * <code>optional bytes outer_ipv6_dst = 66;</code>
     * @return Whether the outerIpv6Dst field is set.
     */
    boolean hasOuterIpv6Dst();
    /**
     * <code>optional bytes outer_ipv6_dst = 66;</code>
     * @return The outerIpv6Dst.
     */
    com.google.protobuf.ByteString getOuterIpv6Dst();

    /**
     * <code>optional uint32 outSrcPort = 67;</code>
     * @return Whether the outSrcPort field is set.
     */
    boolean hasOutSrcPort();
    /**
     * <code>optional uint32 outSrcPort = 67;</code>
     * @return The outSrcPort.
     */
    int getOutSrcPort();

    /**
     * <code>optional uint32 outDstPort = 68;</code>
     * @return Whether the outDstPort field is set.
     */
    boolean hasOutDstPort();
    /**
     * <code>optional uint32 outDstPort = 68;</code>
     * @return The outDstPort.
     */
    int getOutDstPort();

    /**
     * <code>optional uint32 outTransProto = 69;</code>
     * @return Whether the outTransProto field is set.
     */
    boolean hasOutTransProto();
    /**
     * <code>optional uint32 outTransProto = 69;</code>
     * @return The outTransProto.
     */
    int getOutTransProto();

    /**
     * <code>optional uint64 captureTime = 70;</code>
     * @return Whether the captureTime field is set.
     */
    boolean hasCaptureTime();
    /**
     * <code>optional uint64 captureTime = 70;</code>
     * @return The captureTime.
     */
    long getCaptureTime();
  }
  /**
   * Protobuf type {@code CommonInfo}
   */
  public static final class CommonInfo extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:CommonInfo)
      CommonInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        CommonInfo.class.getName());
    }
    // Use CommonInfo.newBuilder() to construct.
    private CommonInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CommonInfo() {
      srcMacOui_ = com.google.protobuf.ByteString.EMPTY;
      dstMacOui_ = com.google.protobuf.ByteString.EMPTY;
      lineName1_ = com.google.protobuf.ByteString.EMPTY;
      lineName2_ = com.google.protobuf.ByteString.EMPTY;
      tagsRule_ = com.google.protobuf.ByteString.EMPTY;
      meanID_ = com.google.protobuf.ByteString.EMPTY;
      siteID_ = com.google.protobuf.ByteString.EMPTY;
      unitID_ = com.google.protobuf.ByteString.EMPTY;
      taskID_ = com.google.protobuf.ByteString.EMPTY;
      mdsecdeg_ = com.google.protobuf.ByteString.EMPTY;
      filesecdeg_ = com.google.protobuf.ByteString.EMPTY;
      secdegpro_ = com.google.protobuf.ByteString.EMPTY;
      commsrcAddrV6_ = com.google.protobuf.ByteString.EMPTY;
      commdstAddrV6_ = com.google.protobuf.ByteString.EMPTY;
      commprotInfo_ = com.google.protobuf.ByteString.EMPTY;
      commprotType_ = com.google.protobuf.ByteString.EMPTY;
      commprotName_ = com.google.protobuf.ByteString.EMPTY;
      commetags_ = com.google.protobuf.ByteString.EMPTY;
      commttags_ = com.google.protobuf.ByteString.EMPTY;
      commatags_ = com.google.protobuf.ByteString.EMPTY;
      commutags_ = com.google.protobuf.ByteString.EMPTY;
      commsrcMac_ = com.google.protobuf.ByteString.EMPTY;
      commdstMac_ = com.google.protobuf.ByteString.EMPTY;
      commsrcCountry_ = com.google.protobuf.ByteString.EMPTY;
      commsrcState_ = com.google.protobuf.ByteString.EMPTY;
      commsrcCity_ = com.google.protobuf.ByteString.EMPTY;
      commsrcISP_ = com.google.protobuf.ByteString.EMPTY;
      commdstCountry_ = com.google.protobuf.ByteString.EMPTY;
      commdstState_ = com.google.protobuf.ByteString.EMPTY;
      commdstCity_ = com.google.protobuf.ByteString.EMPTY;
      commdstISP_ = com.google.protobuf.ByteString.EMPTY;
      outerIpv6Src_ = com.google.protobuf.ByteString.EMPTY;
      outerIpv6Dst_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return CommonInfoOuterClass.internal_static_CommonInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return CommonInfoOuterClass.internal_static_CommonInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              CommonInfoOuterClass.CommonInfo.class, CommonInfoOuterClass.CommonInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    public static final int SRCMACOUI_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString srcMacOui_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srcMacOui = 1;</code>
     * @return Whether the srcMacOui field is set.
     */
    @java.lang.Override
    public boolean hasSrcMacOui() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes srcMacOui = 1;</code>
     * @return The srcMacOui.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrcMacOui() {
      return srcMacOui_;
    }

    public static final int DSTMACOUI_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString dstMacOui_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes dstMacOui = 2;</code>
     * @return Whether the dstMacOui field is set.
     */
    @java.lang.Override
    public boolean hasDstMacOui() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes dstMacOui = 2;</code>
     * @return The dstMacOui.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDstMacOui() {
      return dstMacOui_;
    }

    public static final int LINENAME1_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString lineName1_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes lineName1 = 3;</code>
     * @return Whether the lineName1 field is set.
     */
    @java.lang.Override
    public boolean hasLineName1() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes lineName1 = 3;</code>
     * @return The lineName1.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getLineName1() {
      return lineName1_;
    }

    public static final int LINENAME2_FIELD_NUMBER = 4;
    private com.google.protobuf.ByteString lineName2_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes lineName2 = 4;</code>
     * @return Whether the lineName2 field is set.
     */
    @java.lang.Override
    public boolean hasLineName2() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes lineName2 = 4;</code>
     * @return The lineName2.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getLineName2() {
      return lineName2_;
    }

    public static final int BEGTIME_FIELD_NUMBER = 5;
    private long begTime_ = 0L;
    /**
     * <code>optional uint64 begTime = 5;</code>
     * @return Whether the begTime field is set.
     */
    @java.lang.Override
    public boolean hasBegTime() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint64 begTime = 5;</code>
     * @return The begTime.
     */
    @java.lang.Override
    public long getBegTime() {
      return begTime_;
    }

    public static final int TAGSRULE_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString tagsRule_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes tagsRule = 6;</code>
     * @return Whether the tagsRule field is set.
     */
    @java.lang.Override
    public boolean hasTagsRule() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes tagsRule = 6;</code>
     * @return The tagsRule.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTagsRule() {
      return tagsRule_;
    }

    public static final int ENDTIME_FIELD_NUMBER = 7;
    private long endTime_ = 0L;
    /**
     * <code>optional uint64 endTime = 7;</code>
     * @return Whether the endTime field is set.
     */
    @java.lang.Override
    public boolean hasEndTime() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint64 endTime = 7;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public long getEndTime() {
      return endTime_;
    }

    public static final int COMDUR_FIELD_NUMBER = 8;
    private int comDur_ = 0;
    /**
     * <code>optional uint32 comDur = 8;</code>
     * @return Whether the comDur field is set.
     */
    @java.lang.Override
    public boolean hasComDur() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 comDur = 8;</code>
     * @return The comDur.
     */
    @java.lang.Override
    public int getComDur() {
      return comDur_;
    }

    public static final int MEANID_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString meanID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes meanID = 9;</code>
     * @return Whether the meanID field is set.
     */
    @java.lang.Override
    public boolean hasMeanID() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes meanID = 9;</code>
     * @return The meanID.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMeanID() {
      return meanID_;
    }

    public static final int SITEID_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString siteID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes siteID = 10;</code>
     * @return Whether the siteID field is set.
     */
    @java.lang.Override
    public boolean hasSiteID() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes siteID = 10;</code>
     * @return The siteID.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSiteID() {
      return siteID_;
    }

    public static final int UNITID_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString unitID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes unitID = 11;</code>
     * @return Whether the unitID field is set.
     */
    @java.lang.Override
    public boolean hasUnitID() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes unitID = 11;</code>
     * @return The unitID.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUnitID() {
      return unitID_;
    }

    public static final int TASKID_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString taskID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes taskID = 12;</code>
     * @return Whether the taskID field is set.
     */
    @java.lang.Override
    public boolean hasTaskID() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes taskID = 12;</code>
     * @return The taskID.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTaskID() {
      return taskID_;
    }

    public static final int GUID_FIELD_NUMBER = 13;
    private long guid_ = 0L;
    /**
     * <code>optional uint64 guid = 13;</code>
     * @return Whether the guid field is set.
     */
    @java.lang.Override
    public boolean hasGuid() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint64 guid = 13;</code>
     * @return The guid.
     */
    @java.lang.Override
    public long getGuid() {
      return guid_;
    }

    public static final int STORTIME_FIELD_NUMBER = 14;
    private long stortime_ = 0L;
    /**
     * <code>optional uint64 stortime = 14;</code>
     * @return Whether the stortime field is set.
     */
    @java.lang.Override
    public boolean hasStortime() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional uint64 stortime = 14;</code>
     * @return The stortime.
     */
    @java.lang.Override
    public long getStortime() {
      return stortime_;
    }

    public static final int MDSECDEG_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString mdsecdeg_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes mdsecdeg = 15;</code>
     * @return Whether the mdsecdeg field is set.
     */
    @java.lang.Override
    public boolean hasMdsecdeg() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes mdsecdeg = 15;</code>
     * @return The mdsecdeg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMdsecdeg() {
      return mdsecdeg_;
    }

    public static final int FILESECDEG_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString filesecdeg_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes filesecdeg = 16;</code>
     * @return Whether the filesecdeg field is set.
     */
    @java.lang.Override
    public boolean hasFilesecdeg() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes filesecdeg = 16;</code>
     * @return The filesecdeg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getFilesecdeg() {
      return filesecdeg_;
    }

    public static final int SECDEGPRO_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString secdegpro_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes secdegpro = 17;</code>
     * @return Whether the secdegpro field is set.
     */
    @java.lang.Override
    public boolean hasSecdegpro() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes secdegpro = 17;</code>
     * @return The secdegpro.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSecdegpro() {
      return secdegpro_;
    }

    public static final int COMMIPVER_FIELD_NUMBER = 18;
    private int commipVer_ = 0;
    /**
     * <code>optional uint32 commipVer = 18;</code>
     * @return Whether the commipVer field is set.
     */
    @java.lang.Override
    public boolean hasCommipVer() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 commipVer = 18;</code>
     * @return The commipVer.
     */
    @java.lang.Override
    public int getCommipVer() {
      return commipVer_;
    }

    public static final int COMMSRCADDR_FIELD_NUMBER = 19;
    private long commsrcAddr_ = 0L;
    /**
     * <code>optional uint64 commsrcAddr = 19;</code>
     * @return Whether the commsrcAddr field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcAddr() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint64 commsrcAddr = 19;</code>
     * @return The commsrcAddr.
     */
    @java.lang.Override
    public long getCommsrcAddr() {
      return commsrcAddr_;
    }

    public static final int COMMDSTADDR_FIELD_NUMBER = 20;
    private long commdstAddr_ = 0L;
    /**
     * <code>optional uint64 commdstAddr = 20;</code>
     * @return Whether the commdstAddr field is set.
     */
    @java.lang.Override
    public boolean hasCommdstAddr() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint64 commdstAddr = 20;</code>
     * @return The commdstAddr.
     */
    @java.lang.Override
    public long getCommdstAddr() {
      return commdstAddr_;
    }

    public static final int COMMSRCPORT_FIELD_NUMBER = 21;
    private int commsrcPort_ = 0;
    /**
     * <code>optional uint32 commsrcPort = 21;</code>
     * @return Whether the commsrcPort field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcPort() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional uint32 commsrcPort = 21;</code>
     * @return The commsrcPort.
     */
    @java.lang.Override
    public int getCommsrcPort() {
      return commsrcPort_;
    }

    public static final int COMMDSTPORT_FIELD_NUMBER = 22;
    private int commdstPort_ = 0;
    /**
     * <code>optional uint32 commdstPort = 22;</code>
     * @return Whether the commdstPort field is set.
     */
    @java.lang.Override
    public boolean hasCommdstPort() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 commdstPort = 22;</code>
     * @return The commdstPort.
     */
    @java.lang.Override
    public int getCommdstPort() {
      return commdstPort_;
    }

    public static final int COMMPROTNUM_FIELD_NUMBER = 23;
    private int commprotNum_ = 0;
    /**
     * <code>optional uint32 commprotNum = 23;</code>
     * @return Whether the commprotNum field is set.
     */
    @java.lang.Override
    public boolean hasCommprotNum() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional uint32 commprotNum = 23;</code>
     * @return The commprotNum.
     */
    @java.lang.Override
    public int getCommprotNum() {
      return commprotNum_;
    }

    public static final int COMMSRCADDRV6_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString commsrcAddrV6_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commsrcAddrV6 = 24;</code>
     * @return Whether the commsrcAddrV6 field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcAddrV6() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes commsrcAddrV6 = 24;</code>
     * @return The commsrcAddrV6.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommsrcAddrV6() {
      return commsrcAddrV6_;
    }

    public static final int COMMDSTADDRV6_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString commdstAddrV6_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commdstAddrV6 = 25;</code>
     * @return Whether the commdstAddrV6 field is set.
     */
    @java.lang.Override
    public boolean hasCommdstAddrV6() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes commdstAddrV6 = 25;</code>
     * @return The commdstAddrV6.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommdstAddrV6() {
      return commdstAddrV6_;
    }

    public static final int COMMPROTINFO_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString commprotInfo_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commprotInfo = 26;</code>
     * @return Whether the commprotInfo field is set.
     */
    @java.lang.Override
    public boolean hasCommprotInfo() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes commprotInfo = 26;</code>
     * @return The commprotInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommprotInfo() {
      return commprotInfo_;
    }

    public static final int COMMPROTTYPE_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString commprotType_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commprotType = 27;</code>
     * @return Whether the commprotType field is set.
     */
    @java.lang.Override
    public boolean hasCommprotType() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes commprotType = 27;</code>
     * @return The commprotType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommprotType() {
      return commprotType_;
    }

    public static final int COMMPROTNAME_FIELD_NUMBER = 28;
    private com.google.protobuf.ByteString commprotName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commprotName = 28;</code>
     * @return Whether the commprotName field is set.
     */
    @java.lang.Override
    public boolean hasCommprotName() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes commprotName = 28;</code>
     * @return The commprotName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommprotName() {
      return commprotName_;
    }

    public static final int COMMMULROUFLAG_FIELD_NUMBER = 29;
    private int commmulRouFlag_ = 0;
    /**
     * <code>optional uint32 commmulRouFlag = 29;</code>
     * @return Whether the commmulRouFlag field is set.
     */
    @java.lang.Override
    public boolean hasCommmulRouFlag() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional uint32 commmulRouFlag = 29;</code>
     * @return The commmulRouFlag.
     */
    @java.lang.Override
    public int getCommmulRouFlag() {
      return commmulRouFlag_;
    }

    public static final int COMMINTFLAG_FIELD_NUMBER = 30;
    private int commintFlag_ = 0;
    /**
     * <code>optional uint32 commintFlag = 30;</code>
     * @return Whether the commintFlag field is set.
     */
    @java.lang.Override
    public boolean hasCommintFlag() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional uint32 commintFlag = 30;</code>
     * @return The commintFlag.
     */
    @java.lang.Override
    public int getCommintFlag() {
      return commintFlag_;
    }

    public static final int COMMSTRDIREC_FIELD_NUMBER = 31;
    private int commstrDirec_ = 0;
    /**
     * <code>optional uint32 commstrDirec = 31;</code>
     * @return Whether the commstrDirec field is set.
     */
    @java.lang.Override
    public boolean hasCommstrDirec() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional uint32 commstrDirec = 31;</code>
     * @return The commstrDirec.
     */
    @java.lang.Override
    public int getCommstrDirec() {
      return commstrDirec_;
    }

    public static final int COMMPKTNUM_FIELD_NUMBER = 32;
    private int commpktNum_ = 0;
    /**
     * <code>optional uint32 commpktNum = 32;</code>
     * @return Whether the commpktNum field is set.
     */
    @java.lang.Override
    public boolean hasCommpktNum() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint32 commpktNum = 32;</code>
     * @return The commpktNum.
     */
    @java.lang.Override
    public int getCommpktNum() {
      return commpktNum_;
    }

    public static final int COMMPAYLEN_FIELD_NUMBER = 33;
    private long commpayLen_ = 0L;
    /**
     * <code>optional uint64 commpayLen = 33;</code>
     * @return Whether the commpayLen field is set.
     */
    @java.lang.Override
    public boolean hasCommpayLen() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint64 commpayLen = 33;</code>
     * @return The commpayLen.
     */
    @java.lang.Override
    public long getCommpayLen() {
      return commpayLen_;
    }

    public static final int COMMSTREAMID_FIELD_NUMBER = 34;
    private long commstreamId_ = 0L;
    /**
     * <code>optional uint64 commstreamId = 34;</code>
     * @return Whether the commstreamId field is set.
     */
    @java.lang.Override
    public boolean hasCommstreamId() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint64 commstreamId = 34;</code>
     * @return The commstreamId.
     */
    @java.lang.Override
    public long getCommstreamId() {
      return commstreamId_;
    }

    public static final int COMMETAGS_FIELD_NUMBER = 35;
    private com.google.protobuf.ByteString commetags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commetags = 35;</code>
     * @return Whether the commetags field is set.
     */
    @java.lang.Override
    public boolean hasCommetags() {
      return ((bitField1_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes commetags = 35;</code>
     * @return The commetags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommetags() {
      return commetags_;
    }

    public static final int COMMTTAGS_FIELD_NUMBER = 36;
    private com.google.protobuf.ByteString commttags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commttags = 36;</code>
     * @return Whether the commttags field is set.
     */
    @java.lang.Override
    public boolean hasCommttags() {
      return ((bitField1_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes commttags = 36;</code>
     * @return The commttags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommttags() {
      return commttags_;
    }

    public static final int COMMATAGS_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString commatags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commatags = 37;</code>
     * @return Whether the commatags field is set.
     */
    @java.lang.Override
    public boolean hasCommatags() {
      return ((bitField1_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes commatags = 37;</code>
     * @return The commatags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommatags() {
      return commatags_;
    }

    public static final int COMMUTAGS_FIELD_NUMBER = 38;
    private com.google.protobuf.ByteString commutags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commutags = 38;</code>
     * @return Whether the commutags field is set.
     */
    @java.lang.Override
    public boolean hasCommutags() {
      return ((bitField1_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes commutags = 38;</code>
     * @return The commutags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommutags() {
      return commutags_;
    }

    public static final int COMMLABLE1_FIELD_NUMBER = 39;
    private int commlable1_ = 0;
    /**
     * <code>optional uint32 commlable1 = 39;</code>
     * @return Whether the commlable1 field is set.
     */
    @java.lang.Override
    public boolean hasCommlable1() {
      return ((bitField1_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 commlable1 = 39;</code>
     * @return The commlable1.
     */
    @java.lang.Override
    public int getCommlable1() {
      return commlable1_;
    }

    public static final int COMMLABLE2_FIELD_NUMBER = 40;
    private int commlable2_ = 0;
    /**
     * <code>optional uint32 commlable2 = 40;</code>
     * @return Whether the commlable2 field is set.
     */
    @java.lang.Override
    public boolean hasCommlable2() {
      return ((bitField1_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 commlable2 = 40;</code>
     * @return The commlable2.
     */
    @java.lang.Override
    public int getCommlable2() {
      return commlable2_;
    }

    public static final int COMMLABLE3_FIELD_NUMBER = 41;
    private int commlable3_ = 0;
    /**
     * <code>optional uint32 commlable3 = 41;</code>
     * @return Whether the commlable3 field is set.
     */
    @java.lang.Override
    public boolean hasCommlable3() {
      return ((bitField1_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint32 commlable3 = 41;</code>
     * @return The commlable3.
     */
    @java.lang.Override
    public int getCommlable3() {
      return commlable3_;
    }

    public static final int COMMLABLE4_FIELD_NUMBER = 42;
    private int commlable4_ = 0;
    /**
     * <code>optional uint32 commlable4 = 42;</code>
     * @return Whether the commlable4 field is set.
     */
    @java.lang.Override
    public boolean hasCommlable4() {
      return ((bitField1_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint32 commlable4 = 42;</code>
     * @return The commlable4.
     */
    @java.lang.Override
    public int getCommlable4() {
      return commlable4_;
    }

    public static final int COMMVLANID1_FIELD_NUMBER = 43;
    private int commvlanID1_ = 0;
    /**
     * <code>optional uint32 commvlanID1 = 43;</code>
     * @return Whether the commvlanID1 field is set.
     */
    @java.lang.Override
    public boolean hasCommvlanID1() {
      return ((bitField1_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint32 commvlanID1 = 43;</code>
     * @return The commvlanID1.
     */
    @java.lang.Override
    public int getCommvlanID1() {
      return commvlanID1_;
    }

    public static final int COMMVLANID2_FIELD_NUMBER = 44;
    private int commvlanID2_ = 0;
    /**
     * <code>optional uint32 commvlanID2 = 44;</code>
     * @return Whether the commvlanID2 field is set.
     */
    @java.lang.Override
    public boolean hasCommvlanID2() {
      return ((bitField1_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 commvlanID2 = 44;</code>
     * @return The commvlanID2.
     */
    @java.lang.Override
    public int getCommvlanID2() {
      return commvlanID2_;
    }

    public static final int COMMSRCMAC_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString commsrcMac_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commsrcMac = 45;</code>
     * @return Whether the commsrcMac field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcMac() {
      return ((bitField1_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes commsrcMac = 45;</code>
     * @return The commsrcMac.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommsrcMac() {
      return commsrcMac_;
    }

    public static final int COMMDSTMAC_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString commdstMac_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commdstMac = 46;</code>
     * @return Whether the commdstMac field is set.
     */
    @java.lang.Override
    public boolean hasCommdstMac() {
      return ((bitField1_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes commdstMac = 46;</code>
     * @return The commdstMac.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommdstMac() {
      return commdstMac_;
    }

    public static final int COMMTUNNELID_FIELD_NUMBER = 47;
    private int commtunnelID_ = 0;
    /**
     * <code>optional uint32 commtunnelID = 47;</code>
     * @return Whether the commtunnelID field is set.
     */
    @java.lang.Override
    public boolean hasCommtunnelID() {
      return ((bitField1_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 commtunnelID = 47;</code>
     * @return The commtunnelID.
     */
    @java.lang.Override
    public int getCommtunnelID() {
      return commtunnelID_;
    }

    public static final int COMMSRCCOUNTRY_FIELD_NUMBER = 48;
    private com.google.protobuf.ByteString commsrcCountry_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commsrcCountry = 48;</code>
     * @return Whether the commsrcCountry field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcCountry() {
      return ((bitField1_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes commsrcCountry = 48;</code>
     * @return The commsrcCountry.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommsrcCountry() {
      return commsrcCountry_;
    }

    public static final int COMMSRCSTATE_FIELD_NUMBER = 49;
    private com.google.protobuf.ByteString commsrcState_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commsrcState = 49;</code>
     * @return Whether the commsrcState field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcState() {
      return ((bitField1_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes commsrcState = 49;</code>
     * @return The commsrcState.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommsrcState() {
      return commsrcState_;
    }

    public static final int COMMSRCCITY_FIELD_NUMBER = 50;
    private com.google.protobuf.ByteString commsrcCity_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commsrcCity = 50;</code>
     * @return Whether the commsrcCity field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcCity() {
      return ((bitField1_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes commsrcCity = 50;</code>
     * @return The commsrcCity.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommsrcCity() {
      return commsrcCity_;
    }

    public static final int COMMSRCLONGITUDE_FIELD_NUMBER = 51;
    private float commsrcLongitude_ = 0F;
    /**
     * <code>optional float commsrcLongitude = 51;</code>
     * @return Whether the commsrcLongitude field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcLongitude() {
      return ((bitField1_ & 0x00040000) != 0);
    }
    /**
     * <code>optional float commsrcLongitude = 51;</code>
     * @return The commsrcLongitude.
     */
    @java.lang.Override
    public float getCommsrcLongitude() {
      return commsrcLongitude_;
    }

    public static final int COMMSRCLATITUDE_FIELD_NUMBER = 52;
    private float commsrcLatitude_ = 0F;
    /**
     * <code>optional float commsrcLatitude = 52;</code>
     * @return Whether the commsrcLatitude field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcLatitude() {
      return ((bitField1_ & 0x00080000) != 0);
    }
    /**
     * <code>optional float commsrcLatitude = 52;</code>
     * @return The commsrcLatitude.
     */
    @java.lang.Override
    public float getCommsrcLatitude() {
      return commsrcLatitude_;
    }

    public static final int COMMSRCISP_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString commsrcISP_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commsrcISP = 53;</code>
     * @return Whether the commsrcISP field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcISP() {
      return ((bitField1_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes commsrcISP = 53;</code>
     * @return The commsrcISP.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommsrcISP() {
      return commsrcISP_;
    }

    public static final int COMMSRCASN_FIELD_NUMBER = 54;
    private int commsrcASN_ = 0;
    /**
     * <code>optional uint32 commsrcASN = 54;</code>
     * @return Whether the commsrcASN field is set.
     */
    @java.lang.Override
    public boolean hasCommsrcASN() {
      return ((bitField1_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 commsrcASN = 54;</code>
     * @return The commsrcASN.
     */
    @java.lang.Override
    public int getCommsrcASN() {
      return commsrcASN_;
    }

    public static final int COMMDSTCOUNTRY_FIELD_NUMBER = 55;
    private com.google.protobuf.ByteString commdstCountry_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commdstCountry = 55;</code>
     * @return Whether the commdstCountry field is set.
     */
    @java.lang.Override
    public boolean hasCommdstCountry() {
      return ((bitField1_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes commdstCountry = 55;</code>
     * @return The commdstCountry.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommdstCountry() {
      return commdstCountry_;
    }

    public static final int COMMDSTSTATE_FIELD_NUMBER = 56;
    private com.google.protobuf.ByteString commdstState_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commdstState = 56;</code>
     * @return Whether the commdstState field is set.
     */
    @java.lang.Override
    public boolean hasCommdstState() {
      return ((bitField1_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes commdstState = 56;</code>
     * @return The commdstState.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommdstState() {
      return commdstState_;
    }

    public static final int COMMDSTCITY_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString commdstCity_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commdstCity = 57;</code>
     * @return Whether the commdstCity field is set.
     */
    @java.lang.Override
    public boolean hasCommdstCity() {
      return ((bitField1_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes commdstCity = 57;</code>
     * @return The commdstCity.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommdstCity() {
      return commdstCity_;
    }

    public static final int COMMDSTLONGITUDE_FIELD_NUMBER = 58;
    private float commdstLongitude_ = 0F;
    /**
     * <code>optional float commdstLongitude = 58;</code>
     * @return Whether the commdstLongitude field is set.
     */
    @java.lang.Override
    public boolean hasCommdstLongitude() {
      return ((bitField1_ & 0x02000000) != 0);
    }
    /**
     * <code>optional float commdstLongitude = 58;</code>
     * @return The commdstLongitude.
     */
    @java.lang.Override
    public float getCommdstLongitude() {
      return commdstLongitude_;
    }

    public static final int COMMDSTLATITUDE_FIELD_NUMBER = 59;
    private float commdstLatitude_ = 0F;
    /**
     * <code>optional float commdstLatitude = 59;</code>
     * @return Whether the commdstLatitude field is set.
     */
    @java.lang.Override
    public boolean hasCommdstLatitude() {
      return ((bitField1_ & 0x04000000) != 0);
    }
    /**
     * <code>optional float commdstLatitude = 59;</code>
     * @return The commdstLatitude.
     */
    @java.lang.Override
    public float getCommdstLatitude() {
      return commdstLatitude_;
    }

    public static final int COMMDSTISP_FIELD_NUMBER = 60;
    private com.google.protobuf.ByteString commdstISP_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes commdstISP = 60;</code>
     * @return Whether the commdstISP field is set.
     */
    @java.lang.Override
    public boolean hasCommdstISP() {
      return ((bitField1_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes commdstISP = 60;</code>
     * @return The commdstISP.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCommdstISP() {
      return commdstISP_;
    }

    public static final int COMMDSTASN_FIELD_NUMBER = 61;
    private int commdstASN_ = 0;
    /**
     * <code>optional uint32 commdstASN = 61;</code>
     * @return Whether the commdstASN field is set.
     */
    @java.lang.Override
    public boolean hasCommdstASN() {
      return ((bitField1_ & 0x10000000) != 0);
    }
    /**
     * <code>optional uint32 commdstASN = 61;</code>
     * @return The commdstASN.
     */
    @java.lang.Override
    public int getCommdstASN() {
      return commdstASN_;
    }

    public static final int OUTADDRTYPE_FIELD_NUMBER = 62;
    private int outAddrType_ = 0;
    /**
     * <code>optional uint32 outAddrType = 62;</code>
     * @return Whether the outAddrType field is set.
     */
    @java.lang.Override
    public boolean hasOutAddrType() {
      return ((bitField1_ & 0x20000000) != 0);
    }
    /**
     * <code>optional uint32 outAddrType = 62;</code>
     * @return The outAddrType.
     */
    @java.lang.Override
    public int getOutAddrType() {
      return outAddrType_;
    }

    public static final int OUTSRCADDR_FIELD_NUMBER = 63;
    private int outSrcAddr_ = 0;
    /**
     * <code>optional uint32 outSrcAddr = 63;</code>
     * @return Whether the outSrcAddr field is set.
     */
    @java.lang.Override
    public boolean hasOutSrcAddr() {
      return ((bitField1_ & 0x40000000) != 0);
    }
    /**
     * <code>optional uint32 outSrcAddr = 63;</code>
     * @return The outSrcAddr.
     */
    @java.lang.Override
    public int getOutSrcAddr() {
      return outSrcAddr_;
    }

    public static final int OUTDSTADDR_FIELD_NUMBER = 64;
    private int outDstAddr_ = 0;
    /**
     * <code>optional uint32 outDstAddr = 64;</code>
     * @return Whether the outDstAddr field is set.
     */
    @java.lang.Override
    public boolean hasOutDstAddr() {
      return ((bitField1_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint32 outDstAddr = 64;</code>
     * @return The outDstAddr.
     */
    @java.lang.Override
    public int getOutDstAddr() {
      return outDstAddr_;
    }

    public static final int OUTER_IPV6_SRC_FIELD_NUMBER = 65;
    private com.google.protobuf.ByteString outerIpv6Src_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes outer_ipv6_src = 65;</code>
     * @return Whether the outerIpv6Src field is set.
     */
    @java.lang.Override
    public boolean hasOuterIpv6Src() {
      return ((bitField2_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes outer_ipv6_src = 65;</code>
     * @return The outerIpv6Src.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getOuterIpv6Src() {
      return outerIpv6Src_;
    }

    public static final int OUTER_IPV6_DST_FIELD_NUMBER = 66;
    private com.google.protobuf.ByteString outerIpv6Dst_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes outer_ipv6_dst = 66;</code>
     * @return Whether the outerIpv6Dst field is set.
     */
    @java.lang.Override
    public boolean hasOuterIpv6Dst() {
      return ((bitField2_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes outer_ipv6_dst = 66;</code>
     * @return The outerIpv6Dst.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getOuterIpv6Dst() {
      return outerIpv6Dst_;
    }

    public static final int OUTSRCPORT_FIELD_NUMBER = 67;
    private int outSrcPort_ = 0;
    /**
     * <code>optional uint32 outSrcPort = 67;</code>
     * @return Whether the outSrcPort field is set.
     */
    @java.lang.Override
    public boolean hasOutSrcPort() {
      return ((bitField2_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 outSrcPort = 67;</code>
     * @return The outSrcPort.
     */
    @java.lang.Override
    public int getOutSrcPort() {
      return outSrcPort_;
    }

    public static final int OUTDSTPORT_FIELD_NUMBER = 68;
    private int outDstPort_ = 0;
    /**
     * <code>optional uint32 outDstPort = 68;</code>
     * @return Whether the outDstPort field is set.
     */
    @java.lang.Override
    public boolean hasOutDstPort() {
      return ((bitField2_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 outDstPort = 68;</code>
     * @return The outDstPort.
     */
    @java.lang.Override
    public int getOutDstPort() {
      return outDstPort_;
    }

    public static final int OUTTRANSPROTO_FIELD_NUMBER = 69;
    private int outTransProto_ = 0;
    /**
     * <code>optional uint32 outTransProto = 69;</code>
     * @return Whether the outTransProto field is set.
     */
    @java.lang.Override
    public boolean hasOutTransProto() {
      return ((bitField2_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 outTransProto = 69;</code>
     * @return The outTransProto.
     */
    @java.lang.Override
    public int getOutTransProto() {
      return outTransProto_;
    }

    public static final int CAPTURETIME_FIELD_NUMBER = 70;
    private long captureTime_ = 0L;
    /**
     * <code>optional uint64 captureTime = 70;</code>
     * @return Whether the captureTime field is set.
     */
    @java.lang.Override
    public boolean hasCaptureTime() {
      return ((bitField2_ & 0x00000020) != 0);
    }
    /**
     * <code>optional uint64 captureTime = 70;</code>
     * @return The captureTime.
     */
    @java.lang.Override
    public long getCaptureTime() {
      return captureTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, srcMacOui_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, dstMacOui_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBytes(3, lineName1_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBytes(4, lineName2_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeUInt64(5, begTime_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(6, tagsRule_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeUInt64(7, endTime_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeUInt32(8, comDur_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeBytes(9, meanID_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeBytes(10, siteID_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeBytes(11, unitID_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeBytes(12, taskID_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt64(13, guid_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeUInt64(14, stortime_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeBytes(15, mdsecdeg_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeBytes(16, filesecdeg_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(17, secdegpro_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeUInt32(18, commipVer_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeUInt64(19, commsrcAddr_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeUInt64(20, commdstAddr_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeUInt32(21, commsrcPort_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeUInt32(22, commdstPort_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeUInt32(23, commprotNum_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeBytes(24, commsrcAddrV6_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeBytes(25, commdstAddrV6_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeBytes(26, commprotInfo_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeBytes(27, commprotType_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeBytes(28, commprotName_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeUInt32(29, commmulRouFlag_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeUInt32(30, commintFlag_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeUInt32(31, commstrDirec_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeUInt32(32, commpktNum_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeUInt64(33, commpayLen_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeUInt64(34, commstreamId_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        output.writeBytes(35, commetags_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        output.writeBytes(36, commttags_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        output.writeBytes(37, commatags_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        output.writeBytes(38, commutags_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        output.writeUInt32(39, commlable1_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        output.writeUInt32(40, commlable2_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        output.writeUInt32(41, commlable3_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        output.writeUInt32(42, commlable4_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        output.writeUInt32(43, commvlanID1_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        output.writeUInt32(44, commvlanID2_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        output.writeBytes(45, commsrcMac_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        output.writeBytes(46, commdstMac_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        output.writeUInt32(47, commtunnelID_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        output.writeBytes(48, commsrcCountry_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        output.writeBytes(49, commsrcState_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        output.writeBytes(50, commsrcCity_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        output.writeFloat(51, commsrcLongitude_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        output.writeFloat(52, commsrcLatitude_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        output.writeBytes(53, commsrcISP_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        output.writeUInt32(54, commsrcASN_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        output.writeBytes(55, commdstCountry_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        output.writeBytes(56, commdstState_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        output.writeBytes(57, commdstCity_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        output.writeFloat(58, commdstLongitude_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        output.writeFloat(59, commdstLatitude_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        output.writeBytes(60, commdstISP_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        output.writeUInt32(61, commdstASN_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        output.writeUInt32(62, outAddrType_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        output.writeUInt32(63, outSrcAddr_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        output.writeUInt32(64, outDstAddr_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        output.writeBytes(65, outerIpv6Src_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        output.writeBytes(66, outerIpv6Dst_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        output.writeUInt32(67, outSrcPort_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        output.writeUInt32(68, outDstPort_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        output.writeUInt32(69, outTransProto_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        output.writeUInt64(70, captureTime_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, srcMacOui_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, dstMacOui_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, lineName1_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, lineName2_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, begTime_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, tagsRule_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, endTime_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, comDur_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, meanID_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, siteID_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, unitID_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, taskID_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(13, guid_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, stortime_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, mdsecdeg_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, filesecdeg_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, secdegpro_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, commipVer_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(19, commsrcAddr_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(20, commdstAddr_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(21, commsrcPort_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, commdstPort_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(23, commprotNum_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, commsrcAddrV6_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, commdstAddrV6_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, commprotInfo_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, commprotType_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(28, commprotName_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(29, commmulRouFlag_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(30, commintFlag_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(31, commstrDirec_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(32, commpktNum_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(33, commpayLen_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(34, commstreamId_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(35, commetags_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(36, commttags_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, commatags_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(38, commutags_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(39, commlable1_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(40, commlable2_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(41, commlable3_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(42, commlable4_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(43, commvlanID1_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(44, commvlanID2_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, commsrcMac_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, commdstMac_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(47, commtunnelID_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(48, commsrcCountry_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(49, commsrcState_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(50, commsrcCity_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(51, commsrcLongitude_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(52, commsrcLatitude_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, commsrcISP_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(54, commsrcASN_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(55, commdstCountry_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(56, commdstState_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, commdstCity_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(58, commdstLongitude_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(59, commdstLatitude_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(60, commdstISP_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(61, commdstASN_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(62, outAddrType_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(63, outSrcAddr_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(64, outDstAddr_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(65, outerIpv6Src_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(66, outerIpv6Dst_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(67, outSrcPort_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(68, outDstPort_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(69, outTransProto_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(70, captureTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof CommonInfoOuterClass.CommonInfo)) {
        return super.equals(obj);
      }
      CommonInfoOuterClass.CommonInfo other = (CommonInfoOuterClass.CommonInfo) obj;

      if (hasSrcMacOui() != other.hasSrcMacOui()) return false;
      if (hasSrcMacOui()) {
        if (!getSrcMacOui()
            .equals(other.getSrcMacOui())) return false;
      }
      if (hasDstMacOui() != other.hasDstMacOui()) return false;
      if (hasDstMacOui()) {
        if (!getDstMacOui()
            .equals(other.getDstMacOui())) return false;
      }
      if (hasLineName1() != other.hasLineName1()) return false;
      if (hasLineName1()) {
        if (!getLineName1()
            .equals(other.getLineName1())) return false;
      }
      if (hasLineName2() != other.hasLineName2()) return false;
      if (hasLineName2()) {
        if (!getLineName2()
            .equals(other.getLineName2())) return false;
      }
      if (hasBegTime() != other.hasBegTime()) return false;
      if (hasBegTime()) {
        if (getBegTime()
            != other.getBegTime()) return false;
      }
      if (hasTagsRule() != other.hasTagsRule()) return false;
      if (hasTagsRule()) {
        if (!getTagsRule()
            .equals(other.getTagsRule())) return false;
      }
      if (hasEndTime() != other.hasEndTime()) return false;
      if (hasEndTime()) {
        if (getEndTime()
            != other.getEndTime()) return false;
      }
      if (hasComDur() != other.hasComDur()) return false;
      if (hasComDur()) {
        if (getComDur()
            != other.getComDur()) return false;
      }
      if (hasMeanID() != other.hasMeanID()) return false;
      if (hasMeanID()) {
        if (!getMeanID()
            .equals(other.getMeanID())) return false;
      }
      if (hasSiteID() != other.hasSiteID()) return false;
      if (hasSiteID()) {
        if (!getSiteID()
            .equals(other.getSiteID())) return false;
      }
      if (hasUnitID() != other.hasUnitID()) return false;
      if (hasUnitID()) {
        if (!getUnitID()
            .equals(other.getUnitID())) return false;
      }
      if (hasTaskID() != other.hasTaskID()) return false;
      if (hasTaskID()) {
        if (!getTaskID()
            .equals(other.getTaskID())) return false;
      }
      if (hasGuid() != other.hasGuid()) return false;
      if (hasGuid()) {
        if (getGuid()
            != other.getGuid()) return false;
      }
      if (hasStortime() != other.hasStortime()) return false;
      if (hasStortime()) {
        if (getStortime()
            != other.getStortime()) return false;
      }
      if (hasMdsecdeg() != other.hasMdsecdeg()) return false;
      if (hasMdsecdeg()) {
        if (!getMdsecdeg()
            .equals(other.getMdsecdeg())) return false;
      }
      if (hasFilesecdeg() != other.hasFilesecdeg()) return false;
      if (hasFilesecdeg()) {
        if (!getFilesecdeg()
            .equals(other.getFilesecdeg())) return false;
      }
      if (hasSecdegpro() != other.hasSecdegpro()) return false;
      if (hasSecdegpro()) {
        if (!getSecdegpro()
            .equals(other.getSecdegpro())) return false;
      }
      if (hasCommipVer() != other.hasCommipVer()) return false;
      if (hasCommipVer()) {
        if (getCommipVer()
            != other.getCommipVer()) return false;
      }
      if (hasCommsrcAddr() != other.hasCommsrcAddr()) return false;
      if (hasCommsrcAddr()) {
        if (getCommsrcAddr()
            != other.getCommsrcAddr()) return false;
      }
      if (hasCommdstAddr() != other.hasCommdstAddr()) return false;
      if (hasCommdstAddr()) {
        if (getCommdstAddr()
            != other.getCommdstAddr()) return false;
      }
      if (hasCommsrcPort() != other.hasCommsrcPort()) return false;
      if (hasCommsrcPort()) {
        if (getCommsrcPort()
            != other.getCommsrcPort()) return false;
      }
      if (hasCommdstPort() != other.hasCommdstPort()) return false;
      if (hasCommdstPort()) {
        if (getCommdstPort()
            != other.getCommdstPort()) return false;
      }
      if (hasCommprotNum() != other.hasCommprotNum()) return false;
      if (hasCommprotNum()) {
        if (getCommprotNum()
            != other.getCommprotNum()) return false;
      }
      if (hasCommsrcAddrV6() != other.hasCommsrcAddrV6()) return false;
      if (hasCommsrcAddrV6()) {
        if (!getCommsrcAddrV6()
            .equals(other.getCommsrcAddrV6())) return false;
      }
      if (hasCommdstAddrV6() != other.hasCommdstAddrV6()) return false;
      if (hasCommdstAddrV6()) {
        if (!getCommdstAddrV6()
            .equals(other.getCommdstAddrV6())) return false;
      }
      if (hasCommprotInfo() != other.hasCommprotInfo()) return false;
      if (hasCommprotInfo()) {
        if (!getCommprotInfo()
            .equals(other.getCommprotInfo())) return false;
      }
      if (hasCommprotType() != other.hasCommprotType()) return false;
      if (hasCommprotType()) {
        if (!getCommprotType()
            .equals(other.getCommprotType())) return false;
      }
      if (hasCommprotName() != other.hasCommprotName()) return false;
      if (hasCommprotName()) {
        if (!getCommprotName()
            .equals(other.getCommprotName())) return false;
      }
      if (hasCommmulRouFlag() != other.hasCommmulRouFlag()) return false;
      if (hasCommmulRouFlag()) {
        if (getCommmulRouFlag()
            != other.getCommmulRouFlag()) return false;
      }
      if (hasCommintFlag() != other.hasCommintFlag()) return false;
      if (hasCommintFlag()) {
        if (getCommintFlag()
            != other.getCommintFlag()) return false;
      }
      if (hasCommstrDirec() != other.hasCommstrDirec()) return false;
      if (hasCommstrDirec()) {
        if (getCommstrDirec()
            != other.getCommstrDirec()) return false;
      }
      if (hasCommpktNum() != other.hasCommpktNum()) return false;
      if (hasCommpktNum()) {
        if (getCommpktNum()
            != other.getCommpktNum()) return false;
      }
      if (hasCommpayLen() != other.hasCommpayLen()) return false;
      if (hasCommpayLen()) {
        if (getCommpayLen()
            != other.getCommpayLen()) return false;
      }
      if (hasCommstreamId() != other.hasCommstreamId()) return false;
      if (hasCommstreamId()) {
        if (getCommstreamId()
            != other.getCommstreamId()) return false;
      }
      if (hasCommetags() != other.hasCommetags()) return false;
      if (hasCommetags()) {
        if (!getCommetags()
            .equals(other.getCommetags())) return false;
      }
      if (hasCommttags() != other.hasCommttags()) return false;
      if (hasCommttags()) {
        if (!getCommttags()
            .equals(other.getCommttags())) return false;
      }
      if (hasCommatags() != other.hasCommatags()) return false;
      if (hasCommatags()) {
        if (!getCommatags()
            .equals(other.getCommatags())) return false;
      }
      if (hasCommutags() != other.hasCommutags()) return false;
      if (hasCommutags()) {
        if (!getCommutags()
            .equals(other.getCommutags())) return false;
      }
      if (hasCommlable1() != other.hasCommlable1()) return false;
      if (hasCommlable1()) {
        if (getCommlable1()
            != other.getCommlable1()) return false;
      }
      if (hasCommlable2() != other.hasCommlable2()) return false;
      if (hasCommlable2()) {
        if (getCommlable2()
            != other.getCommlable2()) return false;
      }
      if (hasCommlable3() != other.hasCommlable3()) return false;
      if (hasCommlable3()) {
        if (getCommlable3()
            != other.getCommlable3()) return false;
      }
      if (hasCommlable4() != other.hasCommlable4()) return false;
      if (hasCommlable4()) {
        if (getCommlable4()
            != other.getCommlable4()) return false;
      }
      if (hasCommvlanID1() != other.hasCommvlanID1()) return false;
      if (hasCommvlanID1()) {
        if (getCommvlanID1()
            != other.getCommvlanID1()) return false;
      }
      if (hasCommvlanID2() != other.hasCommvlanID2()) return false;
      if (hasCommvlanID2()) {
        if (getCommvlanID2()
            != other.getCommvlanID2()) return false;
      }
      if (hasCommsrcMac() != other.hasCommsrcMac()) return false;
      if (hasCommsrcMac()) {
        if (!getCommsrcMac()
            .equals(other.getCommsrcMac())) return false;
      }
      if (hasCommdstMac() != other.hasCommdstMac()) return false;
      if (hasCommdstMac()) {
        if (!getCommdstMac()
            .equals(other.getCommdstMac())) return false;
      }
      if (hasCommtunnelID() != other.hasCommtunnelID()) return false;
      if (hasCommtunnelID()) {
        if (getCommtunnelID()
            != other.getCommtunnelID()) return false;
      }
      if (hasCommsrcCountry() != other.hasCommsrcCountry()) return false;
      if (hasCommsrcCountry()) {
        if (!getCommsrcCountry()
            .equals(other.getCommsrcCountry())) return false;
      }
      if (hasCommsrcState() != other.hasCommsrcState()) return false;
      if (hasCommsrcState()) {
        if (!getCommsrcState()
            .equals(other.getCommsrcState())) return false;
      }
      if (hasCommsrcCity() != other.hasCommsrcCity()) return false;
      if (hasCommsrcCity()) {
        if (!getCommsrcCity()
            .equals(other.getCommsrcCity())) return false;
      }
      if (hasCommsrcLongitude() != other.hasCommsrcLongitude()) return false;
      if (hasCommsrcLongitude()) {
        if (java.lang.Float.floatToIntBits(getCommsrcLongitude())
            != java.lang.Float.floatToIntBits(
                other.getCommsrcLongitude())) return false;
      }
      if (hasCommsrcLatitude() != other.hasCommsrcLatitude()) return false;
      if (hasCommsrcLatitude()) {
        if (java.lang.Float.floatToIntBits(getCommsrcLatitude())
            != java.lang.Float.floatToIntBits(
                other.getCommsrcLatitude())) return false;
      }
      if (hasCommsrcISP() != other.hasCommsrcISP()) return false;
      if (hasCommsrcISP()) {
        if (!getCommsrcISP()
            .equals(other.getCommsrcISP())) return false;
      }
      if (hasCommsrcASN() != other.hasCommsrcASN()) return false;
      if (hasCommsrcASN()) {
        if (getCommsrcASN()
            != other.getCommsrcASN()) return false;
      }
      if (hasCommdstCountry() != other.hasCommdstCountry()) return false;
      if (hasCommdstCountry()) {
        if (!getCommdstCountry()
            .equals(other.getCommdstCountry())) return false;
      }
      if (hasCommdstState() != other.hasCommdstState()) return false;
      if (hasCommdstState()) {
        if (!getCommdstState()
            .equals(other.getCommdstState())) return false;
      }
      if (hasCommdstCity() != other.hasCommdstCity()) return false;
      if (hasCommdstCity()) {
        if (!getCommdstCity()
            .equals(other.getCommdstCity())) return false;
      }
      if (hasCommdstLongitude() != other.hasCommdstLongitude()) return false;
      if (hasCommdstLongitude()) {
        if (java.lang.Float.floatToIntBits(getCommdstLongitude())
            != java.lang.Float.floatToIntBits(
                other.getCommdstLongitude())) return false;
      }
      if (hasCommdstLatitude() != other.hasCommdstLatitude()) return false;
      if (hasCommdstLatitude()) {
        if (java.lang.Float.floatToIntBits(getCommdstLatitude())
            != java.lang.Float.floatToIntBits(
                other.getCommdstLatitude())) return false;
      }
      if (hasCommdstISP() != other.hasCommdstISP()) return false;
      if (hasCommdstISP()) {
        if (!getCommdstISP()
            .equals(other.getCommdstISP())) return false;
      }
      if (hasCommdstASN() != other.hasCommdstASN()) return false;
      if (hasCommdstASN()) {
        if (getCommdstASN()
            != other.getCommdstASN()) return false;
      }
      if (hasOutAddrType() != other.hasOutAddrType()) return false;
      if (hasOutAddrType()) {
        if (getOutAddrType()
            != other.getOutAddrType()) return false;
      }
      if (hasOutSrcAddr() != other.hasOutSrcAddr()) return false;
      if (hasOutSrcAddr()) {
        if (getOutSrcAddr()
            != other.getOutSrcAddr()) return false;
      }
      if (hasOutDstAddr() != other.hasOutDstAddr()) return false;
      if (hasOutDstAddr()) {
        if (getOutDstAddr()
            != other.getOutDstAddr()) return false;
      }
      if (hasOuterIpv6Src() != other.hasOuterIpv6Src()) return false;
      if (hasOuterIpv6Src()) {
        if (!getOuterIpv6Src()
            .equals(other.getOuterIpv6Src())) return false;
      }
      if (hasOuterIpv6Dst() != other.hasOuterIpv6Dst()) return false;
      if (hasOuterIpv6Dst()) {
        if (!getOuterIpv6Dst()
            .equals(other.getOuterIpv6Dst())) return false;
      }
      if (hasOutSrcPort() != other.hasOutSrcPort()) return false;
      if (hasOutSrcPort()) {
        if (getOutSrcPort()
            != other.getOutSrcPort()) return false;
      }
      if (hasOutDstPort() != other.hasOutDstPort()) return false;
      if (hasOutDstPort()) {
        if (getOutDstPort()
            != other.getOutDstPort()) return false;
      }
      if (hasOutTransProto() != other.hasOutTransProto()) return false;
      if (hasOutTransProto()) {
        if (getOutTransProto()
            != other.getOutTransProto()) return false;
      }
      if (hasCaptureTime() != other.hasCaptureTime()) return false;
      if (hasCaptureTime()) {
        if (getCaptureTime()
            != other.getCaptureTime()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSrcMacOui()) {
        hash = (37 * hash) + SRCMACOUI_FIELD_NUMBER;
        hash = (53 * hash) + getSrcMacOui().hashCode();
      }
      if (hasDstMacOui()) {
        hash = (37 * hash) + DSTMACOUI_FIELD_NUMBER;
        hash = (53 * hash) + getDstMacOui().hashCode();
      }
      if (hasLineName1()) {
        hash = (37 * hash) + LINENAME1_FIELD_NUMBER;
        hash = (53 * hash) + getLineName1().hashCode();
      }
      if (hasLineName2()) {
        hash = (37 * hash) + LINENAME2_FIELD_NUMBER;
        hash = (53 * hash) + getLineName2().hashCode();
      }
      if (hasBegTime()) {
        hash = (37 * hash) + BEGTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBegTime());
      }
      if (hasTagsRule()) {
        hash = (37 * hash) + TAGSRULE_FIELD_NUMBER;
        hash = (53 * hash) + getTagsRule().hashCode();
      }
      if (hasEndTime()) {
        hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEndTime());
      }
      if (hasComDur()) {
        hash = (37 * hash) + COMDUR_FIELD_NUMBER;
        hash = (53 * hash) + getComDur();
      }
      if (hasMeanID()) {
        hash = (37 * hash) + MEANID_FIELD_NUMBER;
        hash = (53 * hash) + getMeanID().hashCode();
      }
      if (hasSiteID()) {
        hash = (37 * hash) + SITEID_FIELD_NUMBER;
        hash = (53 * hash) + getSiteID().hashCode();
      }
      if (hasUnitID()) {
        hash = (37 * hash) + UNITID_FIELD_NUMBER;
        hash = (53 * hash) + getUnitID().hashCode();
      }
      if (hasTaskID()) {
        hash = (37 * hash) + TASKID_FIELD_NUMBER;
        hash = (53 * hash) + getTaskID().hashCode();
      }
      if (hasGuid()) {
        hash = (37 * hash) + GUID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getGuid());
      }
      if (hasStortime()) {
        hash = (37 * hash) + STORTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStortime());
      }
      if (hasMdsecdeg()) {
        hash = (37 * hash) + MDSECDEG_FIELD_NUMBER;
        hash = (53 * hash) + getMdsecdeg().hashCode();
      }
      if (hasFilesecdeg()) {
        hash = (37 * hash) + FILESECDEG_FIELD_NUMBER;
        hash = (53 * hash) + getFilesecdeg().hashCode();
      }
      if (hasSecdegpro()) {
        hash = (37 * hash) + SECDEGPRO_FIELD_NUMBER;
        hash = (53 * hash) + getSecdegpro().hashCode();
      }
      if (hasCommipVer()) {
        hash = (37 * hash) + COMMIPVER_FIELD_NUMBER;
        hash = (53 * hash) + getCommipVer();
      }
      if (hasCommsrcAddr()) {
        hash = (37 * hash) + COMMSRCADDR_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCommsrcAddr());
      }
      if (hasCommdstAddr()) {
        hash = (37 * hash) + COMMDSTADDR_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCommdstAddr());
      }
      if (hasCommsrcPort()) {
        hash = (37 * hash) + COMMSRCPORT_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcPort();
      }
      if (hasCommdstPort()) {
        hash = (37 * hash) + COMMDSTPORT_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstPort();
      }
      if (hasCommprotNum()) {
        hash = (37 * hash) + COMMPROTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getCommprotNum();
      }
      if (hasCommsrcAddrV6()) {
        hash = (37 * hash) + COMMSRCADDRV6_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcAddrV6().hashCode();
      }
      if (hasCommdstAddrV6()) {
        hash = (37 * hash) + COMMDSTADDRV6_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstAddrV6().hashCode();
      }
      if (hasCommprotInfo()) {
        hash = (37 * hash) + COMMPROTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getCommprotInfo().hashCode();
      }
      if (hasCommprotType()) {
        hash = (37 * hash) + COMMPROTTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCommprotType().hashCode();
      }
      if (hasCommprotName()) {
        hash = (37 * hash) + COMMPROTNAME_FIELD_NUMBER;
        hash = (53 * hash) + getCommprotName().hashCode();
      }
      if (hasCommmulRouFlag()) {
        hash = (37 * hash) + COMMMULROUFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getCommmulRouFlag();
      }
      if (hasCommintFlag()) {
        hash = (37 * hash) + COMMINTFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getCommintFlag();
      }
      if (hasCommstrDirec()) {
        hash = (37 * hash) + COMMSTRDIREC_FIELD_NUMBER;
        hash = (53 * hash) + getCommstrDirec();
      }
      if (hasCommpktNum()) {
        hash = (37 * hash) + COMMPKTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getCommpktNum();
      }
      if (hasCommpayLen()) {
        hash = (37 * hash) + COMMPAYLEN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCommpayLen());
      }
      if (hasCommstreamId()) {
        hash = (37 * hash) + COMMSTREAMID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCommstreamId());
      }
      if (hasCommetags()) {
        hash = (37 * hash) + COMMETAGS_FIELD_NUMBER;
        hash = (53 * hash) + getCommetags().hashCode();
      }
      if (hasCommttags()) {
        hash = (37 * hash) + COMMTTAGS_FIELD_NUMBER;
        hash = (53 * hash) + getCommttags().hashCode();
      }
      if (hasCommatags()) {
        hash = (37 * hash) + COMMATAGS_FIELD_NUMBER;
        hash = (53 * hash) + getCommatags().hashCode();
      }
      if (hasCommutags()) {
        hash = (37 * hash) + COMMUTAGS_FIELD_NUMBER;
        hash = (53 * hash) + getCommutags().hashCode();
      }
      if (hasCommlable1()) {
        hash = (37 * hash) + COMMLABLE1_FIELD_NUMBER;
        hash = (53 * hash) + getCommlable1();
      }
      if (hasCommlable2()) {
        hash = (37 * hash) + COMMLABLE2_FIELD_NUMBER;
        hash = (53 * hash) + getCommlable2();
      }
      if (hasCommlable3()) {
        hash = (37 * hash) + COMMLABLE3_FIELD_NUMBER;
        hash = (53 * hash) + getCommlable3();
      }
      if (hasCommlable4()) {
        hash = (37 * hash) + COMMLABLE4_FIELD_NUMBER;
        hash = (53 * hash) + getCommlable4();
      }
      if (hasCommvlanID1()) {
        hash = (37 * hash) + COMMVLANID1_FIELD_NUMBER;
        hash = (53 * hash) + getCommvlanID1();
      }
      if (hasCommvlanID2()) {
        hash = (37 * hash) + COMMVLANID2_FIELD_NUMBER;
        hash = (53 * hash) + getCommvlanID2();
      }
      if (hasCommsrcMac()) {
        hash = (37 * hash) + COMMSRCMAC_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcMac().hashCode();
      }
      if (hasCommdstMac()) {
        hash = (37 * hash) + COMMDSTMAC_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstMac().hashCode();
      }
      if (hasCommtunnelID()) {
        hash = (37 * hash) + COMMTUNNELID_FIELD_NUMBER;
        hash = (53 * hash) + getCommtunnelID();
      }
      if (hasCommsrcCountry()) {
        hash = (37 * hash) + COMMSRCCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcCountry().hashCode();
      }
      if (hasCommsrcState()) {
        hash = (37 * hash) + COMMSRCSTATE_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcState().hashCode();
      }
      if (hasCommsrcCity()) {
        hash = (37 * hash) + COMMSRCCITY_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcCity().hashCode();
      }
      if (hasCommsrcLongitude()) {
        hash = (37 * hash) + COMMSRCLONGITUDE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getCommsrcLongitude());
      }
      if (hasCommsrcLatitude()) {
        hash = (37 * hash) + COMMSRCLATITUDE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getCommsrcLatitude());
      }
      if (hasCommsrcISP()) {
        hash = (37 * hash) + COMMSRCISP_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcISP().hashCode();
      }
      if (hasCommsrcASN()) {
        hash = (37 * hash) + COMMSRCASN_FIELD_NUMBER;
        hash = (53 * hash) + getCommsrcASN();
      }
      if (hasCommdstCountry()) {
        hash = (37 * hash) + COMMDSTCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstCountry().hashCode();
      }
      if (hasCommdstState()) {
        hash = (37 * hash) + COMMDSTSTATE_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstState().hashCode();
      }
      if (hasCommdstCity()) {
        hash = (37 * hash) + COMMDSTCITY_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstCity().hashCode();
      }
      if (hasCommdstLongitude()) {
        hash = (37 * hash) + COMMDSTLONGITUDE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getCommdstLongitude());
      }
      if (hasCommdstLatitude()) {
        hash = (37 * hash) + COMMDSTLATITUDE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getCommdstLatitude());
      }
      if (hasCommdstISP()) {
        hash = (37 * hash) + COMMDSTISP_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstISP().hashCode();
      }
      if (hasCommdstASN()) {
        hash = (37 * hash) + COMMDSTASN_FIELD_NUMBER;
        hash = (53 * hash) + getCommdstASN();
      }
      if (hasOutAddrType()) {
        hash = (37 * hash) + OUTADDRTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getOutAddrType();
      }
      if (hasOutSrcAddr()) {
        hash = (37 * hash) + OUTSRCADDR_FIELD_NUMBER;
        hash = (53 * hash) + getOutSrcAddr();
      }
      if (hasOutDstAddr()) {
        hash = (37 * hash) + OUTDSTADDR_FIELD_NUMBER;
        hash = (53 * hash) + getOutDstAddr();
      }
      if (hasOuterIpv6Src()) {
        hash = (37 * hash) + OUTER_IPV6_SRC_FIELD_NUMBER;
        hash = (53 * hash) + getOuterIpv6Src().hashCode();
      }
      if (hasOuterIpv6Dst()) {
        hash = (37 * hash) + OUTER_IPV6_DST_FIELD_NUMBER;
        hash = (53 * hash) + getOuterIpv6Dst().hashCode();
      }
      if (hasOutSrcPort()) {
        hash = (37 * hash) + OUTSRCPORT_FIELD_NUMBER;
        hash = (53 * hash) + getOutSrcPort();
      }
      if (hasOutDstPort()) {
        hash = (37 * hash) + OUTDSTPORT_FIELD_NUMBER;
        hash = (53 * hash) + getOutDstPort();
      }
      if (hasOutTransProto()) {
        hash = (37 * hash) + OUTTRANSPROTO_FIELD_NUMBER;
        hash = (53 * hash) + getOutTransProto();
      }
      if (hasCaptureTime()) {
        hash = (37 * hash) + CAPTURETIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCaptureTime());
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static CommonInfoOuterClass.CommonInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static CommonInfoOuterClass.CommonInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static CommonInfoOuterClass.CommonInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static CommonInfoOuterClass.CommonInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(CommonInfoOuterClass.CommonInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code CommonInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CommonInfo)
        CommonInfoOuterClass.CommonInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return CommonInfoOuterClass.internal_static_CommonInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return CommonInfoOuterClass.internal_static_CommonInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                CommonInfoOuterClass.CommonInfo.class, CommonInfoOuterClass.CommonInfo.Builder.class);
      }

      // Construct using CommonInfoOuterClass.CommonInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        bitField2_ = 0;
        srcMacOui_ = com.google.protobuf.ByteString.EMPTY;
        dstMacOui_ = com.google.protobuf.ByteString.EMPTY;
        lineName1_ = com.google.protobuf.ByteString.EMPTY;
        lineName2_ = com.google.protobuf.ByteString.EMPTY;
        begTime_ = 0L;
        tagsRule_ = com.google.protobuf.ByteString.EMPTY;
        endTime_ = 0L;
        comDur_ = 0;
        meanID_ = com.google.protobuf.ByteString.EMPTY;
        siteID_ = com.google.protobuf.ByteString.EMPTY;
        unitID_ = com.google.protobuf.ByteString.EMPTY;
        taskID_ = com.google.protobuf.ByteString.EMPTY;
        guid_ = 0L;
        stortime_ = 0L;
        mdsecdeg_ = com.google.protobuf.ByteString.EMPTY;
        filesecdeg_ = com.google.protobuf.ByteString.EMPTY;
        secdegpro_ = com.google.protobuf.ByteString.EMPTY;
        commipVer_ = 0;
        commsrcAddr_ = 0L;
        commdstAddr_ = 0L;
        commsrcPort_ = 0;
        commdstPort_ = 0;
        commprotNum_ = 0;
        commsrcAddrV6_ = com.google.protobuf.ByteString.EMPTY;
        commdstAddrV6_ = com.google.protobuf.ByteString.EMPTY;
        commprotInfo_ = com.google.protobuf.ByteString.EMPTY;
        commprotType_ = com.google.protobuf.ByteString.EMPTY;
        commprotName_ = com.google.protobuf.ByteString.EMPTY;
        commmulRouFlag_ = 0;
        commintFlag_ = 0;
        commstrDirec_ = 0;
        commpktNum_ = 0;
        commpayLen_ = 0L;
        commstreamId_ = 0L;
        commetags_ = com.google.protobuf.ByteString.EMPTY;
        commttags_ = com.google.protobuf.ByteString.EMPTY;
        commatags_ = com.google.protobuf.ByteString.EMPTY;
        commutags_ = com.google.protobuf.ByteString.EMPTY;
        commlable1_ = 0;
        commlable2_ = 0;
        commlable3_ = 0;
        commlable4_ = 0;
        commvlanID1_ = 0;
        commvlanID2_ = 0;
        commsrcMac_ = com.google.protobuf.ByteString.EMPTY;
        commdstMac_ = com.google.protobuf.ByteString.EMPTY;
        commtunnelID_ = 0;
        commsrcCountry_ = com.google.protobuf.ByteString.EMPTY;
        commsrcState_ = com.google.protobuf.ByteString.EMPTY;
        commsrcCity_ = com.google.protobuf.ByteString.EMPTY;
        commsrcLongitude_ = 0F;
        commsrcLatitude_ = 0F;
        commsrcISP_ = com.google.protobuf.ByteString.EMPTY;
        commsrcASN_ = 0;
        commdstCountry_ = com.google.protobuf.ByteString.EMPTY;
        commdstState_ = com.google.protobuf.ByteString.EMPTY;
        commdstCity_ = com.google.protobuf.ByteString.EMPTY;
        commdstLongitude_ = 0F;
        commdstLatitude_ = 0F;
        commdstISP_ = com.google.protobuf.ByteString.EMPTY;
        commdstASN_ = 0;
        outAddrType_ = 0;
        outSrcAddr_ = 0;
        outDstAddr_ = 0;
        outerIpv6Src_ = com.google.protobuf.ByteString.EMPTY;
        outerIpv6Dst_ = com.google.protobuf.ByteString.EMPTY;
        outSrcPort_ = 0;
        outDstPort_ = 0;
        outTransProto_ = 0;
        captureTime_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return CommonInfoOuterClass.internal_static_CommonInfo_descriptor;
      }

      @java.lang.Override
      public CommonInfoOuterClass.CommonInfo getDefaultInstanceForType() {
        return CommonInfoOuterClass.CommonInfo.getDefaultInstance();
      }

      @java.lang.Override
      public CommonInfoOuterClass.CommonInfo build() {
        CommonInfoOuterClass.CommonInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public CommonInfoOuterClass.CommonInfo buildPartial() {
        CommonInfoOuterClass.CommonInfo result = new CommonInfoOuterClass.CommonInfo(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        if (bitField2_ != 0) { buildPartial2(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(CommonInfoOuterClass.CommonInfo result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.srcMacOui_ = srcMacOui_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.dstMacOui_ = dstMacOui_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.lineName1_ = lineName1_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lineName2_ = lineName2_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.begTime_ = begTime_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.tagsRule_ = tagsRule_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.endTime_ = endTime_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.comDur_ = comDur_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.meanID_ = meanID_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.siteID_ = siteID_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.unitID_ = unitID_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.taskID_ = taskID_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.guid_ = guid_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.stortime_ = stortime_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.mdsecdeg_ = mdsecdeg_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.filesecdeg_ = filesecdeg_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.secdegpro_ = secdegpro_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.commipVer_ = commipVer_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.commsrcAddr_ = commsrcAddr_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.commdstAddr_ = commdstAddr_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.commsrcPort_ = commsrcPort_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.commdstPort_ = commdstPort_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.commprotNum_ = commprotNum_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.commsrcAddrV6_ = commsrcAddrV6_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.commdstAddrV6_ = commdstAddrV6_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.commprotInfo_ = commprotInfo_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.commprotType_ = commprotType_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.commprotName_ = commprotName_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.commmulRouFlag_ = commmulRouFlag_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.commintFlag_ = commintFlag_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.commstrDirec_ = commstrDirec_;
          to_bitField0_ |= 0x40000000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.commpktNum_ = commpktNum_;
          to_bitField0_ |= 0x80000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(CommonInfoOuterClass.CommonInfo result) {
        int from_bitField1_ = bitField1_;
        int to_bitField1_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.commpayLen_ = commpayLen_;
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.commstreamId_ = commstreamId_;
          to_bitField1_ |= 0x00000002;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.commetags_ = commetags_;
          to_bitField1_ |= 0x00000004;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.commttags_ = commttags_;
          to_bitField1_ |= 0x00000008;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.commatags_ = commatags_;
          to_bitField1_ |= 0x00000010;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.commutags_ = commutags_;
          to_bitField1_ |= 0x00000020;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.commlable1_ = commlable1_;
          to_bitField1_ |= 0x00000040;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          result.commlable2_ = commlable2_;
          to_bitField1_ |= 0x00000080;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.commlable3_ = commlable3_;
          to_bitField1_ |= 0x00000100;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          result.commlable4_ = commlable4_;
          to_bitField1_ |= 0x00000200;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          result.commvlanID1_ = commvlanID1_;
          to_bitField1_ |= 0x00000400;
        }
        if (((from_bitField1_ & 0x00000800) != 0)) {
          result.commvlanID2_ = commvlanID2_;
          to_bitField1_ |= 0x00000800;
        }
        if (((from_bitField1_ & 0x00001000) != 0)) {
          result.commsrcMac_ = commsrcMac_;
          to_bitField1_ |= 0x00001000;
        }
        if (((from_bitField1_ & 0x00002000) != 0)) {
          result.commdstMac_ = commdstMac_;
          to_bitField1_ |= 0x00002000;
        }
        if (((from_bitField1_ & 0x00004000) != 0)) {
          result.commtunnelID_ = commtunnelID_;
          to_bitField1_ |= 0x00004000;
        }
        if (((from_bitField1_ & 0x00008000) != 0)) {
          result.commsrcCountry_ = commsrcCountry_;
          to_bitField1_ |= 0x00008000;
        }
        if (((from_bitField1_ & 0x00010000) != 0)) {
          result.commsrcState_ = commsrcState_;
          to_bitField1_ |= 0x00010000;
        }
        if (((from_bitField1_ & 0x00020000) != 0)) {
          result.commsrcCity_ = commsrcCity_;
          to_bitField1_ |= 0x00020000;
        }
        if (((from_bitField1_ & 0x00040000) != 0)) {
          result.commsrcLongitude_ = commsrcLongitude_;
          to_bitField1_ |= 0x00040000;
        }
        if (((from_bitField1_ & 0x00080000) != 0)) {
          result.commsrcLatitude_ = commsrcLatitude_;
          to_bitField1_ |= 0x00080000;
        }
        if (((from_bitField1_ & 0x00100000) != 0)) {
          result.commsrcISP_ = commsrcISP_;
          to_bitField1_ |= 0x00100000;
        }
        if (((from_bitField1_ & 0x00200000) != 0)) {
          result.commsrcASN_ = commsrcASN_;
          to_bitField1_ |= 0x00200000;
        }
        if (((from_bitField1_ & 0x00400000) != 0)) {
          result.commdstCountry_ = commdstCountry_;
          to_bitField1_ |= 0x00400000;
        }
        if (((from_bitField1_ & 0x00800000) != 0)) {
          result.commdstState_ = commdstState_;
          to_bitField1_ |= 0x00800000;
        }
        if (((from_bitField1_ & 0x01000000) != 0)) {
          result.commdstCity_ = commdstCity_;
          to_bitField1_ |= 0x01000000;
        }
        if (((from_bitField1_ & 0x02000000) != 0)) {
          result.commdstLongitude_ = commdstLongitude_;
          to_bitField1_ |= 0x02000000;
        }
        if (((from_bitField1_ & 0x04000000) != 0)) {
          result.commdstLatitude_ = commdstLatitude_;
          to_bitField1_ |= 0x04000000;
        }
        if (((from_bitField1_ & 0x08000000) != 0)) {
          result.commdstISP_ = commdstISP_;
          to_bitField1_ |= 0x08000000;
        }
        if (((from_bitField1_ & 0x10000000) != 0)) {
          result.commdstASN_ = commdstASN_;
          to_bitField1_ |= 0x10000000;
        }
        if (((from_bitField1_ & 0x20000000) != 0)) {
          result.outAddrType_ = outAddrType_;
          to_bitField1_ |= 0x20000000;
        }
        if (((from_bitField1_ & 0x40000000) != 0)) {
          result.outSrcAddr_ = outSrcAddr_;
          to_bitField1_ |= 0x40000000;
        }
        if (((from_bitField1_ & 0x80000000) != 0)) {
          result.outDstAddr_ = outDstAddr_;
          to_bitField1_ |= 0x80000000;
        }
        result.bitField1_ |= to_bitField1_;
      }

      private void buildPartial2(CommonInfoOuterClass.CommonInfo result) {
        int from_bitField2_ = bitField2_;
        int to_bitField2_ = 0;
        if (((from_bitField2_ & 0x00000001) != 0)) {
          result.outerIpv6Src_ = outerIpv6Src_;
          to_bitField2_ |= 0x00000001;
        }
        if (((from_bitField2_ & 0x00000002) != 0)) {
          result.outerIpv6Dst_ = outerIpv6Dst_;
          to_bitField2_ |= 0x00000002;
        }
        if (((from_bitField2_ & 0x00000004) != 0)) {
          result.outSrcPort_ = outSrcPort_;
          to_bitField2_ |= 0x00000004;
        }
        if (((from_bitField2_ & 0x00000008) != 0)) {
          result.outDstPort_ = outDstPort_;
          to_bitField2_ |= 0x00000008;
        }
        if (((from_bitField2_ & 0x00000010) != 0)) {
          result.outTransProto_ = outTransProto_;
          to_bitField2_ |= 0x00000010;
        }
        if (((from_bitField2_ & 0x00000020) != 0)) {
          result.captureTime_ = captureTime_;
          to_bitField2_ |= 0x00000020;
        }
        result.bitField2_ |= to_bitField2_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof CommonInfoOuterClass.CommonInfo) {
          return mergeFrom((CommonInfoOuterClass.CommonInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(CommonInfoOuterClass.CommonInfo other) {
        if (other == CommonInfoOuterClass.CommonInfo.getDefaultInstance()) return this;
        if (other.hasSrcMacOui()) {
          setSrcMacOui(other.getSrcMacOui());
        }
        if (other.hasDstMacOui()) {
          setDstMacOui(other.getDstMacOui());
        }
        if (other.hasLineName1()) {
          setLineName1(other.getLineName1());
        }
        if (other.hasLineName2()) {
          setLineName2(other.getLineName2());
        }
        if (other.hasBegTime()) {
          setBegTime(other.getBegTime());
        }
        if (other.hasTagsRule()) {
          setTagsRule(other.getTagsRule());
        }
        if (other.hasEndTime()) {
          setEndTime(other.getEndTime());
        }
        if (other.hasComDur()) {
          setComDur(other.getComDur());
        }
        if (other.hasMeanID()) {
          setMeanID(other.getMeanID());
        }
        if (other.hasSiteID()) {
          setSiteID(other.getSiteID());
        }
        if (other.hasUnitID()) {
          setUnitID(other.getUnitID());
        }
        if (other.hasTaskID()) {
          setTaskID(other.getTaskID());
        }
        if (other.hasGuid()) {
          setGuid(other.getGuid());
        }
        if (other.hasStortime()) {
          setStortime(other.getStortime());
        }
        if (other.hasMdsecdeg()) {
          setMdsecdeg(other.getMdsecdeg());
        }
        if (other.hasFilesecdeg()) {
          setFilesecdeg(other.getFilesecdeg());
        }
        if (other.hasSecdegpro()) {
          setSecdegpro(other.getSecdegpro());
        }
        if (other.hasCommipVer()) {
          setCommipVer(other.getCommipVer());
        }
        if (other.hasCommsrcAddr()) {
          setCommsrcAddr(other.getCommsrcAddr());
        }
        if (other.hasCommdstAddr()) {
          setCommdstAddr(other.getCommdstAddr());
        }
        if (other.hasCommsrcPort()) {
          setCommsrcPort(other.getCommsrcPort());
        }
        if (other.hasCommdstPort()) {
          setCommdstPort(other.getCommdstPort());
        }
        if (other.hasCommprotNum()) {
          setCommprotNum(other.getCommprotNum());
        }
        if (other.hasCommsrcAddrV6()) {
          setCommsrcAddrV6(other.getCommsrcAddrV6());
        }
        if (other.hasCommdstAddrV6()) {
          setCommdstAddrV6(other.getCommdstAddrV6());
        }
        if (other.hasCommprotInfo()) {
          setCommprotInfo(other.getCommprotInfo());
        }
        if (other.hasCommprotType()) {
          setCommprotType(other.getCommprotType());
        }
        if (other.hasCommprotName()) {
          setCommprotName(other.getCommprotName());
        }
        if (other.hasCommmulRouFlag()) {
          setCommmulRouFlag(other.getCommmulRouFlag());
        }
        if (other.hasCommintFlag()) {
          setCommintFlag(other.getCommintFlag());
        }
        if (other.hasCommstrDirec()) {
          setCommstrDirec(other.getCommstrDirec());
        }
        if (other.hasCommpktNum()) {
          setCommpktNum(other.getCommpktNum());
        }
        if (other.hasCommpayLen()) {
          setCommpayLen(other.getCommpayLen());
        }
        if (other.hasCommstreamId()) {
          setCommstreamId(other.getCommstreamId());
        }
        if (other.hasCommetags()) {
          setCommetags(other.getCommetags());
        }
        if (other.hasCommttags()) {
          setCommttags(other.getCommttags());
        }
        if (other.hasCommatags()) {
          setCommatags(other.getCommatags());
        }
        if (other.hasCommutags()) {
          setCommutags(other.getCommutags());
        }
        if (other.hasCommlable1()) {
          setCommlable1(other.getCommlable1());
        }
        if (other.hasCommlable2()) {
          setCommlable2(other.getCommlable2());
        }
        if (other.hasCommlable3()) {
          setCommlable3(other.getCommlable3());
        }
        if (other.hasCommlable4()) {
          setCommlable4(other.getCommlable4());
        }
        if (other.hasCommvlanID1()) {
          setCommvlanID1(other.getCommvlanID1());
        }
        if (other.hasCommvlanID2()) {
          setCommvlanID2(other.getCommvlanID2());
        }
        if (other.hasCommsrcMac()) {
          setCommsrcMac(other.getCommsrcMac());
        }
        if (other.hasCommdstMac()) {
          setCommdstMac(other.getCommdstMac());
        }
        if (other.hasCommtunnelID()) {
          setCommtunnelID(other.getCommtunnelID());
        }
        if (other.hasCommsrcCountry()) {
          setCommsrcCountry(other.getCommsrcCountry());
        }
        if (other.hasCommsrcState()) {
          setCommsrcState(other.getCommsrcState());
        }
        if (other.hasCommsrcCity()) {
          setCommsrcCity(other.getCommsrcCity());
        }
        if (other.hasCommsrcLongitude()) {
          setCommsrcLongitude(other.getCommsrcLongitude());
        }
        if (other.hasCommsrcLatitude()) {
          setCommsrcLatitude(other.getCommsrcLatitude());
        }
        if (other.hasCommsrcISP()) {
          setCommsrcISP(other.getCommsrcISP());
        }
        if (other.hasCommsrcASN()) {
          setCommsrcASN(other.getCommsrcASN());
        }
        if (other.hasCommdstCountry()) {
          setCommdstCountry(other.getCommdstCountry());
        }
        if (other.hasCommdstState()) {
          setCommdstState(other.getCommdstState());
        }
        if (other.hasCommdstCity()) {
          setCommdstCity(other.getCommdstCity());
        }
        if (other.hasCommdstLongitude()) {
          setCommdstLongitude(other.getCommdstLongitude());
        }
        if (other.hasCommdstLatitude()) {
          setCommdstLatitude(other.getCommdstLatitude());
        }
        if (other.hasCommdstISP()) {
          setCommdstISP(other.getCommdstISP());
        }
        if (other.hasCommdstASN()) {
          setCommdstASN(other.getCommdstASN());
        }
        if (other.hasOutAddrType()) {
          setOutAddrType(other.getOutAddrType());
        }
        if (other.hasOutSrcAddr()) {
          setOutSrcAddr(other.getOutSrcAddr());
        }
        if (other.hasOutDstAddr()) {
          setOutDstAddr(other.getOutDstAddr());
        }
        if (other.hasOuterIpv6Src()) {
          setOuterIpv6Src(other.getOuterIpv6Src());
        }
        if (other.hasOuterIpv6Dst()) {
          setOuterIpv6Dst(other.getOuterIpv6Dst());
        }
        if (other.hasOutSrcPort()) {
          setOutSrcPort(other.getOutSrcPort());
        }
        if (other.hasOutDstPort()) {
          setOutDstPort(other.getOutDstPort());
        }
        if (other.hasOutTransProto()) {
          setOutTransProto(other.getOutTransProto());
        }
        if (other.hasCaptureTime()) {
          setCaptureTime(other.getCaptureTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                srcMacOui_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                dstMacOui_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                lineName1_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                lineName2_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                begTime_ = input.readUInt64();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 50: {
                tagsRule_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 56: {
                endTime_ = input.readUInt64();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                comDur_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 74: {
                meanID_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                siteID_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                unitID_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                taskID_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 104: {
                guid_ = input.readUInt64();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                stortime_ = input.readUInt64();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 122: {
                mdsecdeg_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                filesecdeg_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                secdegpro_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 144: {
                commipVer_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 152: {
                commsrcAddr_ = input.readUInt64();
                bitField0_ |= 0x00040000;
                break;
              } // case 152
              case 160: {
                commdstAddr_ = input.readUInt64();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              case 168: {
                commsrcPort_ = input.readUInt32();
                bitField0_ |= 0x00100000;
                break;
              } // case 168
              case 176: {
                commdstPort_ = input.readUInt32();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              case 184: {
                commprotNum_ = input.readUInt32();
                bitField0_ |= 0x00400000;
                break;
              } // case 184
              case 194: {
                commsrcAddrV6_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                commdstAddrV6_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                commprotInfo_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 218: {
                commprotType_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 226: {
                commprotName_ = input.readBytes();
                bitField0_ |= 0x08000000;
                break;
              } // case 226
              case 232: {
                commmulRouFlag_ = input.readUInt32();
                bitField0_ |= 0x10000000;
                break;
              } // case 232
              case 240: {
                commintFlag_ = input.readUInt32();
                bitField0_ |= 0x20000000;
                break;
              } // case 240
              case 248: {
                commstrDirec_ = input.readUInt32();
                bitField0_ |= 0x40000000;
                break;
              } // case 248
              case 256: {
                commpktNum_ = input.readUInt32();
                bitField0_ |= 0x80000000;
                break;
              } // case 256
              case 264: {
                commpayLen_ = input.readUInt64();
                bitField1_ |= 0x00000001;
                break;
              } // case 264
              case 272: {
                commstreamId_ = input.readUInt64();
                bitField1_ |= 0x00000002;
                break;
              } // case 272
              case 282: {
                commetags_ = input.readBytes();
                bitField1_ |= 0x00000004;
                break;
              } // case 282
              case 290: {
                commttags_ = input.readBytes();
                bitField1_ |= 0x00000008;
                break;
              } // case 290
              case 298: {
                commatags_ = input.readBytes();
                bitField1_ |= 0x00000010;
                break;
              } // case 298
              case 306: {
                commutags_ = input.readBytes();
                bitField1_ |= 0x00000020;
                break;
              } // case 306
              case 312: {
                commlable1_ = input.readUInt32();
                bitField1_ |= 0x00000040;
                break;
              } // case 312
              case 320: {
                commlable2_ = input.readUInt32();
                bitField1_ |= 0x00000080;
                break;
              } // case 320
              case 328: {
                commlable3_ = input.readUInt32();
                bitField1_ |= 0x00000100;
                break;
              } // case 328
              case 336: {
                commlable4_ = input.readUInt32();
                bitField1_ |= 0x00000200;
                break;
              } // case 336
              case 344: {
                commvlanID1_ = input.readUInt32();
                bitField1_ |= 0x00000400;
                break;
              } // case 344
              case 352: {
                commvlanID2_ = input.readUInt32();
                bitField1_ |= 0x00000800;
                break;
              } // case 352
              case 362: {
                commsrcMac_ = input.readBytes();
                bitField1_ |= 0x00001000;
                break;
              } // case 362
              case 370: {
                commdstMac_ = input.readBytes();
                bitField1_ |= 0x00002000;
                break;
              } // case 370
              case 376: {
                commtunnelID_ = input.readUInt32();
                bitField1_ |= 0x00004000;
                break;
              } // case 376
              case 386: {
                commsrcCountry_ = input.readBytes();
                bitField1_ |= 0x00008000;
                break;
              } // case 386
              case 394: {
                commsrcState_ = input.readBytes();
                bitField1_ |= 0x00010000;
                break;
              } // case 394
              case 402: {
                commsrcCity_ = input.readBytes();
                bitField1_ |= 0x00020000;
                break;
              } // case 402
              case 413: {
                commsrcLongitude_ = input.readFloat();
                bitField1_ |= 0x00040000;
                break;
              } // case 413
              case 421: {
                commsrcLatitude_ = input.readFloat();
                bitField1_ |= 0x00080000;
                break;
              } // case 421
              case 426: {
                commsrcISP_ = input.readBytes();
                bitField1_ |= 0x00100000;
                break;
              } // case 426
              case 432: {
                commsrcASN_ = input.readUInt32();
                bitField1_ |= 0x00200000;
                break;
              } // case 432
              case 442: {
                commdstCountry_ = input.readBytes();
                bitField1_ |= 0x00400000;
                break;
              } // case 442
              case 450: {
                commdstState_ = input.readBytes();
                bitField1_ |= 0x00800000;
                break;
              } // case 450
              case 458: {
                commdstCity_ = input.readBytes();
                bitField1_ |= 0x01000000;
                break;
              } // case 458
              case 469: {
                commdstLongitude_ = input.readFloat();
                bitField1_ |= 0x02000000;
                break;
              } // case 469
              case 477: {
                commdstLatitude_ = input.readFloat();
                bitField1_ |= 0x04000000;
                break;
              } // case 477
              case 482: {
                commdstISP_ = input.readBytes();
                bitField1_ |= 0x08000000;
                break;
              } // case 482
              case 488: {
                commdstASN_ = input.readUInt32();
                bitField1_ |= 0x10000000;
                break;
              } // case 488
              case 496: {
                outAddrType_ = input.readUInt32();
                bitField1_ |= 0x20000000;
                break;
              } // case 496
              case 504: {
                outSrcAddr_ = input.readUInt32();
                bitField1_ |= 0x40000000;
                break;
              } // case 504
              case 512: {
                outDstAddr_ = input.readUInt32();
                bitField1_ |= 0x80000000;
                break;
              } // case 512
              case 522: {
                outerIpv6Src_ = input.readBytes();
                bitField2_ |= 0x00000001;
                break;
              } // case 522
              case 530: {
                outerIpv6Dst_ = input.readBytes();
                bitField2_ |= 0x00000002;
                break;
              } // case 530
              case 536: {
                outSrcPort_ = input.readUInt32();
                bitField2_ |= 0x00000004;
                break;
              } // case 536
              case 544: {
                outDstPort_ = input.readUInt32();
                bitField2_ |= 0x00000008;
                break;
              } // case 544
              case 552: {
                outTransProto_ = input.readUInt32();
                bitField2_ |= 0x00000010;
                break;
              } // case 552
              case 560: {
                captureTime_ = input.readUInt64();
                bitField2_ |= 0x00000020;
                break;
              } // case 560
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;

      private com.google.protobuf.ByteString srcMacOui_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srcMacOui = 1;</code>
       * @return Whether the srcMacOui field is set.
       */
      @java.lang.Override
      public boolean hasSrcMacOui() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes srcMacOui = 1;</code>
       * @return The srcMacOui.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrcMacOui() {
        return srcMacOui_;
      }
      /**
       * <code>optional bytes srcMacOui = 1;</code>
       * @param value The srcMacOui to set.
       * @return This builder for chaining.
       */
      public Builder setSrcMacOui(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srcMacOui_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srcMacOui = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrcMacOui() {
        bitField0_ = (bitField0_ & ~0x00000001);
        srcMacOui_ = getDefaultInstance().getSrcMacOui();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dstMacOui_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes dstMacOui = 2;</code>
       * @return Whether the dstMacOui field is set.
       */
      @java.lang.Override
      public boolean hasDstMacOui() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes dstMacOui = 2;</code>
       * @return The dstMacOui.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDstMacOui() {
        return dstMacOui_;
      }
      /**
       * <code>optional bytes dstMacOui = 2;</code>
       * @param value The dstMacOui to set.
       * @return This builder for chaining.
       */
      public Builder setDstMacOui(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dstMacOui_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes dstMacOui = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDstMacOui() {
        bitField0_ = (bitField0_ & ~0x00000002);
        dstMacOui_ = getDefaultInstance().getDstMacOui();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString lineName1_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes lineName1 = 3;</code>
       * @return Whether the lineName1 field is set.
       */
      @java.lang.Override
      public boolean hasLineName1() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes lineName1 = 3;</code>
       * @return The lineName1.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getLineName1() {
        return lineName1_;
      }
      /**
       * <code>optional bytes lineName1 = 3;</code>
       * @param value The lineName1 to set.
       * @return This builder for chaining.
       */
      public Builder setLineName1(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        lineName1_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes lineName1 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineName1() {
        bitField0_ = (bitField0_ & ~0x00000004);
        lineName1_ = getDefaultInstance().getLineName1();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString lineName2_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes lineName2 = 4;</code>
       * @return Whether the lineName2 field is set.
       */
      @java.lang.Override
      public boolean hasLineName2() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes lineName2 = 4;</code>
       * @return The lineName2.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getLineName2() {
        return lineName2_;
      }
      /**
       * <code>optional bytes lineName2 = 4;</code>
       * @param value The lineName2 to set.
       * @return This builder for chaining.
       */
      public Builder setLineName2(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        lineName2_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes lineName2 = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineName2() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lineName2_ = getDefaultInstance().getLineName2();
        onChanged();
        return this;
      }

      private long begTime_ ;
      /**
       * <code>optional uint64 begTime = 5;</code>
       * @return Whether the begTime field is set.
       */
      @java.lang.Override
      public boolean hasBegTime() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint64 begTime = 5;</code>
       * @return The begTime.
       */
      @java.lang.Override
      public long getBegTime() {
        return begTime_;
      }
      /**
       * <code>optional uint64 begTime = 5;</code>
       * @param value The begTime to set.
       * @return This builder for chaining.
       */
      public Builder setBegTime(long value) {

        begTime_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 begTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearBegTime() {
        bitField0_ = (bitField0_ & ~0x00000010);
        begTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString tagsRule_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes tagsRule = 6;</code>
       * @return Whether the tagsRule field is set.
       */
      @java.lang.Override
      public boolean hasTagsRule() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes tagsRule = 6;</code>
       * @return The tagsRule.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTagsRule() {
        return tagsRule_;
      }
      /**
       * <code>optional bytes tagsRule = 6;</code>
       * @param value The tagsRule to set.
       * @return This builder for chaining.
       */
      public Builder setTagsRule(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        tagsRule_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes tagsRule = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTagsRule() {
        bitField0_ = (bitField0_ & ~0x00000020);
        tagsRule_ = getDefaultInstance().getTagsRule();
        onChanged();
        return this;
      }

      private long endTime_ ;
      /**
       * <code>optional uint64 endTime = 7;</code>
       * @return Whether the endTime field is set.
       */
      @java.lang.Override
      public boolean hasEndTime() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint64 endTime = 7;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public long getEndTime() {
        return endTime_;
      }
      /**
       * <code>optional uint64 endTime = 7;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(long value) {

        endTime_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 endTime = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        bitField0_ = (bitField0_ & ~0x00000040);
        endTime_ = 0L;
        onChanged();
        return this;
      }

      private int comDur_ ;
      /**
       * <code>optional uint32 comDur = 8;</code>
       * @return Whether the comDur field is set.
       */
      @java.lang.Override
      public boolean hasComDur() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 comDur = 8;</code>
       * @return The comDur.
       */
      @java.lang.Override
      public int getComDur() {
        return comDur_;
      }
      /**
       * <code>optional uint32 comDur = 8;</code>
       * @param value The comDur to set.
       * @return This builder for chaining.
       */
      public Builder setComDur(int value) {

        comDur_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 comDur = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearComDur() {
        bitField0_ = (bitField0_ & ~0x00000080);
        comDur_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString meanID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes meanID = 9;</code>
       * @return Whether the meanID field is set.
       */
      @java.lang.Override
      public boolean hasMeanID() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes meanID = 9;</code>
       * @return The meanID.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMeanID() {
        return meanID_;
      }
      /**
       * <code>optional bytes meanID = 9;</code>
       * @param value The meanID to set.
       * @return This builder for chaining.
       */
      public Builder setMeanID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        meanID_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes meanID = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearMeanID() {
        bitField0_ = (bitField0_ & ~0x00000100);
        meanID_ = getDefaultInstance().getMeanID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString siteID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes siteID = 10;</code>
       * @return Whether the siteID field is set.
       */
      @java.lang.Override
      public boolean hasSiteID() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes siteID = 10;</code>
       * @return The siteID.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSiteID() {
        return siteID_;
      }
      /**
       * <code>optional bytes siteID = 10;</code>
       * @param value The siteID to set.
       * @return This builder for chaining.
       */
      public Builder setSiteID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        siteID_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes siteID = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearSiteID() {
        bitField0_ = (bitField0_ & ~0x00000200);
        siteID_ = getDefaultInstance().getSiteID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString unitID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes unitID = 11;</code>
       * @return Whether the unitID field is set.
       */
      @java.lang.Override
      public boolean hasUnitID() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes unitID = 11;</code>
       * @return The unitID.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUnitID() {
        return unitID_;
      }
      /**
       * <code>optional bytes unitID = 11;</code>
       * @param value The unitID to set.
       * @return This builder for chaining.
       */
      public Builder setUnitID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        unitID_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes unitID = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnitID() {
        bitField0_ = (bitField0_ & ~0x00000400);
        unitID_ = getDefaultInstance().getUnitID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString taskID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes taskID = 12;</code>
       * @return Whether the taskID field is set.
       */
      @java.lang.Override
      public boolean hasTaskID() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes taskID = 12;</code>
       * @return The taskID.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTaskID() {
        return taskID_;
      }
      /**
       * <code>optional bytes taskID = 12;</code>
       * @param value The taskID to set.
       * @return This builder for chaining.
       */
      public Builder setTaskID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        taskID_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes taskID = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearTaskID() {
        bitField0_ = (bitField0_ & ~0x00000800);
        taskID_ = getDefaultInstance().getTaskID();
        onChanged();
        return this;
      }

      private long guid_ ;
      /**
       * <code>optional uint64 guid = 13;</code>
       * @return Whether the guid field is set.
       */
      @java.lang.Override
      public boolean hasGuid() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint64 guid = 13;</code>
       * @return The guid.
       */
      @java.lang.Override
      public long getGuid() {
        return guid_;
      }
      /**
       * <code>optional uint64 guid = 13;</code>
       * @param value The guid to set.
       * @return This builder for chaining.
       */
      public Builder setGuid(long value) {

        guid_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 guid = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearGuid() {
        bitField0_ = (bitField0_ & ~0x00001000);
        guid_ = 0L;
        onChanged();
        return this;
      }

      private long stortime_ ;
      /**
       * <code>optional uint64 stortime = 14;</code>
       * @return Whether the stortime field is set.
       */
      @java.lang.Override
      public boolean hasStortime() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint64 stortime = 14;</code>
       * @return The stortime.
       */
      @java.lang.Override
      public long getStortime() {
        return stortime_;
      }
      /**
       * <code>optional uint64 stortime = 14;</code>
       * @param value The stortime to set.
       * @return This builder for chaining.
       */
      public Builder setStortime(long value) {

        stortime_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 stortime = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearStortime() {
        bitField0_ = (bitField0_ & ~0x00002000);
        stortime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString mdsecdeg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes mdsecdeg = 15;</code>
       * @return Whether the mdsecdeg field is set.
       */
      @java.lang.Override
      public boolean hasMdsecdeg() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes mdsecdeg = 15;</code>
       * @return The mdsecdeg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMdsecdeg() {
        return mdsecdeg_;
      }
      /**
       * <code>optional bytes mdsecdeg = 15;</code>
       * @param value The mdsecdeg to set.
       * @return This builder for chaining.
       */
      public Builder setMdsecdeg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        mdsecdeg_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes mdsecdeg = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearMdsecdeg() {
        bitField0_ = (bitField0_ & ~0x00004000);
        mdsecdeg_ = getDefaultInstance().getMdsecdeg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString filesecdeg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes filesecdeg = 16;</code>
       * @return Whether the filesecdeg field is set.
       */
      @java.lang.Override
      public boolean hasFilesecdeg() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes filesecdeg = 16;</code>
       * @return The filesecdeg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFilesecdeg() {
        return filesecdeg_;
      }
      /**
       * <code>optional bytes filesecdeg = 16;</code>
       * @param value The filesecdeg to set.
       * @return This builder for chaining.
       */
      public Builder setFilesecdeg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        filesecdeg_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes filesecdeg = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearFilesecdeg() {
        bitField0_ = (bitField0_ & ~0x00008000);
        filesecdeg_ = getDefaultInstance().getFilesecdeg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString secdegpro_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes secdegpro = 17;</code>
       * @return Whether the secdegpro field is set.
       */
      @java.lang.Override
      public boolean hasSecdegpro() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes secdegpro = 17;</code>
       * @return The secdegpro.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSecdegpro() {
        return secdegpro_;
      }
      /**
       * <code>optional bytes secdegpro = 17;</code>
       * @param value The secdegpro to set.
       * @return This builder for chaining.
       */
      public Builder setSecdegpro(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        secdegpro_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes secdegpro = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearSecdegpro() {
        bitField0_ = (bitField0_ & ~0x00010000);
        secdegpro_ = getDefaultInstance().getSecdegpro();
        onChanged();
        return this;
      }

      private int commipVer_ ;
      /**
       * <code>optional uint32 commipVer = 18;</code>
       * @return Whether the commipVer field is set.
       */
      @java.lang.Override
      public boolean hasCommipVer() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 commipVer = 18;</code>
       * @return The commipVer.
       */
      @java.lang.Override
      public int getCommipVer() {
        return commipVer_;
      }
      /**
       * <code>optional uint32 commipVer = 18;</code>
       * @param value The commipVer to set.
       * @return This builder for chaining.
       */
      public Builder setCommipVer(int value) {

        commipVer_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commipVer = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommipVer() {
        bitField0_ = (bitField0_ & ~0x00020000);
        commipVer_ = 0;
        onChanged();
        return this;
      }

      private long commsrcAddr_ ;
      /**
       * <code>optional uint64 commsrcAddr = 19;</code>
       * @return Whether the commsrcAddr field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcAddr() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional uint64 commsrcAddr = 19;</code>
       * @return The commsrcAddr.
       */
      @java.lang.Override
      public long getCommsrcAddr() {
        return commsrcAddr_;
      }
      /**
       * <code>optional uint64 commsrcAddr = 19;</code>
       * @param value The commsrcAddr to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcAddr(long value) {

        commsrcAddr_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 commsrcAddr = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcAddr() {
        bitField0_ = (bitField0_ & ~0x00040000);
        commsrcAddr_ = 0L;
        onChanged();
        return this;
      }

      private long commdstAddr_ ;
      /**
       * <code>optional uint64 commdstAddr = 20;</code>
       * @return Whether the commdstAddr field is set.
       */
      @java.lang.Override
      public boolean hasCommdstAddr() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint64 commdstAddr = 20;</code>
       * @return The commdstAddr.
       */
      @java.lang.Override
      public long getCommdstAddr() {
        return commdstAddr_;
      }
      /**
       * <code>optional uint64 commdstAddr = 20;</code>
       * @param value The commdstAddr to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstAddr(long value) {

        commdstAddr_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 commdstAddr = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstAddr() {
        bitField0_ = (bitField0_ & ~0x00080000);
        commdstAddr_ = 0L;
        onChanged();
        return this;
      }

      private int commsrcPort_ ;
      /**
       * <code>optional uint32 commsrcPort = 21;</code>
       * @return Whether the commsrcPort field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcPort() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional uint32 commsrcPort = 21;</code>
       * @return The commsrcPort.
       */
      @java.lang.Override
      public int getCommsrcPort() {
        return commsrcPort_;
      }
      /**
       * <code>optional uint32 commsrcPort = 21;</code>
       * @param value The commsrcPort to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcPort(int value) {

        commsrcPort_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commsrcPort = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcPort() {
        bitField0_ = (bitField0_ & ~0x00100000);
        commsrcPort_ = 0;
        onChanged();
        return this;
      }

      private int commdstPort_ ;
      /**
       * <code>optional uint32 commdstPort = 22;</code>
       * @return Whether the commdstPort field is set.
       */
      @java.lang.Override
      public boolean hasCommdstPort() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 commdstPort = 22;</code>
       * @return The commdstPort.
       */
      @java.lang.Override
      public int getCommdstPort() {
        return commdstPort_;
      }
      /**
       * <code>optional uint32 commdstPort = 22;</code>
       * @param value The commdstPort to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstPort(int value) {

        commdstPort_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commdstPort = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstPort() {
        bitField0_ = (bitField0_ & ~0x00200000);
        commdstPort_ = 0;
        onChanged();
        return this;
      }

      private int commprotNum_ ;
      /**
       * <code>optional uint32 commprotNum = 23;</code>
       * @return Whether the commprotNum field is set.
       */
      @java.lang.Override
      public boolean hasCommprotNum() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional uint32 commprotNum = 23;</code>
       * @return The commprotNum.
       */
      @java.lang.Override
      public int getCommprotNum() {
        return commprotNum_;
      }
      /**
       * <code>optional uint32 commprotNum = 23;</code>
       * @param value The commprotNum to set.
       * @return This builder for chaining.
       */
      public Builder setCommprotNum(int value) {

        commprotNum_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commprotNum = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommprotNum() {
        bitField0_ = (bitField0_ & ~0x00400000);
        commprotNum_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commsrcAddrV6_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commsrcAddrV6 = 24;</code>
       * @return Whether the commsrcAddrV6 field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcAddrV6() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes commsrcAddrV6 = 24;</code>
       * @return The commsrcAddrV6.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommsrcAddrV6() {
        return commsrcAddrV6_;
      }
      /**
       * <code>optional bytes commsrcAddrV6 = 24;</code>
       * @param value The commsrcAddrV6 to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcAddrV6(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commsrcAddrV6_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commsrcAddrV6 = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcAddrV6() {
        bitField0_ = (bitField0_ & ~0x00800000);
        commsrcAddrV6_ = getDefaultInstance().getCommsrcAddrV6();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commdstAddrV6_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commdstAddrV6 = 25;</code>
       * @return Whether the commdstAddrV6 field is set.
       */
      @java.lang.Override
      public boolean hasCommdstAddrV6() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes commdstAddrV6 = 25;</code>
       * @return The commdstAddrV6.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommdstAddrV6() {
        return commdstAddrV6_;
      }
      /**
       * <code>optional bytes commdstAddrV6 = 25;</code>
       * @param value The commdstAddrV6 to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstAddrV6(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commdstAddrV6_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commdstAddrV6 = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstAddrV6() {
        bitField0_ = (bitField0_ & ~0x01000000);
        commdstAddrV6_ = getDefaultInstance().getCommdstAddrV6();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commprotInfo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commprotInfo = 26;</code>
       * @return Whether the commprotInfo field is set.
       */
      @java.lang.Override
      public boolean hasCommprotInfo() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes commprotInfo = 26;</code>
       * @return The commprotInfo.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommprotInfo() {
        return commprotInfo_;
      }
      /**
       * <code>optional bytes commprotInfo = 26;</code>
       * @param value The commprotInfo to set.
       * @return This builder for chaining.
       */
      public Builder setCommprotInfo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commprotInfo_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commprotInfo = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommprotInfo() {
        bitField0_ = (bitField0_ & ~0x02000000);
        commprotInfo_ = getDefaultInstance().getCommprotInfo();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commprotType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commprotType = 27;</code>
       * @return Whether the commprotType field is set.
       */
      @java.lang.Override
      public boolean hasCommprotType() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes commprotType = 27;</code>
       * @return The commprotType.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommprotType() {
        return commprotType_;
      }
      /**
       * <code>optional bytes commprotType = 27;</code>
       * @param value The commprotType to set.
       * @return This builder for chaining.
       */
      public Builder setCommprotType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commprotType_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commprotType = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommprotType() {
        bitField0_ = (bitField0_ & ~0x04000000);
        commprotType_ = getDefaultInstance().getCommprotType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commprotName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commprotName = 28;</code>
       * @return Whether the commprotName field is set.
       */
      @java.lang.Override
      public boolean hasCommprotName() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes commprotName = 28;</code>
       * @return The commprotName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommprotName() {
        return commprotName_;
      }
      /**
       * <code>optional bytes commprotName = 28;</code>
       * @param value The commprotName to set.
       * @return This builder for chaining.
       */
      public Builder setCommprotName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commprotName_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commprotName = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommprotName() {
        bitField0_ = (bitField0_ & ~0x08000000);
        commprotName_ = getDefaultInstance().getCommprotName();
        onChanged();
        return this;
      }

      private int commmulRouFlag_ ;
      /**
       * <code>optional uint32 commmulRouFlag = 29;</code>
       * @return Whether the commmulRouFlag field is set.
       */
      @java.lang.Override
      public boolean hasCommmulRouFlag() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional uint32 commmulRouFlag = 29;</code>
       * @return The commmulRouFlag.
       */
      @java.lang.Override
      public int getCommmulRouFlag() {
        return commmulRouFlag_;
      }
      /**
       * <code>optional uint32 commmulRouFlag = 29;</code>
       * @param value The commmulRouFlag to set.
       * @return This builder for chaining.
       */
      public Builder setCommmulRouFlag(int value) {

        commmulRouFlag_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commmulRouFlag = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommmulRouFlag() {
        bitField0_ = (bitField0_ & ~0x10000000);
        commmulRouFlag_ = 0;
        onChanged();
        return this;
      }

      private int commintFlag_ ;
      /**
       * <code>optional uint32 commintFlag = 30;</code>
       * @return Whether the commintFlag field is set.
       */
      @java.lang.Override
      public boolean hasCommintFlag() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <code>optional uint32 commintFlag = 30;</code>
       * @return The commintFlag.
       */
      @java.lang.Override
      public int getCommintFlag() {
        return commintFlag_;
      }
      /**
       * <code>optional uint32 commintFlag = 30;</code>
       * @param value The commintFlag to set.
       * @return This builder for chaining.
       */
      public Builder setCommintFlag(int value) {

        commintFlag_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commintFlag = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommintFlag() {
        bitField0_ = (bitField0_ & ~0x20000000);
        commintFlag_ = 0;
        onChanged();
        return this;
      }

      private int commstrDirec_ ;
      /**
       * <code>optional uint32 commstrDirec = 31;</code>
       * @return Whether the commstrDirec field is set.
       */
      @java.lang.Override
      public boolean hasCommstrDirec() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional uint32 commstrDirec = 31;</code>
       * @return The commstrDirec.
       */
      @java.lang.Override
      public int getCommstrDirec() {
        return commstrDirec_;
      }
      /**
       * <code>optional uint32 commstrDirec = 31;</code>
       * @param value The commstrDirec to set.
       * @return This builder for chaining.
       */
      public Builder setCommstrDirec(int value) {

        commstrDirec_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commstrDirec = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommstrDirec() {
        bitField0_ = (bitField0_ & ~0x40000000);
        commstrDirec_ = 0;
        onChanged();
        return this;
      }

      private int commpktNum_ ;
      /**
       * <code>optional uint32 commpktNum = 32;</code>
       * @return Whether the commpktNum field is set.
       */
      @java.lang.Override
      public boolean hasCommpktNum() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 commpktNum = 32;</code>
       * @return The commpktNum.
       */
      @java.lang.Override
      public int getCommpktNum() {
        return commpktNum_;
      }
      /**
       * <code>optional uint32 commpktNum = 32;</code>
       * @param value The commpktNum to set.
       * @return This builder for chaining.
       */
      public Builder setCommpktNum(int value) {

        commpktNum_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commpktNum = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommpktNum() {
        bitField0_ = (bitField0_ & ~0x80000000);
        commpktNum_ = 0;
        onChanged();
        return this;
      }

      private long commpayLen_ ;
      /**
       * <code>optional uint64 commpayLen = 33;</code>
       * @return Whether the commpayLen field is set.
       */
      @java.lang.Override
      public boolean hasCommpayLen() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint64 commpayLen = 33;</code>
       * @return The commpayLen.
       */
      @java.lang.Override
      public long getCommpayLen() {
        return commpayLen_;
      }
      /**
       * <code>optional uint64 commpayLen = 33;</code>
       * @param value The commpayLen to set.
       * @return This builder for chaining.
       */
      public Builder setCommpayLen(long value) {

        commpayLen_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 commpayLen = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommpayLen() {
        bitField1_ = (bitField1_ & ~0x00000001);
        commpayLen_ = 0L;
        onChanged();
        return this;
      }

      private long commstreamId_ ;
      /**
       * <code>optional uint64 commstreamId = 34;</code>
       * @return Whether the commstreamId field is set.
       */
      @java.lang.Override
      public boolean hasCommstreamId() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint64 commstreamId = 34;</code>
       * @return The commstreamId.
       */
      @java.lang.Override
      public long getCommstreamId() {
        return commstreamId_;
      }
      /**
       * <code>optional uint64 commstreamId = 34;</code>
       * @param value The commstreamId to set.
       * @return This builder for chaining.
       */
      public Builder setCommstreamId(long value) {

        commstreamId_ = value;
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 commstreamId = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommstreamId() {
        bitField1_ = (bitField1_ & ~0x00000002);
        commstreamId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commetags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commetags = 35;</code>
       * @return Whether the commetags field is set.
       */
      @java.lang.Override
      public boolean hasCommetags() {
        return ((bitField1_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes commetags = 35;</code>
       * @return The commetags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommetags() {
        return commetags_;
      }
      /**
       * <code>optional bytes commetags = 35;</code>
       * @param value The commetags to set.
       * @return This builder for chaining.
       */
      public Builder setCommetags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commetags_ = value;
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commetags = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommetags() {
        bitField1_ = (bitField1_ & ~0x00000004);
        commetags_ = getDefaultInstance().getCommetags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commttags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commttags = 36;</code>
       * @return Whether the commttags field is set.
       */
      @java.lang.Override
      public boolean hasCommttags() {
        return ((bitField1_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes commttags = 36;</code>
       * @return The commttags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommttags() {
        return commttags_;
      }
      /**
       * <code>optional bytes commttags = 36;</code>
       * @param value The commttags to set.
       * @return This builder for chaining.
       */
      public Builder setCommttags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commttags_ = value;
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commttags = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommttags() {
        bitField1_ = (bitField1_ & ~0x00000008);
        commttags_ = getDefaultInstance().getCommttags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commatags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commatags = 37;</code>
       * @return Whether the commatags field is set.
       */
      @java.lang.Override
      public boolean hasCommatags() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes commatags = 37;</code>
       * @return The commatags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommatags() {
        return commatags_;
      }
      /**
       * <code>optional bytes commatags = 37;</code>
       * @param value The commatags to set.
       * @return This builder for chaining.
       */
      public Builder setCommatags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commatags_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commatags = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommatags() {
        bitField1_ = (bitField1_ & ~0x00000010);
        commatags_ = getDefaultInstance().getCommatags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commutags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commutags = 38;</code>
       * @return Whether the commutags field is set.
       */
      @java.lang.Override
      public boolean hasCommutags() {
        return ((bitField1_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes commutags = 38;</code>
       * @return The commutags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommutags() {
        return commutags_;
      }
      /**
       * <code>optional bytes commutags = 38;</code>
       * @param value The commutags to set.
       * @return This builder for chaining.
       */
      public Builder setCommutags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commutags_ = value;
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commutags = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommutags() {
        bitField1_ = (bitField1_ & ~0x00000020);
        commutags_ = getDefaultInstance().getCommutags();
        onChanged();
        return this;
      }

      private int commlable1_ ;
      /**
       * <code>optional uint32 commlable1 = 39;</code>
       * @return Whether the commlable1 field is set.
       */
      @java.lang.Override
      public boolean hasCommlable1() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 commlable1 = 39;</code>
       * @return The commlable1.
       */
      @java.lang.Override
      public int getCommlable1() {
        return commlable1_;
      }
      /**
       * <code>optional uint32 commlable1 = 39;</code>
       * @param value The commlable1 to set.
       * @return This builder for chaining.
       */
      public Builder setCommlable1(int value) {

        commlable1_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commlable1 = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommlable1() {
        bitField1_ = (bitField1_ & ~0x00000040);
        commlable1_ = 0;
        onChanged();
        return this;
      }

      private int commlable2_ ;
      /**
       * <code>optional uint32 commlable2 = 40;</code>
       * @return Whether the commlable2 field is set.
       */
      @java.lang.Override
      public boolean hasCommlable2() {
        return ((bitField1_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 commlable2 = 40;</code>
       * @return The commlable2.
       */
      @java.lang.Override
      public int getCommlable2() {
        return commlable2_;
      }
      /**
       * <code>optional uint32 commlable2 = 40;</code>
       * @param value The commlable2 to set.
       * @return This builder for chaining.
       */
      public Builder setCommlable2(int value) {

        commlable2_ = value;
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commlable2 = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommlable2() {
        bitField1_ = (bitField1_ & ~0x00000080);
        commlable2_ = 0;
        onChanged();
        return this;
      }

      private int commlable3_ ;
      /**
       * <code>optional uint32 commlable3 = 41;</code>
       * @return Whether the commlable3 field is set.
       */
      @java.lang.Override
      public boolean hasCommlable3() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 commlable3 = 41;</code>
       * @return The commlable3.
       */
      @java.lang.Override
      public int getCommlable3() {
        return commlable3_;
      }
      /**
       * <code>optional uint32 commlable3 = 41;</code>
       * @param value The commlable3 to set.
       * @return This builder for chaining.
       */
      public Builder setCommlable3(int value) {

        commlable3_ = value;
        bitField1_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commlable3 = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommlable3() {
        bitField1_ = (bitField1_ & ~0x00000100);
        commlable3_ = 0;
        onChanged();
        return this;
      }

      private int commlable4_ ;
      /**
       * <code>optional uint32 commlable4 = 42;</code>
       * @return Whether the commlable4 field is set.
       */
      @java.lang.Override
      public boolean hasCommlable4() {
        return ((bitField1_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint32 commlable4 = 42;</code>
       * @return The commlable4.
       */
      @java.lang.Override
      public int getCommlable4() {
        return commlable4_;
      }
      /**
       * <code>optional uint32 commlable4 = 42;</code>
       * @param value The commlable4 to set.
       * @return This builder for chaining.
       */
      public Builder setCommlable4(int value) {

        commlable4_ = value;
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commlable4 = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommlable4() {
        bitField1_ = (bitField1_ & ~0x00000200);
        commlable4_ = 0;
        onChanged();
        return this;
      }

      private int commvlanID1_ ;
      /**
       * <code>optional uint32 commvlanID1 = 43;</code>
       * @return Whether the commvlanID1 field is set.
       */
      @java.lang.Override
      public boolean hasCommvlanID1() {
        return ((bitField1_ & 0x00000400) != 0);
      }
      /**
       * <code>optional uint32 commvlanID1 = 43;</code>
       * @return The commvlanID1.
       */
      @java.lang.Override
      public int getCommvlanID1() {
        return commvlanID1_;
      }
      /**
       * <code>optional uint32 commvlanID1 = 43;</code>
       * @param value The commvlanID1 to set.
       * @return This builder for chaining.
       */
      public Builder setCommvlanID1(int value) {

        commvlanID1_ = value;
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commvlanID1 = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommvlanID1() {
        bitField1_ = (bitField1_ & ~0x00000400);
        commvlanID1_ = 0;
        onChanged();
        return this;
      }

      private int commvlanID2_ ;
      /**
       * <code>optional uint32 commvlanID2 = 44;</code>
       * @return Whether the commvlanID2 field is set.
       */
      @java.lang.Override
      public boolean hasCommvlanID2() {
        return ((bitField1_ & 0x00000800) != 0);
      }
      /**
       * <code>optional uint32 commvlanID2 = 44;</code>
       * @return The commvlanID2.
       */
      @java.lang.Override
      public int getCommvlanID2() {
        return commvlanID2_;
      }
      /**
       * <code>optional uint32 commvlanID2 = 44;</code>
       * @param value The commvlanID2 to set.
       * @return This builder for chaining.
       */
      public Builder setCommvlanID2(int value) {

        commvlanID2_ = value;
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commvlanID2 = 44;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommvlanID2() {
        bitField1_ = (bitField1_ & ~0x00000800);
        commvlanID2_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commsrcMac_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commsrcMac = 45;</code>
       * @return Whether the commsrcMac field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcMac() {
        return ((bitField1_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes commsrcMac = 45;</code>
       * @return The commsrcMac.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommsrcMac() {
        return commsrcMac_;
      }
      /**
       * <code>optional bytes commsrcMac = 45;</code>
       * @param value The commsrcMac to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcMac(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commsrcMac_ = value;
        bitField1_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commsrcMac = 45;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcMac() {
        bitField1_ = (bitField1_ & ~0x00001000);
        commsrcMac_ = getDefaultInstance().getCommsrcMac();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commdstMac_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commdstMac = 46;</code>
       * @return Whether the commdstMac field is set.
       */
      @java.lang.Override
      public boolean hasCommdstMac() {
        return ((bitField1_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes commdstMac = 46;</code>
       * @return The commdstMac.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommdstMac() {
        return commdstMac_;
      }
      /**
       * <code>optional bytes commdstMac = 46;</code>
       * @param value The commdstMac to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstMac(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commdstMac_ = value;
        bitField1_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commdstMac = 46;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstMac() {
        bitField1_ = (bitField1_ & ~0x00002000);
        commdstMac_ = getDefaultInstance().getCommdstMac();
        onChanged();
        return this;
      }

      private int commtunnelID_ ;
      /**
       * <code>optional uint32 commtunnelID = 47;</code>
       * @return Whether the commtunnelID field is set.
       */
      @java.lang.Override
      public boolean hasCommtunnelID() {
        return ((bitField1_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 commtunnelID = 47;</code>
       * @return The commtunnelID.
       */
      @java.lang.Override
      public int getCommtunnelID() {
        return commtunnelID_;
      }
      /**
       * <code>optional uint32 commtunnelID = 47;</code>
       * @param value The commtunnelID to set.
       * @return This builder for chaining.
       */
      public Builder setCommtunnelID(int value) {

        commtunnelID_ = value;
        bitField1_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commtunnelID = 47;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommtunnelID() {
        bitField1_ = (bitField1_ & ~0x00004000);
        commtunnelID_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commsrcCountry_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commsrcCountry = 48;</code>
       * @return Whether the commsrcCountry field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcCountry() {
        return ((bitField1_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes commsrcCountry = 48;</code>
       * @return The commsrcCountry.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommsrcCountry() {
        return commsrcCountry_;
      }
      /**
       * <code>optional bytes commsrcCountry = 48;</code>
       * @param value The commsrcCountry to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcCountry(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commsrcCountry_ = value;
        bitField1_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commsrcCountry = 48;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcCountry() {
        bitField1_ = (bitField1_ & ~0x00008000);
        commsrcCountry_ = getDefaultInstance().getCommsrcCountry();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commsrcState_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commsrcState = 49;</code>
       * @return Whether the commsrcState field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcState() {
        return ((bitField1_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes commsrcState = 49;</code>
       * @return The commsrcState.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommsrcState() {
        return commsrcState_;
      }
      /**
       * <code>optional bytes commsrcState = 49;</code>
       * @param value The commsrcState to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcState(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commsrcState_ = value;
        bitField1_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commsrcState = 49;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcState() {
        bitField1_ = (bitField1_ & ~0x00010000);
        commsrcState_ = getDefaultInstance().getCommsrcState();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commsrcCity_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commsrcCity = 50;</code>
       * @return Whether the commsrcCity field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcCity() {
        return ((bitField1_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes commsrcCity = 50;</code>
       * @return The commsrcCity.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommsrcCity() {
        return commsrcCity_;
      }
      /**
       * <code>optional bytes commsrcCity = 50;</code>
       * @param value The commsrcCity to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcCity(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commsrcCity_ = value;
        bitField1_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commsrcCity = 50;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcCity() {
        bitField1_ = (bitField1_ & ~0x00020000);
        commsrcCity_ = getDefaultInstance().getCommsrcCity();
        onChanged();
        return this;
      }

      private float commsrcLongitude_ ;
      /**
       * <code>optional float commsrcLongitude = 51;</code>
       * @return Whether the commsrcLongitude field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcLongitude() {
        return ((bitField1_ & 0x00040000) != 0);
      }
      /**
       * <code>optional float commsrcLongitude = 51;</code>
       * @return The commsrcLongitude.
       */
      @java.lang.Override
      public float getCommsrcLongitude() {
        return commsrcLongitude_;
      }
      /**
       * <code>optional float commsrcLongitude = 51;</code>
       * @param value The commsrcLongitude to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcLongitude(float value) {

        commsrcLongitude_ = value;
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional float commsrcLongitude = 51;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcLongitude() {
        bitField1_ = (bitField1_ & ~0x00040000);
        commsrcLongitude_ = 0F;
        onChanged();
        return this;
      }

      private float commsrcLatitude_ ;
      /**
       * <code>optional float commsrcLatitude = 52;</code>
       * @return Whether the commsrcLatitude field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcLatitude() {
        return ((bitField1_ & 0x00080000) != 0);
      }
      /**
       * <code>optional float commsrcLatitude = 52;</code>
       * @return The commsrcLatitude.
       */
      @java.lang.Override
      public float getCommsrcLatitude() {
        return commsrcLatitude_;
      }
      /**
       * <code>optional float commsrcLatitude = 52;</code>
       * @param value The commsrcLatitude to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcLatitude(float value) {

        commsrcLatitude_ = value;
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional float commsrcLatitude = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcLatitude() {
        bitField1_ = (bitField1_ & ~0x00080000);
        commsrcLatitude_ = 0F;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commsrcISP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commsrcISP = 53;</code>
       * @return Whether the commsrcISP field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcISP() {
        return ((bitField1_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes commsrcISP = 53;</code>
       * @return The commsrcISP.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommsrcISP() {
        return commsrcISP_;
      }
      /**
       * <code>optional bytes commsrcISP = 53;</code>
       * @param value The commsrcISP to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcISP(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commsrcISP_ = value;
        bitField1_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commsrcISP = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcISP() {
        bitField1_ = (bitField1_ & ~0x00100000);
        commsrcISP_ = getDefaultInstance().getCommsrcISP();
        onChanged();
        return this;
      }

      private int commsrcASN_ ;
      /**
       * <code>optional uint32 commsrcASN = 54;</code>
       * @return Whether the commsrcASN field is set.
       */
      @java.lang.Override
      public boolean hasCommsrcASN() {
        return ((bitField1_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 commsrcASN = 54;</code>
       * @return The commsrcASN.
       */
      @java.lang.Override
      public int getCommsrcASN() {
        return commsrcASN_;
      }
      /**
       * <code>optional uint32 commsrcASN = 54;</code>
       * @param value The commsrcASN to set.
       * @return This builder for chaining.
       */
      public Builder setCommsrcASN(int value) {

        commsrcASN_ = value;
        bitField1_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commsrcASN = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommsrcASN() {
        bitField1_ = (bitField1_ & ~0x00200000);
        commsrcASN_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commdstCountry_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commdstCountry = 55;</code>
       * @return Whether the commdstCountry field is set.
       */
      @java.lang.Override
      public boolean hasCommdstCountry() {
        return ((bitField1_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes commdstCountry = 55;</code>
       * @return The commdstCountry.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommdstCountry() {
        return commdstCountry_;
      }
      /**
       * <code>optional bytes commdstCountry = 55;</code>
       * @param value The commdstCountry to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstCountry(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commdstCountry_ = value;
        bitField1_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commdstCountry = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstCountry() {
        bitField1_ = (bitField1_ & ~0x00400000);
        commdstCountry_ = getDefaultInstance().getCommdstCountry();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commdstState_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commdstState = 56;</code>
       * @return Whether the commdstState field is set.
       */
      @java.lang.Override
      public boolean hasCommdstState() {
        return ((bitField1_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes commdstState = 56;</code>
       * @return The commdstState.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommdstState() {
        return commdstState_;
      }
      /**
       * <code>optional bytes commdstState = 56;</code>
       * @param value The commdstState to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstState(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commdstState_ = value;
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commdstState = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstState() {
        bitField1_ = (bitField1_ & ~0x00800000);
        commdstState_ = getDefaultInstance().getCommdstState();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commdstCity_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commdstCity = 57;</code>
       * @return Whether the commdstCity field is set.
       */
      @java.lang.Override
      public boolean hasCommdstCity() {
        return ((bitField1_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes commdstCity = 57;</code>
       * @return The commdstCity.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommdstCity() {
        return commdstCity_;
      }
      /**
       * <code>optional bytes commdstCity = 57;</code>
       * @param value The commdstCity to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstCity(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commdstCity_ = value;
        bitField1_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commdstCity = 57;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstCity() {
        bitField1_ = (bitField1_ & ~0x01000000);
        commdstCity_ = getDefaultInstance().getCommdstCity();
        onChanged();
        return this;
      }

      private float commdstLongitude_ ;
      /**
       * <code>optional float commdstLongitude = 58;</code>
       * @return Whether the commdstLongitude field is set.
       */
      @java.lang.Override
      public boolean hasCommdstLongitude() {
        return ((bitField1_ & 0x02000000) != 0);
      }
      /**
       * <code>optional float commdstLongitude = 58;</code>
       * @return The commdstLongitude.
       */
      @java.lang.Override
      public float getCommdstLongitude() {
        return commdstLongitude_;
      }
      /**
       * <code>optional float commdstLongitude = 58;</code>
       * @param value The commdstLongitude to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstLongitude(float value) {

        commdstLongitude_ = value;
        bitField1_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional float commdstLongitude = 58;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstLongitude() {
        bitField1_ = (bitField1_ & ~0x02000000);
        commdstLongitude_ = 0F;
        onChanged();
        return this;
      }

      private float commdstLatitude_ ;
      /**
       * <code>optional float commdstLatitude = 59;</code>
       * @return Whether the commdstLatitude field is set.
       */
      @java.lang.Override
      public boolean hasCommdstLatitude() {
        return ((bitField1_ & 0x04000000) != 0);
      }
      /**
       * <code>optional float commdstLatitude = 59;</code>
       * @return The commdstLatitude.
       */
      @java.lang.Override
      public float getCommdstLatitude() {
        return commdstLatitude_;
      }
      /**
       * <code>optional float commdstLatitude = 59;</code>
       * @param value The commdstLatitude to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstLatitude(float value) {

        commdstLatitude_ = value;
        bitField1_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional float commdstLatitude = 59;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstLatitude() {
        bitField1_ = (bitField1_ & ~0x04000000);
        commdstLatitude_ = 0F;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString commdstISP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes commdstISP = 60;</code>
       * @return Whether the commdstISP field is set.
       */
      @java.lang.Override
      public boolean hasCommdstISP() {
        return ((bitField1_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes commdstISP = 60;</code>
       * @return The commdstISP.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCommdstISP() {
        return commdstISP_;
      }
      /**
       * <code>optional bytes commdstISP = 60;</code>
       * @param value The commdstISP to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstISP(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        commdstISP_ = value;
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes commdstISP = 60;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstISP() {
        bitField1_ = (bitField1_ & ~0x08000000);
        commdstISP_ = getDefaultInstance().getCommdstISP();
        onChanged();
        return this;
      }

      private int commdstASN_ ;
      /**
       * <code>optional uint32 commdstASN = 61;</code>
       * @return Whether the commdstASN field is set.
       */
      @java.lang.Override
      public boolean hasCommdstASN() {
        return ((bitField1_ & 0x10000000) != 0);
      }
      /**
       * <code>optional uint32 commdstASN = 61;</code>
       * @return The commdstASN.
       */
      @java.lang.Override
      public int getCommdstASN() {
        return commdstASN_;
      }
      /**
       * <code>optional uint32 commdstASN = 61;</code>
       * @param value The commdstASN to set.
       * @return This builder for chaining.
       */
      public Builder setCommdstASN(int value) {

        commdstASN_ = value;
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 commdstASN = 61;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommdstASN() {
        bitField1_ = (bitField1_ & ~0x10000000);
        commdstASN_ = 0;
        onChanged();
        return this;
      }

      private int outAddrType_ ;
      /**
       * <code>optional uint32 outAddrType = 62;</code>
       * @return Whether the outAddrType field is set.
       */
      @java.lang.Override
      public boolean hasOutAddrType() {
        return ((bitField1_ & 0x20000000) != 0);
      }
      /**
       * <code>optional uint32 outAddrType = 62;</code>
       * @return The outAddrType.
       */
      @java.lang.Override
      public int getOutAddrType() {
        return outAddrType_;
      }
      /**
       * <code>optional uint32 outAddrType = 62;</code>
       * @param value The outAddrType to set.
       * @return This builder for chaining.
       */
      public Builder setOutAddrType(int value) {

        outAddrType_ = value;
        bitField1_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outAddrType = 62;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutAddrType() {
        bitField1_ = (bitField1_ & ~0x20000000);
        outAddrType_ = 0;
        onChanged();
        return this;
      }

      private int outSrcAddr_ ;
      /**
       * <code>optional uint32 outSrcAddr = 63;</code>
       * @return Whether the outSrcAddr field is set.
       */
      @java.lang.Override
      public boolean hasOutSrcAddr() {
        return ((bitField1_ & 0x40000000) != 0);
      }
      /**
       * <code>optional uint32 outSrcAddr = 63;</code>
       * @return The outSrcAddr.
       */
      @java.lang.Override
      public int getOutSrcAddr() {
        return outSrcAddr_;
      }
      /**
       * <code>optional uint32 outSrcAddr = 63;</code>
       * @param value The outSrcAddr to set.
       * @return This builder for chaining.
       */
      public Builder setOutSrcAddr(int value) {

        outSrcAddr_ = value;
        bitField1_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outSrcAddr = 63;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutSrcAddr() {
        bitField1_ = (bitField1_ & ~0x40000000);
        outSrcAddr_ = 0;
        onChanged();
        return this;
      }

      private int outDstAddr_ ;
      /**
       * <code>optional uint32 outDstAddr = 64;</code>
       * @return Whether the outDstAddr field is set.
       */
      @java.lang.Override
      public boolean hasOutDstAddr() {
        return ((bitField1_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 outDstAddr = 64;</code>
       * @return The outDstAddr.
       */
      @java.lang.Override
      public int getOutDstAddr() {
        return outDstAddr_;
      }
      /**
       * <code>optional uint32 outDstAddr = 64;</code>
       * @param value The outDstAddr to set.
       * @return This builder for chaining.
       */
      public Builder setOutDstAddr(int value) {

        outDstAddr_ = value;
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outDstAddr = 64;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutDstAddr() {
        bitField1_ = (bitField1_ & ~0x80000000);
        outDstAddr_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString outerIpv6Src_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes outer_ipv6_src = 65;</code>
       * @return Whether the outerIpv6Src field is set.
       */
      @java.lang.Override
      public boolean hasOuterIpv6Src() {
        return ((bitField2_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes outer_ipv6_src = 65;</code>
       * @return The outerIpv6Src.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getOuterIpv6Src() {
        return outerIpv6Src_;
      }
      /**
       * <code>optional bytes outer_ipv6_src = 65;</code>
       * @param value The outerIpv6Src to set.
       * @return This builder for chaining.
       */
      public Builder setOuterIpv6Src(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        outerIpv6Src_ = value;
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes outer_ipv6_src = 65;</code>
       * @return This builder for chaining.
       */
      public Builder clearOuterIpv6Src() {
        bitField2_ = (bitField2_ & ~0x00000001);
        outerIpv6Src_ = getDefaultInstance().getOuterIpv6Src();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString outerIpv6Dst_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes outer_ipv6_dst = 66;</code>
       * @return Whether the outerIpv6Dst field is set.
       */
      @java.lang.Override
      public boolean hasOuterIpv6Dst() {
        return ((bitField2_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes outer_ipv6_dst = 66;</code>
       * @return The outerIpv6Dst.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getOuterIpv6Dst() {
        return outerIpv6Dst_;
      }
      /**
       * <code>optional bytes outer_ipv6_dst = 66;</code>
       * @param value The outerIpv6Dst to set.
       * @return This builder for chaining.
       */
      public Builder setOuterIpv6Dst(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        outerIpv6Dst_ = value;
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes outer_ipv6_dst = 66;</code>
       * @return This builder for chaining.
       */
      public Builder clearOuterIpv6Dst() {
        bitField2_ = (bitField2_ & ~0x00000002);
        outerIpv6Dst_ = getDefaultInstance().getOuterIpv6Dst();
        onChanged();
        return this;
      }

      private int outSrcPort_ ;
      /**
       * <code>optional uint32 outSrcPort = 67;</code>
       * @return Whether the outSrcPort field is set.
       */
      @java.lang.Override
      public boolean hasOutSrcPort() {
        return ((bitField2_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 outSrcPort = 67;</code>
       * @return The outSrcPort.
       */
      @java.lang.Override
      public int getOutSrcPort() {
        return outSrcPort_;
      }
      /**
       * <code>optional uint32 outSrcPort = 67;</code>
       * @param value The outSrcPort to set.
       * @return This builder for chaining.
       */
      public Builder setOutSrcPort(int value) {

        outSrcPort_ = value;
        bitField2_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outSrcPort = 67;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutSrcPort() {
        bitField2_ = (bitField2_ & ~0x00000004);
        outSrcPort_ = 0;
        onChanged();
        return this;
      }

      private int outDstPort_ ;
      /**
       * <code>optional uint32 outDstPort = 68;</code>
       * @return Whether the outDstPort field is set.
       */
      @java.lang.Override
      public boolean hasOutDstPort() {
        return ((bitField2_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 outDstPort = 68;</code>
       * @return The outDstPort.
       */
      @java.lang.Override
      public int getOutDstPort() {
        return outDstPort_;
      }
      /**
       * <code>optional uint32 outDstPort = 68;</code>
       * @param value The outDstPort to set.
       * @return This builder for chaining.
       */
      public Builder setOutDstPort(int value) {

        outDstPort_ = value;
        bitField2_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outDstPort = 68;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutDstPort() {
        bitField2_ = (bitField2_ & ~0x00000008);
        outDstPort_ = 0;
        onChanged();
        return this;
      }

      private int outTransProto_ ;
      /**
       * <code>optional uint32 outTransProto = 69;</code>
       * @return Whether the outTransProto field is set.
       */
      @java.lang.Override
      public boolean hasOutTransProto() {
        return ((bitField2_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 outTransProto = 69;</code>
       * @return The outTransProto.
       */
      @java.lang.Override
      public int getOutTransProto() {
        return outTransProto_;
      }
      /**
       * <code>optional uint32 outTransProto = 69;</code>
       * @param value The outTransProto to set.
       * @return This builder for chaining.
       */
      public Builder setOutTransProto(int value) {

        outTransProto_ = value;
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outTransProto = 69;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutTransProto() {
        bitField2_ = (bitField2_ & ~0x00000010);
        outTransProto_ = 0;
        onChanged();
        return this;
      }

      private long captureTime_ ;
      /**
       * <code>optional uint64 captureTime = 70;</code>
       * @return Whether the captureTime field is set.
       */
      @java.lang.Override
      public boolean hasCaptureTime() {
        return ((bitField2_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint64 captureTime = 70;</code>
       * @return The captureTime.
       */
      @java.lang.Override
      public long getCaptureTime() {
        return captureTime_;
      }
      /**
       * <code>optional uint64 captureTime = 70;</code>
       * @param value The captureTime to set.
       * @return This builder for chaining.
       */
      public Builder setCaptureTime(long value) {

        captureTime_ = value;
        bitField2_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 captureTime = 70;</code>
       * @return This builder for chaining.
       */
      public Builder clearCaptureTime() {
        bitField2_ = (bitField2_ & ~0x00000020);
        captureTime_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:CommonInfo)
    }

    // @@protoc_insertion_point(class_scope:CommonInfo)
    private static final CommonInfoOuterClass.CommonInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new CommonInfoOuterClass.CommonInfo();
    }

    public static CommonInfoOuterClass.CommonInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CommonInfo>
        PARSER = new com.google.protobuf.AbstractParser<CommonInfo>() {
      @java.lang.Override
      public CommonInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CommonInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CommonInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public CommonInfoOuterClass.CommonInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CommonInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_CommonInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020CommonInfo.proto\"\243\013\n\nCommonInfo\022\021\n\tsrc" +
      "MacOui\030\001 \001(\014\022\021\n\tdstMacOui\030\002 \001(\014\022\021\n\tlineN" +
      "ame1\030\003 \001(\014\022\021\n\tlineName2\030\004 \001(\014\022\017\n\007begTime" +
      "\030\005 \001(\004\022\020\n\010tagsRule\030\006 \001(\014\022\017\n\007endTime\030\007 \001(" +
      "\004\022\016\n\006comDur\030\010 \001(\r\022\016\n\006meanID\030\t \001(\014\022\016\n\006sit" +
      "eID\030\n \001(\014\022\016\n\006unitID\030\013 \001(\014\022\016\n\006taskID\030\014 \001(" +
      "\014\022\014\n\004guid\030\r \001(\004\022\020\n\010stortime\030\016 \001(\004\022\020\n\010mds" +
      "ecdeg\030\017 \001(\014\022\022\n\nfilesecdeg\030\020 \001(\014\022\021\n\tsecde" +
      "gpro\030\021 \001(\014\022\021\n\tcommipVer\030\022 \001(\r\022\023\n\013commsrc" +
      "Addr\030\023 \001(\004\022\023\n\013commdstAddr\030\024 \001(\004\022\023\n\013comms" +
      "rcPort\030\025 \001(\r\022\023\n\013commdstPort\030\026 \001(\r\022\023\n\013com" +
      "mprotNum\030\027 \001(\r\022\025\n\rcommsrcAddrV6\030\030 \001(\014\022\025\n" +
      "\rcommdstAddrV6\030\031 \001(\014\022\024\n\014commprotInfo\030\032 \001" +
      "(\014\022\024\n\014commprotType\030\033 \001(\014\022\024\n\014commprotName" +
      "\030\034 \001(\014\022\026\n\016commmulRouFlag\030\035 \001(\r\022\023\n\013commin" +
      "tFlag\030\036 \001(\r\022\024\n\014commstrDirec\030\037 \001(\r\022\022\n\ncom" +
      "mpktNum\030  \001(\r\022\022\n\ncommpayLen\030! \001(\004\022\024\n\014com" +
      "mstreamId\030\" \001(\004\022\021\n\tcommetags\030# \001(\014\022\021\n\tco" +
      "mmttags\030$ \001(\014\022\021\n\tcommatags\030% \001(\014\022\021\n\tcomm" +
      "utags\030& \001(\014\022\022\n\ncommlable1\030\' \001(\r\022\022\n\ncomml" +
      "able2\030( \001(\r\022\022\n\ncommlable3\030) \001(\r\022\022\n\ncomml" +
      "able4\030* \001(\r\022\023\n\013commvlanID1\030+ \001(\r\022\023\n\013comm" +
      "vlanID2\030, \001(\r\022\022\n\ncommsrcMac\030- \001(\014\022\022\n\ncom" +
      "mdstMac\030. \001(\014\022\024\n\014commtunnelID\030/ \001(\r\022\026\n\016c" +
      "ommsrcCountry\0300 \001(\014\022\024\n\014commsrcState\0301 \001(" +
      "\014\022\023\n\013commsrcCity\0302 \001(\014\022\030\n\020commsrcLongitu" +
      "de\0303 \001(\002\022\027\n\017commsrcLatitude\0304 \001(\002\022\022\n\ncom" +
      "msrcISP\0305 \001(\014\022\022\n\ncommsrcASN\0306 \001(\r\022\026\n\016com" +
      "mdstCountry\0307 \001(\014\022\024\n\014commdstState\0308 \001(\014\022" +
      "\023\n\013commdstCity\0309 \001(\014\022\030\n\020commdstLongitude" +
      "\030: \001(\002\022\027\n\017commdstLatitude\030; \001(\002\022\022\n\ncommd" +
      "stISP\030< \001(\014\022\022\n\ncommdstASN\030= \001(\r\022\023\n\013outAd" +
      "drType\030> \001(\r\022\022\n\noutSrcAddr\030? \001(\r\022\022\n\noutD" +
      "stAddr\030@ \001(\r\022\026\n\016outer_ipv6_src\030A \001(\014\022\026\n\016" +
      "outer_ipv6_dst\030B \001(\014\022\022\n\noutSrcPort\030C \001(\r" +
      "\022\022\n\noutDstPort\030D \001(\r\022\025\n\routTransProto\030E " +
      "\001(\r\022\023\n\013captureTime\030F \001(\004"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_CommonInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_CommonInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_CommonInfo_descriptor,
        new java.lang.String[] { "SrcMacOui", "DstMacOui", "LineName1", "LineName2", "BegTime", "TagsRule", "EndTime", "ComDur", "MeanID", "SiteID", "UnitID", "TaskID", "Guid", "Stortime", "Mdsecdeg", "Filesecdeg", "Secdegpro", "CommipVer", "CommsrcAddr", "CommdstAddr", "CommsrcPort", "CommdstPort", "CommprotNum", "CommsrcAddrV6", "CommdstAddrV6", "CommprotInfo", "CommprotType", "CommprotName", "CommmulRouFlag", "CommintFlag", "CommstrDirec", "CommpktNum", "CommpayLen", "CommstreamId", "Commetags", "Commttags", "Commatags", "Commutags", "Commlable1", "Commlable2", "Commlable3", "Commlable4", "CommvlanID1", "CommvlanID2", "CommsrcMac", "CommdstMac", "CommtunnelID", "CommsrcCountry", "CommsrcState", "CommsrcCity", "CommsrcLongitude", "CommsrcLatitude", "CommsrcISP", "CommsrcASN", "CommdstCountry", "CommdstState", "CommdstCity", "CommdstLongitude", "CommdstLatitude", "CommdstISP", "CommdstASN", "OutAddrType", "OutSrcAddr", "OutDstAddr", "OuterIpv6Src", "OuterIpv6Dst", "OutSrcPort", "OutDstPort", "OutTransProto", "CaptureTime", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
