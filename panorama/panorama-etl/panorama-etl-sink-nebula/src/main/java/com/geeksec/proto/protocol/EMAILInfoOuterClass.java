package com.geeksec.proto.protocol;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: EMAILInfo.proto
// Protobuf Java Version: 4.29.4

public final class EMAILInfoOuterClass {
  private EMAILInfoOuterClass() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      EMAILInfoOuterClass.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface EMAILInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:EMAILInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated bytes attType = 1;</code>
     * @return A list containing the attType.
     */
    java.util.List<com.google.protobuf.ByteString> getAttTypeList();
    /**
     * <code>repeated bytes attType = 1;</code>
     * @return The count of attType.
     */
    int getAttTypeCount();
    /**
     * <code>repeated bytes attType = 1;</code>
     * @param index The index of the element to return.
     * @return The attType at the given index.
     */
    com.google.protobuf.ByteString getAttType(int index);

    /**
     * <code>optional uint32 attTypeCnt = 2;</code>
     * @return Whether the attTypeCnt field is set.
     */
    boolean hasAttTypeCnt();
    /**
     * <code>optional uint32 attTypeCnt = 2;</code>
     * @return The attTypeCnt.
     */
    int getAttTypeCnt();

    /**
     * <code>repeated bytes attFileName = 3;</code>
     * @return A list containing the attFileName.
     */
    java.util.List<com.google.protobuf.ByteString> getAttFileNameList();
    /**
     * <code>repeated bytes attFileName = 3;</code>
     * @return The count of attFileName.
     */
    int getAttFileNameCount();
    /**
     * <code>repeated bytes attFileName = 3;</code>
     * @param index The index of the element to return.
     * @return The attFileName at the given index.
     */
    com.google.protobuf.ByteString getAttFileName(int index);

    /**
     * <code>optional uint32 attFileNameCnt = 4;</code>
     * @return Whether the attFileNameCnt field is set.
     */
    boolean hasAttFileNameCnt();
    /**
     * <code>optional uint32 attFileNameCnt = 4;</code>
     * @return The attFileNameCnt.
     */
    int getAttFileNameCnt();

    /**
     * <code>repeated uint64 attConSize = 5;</code>
     * @return A list containing the attConSize.
     */
    java.util.List<java.lang.Long> getAttConSizeList();
    /**
     * <code>repeated uint64 attConSize = 5;</code>
     * @return The count of attConSize.
     */
    int getAttConSizeCount();
    /**
     * <code>repeated uint64 attConSize = 5;</code>
     * @param index The index of the element to return.
     * @return The attConSize at the given index.
     */
    long getAttConSize(int index);

    /**
     * <code>repeated bytes attMD5 = 6;</code>
     * @return A list containing the attMD5.
     */
    java.util.List<com.google.protobuf.ByteString> getAttMD5List();
    /**
     * <code>repeated bytes attMD5 = 6;</code>
     * @return The count of attMD5.
     */
    int getAttMD5Count();
    /**
     * <code>repeated bytes attMD5 = 6;</code>
     * @param index The index of the element to return.
     * @return The attMD5 at the given index.
     */
    com.google.protobuf.ByteString getAttMD5(int index);

    /**
     * <code>optional uint32 attMD5Cnt = 7;</code>
     * @return Whether the attMD5Cnt field is set.
     */
    boolean hasAttMD5Cnt();
    /**
     * <code>optional uint32 attMD5Cnt = 7;</code>
     * @return The attMD5Cnt.
     */
    int getAttMD5Cnt();

    /**
     * <code>optional uint32 authRelt = 8;</code>
     * @return Whether the authRelt field is set.
     */
    boolean hasAuthRelt();
    /**
     * <code>optional uint32 authRelt = 8;</code>
     * @return The authRelt.
     */
    int getAuthRelt();

    /**
     * <code>repeated bytes BCC = 9;</code>
     * @return A list containing the bCC.
     */
    java.util.List<com.google.protobuf.ByteString> getBCCList();
    /**
     * <code>repeated bytes BCC = 9;</code>
     * @return The count of bCC.
     */
    int getBCCCount();
    /**
     * <code>repeated bytes BCC = 9;</code>
     * @param index The index of the element to return.
     * @return The bCC at the given index.
     */
    com.google.protobuf.ByteString getBCC(int index);

    /**
     * <code>optional bytes body = 10;</code>
     * @return Whether the body field is set.
     */
    boolean hasBody();
    /**
     * <code>optional bytes body = 10;</code>
     * @return The body.
     */
    com.google.protobuf.ByteString getBody();

    /**
     * <code>optional bytes bodyTexCha = 11;</code>
     * @return Whether the bodyTexCha field is set.
     */
    boolean hasBodyTexCha();
    /**
     * <code>optional bytes bodyTexCha = 11;</code>
     * @return The bodyTexCha.
     */
    com.google.protobuf.ByteString getBodyTexCha();

    /**
     * <code>optional bytes bodyTraEnc = 12;</code>
     * @return Whether the bodyTraEnc field is set.
     */
    boolean hasBodyTraEnc();
    /**
     * <code>optional bytes bodyTraEnc = 12;</code>
     * @return The bodyTraEnc.
     */
    com.google.protobuf.ByteString getBodyTraEnc();

    /**
     * <code>optional bytes bodyMD5 = 13;</code>
     * @return Whether the bodyMD5 field is set.
     */
    boolean hasBodyMD5();
    /**
     * <code>optional bytes bodyMD5 = 13;</code>
     * @return The bodyMD5.
     */
    com.google.protobuf.ByteString getBodyMD5();

    /**
     * <code>repeated bytes bodyType = 14;</code>
     * @return A list containing the bodyType.
     */
    java.util.List<com.google.protobuf.ByteString> getBodyTypeList();
    /**
     * <code>repeated bytes bodyType = 14;</code>
     * @return The count of bodyType.
     */
    int getBodyTypeCount();
    /**
     * <code>repeated bytes bodyType = 14;</code>
     * @param index The index of the element to return.
     * @return The bodyType at the given index.
     */
    com.google.protobuf.ByteString getBodyType(int index);

    /**
     * <code>optional uint32 bodyTypeCnt = 15;</code>
     * @return Whether the bodyTypeCnt field is set.
     */
    boolean hasBodyTypeCnt();
    /**
     * <code>optional uint32 bodyTypeCnt = 15;</code>
     * @return The bodyTypeCnt.
     */
    int getBodyTypeCnt();

    /**
     * <code>optional bytes bodyURL = 16;</code>
     * @return Whether the bodyURL field is set.
     */
    boolean hasBodyURL();
    /**
     * <code>optional bytes bodyURL = 16;</code>
     * @return The bodyURL.
     */
    com.google.protobuf.ByteString getBodyURL();

    /**
     * <code>optional uint32 bodyURLCnt = 17;</code>
     * @return Whether the bodyURLCnt field is set.
     */
    boolean hasBodyURLCnt();
    /**
     * <code>optional uint32 bodyURLCnt = 17;</code>
     * @return The bodyURLCnt.
     */
    int getBodyURLCnt();

    /**
     * <code>optional uint32 bodyLen = 18;</code>
     * @return Whether the bodyLen field is set.
     */
    boolean hasBodyLen();
    /**
     * <code>optional uint32 bodyLen = 18;</code>
     * @return The bodyLen.
     */
    int getBodyLen();

    /**
     * <code>repeated bytes ByAsn = 19;</code>
     * @return A list containing the byAsn.
     */
    java.util.List<com.google.protobuf.ByteString> getByAsnList();
    /**
     * <code>repeated bytes ByAsn = 19;</code>
     * @return The count of byAsn.
     */
    int getByAsnCount();
    /**
     * <code>repeated bytes ByAsn = 19;</code>
     * @param index The index of the element to return.
     * @return The byAsn at the given index.
     */
    com.google.protobuf.ByteString getByAsn(int index);

    /**
     * <code>repeated bytes ByCountry = 20;</code>
     * @return A list containing the byCountry.
     */
    java.util.List<com.google.protobuf.ByteString> getByCountryList();
    /**
     * <code>repeated bytes ByCountry = 20;</code>
     * @return The count of byCountry.
     */
    int getByCountryCount();
    /**
     * <code>repeated bytes ByCountry = 20;</code>
     * @param index The index of the element to return.
     * @return The byCountry at the given index.
     */
    com.google.protobuf.ByteString getByCountry(int index);

    /**
     * <code>repeated bytes ByDom = 21;</code>
     * @return A list containing the byDom.
     */
    java.util.List<com.google.protobuf.ByteString> getByDomList();
    /**
     * <code>repeated bytes ByDom = 21;</code>
     * @return The count of byDom.
     */
    int getByDomCount();
    /**
     * <code>repeated bytes ByDom = 21;</code>
     * @param index The index of the element to return.
     * @return The byDom at the given index.
     */
    com.google.protobuf.ByteString getByDom(int index);

    /**
     * <code>optional uint32 ByDomCnt = 22;</code>
     * @return Whether the byDomCnt field is set.
     */
    boolean hasByDomCnt();
    /**
     * <code>optional uint32 ByDomCnt = 22;</code>
     * @return The byDomCnt.
     */
    int getByDomCnt();

    /**
     * <code>optional bytes ByIP = 23;</code>
     * @return Whether the byIP field is set.
     */
    boolean hasByIP();
    /**
     * <code>optional bytes ByIP = 23;</code>
     * @return The byIP.
     */
    com.google.protobuf.ByteString getByIP();

    /**
     * <code>optional uint32 ByIpCnt = 24;</code>
     * @return Whether the byIpCnt field is set.
     */
    boolean hasByIpCnt();
    /**
     * <code>optional uint32 ByIpCnt = 24;</code>
     * @return The byIpCnt.
     */
    int getByIpCnt();

    /**
     * <code>repeated bytes CC = 25;</code>
     * @return A list containing the cC.
     */
    java.util.List<com.google.protobuf.ByteString> getCCList();
    /**
     * <code>repeated bytes CC = 25;</code>
     * @return The count of cC.
     */
    int getCCCount();
    /**
     * <code>repeated bytes CC = 25;</code>
     * @param index The index of the element to return.
     * @return The cC at the given index.
     */
    com.google.protobuf.ByteString getCC(int index);

    /**
     * <code>repeated bytes CCAli = 26;</code>
     * @return A list containing the cCAli.
     */
    java.util.List<com.google.protobuf.ByteString> getCCAliList();
    /**
     * <code>repeated bytes CCAli = 26;</code>
     * @return The count of cCAli.
     */
    int getCCAliCount();
    /**
     * <code>repeated bytes CCAli = 26;</code>
     * @param index The index of the element to return.
     * @return The cCAli at the given index.
     */
    com.google.protobuf.ByteString getCCAli(int index);

    /**
     * <code>repeated bytes Command = 27;</code>
     * @return A list containing the command.
     */
    java.util.List<com.google.protobuf.ByteString> getCommandList();
    /**
     * <code>repeated bytes Command = 27;</code>
     * @return The count of command.
     */
    int getCommandCount();
    /**
     * <code>repeated bytes Command = 27;</code>
     * @param index The index of the element to return.
     * @return The command at the given index.
     */
    com.google.protobuf.ByteString getCommand(int index);

    /**
     * <code>optional uint32 count = 28;</code>
     * @return Whether the count field is set.
     */
    boolean hasCount();
    /**
     * <code>optional uint32 count = 28;</code>
     * @return The count.
     */
    int getCount();

    /**
     * <code>optional bytes content = 29;</code>
     * @return Whether the content field is set.
     */
    boolean hasContent();
    /**
     * <code>optional bytes content = 29;</code>
     * @return The content.
     */
    com.google.protobuf.ByteString getContent();

    /**
     * <code>repeated bytes conType = 30;</code>
     * @return A list containing the conType.
     */
    java.util.List<com.google.protobuf.ByteString> getConTypeList();
    /**
     * <code>repeated bytes conType = 30;</code>
     * @return The count of conType.
     */
    int getConTypeCount();
    /**
     * <code>repeated bytes conType = 30;</code>
     * @param index The index of the element to return.
     * @return The conType at the given index.
     */
    com.google.protobuf.ByteString getConType(int index);

    /**
     * <code>optional uint32 conTypeCnt = 31;</code>
     * @return Whether the conTypeCnt field is set.
     */
    boolean hasConTypeCnt();
    /**
     * <code>optional uint32 conTypeCnt = 31;</code>
     * @return The conTypeCnt.
     */
    int getConTypeCnt();

    /**
     * <code>optional uint32 date = 32;</code>
     * @return Whether the date field is set.
     */
    boolean hasDate();
    /**
     * <code>optional uint32 date = 32;</code>
     * @return The date.
     */
    int getDate();

    /**
     * <code>optional bytes deliveredTo = 33;</code>
     * @return Whether the deliveredTo field is set.
     */
    boolean hasDeliveredTo();
    /**
     * <code>optional bytes deliveredTo = 33;</code>
     * @return The deliveredTo.
     */
    com.google.protobuf.ByteString getDeliveredTo();

    /**
     * <code>repeated bytes FromAsn = 34;</code>
     * @return A list containing the fromAsn.
     */
    java.util.List<com.google.protobuf.ByteString> getFromAsnList();
    /**
     * <code>repeated bytes FromAsn = 34;</code>
     * @return The count of fromAsn.
     */
    int getFromAsnCount();
    /**
     * <code>repeated bytes FromAsn = 34;</code>
     * @param index The index of the element to return.
     * @return The fromAsn at the given index.
     */
    com.google.protobuf.ByteString getFromAsn(int index);

    /**
     * <code>repeated bytes FromCountry = 35;</code>
     * @return A list containing the fromCountry.
     */
    java.util.List<com.google.protobuf.ByteString> getFromCountryList();
    /**
     * <code>repeated bytes FromCountry = 35;</code>
     * @return The count of fromCountry.
     */
    int getFromCountryCount();
    /**
     * <code>repeated bytes FromCountry = 35;</code>
     * @param index The index of the element to return.
     * @return The fromCountry at the given index.
     */
    com.google.protobuf.ByteString getFromCountry(int index);

    /**
     * <code>repeated bytes FromDom = 36;</code>
     * @return A list containing the fromDom.
     */
    java.util.List<com.google.protobuf.ByteString> getFromDomList();
    /**
     * <code>repeated bytes FromDom = 36;</code>
     * @return The count of fromDom.
     */
    int getFromDomCount();
    /**
     * <code>repeated bytes FromDom = 36;</code>
     * @param index The index of the element to return.
     * @return The fromDom at the given index.
     */
    com.google.protobuf.ByteString getFromDom(int index);

    /**
     * <code>optional uint32 FromDomCnt = 37;</code>
     * @return Whether the fromDomCnt field is set.
     */
    boolean hasFromDomCnt();
    /**
     * <code>optional uint32 FromDomCnt = 37;</code>
     * @return The fromDomCnt.
     */
    int getFromDomCnt();

    /**
     * <code>repeated uint32 FromIp = 38;</code>
     * @return A list containing the fromIp.
     */
    java.util.List<java.lang.Integer> getFromIpList();
    /**
     * <code>repeated uint32 FromIp = 38;</code>
     * @return The count of fromIp.
     */
    int getFromIpCount();
    /**
     * <code>repeated uint32 FromIp = 38;</code>
     * @param index The index of the element to return.
     * @return The fromIp at the given index.
     */
    int getFromIp(int index);

    /**
     * <code>optional uint32 FromIpCnt = 39;</code>
     * @return Whether the fromIpCnt field is set.
     */
    boolean hasFromIpCnt();
    /**
     * <code>optional uint32 FromIpCnt = 39;</code>
     * @return The fromIpCnt.
     */
    int getFromIpCnt();

    /**
     * <code>repeated bytes headSet = 40;</code>
     * @return A list containing the headSet.
     */
    java.util.List<com.google.protobuf.ByteString> getHeadSetList();
    /**
     * <code>repeated bytes headSet = 40;</code>
     * @return The count of headSet.
     */
    int getHeadSetCount();
    /**
     * <code>repeated bytes headSet = 40;</code>
     * @param index The index of the element to return.
     * @return The headSet at the given index.
     */
    com.google.protobuf.ByteString getHeadSet(int index);

    /**
     * <code>optional uint32 headSetCnt = 41;</code>
     * @return Whether the headSetCnt field is set.
     */
    boolean hasHeadSetCnt();
    /**
     * <code>optional uint32 headSetCnt = 41;</code>
     * @return The headSetCnt.
     */
    int getHeadSetCnt();

    /**
     * <code>optional bytes host = 42;</code>
     * @return Whether the host field is set.
     */
    boolean hasHost();
    /**
     * <code>optional bytes host = 42;</code>
     * @return The host.
     */
    com.google.protobuf.ByteString getHost();

    /**
     * <code>optional bytes name = 43;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional bytes name = 43;</code>
     * @return The name.
     */
    com.google.protobuf.ByteString getName();

    /**
     * <code>optional bytes os = 44;</code>
     * @return Whether the os field is set.
     */
    boolean hasOs();
    /**
     * <code>optional bytes os = 44;</code>
     * @return The os.
     */
    com.google.protobuf.ByteString getOs();

    /**
     * <code>optional bytes osVer = 45;</code>
     * @return Whether the osVer field is set.
     */
    boolean hasOsVer();
    /**
     * <code>optional bytes osVer = 45;</code>
     * @return The osVer.
     */
    com.google.protobuf.ByteString getOsVer();

    /**
     * <code>optional bytes vendor = 46;</code>
     * @return Whether the vendor field is set.
     */
    boolean hasVendor();
    /**
     * <code>optional bytes vendor = 46;</code>
     * @return The vendor.
     */
    com.google.protobuf.ByteString getVendor();

    /**
     * <code>optional bytes ver = 47;</code>
     * @return Whether the ver field is set.
     */
    boolean hasVer();
    /**
     * <code>optional bytes ver = 47;</code>
     * @return The ver.
     */
    com.google.protobuf.ByteString getVer();

    /**
     * <code>optional bytes emaInd = 48;</code>
     * @return Whether the emaInd field is set.
     */
    boolean hasEmaInd();
    /**
     * <code>optional bytes emaInd = 48;</code>
     * @return The emaInd.
     */
    com.google.protobuf.ByteString getEmaInd();

    /**
     * <code>optional bytes login = 49;</code>
     * @return Whether the login field is set.
     */
    boolean hasLogin();
    /**
     * <code>optional bytes login = 49;</code>
     * @return The login.
     */
    com.google.protobuf.ByteString getLogin();

    /**
     * <code>optional bytes loginsrv = 50;</code>
     * @return Whether the loginsrv field is set.
     */
    boolean hasLoginsrv();
    /**
     * <code>optional bytes loginsrv = 50;</code>
     * @return The loginsrv.
     */
    com.google.protobuf.ByteString getLoginsrv();

    /**
     * <code>repeated bytes mailFrom = 51;</code>
     * @return A list containing the mailFrom.
     */
    java.util.List<com.google.protobuf.ByteString> getMailFromList();
    /**
     * <code>repeated bytes mailFrom = 51;</code>
     * @return The count of mailFrom.
     */
    int getMailFromCount();
    /**
     * <code>repeated bytes mailFrom = 51;</code>
     * @param index The index of the element to return.
     * @return The mailFrom at the given index.
     */
    com.google.protobuf.ByteString getMailFrom(int index);

    /**
     * <code>repeated bytes mailFromDom = 52;</code>
     * @return A list containing the mailFromDom.
     */
    java.util.List<com.google.protobuf.ByteString> getMailFromDomList();
    /**
     * <code>repeated bytes mailFromDom = 52;</code>
     * @return The count of mailFromDom.
     */
    int getMailFromDomCount();
    /**
     * <code>repeated bytes mailFromDom = 52;</code>
     * @param index The index of the element to return.
     * @return The mailFromDom at the given index.
     */
    com.google.protobuf.ByteString getMailFromDom(int index);

    /**
     * <code>optional bytes mailFromDomCnt = 53;</code>
     * @return Whether the mailFromDomCnt field is set.
     */
    boolean hasMailFromDomCnt();
    /**
     * <code>optional bytes mailFromDomCnt = 53;</code>
     * @return The mailFromDomCnt.
     */
    com.google.protobuf.ByteString getMailFromDomCnt();

    /**
     * <code>optional bytes mimeVer = 54;</code>
     * @return Whether the mimeVer field is set.
     */
    boolean hasMimeVer();
    /**
     * <code>optional bytes mimeVer = 54;</code>
     * @return The mimeVer.
     */
    com.google.protobuf.ByteString getMimeVer();

    /**
     * <code>optional uint32 mimeVerCnt = 55;</code>
     * @return Whether the mimeVerCnt field is set.
     */
    boolean hasMimeVerCnt();
    /**
     * <code>optional uint32 mimeVerCnt = 55;</code>
     * @return The mimeVerCnt.
     */
    int getMimeVerCnt();

    /**
     * <code>repeated bytes msgID = 56;</code>
     * @return A list containing the msgID.
     */
    java.util.List<com.google.protobuf.ByteString> getMsgIDList();
    /**
     * <code>repeated bytes msgID = 56;</code>
     * @return The count of msgID.
     */
    int getMsgIDCount();
    /**
     * <code>repeated bytes msgID = 56;</code>
     * @param index The index of the element to return.
     * @return The msgID at the given index.
     */
    com.google.protobuf.ByteString getMsgID(int index);

    /**
     * <code>optional uint32 msgIDCnt = 57;</code>
     * @return Whether the msgIDCnt field is set.
     */
    boolean hasMsgIDCnt();
    /**
     * <code>optional uint32 msgIDCnt = 57;</code>
     * @return The msgIDCnt.
     */
    int getMsgIDCnt();

    /**
     * <code>optional bytes emaProtType = 58;</code>
     * @return Whether the emaProtType field is set.
     */
    boolean hasEmaProtType();
    /**
     * <code>optional bytes emaProtType = 58;</code>
     * @return The emaProtType.
     */
    com.google.protobuf.ByteString getEmaProtType();

    /**
     * <code>optional bytes pwd = 59;</code>
     * @return Whether the pwd field is set.
     */
    boolean hasPwd();
    /**
     * <code>optional bytes pwd = 59;</code>
     * @return The pwd.
     */
    com.google.protobuf.ByteString getPwd();

    /**
     * <code>repeated bytes rcptTo = 60;</code>
     * @return A list containing the rcptTo.
     */
    java.util.List<com.google.protobuf.ByteString> getRcptToList();
    /**
     * <code>repeated bytes rcptTo = 60;</code>
     * @return The count of rcptTo.
     */
    int getRcptToCount();
    /**
     * <code>repeated bytes rcptTo = 60;</code>
     * @param index The index of the element to return.
     * @return The rcptTo at the given index.
     */
    com.google.protobuf.ByteString getRcptTo(int index);

    /**
     * <code>repeated bytes rcptToDom = 61;</code>
     * @return A list containing the rcptToDom.
     */
    java.util.List<com.google.protobuf.ByteString> getRcptToDomList();
    /**
     * <code>repeated bytes rcptToDom = 61;</code>
     * @return The count of rcptToDom.
     */
    int getRcptToDomCount();
    /**
     * <code>repeated bytes rcptToDom = 61;</code>
     * @param index The index of the element to return.
     * @return The rcptToDom at the given index.
     */
    com.google.protobuf.ByteString getRcptToDom(int index);

    /**
     * <code>optional uint32 rcptToDomCnt = 62;</code>
     * @return Whether the rcptToDomCnt field is set.
     */
    boolean hasRcptToDomCnt();
    /**
     * <code>optional uint32 rcptToDomCnt = 62;</code>
     * @return The rcptToDomCnt.
     */
    int getRcptToDomCnt();

    /**
     * <code>optional bytes repTo = 63;</code>
     * @return Whether the repTo field is set.
     */
    boolean hasRepTo();
    /**
     * <code>optional bytes repTo = 63;</code>
     * @return The repTo.
     */
    com.google.protobuf.ByteString getRepTo();

    /**
     * <code>repeated bytes received = 64;</code>
     * @return A list containing the received.
     */
    java.util.List<com.google.protobuf.ByteString> getReceivedList();
    /**
     * <code>repeated bytes received = 64;</code>
     * @return The count of received.
     */
    int getReceivedCount();
    /**
     * <code>repeated bytes received = 64;</code>
     * @param index The index of the element to return.
     * @return The received at the given index.
     */
    com.google.protobuf.ByteString getReceived(int index);

    /**
     * <code>repeated bytes rcvrEmail = 65;</code>
     * @return A list containing the rcvrEmail.
     */
    java.util.List<com.google.protobuf.ByteString> getRcvrEmailList();
    /**
     * <code>repeated bytes rcvrEmail = 65;</code>
     * @return The count of rcvrEmail.
     */
    int getRcvrEmailCount();
    /**
     * <code>repeated bytes rcvrEmail = 65;</code>
     * @param index The index of the element to return.
     * @return The rcvrEmail at the given index.
     */
    com.google.protobuf.ByteString getRcvrEmail(int index);

    /**
     * <code>repeated bytes rcvrAli = 66;</code>
     * @return A list containing the rcvrAli.
     */
    java.util.List<com.google.protobuf.ByteString> getRcvrAliList();
    /**
     * <code>repeated bytes rcvrAli = 66;</code>
     * @return The count of rcvrAli.
     */
    int getRcvrAliCount();
    /**
     * <code>repeated bytes rcvrAli = 66;</code>
     * @param index The index of the element to return.
     * @return The rcvrAli at the given index.
     */
    com.google.protobuf.ByteString getRcvrAli(int index);

    /**
     * <code>optional uint32 rcvrAliCnt = 67;</code>
     * @return Whether the rcvrAliCnt field is set.
     */
    boolean hasRcvrAliCnt();
    /**
     * <code>optional uint32 rcvrAliCnt = 67;</code>
     * @return The rcvrAliCnt.
     */
    int getRcvrAliCnt();

    /**
     * <code>optional uint32 rcvrEmailCnt = 68;</code>
     * @return Whether the rcvrEmailCnt field is set.
     */
    boolean hasRcvrEmailCnt();
    /**
     * <code>optional uint32 rcvrEmailCnt = 68;</code>
     * @return The rcvrEmailCnt.
     */
    int getRcvrEmailCnt();

    /**
     * <code>repeated bytes rcvrDom = 69;</code>
     * @return A list containing the rcvrDom.
     */
    java.util.List<com.google.protobuf.ByteString> getRcvrDomList();
    /**
     * <code>repeated bytes rcvrDom = 69;</code>
     * @return The count of rcvrDom.
     */
    int getRcvrDomCount();
    /**
     * <code>repeated bytes rcvrDom = 69;</code>
     * @param index The index of the element to return.
     * @return The rcvrDom at the given index.
     */
    com.google.protobuf.ByteString getRcvrDom(int index);

    /**
     * <code>optional bytes resentSrvAge = 70;</code>
     * @return Whether the resentSrvAge field is set.
     */
    boolean hasResentSrvAge();
    /**
     * <code>optional bytes resentSrvAge = 70;</code>
     * @return The resentSrvAge.
     */
    com.google.protobuf.ByteString getResentSrvAge();

    /**
     * <code>optional uint32 resentDate = 71;</code>
     * @return Whether the resentDate field is set.
     */
    boolean hasResentDate();
    /**
     * <code>optional uint32 resentDate = 71;</code>
     * @return The resentDate.
     */
    int getResentDate();

    /**
     * <code>optional bytes resentFrom = 72;</code>
     * @return Whether the resentFrom field is set.
     */
    boolean hasResentFrom();
    /**
     * <code>optional bytes resentFrom = 72;</code>
     * @return The resentFrom.
     */
    com.google.protobuf.ByteString getResentFrom();

    /**
     * <code>repeated bytes resentTo = 73;</code>
     * @return A list containing the resentTo.
     */
    java.util.List<com.google.protobuf.ByteString> getResentToList();
    /**
     * <code>repeated bytes resentTo = 73;</code>
     * @return The count of resentTo.
     */
    int getResentToCount();
    /**
     * <code>repeated bytes resentTo = 73;</code>
     * @param index The index of the element to return.
     * @return The resentTo at the given index.
     */
    com.google.protobuf.ByteString getResentTo(int index);

    /**
     * <code>optional bytes senderEmail = 74;</code>
     * @return Whether the senderEmail field is set.
     */
    boolean hasSenderEmail();
    /**
     * <code>optional bytes senderEmail = 74;</code>
     * @return The senderEmail.
     */
    com.google.protobuf.ByteString getSenderEmail();

    /**
     * <code>optional bytes senderAli = 75;</code>
     * @return Whether the senderAli field is set.
     */
    boolean hasSenderAli();
    /**
     * <code>optional bytes senderAli = 75;</code>
     * @return The senderAli.
     */
    com.google.protobuf.ByteString getSenderAli();

    /**
     * <code>optional bytes senderDom = 76;</code>
     * @return Whether the senderDom field is set.
     */
    boolean hasSenderDom();
    /**
     * <code>optional bytes senderDom = 76;</code>
     * @return The senderDom.
     */
    com.google.protobuf.ByteString getSenderDom();

    /**
     * <code>optional bytes SMTPSrv = 77;</code>
     * @return Whether the sMTPSrv field is set.
     */
    boolean hasSMTPSrv();
    /**
     * <code>optional bytes SMTPSrv = 77;</code>
     * @return The sMTPSrv.
     */
    com.google.protobuf.ByteString getSMTPSrv();

    /**
     * <code>optional bytes SMTPSrvAge = 78;</code>
     * @return Whether the sMTPSrvAge field is set.
     */
    boolean hasSMTPSrvAge();
    /**
     * <code>optional bytes SMTPSrvAge = 78;</code>
     * @return The sMTPSrvAge.
     */
    com.google.protobuf.ByteString getSMTPSrvAge();

    /**
     * <code>optional bytes receivedSPF = 79;</code>
     * @return Whether the receivedSPF field is set.
     */
    boolean hasReceivedSPF();
    /**
     * <code>optional bytes receivedSPF = 79;</code>
     * @return The receivedSPF.
     */
    com.google.protobuf.ByteString getReceivedSPF();

    /**
     * <code>optional uint32 startTLS = 80;</code>
     * @return Whether the startTLS field is set.
     */
    boolean hasStartTLS();
    /**
     * <code>optional uint32 startTLS = 80;</code>
     * @return The startTLS.
     */
    int getStartTLS();

    /**
     * <code>optional bytes subj = 81;</code>
     * @return Whether the subj field is set.
     */
    boolean hasSubj();
    /**
     * <code>optional bytes subj = 81;</code>
     * @return The subj.
     */
    com.google.protobuf.ByteString getSubj();

    /**
     * <code>optional uint32 subj_cnt = 82;</code>
     * @return Whether the subjCnt field is set.
     */
    boolean hasSubjCnt();
    /**
     * <code>optional uint32 subj_cnt = 82;</code>
     * @return The subjCnt.
     */
    int getSubjCnt();

    /**
     * <code>optional bytes usrAge = 83;</code>
     * @return Whether the usrAge field is set.
     */
    boolean hasUsrAge();
    /**
     * <code>optional bytes usrAge = 83;</code>
     * @return The usrAge.
     */
    com.google.protobuf.ByteString getUsrAge();

    /**
     * <code>optional bytes emailVersion = 84;</code>
     * @return Whether the emailVersion field is set.
     */
    boolean hasEmailVersion();
    /**
     * <code>optional bytes emailVersion = 84;</code>
     * @return The emailVersion.
     */
    com.google.protobuf.ByteString getEmailVersion();

    /**
     * <code>optional bytes rcvWit = 85;</code>
     * @return Whether the rcvWit field is set.
     */
    boolean hasRcvWit();
    /**
     * <code>optional bytes rcvWit = 85;</code>
     * @return The rcvWit.
     */
    com.google.protobuf.ByteString getRcvWit();

    /**
     * <code>optional bytes xMai = 86;</code>
     * @return Whether the xMai field is set.
     */
    boolean hasXMai();
    /**
     * <code>optional bytes xMai = 86;</code>
     * @return The xMai.
     */
    com.google.protobuf.ByteString getXMai();

    /**
     * <code>optional uint32 xMaiCnt = 87;</code>
     * @return Whether the xMaiCnt field is set.
     */
    boolean hasXMaiCnt();
    /**
     * <code>optional uint32 xMaiCnt = 87;</code>
     * @return The xMaiCnt.
     */
    int getXMaiCnt();

    /**
     * <code>optional uint32 xOriIP = 88;</code>
     * @return Whether the xOriIP field is set.
     */
    boolean hasXOriIP();
    /**
     * <code>optional uint32 xOriIP = 88;</code>
     * @return The xOriIP.
     */
    int getXOriIP();

    /**
     * <code>optional uint32 senderEmailCnt = 89;</code>
     * @return Whether the senderEmailCnt field is set.
     */
    boolean hasSenderEmailCnt();
    /**
     * <code>optional uint32 senderEmailCnt = 89;</code>
     * @return The senderEmailCnt.
     */
    int getSenderEmailCnt();

    /**
     * <code>optional bytes fw = 90;</code>
     * @return Whether the fw field is set.
     */
    boolean hasFw();
    /**
     * <code>optional bytes fw = 90;</code>
     * @return The fw.
     */
    com.google.protobuf.ByteString getFw();

    /**
     * <code>optional bytes befw = 91;</code>
     * @return Whether the befw field is set.
     */
    boolean hasBefw();
    /**
     * <code>optional bytes befw = 91;</code>
     * @return The befw.
     */
    com.google.protobuf.ByteString getBefw();

    /**
     * <code>optional uint64 rcvDate = 92;</code>
     * @return Whether the rcvDate field is set.
     */
    boolean hasRcvDate();
    /**
     * <code>optional uint64 rcvDate = 92;</code>
     * @return The rcvDate.
     */
    long getRcvDate();

    /**
     * <code>optional bytes rcvSrvAge = 93;</code>
     * @return Whether the rcvSrvAge field is set.
     */
    boolean hasRcvSrvAge();
    /**
     * <code>optional bytes rcvSrvAge = 93;</code>
     * @return The rcvSrvAge.
     */
    com.google.protobuf.ByteString getRcvSrvAge();

    /**
     * <code>optional bytes conTraEnc = 94;</code>
     * @return Whether the conTraEnc field is set.
     */
    boolean hasConTraEnc();
    /**
     * <code>optional bytes conTraEnc = 94;</code>
     * @return The conTraEnc.
     */
    com.google.protobuf.ByteString getConTraEnc();

    /**
     * <code>optional bytes conTexCha = 95;</code>
     * @return Whether the conTexCha field is set.
     */
    boolean hasConTexCha();
    /**
     * <code>optional bytes conTexCha = 95;</code>
     * @return The conTexCha.
     */
    com.google.protobuf.ByteString getConTexCha();

    /**
     * <code>optional bytes realFrom = 96;</code>
     * @return Whether the realFrom field is set.
     */
    boolean hasRealFrom();
    /**
     * <code>optional bytes realFrom = 96;</code>
     * @return The realFrom.
     */
    com.google.protobuf.ByteString getRealFrom();

    /**
     * <code>optional bytes realTo = 97;</code>
     * @return Whether the realTo field is set.
     */
    boolean hasRealTo();
    /**
     * <code>optional bytes realTo = 97;</code>
     * @return The realTo.
     */
    com.google.protobuf.ByteString getRealTo();

    /**
     * <code>optional bytes emaActRep = 98;</code>
     * @return Whether the emaActRep field is set.
     */
    boolean hasEmaActRep();
    /**
     * <code>optional bytes emaActRep = 98;</code>
     * @return The emaActRep.
     */
    com.google.protobuf.ByteString getEmaActRep();

    /**
     * <code>optional bytes rcvFromName = 99;</code>
     * @return Whether the rcvFromName field is set.
     */
    boolean hasRcvFromName();
    /**
     * <code>optional bytes rcvFromName = 99;</code>
     * @return The rcvFromName.
     */
    com.google.protobuf.ByteString getRcvFromName();

    /**
     * <code>optional bytes errType = 100;</code>
     * @return Whether the errType field is set.
     */
    boolean hasErrType();
    /**
     * <code>optional bytes errType = 100;</code>
     * @return The errType.
     */
    com.google.protobuf.ByteString getErrType();

    /**
     * <code>optional bytes contentWithHtml = 101;</code>
     * @return Whether the contentWithHtml field is set.
     */
    boolean hasContentWithHtml();
    /**
     * <code>optional bytes contentWithHtml = 101;</code>
     * @return The contentWithHtml.
     */
    com.google.protobuf.ByteString getContentWithHtml();

    /**
     * <code>optional bytes charset = 102;</code>
     * @return Whether the charset field is set.
     */
    boolean hasCharset();
    /**
     * <code>optional bytes charset = 102;</code>
     * @return The charset.
     */
    com.google.protobuf.ByteString getCharset();

    /**
     * <code>optional uint32 contentLen = 103;</code>
     * @return Whether the contentLen field is set.
     */
    boolean hasContentLen();
    /**
     * <code>optional uint32 contentLen = 103;</code>
     * @return The contentLen.
     */
    int getContentLen();

    /**
     * <code>optional bytes isBeFw = 104;</code>
     * @return Whether the isBeFw field is set.
     */
    boolean hasIsBeFw();
    /**
     * <code>optional bytes isBeFw = 104;</code>
     * @return The isBeFw.
     */
    com.google.protobuf.ByteString getIsBeFw();

    /**
     * <code>optional bytes attachmentPath = 105;</code>
     * @return Whether the attachmentPath field is set.
     */
    boolean hasAttachmentPath();
    /**
     * <code>optional bytes attachmentPath = 105;</code>
     * @return The attachmentPath.
     */
    com.google.protobuf.ByteString getAttachmentPath();

    /**
     * <code>optional bytes banner = 106;</code>
     * @return Whether the banner field is set.
     */
    boolean hasBanner();
    /**
     * <code>optional bytes banner = 106;</code>
     * @return The banner.
     */
    com.google.protobuf.ByteString getBanner();

    /**
     * <code>repeated bytes senderSoftware = 107;</code>
     * @return A list containing the senderSoftware.
     */
    java.util.List<com.google.protobuf.ByteString> getSenderSoftwareList();
    /**
     * <code>repeated bytes senderSoftware = 107;</code>
     * @return The count of senderSoftware.
     */
    int getSenderSoftwareCount();
    /**
     * <code>repeated bytes senderSoftware = 107;</code>
     * @param index The index of the element to return.
     * @return The senderSoftware at the given index.
     */
    com.google.protobuf.ByteString getSenderSoftware(int index);
  }
  /**
   * Protobuf type {@code EMAILInfo}
   */
  public static final class EMAILInfo extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:EMAILInfo)
      EMAILInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        EMAILInfo.class.getName());
    }
    // Use EMAILInfo.newBuilder() to construct.
    private EMAILInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private EMAILInfo() {
      attType_ = emptyList(com.google.protobuf.ByteString.class);
      attFileName_ = emptyList(com.google.protobuf.ByteString.class);
      attConSize_ = emptyLongList();
      attMD5_ = emptyList(com.google.protobuf.ByteString.class);
      bCC_ = emptyList(com.google.protobuf.ByteString.class);
      body_ = com.google.protobuf.ByteString.EMPTY;
      bodyTexCha_ = com.google.protobuf.ByteString.EMPTY;
      bodyTraEnc_ = com.google.protobuf.ByteString.EMPTY;
      bodyMD5_ = com.google.protobuf.ByteString.EMPTY;
      bodyType_ = emptyList(com.google.protobuf.ByteString.class);
      bodyURL_ = com.google.protobuf.ByteString.EMPTY;
      byAsn_ = emptyList(com.google.protobuf.ByteString.class);
      byCountry_ = emptyList(com.google.protobuf.ByteString.class);
      byDom_ = emptyList(com.google.protobuf.ByteString.class);
      byIP_ = com.google.protobuf.ByteString.EMPTY;
      cC_ = emptyList(com.google.protobuf.ByteString.class);
      cCAli_ = emptyList(com.google.protobuf.ByteString.class);
      command_ = emptyList(com.google.protobuf.ByteString.class);
      content_ = com.google.protobuf.ByteString.EMPTY;
      conType_ = emptyList(com.google.protobuf.ByteString.class);
      deliveredTo_ = com.google.protobuf.ByteString.EMPTY;
      fromAsn_ = emptyList(com.google.protobuf.ByteString.class);
      fromCountry_ = emptyList(com.google.protobuf.ByteString.class);
      fromDom_ = emptyList(com.google.protobuf.ByteString.class);
      fromIp_ = emptyIntList();
      headSet_ = emptyList(com.google.protobuf.ByteString.class);
      host_ = com.google.protobuf.ByteString.EMPTY;
      name_ = com.google.protobuf.ByteString.EMPTY;
      os_ = com.google.protobuf.ByteString.EMPTY;
      osVer_ = com.google.protobuf.ByteString.EMPTY;
      vendor_ = com.google.protobuf.ByteString.EMPTY;
      ver_ = com.google.protobuf.ByteString.EMPTY;
      emaInd_ = com.google.protobuf.ByteString.EMPTY;
      login_ = com.google.protobuf.ByteString.EMPTY;
      loginsrv_ = com.google.protobuf.ByteString.EMPTY;
      mailFrom_ = emptyList(com.google.protobuf.ByteString.class);
      mailFromDom_ = emptyList(com.google.protobuf.ByteString.class);
      mailFromDomCnt_ = com.google.protobuf.ByteString.EMPTY;
      mimeVer_ = com.google.protobuf.ByteString.EMPTY;
      msgID_ = emptyList(com.google.protobuf.ByteString.class);
      emaProtType_ = com.google.protobuf.ByteString.EMPTY;
      pwd_ = com.google.protobuf.ByteString.EMPTY;
      rcptTo_ = emptyList(com.google.protobuf.ByteString.class);
      rcptToDom_ = emptyList(com.google.protobuf.ByteString.class);
      repTo_ = com.google.protobuf.ByteString.EMPTY;
      received_ = emptyList(com.google.protobuf.ByteString.class);
      rcvrEmail_ = emptyList(com.google.protobuf.ByteString.class);
      rcvrAli_ = emptyList(com.google.protobuf.ByteString.class);
      rcvrDom_ = emptyList(com.google.protobuf.ByteString.class);
      resentSrvAge_ = com.google.protobuf.ByteString.EMPTY;
      resentFrom_ = com.google.protobuf.ByteString.EMPTY;
      resentTo_ = emptyList(com.google.protobuf.ByteString.class);
      senderEmail_ = com.google.protobuf.ByteString.EMPTY;
      senderAli_ = com.google.protobuf.ByteString.EMPTY;
      senderDom_ = com.google.protobuf.ByteString.EMPTY;
      sMTPSrv_ = com.google.protobuf.ByteString.EMPTY;
      sMTPSrvAge_ = com.google.protobuf.ByteString.EMPTY;
      receivedSPF_ = com.google.protobuf.ByteString.EMPTY;
      subj_ = com.google.protobuf.ByteString.EMPTY;
      usrAge_ = com.google.protobuf.ByteString.EMPTY;
      emailVersion_ = com.google.protobuf.ByteString.EMPTY;
      rcvWit_ = com.google.protobuf.ByteString.EMPTY;
      xMai_ = com.google.protobuf.ByteString.EMPTY;
      fw_ = com.google.protobuf.ByteString.EMPTY;
      befw_ = com.google.protobuf.ByteString.EMPTY;
      rcvSrvAge_ = com.google.protobuf.ByteString.EMPTY;
      conTraEnc_ = com.google.protobuf.ByteString.EMPTY;
      conTexCha_ = com.google.protobuf.ByteString.EMPTY;
      realFrom_ = com.google.protobuf.ByteString.EMPTY;
      realTo_ = com.google.protobuf.ByteString.EMPTY;
      emaActRep_ = com.google.protobuf.ByteString.EMPTY;
      rcvFromName_ = com.google.protobuf.ByteString.EMPTY;
      errType_ = com.google.protobuf.ByteString.EMPTY;
      contentWithHtml_ = com.google.protobuf.ByteString.EMPTY;
      charset_ = com.google.protobuf.ByteString.EMPTY;
      isBeFw_ = com.google.protobuf.ByteString.EMPTY;
      attachmentPath_ = com.google.protobuf.ByteString.EMPTY;
      banner_ = com.google.protobuf.ByteString.EMPTY;
      senderSoftware_ = emptyList(com.google.protobuf.ByteString.class);
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return EMAILInfoOuterClass.internal_static_EMAILInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return EMAILInfoOuterClass.internal_static_EMAILInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              EMAILInfoOuterClass.EMAILInfo.class, EMAILInfoOuterClass.EMAILInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    public static final int ATTTYPE_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> attType_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes attType = 1;</code>
     * @return A list containing the attType.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAttTypeList() {
      return attType_;
    }
    /**
     * <code>repeated bytes attType = 1;</code>
     * @return The count of attType.
     */
    public int getAttTypeCount() {
      return attType_.size();
    }
    /**
     * <code>repeated bytes attType = 1;</code>
     * @param index The index of the element to return.
     * @return The attType at the given index.
     */
    public com.google.protobuf.ByteString getAttType(int index) {
      return attType_.get(index);
    }

    public static final int ATTTYPECNT_FIELD_NUMBER = 2;
    private int attTypeCnt_ = 0;
    /**
     * <code>optional uint32 attTypeCnt = 2;</code>
     * @return Whether the attTypeCnt field is set.
     */
    @java.lang.Override
    public boolean hasAttTypeCnt() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 attTypeCnt = 2;</code>
     * @return The attTypeCnt.
     */
    @java.lang.Override
    public int getAttTypeCnt() {
      return attTypeCnt_;
    }

    public static final int ATTFILENAME_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> attFileName_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes attFileName = 3;</code>
     * @return A list containing the attFileName.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAttFileNameList() {
      return attFileName_;
    }
    /**
     * <code>repeated bytes attFileName = 3;</code>
     * @return The count of attFileName.
     */
    public int getAttFileNameCount() {
      return attFileName_.size();
    }
    /**
     * <code>repeated bytes attFileName = 3;</code>
     * @param index The index of the element to return.
     * @return The attFileName at the given index.
     */
    public com.google.protobuf.ByteString getAttFileName(int index) {
      return attFileName_.get(index);
    }

    public static final int ATTFILENAMECNT_FIELD_NUMBER = 4;
    private int attFileNameCnt_ = 0;
    /**
     * <code>optional uint32 attFileNameCnt = 4;</code>
     * @return Whether the attFileNameCnt field is set.
     */
    @java.lang.Override
    public boolean hasAttFileNameCnt() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 attFileNameCnt = 4;</code>
     * @return The attFileNameCnt.
     */
    @java.lang.Override
    public int getAttFileNameCnt() {
      return attFileNameCnt_;
    }

    public static final int ATTCONSIZE_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList attConSize_ =
        emptyLongList();
    /**
     * <code>repeated uint64 attConSize = 5;</code>
     * @return A list containing the attConSize.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getAttConSizeList() {
      return attConSize_;
    }
    /**
     * <code>repeated uint64 attConSize = 5;</code>
     * @return The count of attConSize.
     */
    public int getAttConSizeCount() {
      return attConSize_.size();
    }
    /**
     * <code>repeated uint64 attConSize = 5;</code>
     * @param index The index of the element to return.
     * @return The attConSize at the given index.
     */
    public long getAttConSize(int index) {
      return attConSize_.getLong(index);
    }

    public static final int ATTMD5_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> attMD5_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes attMD5 = 6;</code>
     * @return A list containing the attMD5.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAttMD5List() {
      return attMD5_;
    }
    /**
     * <code>repeated bytes attMD5 = 6;</code>
     * @return The count of attMD5.
     */
    public int getAttMD5Count() {
      return attMD5_.size();
    }
    /**
     * <code>repeated bytes attMD5 = 6;</code>
     * @param index The index of the element to return.
     * @return The attMD5 at the given index.
     */
    public com.google.protobuf.ByteString getAttMD5(int index) {
      return attMD5_.get(index);
    }

    public static final int ATTMD5CNT_FIELD_NUMBER = 7;
    private int attMD5Cnt_ = 0;
    /**
     * <code>optional uint32 attMD5Cnt = 7;</code>
     * @return Whether the attMD5Cnt field is set.
     */
    @java.lang.Override
    public boolean hasAttMD5Cnt() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 attMD5Cnt = 7;</code>
     * @return The attMD5Cnt.
     */
    @java.lang.Override
    public int getAttMD5Cnt() {
      return attMD5Cnt_;
    }

    public static final int AUTHRELT_FIELD_NUMBER = 8;
    private int authRelt_ = 0;
    /**
     * <code>optional uint32 authRelt = 8;</code>
     * @return Whether the authRelt field is set.
     */
    @java.lang.Override
    public boolean hasAuthRelt() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 authRelt = 8;</code>
     * @return The authRelt.
     */
    @java.lang.Override
    public int getAuthRelt() {
      return authRelt_;
    }

    public static final int BCC_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> bCC_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes BCC = 9;</code>
     * @return A list containing the bCC.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getBCCList() {
      return bCC_;
    }
    /**
     * <code>repeated bytes BCC = 9;</code>
     * @return The count of bCC.
     */
    public int getBCCCount() {
      return bCC_.size();
    }
    /**
     * <code>repeated bytes BCC = 9;</code>
     * @param index The index of the element to return.
     * @return The bCC at the given index.
     */
    public com.google.protobuf.ByteString getBCC(int index) {
      return bCC_.get(index);
    }

    public static final int BODY_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString body_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes body = 10;</code>
     * @return Whether the body field is set.
     */
    @java.lang.Override
    public boolean hasBody() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes body = 10;</code>
     * @return The body.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBody() {
      return body_;
    }

    public static final int BODYTEXCHA_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString bodyTexCha_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes bodyTexCha = 11;</code>
     * @return Whether the bodyTexCha field is set.
     */
    @java.lang.Override
    public boolean hasBodyTexCha() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes bodyTexCha = 11;</code>
     * @return The bodyTexCha.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBodyTexCha() {
      return bodyTexCha_;
    }

    public static final int BODYTRAENC_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString bodyTraEnc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes bodyTraEnc = 12;</code>
     * @return Whether the bodyTraEnc field is set.
     */
    @java.lang.Override
    public boolean hasBodyTraEnc() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes bodyTraEnc = 12;</code>
     * @return The bodyTraEnc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBodyTraEnc() {
      return bodyTraEnc_;
    }

    public static final int BODYMD5_FIELD_NUMBER = 13;
    private com.google.protobuf.ByteString bodyMD5_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes bodyMD5 = 13;</code>
     * @return Whether the bodyMD5 field is set.
     */
    @java.lang.Override
    public boolean hasBodyMD5() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes bodyMD5 = 13;</code>
     * @return The bodyMD5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBodyMD5() {
      return bodyMD5_;
    }

    public static final int BODYTYPE_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> bodyType_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes bodyType = 14;</code>
     * @return A list containing the bodyType.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getBodyTypeList() {
      return bodyType_;
    }
    /**
     * <code>repeated bytes bodyType = 14;</code>
     * @return The count of bodyType.
     */
    public int getBodyTypeCount() {
      return bodyType_.size();
    }
    /**
     * <code>repeated bytes bodyType = 14;</code>
     * @param index The index of the element to return.
     * @return The bodyType at the given index.
     */
    public com.google.protobuf.ByteString getBodyType(int index) {
      return bodyType_.get(index);
    }

    public static final int BODYTYPECNT_FIELD_NUMBER = 15;
    private int bodyTypeCnt_ = 0;
    /**
     * <code>optional uint32 bodyTypeCnt = 15;</code>
     * @return Whether the bodyTypeCnt field is set.
     */
    @java.lang.Override
    public boolean hasBodyTypeCnt() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint32 bodyTypeCnt = 15;</code>
     * @return The bodyTypeCnt.
     */
    @java.lang.Override
    public int getBodyTypeCnt() {
      return bodyTypeCnt_;
    }

    public static final int BODYURL_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString bodyURL_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes bodyURL = 16;</code>
     * @return Whether the bodyURL field is set.
     */
    @java.lang.Override
    public boolean hasBodyURL() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes bodyURL = 16;</code>
     * @return The bodyURL.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBodyURL() {
      return bodyURL_;
    }

    public static final int BODYURLCNT_FIELD_NUMBER = 17;
    private int bodyURLCnt_ = 0;
    /**
     * <code>optional uint32 bodyURLCnt = 17;</code>
     * @return Whether the bodyURLCnt field is set.
     */
    @java.lang.Override
    public boolean hasBodyURLCnt() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint32 bodyURLCnt = 17;</code>
     * @return The bodyURLCnt.
     */
    @java.lang.Override
    public int getBodyURLCnt() {
      return bodyURLCnt_;
    }

    public static final int BODYLEN_FIELD_NUMBER = 18;
    private int bodyLen_ = 0;
    /**
     * <code>optional uint32 bodyLen = 18;</code>
     * @return Whether the bodyLen field is set.
     */
    @java.lang.Override
    public boolean hasBodyLen() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 bodyLen = 18;</code>
     * @return The bodyLen.
     */
    @java.lang.Override
    public int getBodyLen() {
      return bodyLen_;
    }

    public static final int BYASN_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> byAsn_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes ByAsn = 19;</code>
     * @return A list containing the byAsn.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getByAsnList() {
      return byAsn_;
    }
    /**
     * <code>repeated bytes ByAsn = 19;</code>
     * @return The count of byAsn.
     */
    public int getByAsnCount() {
      return byAsn_.size();
    }
    /**
     * <code>repeated bytes ByAsn = 19;</code>
     * @param index The index of the element to return.
     * @return The byAsn at the given index.
     */
    public com.google.protobuf.ByteString getByAsn(int index) {
      return byAsn_.get(index);
    }

    public static final int BYCOUNTRY_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> byCountry_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes ByCountry = 20;</code>
     * @return A list containing the byCountry.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getByCountryList() {
      return byCountry_;
    }
    /**
     * <code>repeated bytes ByCountry = 20;</code>
     * @return The count of byCountry.
     */
    public int getByCountryCount() {
      return byCountry_.size();
    }
    /**
     * <code>repeated bytes ByCountry = 20;</code>
     * @param index The index of the element to return.
     * @return The byCountry at the given index.
     */
    public com.google.protobuf.ByteString getByCountry(int index) {
      return byCountry_.get(index);
    }

    public static final int BYDOM_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> byDom_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes ByDom = 21;</code>
     * @return A list containing the byDom.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getByDomList() {
      return byDom_;
    }
    /**
     * <code>repeated bytes ByDom = 21;</code>
     * @return The count of byDom.
     */
    public int getByDomCount() {
      return byDom_.size();
    }
    /**
     * <code>repeated bytes ByDom = 21;</code>
     * @param index The index of the element to return.
     * @return The byDom at the given index.
     */
    public com.google.protobuf.ByteString getByDom(int index) {
      return byDom_.get(index);
    }

    public static final int BYDOMCNT_FIELD_NUMBER = 22;
    private int byDomCnt_ = 0;
    /**
     * <code>optional uint32 ByDomCnt = 22;</code>
     * @return Whether the byDomCnt field is set.
     */
    @java.lang.Override
    public boolean hasByDomCnt() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 ByDomCnt = 22;</code>
     * @return The byDomCnt.
     */
    @java.lang.Override
    public int getByDomCnt() {
      return byDomCnt_;
    }

    public static final int BYIP_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString byIP_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ByIP = 23;</code>
     * @return Whether the byIP field is set.
     */
    @java.lang.Override
    public boolean hasByIP() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes ByIP = 23;</code>
     * @return The byIP.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getByIP() {
      return byIP_;
    }

    public static final int BYIPCNT_FIELD_NUMBER = 24;
    private int byIpCnt_ = 0;
    /**
     * <code>optional uint32 ByIpCnt = 24;</code>
     * @return Whether the byIpCnt field is set.
     */
    @java.lang.Override
    public boolean hasByIpCnt() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 ByIpCnt = 24;</code>
     * @return The byIpCnt.
     */
    @java.lang.Override
    public int getByIpCnt() {
      return byIpCnt_;
    }

    public static final int CC_FIELD_NUMBER = 25;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> cC_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes CC = 25;</code>
     * @return A list containing the cC.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getCCList() {
      return cC_;
    }
    /**
     * <code>repeated bytes CC = 25;</code>
     * @return The count of cC.
     */
    public int getCCCount() {
      return cC_.size();
    }
    /**
     * <code>repeated bytes CC = 25;</code>
     * @param index The index of the element to return.
     * @return The cC at the given index.
     */
    public com.google.protobuf.ByteString getCC(int index) {
      return cC_.get(index);
    }

    public static final int CCALI_FIELD_NUMBER = 26;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> cCAli_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes CCAli = 26;</code>
     * @return A list containing the cCAli.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getCCAliList() {
      return cCAli_;
    }
    /**
     * <code>repeated bytes CCAli = 26;</code>
     * @return The count of cCAli.
     */
    public int getCCAliCount() {
      return cCAli_.size();
    }
    /**
     * <code>repeated bytes CCAli = 26;</code>
     * @param index The index of the element to return.
     * @return The cCAli at the given index.
     */
    public com.google.protobuf.ByteString getCCAli(int index) {
      return cCAli_.get(index);
    }

    public static final int COMMAND_FIELD_NUMBER = 27;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> command_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes Command = 27;</code>
     * @return A list containing the command.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getCommandList() {
      return command_;
    }
    /**
     * <code>repeated bytes Command = 27;</code>
     * @return The count of command.
     */
    public int getCommandCount() {
      return command_.size();
    }
    /**
     * <code>repeated bytes Command = 27;</code>
     * @param index The index of the element to return.
     * @return The command at the given index.
     */
    public com.google.protobuf.ByteString getCommand(int index) {
      return command_.get(index);
    }

    public static final int COUNT_FIELD_NUMBER = 28;
    private int count_ = 0;
    /**
     * <code>optional uint32 count = 28;</code>
     * @return Whether the count field is set.
     */
    @java.lang.Override
    public boolean hasCount() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional uint32 count = 28;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    public static final int CONTENT_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString content_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes content = 29;</code>
     * @return Whether the content field is set.
     */
    @java.lang.Override
    public boolean hasContent() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes content = 29;</code>
     * @return The content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getContent() {
      return content_;
    }

    public static final int CONTYPE_FIELD_NUMBER = 30;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> conType_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes conType = 30;</code>
     * @return A list containing the conType.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getConTypeList() {
      return conType_;
    }
    /**
     * <code>repeated bytes conType = 30;</code>
     * @return The count of conType.
     */
    public int getConTypeCount() {
      return conType_.size();
    }
    /**
     * <code>repeated bytes conType = 30;</code>
     * @param index The index of the element to return.
     * @return The conType at the given index.
     */
    public com.google.protobuf.ByteString getConType(int index) {
      return conType_.get(index);
    }

    public static final int CONTYPECNT_FIELD_NUMBER = 31;
    private int conTypeCnt_ = 0;
    /**
     * <code>optional uint32 conTypeCnt = 31;</code>
     * @return Whether the conTypeCnt field is set.
     */
    @java.lang.Override
    public boolean hasConTypeCnt() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 conTypeCnt = 31;</code>
     * @return The conTypeCnt.
     */
    @java.lang.Override
    public int getConTypeCnt() {
      return conTypeCnt_;
    }

    public static final int DATE_FIELD_NUMBER = 32;
    private int date_ = 0;
    /**
     * <code>optional uint32 date = 32;</code>
     * @return Whether the date field is set.
     */
    @java.lang.Override
    public boolean hasDate() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 date = 32;</code>
     * @return The date.
     */
    @java.lang.Override
    public int getDate() {
      return date_;
    }

    public static final int DELIVEREDTO_FIELD_NUMBER = 33;
    private com.google.protobuf.ByteString deliveredTo_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes deliveredTo = 33;</code>
     * @return Whether the deliveredTo field is set.
     */
    @java.lang.Override
    public boolean hasDeliveredTo() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes deliveredTo = 33;</code>
     * @return The deliveredTo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDeliveredTo() {
      return deliveredTo_;
    }

    public static final int FROMASN_FIELD_NUMBER = 34;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> fromAsn_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes FromAsn = 34;</code>
     * @return A list containing the fromAsn.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getFromAsnList() {
      return fromAsn_;
    }
    /**
     * <code>repeated bytes FromAsn = 34;</code>
     * @return The count of fromAsn.
     */
    public int getFromAsnCount() {
      return fromAsn_.size();
    }
    /**
     * <code>repeated bytes FromAsn = 34;</code>
     * @param index The index of the element to return.
     * @return The fromAsn at the given index.
     */
    public com.google.protobuf.ByteString getFromAsn(int index) {
      return fromAsn_.get(index);
    }

    public static final int FROMCOUNTRY_FIELD_NUMBER = 35;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> fromCountry_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes FromCountry = 35;</code>
     * @return A list containing the fromCountry.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getFromCountryList() {
      return fromCountry_;
    }
    /**
     * <code>repeated bytes FromCountry = 35;</code>
     * @return The count of fromCountry.
     */
    public int getFromCountryCount() {
      return fromCountry_.size();
    }
    /**
     * <code>repeated bytes FromCountry = 35;</code>
     * @param index The index of the element to return.
     * @return The fromCountry at the given index.
     */
    public com.google.protobuf.ByteString getFromCountry(int index) {
      return fromCountry_.get(index);
    }

    public static final int FROMDOM_FIELD_NUMBER = 36;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> fromDom_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes FromDom = 36;</code>
     * @return A list containing the fromDom.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getFromDomList() {
      return fromDom_;
    }
    /**
     * <code>repeated bytes FromDom = 36;</code>
     * @return The count of fromDom.
     */
    public int getFromDomCount() {
      return fromDom_.size();
    }
    /**
     * <code>repeated bytes FromDom = 36;</code>
     * @param index The index of the element to return.
     * @return The fromDom at the given index.
     */
    public com.google.protobuf.ByteString getFromDom(int index) {
      return fromDom_.get(index);
    }

    public static final int FROMDOMCNT_FIELD_NUMBER = 37;
    private int fromDomCnt_ = 0;
    /**
     * <code>optional uint32 FromDomCnt = 37;</code>
     * @return Whether the fromDomCnt field is set.
     */
    @java.lang.Override
    public boolean hasFromDomCnt() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional uint32 FromDomCnt = 37;</code>
     * @return The fromDomCnt.
     */
    @java.lang.Override
    public int getFromDomCnt() {
      return fromDomCnt_;
    }

    public static final int FROMIP_FIELD_NUMBER = 38;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList fromIp_ =
        emptyIntList();
    /**
     * <code>repeated uint32 FromIp = 38;</code>
     * @return A list containing the fromIp.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getFromIpList() {
      return fromIp_;
    }
    /**
     * <code>repeated uint32 FromIp = 38;</code>
     * @return The count of fromIp.
     */
    public int getFromIpCount() {
      return fromIp_.size();
    }
    /**
     * <code>repeated uint32 FromIp = 38;</code>
     * @param index The index of the element to return.
     * @return The fromIp at the given index.
     */
    public int getFromIp(int index) {
      return fromIp_.getInt(index);
    }

    public static final int FROMIPCNT_FIELD_NUMBER = 39;
    private int fromIpCnt_ = 0;
    /**
     * <code>optional uint32 FromIpCnt = 39;</code>
     * @return Whether the fromIpCnt field is set.
     */
    @java.lang.Override
    public boolean hasFromIpCnt() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 FromIpCnt = 39;</code>
     * @return The fromIpCnt.
     */
    @java.lang.Override
    public int getFromIpCnt() {
      return fromIpCnt_;
    }

    public static final int HEADSET_FIELD_NUMBER = 40;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> headSet_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes headSet = 40;</code>
     * @return A list containing the headSet.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getHeadSetList() {
      return headSet_;
    }
    /**
     * <code>repeated bytes headSet = 40;</code>
     * @return The count of headSet.
     */
    public int getHeadSetCount() {
      return headSet_.size();
    }
    /**
     * <code>repeated bytes headSet = 40;</code>
     * @param index The index of the element to return.
     * @return The headSet at the given index.
     */
    public com.google.protobuf.ByteString getHeadSet(int index) {
      return headSet_.get(index);
    }

    public static final int HEADSETCNT_FIELD_NUMBER = 41;
    private int headSetCnt_ = 0;
    /**
     * <code>optional uint32 headSetCnt = 41;</code>
     * @return Whether the headSetCnt field is set.
     */
    @java.lang.Override
    public boolean hasHeadSetCnt() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional uint32 headSetCnt = 41;</code>
     * @return The headSetCnt.
     */
    @java.lang.Override
    public int getHeadSetCnt() {
      return headSetCnt_;
    }

    public static final int HOST_FIELD_NUMBER = 42;
    private com.google.protobuf.ByteString host_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes host = 42;</code>
     * @return Whether the host field is set.
     */
    @java.lang.Override
    public boolean hasHost() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes host = 42;</code>
     * @return The host.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getHost() {
      return host_;
    }

    public static final int NAME_FIELD_NUMBER = 43;
    private com.google.protobuf.ByteString name_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes name = 43;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes name = 43;</code>
     * @return The name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getName() {
      return name_;
    }

    public static final int OS_FIELD_NUMBER = 44;
    private com.google.protobuf.ByteString os_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes os = 44;</code>
     * @return Whether the os field is set.
     */
    @java.lang.Override
    public boolean hasOs() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes os = 44;</code>
     * @return The os.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getOs() {
      return os_;
    }

    public static final int OSVER_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString osVer_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes osVer = 45;</code>
     * @return Whether the osVer field is set.
     */
    @java.lang.Override
    public boolean hasOsVer() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes osVer = 45;</code>
     * @return The osVer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getOsVer() {
      return osVer_;
    }

    public static final int VENDOR_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString vendor_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes vendor = 46;</code>
     * @return Whether the vendor field is set.
     */
    @java.lang.Override
    public boolean hasVendor() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes vendor = 46;</code>
     * @return The vendor.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getVendor() {
      return vendor_;
    }

    public static final int VER_FIELD_NUMBER = 47;
    private com.google.protobuf.ByteString ver_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ver = 47;</code>
     * @return Whether the ver field is set.
     */
    @java.lang.Override
    public boolean hasVer() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes ver = 47;</code>
     * @return The ver.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getVer() {
      return ver_;
    }

    public static final int EMAIND_FIELD_NUMBER = 48;
    private com.google.protobuf.ByteString emaInd_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes emaInd = 48;</code>
     * @return Whether the emaInd field is set.
     */
    @java.lang.Override
    public boolean hasEmaInd() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes emaInd = 48;</code>
     * @return The emaInd.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEmaInd() {
      return emaInd_;
    }

    public static final int LOGIN_FIELD_NUMBER = 49;
    private com.google.protobuf.ByteString login_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes login = 49;</code>
     * @return Whether the login field is set.
     */
    @java.lang.Override
    public boolean hasLogin() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes login = 49;</code>
     * @return The login.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getLogin() {
      return login_;
    }

    public static final int LOGINSRV_FIELD_NUMBER = 50;
    private com.google.protobuf.ByteString loginsrv_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes loginsrv = 50;</code>
     * @return Whether the loginsrv field is set.
     */
    @java.lang.Override
    public boolean hasLoginsrv() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <code>optional bytes loginsrv = 50;</code>
     * @return The loginsrv.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getLoginsrv() {
      return loginsrv_;
    }

    public static final int MAILFROM_FIELD_NUMBER = 51;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mailFrom_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes mailFrom = 51;</code>
     * @return A list containing the mailFrom.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getMailFromList() {
      return mailFrom_;
    }
    /**
     * <code>repeated bytes mailFrom = 51;</code>
     * @return The count of mailFrom.
     */
    public int getMailFromCount() {
      return mailFrom_.size();
    }
    /**
     * <code>repeated bytes mailFrom = 51;</code>
     * @param index The index of the element to return.
     * @return The mailFrom at the given index.
     */
    public com.google.protobuf.ByteString getMailFrom(int index) {
      return mailFrom_.get(index);
    }

    public static final int MAILFROMDOM_FIELD_NUMBER = 52;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mailFromDom_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes mailFromDom = 52;</code>
     * @return A list containing the mailFromDom.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getMailFromDomList() {
      return mailFromDom_;
    }
    /**
     * <code>repeated bytes mailFromDom = 52;</code>
     * @return The count of mailFromDom.
     */
    public int getMailFromDomCount() {
      return mailFromDom_.size();
    }
    /**
     * <code>repeated bytes mailFromDom = 52;</code>
     * @param index The index of the element to return.
     * @return The mailFromDom at the given index.
     */
    public com.google.protobuf.ByteString getMailFromDom(int index) {
      return mailFromDom_.get(index);
    }

    public static final int MAILFROMDOMCNT_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString mailFromDomCnt_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes mailFromDomCnt = 53;</code>
     * @return Whether the mailFromDomCnt field is set.
     */
    @java.lang.Override
    public boolean hasMailFromDomCnt() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes mailFromDomCnt = 53;</code>
     * @return The mailFromDomCnt.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMailFromDomCnt() {
      return mailFromDomCnt_;
    }

    public static final int MIMEVER_FIELD_NUMBER = 54;
    private com.google.protobuf.ByteString mimeVer_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes mimeVer = 54;</code>
     * @return Whether the mimeVer field is set.
     */
    @java.lang.Override
    public boolean hasMimeVer() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes mimeVer = 54;</code>
     * @return The mimeVer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMimeVer() {
      return mimeVer_;
    }

    public static final int MIMEVERCNT_FIELD_NUMBER = 55;
    private int mimeVerCnt_ = 0;
    /**
     * <code>optional uint32 mimeVerCnt = 55;</code>
     * @return Whether the mimeVerCnt field is set.
     */
    @java.lang.Override
    public boolean hasMimeVerCnt() {
      return ((bitField1_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 mimeVerCnt = 55;</code>
     * @return The mimeVerCnt.
     */
    @java.lang.Override
    public int getMimeVerCnt() {
      return mimeVerCnt_;
    }

    public static final int MSGID_FIELD_NUMBER = 56;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> msgID_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes msgID = 56;</code>
     * @return A list containing the msgID.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getMsgIDList() {
      return msgID_;
    }
    /**
     * <code>repeated bytes msgID = 56;</code>
     * @return The count of msgID.
     */
    public int getMsgIDCount() {
      return msgID_.size();
    }
    /**
     * <code>repeated bytes msgID = 56;</code>
     * @param index The index of the element to return.
     * @return The msgID at the given index.
     */
    public com.google.protobuf.ByteString getMsgID(int index) {
      return msgID_.get(index);
    }

    public static final int MSGIDCNT_FIELD_NUMBER = 57;
    private int msgIDCnt_ = 0;
    /**
     * <code>optional uint32 msgIDCnt = 57;</code>
     * @return Whether the msgIDCnt field is set.
     */
    @java.lang.Override
    public boolean hasMsgIDCnt() {
      return ((bitField1_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 msgIDCnt = 57;</code>
     * @return The msgIDCnt.
     */
    @java.lang.Override
    public int getMsgIDCnt() {
      return msgIDCnt_;
    }

    public static final int EMAPROTTYPE_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString emaProtType_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes emaProtType = 58;</code>
     * @return Whether the emaProtType field is set.
     */
    @java.lang.Override
    public boolean hasEmaProtType() {
      return ((bitField1_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes emaProtType = 58;</code>
     * @return The emaProtType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEmaProtType() {
      return emaProtType_;
    }

    public static final int PWD_FIELD_NUMBER = 59;
    private com.google.protobuf.ByteString pwd_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes pwd = 59;</code>
     * @return Whether the pwd field is set.
     */
    @java.lang.Override
    public boolean hasPwd() {
      return ((bitField1_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes pwd = 59;</code>
     * @return The pwd.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPwd() {
      return pwd_;
    }

    public static final int RCPTTO_FIELD_NUMBER = 60;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcptTo_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes rcptTo = 60;</code>
     * @return A list containing the rcptTo.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getRcptToList() {
      return rcptTo_;
    }
    /**
     * <code>repeated bytes rcptTo = 60;</code>
     * @return The count of rcptTo.
     */
    public int getRcptToCount() {
      return rcptTo_.size();
    }
    /**
     * <code>repeated bytes rcptTo = 60;</code>
     * @param index The index of the element to return.
     * @return The rcptTo at the given index.
     */
    public com.google.protobuf.ByteString getRcptTo(int index) {
      return rcptTo_.get(index);
    }

    public static final int RCPTTODOM_FIELD_NUMBER = 61;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcptToDom_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes rcptToDom = 61;</code>
     * @return A list containing the rcptToDom.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getRcptToDomList() {
      return rcptToDom_;
    }
    /**
     * <code>repeated bytes rcptToDom = 61;</code>
     * @return The count of rcptToDom.
     */
    public int getRcptToDomCount() {
      return rcptToDom_.size();
    }
    /**
     * <code>repeated bytes rcptToDom = 61;</code>
     * @param index The index of the element to return.
     * @return The rcptToDom at the given index.
     */
    public com.google.protobuf.ByteString getRcptToDom(int index) {
      return rcptToDom_.get(index);
    }

    public static final int RCPTTODOMCNT_FIELD_NUMBER = 62;
    private int rcptToDomCnt_ = 0;
    /**
     * <code>optional uint32 rcptToDomCnt = 62;</code>
     * @return Whether the rcptToDomCnt field is set.
     */
    @java.lang.Override
    public boolean hasRcptToDomCnt() {
      return ((bitField1_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 rcptToDomCnt = 62;</code>
     * @return The rcptToDomCnt.
     */
    @java.lang.Override
    public int getRcptToDomCnt() {
      return rcptToDomCnt_;
    }

    public static final int REPTO_FIELD_NUMBER = 63;
    private com.google.protobuf.ByteString repTo_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes repTo = 63;</code>
     * @return Whether the repTo field is set.
     */
    @java.lang.Override
    public boolean hasRepTo() {
      return ((bitField1_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes repTo = 63;</code>
     * @return The repTo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRepTo() {
      return repTo_;
    }

    public static final int RECEIVED_FIELD_NUMBER = 64;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> received_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes received = 64;</code>
     * @return A list containing the received.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getReceivedList() {
      return received_;
    }
    /**
     * <code>repeated bytes received = 64;</code>
     * @return The count of received.
     */
    public int getReceivedCount() {
      return received_.size();
    }
    /**
     * <code>repeated bytes received = 64;</code>
     * @param index The index of the element to return.
     * @return The received at the given index.
     */
    public com.google.protobuf.ByteString getReceived(int index) {
      return received_.get(index);
    }

    public static final int RCVREMAIL_FIELD_NUMBER = 65;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcvrEmail_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes rcvrEmail = 65;</code>
     * @return A list containing the rcvrEmail.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getRcvrEmailList() {
      return rcvrEmail_;
    }
    /**
     * <code>repeated bytes rcvrEmail = 65;</code>
     * @return The count of rcvrEmail.
     */
    public int getRcvrEmailCount() {
      return rcvrEmail_.size();
    }
    /**
     * <code>repeated bytes rcvrEmail = 65;</code>
     * @param index The index of the element to return.
     * @return The rcvrEmail at the given index.
     */
    public com.google.protobuf.ByteString getRcvrEmail(int index) {
      return rcvrEmail_.get(index);
    }

    public static final int RCVRALI_FIELD_NUMBER = 66;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcvrAli_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes rcvrAli = 66;</code>
     * @return A list containing the rcvrAli.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getRcvrAliList() {
      return rcvrAli_;
    }
    /**
     * <code>repeated bytes rcvrAli = 66;</code>
     * @return The count of rcvrAli.
     */
    public int getRcvrAliCount() {
      return rcvrAli_.size();
    }
    /**
     * <code>repeated bytes rcvrAli = 66;</code>
     * @param index The index of the element to return.
     * @return The rcvrAli at the given index.
     */
    public com.google.protobuf.ByteString getRcvrAli(int index) {
      return rcvrAli_.get(index);
    }

    public static final int RCVRALICNT_FIELD_NUMBER = 67;
    private int rcvrAliCnt_ = 0;
    /**
     * <code>optional uint32 rcvrAliCnt = 67;</code>
     * @return Whether the rcvrAliCnt field is set.
     */
    @java.lang.Override
    public boolean hasRcvrAliCnt() {
      return ((bitField1_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint32 rcvrAliCnt = 67;</code>
     * @return The rcvrAliCnt.
     */
    @java.lang.Override
    public int getRcvrAliCnt() {
      return rcvrAliCnt_;
    }

    public static final int RCVREMAILCNT_FIELD_NUMBER = 68;
    private int rcvrEmailCnt_ = 0;
    /**
     * <code>optional uint32 rcvrEmailCnt = 68;</code>
     * @return Whether the rcvrEmailCnt field is set.
     */
    @java.lang.Override
    public boolean hasRcvrEmailCnt() {
      return ((bitField1_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint32 rcvrEmailCnt = 68;</code>
     * @return The rcvrEmailCnt.
     */
    @java.lang.Override
    public int getRcvrEmailCnt() {
      return rcvrEmailCnt_;
    }

    public static final int RCVRDOM_FIELD_NUMBER = 69;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcvrDom_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes rcvrDom = 69;</code>
     * @return A list containing the rcvrDom.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getRcvrDomList() {
      return rcvrDom_;
    }
    /**
     * <code>repeated bytes rcvrDom = 69;</code>
     * @return The count of rcvrDom.
     */
    public int getRcvrDomCount() {
      return rcvrDom_.size();
    }
    /**
     * <code>repeated bytes rcvrDom = 69;</code>
     * @param index The index of the element to return.
     * @return The rcvrDom at the given index.
     */
    public com.google.protobuf.ByteString getRcvrDom(int index) {
      return rcvrDom_.get(index);
    }

    public static final int RESENTSRVAGE_FIELD_NUMBER = 70;
    private com.google.protobuf.ByteString resentSrvAge_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes resentSrvAge = 70;</code>
     * @return Whether the resentSrvAge field is set.
     */
    @java.lang.Override
    public boolean hasResentSrvAge() {
      return ((bitField1_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes resentSrvAge = 70;</code>
     * @return The resentSrvAge.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getResentSrvAge() {
      return resentSrvAge_;
    }

    public static final int RESENTDATE_FIELD_NUMBER = 71;
    private int resentDate_ = 0;
    /**
     * <code>optional uint32 resentDate = 71;</code>
     * @return Whether the resentDate field is set.
     */
    @java.lang.Override
    public boolean hasResentDate() {
      return ((bitField1_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 resentDate = 71;</code>
     * @return The resentDate.
     */
    @java.lang.Override
    public int getResentDate() {
      return resentDate_;
    }

    public static final int RESENTFROM_FIELD_NUMBER = 72;
    private com.google.protobuf.ByteString resentFrom_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes resentFrom = 72;</code>
     * @return Whether the resentFrom field is set.
     */
    @java.lang.Override
    public boolean hasResentFrom() {
      return ((bitField1_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes resentFrom = 72;</code>
     * @return The resentFrom.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getResentFrom() {
      return resentFrom_;
    }

    public static final int RESENTTO_FIELD_NUMBER = 73;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> resentTo_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes resentTo = 73;</code>
     * @return A list containing the resentTo.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getResentToList() {
      return resentTo_;
    }
    /**
     * <code>repeated bytes resentTo = 73;</code>
     * @return The count of resentTo.
     */
    public int getResentToCount() {
      return resentTo_.size();
    }
    /**
     * <code>repeated bytes resentTo = 73;</code>
     * @param index The index of the element to return.
     * @return The resentTo at the given index.
     */
    public com.google.protobuf.ByteString getResentTo(int index) {
      return resentTo_.get(index);
    }

    public static final int SENDEREMAIL_FIELD_NUMBER = 74;
    private com.google.protobuf.ByteString senderEmail_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes senderEmail = 74;</code>
     * @return Whether the senderEmail field is set.
     */
    @java.lang.Override
    public boolean hasSenderEmail() {
      return ((bitField1_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes senderEmail = 74;</code>
     * @return The senderEmail.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSenderEmail() {
      return senderEmail_;
    }

    public static final int SENDERALI_FIELD_NUMBER = 75;
    private com.google.protobuf.ByteString senderAli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes senderAli = 75;</code>
     * @return Whether the senderAli field is set.
     */
    @java.lang.Override
    public boolean hasSenderAli() {
      return ((bitField1_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes senderAli = 75;</code>
     * @return The senderAli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSenderAli() {
      return senderAli_;
    }

    public static final int SENDERDOM_FIELD_NUMBER = 76;
    private com.google.protobuf.ByteString senderDom_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes senderDom = 76;</code>
     * @return Whether the senderDom field is set.
     */
    @java.lang.Override
    public boolean hasSenderDom() {
      return ((bitField1_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes senderDom = 76;</code>
     * @return The senderDom.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSenderDom() {
      return senderDom_;
    }

    public static final int SMTPSRV_FIELD_NUMBER = 77;
    private com.google.protobuf.ByteString sMTPSrv_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes SMTPSrv = 77;</code>
     * @return Whether the sMTPSrv field is set.
     */
    @java.lang.Override
    public boolean hasSMTPSrv() {
      return ((bitField1_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes SMTPSrv = 77;</code>
     * @return The sMTPSrv.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSMTPSrv() {
      return sMTPSrv_;
    }

    public static final int SMTPSRVAGE_FIELD_NUMBER = 78;
    private com.google.protobuf.ByteString sMTPSrvAge_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes SMTPSrvAge = 78;</code>
     * @return Whether the sMTPSrvAge field is set.
     */
    @java.lang.Override
    public boolean hasSMTPSrvAge() {
      return ((bitField1_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes SMTPSrvAge = 78;</code>
     * @return The sMTPSrvAge.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSMTPSrvAge() {
      return sMTPSrvAge_;
    }

    public static final int RECEIVEDSPF_FIELD_NUMBER = 79;
    private com.google.protobuf.ByteString receivedSPF_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes receivedSPF = 79;</code>
     * @return Whether the receivedSPF field is set.
     */
    @java.lang.Override
    public boolean hasReceivedSPF() {
      return ((bitField1_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes receivedSPF = 79;</code>
     * @return The receivedSPF.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getReceivedSPF() {
      return receivedSPF_;
    }

    public static final int STARTTLS_FIELD_NUMBER = 80;
    private int startTLS_ = 0;
    /**
     * <code>optional uint32 startTLS = 80;</code>
     * @return Whether the startTLS field is set.
     */
    @java.lang.Override
    public boolean hasStartTLS() {
      return ((bitField1_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint32 startTLS = 80;</code>
     * @return The startTLS.
     */
    @java.lang.Override
    public int getStartTLS() {
      return startTLS_;
    }

    public static final int SUBJ_FIELD_NUMBER = 81;
    private com.google.protobuf.ByteString subj_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subj = 81;</code>
     * @return Whether the subj field is set.
     */
    @java.lang.Override
    public boolean hasSubj() {
      return ((bitField1_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes subj = 81;</code>
     * @return The subj.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSubj() {
      return subj_;
    }

    public static final int SUBJ_CNT_FIELD_NUMBER = 82;
    private int subjCnt_ = 0;
    /**
     * <code>optional uint32 subj_cnt = 82;</code>
     * @return Whether the subjCnt field is set.
     */
    @java.lang.Override
    public boolean hasSubjCnt() {
      return ((bitField1_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 subj_cnt = 82;</code>
     * @return The subjCnt.
     */
    @java.lang.Override
    public int getSubjCnt() {
      return subjCnt_;
    }

    public static final int USRAGE_FIELD_NUMBER = 83;
    private com.google.protobuf.ByteString usrAge_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes usrAge = 83;</code>
     * @return Whether the usrAge field is set.
     */
    @java.lang.Override
    public boolean hasUsrAge() {
      return ((bitField1_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes usrAge = 83;</code>
     * @return The usrAge.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUsrAge() {
      return usrAge_;
    }

    public static final int EMAILVERSION_FIELD_NUMBER = 84;
    private com.google.protobuf.ByteString emailVersion_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes emailVersion = 84;</code>
     * @return Whether the emailVersion field is set.
     */
    @java.lang.Override
    public boolean hasEmailVersion() {
      return ((bitField1_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes emailVersion = 84;</code>
     * @return The emailVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEmailVersion() {
      return emailVersion_;
    }

    public static final int RCVWIT_FIELD_NUMBER = 85;
    private com.google.protobuf.ByteString rcvWit_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes rcvWit = 85;</code>
     * @return Whether the rcvWit field is set.
     */
    @java.lang.Override
    public boolean hasRcvWit() {
      return ((bitField1_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes rcvWit = 85;</code>
     * @return The rcvWit.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRcvWit() {
      return rcvWit_;
    }

    public static final int XMAI_FIELD_NUMBER = 86;
    private com.google.protobuf.ByteString xMai_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes xMai = 86;</code>
     * @return Whether the xMai field is set.
     */
    @java.lang.Override
    public boolean hasXMai() {
      return ((bitField1_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes xMai = 86;</code>
     * @return The xMai.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getXMai() {
      return xMai_;
    }

    public static final int XMAICNT_FIELD_NUMBER = 87;
    private int xMaiCnt_ = 0;
    /**
     * <code>optional uint32 xMaiCnt = 87;</code>
     * @return Whether the xMaiCnt field is set.
     */
    @java.lang.Override
    public boolean hasXMaiCnt() {
      return ((bitField1_ & 0x04000000) != 0);
    }
    /**
     * <code>optional uint32 xMaiCnt = 87;</code>
     * @return The xMaiCnt.
     */
    @java.lang.Override
    public int getXMaiCnt() {
      return xMaiCnt_;
    }

    public static final int XORIIP_FIELD_NUMBER = 88;
    private int xOriIP_ = 0;
    /**
     * <code>optional uint32 xOriIP = 88;</code>
     * @return Whether the xOriIP field is set.
     */
    @java.lang.Override
    public boolean hasXOriIP() {
      return ((bitField1_ & 0x08000000) != 0);
    }
    /**
     * <code>optional uint32 xOriIP = 88;</code>
     * @return The xOriIP.
     */
    @java.lang.Override
    public int getXOriIP() {
      return xOriIP_;
    }

    public static final int SENDEREMAILCNT_FIELD_NUMBER = 89;
    private int senderEmailCnt_ = 0;
    /**
     * <code>optional uint32 senderEmailCnt = 89;</code>
     * @return Whether the senderEmailCnt field is set.
     */
    @java.lang.Override
    public boolean hasSenderEmailCnt() {
      return ((bitField1_ & 0x10000000) != 0);
    }
    /**
     * <code>optional uint32 senderEmailCnt = 89;</code>
     * @return The senderEmailCnt.
     */
    @java.lang.Override
    public int getSenderEmailCnt() {
      return senderEmailCnt_;
    }

    public static final int FW_FIELD_NUMBER = 90;
    private com.google.protobuf.ByteString fw_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes fw = 90;</code>
     * @return Whether the fw field is set.
     */
    @java.lang.Override
    public boolean hasFw() {
      return ((bitField1_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes fw = 90;</code>
     * @return The fw.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getFw() {
      return fw_;
    }

    public static final int BEFW_FIELD_NUMBER = 91;
    private com.google.protobuf.ByteString befw_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes befw = 91;</code>
     * @return Whether the befw field is set.
     */
    @java.lang.Override
    public boolean hasBefw() {
      return ((bitField1_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes befw = 91;</code>
     * @return The befw.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBefw() {
      return befw_;
    }

    public static final int RCVDATE_FIELD_NUMBER = 92;
    private long rcvDate_ = 0L;
    /**
     * <code>optional uint64 rcvDate = 92;</code>
     * @return Whether the rcvDate field is set.
     */
    @java.lang.Override
    public boolean hasRcvDate() {
      return ((bitField1_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint64 rcvDate = 92;</code>
     * @return The rcvDate.
     */
    @java.lang.Override
    public long getRcvDate() {
      return rcvDate_;
    }

    public static final int RCVSRVAGE_FIELD_NUMBER = 93;
    private com.google.protobuf.ByteString rcvSrvAge_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes rcvSrvAge = 93;</code>
     * @return Whether the rcvSrvAge field is set.
     */
    @java.lang.Override
    public boolean hasRcvSrvAge() {
      return ((bitField2_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes rcvSrvAge = 93;</code>
     * @return The rcvSrvAge.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRcvSrvAge() {
      return rcvSrvAge_;
    }

    public static final int CONTRAENC_FIELD_NUMBER = 94;
    private com.google.protobuf.ByteString conTraEnc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conTraEnc = 94;</code>
     * @return Whether the conTraEnc field is set.
     */
    @java.lang.Override
    public boolean hasConTraEnc() {
      return ((bitField2_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes conTraEnc = 94;</code>
     * @return The conTraEnc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConTraEnc() {
      return conTraEnc_;
    }

    public static final int CONTEXCHA_FIELD_NUMBER = 95;
    private com.google.protobuf.ByteString conTexCha_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes conTexCha = 95;</code>
     * @return Whether the conTexCha field is set.
     */
    @java.lang.Override
    public boolean hasConTexCha() {
      return ((bitField2_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes conTexCha = 95;</code>
     * @return The conTexCha.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getConTexCha() {
      return conTexCha_;
    }

    public static final int REALFROM_FIELD_NUMBER = 96;
    private com.google.protobuf.ByteString realFrom_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes realFrom = 96;</code>
     * @return Whether the realFrom field is set.
     */
    @java.lang.Override
    public boolean hasRealFrom() {
      return ((bitField2_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes realFrom = 96;</code>
     * @return The realFrom.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRealFrom() {
      return realFrom_;
    }

    public static final int REALTO_FIELD_NUMBER = 97;
    private com.google.protobuf.ByteString realTo_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes realTo = 97;</code>
     * @return Whether the realTo field is set.
     */
    @java.lang.Override
    public boolean hasRealTo() {
      return ((bitField2_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes realTo = 97;</code>
     * @return The realTo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRealTo() {
      return realTo_;
    }

    public static final int EMAACTREP_FIELD_NUMBER = 98;
    private com.google.protobuf.ByteString emaActRep_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes emaActRep = 98;</code>
     * @return Whether the emaActRep field is set.
     */
    @java.lang.Override
    public boolean hasEmaActRep() {
      return ((bitField2_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes emaActRep = 98;</code>
     * @return The emaActRep.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEmaActRep() {
      return emaActRep_;
    }

    public static final int RCVFROMNAME_FIELD_NUMBER = 99;
    private com.google.protobuf.ByteString rcvFromName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes rcvFromName = 99;</code>
     * @return Whether the rcvFromName field is set.
     */
    @java.lang.Override
    public boolean hasRcvFromName() {
      return ((bitField2_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes rcvFromName = 99;</code>
     * @return The rcvFromName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRcvFromName() {
      return rcvFromName_;
    }

    public static final int ERRTYPE_FIELD_NUMBER = 100;
    private com.google.protobuf.ByteString errType_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes errType = 100;</code>
     * @return Whether the errType field is set.
     */
    @java.lang.Override
    public boolean hasErrType() {
      return ((bitField2_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes errType = 100;</code>
     * @return The errType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getErrType() {
      return errType_;
    }

    public static final int CONTENTWITHHTML_FIELD_NUMBER = 101;
    private com.google.protobuf.ByteString contentWithHtml_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes contentWithHtml = 101;</code>
     * @return Whether the contentWithHtml field is set.
     */
    @java.lang.Override
    public boolean hasContentWithHtml() {
      return ((bitField2_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes contentWithHtml = 101;</code>
     * @return The contentWithHtml.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getContentWithHtml() {
      return contentWithHtml_;
    }

    public static final int CHARSET_FIELD_NUMBER = 102;
    private com.google.protobuf.ByteString charset_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes charset = 102;</code>
     * @return Whether the charset field is set.
     */
    @java.lang.Override
    public boolean hasCharset() {
      return ((bitField2_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes charset = 102;</code>
     * @return The charset.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCharset() {
      return charset_;
    }

    public static final int CONTENTLEN_FIELD_NUMBER = 103;
    private int contentLen_ = 0;
    /**
     * <code>optional uint32 contentLen = 103;</code>
     * @return Whether the contentLen field is set.
     */
    @java.lang.Override
    public boolean hasContentLen() {
      return ((bitField2_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint32 contentLen = 103;</code>
     * @return The contentLen.
     */
    @java.lang.Override
    public int getContentLen() {
      return contentLen_;
    }

    public static final int ISBEFW_FIELD_NUMBER = 104;
    private com.google.protobuf.ByteString isBeFw_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes isBeFw = 104;</code>
     * @return Whether the isBeFw field is set.
     */
    @java.lang.Override
    public boolean hasIsBeFw() {
      return ((bitField2_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes isBeFw = 104;</code>
     * @return The isBeFw.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIsBeFw() {
      return isBeFw_;
    }

    public static final int ATTACHMENTPATH_FIELD_NUMBER = 105;
    private com.google.protobuf.ByteString attachmentPath_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes attachmentPath = 105;</code>
     * @return Whether the attachmentPath field is set.
     */
    @java.lang.Override
    public boolean hasAttachmentPath() {
      return ((bitField2_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes attachmentPath = 105;</code>
     * @return The attachmentPath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAttachmentPath() {
      return attachmentPath_;
    }

    public static final int BANNER_FIELD_NUMBER = 106;
    private com.google.protobuf.ByteString banner_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes banner = 106;</code>
     * @return Whether the banner field is set.
     */
    @java.lang.Override
    public boolean hasBanner() {
      return ((bitField2_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes banner = 106;</code>
     * @return The banner.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getBanner() {
      return banner_;
    }

    public static final int SENDERSOFTWARE_FIELD_NUMBER = 107;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> senderSoftware_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes senderSoftware = 107;</code>
     * @return A list containing the senderSoftware.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getSenderSoftwareList() {
      return senderSoftware_;
    }
    /**
     * <code>repeated bytes senderSoftware = 107;</code>
     * @return The count of senderSoftware.
     */
    public int getSenderSoftwareCount() {
      return senderSoftware_.size();
    }
    /**
     * <code>repeated bytes senderSoftware = 107;</code>
     * @param index The index of the element to return.
     * @return The senderSoftware at the given index.
     */
    public com.google.protobuf.ByteString getSenderSoftware(int index) {
      return senderSoftware_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < attType_.size(); i++) {
        output.writeBytes(1, attType_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(2, attTypeCnt_);
      }
      for (int i = 0; i < attFileName_.size(); i++) {
        output.writeBytes(3, attFileName_.get(i));
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(4, attFileNameCnt_);
      }
      for (int i = 0; i < attConSize_.size(); i++) {
        output.writeUInt64(5, attConSize_.getLong(i));
      }
      for (int i = 0; i < attMD5_.size(); i++) {
        output.writeBytes(6, attMD5_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(7, attMD5Cnt_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(8, authRelt_);
      }
      for (int i = 0; i < bCC_.size(); i++) {
        output.writeBytes(9, bCC_.get(i));
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBytes(10, body_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(11, bodyTexCha_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeBytes(12, bodyTraEnc_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeBytes(13, bodyMD5_);
      }
      for (int i = 0; i < bodyType_.size(); i++) {
        output.writeBytes(14, bodyType_.get(i));
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeUInt32(15, bodyTypeCnt_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeBytes(16, bodyURL_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeUInt32(17, bodyURLCnt_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeUInt32(18, bodyLen_);
      }
      for (int i = 0; i < byAsn_.size(); i++) {
        output.writeBytes(19, byAsn_.get(i));
      }
      for (int i = 0; i < byCountry_.size(); i++) {
        output.writeBytes(20, byCountry_.get(i));
      }
      for (int i = 0; i < byDom_.size(); i++) {
        output.writeBytes(21, byDom_.get(i));
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt32(22, byDomCnt_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeBytes(23, byIP_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeUInt32(24, byIpCnt_);
      }
      for (int i = 0; i < cC_.size(); i++) {
        output.writeBytes(25, cC_.get(i));
      }
      for (int i = 0; i < cCAli_.size(); i++) {
        output.writeBytes(26, cCAli_.get(i));
      }
      for (int i = 0; i < command_.size(); i++) {
        output.writeBytes(27, command_.get(i));
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeUInt32(28, count_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(29, content_);
      }
      for (int i = 0; i < conType_.size(); i++) {
        output.writeBytes(30, conType_.get(i));
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeUInt32(31, conTypeCnt_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeUInt32(32, date_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeBytes(33, deliveredTo_);
      }
      for (int i = 0; i < fromAsn_.size(); i++) {
        output.writeBytes(34, fromAsn_.get(i));
      }
      for (int i = 0; i < fromCountry_.size(); i++) {
        output.writeBytes(35, fromCountry_.get(i));
      }
      for (int i = 0; i < fromDom_.size(); i++) {
        output.writeBytes(36, fromDom_.get(i));
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeUInt32(37, fromDomCnt_);
      }
      for (int i = 0; i < fromIp_.size(); i++) {
        output.writeUInt32(38, fromIp_.getInt(i));
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeUInt32(39, fromIpCnt_);
      }
      for (int i = 0; i < headSet_.size(); i++) {
        output.writeBytes(40, headSet_.get(i));
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeUInt32(41, headSetCnt_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeBytes(42, host_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeBytes(43, name_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeBytes(44, os_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeBytes(45, osVer_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeBytes(46, vendor_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeBytes(47, ver_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeBytes(48, emaInd_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeBytes(49, login_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeBytes(50, loginsrv_);
      }
      for (int i = 0; i < mailFrom_.size(); i++) {
        output.writeBytes(51, mailFrom_.get(i));
      }
      for (int i = 0; i < mailFromDom_.size(); i++) {
        output.writeBytes(52, mailFromDom_.get(i));
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeBytes(53, mailFromDomCnt_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeBytes(54, mimeVer_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        output.writeUInt32(55, mimeVerCnt_);
      }
      for (int i = 0; i < msgID_.size(); i++) {
        output.writeBytes(56, msgID_.get(i));
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        output.writeUInt32(57, msgIDCnt_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        output.writeBytes(58, emaProtType_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        output.writeBytes(59, pwd_);
      }
      for (int i = 0; i < rcptTo_.size(); i++) {
        output.writeBytes(60, rcptTo_.get(i));
      }
      for (int i = 0; i < rcptToDom_.size(); i++) {
        output.writeBytes(61, rcptToDom_.get(i));
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        output.writeUInt32(62, rcptToDomCnt_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        output.writeBytes(63, repTo_);
      }
      for (int i = 0; i < received_.size(); i++) {
        output.writeBytes(64, received_.get(i));
      }
      for (int i = 0; i < rcvrEmail_.size(); i++) {
        output.writeBytes(65, rcvrEmail_.get(i));
      }
      for (int i = 0; i < rcvrAli_.size(); i++) {
        output.writeBytes(66, rcvrAli_.get(i));
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        output.writeUInt32(67, rcvrAliCnt_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        output.writeUInt32(68, rcvrEmailCnt_);
      }
      for (int i = 0; i < rcvrDom_.size(); i++) {
        output.writeBytes(69, rcvrDom_.get(i));
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        output.writeBytes(70, resentSrvAge_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        output.writeUInt32(71, resentDate_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        output.writeBytes(72, resentFrom_);
      }
      for (int i = 0; i < resentTo_.size(); i++) {
        output.writeBytes(73, resentTo_.get(i));
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        output.writeBytes(74, senderEmail_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        output.writeBytes(75, senderAli_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        output.writeBytes(76, senderDom_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        output.writeBytes(77, sMTPSrv_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        output.writeBytes(78, sMTPSrvAge_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        output.writeBytes(79, receivedSPF_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        output.writeUInt32(80, startTLS_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        output.writeBytes(81, subj_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        output.writeUInt32(82, subjCnt_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        output.writeBytes(83, usrAge_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        output.writeBytes(84, emailVersion_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        output.writeBytes(85, rcvWit_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        output.writeBytes(86, xMai_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        output.writeUInt32(87, xMaiCnt_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        output.writeUInt32(88, xOriIP_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        output.writeUInt32(89, senderEmailCnt_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        output.writeBytes(90, fw_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        output.writeBytes(91, befw_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        output.writeUInt64(92, rcvDate_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        output.writeBytes(93, rcvSrvAge_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        output.writeBytes(94, conTraEnc_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        output.writeBytes(95, conTexCha_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        output.writeBytes(96, realFrom_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        output.writeBytes(97, realTo_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        output.writeBytes(98, emaActRep_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        output.writeBytes(99, rcvFromName_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        output.writeBytes(100, errType_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        output.writeBytes(101, contentWithHtml_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        output.writeBytes(102, charset_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        output.writeUInt32(103, contentLen_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        output.writeBytes(104, isBeFw_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        output.writeBytes(105, attachmentPath_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        output.writeBytes(106, banner_);
      }
      for (int i = 0; i < senderSoftware_.size(); i++) {
        output.writeBytes(107, senderSoftware_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < attType_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(attType_.get(i));
        }
        size += dataSize;
        size += 1 * getAttTypeList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, attTypeCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < attFileName_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(attFileName_.get(i));
        }
        size += dataSize;
        size += 1 * getAttFileNameList().size();
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, attFileNameCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < attConSize_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(attConSize_.getLong(i));
        }
        size += dataSize;
        size += 1 * getAttConSizeList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < attMD5_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(attMD5_.get(i));
        }
        size += dataSize;
        size += 1 * getAttMD5List().size();
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, attMD5Cnt_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, authRelt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < bCC_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(bCC_.get(i));
        }
        size += dataSize;
        size += 1 * getBCCList().size();
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, body_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, bodyTexCha_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, bodyTraEnc_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(13, bodyMD5_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < bodyType_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(bodyType_.get(i));
        }
        size += dataSize;
        size += 1 * getBodyTypeList().size();
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, bodyTypeCnt_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, bodyURL_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(17, bodyURLCnt_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, bodyLen_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < byAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(byAsn_.get(i));
        }
        size += dataSize;
        size += 2 * getByAsnList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < byCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(byCountry_.get(i));
        }
        size += dataSize;
        size += 2 * getByCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < byDom_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(byDom_.get(i));
        }
        size += dataSize;
        size += 2 * getByDomList().size();
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, byDomCnt_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, byIP_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(24, byIpCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < cC_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(cC_.get(i));
        }
        size += dataSize;
        size += 2 * getCCList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < cCAli_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(cCAli_.get(i));
        }
        size += dataSize;
        size += 2 * getCCAliList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < command_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(command_.get(i));
        }
        size += dataSize;
        size += 2 * getCommandList().size();
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, count_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, content_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < conType_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(conType_.get(i));
        }
        size += dataSize;
        size += 2 * getConTypeList().size();
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(31, conTypeCnt_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(32, date_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(33, deliveredTo_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < fromAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(fromAsn_.get(i));
        }
        size += dataSize;
        size += 2 * getFromAsnList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < fromCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(fromCountry_.get(i));
        }
        size += dataSize;
        size += 2 * getFromCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < fromDom_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(fromDom_.get(i));
        }
        size += dataSize;
        size += 2 * getFromDomList().size();
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(37, fromDomCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < fromIp_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(fromIp_.getInt(i));
        }
        size += dataSize;
        size += 2 * getFromIpList().size();
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(39, fromIpCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < headSet_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(headSet_.get(i));
        }
        size += dataSize;
        size += 2 * getHeadSetList().size();
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(41, headSetCnt_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(42, host_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(43, name_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(44, os_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, osVer_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, vendor_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(47, ver_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(48, emaInd_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(49, login_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(50, loginsrv_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mailFrom_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mailFrom_.get(i));
        }
        size += dataSize;
        size += 2 * getMailFromList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mailFromDom_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mailFromDom_.get(i));
        }
        size += dataSize;
        size += 2 * getMailFromDomList().size();
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, mailFromDomCnt_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(54, mimeVer_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(55, mimeVerCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < msgID_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(msgID_.get(i));
        }
        size += dataSize;
        size += 2 * getMsgIDList().size();
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(57, msgIDCnt_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, emaProtType_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(59, pwd_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rcptTo_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(rcptTo_.get(i));
        }
        size += dataSize;
        size += 2 * getRcptToList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rcptToDom_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(rcptToDom_.get(i));
        }
        size += dataSize;
        size += 2 * getRcptToDomList().size();
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(62, rcptToDomCnt_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(63, repTo_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < received_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(received_.get(i));
        }
        size += dataSize;
        size += 2 * getReceivedList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rcvrEmail_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(rcvrEmail_.get(i));
        }
        size += dataSize;
        size += 2 * getRcvrEmailList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rcvrAli_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(rcvrAli_.get(i));
        }
        size += dataSize;
        size += 2 * getRcvrAliList().size();
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(67, rcvrAliCnt_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(68, rcvrEmailCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rcvrDom_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(rcvrDom_.get(i));
        }
        size += dataSize;
        size += 2 * getRcvrDomList().size();
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(70, resentSrvAge_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(71, resentDate_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(72, resentFrom_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < resentTo_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(resentTo_.get(i));
        }
        size += dataSize;
        size += 2 * getResentToList().size();
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(74, senderEmail_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(75, senderAli_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(76, senderDom_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(77, sMTPSrv_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(78, sMTPSrvAge_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(79, receivedSPF_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(80, startTLS_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(81, subj_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(82, subjCnt_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(83, usrAge_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(84, emailVersion_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(85, rcvWit_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(86, xMai_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(87, xMaiCnt_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(88, xOriIP_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(89, senderEmailCnt_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(90, fw_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(91, befw_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(92, rcvDate_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(93, rcvSrvAge_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(94, conTraEnc_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(95, conTexCha_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(96, realFrom_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(97, realTo_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(98, emaActRep_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(99, rcvFromName_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(100, errType_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(101, contentWithHtml_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(102, charset_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(103, contentLen_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(104, isBeFw_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(105, attachmentPath_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(106, banner_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < senderSoftware_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(senderSoftware_.get(i));
        }
        size += dataSize;
        size += 2 * getSenderSoftwareList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof EMAILInfoOuterClass.EMAILInfo)) {
        return super.equals(obj);
      }
      EMAILInfoOuterClass.EMAILInfo other = (EMAILInfoOuterClass.EMAILInfo) obj;

      if (!getAttTypeList()
          .equals(other.getAttTypeList())) return false;
      if (hasAttTypeCnt() != other.hasAttTypeCnt()) return false;
      if (hasAttTypeCnt()) {
        if (getAttTypeCnt()
            != other.getAttTypeCnt()) return false;
      }
      if (!getAttFileNameList()
          .equals(other.getAttFileNameList())) return false;
      if (hasAttFileNameCnt() != other.hasAttFileNameCnt()) return false;
      if (hasAttFileNameCnt()) {
        if (getAttFileNameCnt()
            != other.getAttFileNameCnt()) return false;
      }
      if (!getAttConSizeList()
          .equals(other.getAttConSizeList())) return false;
      if (!getAttMD5List()
          .equals(other.getAttMD5List())) return false;
      if (hasAttMD5Cnt() != other.hasAttMD5Cnt()) return false;
      if (hasAttMD5Cnt()) {
        if (getAttMD5Cnt()
            != other.getAttMD5Cnt()) return false;
      }
      if (hasAuthRelt() != other.hasAuthRelt()) return false;
      if (hasAuthRelt()) {
        if (getAuthRelt()
            != other.getAuthRelt()) return false;
      }
      if (!getBCCList()
          .equals(other.getBCCList())) return false;
      if (hasBody() != other.hasBody()) return false;
      if (hasBody()) {
        if (!getBody()
            .equals(other.getBody())) return false;
      }
      if (hasBodyTexCha() != other.hasBodyTexCha()) return false;
      if (hasBodyTexCha()) {
        if (!getBodyTexCha()
            .equals(other.getBodyTexCha())) return false;
      }
      if (hasBodyTraEnc() != other.hasBodyTraEnc()) return false;
      if (hasBodyTraEnc()) {
        if (!getBodyTraEnc()
            .equals(other.getBodyTraEnc())) return false;
      }
      if (hasBodyMD5() != other.hasBodyMD5()) return false;
      if (hasBodyMD5()) {
        if (!getBodyMD5()
            .equals(other.getBodyMD5())) return false;
      }
      if (!getBodyTypeList()
          .equals(other.getBodyTypeList())) return false;
      if (hasBodyTypeCnt() != other.hasBodyTypeCnt()) return false;
      if (hasBodyTypeCnt()) {
        if (getBodyTypeCnt()
            != other.getBodyTypeCnt()) return false;
      }
      if (hasBodyURL() != other.hasBodyURL()) return false;
      if (hasBodyURL()) {
        if (!getBodyURL()
            .equals(other.getBodyURL())) return false;
      }
      if (hasBodyURLCnt() != other.hasBodyURLCnt()) return false;
      if (hasBodyURLCnt()) {
        if (getBodyURLCnt()
            != other.getBodyURLCnt()) return false;
      }
      if (hasBodyLen() != other.hasBodyLen()) return false;
      if (hasBodyLen()) {
        if (getBodyLen()
            != other.getBodyLen()) return false;
      }
      if (!getByAsnList()
          .equals(other.getByAsnList())) return false;
      if (!getByCountryList()
          .equals(other.getByCountryList())) return false;
      if (!getByDomList()
          .equals(other.getByDomList())) return false;
      if (hasByDomCnt() != other.hasByDomCnt()) return false;
      if (hasByDomCnt()) {
        if (getByDomCnt()
            != other.getByDomCnt()) return false;
      }
      if (hasByIP() != other.hasByIP()) return false;
      if (hasByIP()) {
        if (!getByIP()
            .equals(other.getByIP())) return false;
      }
      if (hasByIpCnt() != other.hasByIpCnt()) return false;
      if (hasByIpCnt()) {
        if (getByIpCnt()
            != other.getByIpCnt()) return false;
      }
      if (!getCCList()
          .equals(other.getCCList())) return false;
      if (!getCCAliList()
          .equals(other.getCCAliList())) return false;
      if (!getCommandList()
          .equals(other.getCommandList())) return false;
      if (hasCount() != other.hasCount()) return false;
      if (hasCount()) {
        if (getCount()
            != other.getCount()) return false;
      }
      if (hasContent() != other.hasContent()) return false;
      if (hasContent()) {
        if (!getContent()
            .equals(other.getContent())) return false;
      }
      if (!getConTypeList()
          .equals(other.getConTypeList())) return false;
      if (hasConTypeCnt() != other.hasConTypeCnt()) return false;
      if (hasConTypeCnt()) {
        if (getConTypeCnt()
            != other.getConTypeCnt()) return false;
      }
      if (hasDate() != other.hasDate()) return false;
      if (hasDate()) {
        if (getDate()
            != other.getDate()) return false;
      }
      if (hasDeliveredTo() != other.hasDeliveredTo()) return false;
      if (hasDeliveredTo()) {
        if (!getDeliveredTo()
            .equals(other.getDeliveredTo())) return false;
      }
      if (!getFromAsnList()
          .equals(other.getFromAsnList())) return false;
      if (!getFromCountryList()
          .equals(other.getFromCountryList())) return false;
      if (!getFromDomList()
          .equals(other.getFromDomList())) return false;
      if (hasFromDomCnt() != other.hasFromDomCnt()) return false;
      if (hasFromDomCnt()) {
        if (getFromDomCnt()
            != other.getFromDomCnt()) return false;
      }
      if (!getFromIpList()
          .equals(other.getFromIpList())) return false;
      if (hasFromIpCnt() != other.hasFromIpCnt()) return false;
      if (hasFromIpCnt()) {
        if (getFromIpCnt()
            != other.getFromIpCnt()) return false;
      }
      if (!getHeadSetList()
          .equals(other.getHeadSetList())) return false;
      if (hasHeadSetCnt() != other.hasHeadSetCnt()) return false;
      if (hasHeadSetCnt()) {
        if (getHeadSetCnt()
            != other.getHeadSetCnt()) return false;
      }
      if (hasHost() != other.hasHost()) return false;
      if (hasHost()) {
        if (!getHost()
            .equals(other.getHost())) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (hasOs() != other.hasOs()) return false;
      if (hasOs()) {
        if (!getOs()
            .equals(other.getOs())) return false;
      }
      if (hasOsVer() != other.hasOsVer()) return false;
      if (hasOsVer()) {
        if (!getOsVer()
            .equals(other.getOsVer())) return false;
      }
      if (hasVendor() != other.hasVendor()) return false;
      if (hasVendor()) {
        if (!getVendor()
            .equals(other.getVendor())) return false;
      }
      if (hasVer() != other.hasVer()) return false;
      if (hasVer()) {
        if (!getVer()
            .equals(other.getVer())) return false;
      }
      if (hasEmaInd() != other.hasEmaInd()) return false;
      if (hasEmaInd()) {
        if (!getEmaInd()
            .equals(other.getEmaInd())) return false;
      }
      if (hasLogin() != other.hasLogin()) return false;
      if (hasLogin()) {
        if (!getLogin()
            .equals(other.getLogin())) return false;
      }
      if (hasLoginsrv() != other.hasLoginsrv()) return false;
      if (hasLoginsrv()) {
        if (!getLoginsrv()
            .equals(other.getLoginsrv())) return false;
      }
      if (!getMailFromList()
          .equals(other.getMailFromList())) return false;
      if (!getMailFromDomList()
          .equals(other.getMailFromDomList())) return false;
      if (hasMailFromDomCnt() != other.hasMailFromDomCnt()) return false;
      if (hasMailFromDomCnt()) {
        if (!getMailFromDomCnt()
            .equals(other.getMailFromDomCnt())) return false;
      }
      if (hasMimeVer() != other.hasMimeVer()) return false;
      if (hasMimeVer()) {
        if (!getMimeVer()
            .equals(other.getMimeVer())) return false;
      }
      if (hasMimeVerCnt() != other.hasMimeVerCnt()) return false;
      if (hasMimeVerCnt()) {
        if (getMimeVerCnt()
            != other.getMimeVerCnt()) return false;
      }
      if (!getMsgIDList()
          .equals(other.getMsgIDList())) return false;
      if (hasMsgIDCnt() != other.hasMsgIDCnt()) return false;
      if (hasMsgIDCnt()) {
        if (getMsgIDCnt()
            != other.getMsgIDCnt()) return false;
      }
      if (hasEmaProtType() != other.hasEmaProtType()) return false;
      if (hasEmaProtType()) {
        if (!getEmaProtType()
            .equals(other.getEmaProtType())) return false;
      }
      if (hasPwd() != other.hasPwd()) return false;
      if (hasPwd()) {
        if (!getPwd()
            .equals(other.getPwd())) return false;
      }
      if (!getRcptToList()
          .equals(other.getRcptToList())) return false;
      if (!getRcptToDomList()
          .equals(other.getRcptToDomList())) return false;
      if (hasRcptToDomCnt() != other.hasRcptToDomCnt()) return false;
      if (hasRcptToDomCnt()) {
        if (getRcptToDomCnt()
            != other.getRcptToDomCnt()) return false;
      }
      if (hasRepTo() != other.hasRepTo()) return false;
      if (hasRepTo()) {
        if (!getRepTo()
            .equals(other.getRepTo())) return false;
      }
      if (!getReceivedList()
          .equals(other.getReceivedList())) return false;
      if (!getRcvrEmailList()
          .equals(other.getRcvrEmailList())) return false;
      if (!getRcvrAliList()
          .equals(other.getRcvrAliList())) return false;
      if (hasRcvrAliCnt() != other.hasRcvrAliCnt()) return false;
      if (hasRcvrAliCnt()) {
        if (getRcvrAliCnt()
            != other.getRcvrAliCnt()) return false;
      }
      if (hasRcvrEmailCnt() != other.hasRcvrEmailCnt()) return false;
      if (hasRcvrEmailCnt()) {
        if (getRcvrEmailCnt()
            != other.getRcvrEmailCnt()) return false;
      }
      if (!getRcvrDomList()
          .equals(other.getRcvrDomList())) return false;
      if (hasResentSrvAge() != other.hasResentSrvAge()) return false;
      if (hasResentSrvAge()) {
        if (!getResentSrvAge()
            .equals(other.getResentSrvAge())) return false;
      }
      if (hasResentDate() != other.hasResentDate()) return false;
      if (hasResentDate()) {
        if (getResentDate()
            != other.getResentDate()) return false;
      }
      if (hasResentFrom() != other.hasResentFrom()) return false;
      if (hasResentFrom()) {
        if (!getResentFrom()
            .equals(other.getResentFrom())) return false;
      }
      if (!getResentToList()
          .equals(other.getResentToList())) return false;
      if (hasSenderEmail() != other.hasSenderEmail()) return false;
      if (hasSenderEmail()) {
        if (!getSenderEmail()
            .equals(other.getSenderEmail())) return false;
      }
      if (hasSenderAli() != other.hasSenderAli()) return false;
      if (hasSenderAli()) {
        if (!getSenderAli()
            .equals(other.getSenderAli())) return false;
      }
      if (hasSenderDom() != other.hasSenderDom()) return false;
      if (hasSenderDom()) {
        if (!getSenderDom()
            .equals(other.getSenderDom())) return false;
      }
      if (hasSMTPSrv() != other.hasSMTPSrv()) return false;
      if (hasSMTPSrv()) {
        if (!getSMTPSrv()
            .equals(other.getSMTPSrv())) return false;
      }
      if (hasSMTPSrvAge() != other.hasSMTPSrvAge()) return false;
      if (hasSMTPSrvAge()) {
        if (!getSMTPSrvAge()
            .equals(other.getSMTPSrvAge())) return false;
      }
      if (hasReceivedSPF() != other.hasReceivedSPF()) return false;
      if (hasReceivedSPF()) {
        if (!getReceivedSPF()
            .equals(other.getReceivedSPF())) return false;
      }
      if (hasStartTLS() != other.hasStartTLS()) return false;
      if (hasStartTLS()) {
        if (getStartTLS()
            != other.getStartTLS()) return false;
      }
      if (hasSubj() != other.hasSubj()) return false;
      if (hasSubj()) {
        if (!getSubj()
            .equals(other.getSubj())) return false;
      }
      if (hasSubjCnt() != other.hasSubjCnt()) return false;
      if (hasSubjCnt()) {
        if (getSubjCnt()
            != other.getSubjCnt()) return false;
      }
      if (hasUsrAge() != other.hasUsrAge()) return false;
      if (hasUsrAge()) {
        if (!getUsrAge()
            .equals(other.getUsrAge())) return false;
      }
      if (hasEmailVersion() != other.hasEmailVersion()) return false;
      if (hasEmailVersion()) {
        if (!getEmailVersion()
            .equals(other.getEmailVersion())) return false;
      }
      if (hasRcvWit() != other.hasRcvWit()) return false;
      if (hasRcvWit()) {
        if (!getRcvWit()
            .equals(other.getRcvWit())) return false;
      }
      if (hasXMai() != other.hasXMai()) return false;
      if (hasXMai()) {
        if (!getXMai()
            .equals(other.getXMai())) return false;
      }
      if (hasXMaiCnt() != other.hasXMaiCnt()) return false;
      if (hasXMaiCnt()) {
        if (getXMaiCnt()
            != other.getXMaiCnt()) return false;
      }
      if (hasXOriIP() != other.hasXOriIP()) return false;
      if (hasXOriIP()) {
        if (getXOriIP()
            != other.getXOriIP()) return false;
      }
      if (hasSenderEmailCnt() != other.hasSenderEmailCnt()) return false;
      if (hasSenderEmailCnt()) {
        if (getSenderEmailCnt()
            != other.getSenderEmailCnt()) return false;
      }
      if (hasFw() != other.hasFw()) return false;
      if (hasFw()) {
        if (!getFw()
            .equals(other.getFw())) return false;
      }
      if (hasBefw() != other.hasBefw()) return false;
      if (hasBefw()) {
        if (!getBefw()
            .equals(other.getBefw())) return false;
      }
      if (hasRcvDate() != other.hasRcvDate()) return false;
      if (hasRcvDate()) {
        if (getRcvDate()
            != other.getRcvDate()) return false;
      }
      if (hasRcvSrvAge() != other.hasRcvSrvAge()) return false;
      if (hasRcvSrvAge()) {
        if (!getRcvSrvAge()
            .equals(other.getRcvSrvAge())) return false;
      }
      if (hasConTraEnc() != other.hasConTraEnc()) return false;
      if (hasConTraEnc()) {
        if (!getConTraEnc()
            .equals(other.getConTraEnc())) return false;
      }
      if (hasConTexCha() != other.hasConTexCha()) return false;
      if (hasConTexCha()) {
        if (!getConTexCha()
            .equals(other.getConTexCha())) return false;
      }
      if (hasRealFrom() != other.hasRealFrom()) return false;
      if (hasRealFrom()) {
        if (!getRealFrom()
            .equals(other.getRealFrom())) return false;
      }
      if (hasRealTo() != other.hasRealTo()) return false;
      if (hasRealTo()) {
        if (!getRealTo()
            .equals(other.getRealTo())) return false;
      }
      if (hasEmaActRep() != other.hasEmaActRep()) return false;
      if (hasEmaActRep()) {
        if (!getEmaActRep()
            .equals(other.getEmaActRep())) return false;
      }
      if (hasRcvFromName() != other.hasRcvFromName()) return false;
      if (hasRcvFromName()) {
        if (!getRcvFromName()
            .equals(other.getRcvFromName())) return false;
      }
      if (hasErrType() != other.hasErrType()) return false;
      if (hasErrType()) {
        if (!getErrType()
            .equals(other.getErrType())) return false;
      }
      if (hasContentWithHtml() != other.hasContentWithHtml()) return false;
      if (hasContentWithHtml()) {
        if (!getContentWithHtml()
            .equals(other.getContentWithHtml())) return false;
      }
      if (hasCharset() != other.hasCharset()) return false;
      if (hasCharset()) {
        if (!getCharset()
            .equals(other.getCharset())) return false;
      }
      if (hasContentLen() != other.hasContentLen()) return false;
      if (hasContentLen()) {
        if (getContentLen()
            != other.getContentLen()) return false;
      }
      if (hasIsBeFw() != other.hasIsBeFw()) return false;
      if (hasIsBeFw()) {
        if (!getIsBeFw()
            .equals(other.getIsBeFw())) return false;
      }
      if (hasAttachmentPath() != other.hasAttachmentPath()) return false;
      if (hasAttachmentPath()) {
        if (!getAttachmentPath()
            .equals(other.getAttachmentPath())) return false;
      }
      if (hasBanner() != other.hasBanner()) return false;
      if (hasBanner()) {
        if (!getBanner()
            .equals(other.getBanner())) return false;
      }
      if (!getSenderSoftwareList()
          .equals(other.getSenderSoftwareList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getAttTypeCount() > 0) {
        hash = (37 * hash) + ATTTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getAttTypeList().hashCode();
      }
      if (hasAttTypeCnt()) {
        hash = (37 * hash) + ATTTYPECNT_FIELD_NUMBER;
        hash = (53 * hash) + getAttTypeCnt();
      }
      if (getAttFileNameCount() > 0) {
        hash = (37 * hash) + ATTFILENAME_FIELD_NUMBER;
        hash = (53 * hash) + getAttFileNameList().hashCode();
      }
      if (hasAttFileNameCnt()) {
        hash = (37 * hash) + ATTFILENAMECNT_FIELD_NUMBER;
        hash = (53 * hash) + getAttFileNameCnt();
      }
      if (getAttConSizeCount() > 0) {
        hash = (37 * hash) + ATTCONSIZE_FIELD_NUMBER;
        hash = (53 * hash) + getAttConSizeList().hashCode();
      }
      if (getAttMD5Count() > 0) {
        hash = (37 * hash) + ATTMD5_FIELD_NUMBER;
        hash = (53 * hash) + getAttMD5List().hashCode();
      }
      if (hasAttMD5Cnt()) {
        hash = (37 * hash) + ATTMD5CNT_FIELD_NUMBER;
        hash = (53 * hash) + getAttMD5Cnt();
      }
      if (hasAuthRelt()) {
        hash = (37 * hash) + AUTHRELT_FIELD_NUMBER;
        hash = (53 * hash) + getAuthRelt();
      }
      if (getBCCCount() > 0) {
        hash = (37 * hash) + BCC_FIELD_NUMBER;
        hash = (53 * hash) + getBCCList().hashCode();
      }
      if (hasBody()) {
        hash = (37 * hash) + BODY_FIELD_NUMBER;
        hash = (53 * hash) + getBody().hashCode();
      }
      if (hasBodyTexCha()) {
        hash = (37 * hash) + BODYTEXCHA_FIELD_NUMBER;
        hash = (53 * hash) + getBodyTexCha().hashCode();
      }
      if (hasBodyTraEnc()) {
        hash = (37 * hash) + BODYTRAENC_FIELD_NUMBER;
        hash = (53 * hash) + getBodyTraEnc().hashCode();
      }
      if (hasBodyMD5()) {
        hash = (37 * hash) + BODYMD5_FIELD_NUMBER;
        hash = (53 * hash) + getBodyMD5().hashCode();
      }
      if (getBodyTypeCount() > 0) {
        hash = (37 * hash) + BODYTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getBodyTypeList().hashCode();
      }
      if (hasBodyTypeCnt()) {
        hash = (37 * hash) + BODYTYPECNT_FIELD_NUMBER;
        hash = (53 * hash) + getBodyTypeCnt();
      }
      if (hasBodyURL()) {
        hash = (37 * hash) + BODYURL_FIELD_NUMBER;
        hash = (53 * hash) + getBodyURL().hashCode();
      }
      if (hasBodyURLCnt()) {
        hash = (37 * hash) + BODYURLCNT_FIELD_NUMBER;
        hash = (53 * hash) + getBodyURLCnt();
      }
      if (hasBodyLen()) {
        hash = (37 * hash) + BODYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getBodyLen();
      }
      if (getByAsnCount() > 0) {
        hash = (37 * hash) + BYASN_FIELD_NUMBER;
        hash = (53 * hash) + getByAsnList().hashCode();
      }
      if (getByCountryCount() > 0) {
        hash = (37 * hash) + BYCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getByCountryList().hashCode();
      }
      if (getByDomCount() > 0) {
        hash = (37 * hash) + BYDOM_FIELD_NUMBER;
        hash = (53 * hash) + getByDomList().hashCode();
      }
      if (hasByDomCnt()) {
        hash = (37 * hash) + BYDOMCNT_FIELD_NUMBER;
        hash = (53 * hash) + getByDomCnt();
      }
      if (hasByIP()) {
        hash = (37 * hash) + BYIP_FIELD_NUMBER;
        hash = (53 * hash) + getByIP().hashCode();
      }
      if (hasByIpCnt()) {
        hash = (37 * hash) + BYIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getByIpCnt();
      }
      if (getCCCount() > 0) {
        hash = (37 * hash) + CC_FIELD_NUMBER;
        hash = (53 * hash) + getCCList().hashCode();
      }
      if (getCCAliCount() > 0) {
        hash = (37 * hash) + CCALI_FIELD_NUMBER;
        hash = (53 * hash) + getCCAliList().hashCode();
      }
      if (getCommandCount() > 0) {
        hash = (37 * hash) + COMMAND_FIELD_NUMBER;
        hash = (53 * hash) + getCommandList().hashCode();
      }
      if (hasCount()) {
        hash = (37 * hash) + COUNT_FIELD_NUMBER;
        hash = (53 * hash) + getCount();
      }
      if (hasContent()) {
        hash = (37 * hash) + CONTENT_FIELD_NUMBER;
        hash = (53 * hash) + getContent().hashCode();
      }
      if (getConTypeCount() > 0) {
        hash = (37 * hash) + CONTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getConTypeList().hashCode();
      }
      if (hasConTypeCnt()) {
        hash = (37 * hash) + CONTYPECNT_FIELD_NUMBER;
        hash = (53 * hash) + getConTypeCnt();
      }
      if (hasDate()) {
        hash = (37 * hash) + DATE_FIELD_NUMBER;
        hash = (53 * hash) + getDate();
      }
      if (hasDeliveredTo()) {
        hash = (37 * hash) + DELIVEREDTO_FIELD_NUMBER;
        hash = (53 * hash) + getDeliveredTo().hashCode();
      }
      if (getFromAsnCount() > 0) {
        hash = (37 * hash) + FROMASN_FIELD_NUMBER;
        hash = (53 * hash) + getFromAsnList().hashCode();
      }
      if (getFromCountryCount() > 0) {
        hash = (37 * hash) + FROMCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getFromCountryList().hashCode();
      }
      if (getFromDomCount() > 0) {
        hash = (37 * hash) + FROMDOM_FIELD_NUMBER;
        hash = (53 * hash) + getFromDomList().hashCode();
      }
      if (hasFromDomCnt()) {
        hash = (37 * hash) + FROMDOMCNT_FIELD_NUMBER;
        hash = (53 * hash) + getFromDomCnt();
      }
      if (getFromIpCount() > 0) {
        hash = (37 * hash) + FROMIP_FIELD_NUMBER;
        hash = (53 * hash) + getFromIpList().hashCode();
      }
      if (hasFromIpCnt()) {
        hash = (37 * hash) + FROMIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getFromIpCnt();
      }
      if (getHeadSetCount() > 0) {
        hash = (37 * hash) + HEADSET_FIELD_NUMBER;
        hash = (53 * hash) + getHeadSetList().hashCode();
      }
      if (hasHeadSetCnt()) {
        hash = (37 * hash) + HEADSETCNT_FIELD_NUMBER;
        hash = (53 * hash) + getHeadSetCnt();
      }
      if (hasHost()) {
        hash = (37 * hash) + HOST_FIELD_NUMBER;
        hash = (53 * hash) + getHost().hashCode();
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      if (hasOs()) {
        hash = (37 * hash) + OS_FIELD_NUMBER;
        hash = (53 * hash) + getOs().hashCode();
      }
      if (hasOsVer()) {
        hash = (37 * hash) + OSVER_FIELD_NUMBER;
        hash = (53 * hash) + getOsVer().hashCode();
      }
      if (hasVendor()) {
        hash = (37 * hash) + VENDOR_FIELD_NUMBER;
        hash = (53 * hash) + getVendor().hashCode();
      }
      if (hasVer()) {
        hash = (37 * hash) + VER_FIELD_NUMBER;
        hash = (53 * hash) + getVer().hashCode();
      }
      if (hasEmaInd()) {
        hash = (37 * hash) + EMAIND_FIELD_NUMBER;
        hash = (53 * hash) + getEmaInd().hashCode();
      }
      if (hasLogin()) {
        hash = (37 * hash) + LOGIN_FIELD_NUMBER;
        hash = (53 * hash) + getLogin().hashCode();
      }
      if (hasLoginsrv()) {
        hash = (37 * hash) + LOGINSRV_FIELD_NUMBER;
        hash = (53 * hash) + getLoginsrv().hashCode();
      }
      if (getMailFromCount() > 0) {
        hash = (37 * hash) + MAILFROM_FIELD_NUMBER;
        hash = (53 * hash) + getMailFromList().hashCode();
      }
      if (getMailFromDomCount() > 0) {
        hash = (37 * hash) + MAILFROMDOM_FIELD_NUMBER;
        hash = (53 * hash) + getMailFromDomList().hashCode();
      }
      if (hasMailFromDomCnt()) {
        hash = (37 * hash) + MAILFROMDOMCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMailFromDomCnt().hashCode();
      }
      if (hasMimeVer()) {
        hash = (37 * hash) + MIMEVER_FIELD_NUMBER;
        hash = (53 * hash) + getMimeVer().hashCode();
      }
      if (hasMimeVerCnt()) {
        hash = (37 * hash) + MIMEVERCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMimeVerCnt();
      }
      if (getMsgIDCount() > 0) {
        hash = (37 * hash) + MSGID_FIELD_NUMBER;
        hash = (53 * hash) + getMsgIDList().hashCode();
      }
      if (hasMsgIDCnt()) {
        hash = (37 * hash) + MSGIDCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMsgIDCnt();
      }
      if (hasEmaProtType()) {
        hash = (37 * hash) + EMAPROTTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getEmaProtType().hashCode();
      }
      if (hasPwd()) {
        hash = (37 * hash) + PWD_FIELD_NUMBER;
        hash = (53 * hash) + getPwd().hashCode();
      }
      if (getRcptToCount() > 0) {
        hash = (37 * hash) + RCPTTO_FIELD_NUMBER;
        hash = (53 * hash) + getRcptToList().hashCode();
      }
      if (getRcptToDomCount() > 0) {
        hash = (37 * hash) + RCPTTODOM_FIELD_NUMBER;
        hash = (53 * hash) + getRcptToDomList().hashCode();
      }
      if (hasRcptToDomCnt()) {
        hash = (37 * hash) + RCPTTODOMCNT_FIELD_NUMBER;
        hash = (53 * hash) + getRcptToDomCnt();
      }
      if (hasRepTo()) {
        hash = (37 * hash) + REPTO_FIELD_NUMBER;
        hash = (53 * hash) + getRepTo().hashCode();
      }
      if (getReceivedCount() > 0) {
        hash = (37 * hash) + RECEIVED_FIELD_NUMBER;
        hash = (53 * hash) + getReceivedList().hashCode();
      }
      if (getRcvrEmailCount() > 0) {
        hash = (37 * hash) + RCVREMAIL_FIELD_NUMBER;
        hash = (53 * hash) + getRcvrEmailList().hashCode();
      }
      if (getRcvrAliCount() > 0) {
        hash = (37 * hash) + RCVRALI_FIELD_NUMBER;
        hash = (53 * hash) + getRcvrAliList().hashCode();
      }
      if (hasRcvrAliCnt()) {
        hash = (37 * hash) + RCVRALICNT_FIELD_NUMBER;
        hash = (53 * hash) + getRcvrAliCnt();
      }
      if (hasRcvrEmailCnt()) {
        hash = (37 * hash) + RCVREMAILCNT_FIELD_NUMBER;
        hash = (53 * hash) + getRcvrEmailCnt();
      }
      if (getRcvrDomCount() > 0) {
        hash = (37 * hash) + RCVRDOM_FIELD_NUMBER;
        hash = (53 * hash) + getRcvrDomList().hashCode();
      }
      if (hasResentSrvAge()) {
        hash = (37 * hash) + RESENTSRVAGE_FIELD_NUMBER;
        hash = (53 * hash) + getResentSrvAge().hashCode();
      }
      if (hasResentDate()) {
        hash = (37 * hash) + RESENTDATE_FIELD_NUMBER;
        hash = (53 * hash) + getResentDate();
      }
      if (hasResentFrom()) {
        hash = (37 * hash) + RESENTFROM_FIELD_NUMBER;
        hash = (53 * hash) + getResentFrom().hashCode();
      }
      if (getResentToCount() > 0) {
        hash = (37 * hash) + RESENTTO_FIELD_NUMBER;
        hash = (53 * hash) + getResentToList().hashCode();
      }
      if (hasSenderEmail()) {
        hash = (37 * hash) + SENDEREMAIL_FIELD_NUMBER;
        hash = (53 * hash) + getSenderEmail().hashCode();
      }
      if (hasSenderAli()) {
        hash = (37 * hash) + SENDERALI_FIELD_NUMBER;
        hash = (53 * hash) + getSenderAli().hashCode();
      }
      if (hasSenderDom()) {
        hash = (37 * hash) + SENDERDOM_FIELD_NUMBER;
        hash = (53 * hash) + getSenderDom().hashCode();
      }
      if (hasSMTPSrv()) {
        hash = (37 * hash) + SMTPSRV_FIELD_NUMBER;
        hash = (53 * hash) + getSMTPSrv().hashCode();
      }
      if (hasSMTPSrvAge()) {
        hash = (37 * hash) + SMTPSRVAGE_FIELD_NUMBER;
        hash = (53 * hash) + getSMTPSrvAge().hashCode();
      }
      if (hasReceivedSPF()) {
        hash = (37 * hash) + RECEIVEDSPF_FIELD_NUMBER;
        hash = (53 * hash) + getReceivedSPF().hashCode();
      }
      if (hasStartTLS()) {
        hash = (37 * hash) + STARTTLS_FIELD_NUMBER;
        hash = (53 * hash) + getStartTLS();
      }
      if (hasSubj()) {
        hash = (37 * hash) + SUBJ_FIELD_NUMBER;
        hash = (53 * hash) + getSubj().hashCode();
      }
      if (hasSubjCnt()) {
        hash = (37 * hash) + SUBJ_CNT_FIELD_NUMBER;
        hash = (53 * hash) + getSubjCnt();
      }
      if (hasUsrAge()) {
        hash = (37 * hash) + USRAGE_FIELD_NUMBER;
        hash = (53 * hash) + getUsrAge().hashCode();
      }
      if (hasEmailVersion()) {
        hash = (37 * hash) + EMAILVERSION_FIELD_NUMBER;
        hash = (53 * hash) + getEmailVersion().hashCode();
      }
      if (hasRcvWit()) {
        hash = (37 * hash) + RCVWIT_FIELD_NUMBER;
        hash = (53 * hash) + getRcvWit().hashCode();
      }
      if (hasXMai()) {
        hash = (37 * hash) + XMAI_FIELD_NUMBER;
        hash = (53 * hash) + getXMai().hashCode();
      }
      if (hasXMaiCnt()) {
        hash = (37 * hash) + XMAICNT_FIELD_NUMBER;
        hash = (53 * hash) + getXMaiCnt();
      }
      if (hasXOriIP()) {
        hash = (37 * hash) + XORIIP_FIELD_NUMBER;
        hash = (53 * hash) + getXOriIP();
      }
      if (hasSenderEmailCnt()) {
        hash = (37 * hash) + SENDEREMAILCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSenderEmailCnt();
      }
      if (hasFw()) {
        hash = (37 * hash) + FW_FIELD_NUMBER;
        hash = (53 * hash) + getFw().hashCode();
      }
      if (hasBefw()) {
        hash = (37 * hash) + BEFW_FIELD_NUMBER;
        hash = (53 * hash) + getBefw().hashCode();
      }
      if (hasRcvDate()) {
        hash = (37 * hash) + RCVDATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRcvDate());
      }
      if (hasRcvSrvAge()) {
        hash = (37 * hash) + RCVSRVAGE_FIELD_NUMBER;
        hash = (53 * hash) + getRcvSrvAge().hashCode();
      }
      if (hasConTraEnc()) {
        hash = (37 * hash) + CONTRAENC_FIELD_NUMBER;
        hash = (53 * hash) + getConTraEnc().hashCode();
      }
      if (hasConTexCha()) {
        hash = (37 * hash) + CONTEXCHA_FIELD_NUMBER;
        hash = (53 * hash) + getConTexCha().hashCode();
      }
      if (hasRealFrom()) {
        hash = (37 * hash) + REALFROM_FIELD_NUMBER;
        hash = (53 * hash) + getRealFrom().hashCode();
      }
      if (hasRealTo()) {
        hash = (37 * hash) + REALTO_FIELD_NUMBER;
        hash = (53 * hash) + getRealTo().hashCode();
      }
      if (hasEmaActRep()) {
        hash = (37 * hash) + EMAACTREP_FIELD_NUMBER;
        hash = (53 * hash) + getEmaActRep().hashCode();
      }
      if (hasRcvFromName()) {
        hash = (37 * hash) + RCVFROMNAME_FIELD_NUMBER;
        hash = (53 * hash) + getRcvFromName().hashCode();
      }
      if (hasErrType()) {
        hash = (37 * hash) + ERRTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getErrType().hashCode();
      }
      if (hasContentWithHtml()) {
        hash = (37 * hash) + CONTENTWITHHTML_FIELD_NUMBER;
        hash = (53 * hash) + getContentWithHtml().hashCode();
      }
      if (hasCharset()) {
        hash = (37 * hash) + CHARSET_FIELD_NUMBER;
        hash = (53 * hash) + getCharset().hashCode();
      }
      if (hasContentLen()) {
        hash = (37 * hash) + CONTENTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getContentLen();
      }
      if (hasIsBeFw()) {
        hash = (37 * hash) + ISBEFW_FIELD_NUMBER;
        hash = (53 * hash) + getIsBeFw().hashCode();
      }
      if (hasAttachmentPath()) {
        hash = (37 * hash) + ATTACHMENTPATH_FIELD_NUMBER;
        hash = (53 * hash) + getAttachmentPath().hashCode();
      }
      if (hasBanner()) {
        hash = (37 * hash) + BANNER_FIELD_NUMBER;
        hash = (53 * hash) + getBanner().hashCode();
      }
      if (getSenderSoftwareCount() > 0) {
        hash = (37 * hash) + SENDERSOFTWARE_FIELD_NUMBER;
        hash = (53 * hash) + getSenderSoftwareList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static EMAILInfoOuterClass.EMAILInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static EMAILInfoOuterClass.EMAILInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static EMAILInfoOuterClass.EMAILInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(EMAILInfoOuterClass.EMAILInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code EMAILInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:EMAILInfo)
        EMAILInfoOuterClass.EMAILInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return EMAILInfoOuterClass.internal_static_EMAILInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return EMAILInfoOuterClass.internal_static_EMAILInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                EMAILInfoOuterClass.EMAILInfo.class, EMAILInfoOuterClass.EMAILInfo.Builder.class);
      }

      // Construct using EMAILInfoOuterClass.EMAILInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        bitField2_ = 0;
        bitField3_ = 0;
        attType_ = emptyList(com.google.protobuf.ByteString.class);
        attTypeCnt_ = 0;
        attFileName_ = emptyList(com.google.protobuf.ByteString.class);
        attFileNameCnt_ = 0;
        attConSize_ = emptyLongList();
        attMD5_ = emptyList(com.google.protobuf.ByteString.class);
        attMD5Cnt_ = 0;
        authRelt_ = 0;
        bCC_ = emptyList(com.google.protobuf.ByteString.class);
        body_ = com.google.protobuf.ByteString.EMPTY;
        bodyTexCha_ = com.google.protobuf.ByteString.EMPTY;
        bodyTraEnc_ = com.google.protobuf.ByteString.EMPTY;
        bodyMD5_ = com.google.protobuf.ByteString.EMPTY;
        bodyType_ = emptyList(com.google.protobuf.ByteString.class);
        bodyTypeCnt_ = 0;
        bodyURL_ = com.google.protobuf.ByteString.EMPTY;
        bodyURLCnt_ = 0;
        bodyLen_ = 0;
        byAsn_ = emptyList(com.google.protobuf.ByteString.class);
        byCountry_ = emptyList(com.google.protobuf.ByteString.class);
        byDom_ = emptyList(com.google.protobuf.ByteString.class);
        byDomCnt_ = 0;
        byIP_ = com.google.protobuf.ByteString.EMPTY;
        byIpCnt_ = 0;
        cC_ = emptyList(com.google.protobuf.ByteString.class);
        cCAli_ = emptyList(com.google.protobuf.ByteString.class);
        command_ = emptyList(com.google.protobuf.ByteString.class);
        count_ = 0;
        content_ = com.google.protobuf.ByteString.EMPTY;
        conType_ = emptyList(com.google.protobuf.ByteString.class);
        conTypeCnt_ = 0;
        date_ = 0;
        deliveredTo_ = com.google.protobuf.ByteString.EMPTY;
        fromAsn_ = emptyList(com.google.protobuf.ByteString.class);
        fromCountry_ = emptyList(com.google.protobuf.ByteString.class);
        fromDom_ = emptyList(com.google.protobuf.ByteString.class);
        fromDomCnt_ = 0;
        fromIp_ = emptyIntList();
        fromIpCnt_ = 0;
        headSet_ = emptyList(com.google.protobuf.ByteString.class);
        headSetCnt_ = 0;
        host_ = com.google.protobuf.ByteString.EMPTY;
        name_ = com.google.protobuf.ByteString.EMPTY;
        os_ = com.google.protobuf.ByteString.EMPTY;
        osVer_ = com.google.protobuf.ByteString.EMPTY;
        vendor_ = com.google.protobuf.ByteString.EMPTY;
        ver_ = com.google.protobuf.ByteString.EMPTY;
        emaInd_ = com.google.protobuf.ByteString.EMPTY;
        login_ = com.google.protobuf.ByteString.EMPTY;
        loginsrv_ = com.google.protobuf.ByteString.EMPTY;
        mailFrom_ = emptyList(com.google.protobuf.ByteString.class);
        mailFromDom_ = emptyList(com.google.protobuf.ByteString.class);
        mailFromDomCnt_ = com.google.protobuf.ByteString.EMPTY;
        mimeVer_ = com.google.protobuf.ByteString.EMPTY;
        mimeVerCnt_ = 0;
        msgID_ = emptyList(com.google.protobuf.ByteString.class);
        msgIDCnt_ = 0;
        emaProtType_ = com.google.protobuf.ByteString.EMPTY;
        pwd_ = com.google.protobuf.ByteString.EMPTY;
        rcptTo_ = emptyList(com.google.protobuf.ByteString.class);
        rcptToDom_ = emptyList(com.google.protobuf.ByteString.class);
        rcptToDomCnt_ = 0;
        repTo_ = com.google.protobuf.ByteString.EMPTY;
        received_ = emptyList(com.google.protobuf.ByteString.class);
        rcvrEmail_ = emptyList(com.google.protobuf.ByteString.class);
        rcvrAli_ = emptyList(com.google.protobuf.ByteString.class);
        rcvrAliCnt_ = 0;
        rcvrEmailCnt_ = 0;
        rcvrDom_ = emptyList(com.google.protobuf.ByteString.class);
        resentSrvAge_ = com.google.protobuf.ByteString.EMPTY;
        resentDate_ = 0;
        resentFrom_ = com.google.protobuf.ByteString.EMPTY;
        resentTo_ = emptyList(com.google.protobuf.ByteString.class);
        senderEmail_ = com.google.protobuf.ByteString.EMPTY;
        senderAli_ = com.google.protobuf.ByteString.EMPTY;
        senderDom_ = com.google.protobuf.ByteString.EMPTY;
        sMTPSrv_ = com.google.protobuf.ByteString.EMPTY;
        sMTPSrvAge_ = com.google.protobuf.ByteString.EMPTY;
        receivedSPF_ = com.google.protobuf.ByteString.EMPTY;
        startTLS_ = 0;
        subj_ = com.google.protobuf.ByteString.EMPTY;
        subjCnt_ = 0;
        usrAge_ = com.google.protobuf.ByteString.EMPTY;
        emailVersion_ = com.google.protobuf.ByteString.EMPTY;
        rcvWit_ = com.google.protobuf.ByteString.EMPTY;
        xMai_ = com.google.protobuf.ByteString.EMPTY;
        xMaiCnt_ = 0;
        xOriIP_ = 0;
        senderEmailCnt_ = 0;
        fw_ = com.google.protobuf.ByteString.EMPTY;
        befw_ = com.google.protobuf.ByteString.EMPTY;
        rcvDate_ = 0L;
        rcvSrvAge_ = com.google.protobuf.ByteString.EMPTY;
        conTraEnc_ = com.google.protobuf.ByteString.EMPTY;
        conTexCha_ = com.google.protobuf.ByteString.EMPTY;
        realFrom_ = com.google.protobuf.ByteString.EMPTY;
        realTo_ = com.google.protobuf.ByteString.EMPTY;
        emaActRep_ = com.google.protobuf.ByteString.EMPTY;
        rcvFromName_ = com.google.protobuf.ByteString.EMPTY;
        errType_ = com.google.protobuf.ByteString.EMPTY;
        contentWithHtml_ = com.google.protobuf.ByteString.EMPTY;
        charset_ = com.google.protobuf.ByteString.EMPTY;
        contentLen_ = 0;
        isBeFw_ = com.google.protobuf.ByteString.EMPTY;
        attachmentPath_ = com.google.protobuf.ByteString.EMPTY;
        banner_ = com.google.protobuf.ByteString.EMPTY;
        senderSoftware_ = emptyList(com.google.protobuf.ByteString.class);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return EMAILInfoOuterClass.internal_static_EMAILInfo_descriptor;
      }

      @java.lang.Override
      public EMAILInfoOuterClass.EMAILInfo getDefaultInstanceForType() {
        return EMAILInfoOuterClass.EMAILInfo.getDefaultInstance();
      }

      @java.lang.Override
      public EMAILInfoOuterClass.EMAILInfo build() {
        EMAILInfoOuterClass.EMAILInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public EMAILInfoOuterClass.EMAILInfo buildPartial() {
        EMAILInfoOuterClass.EMAILInfo result = new EMAILInfoOuterClass.EMAILInfo(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        if (bitField2_ != 0) { buildPartial2(result); }
        if (bitField3_ != 0) { buildPartial3(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(EMAILInfoOuterClass.EMAILInfo result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          attType_.makeImmutable();
          result.attType_ = attType_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.attTypeCnt_ = attTypeCnt_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          attFileName_.makeImmutable();
          result.attFileName_ = attFileName_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.attFileNameCnt_ = attFileNameCnt_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          attConSize_.makeImmutable();
          result.attConSize_ = attConSize_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          attMD5_.makeImmutable();
          result.attMD5_ = attMD5_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.attMD5Cnt_ = attMD5Cnt_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.authRelt_ = authRelt_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          bCC_.makeImmutable();
          result.bCC_ = bCC_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.body_ = body_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.bodyTexCha_ = bodyTexCha_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.bodyTraEnc_ = bodyTraEnc_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.bodyMD5_ = bodyMD5_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          bodyType_.makeImmutable();
          result.bodyType_ = bodyType_;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.bodyTypeCnt_ = bodyTypeCnt_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.bodyURL_ = bodyURL_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.bodyURLCnt_ = bodyURLCnt_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.bodyLen_ = bodyLen_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          byAsn_.makeImmutable();
          result.byAsn_ = byAsn_;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          byCountry_.makeImmutable();
          result.byCountry_ = byCountry_;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          byDom_.makeImmutable();
          result.byDom_ = byDom_;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.byDomCnt_ = byDomCnt_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.byIP_ = byIP_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.byIpCnt_ = byIpCnt_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          cC_.makeImmutable();
          result.cC_ = cC_;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          cCAli_.makeImmutable();
          result.cCAli_ = cCAli_;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          command_.makeImmutable();
          result.command_ = command_;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.count_ = count_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.content_ = content_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          conType_.makeImmutable();
          result.conType_ = conType_;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.conTypeCnt_ = conTypeCnt_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.date_ = date_;
          to_bitField0_ |= 0x00040000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(EMAILInfoOuterClass.EMAILInfo result) {
        int from_bitField1_ = bitField1_;
        int to_bitField0_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.deliveredTo_ = deliveredTo_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          fromAsn_.makeImmutable();
          result.fromAsn_ = fromAsn_;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          fromCountry_.makeImmutable();
          result.fromCountry_ = fromCountry_;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          fromDom_.makeImmutable();
          result.fromDom_ = fromDom_;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.fromDomCnt_ = fromDomCnt_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          fromIp_.makeImmutable();
          result.fromIp_ = fromIp_;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.fromIpCnt_ = fromIpCnt_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          headSet_.makeImmutable();
          result.headSet_ = headSet_;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.headSetCnt_ = headSetCnt_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          result.host_ = host_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          result.name_ = name_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField1_ & 0x00000800) != 0)) {
          result.os_ = os_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField1_ & 0x00001000) != 0)) {
          result.osVer_ = osVer_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField1_ & 0x00002000) != 0)) {
          result.vendor_ = vendor_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField1_ & 0x00004000) != 0)) {
          result.ver_ = ver_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField1_ & 0x00008000) != 0)) {
          result.emaInd_ = emaInd_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField1_ & 0x00010000) != 0)) {
          result.login_ = login_;
          to_bitField0_ |= 0x40000000;
        }
        if (((from_bitField1_ & 0x00020000) != 0)) {
          result.loginsrv_ = loginsrv_;
          to_bitField0_ |= 0x80000000;
        }
        if (((from_bitField1_ & 0x00040000) != 0)) {
          mailFrom_.makeImmutable();
          result.mailFrom_ = mailFrom_;
        }
        if (((from_bitField1_ & 0x00080000) != 0)) {
          mailFromDom_.makeImmutable();
          result.mailFromDom_ = mailFromDom_;
        }
        int to_bitField1_ = 0;
        if (((from_bitField1_ & 0x00100000) != 0)) {
          result.mailFromDomCnt_ = mailFromDomCnt_;
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00200000) != 0)) {
          result.mimeVer_ = mimeVer_;
          to_bitField1_ |= 0x00000002;
        }
        if (((from_bitField1_ & 0x00400000) != 0)) {
          result.mimeVerCnt_ = mimeVerCnt_;
          to_bitField1_ |= 0x00000004;
        }
        if (((from_bitField1_ & 0x00800000) != 0)) {
          msgID_.makeImmutable();
          result.msgID_ = msgID_;
        }
        if (((from_bitField1_ & 0x01000000) != 0)) {
          result.msgIDCnt_ = msgIDCnt_;
          to_bitField1_ |= 0x00000008;
        }
        if (((from_bitField1_ & 0x02000000) != 0)) {
          result.emaProtType_ = emaProtType_;
          to_bitField1_ |= 0x00000010;
        }
        if (((from_bitField1_ & 0x04000000) != 0)) {
          result.pwd_ = pwd_;
          to_bitField1_ |= 0x00000020;
        }
        if (((from_bitField1_ & 0x08000000) != 0)) {
          rcptTo_.makeImmutable();
          result.rcptTo_ = rcptTo_;
        }
        if (((from_bitField1_ & 0x10000000) != 0)) {
          rcptToDom_.makeImmutable();
          result.rcptToDom_ = rcptToDom_;
        }
        if (((from_bitField1_ & 0x20000000) != 0)) {
          result.rcptToDomCnt_ = rcptToDomCnt_;
          to_bitField1_ |= 0x00000040;
        }
        if (((from_bitField1_ & 0x40000000) != 0)) {
          result.repTo_ = repTo_;
          to_bitField1_ |= 0x00000080;
        }
        if (((from_bitField1_ & 0x80000000) != 0)) {
          received_.makeImmutable();
          result.received_ = received_;
        }
        result.bitField0_ |= to_bitField0_;
        result.bitField1_ |= to_bitField1_;
      }

      private void buildPartial2(EMAILInfoOuterClass.EMAILInfo result) {
        int from_bitField2_ = bitField2_;
        if (((from_bitField2_ & 0x00000001) != 0)) {
          rcvrEmail_.makeImmutable();
          result.rcvrEmail_ = rcvrEmail_;
        }
        if (((from_bitField2_ & 0x00000002) != 0)) {
          rcvrAli_.makeImmutable();
          result.rcvrAli_ = rcvrAli_;
        }
        int to_bitField1_ = 0;
        if (((from_bitField2_ & 0x00000004) != 0)) {
          result.rcvrAliCnt_ = rcvrAliCnt_;
          to_bitField1_ |= 0x00000100;
        }
        if (((from_bitField2_ & 0x00000008) != 0)) {
          result.rcvrEmailCnt_ = rcvrEmailCnt_;
          to_bitField1_ |= 0x00000200;
        }
        if (((from_bitField2_ & 0x00000010) != 0)) {
          rcvrDom_.makeImmutable();
          result.rcvrDom_ = rcvrDom_;
        }
        if (((from_bitField2_ & 0x00000020) != 0)) {
          result.resentSrvAge_ = resentSrvAge_;
          to_bitField1_ |= 0x00000400;
        }
        if (((from_bitField2_ & 0x00000040) != 0)) {
          result.resentDate_ = resentDate_;
          to_bitField1_ |= 0x00000800;
        }
        if (((from_bitField2_ & 0x00000080) != 0)) {
          result.resentFrom_ = resentFrom_;
          to_bitField1_ |= 0x00001000;
        }
        if (((from_bitField2_ & 0x00000100) != 0)) {
          resentTo_.makeImmutable();
          result.resentTo_ = resentTo_;
        }
        if (((from_bitField2_ & 0x00000200) != 0)) {
          result.senderEmail_ = senderEmail_;
          to_bitField1_ |= 0x00002000;
        }
        if (((from_bitField2_ & 0x00000400) != 0)) {
          result.senderAli_ = senderAli_;
          to_bitField1_ |= 0x00004000;
        }
        if (((from_bitField2_ & 0x00000800) != 0)) {
          result.senderDom_ = senderDom_;
          to_bitField1_ |= 0x00008000;
        }
        if (((from_bitField2_ & 0x00001000) != 0)) {
          result.sMTPSrv_ = sMTPSrv_;
          to_bitField1_ |= 0x00010000;
        }
        if (((from_bitField2_ & 0x00002000) != 0)) {
          result.sMTPSrvAge_ = sMTPSrvAge_;
          to_bitField1_ |= 0x00020000;
        }
        if (((from_bitField2_ & 0x00004000) != 0)) {
          result.receivedSPF_ = receivedSPF_;
          to_bitField1_ |= 0x00040000;
        }
        if (((from_bitField2_ & 0x00008000) != 0)) {
          result.startTLS_ = startTLS_;
          to_bitField1_ |= 0x00080000;
        }
        if (((from_bitField2_ & 0x00010000) != 0)) {
          result.subj_ = subj_;
          to_bitField1_ |= 0x00100000;
        }
        if (((from_bitField2_ & 0x00020000) != 0)) {
          result.subjCnt_ = subjCnt_;
          to_bitField1_ |= 0x00200000;
        }
        if (((from_bitField2_ & 0x00040000) != 0)) {
          result.usrAge_ = usrAge_;
          to_bitField1_ |= 0x00400000;
        }
        if (((from_bitField2_ & 0x00080000) != 0)) {
          result.emailVersion_ = emailVersion_;
          to_bitField1_ |= 0x00800000;
        }
        if (((from_bitField2_ & 0x00100000) != 0)) {
          result.rcvWit_ = rcvWit_;
          to_bitField1_ |= 0x01000000;
        }
        if (((from_bitField2_ & 0x00200000) != 0)) {
          result.xMai_ = xMai_;
          to_bitField1_ |= 0x02000000;
        }
        if (((from_bitField2_ & 0x00400000) != 0)) {
          result.xMaiCnt_ = xMaiCnt_;
          to_bitField1_ |= 0x04000000;
        }
        if (((from_bitField2_ & 0x00800000) != 0)) {
          result.xOriIP_ = xOriIP_;
          to_bitField1_ |= 0x08000000;
        }
        if (((from_bitField2_ & 0x01000000) != 0)) {
          result.senderEmailCnt_ = senderEmailCnt_;
          to_bitField1_ |= 0x10000000;
        }
        if (((from_bitField2_ & 0x02000000) != 0)) {
          result.fw_ = fw_;
          to_bitField1_ |= 0x20000000;
        }
        if (((from_bitField2_ & 0x04000000) != 0)) {
          result.befw_ = befw_;
          to_bitField1_ |= 0x40000000;
        }
        if (((from_bitField2_ & 0x08000000) != 0)) {
          result.rcvDate_ = rcvDate_;
          to_bitField1_ |= 0x80000000;
        }
        int to_bitField2_ = 0;
        if (((from_bitField2_ & 0x10000000) != 0)) {
          result.rcvSrvAge_ = rcvSrvAge_;
          to_bitField2_ |= 0x00000001;
        }
        if (((from_bitField2_ & 0x20000000) != 0)) {
          result.conTraEnc_ = conTraEnc_;
          to_bitField2_ |= 0x00000002;
        }
        if (((from_bitField2_ & 0x40000000) != 0)) {
          result.conTexCha_ = conTexCha_;
          to_bitField2_ |= 0x00000004;
        }
        if (((from_bitField2_ & 0x80000000) != 0)) {
          result.realFrom_ = realFrom_;
          to_bitField2_ |= 0x00000008;
        }
        result.bitField1_ |= to_bitField1_;
        result.bitField2_ |= to_bitField2_;
      }

      private void buildPartial3(EMAILInfoOuterClass.EMAILInfo result) {
        int from_bitField3_ = bitField3_;
        int to_bitField2_ = 0;
        if (((from_bitField3_ & 0x00000001) != 0)) {
          result.realTo_ = realTo_;
          to_bitField2_ |= 0x00000010;
        }
        if (((from_bitField3_ & 0x00000002) != 0)) {
          result.emaActRep_ = emaActRep_;
          to_bitField2_ |= 0x00000020;
        }
        if (((from_bitField3_ & 0x00000004) != 0)) {
          result.rcvFromName_ = rcvFromName_;
          to_bitField2_ |= 0x00000040;
        }
        if (((from_bitField3_ & 0x00000008) != 0)) {
          result.errType_ = errType_;
          to_bitField2_ |= 0x00000080;
        }
        if (((from_bitField3_ & 0x00000010) != 0)) {
          result.contentWithHtml_ = contentWithHtml_;
          to_bitField2_ |= 0x00000100;
        }
        if (((from_bitField3_ & 0x00000020) != 0)) {
          result.charset_ = charset_;
          to_bitField2_ |= 0x00000200;
        }
        if (((from_bitField3_ & 0x00000040) != 0)) {
          result.contentLen_ = contentLen_;
          to_bitField2_ |= 0x00000400;
        }
        if (((from_bitField3_ & 0x00000080) != 0)) {
          result.isBeFw_ = isBeFw_;
          to_bitField2_ |= 0x00000800;
        }
        if (((from_bitField3_ & 0x00000100) != 0)) {
          result.attachmentPath_ = attachmentPath_;
          to_bitField2_ |= 0x00001000;
        }
        if (((from_bitField3_ & 0x00000200) != 0)) {
          result.banner_ = banner_;
          to_bitField2_ |= 0x00002000;
        }
        if (((from_bitField3_ & 0x00000400) != 0)) {
          senderSoftware_.makeImmutable();
          result.senderSoftware_ = senderSoftware_;
        }
        result.bitField2_ |= to_bitField2_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof EMAILInfoOuterClass.EMAILInfo) {
          return mergeFrom((EMAILInfoOuterClass.EMAILInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(EMAILInfoOuterClass.EMAILInfo other) {
        if (other == EMAILInfoOuterClass.EMAILInfo.getDefaultInstance()) return this;
        if (!other.attType_.isEmpty()) {
          if (attType_.isEmpty()) {
            attType_ = other.attType_;
            attType_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureAttTypeIsMutable();
            attType_.addAll(other.attType_);
          }
          onChanged();
        }
        if (other.hasAttTypeCnt()) {
          setAttTypeCnt(other.getAttTypeCnt());
        }
        if (!other.attFileName_.isEmpty()) {
          if (attFileName_.isEmpty()) {
            attFileName_ = other.attFileName_;
            attFileName_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureAttFileNameIsMutable();
            attFileName_.addAll(other.attFileName_);
          }
          onChanged();
        }
        if (other.hasAttFileNameCnt()) {
          setAttFileNameCnt(other.getAttFileNameCnt());
        }
        if (!other.attConSize_.isEmpty()) {
          if (attConSize_.isEmpty()) {
            attConSize_ = other.attConSize_;
            attConSize_.makeImmutable();
            bitField0_ |= 0x00000010;
          } else {
            ensureAttConSizeIsMutable();
            attConSize_.addAll(other.attConSize_);
          }
          onChanged();
        }
        if (!other.attMD5_.isEmpty()) {
          if (attMD5_.isEmpty()) {
            attMD5_ = other.attMD5_;
            attMD5_.makeImmutable();
            bitField0_ |= 0x00000020;
          } else {
            ensureAttMD5IsMutable();
            attMD5_.addAll(other.attMD5_);
          }
          onChanged();
        }
        if (other.hasAttMD5Cnt()) {
          setAttMD5Cnt(other.getAttMD5Cnt());
        }
        if (other.hasAuthRelt()) {
          setAuthRelt(other.getAuthRelt());
        }
        if (!other.bCC_.isEmpty()) {
          if (bCC_.isEmpty()) {
            bCC_ = other.bCC_;
            bCC_.makeImmutable();
            bitField0_ |= 0x00000100;
          } else {
            ensureBCCIsMutable();
            bCC_.addAll(other.bCC_);
          }
          onChanged();
        }
        if (other.hasBody()) {
          setBody(other.getBody());
        }
        if (other.hasBodyTexCha()) {
          setBodyTexCha(other.getBodyTexCha());
        }
        if (other.hasBodyTraEnc()) {
          setBodyTraEnc(other.getBodyTraEnc());
        }
        if (other.hasBodyMD5()) {
          setBodyMD5(other.getBodyMD5());
        }
        if (!other.bodyType_.isEmpty()) {
          if (bodyType_.isEmpty()) {
            bodyType_ = other.bodyType_;
            bodyType_.makeImmutable();
            bitField0_ |= 0x00002000;
          } else {
            ensureBodyTypeIsMutable();
            bodyType_.addAll(other.bodyType_);
          }
          onChanged();
        }
        if (other.hasBodyTypeCnt()) {
          setBodyTypeCnt(other.getBodyTypeCnt());
        }
        if (other.hasBodyURL()) {
          setBodyURL(other.getBodyURL());
        }
        if (other.hasBodyURLCnt()) {
          setBodyURLCnt(other.getBodyURLCnt());
        }
        if (other.hasBodyLen()) {
          setBodyLen(other.getBodyLen());
        }
        if (!other.byAsn_.isEmpty()) {
          if (byAsn_.isEmpty()) {
            byAsn_ = other.byAsn_;
            byAsn_.makeImmutable();
            bitField0_ |= 0x00040000;
          } else {
            ensureByAsnIsMutable();
            byAsn_.addAll(other.byAsn_);
          }
          onChanged();
        }
        if (!other.byCountry_.isEmpty()) {
          if (byCountry_.isEmpty()) {
            byCountry_ = other.byCountry_;
            byCountry_.makeImmutable();
            bitField0_ |= 0x00080000;
          } else {
            ensureByCountryIsMutable();
            byCountry_.addAll(other.byCountry_);
          }
          onChanged();
        }
        if (!other.byDom_.isEmpty()) {
          if (byDom_.isEmpty()) {
            byDom_ = other.byDom_;
            byDom_.makeImmutable();
            bitField0_ |= 0x00100000;
          } else {
            ensureByDomIsMutable();
            byDom_.addAll(other.byDom_);
          }
          onChanged();
        }
        if (other.hasByDomCnt()) {
          setByDomCnt(other.getByDomCnt());
        }
        if (other.hasByIP()) {
          setByIP(other.getByIP());
        }
        if (other.hasByIpCnt()) {
          setByIpCnt(other.getByIpCnt());
        }
        if (!other.cC_.isEmpty()) {
          if (cC_.isEmpty()) {
            cC_ = other.cC_;
            cC_.makeImmutable();
            bitField0_ |= 0x01000000;
          } else {
            ensureCCIsMutable();
            cC_.addAll(other.cC_);
          }
          onChanged();
        }
        if (!other.cCAli_.isEmpty()) {
          if (cCAli_.isEmpty()) {
            cCAli_ = other.cCAli_;
            cCAli_.makeImmutable();
            bitField0_ |= 0x02000000;
          } else {
            ensureCCAliIsMutable();
            cCAli_.addAll(other.cCAli_);
          }
          onChanged();
        }
        if (!other.command_.isEmpty()) {
          if (command_.isEmpty()) {
            command_ = other.command_;
            command_.makeImmutable();
            bitField0_ |= 0x04000000;
          } else {
            ensureCommandIsMutable();
            command_.addAll(other.command_);
          }
          onChanged();
        }
        if (other.hasCount()) {
          setCount(other.getCount());
        }
        if (other.hasContent()) {
          setContent(other.getContent());
        }
        if (!other.conType_.isEmpty()) {
          if (conType_.isEmpty()) {
            conType_ = other.conType_;
            conType_.makeImmutable();
            bitField0_ |= 0x20000000;
          } else {
            ensureConTypeIsMutable();
            conType_.addAll(other.conType_);
          }
          onChanged();
        }
        if (other.hasConTypeCnt()) {
          setConTypeCnt(other.getConTypeCnt());
        }
        if (other.hasDate()) {
          setDate(other.getDate());
        }
        if (other.hasDeliveredTo()) {
          setDeliveredTo(other.getDeliveredTo());
        }
        if (!other.fromAsn_.isEmpty()) {
          if (fromAsn_.isEmpty()) {
            fromAsn_ = other.fromAsn_;
            fromAsn_.makeImmutable();
            bitField1_ |= 0x00000002;
          } else {
            ensureFromAsnIsMutable();
            fromAsn_.addAll(other.fromAsn_);
          }
          onChanged();
        }
        if (!other.fromCountry_.isEmpty()) {
          if (fromCountry_.isEmpty()) {
            fromCountry_ = other.fromCountry_;
            fromCountry_.makeImmutable();
            bitField1_ |= 0x00000004;
          } else {
            ensureFromCountryIsMutable();
            fromCountry_.addAll(other.fromCountry_);
          }
          onChanged();
        }
        if (!other.fromDom_.isEmpty()) {
          if (fromDom_.isEmpty()) {
            fromDom_ = other.fromDom_;
            fromDom_.makeImmutable();
            bitField1_ |= 0x00000008;
          } else {
            ensureFromDomIsMutable();
            fromDom_.addAll(other.fromDom_);
          }
          onChanged();
        }
        if (other.hasFromDomCnt()) {
          setFromDomCnt(other.getFromDomCnt());
        }
        if (!other.fromIp_.isEmpty()) {
          if (fromIp_.isEmpty()) {
            fromIp_ = other.fromIp_;
            fromIp_.makeImmutable();
            bitField1_ |= 0x00000020;
          } else {
            ensureFromIpIsMutable();
            fromIp_.addAll(other.fromIp_);
          }
          onChanged();
        }
        if (other.hasFromIpCnt()) {
          setFromIpCnt(other.getFromIpCnt());
        }
        if (!other.headSet_.isEmpty()) {
          if (headSet_.isEmpty()) {
            headSet_ = other.headSet_;
            headSet_.makeImmutable();
            bitField1_ |= 0x00000080;
          } else {
            ensureHeadSetIsMutable();
            headSet_.addAll(other.headSet_);
          }
          onChanged();
        }
        if (other.hasHeadSetCnt()) {
          setHeadSetCnt(other.getHeadSetCnt());
        }
        if (other.hasHost()) {
          setHost(other.getHost());
        }
        if (other.hasName()) {
          setName(other.getName());
        }
        if (other.hasOs()) {
          setOs(other.getOs());
        }
        if (other.hasOsVer()) {
          setOsVer(other.getOsVer());
        }
        if (other.hasVendor()) {
          setVendor(other.getVendor());
        }
        if (other.hasVer()) {
          setVer(other.getVer());
        }
        if (other.hasEmaInd()) {
          setEmaInd(other.getEmaInd());
        }
        if (other.hasLogin()) {
          setLogin(other.getLogin());
        }
        if (other.hasLoginsrv()) {
          setLoginsrv(other.getLoginsrv());
        }
        if (!other.mailFrom_.isEmpty()) {
          if (mailFrom_.isEmpty()) {
            mailFrom_ = other.mailFrom_;
            mailFrom_.makeImmutable();
            bitField1_ |= 0x00040000;
          } else {
            ensureMailFromIsMutable();
            mailFrom_.addAll(other.mailFrom_);
          }
          onChanged();
        }
        if (!other.mailFromDom_.isEmpty()) {
          if (mailFromDom_.isEmpty()) {
            mailFromDom_ = other.mailFromDom_;
            mailFromDom_.makeImmutable();
            bitField1_ |= 0x00080000;
          } else {
            ensureMailFromDomIsMutable();
            mailFromDom_.addAll(other.mailFromDom_);
          }
          onChanged();
        }
        if (other.hasMailFromDomCnt()) {
          setMailFromDomCnt(other.getMailFromDomCnt());
        }
        if (other.hasMimeVer()) {
          setMimeVer(other.getMimeVer());
        }
        if (other.hasMimeVerCnt()) {
          setMimeVerCnt(other.getMimeVerCnt());
        }
        if (!other.msgID_.isEmpty()) {
          if (msgID_.isEmpty()) {
            msgID_ = other.msgID_;
            msgID_.makeImmutable();
            bitField1_ |= 0x00800000;
          } else {
            ensureMsgIDIsMutable();
            msgID_.addAll(other.msgID_);
          }
          onChanged();
        }
        if (other.hasMsgIDCnt()) {
          setMsgIDCnt(other.getMsgIDCnt());
        }
        if (other.hasEmaProtType()) {
          setEmaProtType(other.getEmaProtType());
        }
        if (other.hasPwd()) {
          setPwd(other.getPwd());
        }
        if (!other.rcptTo_.isEmpty()) {
          if (rcptTo_.isEmpty()) {
            rcptTo_ = other.rcptTo_;
            rcptTo_.makeImmutable();
            bitField1_ |= 0x08000000;
          } else {
            ensureRcptToIsMutable();
            rcptTo_.addAll(other.rcptTo_);
          }
          onChanged();
        }
        if (!other.rcptToDom_.isEmpty()) {
          if (rcptToDom_.isEmpty()) {
            rcptToDom_ = other.rcptToDom_;
            rcptToDom_.makeImmutable();
            bitField1_ |= 0x10000000;
          } else {
            ensureRcptToDomIsMutable();
            rcptToDom_.addAll(other.rcptToDom_);
          }
          onChanged();
        }
        if (other.hasRcptToDomCnt()) {
          setRcptToDomCnt(other.getRcptToDomCnt());
        }
        if (other.hasRepTo()) {
          setRepTo(other.getRepTo());
        }
        if (!other.received_.isEmpty()) {
          if (received_.isEmpty()) {
            received_ = other.received_;
            received_.makeImmutable();
            bitField1_ |= 0x80000000;
          } else {
            ensureReceivedIsMutable();
            received_.addAll(other.received_);
          }
          onChanged();
        }
        if (!other.rcvrEmail_.isEmpty()) {
          if (rcvrEmail_.isEmpty()) {
            rcvrEmail_ = other.rcvrEmail_;
            rcvrEmail_.makeImmutable();
            bitField2_ |= 0x00000001;
          } else {
            ensureRcvrEmailIsMutable();
            rcvrEmail_.addAll(other.rcvrEmail_);
          }
          onChanged();
        }
        if (!other.rcvrAli_.isEmpty()) {
          if (rcvrAli_.isEmpty()) {
            rcvrAli_ = other.rcvrAli_;
            rcvrAli_.makeImmutable();
            bitField2_ |= 0x00000002;
          } else {
            ensureRcvrAliIsMutable();
            rcvrAli_.addAll(other.rcvrAli_);
          }
          onChanged();
        }
        if (other.hasRcvrAliCnt()) {
          setRcvrAliCnt(other.getRcvrAliCnt());
        }
        if (other.hasRcvrEmailCnt()) {
          setRcvrEmailCnt(other.getRcvrEmailCnt());
        }
        if (!other.rcvrDom_.isEmpty()) {
          if (rcvrDom_.isEmpty()) {
            rcvrDom_ = other.rcvrDom_;
            rcvrDom_.makeImmutable();
            bitField2_ |= 0x00000010;
          } else {
            ensureRcvrDomIsMutable();
            rcvrDom_.addAll(other.rcvrDom_);
          }
          onChanged();
        }
        if (other.hasResentSrvAge()) {
          setResentSrvAge(other.getResentSrvAge());
        }
        if (other.hasResentDate()) {
          setResentDate(other.getResentDate());
        }
        if (other.hasResentFrom()) {
          setResentFrom(other.getResentFrom());
        }
        if (!other.resentTo_.isEmpty()) {
          if (resentTo_.isEmpty()) {
            resentTo_ = other.resentTo_;
            resentTo_.makeImmutable();
            bitField2_ |= 0x00000100;
          } else {
            ensureResentToIsMutable();
            resentTo_.addAll(other.resentTo_);
          }
          onChanged();
        }
        if (other.hasSenderEmail()) {
          setSenderEmail(other.getSenderEmail());
        }
        if (other.hasSenderAli()) {
          setSenderAli(other.getSenderAli());
        }
        if (other.hasSenderDom()) {
          setSenderDom(other.getSenderDom());
        }
        if (other.hasSMTPSrv()) {
          setSMTPSrv(other.getSMTPSrv());
        }
        if (other.hasSMTPSrvAge()) {
          setSMTPSrvAge(other.getSMTPSrvAge());
        }
        if (other.hasReceivedSPF()) {
          setReceivedSPF(other.getReceivedSPF());
        }
        if (other.hasStartTLS()) {
          setStartTLS(other.getStartTLS());
        }
        if (other.hasSubj()) {
          setSubj(other.getSubj());
        }
        if (other.hasSubjCnt()) {
          setSubjCnt(other.getSubjCnt());
        }
        if (other.hasUsrAge()) {
          setUsrAge(other.getUsrAge());
        }
        if (other.hasEmailVersion()) {
          setEmailVersion(other.getEmailVersion());
        }
        if (other.hasRcvWit()) {
          setRcvWit(other.getRcvWit());
        }
        if (other.hasXMai()) {
          setXMai(other.getXMai());
        }
        if (other.hasXMaiCnt()) {
          setXMaiCnt(other.getXMaiCnt());
        }
        if (other.hasXOriIP()) {
          setXOriIP(other.getXOriIP());
        }
        if (other.hasSenderEmailCnt()) {
          setSenderEmailCnt(other.getSenderEmailCnt());
        }
        if (other.hasFw()) {
          setFw(other.getFw());
        }
        if (other.hasBefw()) {
          setBefw(other.getBefw());
        }
        if (other.hasRcvDate()) {
          setRcvDate(other.getRcvDate());
        }
        if (other.hasRcvSrvAge()) {
          setRcvSrvAge(other.getRcvSrvAge());
        }
        if (other.hasConTraEnc()) {
          setConTraEnc(other.getConTraEnc());
        }
        if (other.hasConTexCha()) {
          setConTexCha(other.getConTexCha());
        }
        if (other.hasRealFrom()) {
          setRealFrom(other.getRealFrom());
        }
        if (other.hasRealTo()) {
          setRealTo(other.getRealTo());
        }
        if (other.hasEmaActRep()) {
          setEmaActRep(other.getEmaActRep());
        }
        if (other.hasRcvFromName()) {
          setRcvFromName(other.getRcvFromName());
        }
        if (other.hasErrType()) {
          setErrType(other.getErrType());
        }
        if (other.hasContentWithHtml()) {
          setContentWithHtml(other.getContentWithHtml());
        }
        if (other.hasCharset()) {
          setCharset(other.getCharset());
        }
        if (other.hasContentLen()) {
          setContentLen(other.getContentLen());
        }
        if (other.hasIsBeFw()) {
          setIsBeFw(other.getIsBeFw());
        }
        if (other.hasAttachmentPath()) {
          setAttachmentPath(other.getAttachmentPath());
        }
        if (other.hasBanner()) {
          setBanner(other.getBanner());
        }
        if (!other.senderSoftware_.isEmpty()) {
          if (senderSoftware_.isEmpty()) {
            senderSoftware_ = other.senderSoftware_;
            senderSoftware_.makeImmutable();
            bitField3_ |= 0x00000400;
          } else {
            ensureSenderSoftwareIsMutable();
            senderSoftware_.addAll(other.senderSoftware_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAttTypeIsMutable();
                attType_.add(v);
                break;
              } // case 10
              case 16: {
                attTypeCnt_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAttFileNameIsMutable();
                attFileName_.add(v);
                break;
              } // case 26
              case 32: {
                attFileNameCnt_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                long v = input.readUInt64();
                ensureAttConSizeIsMutable();
                attConSize_.addLong(v);
                break;
              } // case 40
              case 42: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureAttConSizeIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  attConSize_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 42
              case 50: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAttMD5IsMutable();
                attMD5_.add(v);
                break;
              } // case 50
              case 56: {
                attMD5Cnt_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                authRelt_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 74: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureBCCIsMutable();
                bCC_.add(v);
                break;
              } // case 74
              case 82: {
                body_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                bodyTexCha_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                bodyTraEnc_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                bodyMD5_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureBodyTypeIsMutable();
                bodyType_.add(v);
                break;
              } // case 114
              case 120: {
                bodyTypeCnt_ = input.readUInt32();
                bitField0_ |= 0x00004000;
                break;
              } // case 120
              case 130: {
                bodyURL_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 136: {
                bodyURLCnt_ = input.readUInt32();
                bitField0_ |= 0x00010000;
                break;
              } // case 136
              case 144: {
                bodyLen_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 154: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureByAsnIsMutable();
                byAsn_.add(v);
                break;
              } // case 154
              case 162: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureByCountryIsMutable();
                byCountry_.add(v);
                break;
              } // case 162
              case 170: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureByDomIsMutable();
                byDom_.add(v);
                break;
              } // case 170
              case 176: {
                byDomCnt_ = input.readUInt32();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              case 186: {
                byIP_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 192: {
                byIpCnt_ = input.readUInt32();
                bitField0_ |= 0x00800000;
                break;
              } // case 192
              case 202: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureCCIsMutable();
                cC_.add(v);
                break;
              } // case 202
              case 210: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureCCAliIsMutable();
                cCAli_.add(v);
                break;
              } // case 210
              case 218: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureCommandIsMutable();
                command_.add(v);
                break;
              } // case 218
              case 224: {
                count_ = input.readUInt32();
                bitField0_ |= 0x08000000;
                break;
              } // case 224
              case 234: {
                content_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              case 242: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureConTypeIsMutable();
                conType_.add(v);
                break;
              } // case 242
              case 248: {
                conTypeCnt_ = input.readUInt32();
                bitField0_ |= 0x40000000;
                break;
              } // case 248
              case 256: {
                date_ = input.readUInt32();
                bitField0_ |= 0x80000000;
                break;
              } // case 256
              case 266: {
                deliveredTo_ = input.readBytes();
                bitField1_ |= 0x00000001;
                break;
              } // case 266
              case 274: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureFromAsnIsMutable();
                fromAsn_.add(v);
                break;
              } // case 274
              case 282: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureFromCountryIsMutable();
                fromCountry_.add(v);
                break;
              } // case 282
              case 290: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureFromDomIsMutable();
                fromDom_.add(v);
                break;
              } // case 290
              case 296: {
                fromDomCnt_ = input.readUInt32();
                bitField1_ |= 0x00000010;
                break;
              } // case 296
              case 304: {
                int v = input.readUInt32();
                ensureFromIpIsMutable();
                fromIp_.addInt(v);
                break;
              } // case 304
              case 306: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureFromIpIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  fromIp_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 306
              case 312: {
                fromIpCnt_ = input.readUInt32();
                bitField1_ |= 0x00000040;
                break;
              } // case 312
              case 322: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureHeadSetIsMutable();
                headSet_.add(v);
                break;
              } // case 322
              case 328: {
                headSetCnt_ = input.readUInt32();
                bitField1_ |= 0x00000100;
                break;
              } // case 328
              case 338: {
                host_ = input.readBytes();
                bitField1_ |= 0x00000200;
                break;
              } // case 338
              case 346: {
                name_ = input.readBytes();
                bitField1_ |= 0x00000400;
                break;
              } // case 346
              case 354: {
                os_ = input.readBytes();
                bitField1_ |= 0x00000800;
                break;
              } // case 354
              case 362: {
                osVer_ = input.readBytes();
                bitField1_ |= 0x00001000;
                break;
              } // case 362
              case 370: {
                vendor_ = input.readBytes();
                bitField1_ |= 0x00002000;
                break;
              } // case 370
              case 378: {
                ver_ = input.readBytes();
                bitField1_ |= 0x00004000;
                break;
              } // case 378
              case 386: {
                emaInd_ = input.readBytes();
                bitField1_ |= 0x00008000;
                break;
              } // case 386
              case 394: {
                login_ = input.readBytes();
                bitField1_ |= 0x00010000;
                break;
              } // case 394
              case 402: {
                loginsrv_ = input.readBytes();
                bitField1_ |= 0x00020000;
                break;
              } // case 402
              case 410: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureMailFromIsMutable();
                mailFrom_.add(v);
                break;
              } // case 410
              case 418: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureMailFromDomIsMutable();
                mailFromDom_.add(v);
                break;
              } // case 418
              case 426: {
                mailFromDomCnt_ = input.readBytes();
                bitField1_ |= 0x00100000;
                break;
              } // case 426
              case 434: {
                mimeVer_ = input.readBytes();
                bitField1_ |= 0x00200000;
                break;
              } // case 434
              case 440: {
                mimeVerCnt_ = input.readUInt32();
                bitField1_ |= 0x00400000;
                break;
              } // case 440
              case 450: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureMsgIDIsMutable();
                msgID_.add(v);
                break;
              } // case 450
              case 456: {
                msgIDCnt_ = input.readUInt32();
                bitField1_ |= 0x01000000;
                break;
              } // case 456
              case 466: {
                emaProtType_ = input.readBytes();
                bitField1_ |= 0x02000000;
                break;
              } // case 466
              case 474: {
                pwd_ = input.readBytes();
                bitField1_ |= 0x04000000;
                break;
              } // case 474
              case 482: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureRcptToIsMutable();
                rcptTo_.add(v);
                break;
              } // case 482
              case 490: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureRcptToDomIsMutable();
                rcptToDom_.add(v);
                break;
              } // case 490
              case 496: {
                rcptToDomCnt_ = input.readUInt32();
                bitField1_ |= 0x20000000;
                break;
              } // case 496
              case 506: {
                repTo_ = input.readBytes();
                bitField1_ |= 0x40000000;
                break;
              } // case 506
              case 514: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureReceivedIsMutable();
                received_.add(v);
                break;
              } // case 514
              case 522: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureRcvrEmailIsMutable();
                rcvrEmail_.add(v);
                break;
              } // case 522
              case 530: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureRcvrAliIsMutable();
                rcvrAli_.add(v);
                break;
              } // case 530
              case 536: {
                rcvrAliCnt_ = input.readUInt32();
                bitField2_ |= 0x00000004;
                break;
              } // case 536
              case 544: {
                rcvrEmailCnt_ = input.readUInt32();
                bitField2_ |= 0x00000008;
                break;
              } // case 544
              case 554: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureRcvrDomIsMutable();
                rcvrDom_.add(v);
                break;
              } // case 554
              case 562: {
                resentSrvAge_ = input.readBytes();
                bitField2_ |= 0x00000020;
                break;
              } // case 562
              case 568: {
                resentDate_ = input.readUInt32();
                bitField2_ |= 0x00000040;
                break;
              } // case 568
              case 578: {
                resentFrom_ = input.readBytes();
                bitField2_ |= 0x00000080;
                break;
              } // case 578
              case 586: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureResentToIsMutable();
                resentTo_.add(v);
                break;
              } // case 586
              case 594: {
                senderEmail_ = input.readBytes();
                bitField2_ |= 0x00000200;
                break;
              } // case 594
              case 602: {
                senderAli_ = input.readBytes();
                bitField2_ |= 0x00000400;
                break;
              } // case 602
              case 610: {
                senderDom_ = input.readBytes();
                bitField2_ |= 0x00000800;
                break;
              } // case 610
              case 618: {
                sMTPSrv_ = input.readBytes();
                bitField2_ |= 0x00001000;
                break;
              } // case 618
              case 626: {
                sMTPSrvAge_ = input.readBytes();
                bitField2_ |= 0x00002000;
                break;
              } // case 626
              case 634: {
                receivedSPF_ = input.readBytes();
                bitField2_ |= 0x00004000;
                break;
              } // case 634
              case 640: {
                startTLS_ = input.readUInt32();
                bitField2_ |= 0x00008000;
                break;
              } // case 640
              case 650: {
                subj_ = input.readBytes();
                bitField2_ |= 0x00010000;
                break;
              } // case 650
              case 656: {
                subjCnt_ = input.readUInt32();
                bitField2_ |= 0x00020000;
                break;
              } // case 656
              case 666: {
                usrAge_ = input.readBytes();
                bitField2_ |= 0x00040000;
                break;
              } // case 666
              case 674: {
                emailVersion_ = input.readBytes();
                bitField2_ |= 0x00080000;
                break;
              } // case 674
              case 682: {
                rcvWit_ = input.readBytes();
                bitField2_ |= 0x00100000;
                break;
              } // case 682
              case 690: {
                xMai_ = input.readBytes();
                bitField2_ |= 0x00200000;
                break;
              } // case 690
              case 696: {
                xMaiCnt_ = input.readUInt32();
                bitField2_ |= 0x00400000;
                break;
              } // case 696
              case 704: {
                xOriIP_ = input.readUInt32();
                bitField2_ |= 0x00800000;
                break;
              } // case 704
              case 712: {
                senderEmailCnt_ = input.readUInt32();
                bitField2_ |= 0x01000000;
                break;
              } // case 712
              case 722: {
                fw_ = input.readBytes();
                bitField2_ |= 0x02000000;
                break;
              } // case 722
              case 730: {
                befw_ = input.readBytes();
                bitField2_ |= 0x04000000;
                break;
              } // case 730
              case 736: {
                rcvDate_ = input.readUInt64();
                bitField2_ |= 0x08000000;
                break;
              } // case 736
              case 746: {
                rcvSrvAge_ = input.readBytes();
                bitField2_ |= 0x10000000;
                break;
              } // case 746
              case 754: {
                conTraEnc_ = input.readBytes();
                bitField2_ |= 0x20000000;
                break;
              } // case 754
              case 762: {
                conTexCha_ = input.readBytes();
                bitField2_ |= 0x40000000;
                break;
              } // case 762
              case 770: {
                realFrom_ = input.readBytes();
                bitField2_ |= 0x80000000;
                break;
              } // case 770
              case 778: {
                realTo_ = input.readBytes();
                bitField3_ |= 0x00000001;
                break;
              } // case 778
              case 786: {
                emaActRep_ = input.readBytes();
                bitField3_ |= 0x00000002;
                break;
              } // case 786
              case 794: {
                rcvFromName_ = input.readBytes();
                bitField3_ |= 0x00000004;
                break;
              } // case 794
              case 802: {
                errType_ = input.readBytes();
                bitField3_ |= 0x00000008;
                break;
              } // case 802
              case 810: {
                contentWithHtml_ = input.readBytes();
                bitField3_ |= 0x00000010;
                break;
              } // case 810
              case 818: {
                charset_ = input.readBytes();
                bitField3_ |= 0x00000020;
                break;
              } // case 818
              case 824: {
                contentLen_ = input.readUInt32();
                bitField3_ |= 0x00000040;
                break;
              } // case 824
              case 834: {
                isBeFw_ = input.readBytes();
                bitField3_ |= 0x00000080;
                break;
              } // case 834
              case 842: {
                attachmentPath_ = input.readBytes();
                bitField3_ |= 0x00000100;
                break;
              } // case 842
              case 850: {
                banner_ = input.readBytes();
                bitField3_ |= 0x00000200;
                break;
              } // case 850
              case 858: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureSenderSoftwareIsMutable();
                senderSoftware_.add(v);
                break;
              } // case 858
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;
      private int bitField3_;

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> attType_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAttTypeIsMutable() {
        if (!attType_.isModifiable()) {
          attType_ = makeMutableCopy(attType_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <code>repeated bytes attType = 1;</code>
       * @return A list containing the attType.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAttTypeList() {
        attType_.makeImmutable();
        return attType_;
      }
      /**
       * <code>repeated bytes attType = 1;</code>
       * @return The count of attType.
       */
      public int getAttTypeCount() {
        return attType_.size();
      }
      /**
       * <code>repeated bytes attType = 1;</code>
       * @param index The index of the element to return.
       * @return The attType at the given index.
       */
      public com.google.protobuf.ByteString getAttType(int index) {
        return attType_.get(index);
      }
      /**
       * <code>repeated bytes attType = 1;</code>
       * @param index The index to set the value at.
       * @param value The attType to set.
       * @return This builder for chaining.
       */
      public Builder setAttType(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAttTypeIsMutable();
        attType_.set(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attType = 1;</code>
       * @param value The attType to add.
       * @return This builder for chaining.
       */
      public Builder addAttType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAttTypeIsMutable();
        attType_.add(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attType = 1;</code>
       * @param values The attType to add.
       * @return This builder for chaining.
       */
      public Builder addAllAttType(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAttTypeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attType_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttType() {
        attType_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private int attTypeCnt_ ;
      /**
       * <code>optional uint32 attTypeCnt = 2;</code>
       * @return Whether the attTypeCnt field is set.
       */
      @java.lang.Override
      public boolean hasAttTypeCnt() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 attTypeCnt = 2;</code>
       * @return The attTypeCnt.
       */
      @java.lang.Override
      public int getAttTypeCnt() {
        return attTypeCnt_;
      }
      /**
       * <code>optional uint32 attTypeCnt = 2;</code>
       * @param value The attTypeCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAttTypeCnt(int value) {

        attTypeCnt_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 attTypeCnt = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttTypeCnt() {
        bitField0_ = (bitField0_ & ~0x00000002);
        attTypeCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> attFileName_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAttFileNameIsMutable() {
        if (!attFileName_.isModifiable()) {
          attFileName_ = makeMutableCopy(attFileName_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <code>repeated bytes attFileName = 3;</code>
       * @return A list containing the attFileName.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAttFileNameList() {
        attFileName_.makeImmutable();
        return attFileName_;
      }
      /**
       * <code>repeated bytes attFileName = 3;</code>
       * @return The count of attFileName.
       */
      public int getAttFileNameCount() {
        return attFileName_.size();
      }
      /**
       * <code>repeated bytes attFileName = 3;</code>
       * @param index The index of the element to return.
       * @return The attFileName at the given index.
       */
      public com.google.protobuf.ByteString getAttFileName(int index) {
        return attFileName_.get(index);
      }
      /**
       * <code>repeated bytes attFileName = 3;</code>
       * @param index The index to set the value at.
       * @param value The attFileName to set.
       * @return This builder for chaining.
       */
      public Builder setAttFileName(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAttFileNameIsMutable();
        attFileName_.set(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attFileName = 3;</code>
       * @param value The attFileName to add.
       * @return This builder for chaining.
       */
      public Builder addAttFileName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAttFileNameIsMutable();
        attFileName_.add(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attFileName = 3;</code>
       * @param values The attFileName to add.
       * @return This builder for chaining.
       */
      public Builder addAllAttFileName(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAttFileNameIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attFileName_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attFileName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttFileName() {
        attFileName_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private int attFileNameCnt_ ;
      /**
       * <code>optional uint32 attFileNameCnt = 4;</code>
       * @return Whether the attFileNameCnt field is set.
       */
      @java.lang.Override
      public boolean hasAttFileNameCnt() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 attFileNameCnt = 4;</code>
       * @return The attFileNameCnt.
       */
      @java.lang.Override
      public int getAttFileNameCnt() {
        return attFileNameCnt_;
      }
      /**
       * <code>optional uint32 attFileNameCnt = 4;</code>
       * @param value The attFileNameCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAttFileNameCnt(int value) {

        attFileNameCnt_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 attFileNameCnt = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttFileNameCnt() {
        bitField0_ = (bitField0_ & ~0x00000008);
        attFileNameCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList attConSize_ = emptyLongList();
      private void ensureAttConSizeIsMutable() {
        if (!attConSize_.isModifiable()) {
          attConSize_ = makeMutableCopy(attConSize_);
        }
        bitField0_ |= 0x00000010;
      }
      /**
       * <code>repeated uint64 attConSize = 5;</code>
       * @return A list containing the attConSize.
       */
      public java.util.List<java.lang.Long>
          getAttConSizeList() {
        attConSize_.makeImmutable();
        return attConSize_;
      }
      /**
       * <code>repeated uint64 attConSize = 5;</code>
       * @return The count of attConSize.
       */
      public int getAttConSizeCount() {
        return attConSize_.size();
      }
      /**
       * <code>repeated uint64 attConSize = 5;</code>
       * @param index The index of the element to return.
       * @return The attConSize at the given index.
       */
      public long getAttConSize(int index) {
        return attConSize_.getLong(index);
      }
      /**
       * <code>repeated uint64 attConSize = 5;</code>
       * @param index The index to set the value at.
       * @param value The attConSize to set.
       * @return This builder for chaining.
       */
      public Builder setAttConSize(
          int index, long value) {

        ensureAttConSizeIsMutable();
        attConSize_.setLong(index, value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 attConSize = 5;</code>
       * @param value The attConSize to add.
       * @return This builder for chaining.
       */
      public Builder addAttConSize(long value) {

        ensureAttConSizeIsMutable();
        attConSize_.addLong(value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 attConSize = 5;</code>
       * @param values The attConSize to add.
       * @return This builder for chaining.
       */
      public Builder addAllAttConSize(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureAttConSizeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attConSize_);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 attConSize = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttConSize() {
        attConSize_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> attMD5_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAttMD5IsMutable() {
        if (!attMD5_.isModifiable()) {
          attMD5_ = makeMutableCopy(attMD5_);
        }
        bitField0_ |= 0x00000020;
      }
      /**
       * <code>repeated bytes attMD5 = 6;</code>
       * @return A list containing the attMD5.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAttMD5List() {
        attMD5_.makeImmutable();
        return attMD5_;
      }
      /**
       * <code>repeated bytes attMD5 = 6;</code>
       * @return The count of attMD5.
       */
      public int getAttMD5Count() {
        return attMD5_.size();
      }
      /**
       * <code>repeated bytes attMD5 = 6;</code>
       * @param index The index of the element to return.
       * @return The attMD5 at the given index.
       */
      public com.google.protobuf.ByteString getAttMD5(int index) {
        return attMD5_.get(index);
      }
      /**
       * <code>repeated bytes attMD5 = 6;</code>
       * @param index The index to set the value at.
       * @param value The attMD5 to set.
       * @return This builder for chaining.
       */
      public Builder setAttMD5(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAttMD5IsMutable();
        attMD5_.set(index, value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attMD5 = 6;</code>
       * @param value The attMD5 to add.
       * @return This builder for chaining.
       */
      public Builder addAttMD5(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAttMD5IsMutable();
        attMD5_.add(value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attMD5 = 6;</code>
       * @param values The attMD5 to add.
       * @return This builder for chaining.
       */
      public Builder addAllAttMD5(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAttMD5IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, attMD5_);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes attMD5 = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttMD5() {
        attMD5_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }

      private int attMD5Cnt_ ;
      /**
       * <code>optional uint32 attMD5Cnt = 7;</code>
       * @return Whether the attMD5Cnt field is set.
       */
      @java.lang.Override
      public boolean hasAttMD5Cnt() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 attMD5Cnt = 7;</code>
       * @return The attMD5Cnt.
       */
      @java.lang.Override
      public int getAttMD5Cnt() {
        return attMD5Cnt_;
      }
      /**
       * <code>optional uint32 attMD5Cnt = 7;</code>
       * @param value The attMD5Cnt to set.
       * @return This builder for chaining.
       */
      public Builder setAttMD5Cnt(int value) {

        attMD5Cnt_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 attMD5Cnt = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttMD5Cnt() {
        bitField0_ = (bitField0_ & ~0x00000040);
        attMD5Cnt_ = 0;
        onChanged();
        return this;
      }

      private int authRelt_ ;
      /**
       * <code>optional uint32 authRelt = 8;</code>
       * @return Whether the authRelt field is set.
       */
      @java.lang.Override
      public boolean hasAuthRelt() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 authRelt = 8;</code>
       * @return The authRelt.
       */
      @java.lang.Override
      public int getAuthRelt() {
        return authRelt_;
      }
      /**
       * <code>optional uint32 authRelt = 8;</code>
       * @param value The authRelt to set.
       * @return This builder for chaining.
       */
      public Builder setAuthRelt(int value) {

        authRelt_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 authRelt = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthRelt() {
        bitField0_ = (bitField0_ & ~0x00000080);
        authRelt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> bCC_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureBCCIsMutable() {
        if (!bCC_.isModifiable()) {
          bCC_ = makeMutableCopy(bCC_);
        }
        bitField0_ |= 0x00000100;
      }
      /**
       * <code>repeated bytes BCC = 9;</code>
       * @return A list containing the bCC.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getBCCList() {
        bCC_.makeImmutable();
        return bCC_;
      }
      /**
       * <code>repeated bytes BCC = 9;</code>
       * @return The count of bCC.
       */
      public int getBCCCount() {
        return bCC_.size();
      }
      /**
       * <code>repeated bytes BCC = 9;</code>
       * @param index The index of the element to return.
       * @return The bCC at the given index.
       */
      public com.google.protobuf.ByteString getBCC(int index) {
        return bCC_.get(index);
      }
      /**
       * <code>repeated bytes BCC = 9;</code>
       * @param index The index to set the value at.
       * @param value The bCC to set.
       * @return This builder for chaining.
       */
      public Builder setBCC(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureBCCIsMutable();
        bCC_.set(index, value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes BCC = 9;</code>
       * @param value The bCC to add.
       * @return This builder for chaining.
       */
      public Builder addBCC(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureBCCIsMutable();
        bCC_.add(value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes BCC = 9;</code>
       * @param values The bCC to add.
       * @return This builder for chaining.
       */
      public Builder addAllBCC(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureBCCIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, bCC_);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes BCC = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearBCC() {
        bCC_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString body_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes body = 10;</code>
       * @return Whether the body field is set.
       */
      @java.lang.Override
      public boolean hasBody() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes body = 10;</code>
       * @return The body.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBody() {
        return body_;
      }
      /**
       * <code>optional bytes body = 10;</code>
       * @param value The body to set.
       * @return This builder for chaining.
       */
      public Builder setBody(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        body_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes body = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearBody() {
        bitField0_ = (bitField0_ & ~0x00000200);
        body_ = getDefaultInstance().getBody();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString bodyTexCha_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes bodyTexCha = 11;</code>
       * @return Whether the bodyTexCha field is set.
       */
      @java.lang.Override
      public boolean hasBodyTexCha() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes bodyTexCha = 11;</code>
       * @return The bodyTexCha.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBodyTexCha() {
        return bodyTexCha_;
      }
      /**
       * <code>optional bytes bodyTexCha = 11;</code>
       * @param value The bodyTexCha to set.
       * @return This builder for chaining.
       */
      public Builder setBodyTexCha(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        bodyTexCha_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes bodyTexCha = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyTexCha() {
        bitField0_ = (bitField0_ & ~0x00000400);
        bodyTexCha_ = getDefaultInstance().getBodyTexCha();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString bodyTraEnc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes bodyTraEnc = 12;</code>
       * @return Whether the bodyTraEnc field is set.
       */
      @java.lang.Override
      public boolean hasBodyTraEnc() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes bodyTraEnc = 12;</code>
       * @return The bodyTraEnc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBodyTraEnc() {
        return bodyTraEnc_;
      }
      /**
       * <code>optional bytes bodyTraEnc = 12;</code>
       * @param value The bodyTraEnc to set.
       * @return This builder for chaining.
       */
      public Builder setBodyTraEnc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        bodyTraEnc_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes bodyTraEnc = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyTraEnc() {
        bitField0_ = (bitField0_ & ~0x00000800);
        bodyTraEnc_ = getDefaultInstance().getBodyTraEnc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString bodyMD5_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes bodyMD5 = 13;</code>
       * @return Whether the bodyMD5 field is set.
       */
      @java.lang.Override
      public boolean hasBodyMD5() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes bodyMD5 = 13;</code>
       * @return The bodyMD5.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBodyMD5() {
        return bodyMD5_;
      }
      /**
       * <code>optional bytes bodyMD5 = 13;</code>
       * @param value The bodyMD5 to set.
       * @return This builder for chaining.
       */
      public Builder setBodyMD5(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        bodyMD5_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes bodyMD5 = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyMD5() {
        bitField0_ = (bitField0_ & ~0x00001000);
        bodyMD5_ = getDefaultInstance().getBodyMD5();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> bodyType_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureBodyTypeIsMutable() {
        if (!bodyType_.isModifiable()) {
          bodyType_ = makeMutableCopy(bodyType_);
        }
        bitField0_ |= 0x00002000;
      }
      /**
       * <code>repeated bytes bodyType = 14;</code>
       * @return A list containing the bodyType.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getBodyTypeList() {
        bodyType_.makeImmutable();
        return bodyType_;
      }
      /**
       * <code>repeated bytes bodyType = 14;</code>
       * @return The count of bodyType.
       */
      public int getBodyTypeCount() {
        return bodyType_.size();
      }
      /**
       * <code>repeated bytes bodyType = 14;</code>
       * @param index The index of the element to return.
       * @return The bodyType at the given index.
       */
      public com.google.protobuf.ByteString getBodyType(int index) {
        return bodyType_.get(index);
      }
      /**
       * <code>repeated bytes bodyType = 14;</code>
       * @param index The index to set the value at.
       * @param value The bodyType to set.
       * @return This builder for chaining.
       */
      public Builder setBodyType(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureBodyTypeIsMutable();
        bodyType_.set(index, value);
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes bodyType = 14;</code>
       * @param value The bodyType to add.
       * @return This builder for chaining.
       */
      public Builder addBodyType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureBodyTypeIsMutable();
        bodyType_.add(value);
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes bodyType = 14;</code>
       * @param values The bodyType to add.
       * @return This builder for chaining.
       */
      public Builder addAllBodyType(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureBodyTypeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, bodyType_);
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes bodyType = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyType() {
        bodyType_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00002000);
        onChanged();
        return this;
      }

      private int bodyTypeCnt_ ;
      /**
       * <code>optional uint32 bodyTypeCnt = 15;</code>
       * @return Whether the bodyTypeCnt field is set.
       */
      @java.lang.Override
      public boolean hasBodyTypeCnt() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 bodyTypeCnt = 15;</code>
       * @return The bodyTypeCnt.
       */
      @java.lang.Override
      public int getBodyTypeCnt() {
        return bodyTypeCnt_;
      }
      /**
       * <code>optional uint32 bodyTypeCnt = 15;</code>
       * @param value The bodyTypeCnt to set.
       * @return This builder for chaining.
       */
      public Builder setBodyTypeCnt(int value) {

        bodyTypeCnt_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 bodyTypeCnt = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyTypeCnt() {
        bitField0_ = (bitField0_ & ~0x00004000);
        bodyTypeCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString bodyURL_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes bodyURL = 16;</code>
       * @return Whether the bodyURL field is set.
       */
      @java.lang.Override
      public boolean hasBodyURL() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes bodyURL = 16;</code>
       * @return The bodyURL.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBodyURL() {
        return bodyURL_;
      }
      /**
       * <code>optional bytes bodyURL = 16;</code>
       * @param value The bodyURL to set.
       * @return This builder for chaining.
       */
      public Builder setBodyURL(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        bodyURL_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes bodyURL = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyURL() {
        bitField0_ = (bitField0_ & ~0x00008000);
        bodyURL_ = getDefaultInstance().getBodyURL();
        onChanged();
        return this;
      }

      private int bodyURLCnt_ ;
      /**
       * <code>optional uint32 bodyURLCnt = 17;</code>
       * @return Whether the bodyURLCnt field is set.
       */
      @java.lang.Override
      public boolean hasBodyURLCnt() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint32 bodyURLCnt = 17;</code>
       * @return The bodyURLCnt.
       */
      @java.lang.Override
      public int getBodyURLCnt() {
        return bodyURLCnt_;
      }
      /**
       * <code>optional uint32 bodyURLCnt = 17;</code>
       * @param value The bodyURLCnt to set.
       * @return This builder for chaining.
       */
      public Builder setBodyURLCnt(int value) {

        bodyURLCnt_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 bodyURLCnt = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyURLCnt() {
        bitField0_ = (bitField0_ & ~0x00010000);
        bodyURLCnt_ = 0;
        onChanged();
        return this;
      }

      private int bodyLen_ ;
      /**
       * <code>optional uint32 bodyLen = 18;</code>
       * @return Whether the bodyLen field is set.
       */
      @java.lang.Override
      public boolean hasBodyLen() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 bodyLen = 18;</code>
       * @return The bodyLen.
       */
      @java.lang.Override
      public int getBodyLen() {
        return bodyLen_;
      }
      /**
       * <code>optional uint32 bodyLen = 18;</code>
       * @param value The bodyLen to set.
       * @return This builder for chaining.
       */
      public Builder setBodyLen(int value) {

        bodyLen_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 bodyLen = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearBodyLen() {
        bitField0_ = (bitField0_ & ~0x00020000);
        bodyLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> byAsn_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureByAsnIsMutable() {
        if (!byAsn_.isModifiable()) {
          byAsn_ = makeMutableCopy(byAsn_);
        }
        bitField0_ |= 0x00040000;
      }
      /**
       * <code>repeated bytes ByAsn = 19;</code>
       * @return A list containing the byAsn.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getByAsnList() {
        byAsn_.makeImmutable();
        return byAsn_;
      }
      /**
       * <code>repeated bytes ByAsn = 19;</code>
       * @return The count of byAsn.
       */
      public int getByAsnCount() {
        return byAsn_.size();
      }
      /**
       * <code>repeated bytes ByAsn = 19;</code>
       * @param index The index of the element to return.
       * @return The byAsn at the given index.
       */
      public com.google.protobuf.ByteString getByAsn(int index) {
        return byAsn_.get(index);
      }
      /**
       * <code>repeated bytes ByAsn = 19;</code>
       * @param index The index to set the value at.
       * @param value The byAsn to set.
       * @return This builder for chaining.
       */
      public Builder setByAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureByAsnIsMutable();
        byAsn_.set(index, value);
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByAsn = 19;</code>
       * @param value The byAsn to add.
       * @return This builder for chaining.
       */
      public Builder addByAsn(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureByAsnIsMutable();
        byAsn_.add(value);
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByAsn = 19;</code>
       * @param values The byAsn to add.
       * @return This builder for chaining.
       */
      public Builder addAllByAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureByAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, byAsn_);
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByAsn = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearByAsn() {
        byAsn_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> byCountry_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureByCountryIsMutable() {
        if (!byCountry_.isModifiable()) {
          byCountry_ = makeMutableCopy(byCountry_);
        }
        bitField0_ |= 0x00080000;
      }
      /**
       * <code>repeated bytes ByCountry = 20;</code>
       * @return A list containing the byCountry.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getByCountryList() {
        byCountry_.makeImmutable();
        return byCountry_;
      }
      /**
       * <code>repeated bytes ByCountry = 20;</code>
       * @return The count of byCountry.
       */
      public int getByCountryCount() {
        return byCountry_.size();
      }
      /**
       * <code>repeated bytes ByCountry = 20;</code>
       * @param index The index of the element to return.
       * @return The byCountry at the given index.
       */
      public com.google.protobuf.ByteString getByCountry(int index) {
        return byCountry_.get(index);
      }
      /**
       * <code>repeated bytes ByCountry = 20;</code>
       * @param index The index to set the value at.
       * @param value The byCountry to set.
       * @return This builder for chaining.
       */
      public Builder setByCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureByCountryIsMutable();
        byCountry_.set(index, value);
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByCountry = 20;</code>
       * @param value The byCountry to add.
       * @return This builder for chaining.
       */
      public Builder addByCountry(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureByCountryIsMutable();
        byCountry_.add(value);
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByCountry = 20;</code>
       * @param values The byCountry to add.
       * @return This builder for chaining.
       */
      public Builder addAllByCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureByCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, byCountry_);
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByCountry = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearByCountry() {
        byCountry_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> byDom_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureByDomIsMutable() {
        if (!byDom_.isModifiable()) {
          byDom_ = makeMutableCopy(byDom_);
        }
        bitField0_ |= 0x00100000;
      }
      /**
       * <code>repeated bytes ByDom = 21;</code>
       * @return A list containing the byDom.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getByDomList() {
        byDom_.makeImmutable();
        return byDom_;
      }
      /**
       * <code>repeated bytes ByDom = 21;</code>
       * @return The count of byDom.
       */
      public int getByDomCount() {
        return byDom_.size();
      }
      /**
       * <code>repeated bytes ByDom = 21;</code>
       * @param index The index of the element to return.
       * @return The byDom at the given index.
       */
      public com.google.protobuf.ByteString getByDom(int index) {
        return byDom_.get(index);
      }
      /**
       * <code>repeated bytes ByDom = 21;</code>
       * @param index The index to set the value at.
       * @param value The byDom to set.
       * @return This builder for chaining.
       */
      public Builder setByDom(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureByDomIsMutable();
        byDom_.set(index, value);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByDom = 21;</code>
       * @param value The byDom to add.
       * @return This builder for chaining.
       */
      public Builder addByDom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureByDomIsMutable();
        byDom_.add(value);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByDom = 21;</code>
       * @param values The byDom to add.
       * @return This builder for chaining.
       */
      public Builder addAllByDom(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureByDomIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, byDom_);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ByDom = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearByDom() {
        byDom_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }

      private int byDomCnt_ ;
      /**
       * <code>optional uint32 ByDomCnt = 22;</code>
       * @return Whether the byDomCnt field is set.
       */
      @java.lang.Override
      public boolean hasByDomCnt() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 ByDomCnt = 22;</code>
       * @return The byDomCnt.
       */
      @java.lang.Override
      public int getByDomCnt() {
        return byDomCnt_;
      }
      /**
       * <code>optional uint32 ByDomCnt = 22;</code>
       * @param value The byDomCnt to set.
       * @return This builder for chaining.
       */
      public Builder setByDomCnt(int value) {

        byDomCnt_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ByDomCnt = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearByDomCnt() {
        bitField0_ = (bitField0_ & ~0x00200000);
        byDomCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString byIP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ByIP = 23;</code>
       * @return Whether the byIP field is set.
       */
      @java.lang.Override
      public boolean hasByIP() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes ByIP = 23;</code>
       * @return The byIP.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getByIP() {
        return byIP_;
      }
      /**
       * <code>optional bytes ByIP = 23;</code>
       * @param value The byIP to set.
       * @return This builder for chaining.
       */
      public Builder setByIP(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        byIP_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ByIP = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearByIP() {
        bitField0_ = (bitField0_ & ~0x00400000);
        byIP_ = getDefaultInstance().getByIP();
        onChanged();
        return this;
      }

      private int byIpCnt_ ;
      /**
       * <code>optional uint32 ByIpCnt = 24;</code>
       * @return Whether the byIpCnt field is set.
       */
      @java.lang.Override
      public boolean hasByIpCnt() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional uint32 ByIpCnt = 24;</code>
       * @return The byIpCnt.
       */
      @java.lang.Override
      public int getByIpCnt() {
        return byIpCnt_;
      }
      /**
       * <code>optional uint32 ByIpCnt = 24;</code>
       * @param value The byIpCnt to set.
       * @return This builder for chaining.
       */
      public Builder setByIpCnt(int value) {

        byIpCnt_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ByIpCnt = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearByIpCnt() {
        bitField0_ = (bitField0_ & ~0x00800000);
        byIpCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> cC_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureCCIsMutable() {
        if (!cC_.isModifiable()) {
          cC_ = makeMutableCopy(cC_);
        }
        bitField0_ |= 0x01000000;
      }
      /**
       * <code>repeated bytes CC = 25;</code>
       * @return A list containing the cC.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getCCList() {
        cC_.makeImmutable();
        return cC_;
      }
      /**
       * <code>repeated bytes CC = 25;</code>
       * @return The count of cC.
       */
      public int getCCCount() {
        return cC_.size();
      }
      /**
       * <code>repeated bytes CC = 25;</code>
       * @param index The index of the element to return.
       * @return The cC at the given index.
       */
      public com.google.protobuf.ByteString getCC(int index) {
        return cC_.get(index);
      }
      /**
       * <code>repeated bytes CC = 25;</code>
       * @param index The index to set the value at.
       * @param value The cC to set.
       * @return This builder for chaining.
       */
      public Builder setCC(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCCIsMutable();
        cC_.set(index, value);
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes CC = 25;</code>
       * @param value The cC to add.
       * @return This builder for chaining.
       */
      public Builder addCC(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCCIsMutable();
        cC_.add(value);
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes CC = 25;</code>
       * @param values The cC to add.
       * @return This builder for chaining.
       */
      public Builder addAllCC(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureCCIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cC_);
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes CC = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearCC() {
        cC_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x01000000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> cCAli_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureCCAliIsMutable() {
        if (!cCAli_.isModifiable()) {
          cCAli_ = makeMutableCopy(cCAli_);
        }
        bitField0_ |= 0x02000000;
      }
      /**
       * <code>repeated bytes CCAli = 26;</code>
       * @return A list containing the cCAli.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getCCAliList() {
        cCAli_.makeImmutable();
        return cCAli_;
      }
      /**
       * <code>repeated bytes CCAli = 26;</code>
       * @return The count of cCAli.
       */
      public int getCCAliCount() {
        return cCAli_.size();
      }
      /**
       * <code>repeated bytes CCAli = 26;</code>
       * @param index The index of the element to return.
       * @return The cCAli at the given index.
       */
      public com.google.protobuf.ByteString getCCAli(int index) {
        return cCAli_.get(index);
      }
      /**
       * <code>repeated bytes CCAli = 26;</code>
       * @param index The index to set the value at.
       * @param value The cCAli to set.
       * @return This builder for chaining.
       */
      public Builder setCCAli(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCCAliIsMutable();
        cCAli_.set(index, value);
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes CCAli = 26;</code>
       * @param value The cCAli to add.
       * @return This builder for chaining.
       */
      public Builder addCCAli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCCAliIsMutable();
        cCAli_.add(value);
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes CCAli = 26;</code>
       * @param values The cCAli to add.
       * @return This builder for chaining.
       */
      public Builder addAllCCAli(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureCCAliIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cCAli_);
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes CCAli = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearCCAli() {
        cCAli_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x02000000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> command_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureCommandIsMutable() {
        if (!command_.isModifiable()) {
          command_ = makeMutableCopy(command_);
        }
        bitField0_ |= 0x04000000;
      }
      /**
       * <code>repeated bytes Command = 27;</code>
       * @return A list containing the command.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getCommandList() {
        command_.makeImmutable();
        return command_;
      }
      /**
       * <code>repeated bytes Command = 27;</code>
       * @return The count of command.
       */
      public int getCommandCount() {
        return command_.size();
      }
      /**
       * <code>repeated bytes Command = 27;</code>
       * @param index The index of the element to return.
       * @return The command at the given index.
       */
      public com.google.protobuf.ByteString getCommand(int index) {
        return command_.get(index);
      }
      /**
       * <code>repeated bytes Command = 27;</code>
       * @param index The index to set the value at.
       * @param value The command to set.
       * @return This builder for chaining.
       */
      public Builder setCommand(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCommandIsMutable();
        command_.set(index, value);
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes Command = 27;</code>
       * @param value The command to add.
       * @return This builder for chaining.
       */
      public Builder addCommand(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureCommandIsMutable();
        command_.add(value);
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes Command = 27;</code>
       * @param values The command to add.
       * @return This builder for chaining.
       */
      public Builder addAllCommand(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureCommandIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, command_);
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes Command = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommand() {
        command_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x04000000);
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <code>optional uint32 count = 28;</code>
       * @return Whether the count field is set.
       */
      @java.lang.Override
      public boolean hasCount() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint32 count = 28;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <code>optional uint32 count = 28;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {

        count_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 count = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        bitField0_ = (bitField0_ & ~0x08000000);
        count_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString content_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes content = 29;</code>
       * @return Whether the content field is set.
       */
      @java.lang.Override
      public boolean hasContent() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes content = 29;</code>
       * @return The content.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getContent() {
        return content_;
      }
      /**
       * <code>optional bytes content = 29;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        content_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes content = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        bitField0_ = (bitField0_ & ~0x10000000);
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> conType_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureConTypeIsMutable() {
        if (!conType_.isModifiable()) {
          conType_ = makeMutableCopy(conType_);
        }
        bitField0_ |= 0x20000000;
      }
      /**
       * <code>repeated bytes conType = 30;</code>
       * @return A list containing the conType.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getConTypeList() {
        conType_.makeImmutable();
        return conType_;
      }
      /**
       * <code>repeated bytes conType = 30;</code>
       * @return The count of conType.
       */
      public int getConTypeCount() {
        return conType_.size();
      }
      /**
       * <code>repeated bytes conType = 30;</code>
       * @param index The index of the element to return.
       * @return The conType at the given index.
       */
      public com.google.protobuf.ByteString getConType(int index) {
        return conType_.get(index);
      }
      /**
       * <code>repeated bytes conType = 30;</code>
       * @param index The index to set the value at.
       * @param value The conType to set.
       * @return This builder for chaining.
       */
      public Builder setConType(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureConTypeIsMutable();
        conType_.set(index, value);
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes conType = 30;</code>
       * @param value The conType to add.
       * @return This builder for chaining.
       */
      public Builder addConType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureConTypeIsMutable();
        conType_.add(value);
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes conType = 30;</code>
       * @param values The conType to add.
       * @return This builder for chaining.
       */
      public Builder addAllConType(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureConTypeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, conType_);
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes conType = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearConType() {
        conType_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x20000000);
        onChanged();
        return this;
      }

      private int conTypeCnt_ ;
      /**
       * <code>optional uint32 conTypeCnt = 31;</code>
       * @return Whether the conTypeCnt field is set.
       */
      @java.lang.Override
      public boolean hasConTypeCnt() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional uint32 conTypeCnt = 31;</code>
       * @return The conTypeCnt.
       */
      @java.lang.Override
      public int getConTypeCnt() {
        return conTypeCnt_;
      }
      /**
       * <code>optional uint32 conTypeCnt = 31;</code>
       * @param value The conTypeCnt to set.
       * @return This builder for chaining.
       */
      public Builder setConTypeCnt(int value) {

        conTypeCnt_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conTypeCnt = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearConTypeCnt() {
        bitField0_ = (bitField0_ & ~0x40000000);
        conTypeCnt_ = 0;
        onChanged();
        return this;
      }

      private int date_ ;
      /**
       * <code>optional uint32 date = 32;</code>
       * @return Whether the date field is set.
       */
      @java.lang.Override
      public boolean hasDate() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 date = 32;</code>
       * @return The date.
       */
      @java.lang.Override
      public int getDate() {
        return date_;
      }
      /**
       * <code>optional uint32 date = 32;</code>
       * @param value The date to set.
       * @return This builder for chaining.
       */
      public Builder setDate(int value) {

        date_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 date = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearDate() {
        bitField0_ = (bitField0_ & ~0x80000000);
        date_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString deliveredTo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes deliveredTo = 33;</code>
       * @return Whether the deliveredTo field is set.
       */
      @java.lang.Override
      public boolean hasDeliveredTo() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes deliveredTo = 33;</code>
       * @return The deliveredTo.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDeliveredTo() {
        return deliveredTo_;
      }
      /**
       * <code>optional bytes deliveredTo = 33;</code>
       * @param value The deliveredTo to set.
       * @return This builder for chaining.
       */
      public Builder setDeliveredTo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        deliveredTo_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes deliveredTo = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeliveredTo() {
        bitField1_ = (bitField1_ & ~0x00000001);
        deliveredTo_ = getDefaultInstance().getDeliveredTo();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> fromAsn_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureFromAsnIsMutable() {
        if (!fromAsn_.isModifiable()) {
          fromAsn_ = makeMutableCopy(fromAsn_);
        }
        bitField1_ |= 0x00000002;
      }
      /**
       * <code>repeated bytes FromAsn = 34;</code>
       * @return A list containing the fromAsn.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getFromAsnList() {
        fromAsn_.makeImmutable();
        return fromAsn_;
      }
      /**
       * <code>repeated bytes FromAsn = 34;</code>
       * @return The count of fromAsn.
       */
      public int getFromAsnCount() {
        return fromAsn_.size();
      }
      /**
       * <code>repeated bytes FromAsn = 34;</code>
       * @param index The index of the element to return.
       * @return The fromAsn at the given index.
       */
      public com.google.protobuf.ByteString getFromAsn(int index) {
        return fromAsn_.get(index);
      }
      /**
       * <code>repeated bytes FromAsn = 34;</code>
       * @param index The index to set the value at.
       * @param value The fromAsn to set.
       * @return This builder for chaining.
       */
      public Builder setFromAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureFromAsnIsMutable();
        fromAsn_.set(index, value);
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromAsn = 34;</code>
       * @param value The fromAsn to add.
       * @return This builder for chaining.
       */
      public Builder addFromAsn(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureFromAsnIsMutable();
        fromAsn_.add(value);
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromAsn = 34;</code>
       * @param values The fromAsn to add.
       * @return This builder for chaining.
       */
      public Builder addAllFromAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureFromAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fromAsn_);
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromAsn = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromAsn() {
        fromAsn_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00000002);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> fromCountry_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureFromCountryIsMutable() {
        if (!fromCountry_.isModifiable()) {
          fromCountry_ = makeMutableCopy(fromCountry_);
        }
        bitField1_ |= 0x00000004;
      }
      /**
       * <code>repeated bytes FromCountry = 35;</code>
       * @return A list containing the fromCountry.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getFromCountryList() {
        fromCountry_.makeImmutable();
        return fromCountry_;
      }
      /**
       * <code>repeated bytes FromCountry = 35;</code>
       * @return The count of fromCountry.
       */
      public int getFromCountryCount() {
        return fromCountry_.size();
      }
      /**
       * <code>repeated bytes FromCountry = 35;</code>
       * @param index The index of the element to return.
       * @return The fromCountry at the given index.
       */
      public com.google.protobuf.ByteString getFromCountry(int index) {
        return fromCountry_.get(index);
      }
      /**
       * <code>repeated bytes FromCountry = 35;</code>
       * @param index The index to set the value at.
       * @param value The fromCountry to set.
       * @return This builder for chaining.
       */
      public Builder setFromCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureFromCountryIsMutable();
        fromCountry_.set(index, value);
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromCountry = 35;</code>
       * @param value The fromCountry to add.
       * @return This builder for chaining.
       */
      public Builder addFromCountry(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureFromCountryIsMutable();
        fromCountry_.add(value);
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromCountry = 35;</code>
       * @param values The fromCountry to add.
       * @return This builder for chaining.
       */
      public Builder addAllFromCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureFromCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fromCountry_);
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromCountry = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromCountry() {
        fromCountry_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00000004);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> fromDom_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureFromDomIsMutable() {
        if (!fromDom_.isModifiable()) {
          fromDom_ = makeMutableCopy(fromDom_);
        }
        bitField1_ |= 0x00000008;
      }
      /**
       * <code>repeated bytes FromDom = 36;</code>
       * @return A list containing the fromDom.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getFromDomList() {
        fromDom_.makeImmutable();
        return fromDom_;
      }
      /**
       * <code>repeated bytes FromDom = 36;</code>
       * @return The count of fromDom.
       */
      public int getFromDomCount() {
        return fromDom_.size();
      }
      /**
       * <code>repeated bytes FromDom = 36;</code>
       * @param index The index of the element to return.
       * @return The fromDom at the given index.
       */
      public com.google.protobuf.ByteString getFromDom(int index) {
        return fromDom_.get(index);
      }
      /**
       * <code>repeated bytes FromDom = 36;</code>
       * @param index The index to set the value at.
       * @param value The fromDom to set.
       * @return This builder for chaining.
       */
      public Builder setFromDom(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureFromDomIsMutable();
        fromDom_.set(index, value);
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromDom = 36;</code>
       * @param value The fromDom to add.
       * @return This builder for chaining.
       */
      public Builder addFromDom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureFromDomIsMutable();
        fromDom_.add(value);
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromDom = 36;</code>
       * @param values The fromDom to add.
       * @return This builder for chaining.
       */
      public Builder addAllFromDom(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureFromDomIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fromDom_);
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes FromDom = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromDom() {
        fromDom_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00000008);
        onChanged();
        return this;
      }

      private int fromDomCnt_ ;
      /**
       * <code>optional uint32 FromDomCnt = 37;</code>
       * @return Whether the fromDomCnt field is set.
       */
      @java.lang.Override
      public boolean hasFromDomCnt() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 FromDomCnt = 37;</code>
       * @return The fromDomCnt.
       */
      @java.lang.Override
      public int getFromDomCnt() {
        return fromDomCnt_;
      }
      /**
       * <code>optional uint32 FromDomCnt = 37;</code>
       * @param value The fromDomCnt to set.
       * @return This builder for chaining.
       */
      public Builder setFromDomCnt(int value) {

        fromDomCnt_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 FromDomCnt = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromDomCnt() {
        bitField1_ = (bitField1_ & ~0x00000010);
        fromDomCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList fromIp_ = emptyIntList();
      private void ensureFromIpIsMutable() {
        if (!fromIp_.isModifiable()) {
          fromIp_ = makeMutableCopy(fromIp_);
        }
        bitField1_ |= 0x00000020;
      }
      /**
       * <code>repeated uint32 FromIp = 38;</code>
       * @return A list containing the fromIp.
       */
      public java.util.List<java.lang.Integer>
          getFromIpList() {
        fromIp_.makeImmutable();
        return fromIp_;
      }
      /**
       * <code>repeated uint32 FromIp = 38;</code>
       * @return The count of fromIp.
       */
      public int getFromIpCount() {
        return fromIp_.size();
      }
      /**
       * <code>repeated uint32 FromIp = 38;</code>
       * @param index The index of the element to return.
       * @return The fromIp at the given index.
       */
      public int getFromIp(int index) {
        return fromIp_.getInt(index);
      }
      /**
       * <code>repeated uint32 FromIp = 38;</code>
       * @param index The index to set the value at.
       * @param value The fromIp to set.
       * @return This builder for chaining.
       */
      public Builder setFromIp(
          int index, int value) {

        ensureFromIpIsMutable();
        fromIp_.setInt(index, value);
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 FromIp = 38;</code>
       * @param value The fromIp to add.
       * @return This builder for chaining.
       */
      public Builder addFromIp(int value) {

        ensureFromIpIsMutable();
        fromIp_.addInt(value);
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 FromIp = 38;</code>
       * @param values The fromIp to add.
       * @return This builder for chaining.
       */
      public Builder addAllFromIp(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureFromIpIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, fromIp_);
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 FromIp = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromIp() {
        fromIp_ = emptyIntList();
        bitField1_ = (bitField1_ & ~0x00000020);
        onChanged();
        return this;
      }

      private int fromIpCnt_ ;
      /**
       * <code>optional uint32 FromIpCnt = 39;</code>
       * @return Whether the fromIpCnt field is set.
       */
      @java.lang.Override
      public boolean hasFromIpCnt() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 FromIpCnt = 39;</code>
       * @return The fromIpCnt.
       */
      @java.lang.Override
      public int getFromIpCnt() {
        return fromIpCnt_;
      }
      /**
       * <code>optional uint32 FromIpCnt = 39;</code>
       * @param value The fromIpCnt to set.
       * @return This builder for chaining.
       */
      public Builder setFromIpCnt(int value) {

        fromIpCnt_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 FromIpCnt = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearFromIpCnt() {
        bitField1_ = (bitField1_ & ~0x00000040);
        fromIpCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> headSet_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureHeadSetIsMutable() {
        if (!headSet_.isModifiable()) {
          headSet_ = makeMutableCopy(headSet_);
        }
        bitField1_ |= 0x00000080;
      }
      /**
       * <code>repeated bytes headSet = 40;</code>
       * @return A list containing the headSet.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getHeadSetList() {
        headSet_.makeImmutable();
        return headSet_;
      }
      /**
       * <code>repeated bytes headSet = 40;</code>
       * @return The count of headSet.
       */
      public int getHeadSetCount() {
        return headSet_.size();
      }
      /**
       * <code>repeated bytes headSet = 40;</code>
       * @param index The index of the element to return.
       * @return The headSet at the given index.
       */
      public com.google.protobuf.ByteString getHeadSet(int index) {
        return headSet_.get(index);
      }
      /**
       * <code>repeated bytes headSet = 40;</code>
       * @param index The index to set the value at.
       * @param value The headSet to set.
       * @return This builder for chaining.
       */
      public Builder setHeadSet(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureHeadSetIsMutable();
        headSet_.set(index, value);
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes headSet = 40;</code>
       * @param value The headSet to add.
       * @return This builder for chaining.
       */
      public Builder addHeadSet(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureHeadSetIsMutable();
        headSet_.add(value);
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes headSet = 40;</code>
       * @param values The headSet to add.
       * @return This builder for chaining.
       */
      public Builder addAllHeadSet(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureHeadSetIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, headSet_);
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes headSet = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeadSet() {
        headSet_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00000080);
        onChanged();
        return this;
      }

      private int headSetCnt_ ;
      /**
       * <code>optional uint32 headSetCnt = 41;</code>
       * @return Whether the headSetCnt field is set.
       */
      @java.lang.Override
      public boolean hasHeadSetCnt() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 headSetCnt = 41;</code>
       * @return The headSetCnt.
       */
      @java.lang.Override
      public int getHeadSetCnt() {
        return headSetCnt_;
      }
      /**
       * <code>optional uint32 headSetCnt = 41;</code>
       * @param value The headSetCnt to set.
       * @return This builder for chaining.
       */
      public Builder setHeadSetCnt(int value) {

        headSetCnt_ = value;
        bitField1_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 headSetCnt = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeadSetCnt() {
        bitField1_ = (bitField1_ & ~0x00000100);
        headSetCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString host_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes host = 42;</code>
       * @return Whether the host field is set.
       */
      @java.lang.Override
      public boolean hasHost() {
        return ((bitField1_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes host = 42;</code>
       * @return The host.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getHost() {
        return host_;
      }
      /**
       * <code>optional bytes host = 42;</code>
       * @param value The host to set.
       * @return This builder for chaining.
       */
      public Builder setHost(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        host_ = value;
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes host = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearHost() {
        bitField1_ = (bitField1_ & ~0x00000200);
        host_ = getDefaultInstance().getHost();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString name_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes name = 43;</code>
       * @return Whether the name field is set.
       */
      @java.lang.Override
      public boolean hasName() {
        return ((bitField1_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes name = 43;</code>
       * @return The name.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getName() {
        return name_;
      }
      /**
       * <code>optional bytes name = 43;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        name_ = value;
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes name = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField1_ = (bitField1_ & ~0x00000400);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString os_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes os = 44;</code>
       * @return Whether the os field is set.
       */
      @java.lang.Override
      public boolean hasOs() {
        return ((bitField1_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes os = 44;</code>
       * @return The os.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getOs() {
        return os_;
      }
      /**
       * <code>optional bytes os = 44;</code>
       * @param value The os to set.
       * @return This builder for chaining.
       */
      public Builder setOs(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        os_ = value;
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes os = 44;</code>
       * @return This builder for chaining.
       */
      public Builder clearOs() {
        bitField1_ = (bitField1_ & ~0x00000800);
        os_ = getDefaultInstance().getOs();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString osVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes osVer = 45;</code>
       * @return Whether the osVer field is set.
       */
      @java.lang.Override
      public boolean hasOsVer() {
        return ((bitField1_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes osVer = 45;</code>
       * @return The osVer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getOsVer() {
        return osVer_;
      }
      /**
       * <code>optional bytes osVer = 45;</code>
       * @param value The osVer to set.
       * @return This builder for chaining.
       */
      public Builder setOsVer(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        osVer_ = value;
        bitField1_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes osVer = 45;</code>
       * @return This builder for chaining.
       */
      public Builder clearOsVer() {
        bitField1_ = (bitField1_ & ~0x00001000);
        osVer_ = getDefaultInstance().getOsVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString vendor_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes vendor = 46;</code>
       * @return Whether the vendor field is set.
       */
      @java.lang.Override
      public boolean hasVendor() {
        return ((bitField1_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes vendor = 46;</code>
       * @return The vendor.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getVendor() {
        return vendor_;
      }
      /**
       * <code>optional bytes vendor = 46;</code>
       * @param value The vendor to set.
       * @return This builder for chaining.
       */
      public Builder setVendor(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        vendor_ = value;
        bitField1_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes vendor = 46;</code>
       * @return This builder for chaining.
       */
      public Builder clearVendor() {
        bitField1_ = (bitField1_ & ~0x00002000);
        vendor_ = getDefaultInstance().getVendor();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ver_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ver = 47;</code>
       * @return Whether the ver field is set.
       */
      @java.lang.Override
      public boolean hasVer() {
        return ((bitField1_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes ver = 47;</code>
       * @return The ver.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getVer() {
        return ver_;
      }
      /**
       * <code>optional bytes ver = 47;</code>
       * @param value The ver to set.
       * @return This builder for chaining.
       */
      public Builder setVer(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ver_ = value;
        bitField1_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ver = 47;</code>
       * @return This builder for chaining.
       */
      public Builder clearVer() {
        bitField1_ = (bitField1_ & ~0x00004000);
        ver_ = getDefaultInstance().getVer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString emaInd_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes emaInd = 48;</code>
       * @return Whether the emaInd field is set.
       */
      @java.lang.Override
      public boolean hasEmaInd() {
        return ((bitField1_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes emaInd = 48;</code>
       * @return The emaInd.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEmaInd() {
        return emaInd_;
      }
      /**
       * <code>optional bytes emaInd = 48;</code>
       * @param value The emaInd to set.
       * @return This builder for chaining.
       */
      public Builder setEmaInd(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emaInd_ = value;
        bitField1_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes emaInd = 48;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmaInd() {
        bitField1_ = (bitField1_ & ~0x00008000);
        emaInd_ = getDefaultInstance().getEmaInd();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString login_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes login = 49;</code>
       * @return Whether the login field is set.
       */
      @java.lang.Override
      public boolean hasLogin() {
        return ((bitField1_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes login = 49;</code>
       * @return The login.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getLogin() {
        return login_;
      }
      /**
       * <code>optional bytes login = 49;</code>
       * @param value The login to set.
       * @return This builder for chaining.
       */
      public Builder setLogin(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        login_ = value;
        bitField1_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes login = 49;</code>
       * @return This builder for chaining.
       */
      public Builder clearLogin() {
        bitField1_ = (bitField1_ & ~0x00010000);
        login_ = getDefaultInstance().getLogin();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString loginsrv_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes loginsrv = 50;</code>
       * @return Whether the loginsrv field is set.
       */
      @java.lang.Override
      public boolean hasLoginsrv() {
        return ((bitField1_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes loginsrv = 50;</code>
       * @return The loginsrv.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getLoginsrv() {
        return loginsrv_;
      }
      /**
       * <code>optional bytes loginsrv = 50;</code>
       * @param value The loginsrv to set.
       * @return This builder for chaining.
       */
      public Builder setLoginsrv(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        loginsrv_ = value;
        bitField1_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes loginsrv = 50;</code>
       * @return This builder for chaining.
       */
      public Builder clearLoginsrv() {
        bitField1_ = (bitField1_ & ~0x00020000);
        loginsrv_ = getDefaultInstance().getLoginsrv();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mailFrom_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureMailFromIsMutable() {
        if (!mailFrom_.isModifiable()) {
          mailFrom_ = makeMutableCopy(mailFrom_);
        }
        bitField1_ |= 0x00040000;
      }
      /**
       * <code>repeated bytes mailFrom = 51;</code>
       * @return A list containing the mailFrom.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMailFromList() {
        mailFrom_.makeImmutable();
        return mailFrom_;
      }
      /**
       * <code>repeated bytes mailFrom = 51;</code>
       * @return The count of mailFrom.
       */
      public int getMailFromCount() {
        return mailFrom_.size();
      }
      /**
       * <code>repeated bytes mailFrom = 51;</code>
       * @param index The index of the element to return.
       * @return The mailFrom at the given index.
       */
      public com.google.protobuf.ByteString getMailFrom(int index) {
        return mailFrom_.get(index);
      }
      /**
       * <code>repeated bytes mailFrom = 51;</code>
       * @param index The index to set the value at.
       * @param value The mailFrom to set.
       * @return This builder for chaining.
       */
      public Builder setMailFrom(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMailFromIsMutable();
        mailFrom_.set(index, value);
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailFrom = 51;</code>
       * @param value The mailFrom to add.
       * @return This builder for chaining.
       */
      public Builder addMailFrom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMailFromIsMutable();
        mailFrom_.add(value);
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailFrom = 51;</code>
       * @param values The mailFrom to add.
       * @return This builder for chaining.
       */
      public Builder addAllMailFrom(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMailFromIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mailFrom_);
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailFrom = 51;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailFrom() {
        mailFrom_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00040000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mailFromDom_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureMailFromDomIsMutable() {
        if (!mailFromDom_.isModifiable()) {
          mailFromDom_ = makeMutableCopy(mailFromDom_);
        }
        bitField1_ |= 0x00080000;
      }
      /**
       * <code>repeated bytes mailFromDom = 52;</code>
       * @return A list containing the mailFromDom.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMailFromDomList() {
        mailFromDom_.makeImmutable();
        return mailFromDom_;
      }
      /**
       * <code>repeated bytes mailFromDom = 52;</code>
       * @return The count of mailFromDom.
       */
      public int getMailFromDomCount() {
        return mailFromDom_.size();
      }
      /**
       * <code>repeated bytes mailFromDom = 52;</code>
       * @param index The index of the element to return.
       * @return The mailFromDom at the given index.
       */
      public com.google.protobuf.ByteString getMailFromDom(int index) {
        return mailFromDom_.get(index);
      }
      /**
       * <code>repeated bytes mailFromDom = 52;</code>
       * @param index The index to set the value at.
       * @param value The mailFromDom to set.
       * @return This builder for chaining.
       */
      public Builder setMailFromDom(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMailFromDomIsMutable();
        mailFromDom_.set(index, value);
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailFromDom = 52;</code>
       * @param value The mailFromDom to add.
       * @return This builder for chaining.
       */
      public Builder addMailFromDom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMailFromDomIsMutable();
        mailFromDom_.add(value);
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailFromDom = 52;</code>
       * @param values The mailFromDom to add.
       * @return This builder for chaining.
       */
      public Builder addAllMailFromDom(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMailFromDomIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mailFromDom_);
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailFromDom = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailFromDom() {
        mailFromDom_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00080000);
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString mailFromDomCnt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes mailFromDomCnt = 53;</code>
       * @return Whether the mailFromDomCnt field is set.
       */
      @java.lang.Override
      public boolean hasMailFromDomCnt() {
        return ((bitField1_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes mailFromDomCnt = 53;</code>
       * @return The mailFromDomCnt.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMailFromDomCnt() {
        return mailFromDomCnt_;
      }
      /**
       * <code>optional bytes mailFromDomCnt = 53;</code>
       * @param value The mailFromDomCnt to set.
       * @return This builder for chaining.
       */
      public Builder setMailFromDomCnt(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        mailFromDomCnt_ = value;
        bitField1_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes mailFromDomCnt = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailFromDomCnt() {
        bitField1_ = (bitField1_ & ~0x00100000);
        mailFromDomCnt_ = getDefaultInstance().getMailFromDomCnt();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString mimeVer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes mimeVer = 54;</code>
       * @return Whether the mimeVer field is set.
       */
      @java.lang.Override
      public boolean hasMimeVer() {
        return ((bitField1_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes mimeVer = 54;</code>
       * @return The mimeVer.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMimeVer() {
        return mimeVer_;
      }
      /**
       * <code>optional bytes mimeVer = 54;</code>
       * @param value The mimeVer to set.
       * @return This builder for chaining.
       */
      public Builder setMimeVer(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        mimeVer_ = value;
        bitField1_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes mimeVer = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearMimeVer() {
        bitField1_ = (bitField1_ & ~0x00200000);
        mimeVer_ = getDefaultInstance().getMimeVer();
        onChanged();
        return this;
      }

      private int mimeVerCnt_ ;
      /**
       * <code>optional uint32 mimeVerCnt = 55;</code>
       * @return Whether the mimeVerCnt field is set.
       */
      @java.lang.Override
      public boolean hasMimeVerCnt() {
        return ((bitField1_ & 0x00400000) != 0);
      }
      /**
       * <code>optional uint32 mimeVerCnt = 55;</code>
       * @return The mimeVerCnt.
       */
      @java.lang.Override
      public int getMimeVerCnt() {
        return mimeVerCnt_;
      }
      /**
       * <code>optional uint32 mimeVerCnt = 55;</code>
       * @param value The mimeVerCnt to set.
       * @return This builder for chaining.
       */
      public Builder setMimeVerCnt(int value) {

        mimeVerCnt_ = value;
        bitField1_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mimeVerCnt = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearMimeVerCnt() {
        bitField1_ = (bitField1_ & ~0x00400000);
        mimeVerCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> msgID_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureMsgIDIsMutable() {
        if (!msgID_.isModifiable()) {
          msgID_ = makeMutableCopy(msgID_);
        }
        bitField1_ |= 0x00800000;
      }
      /**
       * <code>repeated bytes msgID = 56;</code>
       * @return A list containing the msgID.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMsgIDList() {
        msgID_.makeImmutable();
        return msgID_;
      }
      /**
       * <code>repeated bytes msgID = 56;</code>
       * @return The count of msgID.
       */
      public int getMsgIDCount() {
        return msgID_.size();
      }
      /**
       * <code>repeated bytes msgID = 56;</code>
       * @param index The index of the element to return.
       * @return The msgID at the given index.
       */
      public com.google.protobuf.ByteString getMsgID(int index) {
        return msgID_.get(index);
      }
      /**
       * <code>repeated bytes msgID = 56;</code>
       * @param index The index to set the value at.
       * @param value The msgID to set.
       * @return This builder for chaining.
       */
      public Builder setMsgID(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMsgIDIsMutable();
        msgID_.set(index, value);
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes msgID = 56;</code>
       * @param value The msgID to add.
       * @return This builder for chaining.
       */
      public Builder addMsgID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMsgIDIsMutable();
        msgID_.add(value);
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes msgID = 56;</code>
       * @param values The msgID to add.
       * @return This builder for chaining.
       */
      public Builder addAllMsgID(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMsgIDIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, msgID_);
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes msgID = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgID() {
        msgID_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00800000);
        onChanged();
        return this;
      }

      private int msgIDCnt_ ;
      /**
       * <code>optional uint32 msgIDCnt = 57;</code>
       * @return Whether the msgIDCnt field is set.
       */
      @java.lang.Override
      public boolean hasMsgIDCnt() {
        return ((bitField1_ & 0x01000000) != 0);
      }
      /**
       * <code>optional uint32 msgIDCnt = 57;</code>
       * @return The msgIDCnt.
       */
      @java.lang.Override
      public int getMsgIDCnt() {
        return msgIDCnt_;
      }
      /**
       * <code>optional uint32 msgIDCnt = 57;</code>
       * @param value The msgIDCnt to set.
       * @return This builder for chaining.
       */
      public Builder setMsgIDCnt(int value) {

        msgIDCnt_ = value;
        bitField1_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 msgIDCnt = 57;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgIDCnt() {
        bitField1_ = (bitField1_ & ~0x01000000);
        msgIDCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString emaProtType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes emaProtType = 58;</code>
       * @return Whether the emaProtType field is set.
       */
      @java.lang.Override
      public boolean hasEmaProtType() {
        return ((bitField1_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes emaProtType = 58;</code>
       * @return The emaProtType.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEmaProtType() {
        return emaProtType_;
      }
      /**
       * <code>optional bytes emaProtType = 58;</code>
       * @param value The emaProtType to set.
       * @return This builder for chaining.
       */
      public Builder setEmaProtType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emaProtType_ = value;
        bitField1_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes emaProtType = 58;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmaProtType() {
        bitField1_ = (bitField1_ & ~0x02000000);
        emaProtType_ = getDefaultInstance().getEmaProtType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString pwd_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes pwd = 59;</code>
       * @return Whether the pwd field is set.
       */
      @java.lang.Override
      public boolean hasPwd() {
        return ((bitField1_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes pwd = 59;</code>
       * @return The pwd.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPwd() {
        return pwd_;
      }
      /**
       * <code>optional bytes pwd = 59;</code>
       * @param value The pwd to set.
       * @return This builder for chaining.
       */
      public Builder setPwd(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        pwd_ = value;
        bitField1_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes pwd = 59;</code>
       * @return This builder for chaining.
       */
      public Builder clearPwd() {
        bitField1_ = (bitField1_ & ~0x04000000);
        pwd_ = getDefaultInstance().getPwd();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcptTo_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureRcptToIsMutable() {
        if (!rcptTo_.isModifiable()) {
          rcptTo_ = makeMutableCopy(rcptTo_);
        }
        bitField1_ |= 0x08000000;
      }
      /**
       * <code>repeated bytes rcptTo = 60;</code>
       * @return A list containing the rcptTo.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getRcptToList() {
        rcptTo_.makeImmutable();
        return rcptTo_;
      }
      /**
       * <code>repeated bytes rcptTo = 60;</code>
       * @return The count of rcptTo.
       */
      public int getRcptToCount() {
        return rcptTo_.size();
      }
      /**
       * <code>repeated bytes rcptTo = 60;</code>
       * @param index The index of the element to return.
       * @return The rcptTo at the given index.
       */
      public com.google.protobuf.ByteString getRcptTo(int index) {
        return rcptTo_.get(index);
      }
      /**
       * <code>repeated bytes rcptTo = 60;</code>
       * @param index The index to set the value at.
       * @param value The rcptTo to set.
       * @return This builder for chaining.
       */
      public Builder setRcptTo(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcptToIsMutable();
        rcptTo_.set(index, value);
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcptTo = 60;</code>
       * @param value The rcptTo to add.
       * @return This builder for chaining.
       */
      public Builder addRcptTo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcptToIsMutable();
        rcptTo_.add(value);
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcptTo = 60;</code>
       * @param values The rcptTo to add.
       * @return This builder for chaining.
       */
      public Builder addAllRcptTo(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureRcptToIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rcptTo_);
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcptTo = 60;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcptTo() {
        rcptTo_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x08000000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcptToDom_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureRcptToDomIsMutable() {
        if (!rcptToDom_.isModifiable()) {
          rcptToDom_ = makeMutableCopy(rcptToDom_);
        }
        bitField1_ |= 0x10000000;
      }
      /**
       * <code>repeated bytes rcptToDom = 61;</code>
       * @return A list containing the rcptToDom.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getRcptToDomList() {
        rcptToDom_.makeImmutable();
        return rcptToDom_;
      }
      /**
       * <code>repeated bytes rcptToDom = 61;</code>
       * @return The count of rcptToDom.
       */
      public int getRcptToDomCount() {
        return rcptToDom_.size();
      }
      /**
       * <code>repeated bytes rcptToDom = 61;</code>
       * @param index The index of the element to return.
       * @return The rcptToDom at the given index.
       */
      public com.google.protobuf.ByteString getRcptToDom(int index) {
        return rcptToDom_.get(index);
      }
      /**
       * <code>repeated bytes rcptToDom = 61;</code>
       * @param index The index to set the value at.
       * @param value The rcptToDom to set.
       * @return This builder for chaining.
       */
      public Builder setRcptToDom(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcptToDomIsMutable();
        rcptToDom_.set(index, value);
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcptToDom = 61;</code>
       * @param value The rcptToDom to add.
       * @return This builder for chaining.
       */
      public Builder addRcptToDom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcptToDomIsMutable();
        rcptToDom_.add(value);
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcptToDom = 61;</code>
       * @param values The rcptToDom to add.
       * @return This builder for chaining.
       */
      public Builder addAllRcptToDom(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureRcptToDomIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rcptToDom_);
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcptToDom = 61;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcptToDom() {
        rcptToDom_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x10000000);
        onChanged();
        return this;
      }

      private int rcptToDomCnt_ ;
      /**
       * <code>optional uint32 rcptToDomCnt = 62;</code>
       * @return Whether the rcptToDomCnt field is set.
       */
      @java.lang.Override
      public boolean hasRcptToDomCnt() {
        return ((bitField1_ & 0x20000000) != 0);
      }
      /**
       * <code>optional uint32 rcptToDomCnt = 62;</code>
       * @return The rcptToDomCnt.
       */
      @java.lang.Override
      public int getRcptToDomCnt() {
        return rcptToDomCnt_;
      }
      /**
       * <code>optional uint32 rcptToDomCnt = 62;</code>
       * @param value The rcptToDomCnt to set.
       * @return This builder for chaining.
       */
      public Builder setRcptToDomCnt(int value) {

        rcptToDomCnt_ = value;
        bitField1_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rcptToDomCnt = 62;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcptToDomCnt() {
        bitField1_ = (bitField1_ & ~0x20000000);
        rcptToDomCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString repTo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes repTo = 63;</code>
       * @return Whether the repTo field is set.
       */
      @java.lang.Override
      public boolean hasRepTo() {
        return ((bitField1_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes repTo = 63;</code>
       * @return The repTo.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRepTo() {
        return repTo_;
      }
      /**
       * <code>optional bytes repTo = 63;</code>
       * @param value The repTo to set.
       * @return This builder for chaining.
       */
      public Builder setRepTo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        repTo_ = value;
        bitField1_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes repTo = 63;</code>
       * @return This builder for chaining.
       */
      public Builder clearRepTo() {
        bitField1_ = (bitField1_ & ~0x40000000);
        repTo_ = getDefaultInstance().getRepTo();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> received_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureReceivedIsMutable() {
        if (!received_.isModifiable()) {
          received_ = makeMutableCopy(received_);
        }
        bitField1_ |= 0x80000000;
      }
      /**
       * <code>repeated bytes received = 64;</code>
       * @return A list containing the received.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getReceivedList() {
        received_.makeImmutable();
        return received_;
      }
      /**
       * <code>repeated bytes received = 64;</code>
       * @return The count of received.
       */
      public int getReceivedCount() {
        return received_.size();
      }
      /**
       * <code>repeated bytes received = 64;</code>
       * @param index The index of the element to return.
       * @return The received at the given index.
       */
      public com.google.protobuf.ByteString getReceived(int index) {
        return received_.get(index);
      }
      /**
       * <code>repeated bytes received = 64;</code>
       * @param index The index to set the value at.
       * @param value The received to set.
       * @return This builder for chaining.
       */
      public Builder setReceived(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureReceivedIsMutable();
        received_.set(index, value);
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes received = 64;</code>
       * @param value The received to add.
       * @return This builder for chaining.
       */
      public Builder addReceived(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureReceivedIsMutable();
        received_.add(value);
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes received = 64;</code>
       * @param values The received to add.
       * @return This builder for chaining.
       */
      public Builder addAllReceived(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureReceivedIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, received_);
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes received = 64;</code>
       * @return This builder for chaining.
       */
      public Builder clearReceived() {
        received_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x80000000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcvrEmail_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureRcvrEmailIsMutable() {
        if (!rcvrEmail_.isModifiable()) {
          rcvrEmail_ = makeMutableCopy(rcvrEmail_);
        }
        bitField2_ |= 0x00000001;
      }
      /**
       * <code>repeated bytes rcvrEmail = 65;</code>
       * @return A list containing the rcvrEmail.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getRcvrEmailList() {
        rcvrEmail_.makeImmutable();
        return rcvrEmail_;
      }
      /**
       * <code>repeated bytes rcvrEmail = 65;</code>
       * @return The count of rcvrEmail.
       */
      public int getRcvrEmailCount() {
        return rcvrEmail_.size();
      }
      /**
       * <code>repeated bytes rcvrEmail = 65;</code>
       * @param index The index of the element to return.
       * @return The rcvrEmail at the given index.
       */
      public com.google.protobuf.ByteString getRcvrEmail(int index) {
        return rcvrEmail_.get(index);
      }
      /**
       * <code>repeated bytes rcvrEmail = 65;</code>
       * @param index The index to set the value at.
       * @param value The rcvrEmail to set.
       * @return This builder for chaining.
       */
      public Builder setRcvrEmail(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcvrEmailIsMutable();
        rcvrEmail_.set(index, value);
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrEmail = 65;</code>
       * @param value The rcvrEmail to add.
       * @return This builder for chaining.
       */
      public Builder addRcvrEmail(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcvrEmailIsMutable();
        rcvrEmail_.add(value);
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrEmail = 65;</code>
       * @param values The rcvrEmail to add.
       * @return This builder for chaining.
       */
      public Builder addAllRcvrEmail(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureRcvrEmailIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rcvrEmail_);
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrEmail = 65;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvrEmail() {
        rcvrEmail_ = emptyList(com.google.protobuf.ByteString.class);
        bitField2_ = (bitField2_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcvrAli_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureRcvrAliIsMutable() {
        if (!rcvrAli_.isModifiable()) {
          rcvrAli_ = makeMutableCopy(rcvrAli_);
        }
        bitField2_ |= 0x00000002;
      }
      /**
       * <code>repeated bytes rcvrAli = 66;</code>
       * @return A list containing the rcvrAli.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getRcvrAliList() {
        rcvrAli_.makeImmutable();
        return rcvrAli_;
      }
      /**
       * <code>repeated bytes rcvrAli = 66;</code>
       * @return The count of rcvrAli.
       */
      public int getRcvrAliCount() {
        return rcvrAli_.size();
      }
      /**
       * <code>repeated bytes rcvrAli = 66;</code>
       * @param index The index of the element to return.
       * @return The rcvrAli at the given index.
       */
      public com.google.protobuf.ByteString getRcvrAli(int index) {
        return rcvrAli_.get(index);
      }
      /**
       * <code>repeated bytes rcvrAli = 66;</code>
       * @param index The index to set the value at.
       * @param value The rcvrAli to set.
       * @return This builder for chaining.
       */
      public Builder setRcvrAli(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcvrAliIsMutable();
        rcvrAli_.set(index, value);
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrAli = 66;</code>
       * @param value The rcvrAli to add.
       * @return This builder for chaining.
       */
      public Builder addRcvrAli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcvrAliIsMutable();
        rcvrAli_.add(value);
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrAli = 66;</code>
       * @param values The rcvrAli to add.
       * @return This builder for chaining.
       */
      public Builder addAllRcvrAli(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureRcvrAliIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rcvrAli_);
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrAli = 66;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvrAli() {
        rcvrAli_ = emptyList(com.google.protobuf.ByteString.class);
        bitField2_ = (bitField2_ & ~0x00000002);
        onChanged();
        return this;
      }

      private int rcvrAliCnt_ ;
      /**
       * <code>optional uint32 rcvrAliCnt = 67;</code>
       * @return Whether the rcvrAliCnt field is set.
       */
      @java.lang.Override
      public boolean hasRcvrAliCnt() {
        return ((bitField2_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 rcvrAliCnt = 67;</code>
       * @return The rcvrAliCnt.
       */
      @java.lang.Override
      public int getRcvrAliCnt() {
        return rcvrAliCnt_;
      }
      /**
       * <code>optional uint32 rcvrAliCnt = 67;</code>
       * @param value The rcvrAliCnt to set.
       * @return This builder for chaining.
       */
      public Builder setRcvrAliCnt(int value) {

        rcvrAliCnt_ = value;
        bitField2_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rcvrAliCnt = 67;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvrAliCnt() {
        bitField2_ = (bitField2_ & ~0x00000004);
        rcvrAliCnt_ = 0;
        onChanged();
        return this;
      }

      private int rcvrEmailCnt_ ;
      /**
       * <code>optional uint32 rcvrEmailCnt = 68;</code>
       * @return Whether the rcvrEmailCnt field is set.
       */
      @java.lang.Override
      public boolean hasRcvrEmailCnt() {
        return ((bitField2_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 rcvrEmailCnt = 68;</code>
       * @return The rcvrEmailCnt.
       */
      @java.lang.Override
      public int getRcvrEmailCnt() {
        return rcvrEmailCnt_;
      }
      /**
       * <code>optional uint32 rcvrEmailCnt = 68;</code>
       * @param value The rcvrEmailCnt to set.
       * @return This builder for chaining.
       */
      public Builder setRcvrEmailCnt(int value) {

        rcvrEmailCnt_ = value;
        bitField2_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 rcvrEmailCnt = 68;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvrEmailCnt() {
        bitField2_ = (bitField2_ & ~0x00000008);
        rcvrEmailCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> rcvrDom_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureRcvrDomIsMutable() {
        if (!rcvrDom_.isModifiable()) {
          rcvrDom_ = makeMutableCopy(rcvrDom_);
        }
        bitField2_ |= 0x00000010;
      }
      /**
       * <code>repeated bytes rcvrDom = 69;</code>
       * @return A list containing the rcvrDom.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getRcvrDomList() {
        rcvrDom_.makeImmutable();
        return rcvrDom_;
      }
      /**
       * <code>repeated bytes rcvrDom = 69;</code>
       * @return The count of rcvrDom.
       */
      public int getRcvrDomCount() {
        return rcvrDom_.size();
      }
      /**
       * <code>repeated bytes rcvrDom = 69;</code>
       * @param index The index of the element to return.
       * @return The rcvrDom at the given index.
       */
      public com.google.protobuf.ByteString getRcvrDom(int index) {
        return rcvrDom_.get(index);
      }
      /**
       * <code>repeated bytes rcvrDom = 69;</code>
       * @param index The index to set the value at.
       * @param value The rcvrDom to set.
       * @return This builder for chaining.
       */
      public Builder setRcvrDom(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcvrDomIsMutable();
        rcvrDom_.set(index, value);
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrDom = 69;</code>
       * @param value The rcvrDom to add.
       * @return This builder for chaining.
       */
      public Builder addRcvrDom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureRcvrDomIsMutable();
        rcvrDom_.add(value);
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrDom = 69;</code>
       * @param values The rcvrDom to add.
       * @return This builder for chaining.
       */
      public Builder addAllRcvrDom(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureRcvrDomIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rcvrDom_);
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes rcvrDom = 69;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvrDom() {
        rcvrDom_ = emptyList(com.google.protobuf.ByteString.class);
        bitField2_ = (bitField2_ & ~0x00000010);
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString resentSrvAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes resentSrvAge = 70;</code>
       * @return Whether the resentSrvAge field is set.
       */
      @java.lang.Override
      public boolean hasResentSrvAge() {
        return ((bitField2_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes resentSrvAge = 70;</code>
       * @return The resentSrvAge.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getResentSrvAge() {
        return resentSrvAge_;
      }
      /**
       * <code>optional bytes resentSrvAge = 70;</code>
       * @param value The resentSrvAge to set.
       * @return This builder for chaining.
       */
      public Builder setResentSrvAge(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        resentSrvAge_ = value;
        bitField2_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes resentSrvAge = 70;</code>
       * @return This builder for chaining.
       */
      public Builder clearResentSrvAge() {
        bitField2_ = (bitField2_ & ~0x00000020);
        resentSrvAge_ = getDefaultInstance().getResentSrvAge();
        onChanged();
        return this;
      }

      private int resentDate_ ;
      /**
       * <code>optional uint32 resentDate = 71;</code>
       * @return Whether the resentDate field is set.
       */
      @java.lang.Override
      public boolean hasResentDate() {
        return ((bitField2_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 resentDate = 71;</code>
       * @return The resentDate.
       */
      @java.lang.Override
      public int getResentDate() {
        return resentDate_;
      }
      /**
       * <code>optional uint32 resentDate = 71;</code>
       * @param value The resentDate to set.
       * @return This builder for chaining.
       */
      public Builder setResentDate(int value) {

        resentDate_ = value;
        bitField2_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 resentDate = 71;</code>
       * @return This builder for chaining.
       */
      public Builder clearResentDate() {
        bitField2_ = (bitField2_ & ~0x00000040);
        resentDate_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString resentFrom_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes resentFrom = 72;</code>
       * @return Whether the resentFrom field is set.
       */
      @java.lang.Override
      public boolean hasResentFrom() {
        return ((bitField2_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes resentFrom = 72;</code>
       * @return The resentFrom.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getResentFrom() {
        return resentFrom_;
      }
      /**
       * <code>optional bytes resentFrom = 72;</code>
       * @param value The resentFrom to set.
       * @return This builder for chaining.
       */
      public Builder setResentFrom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        resentFrom_ = value;
        bitField2_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes resentFrom = 72;</code>
       * @return This builder for chaining.
       */
      public Builder clearResentFrom() {
        bitField2_ = (bitField2_ & ~0x00000080);
        resentFrom_ = getDefaultInstance().getResentFrom();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> resentTo_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureResentToIsMutable() {
        if (!resentTo_.isModifiable()) {
          resentTo_ = makeMutableCopy(resentTo_);
        }
        bitField2_ |= 0x00000100;
      }
      /**
       * <code>repeated bytes resentTo = 73;</code>
       * @return A list containing the resentTo.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getResentToList() {
        resentTo_.makeImmutable();
        return resentTo_;
      }
      /**
       * <code>repeated bytes resentTo = 73;</code>
       * @return The count of resentTo.
       */
      public int getResentToCount() {
        return resentTo_.size();
      }
      /**
       * <code>repeated bytes resentTo = 73;</code>
       * @param index The index of the element to return.
       * @return The resentTo at the given index.
       */
      public com.google.protobuf.ByteString getResentTo(int index) {
        return resentTo_.get(index);
      }
      /**
       * <code>repeated bytes resentTo = 73;</code>
       * @param index The index to set the value at.
       * @param value The resentTo to set.
       * @return This builder for chaining.
       */
      public Builder setResentTo(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureResentToIsMutable();
        resentTo_.set(index, value);
        bitField2_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes resentTo = 73;</code>
       * @param value The resentTo to add.
       * @return This builder for chaining.
       */
      public Builder addResentTo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureResentToIsMutable();
        resentTo_.add(value);
        bitField2_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes resentTo = 73;</code>
       * @param values The resentTo to add.
       * @return This builder for chaining.
       */
      public Builder addAllResentTo(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureResentToIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, resentTo_);
        bitField2_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes resentTo = 73;</code>
       * @return This builder for chaining.
       */
      public Builder clearResentTo() {
        resentTo_ = emptyList(com.google.protobuf.ByteString.class);
        bitField2_ = (bitField2_ & ~0x00000100);
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString senderEmail_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes senderEmail = 74;</code>
       * @return Whether the senderEmail field is set.
       */
      @java.lang.Override
      public boolean hasSenderEmail() {
        return ((bitField2_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes senderEmail = 74;</code>
       * @return The senderEmail.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSenderEmail() {
        return senderEmail_;
      }
      /**
       * <code>optional bytes senderEmail = 74;</code>
       * @param value The senderEmail to set.
       * @return This builder for chaining.
       */
      public Builder setSenderEmail(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        senderEmail_ = value;
        bitField2_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes senderEmail = 74;</code>
       * @return This builder for chaining.
       */
      public Builder clearSenderEmail() {
        bitField2_ = (bitField2_ & ~0x00000200);
        senderEmail_ = getDefaultInstance().getSenderEmail();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString senderAli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes senderAli = 75;</code>
       * @return Whether the senderAli field is set.
       */
      @java.lang.Override
      public boolean hasSenderAli() {
        return ((bitField2_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes senderAli = 75;</code>
       * @return The senderAli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSenderAli() {
        return senderAli_;
      }
      /**
       * <code>optional bytes senderAli = 75;</code>
       * @param value The senderAli to set.
       * @return This builder for chaining.
       */
      public Builder setSenderAli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        senderAli_ = value;
        bitField2_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes senderAli = 75;</code>
       * @return This builder for chaining.
       */
      public Builder clearSenderAli() {
        bitField2_ = (bitField2_ & ~0x00000400);
        senderAli_ = getDefaultInstance().getSenderAli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString senderDom_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes senderDom = 76;</code>
       * @return Whether the senderDom field is set.
       */
      @java.lang.Override
      public boolean hasSenderDom() {
        return ((bitField2_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes senderDom = 76;</code>
       * @return The senderDom.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSenderDom() {
        return senderDom_;
      }
      /**
       * <code>optional bytes senderDom = 76;</code>
       * @param value The senderDom to set.
       * @return This builder for chaining.
       */
      public Builder setSenderDom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        senderDom_ = value;
        bitField2_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes senderDom = 76;</code>
       * @return This builder for chaining.
       */
      public Builder clearSenderDom() {
        bitField2_ = (bitField2_ & ~0x00000800);
        senderDom_ = getDefaultInstance().getSenderDom();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sMTPSrv_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SMTPSrv = 77;</code>
       * @return Whether the sMTPSrv field is set.
       */
      @java.lang.Override
      public boolean hasSMTPSrv() {
        return ((bitField2_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes SMTPSrv = 77;</code>
       * @return The sMTPSrv.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSMTPSrv() {
        return sMTPSrv_;
      }
      /**
       * <code>optional bytes SMTPSrv = 77;</code>
       * @param value The sMTPSrv to set.
       * @return This builder for chaining.
       */
      public Builder setSMTPSrv(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sMTPSrv_ = value;
        bitField2_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SMTPSrv = 77;</code>
       * @return This builder for chaining.
       */
      public Builder clearSMTPSrv() {
        bitField2_ = (bitField2_ & ~0x00001000);
        sMTPSrv_ = getDefaultInstance().getSMTPSrv();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sMTPSrvAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SMTPSrvAge = 78;</code>
       * @return Whether the sMTPSrvAge field is set.
       */
      @java.lang.Override
      public boolean hasSMTPSrvAge() {
        return ((bitField2_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes SMTPSrvAge = 78;</code>
       * @return The sMTPSrvAge.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSMTPSrvAge() {
        return sMTPSrvAge_;
      }
      /**
       * <code>optional bytes SMTPSrvAge = 78;</code>
       * @param value The sMTPSrvAge to set.
       * @return This builder for chaining.
       */
      public Builder setSMTPSrvAge(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sMTPSrvAge_ = value;
        bitField2_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SMTPSrvAge = 78;</code>
       * @return This builder for chaining.
       */
      public Builder clearSMTPSrvAge() {
        bitField2_ = (bitField2_ & ~0x00002000);
        sMTPSrvAge_ = getDefaultInstance().getSMTPSrvAge();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString receivedSPF_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes receivedSPF = 79;</code>
       * @return Whether the receivedSPF field is set.
       */
      @java.lang.Override
      public boolean hasReceivedSPF() {
        return ((bitField2_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes receivedSPF = 79;</code>
       * @return The receivedSPF.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getReceivedSPF() {
        return receivedSPF_;
      }
      /**
       * <code>optional bytes receivedSPF = 79;</code>
       * @param value The receivedSPF to set.
       * @return This builder for chaining.
       */
      public Builder setReceivedSPF(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        receivedSPF_ = value;
        bitField2_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes receivedSPF = 79;</code>
       * @return This builder for chaining.
       */
      public Builder clearReceivedSPF() {
        bitField2_ = (bitField2_ & ~0x00004000);
        receivedSPF_ = getDefaultInstance().getReceivedSPF();
        onChanged();
        return this;
      }

      private int startTLS_ ;
      /**
       * <code>optional uint32 startTLS = 80;</code>
       * @return Whether the startTLS field is set.
       */
      @java.lang.Override
      public boolean hasStartTLS() {
        return ((bitField2_ & 0x00008000) != 0);
      }
      /**
       * <code>optional uint32 startTLS = 80;</code>
       * @return The startTLS.
       */
      @java.lang.Override
      public int getStartTLS() {
        return startTLS_;
      }
      /**
       * <code>optional uint32 startTLS = 80;</code>
       * @param value The startTLS to set.
       * @return This builder for chaining.
       */
      public Builder setStartTLS(int value) {

        startTLS_ = value;
        bitField2_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 startTLS = 80;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTLS() {
        bitField2_ = (bitField2_ & ~0x00008000);
        startTLS_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subj_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subj = 81;</code>
       * @return Whether the subj field is set.
       */
      @java.lang.Override
      public boolean hasSubj() {
        return ((bitField2_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes subj = 81;</code>
       * @return The subj.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSubj() {
        return subj_;
      }
      /**
       * <code>optional bytes subj = 81;</code>
       * @param value The subj to set.
       * @return This builder for chaining.
       */
      public Builder setSubj(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subj_ = value;
        bitField2_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subj = 81;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubj() {
        bitField2_ = (bitField2_ & ~0x00010000);
        subj_ = getDefaultInstance().getSubj();
        onChanged();
        return this;
      }

      private int subjCnt_ ;
      /**
       * <code>optional uint32 subj_cnt = 82;</code>
       * @return Whether the subjCnt field is set.
       */
      @java.lang.Override
      public boolean hasSubjCnt() {
        return ((bitField2_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 subj_cnt = 82;</code>
       * @return The subjCnt.
       */
      @java.lang.Override
      public int getSubjCnt() {
        return subjCnt_;
      }
      /**
       * <code>optional uint32 subj_cnt = 82;</code>
       * @param value The subjCnt to set.
       * @return This builder for chaining.
       */
      public Builder setSubjCnt(int value) {

        subjCnt_ = value;
        bitField2_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 subj_cnt = 82;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubjCnt() {
        bitField2_ = (bitField2_ & ~0x00020000);
        subjCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString usrAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes usrAge = 83;</code>
       * @return Whether the usrAge field is set.
       */
      @java.lang.Override
      public boolean hasUsrAge() {
        return ((bitField2_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes usrAge = 83;</code>
       * @return The usrAge.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUsrAge() {
        return usrAge_;
      }
      /**
       * <code>optional bytes usrAge = 83;</code>
       * @param value The usrAge to set.
       * @return This builder for chaining.
       */
      public Builder setUsrAge(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        usrAge_ = value;
        bitField2_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes usrAge = 83;</code>
       * @return This builder for chaining.
       */
      public Builder clearUsrAge() {
        bitField2_ = (bitField2_ & ~0x00040000);
        usrAge_ = getDefaultInstance().getUsrAge();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString emailVersion_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes emailVersion = 84;</code>
       * @return Whether the emailVersion field is set.
       */
      @java.lang.Override
      public boolean hasEmailVersion() {
        return ((bitField2_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes emailVersion = 84;</code>
       * @return The emailVersion.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEmailVersion() {
        return emailVersion_;
      }
      /**
       * <code>optional bytes emailVersion = 84;</code>
       * @param value The emailVersion to set.
       * @return This builder for chaining.
       */
      public Builder setEmailVersion(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailVersion_ = value;
        bitField2_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes emailVersion = 84;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailVersion() {
        bitField2_ = (bitField2_ & ~0x00080000);
        emailVersion_ = getDefaultInstance().getEmailVersion();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rcvWit_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes rcvWit = 85;</code>
       * @return Whether the rcvWit field is set.
       */
      @java.lang.Override
      public boolean hasRcvWit() {
        return ((bitField2_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes rcvWit = 85;</code>
       * @return The rcvWit.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRcvWit() {
        return rcvWit_;
      }
      /**
       * <code>optional bytes rcvWit = 85;</code>
       * @param value The rcvWit to set.
       * @return This builder for chaining.
       */
      public Builder setRcvWit(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rcvWit_ = value;
        bitField2_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes rcvWit = 85;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvWit() {
        bitField2_ = (bitField2_ & ~0x00100000);
        rcvWit_ = getDefaultInstance().getRcvWit();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString xMai_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes xMai = 86;</code>
       * @return Whether the xMai field is set.
       */
      @java.lang.Override
      public boolean hasXMai() {
        return ((bitField2_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes xMai = 86;</code>
       * @return The xMai.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getXMai() {
        return xMai_;
      }
      /**
       * <code>optional bytes xMai = 86;</code>
       * @param value The xMai to set.
       * @return This builder for chaining.
       */
      public Builder setXMai(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        xMai_ = value;
        bitField2_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes xMai = 86;</code>
       * @return This builder for chaining.
       */
      public Builder clearXMai() {
        bitField2_ = (bitField2_ & ~0x00200000);
        xMai_ = getDefaultInstance().getXMai();
        onChanged();
        return this;
      }

      private int xMaiCnt_ ;
      /**
       * <code>optional uint32 xMaiCnt = 87;</code>
       * @return Whether the xMaiCnt field is set.
       */
      @java.lang.Override
      public boolean hasXMaiCnt() {
        return ((bitField2_ & 0x00400000) != 0);
      }
      /**
       * <code>optional uint32 xMaiCnt = 87;</code>
       * @return The xMaiCnt.
       */
      @java.lang.Override
      public int getXMaiCnt() {
        return xMaiCnt_;
      }
      /**
       * <code>optional uint32 xMaiCnt = 87;</code>
       * @param value The xMaiCnt to set.
       * @return This builder for chaining.
       */
      public Builder setXMaiCnt(int value) {

        xMaiCnt_ = value;
        bitField2_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 xMaiCnt = 87;</code>
       * @return This builder for chaining.
       */
      public Builder clearXMaiCnt() {
        bitField2_ = (bitField2_ & ~0x00400000);
        xMaiCnt_ = 0;
        onChanged();
        return this;
      }

      private int xOriIP_ ;
      /**
       * <code>optional uint32 xOriIP = 88;</code>
       * @return Whether the xOriIP field is set.
       */
      @java.lang.Override
      public boolean hasXOriIP() {
        return ((bitField2_ & 0x00800000) != 0);
      }
      /**
       * <code>optional uint32 xOriIP = 88;</code>
       * @return The xOriIP.
       */
      @java.lang.Override
      public int getXOriIP() {
        return xOriIP_;
      }
      /**
       * <code>optional uint32 xOriIP = 88;</code>
       * @param value The xOriIP to set.
       * @return This builder for chaining.
       */
      public Builder setXOriIP(int value) {

        xOriIP_ = value;
        bitField2_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 xOriIP = 88;</code>
       * @return This builder for chaining.
       */
      public Builder clearXOriIP() {
        bitField2_ = (bitField2_ & ~0x00800000);
        xOriIP_ = 0;
        onChanged();
        return this;
      }

      private int senderEmailCnt_ ;
      /**
       * <code>optional uint32 senderEmailCnt = 89;</code>
       * @return Whether the senderEmailCnt field is set.
       */
      @java.lang.Override
      public boolean hasSenderEmailCnt() {
        return ((bitField2_ & 0x01000000) != 0);
      }
      /**
       * <code>optional uint32 senderEmailCnt = 89;</code>
       * @return The senderEmailCnt.
       */
      @java.lang.Override
      public int getSenderEmailCnt() {
        return senderEmailCnt_;
      }
      /**
       * <code>optional uint32 senderEmailCnt = 89;</code>
       * @param value The senderEmailCnt to set.
       * @return This builder for chaining.
       */
      public Builder setSenderEmailCnt(int value) {

        senderEmailCnt_ = value;
        bitField2_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 senderEmailCnt = 89;</code>
       * @return This builder for chaining.
       */
      public Builder clearSenderEmailCnt() {
        bitField2_ = (bitField2_ & ~0x01000000);
        senderEmailCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fw_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fw = 90;</code>
       * @return Whether the fw field is set.
       */
      @java.lang.Override
      public boolean hasFw() {
        return ((bitField2_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes fw = 90;</code>
       * @return The fw.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFw() {
        return fw_;
      }
      /**
       * <code>optional bytes fw = 90;</code>
       * @param value The fw to set.
       * @return This builder for chaining.
       */
      public Builder setFw(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fw_ = value;
        bitField2_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fw = 90;</code>
       * @return This builder for chaining.
       */
      public Builder clearFw() {
        bitField2_ = (bitField2_ & ~0x02000000);
        fw_ = getDefaultInstance().getFw();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString befw_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes befw = 91;</code>
       * @return Whether the befw field is set.
       */
      @java.lang.Override
      public boolean hasBefw() {
        return ((bitField2_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes befw = 91;</code>
       * @return The befw.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBefw() {
        return befw_;
      }
      /**
       * <code>optional bytes befw = 91;</code>
       * @param value The befw to set.
       * @return This builder for chaining.
       */
      public Builder setBefw(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        befw_ = value;
        bitField2_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes befw = 91;</code>
       * @return This builder for chaining.
       */
      public Builder clearBefw() {
        bitField2_ = (bitField2_ & ~0x04000000);
        befw_ = getDefaultInstance().getBefw();
        onChanged();
        return this;
      }

      private long rcvDate_ ;
      /**
       * <code>optional uint64 rcvDate = 92;</code>
       * @return Whether the rcvDate field is set.
       */
      @java.lang.Override
      public boolean hasRcvDate() {
        return ((bitField2_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint64 rcvDate = 92;</code>
       * @return The rcvDate.
       */
      @java.lang.Override
      public long getRcvDate() {
        return rcvDate_;
      }
      /**
       * <code>optional uint64 rcvDate = 92;</code>
       * @param value The rcvDate to set.
       * @return This builder for chaining.
       */
      public Builder setRcvDate(long value) {

        rcvDate_ = value;
        bitField2_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 rcvDate = 92;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvDate() {
        bitField2_ = (bitField2_ & ~0x08000000);
        rcvDate_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rcvSrvAge_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes rcvSrvAge = 93;</code>
       * @return Whether the rcvSrvAge field is set.
       */
      @java.lang.Override
      public boolean hasRcvSrvAge() {
        return ((bitField2_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes rcvSrvAge = 93;</code>
       * @return The rcvSrvAge.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRcvSrvAge() {
        return rcvSrvAge_;
      }
      /**
       * <code>optional bytes rcvSrvAge = 93;</code>
       * @param value The rcvSrvAge to set.
       * @return This builder for chaining.
       */
      public Builder setRcvSrvAge(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rcvSrvAge_ = value;
        bitField2_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes rcvSrvAge = 93;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvSrvAge() {
        bitField2_ = (bitField2_ & ~0x10000000);
        rcvSrvAge_ = getDefaultInstance().getRcvSrvAge();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conTraEnc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conTraEnc = 94;</code>
       * @return Whether the conTraEnc field is set.
       */
      @java.lang.Override
      public boolean hasConTraEnc() {
        return ((bitField2_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes conTraEnc = 94;</code>
       * @return The conTraEnc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConTraEnc() {
        return conTraEnc_;
      }
      /**
       * <code>optional bytes conTraEnc = 94;</code>
       * @param value The conTraEnc to set.
       * @return This builder for chaining.
       */
      public Builder setConTraEnc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conTraEnc_ = value;
        bitField2_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conTraEnc = 94;</code>
       * @return This builder for chaining.
       */
      public Builder clearConTraEnc() {
        bitField2_ = (bitField2_ & ~0x20000000);
        conTraEnc_ = getDefaultInstance().getConTraEnc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString conTexCha_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes conTexCha = 95;</code>
       * @return Whether the conTexCha field is set.
       */
      @java.lang.Override
      public boolean hasConTexCha() {
        return ((bitField2_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes conTexCha = 95;</code>
       * @return The conTexCha.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getConTexCha() {
        return conTexCha_;
      }
      /**
       * <code>optional bytes conTexCha = 95;</code>
       * @param value The conTexCha to set.
       * @return This builder for chaining.
       */
      public Builder setConTexCha(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        conTexCha_ = value;
        bitField2_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes conTexCha = 95;</code>
       * @return This builder for chaining.
       */
      public Builder clearConTexCha() {
        bitField2_ = (bitField2_ & ~0x40000000);
        conTexCha_ = getDefaultInstance().getConTexCha();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString realFrom_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes realFrom = 96;</code>
       * @return Whether the realFrom field is set.
       */
      @java.lang.Override
      public boolean hasRealFrom() {
        return ((bitField2_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes realFrom = 96;</code>
       * @return The realFrom.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRealFrom() {
        return realFrom_;
      }
      /**
       * <code>optional bytes realFrom = 96;</code>
       * @param value The realFrom to set.
       * @return This builder for chaining.
       */
      public Builder setRealFrom(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        realFrom_ = value;
        bitField2_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes realFrom = 96;</code>
       * @return This builder for chaining.
       */
      public Builder clearRealFrom() {
        bitField2_ = (bitField2_ & ~0x80000000);
        realFrom_ = getDefaultInstance().getRealFrom();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString realTo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes realTo = 97;</code>
       * @return Whether the realTo field is set.
       */
      @java.lang.Override
      public boolean hasRealTo() {
        return ((bitField3_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes realTo = 97;</code>
       * @return The realTo.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRealTo() {
        return realTo_;
      }
      /**
       * <code>optional bytes realTo = 97;</code>
       * @param value The realTo to set.
       * @return This builder for chaining.
       */
      public Builder setRealTo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        realTo_ = value;
        bitField3_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes realTo = 97;</code>
       * @return This builder for chaining.
       */
      public Builder clearRealTo() {
        bitField3_ = (bitField3_ & ~0x00000001);
        realTo_ = getDefaultInstance().getRealTo();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString emaActRep_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes emaActRep = 98;</code>
       * @return Whether the emaActRep field is set.
       */
      @java.lang.Override
      public boolean hasEmaActRep() {
        return ((bitField3_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes emaActRep = 98;</code>
       * @return The emaActRep.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEmaActRep() {
        return emaActRep_;
      }
      /**
       * <code>optional bytes emaActRep = 98;</code>
       * @param value The emaActRep to set.
       * @return This builder for chaining.
       */
      public Builder setEmaActRep(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emaActRep_ = value;
        bitField3_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes emaActRep = 98;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmaActRep() {
        bitField3_ = (bitField3_ & ~0x00000002);
        emaActRep_ = getDefaultInstance().getEmaActRep();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rcvFromName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes rcvFromName = 99;</code>
       * @return Whether the rcvFromName field is set.
       */
      @java.lang.Override
      public boolean hasRcvFromName() {
        return ((bitField3_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes rcvFromName = 99;</code>
       * @return The rcvFromName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRcvFromName() {
        return rcvFromName_;
      }
      /**
       * <code>optional bytes rcvFromName = 99;</code>
       * @param value The rcvFromName to set.
       * @return This builder for chaining.
       */
      public Builder setRcvFromName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rcvFromName_ = value;
        bitField3_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes rcvFromName = 99;</code>
       * @return This builder for chaining.
       */
      public Builder clearRcvFromName() {
        bitField3_ = (bitField3_ & ~0x00000004);
        rcvFromName_ = getDefaultInstance().getRcvFromName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString errType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes errType = 100;</code>
       * @return Whether the errType field is set.
       */
      @java.lang.Override
      public boolean hasErrType() {
        return ((bitField3_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes errType = 100;</code>
       * @return The errType.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getErrType() {
        return errType_;
      }
      /**
       * <code>optional bytes errType = 100;</code>
       * @param value The errType to set.
       * @return This builder for chaining.
       */
      public Builder setErrType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        errType_ = value;
        bitField3_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes errType = 100;</code>
       * @return This builder for chaining.
       */
      public Builder clearErrType() {
        bitField3_ = (bitField3_ & ~0x00000008);
        errType_ = getDefaultInstance().getErrType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString contentWithHtml_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes contentWithHtml = 101;</code>
       * @return Whether the contentWithHtml field is set.
       */
      @java.lang.Override
      public boolean hasContentWithHtml() {
        return ((bitField3_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes contentWithHtml = 101;</code>
       * @return The contentWithHtml.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getContentWithHtml() {
        return contentWithHtml_;
      }
      /**
       * <code>optional bytes contentWithHtml = 101;</code>
       * @param value The contentWithHtml to set.
       * @return This builder for chaining.
       */
      public Builder setContentWithHtml(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        contentWithHtml_ = value;
        bitField3_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes contentWithHtml = 101;</code>
       * @return This builder for chaining.
       */
      public Builder clearContentWithHtml() {
        bitField3_ = (bitField3_ & ~0x00000010);
        contentWithHtml_ = getDefaultInstance().getContentWithHtml();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString charset_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes charset = 102;</code>
       * @return Whether the charset field is set.
       */
      @java.lang.Override
      public boolean hasCharset() {
        return ((bitField3_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes charset = 102;</code>
       * @return The charset.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCharset() {
        return charset_;
      }
      /**
       * <code>optional bytes charset = 102;</code>
       * @param value The charset to set.
       * @return This builder for chaining.
       */
      public Builder setCharset(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        charset_ = value;
        bitField3_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes charset = 102;</code>
       * @return This builder for chaining.
       */
      public Builder clearCharset() {
        bitField3_ = (bitField3_ & ~0x00000020);
        charset_ = getDefaultInstance().getCharset();
        onChanged();
        return this;
      }

      private int contentLen_ ;
      /**
       * <code>optional uint32 contentLen = 103;</code>
       * @return Whether the contentLen field is set.
       */
      @java.lang.Override
      public boolean hasContentLen() {
        return ((bitField3_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 contentLen = 103;</code>
       * @return The contentLen.
       */
      @java.lang.Override
      public int getContentLen() {
        return contentLen_;
      }
      /**
       * <code>optional uint32 contentLen = 103;</code>
       * @param value The contentLen to set.
       * @return This builder for chaining.
       */
      public Builder setContentLen(int value) {

        contentLen_ = value;
        bitField3_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 contentLen = 103;</code>
       * @return This builder for chaining.
       */
      public Builder clearContentLen() {
        bitField3_ = (bitField3_ & ~0x00000040);
        contentLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString isBeFw_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes isBeFw = 104;</code>
       * @return Whether the isBeFw field is set.
       */
      @java.lang.Override
      public boolean hasIsBeFw() {
        return ((bitField3_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes isBeFw = 104;</code>
       * @return The isBeFw.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIsBeFw() {
        return isBeFw_;
      }
      /**
       * <code>optional bytes isBeFw = 104;</code>
       * @param value The isBeFw to set.
       * @return This builder for chaining.
       */
      public Builder setIsBeFw(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        isBeFw_ = value;
        bitField3_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes isBeFw = 104;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsBeFw() {
        bitField3_ = (bitField3_ & ~0x00000080);
        isBeFw_ = getDefaultInstance().getIsBeFw();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString attachmentPath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes attachmentPath = 105;</code>
       * @return Whether the attachmentPath field is set.
       */
      @java.lang.Override
      public boolean hasAttachmentPath() {
        return ((bitField3_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes attachmentPath = 105;</code>
       * @return The attachmentPath.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAttachmentPath() {
        return attachmentPath_;
      }
      /**
       * <code>optional bytes attachmentPath = 105;</code>
       * @param value The attachmentPath to set.
       * @return This builder for chaining.
       */
      public Builder setAttachmentPath(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        attachmentPath_ = value;
        bitField3_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes attachmentPath = 105;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttachmentPath() {
        bitField3_ = (bitField3_ & ~0x00000100);
        attachmentPath_ = getDefaultInstance().getAttachmentPath();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString banner_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes banner = 106;</code>
       * @return Whether the banner field is set.
       */
      @java.lang.Override
      public boolean hasBanner() {
        return ((bitField3_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes banner = 106;</code>
       * @return The banner.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBanner() {
        return banner_;
      }
      /**
       * <code>optional bytes banner = 106;</code>
       * @param value The banner to set.
       * @return This builder for chaining.
       */
      public Builder setBanner(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        banner_ = value;
        bitField3_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes banner = 106;</code>
       * @return This builder for chaining.
       */
      public Builder clearBanner() {
        bitField3_ = (bitField3_ & ~0x00000200);
        banner_ = getDefaultInstance().getBanner();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> senderSoftware_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureSenderSoftwareIsMutable() {
        if (!senderSoftware_.isModifiable()) {
          senderSoftware_ = makeMutableCopy(senderSoftware_);
        }
        bitField3_ |= 0x00000400;
      }
      /**
       * <code>repeated bytes senderSoftware = 107;</code>
       * @return A list containing the senderSoftware.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getSenderSoftwareList() {
        senderSoftware_.makeImmutable();
        return senderSoftware_;
      }
      /**
       * <code>repeated bytes senderSoftware = 107;</code>
       * @return The count of senderSoftware.
       */
      public int getSenderSoftwareCount() {
        return senderSoftware_.size();
      }
      /**
       * <code>repeated bytes senderSoftware = 107;</code>
       * @param index The index of the element to return.
       * @return The senderSoftware at the given index.
       */
      public com.google.protobuf.ByteString getSenderSoftware(int index) {
        return senderSoftware_.get(index);
      }
      /**
       * <code>repeated bytes senderSoftware = 107;</code>
       * @param index The index to set the value at.
       * @param value The senderSoftware to set.
       * @return This builder for chaining.
       */
      public Builder setSenderSoftware(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureSenderSoftwareIsMutable();
        senderSoftware_.set(index, value);
        bitField3_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes senderSoftware = 107;</code>
       * @param value The senderSoftware to add.
       * @return This builder for chaining.
       */
      public Builder addSenderSoftware(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureSenderSoftwareIsMutable();
        senderSoftware_.add(value);
        bitField3_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes senderSoftware = 107;</code>
       * @param values The senderSoftware to add.
       * @return This builder for chaining.
       */
      public Builder addAllSenderSoftware(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureSenderSoftwareIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, senderSoftware_);
        bitField3_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes senderSoftware = 107;</code>
       * @return This builder for chaining.
       */
      public Builder clearSenderSoftware() {
        senderSoftware_ = emptyList(com.google.protobuf.ByteString.class);
        bitField3_ = (bitField3_ & ~0x00000400);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:EMAILInfo)
    }

    // @@protoc_insertion_point(class_scope:EMAILInfo)
    private static final EMAILInfoOuterClass.EMAILInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new EMAILInfoOuterClass.EMAILInfo();
    }

    public static EMAILInfoOuterClass.EMAILInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<EMAILInfo>
        PARSER = new com.google.protobuf.AbstractParser<EMAILInfo>() {
      @java.lang.Override
      public EMAILInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<EMAILInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EMAILInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public EMAILInfoOuterClass.EMAILInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_EMAILInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_EMAILInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017EMAILInfo.proto\"\206\017\n\tEMAILInfo\022\017\n\007attTy" +
      "pe\030\001 \003(\014\022\022\n\nattTypeCnt\030\002 \001(\r\022\023\n\013attFileN" +
      "ame\030\003 \003(\014\022\026\n\016attFileNameCnt\030\004 \001(\r\022\022\n\natt" +
      "ConSize\030\005 \003(\004\022\016\n\006attMD5\030\006 \003(\014\022\021\n\tattMD5C" +
      "nt\030\007 \001(\r\022\020\n\010authRelt\030\010 \001(\r\022\013\n\003BCC\030\t \003(\014\022" +
      "\014\n\004body\030\n \001(\014\022\022\n\nbodyTexCha\030\013 \001(\014\022\022\n\nbod" +
      "yTraEnc\030\014 \001(\014\022\017\n\007bodyMD5\030\r \001(\014\022\020\n\010bodyTy" +
      "pe\030\016 \003(\014\022\023\n\013bodyTypeCnt\030\017 \001(\r\022\017\n\007bodyURL" +
      "\030\020 \001(\014\022\022\n\nbodyURLCnt\030\021 \001(\r\022\017\n\007bodyLen\030\022 " +
      "\001(\r\022\r\n\005ByAsn\030\023 \003(\014\022\021\n\tByCountry\030\024 \003(\014\022\r\n" +
      "\005ByDom\030\025 \003(\014\022\020\n\010ByDomCnt\030\026 \001(\r\022\014\n\004ByIP\030\027" +
      " \001(\014\022\017\n\007ByIpCnt\030\030 \001(\r\022\n\n\002CC\030\031 \003(\014\022\r\n\005CCA" +
      "li\030\032 \003(\014\022\017\n\007Command\030\033 \003(\014\022\r\n\005count\030\034 \001(\r" +
      "\022\017\n\007content\030\035 \001(\014\022\017\n\007conType\030\036 \003(\014\022\022\n\nco" +
      "nTypeCnt\030\037 \001(\r\022\014\n\004date\030  \001(\r\022\023\n\013delivere" +
      "dTo\030! \001(\014\022\017\n\007FromAsn\030\" \003(\014\022\023\n\013FromCountr" +
      "y\030# \003(\014\022\017\n\007FromDom\030$ \003(\014\022\022\n\nFromDomCnt\030%" +
      " \001(\r\022\016\n\006FromIp\030& \003(\r\022\021\n\tFromIpCnt\030\' \001(\r\022" +
      "\017\n\007headSet\030( \003(\014\022\022\n\nheadSetCnt\030) \001(\r\022\014\n\004" +
      "host\030* \001(\014\022\014\n\004name\030+ \001(\014\022\n\n\002os\030, \001(\014\022\r\n\005" +
      "osVer\030- \001(\014\022\016\n\006vendor\030. \001(\014\022\013\n\003ver\030/ \001(\014" +
      "\022\016\n\006emaInd\0300 \001(\014\022\r\n\005login\0301 \001(\014\022\020\n\010login" +
      "srv\0302 \001(\014\022\020\n\010mailFrom\0303 \003(\014\022\023\n\013mailFromD" +
      "om\0304 \003(\014\022\026\n\016mailFromDomCnt\0305 \001(\014\022\017\n\007mime" +
      "Ver\0306 \001(\014\022\022\n\nmimeVerCnt\0307 \001(\r\022\r\n\005msgID\0308" +
      " \003(\014\022\020\n\010msgIDCnt\0309 \001(\r\022\023\n\013emaProtType\030: " +
      "\001(\014\022\013\n\003pwd\030; \001(\014\022\016\n\006rcptTo\030< \003(\014\022\021\n\trcpt" +
      "ToDom\030= \003(\014\022\024\n\014rcptToDomCnt\030> \001(\r\022\r\n\005rep" +
      "To\030? \001(\014\022\020\n\010received\030@ \003(\014\022\021\n\trcvrEmail\030" +
      "A \003(\014\022\017\n\007rcvrAli\030B \003(\014\022\022\n\nrcvrAliCnt\030C \001" +
      "(\r\022\024\n\014rcvrEmailCnt\030D \001(\r\022\017\n\007rcvrDom\030E \003(" +
      "\014\022\024\n\014resentSrvAge\030F \001(\014\022\022\n\nresentDate\030G " +
      "\001(\r\022\022\n\nresentFrom\030H \001(\014\022\020\n\010resentTo\030I \003(" +
      "\014\022\023\n\013senderEmail\030J \001(\014\022\021\n\tsenderAli\030K \001(" +
      "\014\022\021\n\tsenderDom\030L \001(\014\022\017\n\007SMTPSrv\030M \001(\014\022\022\n" +
      "\nSMTPSrvAge\030N \001(\014\022\023\n\013receivedSPF\030O \001(\014\022\020" +
      "\n\010startTLS\030P \001(\r\022\014\n\004subj\030Q \001(\014\022\020\n\010subj_c" +
      "nt\030R \001(\r\022\016\n\006usrAge\030S \001(\014\022\024\n\014emailVersion" +
      "\030T \001(\014\022\016\n\006rcvWit\030U \001(\014\022\014\n\004xMai\030V \001(\014\022\017\n\007" +
      "xMaiCnt\030W \001(\r\022\016\n\006xOriIP\030X \001(\r\022\026\n\016senderE" +
      "mailCnt\030Y \001(\r\022\n\n\002fw\030Z \001(\014\022\014\n\004befw\030[ \001(\014\022" +
      "\017\n\007rcvDate\030\\ \001(\004\022\021\n\trcvSrvAge\030] \001(\014\022\021\n\tc" +
      "onTraEnc\030^ \001(\014\022\021\n\tconTexCha\030_ \001(\014\022\020\n\010rea" +
      "lFrom\030` \001(\014\022\016\n\006realTo\030a \001(\014\022\021\n\temaActRep" +
      "\030b \001(\014\022\023\n\013rcvFromName\030c \001(\014\022\017\n\007errType\030d" +
      " \001(\014\022\027\n\017contentWithHtml\030e \001(\014\022\017\n\007charset" +
      "\030f \001(\014\022\022\n\ncontentLen\030g \001(\r\022\016\n\006isBeFw\030h \001" +
      "(\014\022\026\n\016attachmentPath\030i \001(\014\022\016\n\006banner\030j \001" +
      "(\014\022\026\n\016senderSoftware\030k \003(\014"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_EMAILInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_EMAILInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_EMAILInfo_descriptor,
        new java.lang.String[] { "AttType", "AttTypeCnt", "AttFileName", "AttFileNameCnt", "AttConSize", "AttMD5", "AttMD5Cnt", "AuthRelt", "BCC", "Body", "BodyTexCha", "BodyTraEnc", "BodyMD5", "BodyType", "BodyTypeCnt", "BodyURL", "BodyURLCnt", "BodyLen", "ByAsn", "ByCountry", "ByDom", "ByDomCnt", "ByIP", "ByIpCnt", "CC", "CCAli", "Command", "Count", "Content", "ConType", "ConTypeCnt", "Date", "DeliveredTo", "FromAsn", "FromCountry", "FromDom", "FromDomCnt", "FromIp", "FromIpCnt", "HeadSet", "HeadSetCnt", "Host", "Name", "Os", "OsVer", "Vendor", "Ver", "EmaInd", "Login", "Loginsrv", "MailFrom", "MailFromDom", "MailFromDomCnt", "MimeVer", "MimeVerCnt", "MsgID", "MsgIDCnt", "EmaProtType", "Pwd", "RcptTo", "RcptToDom", "RcptToDomCnt", "RepTo", "Received", "RcvrEmail", "RcvrAli", "RcvrAliCnt", "RcvrEmailCnt", "RcvrDom", "ResentSrvAge", "ResentDate", "ResentFrom", "ResentTo", "SenderEmail", "SenderAli", "SenderDom", "SMTPSrv", "SMTPSrvAge", "ReceivedSPF", "StartTLS", "Subj", "SubjCnt", "UsrAge", "EmailVersion", "RcvWit", "XMai", "XMaiCnt", "XOriIP", "SenderEmailCnt", "Fw", "Befw", "RcvDate", "RcvSrvAge", "ConTraEnc", "ConTexCha", "RealFrom", "RealTo", "EmaActRep", "RcvFromName", "ErrType", "ContentWithHtml", "Charset", "ContentLen", "IsBeFw", "AttachmentPath", "Banner", "SenderSoftware", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
