package com.geeksec.common.constant;

import java.io.Serializable;

/**
 * @author: jerryzhou
 * @date: 2024/7/17 20:48
 * @Description: 检测类型对照，根据具体值确定告警日志类型取值范围
 **/
public class DetectTypeConstant implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 失陷情报告警信息
     */
    public static final int IOC_ALERT_INFO = 100;

    /**
     * 异常行为告警信息
     */
    public static final int IOB_ALERT_INFO = 101;

    /**
     * 攻击利用告警信息
     */
    public static final int IOA_ALERT_INFO = 102;

    /**
     * 工业物联网告警信息
     */
    public static final int IIOT_ALERT_INFO = 103;

    /**
     * 文件检测告警信息
     */
    public static final int FILE_ALERT_INFO = 104;

    /**
     * 密数据异常告警信息
     */
    public static final int CRYPTO_ALERT_INFO = 105;

    /**
     * 证书异常告警信息
     */
    public static final int CERT_ALERT_INFO = 106;

    /**
     * 邮件威胁告警信息
     */
    public static final int MAIL_ALERT_INFO = 107;

    /**
     * 移动网威胁告警信息
     */
    public static final int MOBILE_ALERT_INFO = 108;

    /**
     * 特色协议威胁告警信息
     */
    public static final int PROTO_ALERT_INFO = 109;
}
