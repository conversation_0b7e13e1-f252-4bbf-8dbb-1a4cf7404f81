package com.geeksec.common.utils;

import java.util.regex.Pattern;

/**
 * @author: jerryzhou
 * @date: 2024/8/13 17:03
 * @Description:
 **/
public class DomainValidator {

    // 域名的正则表达式模式
    private static final String DOMAIN_PATTERN =
            "^((?!-)[A-Za-z0-9-]{1,63}(?<!-)\\.)+[A-Za-z]{2,6}$";

    private static final Pattern PATTERN = Pattern.compile(DOMAIN_PATTERN);

    /**
     * 判断给定的字符串是否是有效的域名
     *
     * @param domain 要检查的域名字符串
     * @return 如果是有效的域名返回true,否则返回false
     */
    public static boolean isValidDomain(String domain) {
        if (domain == null || domain.isEmpty()) {
            return false;
        }

        // 检查域名总长度
        if (domain.length() > 253) {
            return false;
        }

        // 使用正则表达式匹配
        return PATTERN.matcher(domain).matches();
    }
}
