package com.geeksec.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 * DNS 请求响应类型
 */

@Getter
public enum DnsTypeEnum {

    A("A",1,0x0001,"IPv4地址记录"),
    NS("NS",2,0x0002,"权威名称服务器"),
    CNAME("CNAME",5,0x0005,"规范名称记录"),
    SOA("SOA",6,0x0006,"起始授权机构"),
    PTR("PTR",12,0x000C,"指针记录（反向解析）"),
    MX("MX",15,0x000F,"邮件交换记录"),
    TXT("TXT",16,0x0010,"文本记录"),
    AAAA("AAAA",28,0x001C,"IPv6地址记录"),
    SRV("SRV",33,0x0021,"服务定位记录"),
    DNAME("DNAME",39,0x0027,"域别名重定向"),
    DS("DS",43,0x002B,"DNSSEC委托签名"),
    RRSIG("RRSIG",46,0x002E,"DNSSEC资源记录签名"),
    NSEC("NSEC",47,0x002F,"DNSSEC下一安全记录"),
    DNSKEY("DNSKEY",48,0x0030,"DNSSEC公钥记录"),
    NSEC3("NSEC3",50,0x0032,"DNSSEC NSEC第三版"),
    CAA("CAA",257,0x0101,"证书颁发机构授权"),
    ANY("ANY",255,0x00FF,"请求所有记录（仅查询用）");

    /**
     * 类型
     */
    private String type;

    /**
     * 10进制值
     */
    private Integer code;

    /**
     * 16进制值
     */
    private Integer hexCode;

    /**
     * 简介
     */
    private String explain;

    DnsTypeEnum(String type, int code, int hexCode, String explain) {
        this.type = type;
        this.code = code;
        this.hexCode = hexCode;
        this.explain = explain;
    }

    // 通过type得到code
    public static Integer getCodeByType(String type) {
        for (DnsTypeEnum dnsTypeEnum : DnsTypeEnum.values()) {
            if (dnsTypeEnum.getType().equals(type)) {
                return dnsTypeEnum.getCode();
            }
        }
        return null;
    }

    // 通过code得到type
    public static String getTypeByCode(Integer code) {
        for (DnsTypeEnum dnsTypeEnum : DnsTypeEnum.values()) {
            if (dnsTypeEnum.getCode().equals(code)) {
                return dnsTypeEnum.getType();
            }
        }
        return null;
    }

    // 通过code得到hex
    public static Integer getHexByCode(Integer code) {
        for (DnsTypeEnum dnsTypeEnum : DnsTypeEnum.values()) {
            if (dnsTypeEnum.getCode().equals(code)) {
                return dnsTypeEnum.getHexCode();
            }
        }
        return null;
    }

    // 通过hex得到code
    public static Integer getCodeByHex(Integer hexCode) {
        for (DnsTypeEnum dnsTypeEnum : DnsTypeEnum.values()) {
            if (dnsTypeEnum.getHexCode().equals(hexCode)) {
                return dnsTypeEnum.getCode();
            }
        }
        return null;
    }

    // 通过hex得到type
    public static String getTypeByHex(Integer hexCode) {
        for (DnsTypeEnum dnsTypeEnum : DnsTypeEnum.values()) {
            if (dnsTypeEnum.getHexCode().equals(hexCode)) {
                return dnsTypeEnum.getType();
            }
        }
        return null;
    }

    // 通过type得到hex
    public static Integer getHexByType(String type) {
        for (DnsTypeEnum dnsTypeEnum : DnsTypeEnum.values()) {
            if (dnsTypeEnum.getType().equals(type)) {
                return dnsTypeEnum.getHexCode();
            }
        }
        return null;
    }
}
