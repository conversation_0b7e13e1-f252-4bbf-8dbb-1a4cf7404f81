package com.geeksec.proto.protocol;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: X509CerInfo.proto
// Protobuf Java Version: 4.29.4

public final class X509CerInfoOuterClass {
  private X509CerInfoOuterClass() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      X509CerInfoOuterClass.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface X509CerInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:X509CerInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint64 ProtabID = 1;</code>
     * @return Whether the protabID field is set.
     */
    boolean hasProtabID();
    /**
     * <code>optional uint64 ProtabID = 1;</code>
     * @return The protabID.
     */
    long getProtabID();

    /**
     * <code>optional uint32 ver = 2;</code>
     * @return Whether the ver field is set.
     */
    boolean hasVer();
    /**
     * <code>optional uint32 ver = 2;</code>
     * @return The ver.
     */
    int getVer();

    /**
     * <code>optional bytes srvNum = 3;</code>
     * @return Whether the srvNum field is set.
     */
    boolean hasSrvNum();
    /**
     * <code>optional bytes srvNum = 3;</code>
     * @return The srvNum.
     */
    com.google.protobuf.ByteString getSrvNum();

    /**
     * <code>optional uint32 issDataLen = 4;</code>
     * @return Whether the issDataLen field is set.
     */
    boolean hasIssDataLen();
    /**
     * <code>optional uint32 issDataLen = 4;</code>
     * @return The issDataLen.
     */
    int getIssDataLen();

    /**
     * <code>optional bytes issComName = 5;</code>
     * @return Whether the issComName field is set.
     */
    boolean hasIssComName();
    /**
     * <code>optional bytes issComName = 5;</code>
     * @return The issComName.
     */
    com.google.protobuf.ByteString getIssComName();

    /**
     * <code>optional bytes issConName = 6;</code>
     * @return Whether the issConName field is set.
     */
    boolean hasIssConName();
    /**
     * <code>optional bytes issConName = 6;</code>
     * @return The issConName.
     */
    com.google.protobuf.ByteString getIssConName();

    /**
     * <code>optional bytes issLoaName = 7;</code>
     * @return Whether the issLoaName field is set.
     */
    boolean hasIssLoaName();
    /**
     * <code>optional bytes issLoaName = 7;</code>
     * @return The issLoaName.
     */
    com.google.protobuf.ByteString getIssLoaName();

    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     * @return Whether the issStaOrProName field is set.
     */
    boolean hasIssStaOrProName();
    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     * @return The issStaOrProName.
     */
    com.google.protobuf.ByteString getIssStaOrProName();

    /**
     * <code>optional bytes issStrAddr = 9;</code>
     * @return Whether the issStrAddr field is set.
     */
    boolean hasIssStrAddr();
    /**
     * <code>optional bytes issStrAddr = 9;</code>
     * @return The issStrAddr.
     */
    com.google.protobuf.ByteString getIssStrAddr();

    /**
     * <code>optional bytes issOrgName = 10;</code>
     * @return Whether the issOrgName field is set.
     */
    boolean hasIssOrgName();
    /**
     * <code>optional bytes issOrgName = 10;</code>
     * @return The issOrgName.
     */
    com.google.protobuf.ByteString getIssOrgName();

    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     * @return Whether the issOrgUniName field is set.
     */
    boolean hasIssOrgUniName();
    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     * @return The issOrgUniName.
     */
    com.google.protobuf.ByteString getIssOrgUniName();

    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     * @return Whether the issPosOffBox field is set.
     */
    boolean hasIssPosOffBox();
    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     * @return The issPosOffBox.
     */
    com.google.protobuf.ByteString getIssPosOffBox();

    /**
     * <code>optional bytes subComName = 13;</code>
     * @return Whether the subComName field is set.
     */
    boolean hasSubComName();
    /**
     * <code>optional bytes subComName = 13;</code>
     * @return The subComName.
     */
    com.google.protobuf.ByteString getSubComName();

    /**
     * <code>optional bytes subConName = 14;</code>
     * @return Whether the subConName field is set.
     */
    boolean hasSubConName();
    /**
     * <code>optional bytes subConName = 14;</code>
     * @return The subConName.
     */
    com.google.protobuf.ByteString getSubConName();

    /**
     * <code>optional bytes subLoaName = 15;</code>
     * @return Whether the subLoaName field is set.
     */
    boolean hasSubLoaName();
    /**
     * <code>optional bytes subLoaName = 15;</code>
     * @return The subLoaName.
     */
    com.google.protobuf.ByteString getSubLoaName();

    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     * @return Whether the subStaOrProName field is set.
     */
    boolean hasSubStaOrProName();
    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     * @return The subStaOrProName.
     */
    com.google.protobuf.ByteString getSubStaOrProName();

    /**
     * <code>optional bytes subStrAddr = 17;</code>
     * @return Whether the subStrAddr field is set.
     */
    boolean hasSubStrAddr();
    /**
     * <code>optional bytes subStrAddr = 17;</code>
     * @return The subStrAddr.
     */
    com.google.protobuf.ByteString getSubStrAddr();

    /**
     * <code>optional bytes subOrgName = 18;</code>
     * @return Whether the subOrgName field is set.
     */
    boolean hasSubOrgName();
    /**
     * <code>optional bytes subOrgName = 18;</code>
     * @return The subOrgName.
     */
    com.google.protobuf.ByteString getSubOrgName();

    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     * @return Whether the subOrgUniName field is set.
     */
    boolean hasSubOrgUniName();
    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     * @return The subOrgUniName.
     */
    com.google.protobuf.ByteString getSubOrgUniName();

    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     * @return Whether the subPosOffBox field is set.
     */
    boolean hasSubPosOffBox();
    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     * @return The subPosOffBox.
     */
    com.google.protobuf.ByteString getSubPosOffBox();

    /**
     * <code>optional uint64 valNotBef = 21;</code>
     * @return Whether the valNotBef field is set.
     */
    boolean hasValNotBef();
    /**
     * <code>optional uint64 valNotBef = 21;</code>
     * @return The valNotBef.
     */
    long getValNotBef();

    /**
     * <code>optional uint64 valNotAft = 22;</code>
     * @return Whether the valNotAft field is set.
     */
    boolean hasValNotAft();
    /**
     * <code>optional uint64 valNotAft = 22;</code>
     * @return The valNotAft.
     */
    long getValNotAft();

    /**
     * <code>optional bytes RSAMod = 23;</code>
     * @return Whether the rSAMod field is set.
     */
    boolean hasRSAMod();
    /**
     * <code>optional bytes RSAMod = 23;</code>
     * @return The rSAMod.
     */
    com.google.protobuf.ByteString getRSAMod();

    /**
     * <code>optional bytes RSAExp = 24;</code>
     * @return Whether the rSAExp field is set.
     */
    boolean hasRSAExp();
    /**
     * <code>optional bytes RSAExp = 24;</code>
     * @return The rSAExp.
     */
    com.google.protobuf.ByteString getRSAExp();

    /**
     * <code>optional bytes DHPriMod = 25;</code>
     * @return Whether the dHPriMod field is set.
     */
    boolean hasDHPriMod();
    /**
     * <code>optional bytes DHPriMod = 25;</code>
     * @return The dHPriMod.
     */
    com.google.protobuf.ByteString getDHPriMod();

    /**
     * <code>optional bytes DHPGen = 26;</code>
     * @return Whether the dHPGen field is set.
     */
    boolean hasDHPGen();
    /**
     * <code>optional bytes DHPGen = 26;</code>
     * @return The dHPGen.
     */
    com.google.protobuf.ByteString getDHPGen();

    /**
     * <code>optional bytes DHPubKey = 27;</code>
     * @return Whether the dHPubKey field is set.
     */
    boolean hasDHPubKey();
    /**
     * <code>optional bytes DHPubKey = 27;</code>
     * @return The dHPubKey.
     */
    com.google.protobuf.ByteString getDHPubKey();

    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     * @return Whether the dSAPubKeyP field is set.
     */
    boolean hasDSAPubKeyP();
    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     * @return The dSAPubKeyP.
     */
    com.google.protobuf.ByteString getDSAPubKeyP();

    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     * @return Whether the dSAPubKeyQ field is set.
     */
    boolean hasDSAPubKeyQ();
    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     * @return The dSAPubKeyQ.
     */
    com.google.protobuf.ByteString getDSAPubKeyQ();

    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     * @return Whether the dSAPubKeyG field is set.
     */
    boolean hasDSAPubKeyG();
    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     * @return The dSAPubKeyG.
     */
    com.google.protobuf.ByteString getDSAPubKeyG();

    /**
     * <code>optional bytes sigAlg = 31;</code>
     * @return Whether the sigAlg field is set.
     */
    boolean hasSigAlg();
    /**
     * <code>optional bytes sigAlg = 31;</code>
     * @return The sigAlg.
     */
    com.google.protobuf.ByteString getSigAlg();

    /**
     * <code>optional bytes sigVal = 32;</code>
     * @return Whether the sigVal field is set.
     */
    boolean hasSigVal();
    /**
     * <code>optional bytes sigVal = 32;</code>
     * @return The sigVal.
     */
    com.google.protobuf.ByteString getSigVal();

    /**
     * <code>optional bytes authKeyID = 33;</code>
     * @return Whether the authKeyID field is set.
     */
    boolean hasAuthKeyID();
    /**
     * <code>optional bytes authKeyID = 33;</code>
     * @return The authKeyID.
     */
    com.google.protobuf.ByteString getAuthKeyID();

    /**
     * <code>optional bytes subKeyID = 34;</code>
     * @return Whether the subKeyID field is set.
     */
    boolean hasSubKeyID();
    /**
     * <code>optional bytes subKeyID = 34;</code>
     * @return The subKeyID.
     */
    com.google.protobuf.ByteString getSubKeyID();

    /**
     * <code>optional bytes keyUsage = 35;</code>
     * @return Whether the keyUsage field is set.
     */
    boolean hasKeyUsage();
    /**
     * <code>optional bytes keyUsage = 35;</code>
     * @return The keyUsage.
     */
    com.google.protobuf.ByteString getKeyUsage();

    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     * @return Whether the priKeyUsaPerNotBef field is set.
     */
    boolean hasPriKeyUsaPerNotBef();
    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     * @return The priKeyUsaPerNotBef.
     */
    long getPriKeyUsaPerNotBef();

    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     * @return Whether the priKeyUsaPerNotAft field is set.
     */
    boolean hasPriKeyUsaPerNotAft();
    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     * @return The priKeyUsaPerNotAft.
     */
    long getPriKeyUsaPerNotAft();

    /**
     * <code>optional bytes certPol = 38;</code>
     * @return Whether the certPol field is set.
     */
    boolean hasCertPol();
    /**
     * <code>optional bytes certPol = 38;</code>
     * @return The certPol.
     */
    com.google.protobuf.ByteString getCertPol();

    /**
     * <code>optional bytes subAltDNS = 39;</code>
     * @return Whether the subAltDNS field is set.
     */
    boolean hasSubAltDNS();
    /**
     * <code>optional bytes subAltDNS = 39;</code>
     * @return The subAltDNS.
     */
    com.google.protobuf.ByteString getSubAltDNS();

    /**
     * <code>optional bytes subAltIP = 40;</code>
     * @return Whether the subAltIP field is set.
     */
    boolean hasSubAltIP();
    /**
     * <code>optional bytes subAltIP = 40;</code>
     * @return The subAltIP.
     */
    com.google.protobuf.ByteString getSubAltIP();

    /**
     * <code>optional bytes subAltName = 41;</code>
     * @return Whether the subAltName field is set.
     */
    boolean hasSubAltName();
    /**
     * <code>optional bytes subAltName = 41;</code>
     * @return The subAltName.
     */
    com.google.protobuf.ByteString getSubAltName();

    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     * @return Whether the issAltNameSys field is set.
     */
    boolean hasIssAltNameSys();
    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     * @return The issAltNameSys.
     */
    com.google.protobuf.ByteString getIssAltNameSys();

    /**
     * <code>optional bytes issAltIP = 43;</code>
     * @return Whether the issAltIP field is set.
     */
    boolean hasIssAltIP();
    /**
     * <code>optional bytes issAltIP = 43;</code>
     * @return The issAltIP.
     */
    com.google.protobuf.ByteString getIssAltIP();

    /**
     * <code>optional bytes issAltName = 44;</code>
     * @return Whether the issAltName field is set.
     */
    boolean hasIssAltName();
    /**
     * <code>optional bytes issAltName = 44;</code>
     * @return The issAltName.
     */
    com.google.protobuf.ByteString getIssAltName();

    /**
     * <code>optional bytes subDirAtt = 45;</code>
     * @return Whether the subDirAtt field is set.
     */
    boolean hasSubDirAtt();
    /**
     * <code>optional bytes subDirAtt = 45;</code>
     * @return The subDirAtt.
     */
    com.google.protobuf.ByteString getSubDirAtt();

    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     * @return Whether the extKeyUsage field is set.
     */
    boolean hasExtKeyUsage();
    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     * @return The extKeyUsage.
     */
    com.google.protobuf.ByteString getExtKeyUsage();

    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     * @return Whether the certRevListSrc field is set.
     */
    boolean hasCertRevListSrc();
    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     * @return The certRevListSrc.
     */
    com.google.protobuf.ByteString getCertRevListSrc();

    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     * @return Whether the certAuthInfAccMet field is set.
     */
    boolean hasCertAuthInfAccMet();
    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     * @return The certAuthInfAccMet.
     */
    com.google.protobuf.ByteString getCertAuthInfAccMet();

    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     * @return Whether the certAuthInfAccLoc field is set.
     */
    boolean hasCertAuthInfAccLoc();
    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     * @return The certAuthInfAccLoc.
     */
    com.google.protobuf.ByteString getCertAuthInfAccLoc();

    /**
     * <code>optional uint32 extCnt = 50;</code>
     * @return Whether the extCnt field is set.
     */
    boolean hasExtCnt();
    /**
     * <code>optional uint32 extCnt = 50;</code>
     * @return The extCnt.
     */
    int getExtCnt();

    /**
     * <code>optional bytes Protabname = 51;</code>
     * @return Whether the protabname field is set.
     */
    boolean hasProtabname();
    /**
     * <code>optional bytes Protabname = 51;</code>
     * @return The protabname.
     */
    com.google.protobuf.ByteString getProtabname();

    /**
     * <code>optional bytes issuer = 52;</code>
     * @return Whether the issuer field is set.
     */
    boolean hasIssuer();
    /**
     * <code>optional bytes issuer = 52;</code>
     * @return The issuer.
     */
    com.google.protobuf.ByteString getIssuer();

    /**
     * <code>optional bytes subject = 53;</code>
     * @return Whether the subject field is set.
     */
    boolean hasSubject();
    /**
     * <code>optional bytes subject = 53;</code>
     * @return The subject.
     */
    com.google.protobuf.ByteString getSubject();

    /**
     * <code>optional uint32 daysRem = 54;</code>
     * @return Whether the daysRem field is set.
     */
    boolean hasDaysRem();
    /**
     * <code>optional uint32 daysRem = 54;</code>
     * @return The daysRem.
     */
    int getDaysRem();

    /**
     * <code>optional bytes pubkey = 55;</code>
     * @return Whether the pubkey field is set.
     */
    boolean hasPubkey();
    /**
     * <code>optional bytes pubkey = 55;</code>
     * @return The pubkey.
     */
    com.google.protobuf.ByteString getPubkey();

    /**
     * <code>optional bytes fpAlg = 56;</code>
     * @return Whether the fpAlg field is set.
     */
    boolean hasFpAlg();
    /**
     * <code>optional bytes fpAlg = 56;</code>
     * @return The fpAlg.
     */
    com.google.protobuf.ByteString getFpAlg();

    /**
     * <code>optional bytes hash = 57;</code>
     * @return Whether the hash field is set.
     */
    boolean hasHash();
    /**
     * <code>optional bytes hash = 57;</code>
     * @return The hash.
     */
    com.google.protobuf.ByteString getHash();

    /**
     * <code>optional bytes extSet = 58;</code>
     * @return Whether the extSet field is set.
     */
    boolean hasExtSet();
    /**
     * <code>optional bytes extSet = 58;</code>
     * @return The extSet.
     */
    com.google.protobuf.ByteString getExtSet();

    /**
     * <code>optional uint32 daysTotal = 59;</code>
     * @return Whether the daysTotal field is set.
     */
    boolean hasDaysTotal();
    /**
     * <code>optional uint32 daysTotal = 59;</code>
     * @return The daysTotal.
     */
    int getDaysTotal();

    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     * @return Whether the subAltDNSCnt field is set.
     */
    boolean hasSubAltDNSCnt();
    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     * @return The subAltDNSCnt.
     */
    int getSubAltDNSCnt();

    /**
     * <code>optional bytes authinfo = 61;</code>
     * @return Whether the authinfo field is set.
     */
    boolean hasAuthinfo();
    /**
     * <code>optional bytes authinfo = 61;</code>
     * @return The authinfo.
     */
    com.google.protobuf.ByteString getAuthinfo();

    /**
     * <code>optional bytes basicConsCA = 62;</code>
     * @return Whether the basicConsCA field is set.
     */
    boolean hasBasicConsCA();
    /**
     * <code>optional bytes basicConsCA = 62;</code>
     * @return The basicConsCA.
     */
    com.google.protobuf.ByteString getBasicConsCA();

    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     * @return Whether the basicConsPathLen field is set.
     */
    boolean hasBasicConsPathLen();
    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     * @return The basicConsPathLen.
     */
    com.google.protobuf.ByteString getBasicConsPathLen();

    /**
     * <code>optional bytes basicCons = 64;</code>
     * @return Whether the basicCons field is set.
     */
    boolean hasBasicCons();
    /**
     * <code>optional bytes basicCons = 64;</code>
     * @return The basicCons.
     */
    com.google.protobuf.ByteString getBasicCons();

    /**
     * <code>optional bytes KeyPur = 65;</code>
     * @return Whether the keyPur field is set.
     */
    boolean hasKeyPur();
    /**
     * <code>optional bytes KeyPur = 65;</code>
     * @return The keyPur.
     */
    com.google.protobuf.ByteString getKeyPur();

    /**
     * <code>optional uint32 Certype = 66;</code>
     * @return Whether the certype field is set.
     */
    boolean hasCertype();
    /**
     * <code>optional uint32 Certype = 66;</code>
     * @return The certype.
     */
    int getCertype();

    /**
     * <code>optional bytes certFullText = 67;</code>
     * @return Whether the certFullText field is set.
     */
    boolean hasCertFullText();
    /**
     * <code>optional bytes certFullText = 67;</code>
     * @return The certFullText.
     */
    com.google.protobuf.ByteString getCertFullText();

    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     * @return Whether the alternativeIpCount field is set.
     */
    boolean hasAlternativeIpCount();
    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     * @return The alternativeIpCount.
     */
    int getAlternativeIpCount();

    /**
     * <code>optional uint32 source = 69;</code>
     * @return Whether the source field is set.
     */
    boolean hasSource();
    /**
     * <code>optional uint32 source = 69;</code>
     * @return The source.
     */
    int getSource();
  }
  /**
   * Protobuf type {@code X509CerInfo}
   */
  public static final class X509CerInfo extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:X509CerInfo)
      X509CerInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        X509CerInfo.class.getName());
    }
    // Use X509CerInfo.newBuilder() to construct.
    private X509CerInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private X509CerInfo() {
      srvNum_ = com.google.protobuf.ByteString.EMPTY;
      issComName_ = com.google.protobuf.ByteString.EMPTY;
      issConName_ = com.google.protobuf.ByteString.EMPTY;
      issLoaName_ = com.google.protobuf.ByteString.EMPTY;
      issStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      issStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      issOrgName_ = com.google.protobuf.ByteString.EMPTY;
      issOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      issPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      subComName_ = com.google.protobuf.ByteString.EMPTY;
      subConName_ = com.google.protobuf.ByteString.EMPTY;
      subLoaName_ = com.google.protobuf.ByteString.EMPTY;
      subStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      subStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      subOrgName_ = com.google.protobuf.ByteString.EMPTY;
      subOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      subPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      rSAMod_ = com.google.protobuf.ByteString.EMPTY;
      rSAExp_ = com.google.protobuf.ByteString.EMPTY;
      dHPriMod_ = com.google.protobuf.ByteString.EMPTY;
      dHPGen_ = com.google.protobuf.ByteString.EMPTY;
      dHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      dSAPubKeyP_ = com.google.protobuf.ByteString.EMPTY;
      dSAPubKeyQ_ = com.google.protobuf.ByteString.EMPTY;
      dSAPubKeyG_ = com.google.protobuf.ByteString.EMPTY;
      sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      sigVal_ = com.google.protobuf.ByteString.EMPTY;
      authKeyID_ = com.google.protobuf.ByteString.EMPTY;
      subKeyID_ = com.google.protobuf.ByteString.EMPTY;
      keyUsage_ = com.google.protobuf.ByteString.EMPTY;
      certPol_ = com.google.protobuf.ByteString.EMPTY;
      subAltDNS_ = com.google.protobuf.ByteString.EMPTY;
      subAltIP_ = com.google.protobuf.ByteString.EMPTY;
      subAltName_ = com.google.protobuf.ByteString.EMPTY;
      issAltNameSys_ = com.google.protobuf.ByteString.EMPTY;
      issAltIP_ = com.google.protobuf.ByteString.EMPTY;
      issAltName_ = com.google.protobuf.ByteString.EMPTY;
      subDirAtt_ = com.google.protobuf.ByteString.EMPTY;
      extKeyUsage_ = com.google.protobuf.ByteString.EMPTY;
      certRevListSrc_ = com.google.protobuf.ByteString.EMPTY;
      certAuthInfAccMet_ = com.google.protobuf.ByteString.EMPTY;
      certAuthInfAccLoc_ = com.google.protobuf.ByteString.EMPTY;
      protabname_ = com.google.protobuf.ByteString.EMPTY;
      issuer_ = com.google.protobuf.ByteString.EMPTY;
      subject_ = com.google.protobuf.ByteString.EMPTY;
      pubkey_ = com.google.protobuf.ByteString.EMPTY;
      fpAlg_ = com.google.protobuf.ByteString.EMPTY;
      hash_ = com.google.protobuf.ByteString.EMPTY;
      extSet_ = com.google.protobuf.ByteString.EMPTY;
      authinfo_ = com.google.protobuf.ByteString.EMPTY;
      basicConsCA_ = com.google.protobuf.ByteString.EMPTY;
      basicConsPathLen_ = com.google.protobuf.ByteString.EMPTY;
      basicCons_ = com.google.protobuf.ByteString.EMPTY;
      keyPur_ = com.google.protobuf.ByteString.EMPTY;
      certFullText_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return X509CerInfoOuterClass.internal_static_X509CerInfo_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return X509CerInfoOuterClass.internal_static_X509CerInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              X509CerInfo.class, Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    public static final int PROTABID_FIELD_NUMBER = 1;
    private long protabID_ = 0L;
    /**
     * <code>optional uint64 ProtabID = 1;</code>
     * @return Whether the protabID field is set.
     */
    @Override
    public boolean hasProtabID() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint64 ProtabID = 1;</code>
     * @return The protabID.
     */
    @Override
    public long getProtabID() {
      return protabID_;
    }

    public static final int VER_FIELD_NUMBER = 2;
    private int ver_ = 0;
    /**
     * <code>optional uint32 ver = 2;</code>
     * @return Whether the ver field is set.
     */
    @Override
    public boolean hasVer() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 ver = 2;</code>
     * @return The ver.
     */
    @Override
    public int getVer() {
      return ver_;
    }

    public static final int SRVNUM_FIELD_NUMBER = 3;
    private com.google.protobuf.ByteString srvNum_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvNum = 3;</code>
     * @return Whether the srvNum field is set.
     */
    @Override
    public boolean hasSrvNum() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes srvNum = 3;</code>
     * @return The srvNum.
     */
    @Override
    public com.google.protobuf.ByteString getSrvNum() {
      return srvNum_;
    }

    public static final int ISSDATALEN_FIELD_NUMBER = 4;
    private int issDataLen_ = 0;
    /**
     * <code>optional uint32 issDataLen = 4;</code>
     * @return Whether the issDataLen field is set.
     */
    @Override
    public boolean hasIssDataLen() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 issDataLen = 4;</code>
     * @return The issDataLen.
     */
    @Override
    public int getIssDataLen() {
      return issDataLen_;
    }

    public static final int ISSCOMNAME_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString issComName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issComName = 5;</code>
     * @return Whether the issComName field is set.
     */
    @Override
    public boolean hasIssComName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes issComName = 5;</code>
     * @return The issComName.
     */
    @Override
    public com.google.protobuf.ByteString getIssComName() {
      return issComName_;
    }

    public static final int ISSCONNAME_FIELD_NUMBER = 6;
    private com.google.protobuf.ByteString issConName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issConName = 6;</code>
     * @return Whether the issConName field is set.
     */
    @Override
    public boolean hasIssConName() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes issConName = 6;</code>
     * @return The issConName.
     */
    @Override
    public com.google.protobuf.ByteString getIssConName() {
      return issConName_;
    }

    public static final int ISSLOANAME_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString issLoaName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issLoaName = 7;</code>
     * @return Whether the issLoaName field is set.
     */
    @Override
    public boolean hasIssLoaName() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes issLoaName = 7;</code>
     * @return The issLoaName.
     */
    @Override
    public com.google.protobuf.ByteString getIssLoaName() {
      return issLoaName_;
    }

    public static final int ISSSTAORPRONAME_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString issStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     * @return Whether the issStaOrProName field is set.
     */
    @Override
    public boolean hasIssStaOrProName() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes issStaOrProName = 8;</code>
     * @return The issStaOrProName.
     */
    @Override
    public com.google.protobuf.ByteString getIssStaOrProName() {
      return issStaOrProName_;
    }

    public static final int ISSSTRADDR_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString issStrAddr_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issStrAddr = 9;</code>
     * @return Whether the issStrAddr field is set.
     */
    @Override
    public boolean hasIssStrAddr() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes issStrAddr = 9;</code>
     * @return The issStrAddr.
     */
    @Override
    public com.google.protobuf.ByteString getIssStrAddr() {
      return issStrAddr_;
    }

    public static final int ISSORGNAME_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString issOrgName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issOrgName = 10;</code>
     * @return Whether the issOrgName field is set.
     */
    @Override
    public boolean hasIssOrgName() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes issOrgName = 10;</code>
     * @return The issOrgName.
     */
    @Override
    public com.google.protobuf.ByteString getIssOrgName() {
      return issOrgName_;
    }

    public static final int ISSORGUNINAME_FIELD_NUMBER = 11;
    private com.google.protobuf.ByteString issOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     * @return Whether the issOrgUniName field is set.
     */
    @Override
    public boolean hasIssOrgUniName() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes issOrgUniName = 11;</code>
     * @return The issOrgUniName.
     */
    @Override
    public com.google.protobuf.ByteString getIssOrgUniName() {
      return issOrgUniName_;
    }

    public static final int ISSPOSOFFBOX_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString issPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     * @return Whether the issPosOffBox field is set.
     */
    @Override
    public boolean hasIssPosOffBox() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes issPosOffBox = 12;</code>
     * @return The issPosOffBox.
     */
    @Override
    public com.google.protobuf.ByteString getIssPosOffBox() {
      return issPosOffBox_;
    }

    public static final int SUBCOMNAME_FIELD_NUMBER = 13;
    private com.google.protobuf.ByteString subComName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subComName = 13;</code>
     * @return Whether the subComName field is set.
     */
    @Override
    public boolean hasSubComName() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes subComName = 13;</code>
     * @return The subComName.
     */
    @Override
    public com.google.protobuf.ByteString getSubComName() {
      return subComName_;
    }

    public static final int SUBCONNAME_FIELD_NUMBER = 14;
    private com.google.protobuf.ByteString subConName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subConName = 14;</code>
     * @return Whether the subConName field is set.
     */
    @Override
    public boolean hasSubConName() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes subConName = 14;</code>
     * @return The subConName.
     */
    @Override
    public com.google.protobuf.ByteString getSubConName() {
      return subConName_;
    }

    public static final int SUBLOANAME_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString subLoaName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subLoaName = 15;</code>
     * @return Whether the subLoaName field is set.
     */
    @Override
    public boolean hasSubLoaName() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes subLoaName = 15;</code>
     * @return The subLoaName.
     */
    @Override
    public com.google.protobuf.ByteString getSubLoaName() {
      return subLoaName_;
    }

    public static final int SUBSTAORPRONAME_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString subStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     * @return Whether the subStaOrProName field is set.
     */
    @Override
    public boolean hasSubStaOrProName() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes subStaOrProName = 16;</code>
     * @return The subStaOrProName.
     */
    @Override
    public com.google.protobuf.ByteString getSubStaOrProName() {
      return subStaOrProName_;
    }

    public static final int SUBSTRADDR_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString subStrAddr_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subStrAddr = 17;</code>
     * @return Whether the subStrAddr field is set.
     */
    @Override
    public boolean hasSubStrAddr() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes subStrAddr = 17;</code>
     * @return The subStrAddr.
     */
    @Override
    public com.google.protobuf.ByteString getSubStrAddr() {
      return subStrAddr_;
    }

    public static final int SUBORGNAME_FIELD_NUMBER = 18;
    private com.google.protobuf.ByteString subOrgName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subOrgName = 18;</code>
     * @return Whether the subOrgName field is set.
     */
    @Override
    public boolean hasSubOrgName() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes subOrgName = 18;</code>
     * @return The subOrgName.
     */
    @Override
    public com.google.protobuf.ByteString getSubOrgName() {
      return subOrgName_;
    }

    public static final int SUBORGUNINAME_FIELD_NUMBER = 19;
    private com.google.protobuf.ByteString subOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     * @return Whether the subOrgUniName field is set.
     */
    @Override
    public boolean hasSubOrgUniName() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes subOrgUniName = 19;</code>
     * @return The subOrgUniName.
     */
    @Override
    public com.google.protobuf.ByteString getSubOrgUniName() {
      return subOrgUniName_;
    }

    public static final int SUBPOSOFFBOX_FIELD_NUMBER = 20;
    private com.google.protobuf.ByteString subPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     * @return Whether the subPosOffBox field is set.
     */
    @Override
    public boolean hasSubPosOffBox() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes subPosOffBox = 20;</code>
     * @return The subPosOffBox.
     */
    @Override
    public com.google.protobuf.ByteString getSubPosOffBox() {
      return subPosOffBox_;
    }

    public static final int VALNOTBEF_FIELD_NUMBER = 21;
    private long valNotBef_ = 0L;
    /**
     * <code>optional uint64 valNotBef = 21;</code>
     * @return Whether the valNotBef field is set.
     */
    @Override
    public boolean hasValNotBef() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional uint64 valNotBef = 21;</code>
     * @return The valNotBef.
     */
    @Override
    public long getValNotBef() {
      return valNotBef_;
    }

    public static final int VALNOTAFT_FIELD_NUMBER = 22;
    private long valNotAft_ = 0L;
    /**
     * <code>optional uint64 valNotAft = 22;</code>
     * @return Whether the valNotAft field is set.
     */
    @Override
    public boolean hasValNotAft() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint64 valNotAft = 22;</code>
     * @return The valNotAft.
     */
    @Override
    public long getValNotAft() {
      return valNotAft_;
    }

    public static final int RSAMOD_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString rSAMod_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes RSAMod = 23;</code>
     * @return Whether the rSAMod field is set.
     */
    @Override
    public boolean hasRSAMod() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes RSAMod = 23;</code>
     * @return The rSAMod.
     */
    @Override
    public com.google.protobuf.ByteString getRSAMod() {
      return rSAMod_;
    }

    public static final int RSAEXP_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString rSAExp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes RSAExp = 24;</code>
     * @return Whether the rSAExp field is set.
     */
    @Override
    public boolean hasRSAExp() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes RSAExp = 24;</code>
     * @return The rSAExp.
     */
    @Override
    public com.google.protobuf.ByteString getRSAExp() {
      return rSAExp_;
    }

    public static final int DHPRIMOD_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString dHPriMod_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHPriMod = 25;</code>
     * @return Whether the dHPriMod field is set.
     */
    @Override
    public boolean hasDHPriMod() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes DHPriMod = 25;</code>
     * @return The dHPriMod.
     */
    @Override
    public com.google.protobuf.ByteString getDHPriMod() {
      return dHPriMod_;
    }

    public static final int DHPGEN_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString dHPGen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHPGen = 26;</code>
     * @return Whether the dHPGen field is set.
     */
    @Override
    public boolean hasDHPGen() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes DHPGen = 26;</code>
     * @return The dHPGen.
     */
    @Override
    public com.google.protobuf.ByteString getDHPGen() {
      return dHPGen_;
    }

    public static final int DHPUBKEY_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString dHPubKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHPubKey = 27;</code>
     * @return Whether the dHPubKey field is set.
     */
    @Override
    public boolean hasDHPubKey() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes DHPubKey = 27;</code>
     * @return The dHPubKey.
     */
    @Override
    public com.google.protobuf.ByteString getDHPubKey() {
      return dHPubKey_;
    }

    public static final int DSAPUBKEYP_FIELD_NUMBER = 28;
    private com.google.protobuf.ByteString dSAPubKeyP_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     * @return Whether the dSAPubKeyP field is set.
     */
    @Override
    public boolean hasDSAPubKeyP() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes DSAPubKeyP = 28;</code>
     * @return The dSAPubKeyP.
     */
    @Override
    public com.google.protobuf.ByteString getDSAPubKeyP() {
      return dSAPubKeyP_;
    }

    public static final int DSAPUBKEYQ_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString dSAPubKeyQ_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     * @return Whether the dSAPubKeyQ field is set.
     */
    @Override
    public boolean hasDSAPubKeyQ() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes DSAPubKeyQ = 29;</code>
     * @return The dSAPubKeyQ.
     */
    @Override
    public com.google.protobuf.ByteString getDSAPubKeyQ() {
      return dSAPubKeyQ_;
    }

    public static final int DSAPUBKEYG_FIELD_NUMBER = 30;
    private com.google.protobuf.ByteString dSAPubKeyG_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     * @return Whether the dSAPubKeyG field is set.
     */
    @Override
    public boolean hasDSAPubKeyG() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes DSAPubKeyG = 30;</code>
     * @return The dSAPubKeyG.
     */
    @Override
    public com.google.protobuf.ByteString getDSAPubKeyG() {
      return dSAPubKeyG_;
    }

    public static final int SIGALG_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString sigAlg_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes sigAlg = 31;</code>
     * @return Whether the sigAlg field is set.
     */
    @Override
    public boolean hasSigAlg() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes sigAlg = 31;</code>
     * @return The sigAlg.
     */
    @Override
    public com.google.protobuf.ByteString getSigAlg() {
      return sigAlg_;
    }

    public static final int SIGVAL_FIELD_NUMBER = 32;
    private com.google.protobuf.ByteString sigVal_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes sigVal = 32;</code>
     * @return Whether the sigVal field is set.
     */
    @Override
    public boolean hasSigVal() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <code>optional bytes sigVal = 32;</code>
     * @return The sigVal.
     */
    @Override
    public com.google.protobuf.ByteString getSigVal() {
      return sigVal_;
    }

    public static final int AUTHKEYID_FIELD_NUMBER = 33;
    private com.google.protobuf.ByteString authKeyID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes authKeyID = 33;</code>
     * @return Whether the authKeyID field is set.
     */
    @Override
    public boolean hasAuthKeyID() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes authKeyID = 33;</code>
     * @return The authKeyID.
     */
    @Override
    public com.google.protobuf.ByteString getAuthKeyID() {
      return authKeyID_;
    }

    public static final int SUBKEYID_FIELD_NUMBER = 34;
    private com.google.protobuf.ByteString subKeyID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subKeyID = 34;</code>
     * @return Whether the subKeyID field is set.
     */
    @Override
    public boolean hasSubKeyID() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes subKeyID = 34;</code>
     * @return The subKeyID.
     */
    @Override
    public com.google.protobuf.ByteString getSubKeyID() {
      return subKeyID_;
    }

    public static final int KEYUSAGE_FIELD_NUMBER = 35;
    private com.google.protobuf.ByteString keyUsage_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes keyUsage = 35;</code>
     * @return Whether the keyUsage field is set.
     */
    @Override
    public boolean hasKeyUsage() {
      return ((bitField1_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes keyUsage = 35;</code>
     * @return The keyUsage.
     */
    @Override
    public com.google.protobuf.ByteString getKeyUsage() {
      return keyUsage_;
    }

    public static final int PRIKEYUSAPERNOTBEF_FIELD_NUMBER = 36;
    private long priKeyUsaPerNotBef_ = 0L;
    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     * @return Whether the priKeyUsaPerNotBef field is set.
     */
    @Override
    public boolean hasPriKeyUsaPerNotBef() {
      return ((bitField1_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
     * @return The priKeyUsaPerNotBef.
     */
    @Override
    public long getPriKeyUsaPerNotBef() {
      return priKeyUsaPerNotBef_;
    }

    public static final int PRIKEYUSAPERNOTAFT_FIELD_NUMBER = 37;
    private long priKeyUsaPerNotAft_ = 0L;
    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     * @return Whether the priKeyUsaPerNotAft field is set.
     */
    @Override
    public boolean hasPriKeyUsaPerNotAft() {
      return ((bitField1_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
     * @return The priKeyUsaPerNotAft.
     */
    @Override
    public long getPriKeyUsaPerNotAft() {
      return priKeyUsaPerNotAft_;
    }

    public static final int CERTPOL_FIELD_NUMBER = 38;
    private com.google.protobuf.ByteString certPol_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certPol = 38;</code>
     * @return Whether the certPol field is set.
     */
    @Override
    public boolean hasCertPol() {
      return ((bitField1_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes certPol = 38;</code>
     * @return The certPol.
     */
    @Override
    public com.google.protobuf.ByteString getCertPol() {
      return certPol_;
    }

    public static final int SUBALTDNS_FIELD_NUMBER = 39;
    private com.google.protobuf.ByteString subAltDNS_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subAltDNS = 39;</code>
     * @return Whether the subAltDNS field is set.
     */
    @Override
    public boolean hasSubAltDNS() {
      return ((bitField1_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes subAltDNS = 39;</code>
     * @return The subAltDNS.
     */
    @Override
    public com.google.protobuf.ByteString getSubAltDNS() {
      return subAltDNS_;
    }

    public static final int SUBALTIP_FIELD_NUMBER = 40;
    private com.google.protobuf.ByteString subAltIP_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subAltIP = 40;</code>
     * @return Whether the subAltIP field is set.
     */
    @Override
    public boolean hasSubAltIP() {
      return ((bitField1_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes subAltIP = 40;</code>
     * @return The subAltIP.
     */
    @Override
    public com.google.protobuf.ByteString getSubAltIP() {
      return subAltIP_;
    }

    public static final int SUBALTNAME_FIELD_NUMBER = 41;
    private com.google.protobuf.ByteString subAltName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subAltName = 41;</code>
     * @return Whether the subAltName field is set.
     */
    @Override
    public boolean hasSubAltName() {
      return ((bitField1_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes subAltName = 41;</code>
     * @return The subAltName.
     */
    @Override
    public com.google.protobuf.ByteString getSubAltName() {
      return subAltName_;
    }

    public static final int ISSALTNAMESYS_FIELD_NUMBER = 42;
    private com.google.protobuf.ByteString issAltNameSys_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     * @return Whether the issAltNameSys field is set.
     */
    @Override
    public boolean hasIssAltNameSys() {
      return ((bitField1_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes issAltNameSys = 42;</code>
     * @return The issAltNameSys.
     */
    @Override
    public com.google.protobuf.ByteString getIssAltNameSys() {
      return issAltNameSys_;
    }

    public static final int ISSALTIP_FIELD_NUMBER = 43;
    private com.google.protobuf.ByteString issAltIP_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issAltIP = 43;</code>
     * @return Whether the issAltIP field is set.
     */
    @Override
    public boolean hasIssAltIP() {
      return ((bitField1_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes issAltIP = 43;</code>
     * @return The issAltIP.
     */
    @Override
    public com.google.protobuf.ByteString getIssAltIP() {
      return issAltIP_;
    }

    public static final int ISSALTNAME_FIELD_NUMBER = 44;
    private com.google.protobuf.ByteString issAltName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issAltName = 44;</code>
     * @return Whether the issAltName field is set.
     */
    @Override
    public boolean hasIssAltName() {
      return ((bitField1_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes issAltName = 44;</code>
     * @return The issAltName.
     */
    @Override
    public com.google.protobuf.ByteString getIssAltName() {
      return issAltName_;
    }

    public static final int SUBDIRATT_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString subDirAtt_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subDirAtt = 45;</code>
     * @return Whether the subDirAtt field is set.
     */
    @Override
    public boolean hasSubDirAtt() {
      return ((bitField1_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes subDirAtt = 45;</code>
     * @return The subDirAtt.
     */
    @Override
    public com.google.protobuf.ByteString getSubDirAtt() {
      return subDirAtt_;
    }

    public static final int EXTKEYUSAGE_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString extKeyUsage_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     * @return Whether the extKeyUsage field is set.
     */
    @Override
    public boolean hasExtKeyUsage() {
      return ((bitField1_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes extKeyUsage = 46;</code>
     * @return The extKeyUsage.
     */
    @Override
    public com.google.protobuf.ByteString getExtKeyUsage() {
      return extKeyUsage_;
    }

    public static final int CERTREVLISTSRC_FIELD_NUMBER = 47;
    private com.google.protobuf.ByteString certRevListSrc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     * @return Whether the certRevListSrc field is set.
     */
    @Override
    public boolean hasCertRevListSrc() {
      return ((bitField1_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes certRevListSrc = 47;</code>
     * @return The certRevListSrc.
     */
    @Override
    public com.google.protobuf.ByteString getCertRevListSrc() {
      return certRevListSrc_;
    }

    public static final int CERTAUTHINFACCMET_FIELD_NUMBER = 48;
    private com.google.protobuf.ByteString certAuthInfAccMet_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     * @return Whether the certAuthInfAccMet field is set.
     */
    @Override
    public boolean hasCertAuthInfAccMet() {
      return ((bitField1_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes certAuthInfAccMet = 48;</code>
     * @return The certAuthInfAccMet.
     */
    @Override
    public com.google.protobuf.ByteString getCertAuthInfAccMet() {
      return certAuthInfAccMet_;
    }

    public static final int CERTAUTHINFACCLOC_FIELD_NUMBER = 49;
    private com.google.protobuf.ByteString certAuthInfAccLoc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     * @return Whether the certAuthInfAccLoc field is set.
     */
    @Override
    public boolean hasCertAuthInfAccLoc() {
      return ((bitField1_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes certAuthInfAccLoc = 49;</code>
     * @return The certAuthInfAccLoc.
     */
    @Override
    public com.google.protobuf.ByteString getCertAuthInfAccLoc() {
      return certAuthInfAccLoc_;
    }

    public static final int EXTCNT_FIELD_NUMBER = 50;
    private int extCnt_ = 0;
    /**
     * <code>optional uint32 extCnt = 50;</code>
     * @return Whether the extCnt field is set.
     */
    @Override
    public boolean hasExtCnt() {
      return ((bitField1_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 extCnt = 50;</code>
     * @return The extCnt.
     */
    @Override
    public int getExtCnt() {
      return extCnt_;
    }

    public static final int PROTABNAME_FIELD_NUMBER = 51;
    private com.google.protobuf.ByteString protabname_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes Protabname = 51;</code>
     * @return Whether the protabname field is set.
     */
    @Override
    public boolean hasProtabname() {
      return ((bitField1_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes Protabname = 51;</code>
     * @return The protabname.
     */
    @Override
    public com.google.protobuf.ByteString getProtabname() {
      return protabname_;
    }

    public static final int ISSUER_FIELD_NUMBER = 52;
    private com.google.protobuf.ByteString issuer_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes issuer = 52;</code>
     * @return Whether the issuer field is set.
     */
    @Override
    public boolean hasIssuer() {
      return ((bitField1_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes issuer = 52;</code>
     * @return The issuer.
     */
    @Override
    public com.google.protobuf.ByteString getIssuer() {
      return issuer_;
    }

    public static final int SUBJECT_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString subject_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes subject = 53;</code>
     * @return Whether the subject field is set.
     */
    @Override
    public boolean hasSubject() {
      return ((bitField1_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes subject = 53;</code>
     * @return The subject.
     */
    @Override
    public com.google.protobuf.ByteString getSubject() {
      return subject_;
    }

    public static final int DAYSREM_FIELD_NUMBER = 54;
    private int daysRem_ = 0;
    /**
     * <code>optional uint32 daysRem = 54;</code>
     * @return Whether the daysRem field is set.
     */
    @Override
    public boolean hasDaysRem() {
      return ((bitField1_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 daysRem = 54;</code>
     * @return The daysRem.
     */
    @Override
    public int getDaysRem() {
      return daysRem_;
    }

    public static final int PUBKEY_FIELD_NUMBER = 55;
    private com.google.protobuf.ByteString pubkey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes pubkey = 55;</code>
     * @return Whether the pubkey field is set.
     */
    @Override
    public boolean hasPubkey() {
      return ((bitField1_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes pubkey = 55;</code>
     * @return The pubkey.
     */
    @Override
    public com.google.protobuf.ByteString getPubkey() {
      return pubkey_;
    }

    public static final int FPALG_FIELD_NUMBER = 56;
    private com.google.protobuf.ByteString fpAlg_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes fpAlg = 56;</code>
     * @return Whether the fpAlg field is set.
     */
    @Override
    public boolean hasFpAlg() {
      return ((bitField1_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes fpAlg = 56;</code>
     * @return The fpAlg.
     */
    @Override
    public com.google.protobuf.ByteString getFpAlg() {
      return fpAlg_;
    }

    public static final int HASH_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString hash_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes hash = 57;</code>
     * @return Whether the hash field is set.
     */
    @Override
    public boolean hasHash() {
      return ((bitField1_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes hash = 57;</code>
     * @return The hash.
     */
    @Override
    public com.google.protobuf.ByteString getHash() {
      return hash_;
    }

    public static final int EXTSET_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString extSet_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes extSet = 58;</code>
     * @return Whether the extSet field is set.
     */
    @Override
    public boolean hasExtSet() {
      return ((bitField1_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes extSet = 58;</code>
     * @return The extSet.
     */
    @Override
    public com.google.protobuf.ByteString getExtSet() {
      return extSet_;
    }

    public static final int DAYSTOTAL_FIELD_NUMBER = 59;
    private int daysTotal_ = 0;
    /**
     * <code>optional uint32 daysTotal = 59;</code>
     * @return Whether the daysTotal field is set.
     */
    @Override
    public boolean hasDaysTotal() {
      return ((bitField1_ & 0x04000000) != 0);
    }
    /**
     * <code>optional uint32 daysTotal = 59;</code>
     * @return The daysTotal.
     */
    @Override
    public int getDaysTotal() {
      return daysTotal_;
    }

    public static final int SUBALTDNSCNT_FIELD_NUMBER = 60;
    private int subAltDNSCnt_ = 0;
    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     * @return Whether the subAltDNSCnt field is set.
     */
    @Override
    public boolean hasSubAltDNSCnt() {
      return ((bitField1_ & 0x08000000) != 0);
    }
    /**
     * <code>optional uint32 subAltDNSCnt = 60;</code>
     * @return The subAltDNSCnt.
     */
    @Override
    public int getSubAltDNSCnt() {
      return subAltDNSCnt_;
    }

    public static final int AUTHINFO_FIELD_NUMBER = 61;
    private com.google.protobuf.ByteString authinfo_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes authinfo = 61;</code>
     * @return Whether the authinfo field is set.
     */
    @Override
    public boolean hasAuthinfo() {
      return ((bitField1_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes authinfo = 61;</code>
     * @return The authinfo.
     */
    @Override
    public com.google.protobuf.ByteString getAuthinfo() {
      return authinfo_;
    }

    public static final int BASICCONSCA_FIELD_NUMBER = 62;
    private com.google.protobuf.ByteString basicConsCA_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes basicConsCA = 62;</code>
     * @return Whether the basicConsCA field is set.
     */
    @Override
    public boolean hasBasicConsCA() {
      return ((bitField1_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes basicConsCA = 62;</code>
     * @return The basicConsCA.
     */
    @Override
    public com.google.protobuf.ByteString getBasicConsCA() {
      return basicConsCA_;
    }

    public static final int BASICCONSPATHLEN_FIELD_NUMBER = 63;
    private com.google.protobuf.ByteString basicConsPathLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     * @return Whether the basicConsPathLen field is set.
     */
    @Override
    public boolean hasBasicConsPathLen() {
      return ((bitField1_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes basicConsPathLen = 63;</code>
     * @return The basicConsPathLen.
     */
    @Override
    public com.google.protobuf.ByteString getBasicConsPathLen() {
      return basicConsPathLen_;
    }

    public static final int BASICCONS_FIELD_NUMBER = 64;
    private com.google.protobuf.ByteString basicCons_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes basicCons = 64;</code>
     * @return Whether the basicCons field is set.
     */
    @Override
    public boolean hasBasicCons() {
      return ((bitField1_ & 0x80000000) != 0);
    }
    /**
     * <code>optional bytes basicCons = 64;</code>
     * @return The basicCons.
     */
    @Override
    public com.google.protobuf.ByteString getBasicCons() {
      return basicCons_;
    }

    public static final int KEYPUR_FIELD_NUMBER = 65;
    private com.google.protobuf.ByteString keyPur_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes KeyPur = 65;</code>
     * @return Whether the keyPur field is set.
     */
    @Override
    public boolean hasKeyPur() {
      return ((bitField2_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes KeyPur = 65;</code>
     * @return The keyPur.
     */
    @Override
    public com.google.protobuf.ByteString getKeyPur() {
      return keyPur_;
    }

    public static final int CERTYPE_FIELD_NUMBER = 66;
    private int certype_ = 0;
    /**
     * <code>optional uint32 Certype = 66;</code>
     * @return Whether the certype field is set.
     */
    @Override
    public boolean hasCertype() {
      return ((bitField2_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 Certype = 66;</code>
     * @return The certype.
     */
    @Override
    public int getCertype() {
      return certype_;
    }

    public static final int CERTFULLTEXT_FIELD_NUMBER = 67;
    private com.google.protobuf.ByteString certFullText_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certFullText = 67;</code>
     * @return Whether the certFullText field is set.
     */
    @Override
    public boolean hasCertFullText() {
      return ((bitField2_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes certFullText = 67;</code>
     * @return The certFullText.
     */
    @Override
    public com.google.protobuf.ByteString getCertFullText() {
      return certFullText_;
    }

    public static final int ALTERNATIVEIPCOUNT_FIELD_NUMBER = 68;
    private int alternativeIpCount_ = 0;
    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     * @return Whether the alternativeIpCount field is set.
     */
    @Override
    public boolean hasAlternativeIpCount() {
      return ((bitField2_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 alternativeIpCount = 68;</code>
     * @return The alternativeIpCount.
     */
    @Override
    public int getAlternativeIpCount() {
      return alternativeIpCount_;
    }

    public static final int SOURCE_FIELD_NUMBER = 69;
    private int source_ = 0;
    /**
     * <code>optional uint32 source = 69;</code>
     * @return Whether the source field is set.
     */
    @Override
    public boolean hasSource() {
      return ((bitField2_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 source = 69;</code>
     * @return The source.
     */
    @Override
    public int getSource() {
      return source_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt64(1, protabID_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, ver_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeBytes(3, srvNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(4, issDataLen_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBytes(5, issComName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(6, issConName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeBytes(7, issLoaName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeBytes(8, issStaOrProName_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeBytes(9, issStrAddr_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeBytes(10, issOrgName_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeBytes(11, issOrgUniName_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeBytes(12, issPosOffBox_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeBytes(13, subComName_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeBytes(14, subConName_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeBytes(15, subLoaName_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeBytes(16, subStaOrProName_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(17, subStrAddr_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeBytes(18, subOrgName_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeBytes(19, subOrgUniName_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeBytes(20, subPosOffBox_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeUInt64(21, valNotBef_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeUInt64(22, valNotAft_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeBytes(23, rSAMod_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeBytes(24, rSAExp_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeBytes(25, dHPriMod_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeBytes(26, dHPGen_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeBytes(27, dHPubKey_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeBytes(28, dSAPubKeyP_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeBytes(29, dSAPubKeyQ_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeBytes(30, dSAPubKeyG_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeBytes(31, sigAlg_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeBytes(32, sigVal_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeBytes(33, authKeyID_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeBytes(34, subKeyID_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        output.writeBytes(35, keyUsage_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        output.writeUInt64(36, priKeyUsaPerNotBef_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        output.writeUInt64(37, priKeyUsaPerNotAft_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        output.writeBytes(38, certPol_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        output.writeBytes(39, subAltDNS_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        output.writeBytes(40, subAltIP_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        output.writeBytes(41, subAltName_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        output.writeBytes(42, issAltNameSys_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        output.writeBytes(43, issAltIP_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        output.writeBytes(44, issAltName_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        output.writeBytes(45, subDirAtt_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        output.writeBytes(46, extKeyUsage_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        output.writeBytes(47, certRevListSrc_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        output.writeBytes(48, certAuthInfAccMet_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        output.writeBytes(49, certAuthInfAccLoc_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        output.writeUInt32(50, extCnt_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        output.writeBytes(51, protabname_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        output.writeBytes(52, issuer_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        output.writeBytes(53, subject_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        output.writeUInt32(54, daysRem_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        output.writeBytes(55, pubkey_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        output.writeBytes(56, fpAlg_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        output.writeBytes(57, hash_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        output.writeBytes(58, extSet_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        output.writeUInt32(59, daysTotal_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        output.writeUInt32(60, subAltDNSCnt_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        output.writeBytes(61, authinfo_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        output.writeBytes(62, basicConsCA_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        output.writeBytes(63, basicConsPathLen_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        output.writeBytes(64, basicCons_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        output.writeBytes(65, keyPur_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        output.writeUInt32(66, certype_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        output.writeBytes(67, certFullText_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        output.writeUInt32(68, alternativeIpCount_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        output.writeUInt32(69, source_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, protabID_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, ver_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, srvNum_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, issDataLen_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, issComName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, issConName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, issLoaName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, issStaOrProName_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, issStrAddr_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, issOrgName_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, issOrgUniName_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, issPosOffBox_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(13, subComName_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(14, subConName_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, subLoaName_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, subStaOrProName_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, subStrAddr_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(18, subOrgName_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(19, subOrgUniName_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(20, subPosOffBox_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(21, valNotBef_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(22, valNotAft_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, rSAMod_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, rSAExp_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, dHPriMod_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, dHPGen_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, dHPubKey_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(28, dSAPubKeyP_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, dSAPubKeyQ_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(30, dSAPubKeyG_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, sigAlg_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(32, sigVal_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(33, authKeyID_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(34, subKeyID_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(35, keyUsage_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(36, priKeyUsaPerNotBef_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(37, priKeyUsaPerNotAft_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(38, certPol_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(39, subAltDNS_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(40, subAltIP_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(41, subAltName_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(42, issAltNameSys_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(43, issAltIP_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(44, issAltName_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, subDirAtt_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, extKeyUsage_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(47, certRevListSrc_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(48, certAuthInfAccMet_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(49, certAuthInfAccLoc_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(50, extCnt_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(51, protabname_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(52, issuer_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, subject_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(54, daysRem_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(55, pubkey_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(56, fpAlg_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, hash_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, extSet_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(59, daysTotal_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(60, subAltDNSCnt_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(61, authinfo_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(62, basicConsCA_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(63, basicConsPathLen_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(64, basicCons_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(65, keyPur_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(66, certype_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(67, certFullText_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(68, alternativeIpCount_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(69, source_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof X509CerInfo)) {
        return super.equals(obj);
      }
      X509CerInfo other = (X509CerInfo) obj;

      if (hasProtabID() != other.hasProtabID()) return false;
      if (hasProtabID()) {
        if (getProtabID()
            != other.getProtabID()) return false;
      }
      if (hasVer() != other.hasVer()) return false;
      if (hasVer()) {
        if (getVer()
            != other.getVer()) return false;
      }
      if (hasSrvNum() != other.hasSrvNum()) return false;
      if (hasSrvNum()) {
        if (!getSrvNum()
            .equals(other.getSrvNum())) return false;
      }
      if (hasIssDataLen() != other.hasIssDataLen()) return false;
      if (hasIssDataLen()) {
        if (getIssDataLen()
            != other.getIssDataLen()) return false;
      }
      if (hasIssComName() != other.hasIssComName()) return false;
      if (hasIssComName()) {
        if (!getIssComName()
            .equals(other.getIssComName())) return false;
      }
      if (hasIssConName() != other.hasIssConName()) return false;
      if (hasIssConName()) {
        if (!getIssConName()
            .equals(other.getIssConName())) return false;
      }
      if (hasIssLoaName() != other.hasIssLoaName()) return false;
      if (hasIssLoaName()) {
        if (!getIssLoaName()
            .equals(other.getIssLoaName())) return false;
      }
      if (hasIssStaOrProName() != other.hasIssStaOrProName()) return false;
      if (hasIssStaOrProName()) {
        if (!getIssStaOrProName()
            .equals(other.getIssStaOrProName())) return false;
      }
      if (hasIssStrAddr() != other.hasIssStrAddr()) return false;
      if (hasIssStrAddr()) {
        if (!getIssStrAddr()
            .equals(other.getIssStrAddr())) return false;
      }
      if (hasIssOrgName() != other.hasIssOrgName()) return false;
      if (hasIssOrgName()) {
        if (!getIssOrgName()
            .equals(other.getIssOrgName())) return false;
      }
      if (hasIssOrgUniName() != other.hasIssOrgUniName()) return false;
      if (hasIssOrgUniName()) {
        if (!getIssOrgUniName()
            .equals(other.getIssOrgUniName())) return false;
      }
      if (hasIssPosOffBox() != other.hasIssPosOffBox()) return false;
      if (hasIssPosOffBox()) {
        if (!getIssPosOffBox()
            .equals(other.getIssPosOffBox())) return false;
      }
      if (hasSubComName() != other.hasSubComName()) return false;
      if (hasSubComName()) {
        if (!getSubComName()
            .equals(other.getSubComName())) return false;
      }
      if (hasSubConName() != other.hasSubConName()) return false;
      if (hasSubConName()) {
        if (!getSubConName()
            .equals(other.getSubConName())) return false;
      }
      if (hasSubLoaName() != other.hasSubLoaName()) return false;
      if (hasSubLoaName()) {
        if (!getSubLoaName()
            .equals(other.getSubLoaName())) return false;
      }
      if (hasSubStaOrProName() != other.hasSubStaOrProName()) return false;
      if (hasSubStaOrProName()) {
        if (!getSubStaOrProName()
            .equals(other.getSubStaOrProName())) return false;
      }
      if (hasSubStrAddr() != other.hasSubStrAddr()) return false;
      if (hasSubStrAddr()) {
        if (!getSubStrAddr()
            .equals(other.getSubStrAddr())) return false;
      }
      if (hasSubOrgName() != other.hasSubOrgName()) return false;
      if (hasSubOrgName()) {
        if (!getSubOrgName()
            .equals(other.getSubOrgName())) return false;
      }
      if (hasSubOrgUniName() != other.hasSubOrgUniName()) return false;
      if (hasSubOrgUniName()) {
        if (!getSubOrgUniName()
            .equals(other.getSubOrgUniName())) return false;
      }
      if (hasSubPosOffBox() != other.hasSubPosOffBox()) return false;
      if (hasSubPosOffBox()) {
        if (!getSubPosOffBox()
            .equals(other.getSubPosOffBox())) return false;
      }
      if (hasValNotBef() != other.hasValNotBef()) return false;
      if (hasValNotBef()) {
        if (getValNotBef()
            != other.getValNotBef()) return false;
      }
      if (hasValNotAft() != other.hasValNotAft()) return false;
      if (hasValNotAft()) {
        if (getValNotAft()
            != other.getValNotAft()) return false;
      }
      if (hasRSAMod() != other.hasRSAMod()) return false;
      if (hasRSAMod()) {
        if (!getRSAMod()
            .equals(other.getRSAMod())) return false;
      }
      if (hasRSAExp() != other.hasRSAExp()) return false;
      if (hasRSAExp()) {
        if (!getRSAExp()
            .equals(other.getRSAExp())) return false;
      }
      if (hasDHPriMod() != other.hasDHPriMod()) return false;
      if (hasDHPriMod()) {
        if (!getDHPriMod()
            .equals(other.getDHPriMod())) return false;
      }
      if (hasDHPGen() != other.hasDHPGen()) return false;
      if (hasDHPGen()) {
        if (!getDHPGen()
            .equals(other.getDHPGen())) return false;
      }
      if (hasDHPubKey() != other.hasDHPubKey()) return false;
      if (hasDHPubKey()) {
        if (!getDHPubKey()
            .equals(other.getDHPubKey())) return false;
      }
      if (hasDSAPubKeyP() != other.hasDSAPubKeyP()) return false;
      if (hasDSAPubKeyP()) {
        if (!getDSAPubKeyP()
            .equals(other.getDSAPubKeyP())) return false;
      }
      if (hasDSAPubKeyQ() != other.hasDSAPubKeyQ()) return false;
      if (hasDSAPubKeyQ()) {
        if (!getDSAPubKeyQ()
            .equals(other.getDSAPubKeyQ())) return false;
      }
      if (hasDSAPubKeyG() != other.hasDSAPubKeyG()) return false;
      if (hasDSAPubKeyG()) {
        if (!getDSAPubKeyG()
            .equals(other.getDSAPubKeyG())) return false;
      }
      if (hasSigAlg() != other.hasSigAlg()) return false;
      if (hasSigAlg()) {
        if (!getSigAlg()
            .equals(other.getSigAlg())) return false;
      }
      if (hasSigVal() != other.hasSigVal()) return false;
      if (hasSigVal()) {
        if (!getSigVal()
            .equals(other.getSigVal())) return false;
      }
      if (hasAuthKeyID() != other.hasAuthKeyID()) return false;
      if (hasAuthKeyID()) {
        if (!getAuthKeyID()
            .equals(other.getAuthKeyID())) return false;
      }
      if (hasSubKeyID() != other.hasSubKeyID()) return false;
      if (hasSubKeyID()) {
        if (!getSubKeyID()
            .equals(other.getSubKeyID())) return false;
      }
      if (hasKeyUsage() != other.hasKeyUsage()) return false;
      if (hasKeyUsage()) {
        if (!getKeyUsage()
            .equals(other.getKeyUsage())) return false;
      }
      if (hasPriKeyUsaPerNotBef() != other.hasPriKeyUsaPerNotBef()) return false;
      if (hasPriKeyUsaPerNotBef()) {
        if (getPriKeyUsaPerNotBef()
            != other.getPriKeyUsaPerNotBef()) return false;
      }
      if (hasPriKeyUsaPerNotAft() != other.hasPriKeyUsaPerNotAft()) return false;
      if (hasPriKeyUsaPerNotAft()) {
        if (getPriKeyUsaPerNotAft()
            != other.getPriKeyUsaPerNotAft()) return false;
      }
      if (hasCertPol() != other.hasCertPol()) return false;
      if (hasCertPol()) {
        if (!getCertPol()
            .equals(other.getCertPol())) return false;
      }
      if (hasSubAltDNS() != other.hasSubAltDNS()) return false;
      if (hasSubAltDNS()) {
        if (!getSubAltDNS()
            .equals(other.getSubAltDNS())) return false;
      }
      if (hasSubAltIP() != other.hasSubAltIP()) return false;
      if (hasSubAltIP()) {
        if (!getSubAltIP()
            .equals(other.getSubAltIP())) return false;
      }
      if (hasSubAltName() != other.hasSubAltName()) return false;
      if (hasSubAltName()) {
        if (!getSubAltName()
            .equals(other.getSubAltName())) return false;
      }
      if (hasIssAltNameSys() != other.hasIssAltNameSys()) return false;
      if (hasIssAltNameSys()) {
        if (!getIssAltNameSys()
            .equals(other.getIssAltNameSys())) return false;
      }
      if (hasIssAltIP() != other.hasIssAltIP()) return false;
      if (hasIssAltIP()) {
        if (!getIssAltIP()
            .equals(other.getIssAltIP())) return false;
      }
      if (hasIssAltName() != other.hasIssAltName()) return false;
      if (hasIssAltName()) {
        if (!getIssAltName()
            .equals(other.getIssAltName())) return false;
      }
      if (hasSubDirAtt() != other.hasSubDirAtt()) return false;
      if (hasSubDirAtt()) {
        if (!getSubDirAtt()
            .equals(other.getSubDirAtt())) return false;
      }
      if (hasExtKeyUsage() != other.hasExtKeyUsage()) return false;
      if (hasExtKeyUsage()) {
        if (!getExtKeyUsage()
            .equals(other.getExtKeyUsage())) return false;
      }
      if (hasCertRevListSrc() != other.hasCertRevListSrc()) return false;
      if (hasCertRevListSrc()) {
        if (!getCertRevListSrc()
            .equals(other.getCertRevListSrc())) return false;
      }
      if (hasCertAuthInfAccMet() != other.hasCertAuthInfAccMet()) return false;
      if (hasCertAuthInfAccMet()) {
        if (!getCertAuthInfAccMet()
            .equals(other.getCertAuthInfAccMet())) return false;
      }
      if (hasCertAuthInfAccLoc() != other.hasCertAuthInfAccLoc()) return false;
      if (hasCertAuthInfAccLoc()) {
        if (!getCertAuthInfAccLoc()
            .equals(other.getCertAuthInfAccLoc())) return false;
      }
      if (hasExtCnt() != other.hasExtCnt()) return false;
      if (hasExtCnt()) {
        if (getExtCnt()
            != other.getExtCnt()) return false;
      }
      if (hasProtabname() != other.hasProtabname()) return false;
      if (hasProtabname()) {
        if (!getProtabname()
            .equals(other.getProtabname())) return false;
      }
      if (hasIssuer() != other.hasIssuer()) return false;
      if (hasIssuer()) {
        if (!getIssuer()
            .equals(other.getIssuer())) return false;
      }
      if (hasSubject() != other.hasSubject()) return false;
      if (hasSubject()) {
        if (!getSubject()
            .equals(other.getSubject())) return false;
      }
      if (hasDaysRem() != other.hasDaysRem()) return false;
      if (hasDaysRem()) {
        if (getDaysRem()
            != other.getDaysRem()) return false;
      }
      if (hasPubkey() != other.hasPubkey()) return false;
      if (hasPubkey()) {
        if (!getPubkey()
            .equals(other.getPubkey())) return false;
      }
      if (hasFpAlg() != other.hasFpAlg()) return false;
      if (hasFpAlg()) {
        if (!getFpAlg()
            .equals(other.getFpAlg())) return false;
      }
      if (hasHash() != other.hasHash()) return false;
      if (hasHash()) {
        if (!getHash()
            .equals(other.getHash())) return false;
      }
      if (hasExtSet() != other.hasExtSet()) return false;
      if (hasExtSet()) {
        if (!getExtSet()
            .equals(other.getExtSet())) return false;
      }
      if (hasDaysTotal() != other.hasDaysTotal()) return false;
      if (hasDaysTotal()) {
        if (getDaysTotal()
            != other.getDaysTotal()) return false;
      }
      if (hasSubAltDNSCnt() != other.hasSubAltDNSCnt()) return false;
      if (hasSubAltDNSCnt()) {
        if (getSubAltDNSCnt()
            != other.getSubAltDNSCnt()) return false;
      }
      if (hasAuthinfo() != other.hasAuthinfo()) return false;
      if (hasAuthinfo()) {
        if (!getAuthinfo()
            .equals(other.getAuthinfo())) return false;
      }
      if (hasBasicConsCA() != other.hasBasicConsCA()) return false;
      if (hasBasicConsCA()) {
        if (!getBasicConsCA()
            .equals(other.getBasicConsCA())) return false;
      }
      if (hasBasicConsPathLen() != other.hasBasicConsPathLen()) return false;
      if (hasBasicConsPathLen()) {
        if (!getBasicConsPathLen()
            .equals(other.getBasicConsPathLen())) return false;
      }
      if (hasBasicCons() != other.hasBasicCons()) return false;
      if (hasBasicCons()) {
        if (!getBasicCons()
            .equals(other.getBasicCons())) return false;
      }
      if (hasKeyPur() != other.hasKeyPur()) return false;
      if (hasKeyPur()) {
        if (!getKeyPur()
            .equals(other.getKeyPur())) return false;
      }
      if (hasCertype() != other.hasCertype()) return false;
      if (hasCertype()) {
        if (getCertype()
            != other.getCertype()) return false;
      }
      if (hasCertFullText() != other.hasCertFullText()) return false;
      if (hasCertFullText()) {
        if (!getCertFullText()
            .equals(other.getCertFullText())) return false;
      }
      if (hasAlternativeIpCount() != other.hasAlternativeIpCount()) return false;
      if (hasAlternativeIpCount()) {
        if (getAlternativeIpCount()
            != other.getAlternativeIpCount()) return false;
      }
      if (hasSource() != other.hasSource()) return false;
      if (hasSource()) {
        if (getSource()
            != other.getSource()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasProtabID()) {
        hash = (37 * hash) + PROTABID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getProtabID());
      }
      if (hasVer()) {
        hash = (37 * hash) + VER_FIELD_NUMBER;
        hash = (53 * hash) + getVer();
      }
      if (hasSrvNum()) {
        hash = (37 * hash) + SRVNUM_FIELD_NUMBER;
        hash = (53 * hash) + getSrvNum().hashCode();
      }
      if (hasIssDataLen()) {
        hash = (37 * hash) + ISSDATALEN_FIELD_NUMBER;
        hash = (53 * hash) + getIssDataLen();
      }
      if (hasIssComName()) {
        hash = (37 * hash) + ISSCOMNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssComName().hashCode();
      }
      if (hasIssConName()) {
        hash = (37 * hash) + ISSCONNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssConName().hashCode();
      }
      if (hasIssLoaName()) {
        hash = (37 * hash) + ISSLOANAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssLoaName().hashCode();
      }
      if (hasIssStaOrProName()) {
        hash = (37 * hash) + ISSSTAORPRONAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssStaOrProName().hashCode();
      }
      if (hasIssStrAddr()) {
        hash = (37 * hash) + ISSSTRADDR_FIELD_NUMBER;
        hash = (53 * hash) + getIssStrAddr().hashCode();
      }
      if (hasIssOrgName()) {
        hash = (37 * hash) + ISSORGNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssOrgName().hashCode();
      }
      if (hasIssOrgUniName()) {
        hash = (37 * hash) + ISSORGUNINAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssOrgUniName().hashCode();
      }
      if (hasIssPosOffBox()) {
        hash = (37 * hash) + ISSPOSOFFBOX_FIELD_NUMBER;
        hash = (53 * hash) + getIssPosOffBox().hashCode();
      }
      if (hasSubComName()) {
        hash = (37 * hash) + SUBCOMNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubComName().hashCode();
      }
      if (hasSubConName()) {
        hash = (37 * hash) + SUBCONNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubConName().hashCode();
      }
      if (hasSubLoaName()) {
        hash = (37 * hash) + SUBLOANAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubLoaName().hashCode();
      }
      if (hasSubStaOrProName()) {
        hash = (37 * hash) + SUBSTAORPRONAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubStaOrProName().hashCode();
      }
      if (hasSubStrAddr()) {
        hash = (37 * hash) + SUBSTRADDR_FIELD_NUMBER;
        hash = (53 * hash) + getSubStrAddr().hashCode();
      }
      if (hasSubOrgName()) {
        hash = (37 * hash) + SUBORGNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubOrgName().hashCode();
      }
      if (hasSubOrgUniName()) {
        hash = (37 * hash) + SUBORGUNINAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubOrgUniName().hashCode();
      }
      if (hasSubPosOffBox()) {
        hash = (37 * hash) + SUBPOSOFFBOX_FIELD_NUMBER;
        hash = (53 * hash) + getSubPosOffBox().hashCode();
      }
      if (hasValNotBef()) {
        hash = (37 * hash) + VALNOTBEF_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getValNotBef());
      }
      if (hasValNotAft()) {
        hash = (37 * hash) + VALNOTAFT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getValNotAft());
      }
      if (hasRSAMod()) {
        hash = (37 * hash) + RSAMOD_FIELD_NUMBER;
        hash = (53 * hash) + getRSAMod().hashCode();
      }
      if (hasRSAExp()) {
        hash = (37 * hash) + RSAEXP_FIELD_NUMBER;
        hash = (53 * hash) + getRSAExp().hashCode();
      }
      if (hasDHPriMod()) {
        hash = (37 * hash) + DHPRIMOD_FIELD_NUMBER;
        hash = (53 * hash) + getDHPriMod().hashCode();
      }
      if (hasDHPGen()) {
        hash = (37 * hash) + DHPGEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHPGen().hashCode();
      }
      if (hasDHPubKey()) {
        hash = (37 * hash) + DHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getDHPubKey().hashCode();
      }
      if (hasDSAPubKeyP()) {
        hash = (37 * hash) + DSAPUBKEYP_FIELD_NUMBER;
        hash = (53 * hash) + getDSAPubKeyP().hashCode();
      }
      if (hasDSAPubKeyQ()) {
        hash = (37 * hash) + DSAPUBKEYQ_FIELD_NUMBER;
        hash = (53 * hash) + getDSAPubKeyQ().hashCode();
      }
      if (hasDSAPubKeyG()) {
        hash = (37 * hash) + DSAPUBKEYG_FIELD_NUMBER;
        hash = (53 * hash) + getDSAPubKeyG().hashCode();
      }
      if (hasSigAlg()) {
        hash = (37 * hash) + SIGALG_FIELD_NUMBER;
        hash = (53 * hash) + getSigAlg().hashCode();
      }
      if (hasSigVal()) {
        hash = (37 * hash) + SIGVAL_FIELD_NUMBER;
        hash = (53 * hash) + getSigVal().hashCode();
      }
      if (hasAuthKeyID()) {
        hash = (37 * hash) + AUTHKEYID_FIELD_NUMBER;
        hash = (53 * hash) + getAuthKeyID().hashCode();
      }
      if (hasSubKeyID()) {
        hash = (37 * hash) + SUBKEYID_FIELD_NUMBER;
        hash = (53 * hash) + getSubKeyID().hashCode();
      }
      if (hasKeyUsage()) {
        hash = (37 * hash) + KEYUSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getKeyUsage().hashCode();
      }
      if (hasPriKeyUsaPerNotBef()) {
        hash = (37 * hash) + PRIKEYUSAPERNOTBEF_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPriKeyUsaPerNotBef());
      }
      if (hasPriKeyUsaPerNotAft()) {
        hash = (37 * hash) + PRIKEYUSAPERNOTAFT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPriKeyUsaPerNotAft());
      }
      if (hasCertPol()) {
        hash = (37 * hash) + CERTPOL_FIELD_NUMBER;
        hash = (53 * hash) + getCertPol().hashCode();
      }
      if (hasSubAltDNS()) {
        hash = (37 * hash) + SUBALTDNS_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltDNS().hashCode();
      }
      if (hasSubAltIP()) {
        hash = (37 * hash) + SUBALTIP_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltIP().hashCode();
      }
      if (hasSubAltName()) {
        hash = (37 * hash) + SUBALTNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltName().hashCode();
      }
      if (hasIssAltNameSys()) {
        hash = (37 * hash) + ISSALTNAMESYS_FIELD_NUMBER;
        hash = (53 * hash) + getIssAltNameSys().hashCode();
      }
      if (hasIssAltIP()) {
        hash = (37 * hash) + ISSALTIP_FIELD_NUMBER;
        hash = (53 * hash) + getIssAltIP().hashCode();
      }
      if (hasIssAltName()) {
        hash = (37 * hash) + ISSALTNAME_FIELD_NUMBER;
        hash = (53 * hash) + getIssAltName().hashCode();
      }
      if (hasSubDirAtt()) {
        hash = (37 * hash) + SUBDIRATT_FIELD_NUMBER;
        hash = (53 * hash) + getSubDirAtt().hashCode();
      }
      if (hasExtKeyUsage()) {
        hash = (37 * hash) + EXTKEYUSAGE_FIELD_NUMBER;
        hash = (53 * hash) + getExtKeyUsage().hashCode();
      }
      if (hasCertRevListSrc()) {
        hash = (37 * hash) + CERTREVLISTSRC_FIELD_NUMBER;
        hash = (53 * hash) + getCertRevListSrc().hashCode();
      }
      if (hasCertAuthInfAccMet()) {
        hash = (37 * hash) + CERTAUTHINFACCMET_FIELD_NUMBER;
        hash = (53 * hash) + getCertAuthInfAccMet().hashCode();
      }
      if (hasCertAuthInfAccLoc()) {
        hash = (37 * hash) + CERTAUTHINFACCLOC_FIELD_NUMBER;
        hash = (53 * hash) + getCertAuthInfAccLoc().hashCode();
      }
      if (hasExtCnt()) {
        hash = (37 * hash) + EXTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getExtCnt();
      }
      if (hasProtabname()) {
        hash = (37 * hash) + PROTABNAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtabname().hashCode();
      }
      if (hasIssuer()) {
        hash = (37 * hash) + ISSUER_FIELD_NUMBER;
        hash = (53 * hash) + getIssuer().hashCode();
      }
      if (hasSubject()) {
        hash = (37 * hash) + SUBJECT_FIELD_NUMBER;
        hash = (53 * hash) + getSubject().hashCode();
      }
      if (hasDaysRem()) {
        hash = (37 * hash) + DAYSREM_FIELD_NUMBER;
        hash = (53 * hash) + getDaysRem();
      }
      if (hasPubkey()) {
        hash = (37 * hash) + PUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getPubkey().hashCode();
      }
      if (hasFpAlg()) {
        hash = (37 * hash) + FPALG_FIELD_NUMBER;
        hash = (53 * hash) + getFpAlg().hashCode();
      }
      if (hasHash()) {
        hash = (37 * hash) + HASH_FIELD_NUMBER;
        hash = (53 * hash) + getHash().hashCode();
      }
      if (hasExtSet()) {
        hash = (37 * hash) + EXTSET_FIELD_NUMBER;
        hash = (53 * hash) + getExtSet().hashCode();
      }
      if (hasDaysTotal()) {
        hash = (37 * hash) + DAYSTOTAL_FIELD_NUMBER;
        hash = (53 * hash) + getDaysTotal();
      }
      if (hasSubAltDNSCnt()) {
        hash = (37 * hash) + SUBALTDNSCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSubAltDNSCnt();
      }
      if (hasAuthinfo()) {
        hash = (37 * hash) + AUTHINFO_FIELD_NUMBER;
        hash = (53 * hash) + getAuthinfo().hashCode();
      }
      if (hasBasicConsCA()) {
        hash = (37 * hash) + BASICCONSCA_FIELD_NUMBER;
        hash = (53 * hash) + getBasicConsCA().hashCode();
      }
      if (hasBasicConsPathLen()) {
        hash = (37 * hash) + BASICCONSPATHLEN_FIELD_NUMBER;
        hash = (53 * hash) + getBasicConsPathLen().hashCode();
      }
      if (hasBasicCons()) {
        hash = (37 * hash) + BASICCONS_FIELD_NUMBER;
        hash = (53 * hash) + getBasicCons().hashCode();
      }
      if (hasKeyPur()) {
        hash = (37 * hash) + KEYPUR_FIELD_NUMBER;
        hash = (53 * hash) + getKeyPur().hashCode();
      }
      if (hasCertype()) {
        hash = (37 * hash) + CERTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCertype();
      }
      if (hasCertFullText()) {
        hash = (37 * hash) + CERTFULLTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getCertFullText().hashCode();
      }
      if (hasAlternativeIpCount()) {
        hash = (37 * hash) + ALTERNATIVEIPCOUNT_FIELD_NUMBER;
        hash = (53 * hash) + getAlternativeIpCount();
      }
      if (hasSource()) {
        hash = (37 * hash) + SOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getSource();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static X509CerInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static X509CerInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static X509CerInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static X509CerInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static X509CerInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static X509CerInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static X509CerInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static X509CerInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static X509CerInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(X509CerInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code X509CerInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:X509CerInfo)
        X509CerInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return X509CerInfoOuterClass.internal_static_X509CerInfo_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return X509CerInfoOuterClass.internal_static_X509CerInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                X509CerInfo.class, Builder.class);
      }

      // Construct using X509CerInfoOuterClass.X509CerInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        bitField2_ = 0;
        protabID_ = 0L;
        ver_ = 0;
        srvNum_ = com.google.protobuf.ByteString.EMPTY;
        issDataLen_ = 0;
        issComName_ = com.google.protobuf.ByteString.EMPTY;
        issConName_ = com.google.protobuf.ByteString.EMPTY;
        issLoaName_ = com.google.protobuf.ByteString.EMPTY;
        issStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
        issStrAddr_ = com.google.protobuf.ByteString.EMPTY;
        issOrgName_ = com.google.protobuf.ByteString.EMPTY;
        issOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
        issPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
        subComName_ = com.google.protobuf.ByteString.EMPTY;
        subConName_ = com.google.protobuf.ByteString.EMPTY;
        subLoaName_ = com.google.protobuf.ByteString.EMPTY;
        subStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
        subStrAddr_ = com.google.protobuf.ByteString.EMPTY;
        subOrgName_ = com.google.protobuf.ByteString.EMPTY;
        subOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
        subPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
        valNotBef_ = 0L;
        valNotAft_ = 0L;
        rSAMod_ = com.google.protobuf.ByteString.EMPTY;
        rSAExp_ = com.google.protobuf.ByteString.EMPTY;
        dHPriMod_ = com.google.protobuf.ByteString.EMPTY;
        dHPGen_ = com.google.protobuf.ByteString.EMPTY;
        dHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        dSAPubKeyP_ = com.google.protobuf.ByteString.EMPTY;
        dSAPubKeyQ_ = com.google.protobuf.ByteString.EMPTY;
        dSAPubKeyG_ = com.google.protobuf.ByteString.EMPTY;
        sigAlg_ = com.google.protobuf.ByteString.EMPTY;
        sigVal_ = com.google.protobuf.ByteString.EMPTY;
        authKeyID_ = com.google.protobuf.ByteString.EMPTY;
        subKeyID_ = com.google.protobuf.ByteString.EMPTY;
        keyUsage_ = com.google.protobuf.ByteString.EMPTY;
        priKeyUsaPerNotBef_ = 0L;
        priKeyUsaPerNotAft_ = 0L;
        certPol_ = com.google.protobuf.ByteString.EMPTY;
        subAltDNS_ = com.google.protobuf.ByteString.EMPTY;
        subAltIP_ = com.google.protobuf.ByteString.EMPTY;
        subAltName_ = com.google.protobuf.ByteString.EMPTY;
        issAltNameSys_ = com.google.protobuf.ByteString.EMPTY;
        issAltIP_ = com.google.protobuf.ByteString.EMPTY;
        issAltName_ = com.google.protobuf.ByteString.EMPTY;
        subDirAtt_ = com.google.protobuf.ByteString.EMPTY;
        extKeyUsage_ = com.google.protobuf.ByteString.EMPTY;
        certRevListSrc_ = com.google.protobuf.ByteString.EMPTY;
        certAuthInfAccMet_ = com.google.protobuf.ByteString.EMPTY;
        certAuthInfAccLoc_ = com.google.protobuf.ByteString.EMPTY;
        extCnt_ = 0;
        protabname_ = com.google.protobuf.ByteString.EMPTY;
        issuer_ = com.google.protobuf.ByteString.EMPTY;
        subject_ = com.google.protobuf.ByteString.EMPTY;
        daysRem_ = 0;
        pubkey_ = com.google.protobuf.ByteString.EMPTY;
        fpAlg_ = com.google.protobuf.ByteString.EMPTY;
        hash_ = com.google.protobuf.ByteString.EMPTY;
        extSet_ = com.google.protobuf.ByteString.EMPTY;
        daysTotal_ = 0;
        subAltDNSCnt_ = 0;
        authinfo_ = com.google.protobuf.ByteString.EMPTY;
        basicConsCA_ = com.google.protobuf.ByteString.EMPTY;
        basicConsPathLen_ = com.google.protobuf.ByteString.EMPTY;
        basicCons_ = com.google.protobuf.ByteString.EMPTY;
        keyPur_ = com.google.protobuf.ByteString.EMPTY;
        certype_ = 0;
        certFullText_ = com.google.protobuf.ByteString.EMPTY;
        alternativeIpCount_ = 0;
        source_ = 0;
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return X509CerInfoOuterClass.internal_static_X509CerInfo_descriptor;
      }

      @Override
      public X509CerInfo getDefaultInstanceForType() {
        return X509CerInfo.getDefaultInstance();
      }

      @Override
      public X509CerInfo build() {
        X509CerInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public X509CerInfo buildPartial() {
        X509CerInfo result = new X509CerInfo(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        if (bitField2_ != 0) { buildPartial2(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(X509CerInfo result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.protabID_ = protabID_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ver_ = ver_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.srvNum_ = srvNum_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.issDataLen_ = issDataLen_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.issComName_ = issComName_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.issConName_ = issConName_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.issLoaName_ = issLoaName_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.issStaOrProName_ = issStaOrProName_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.issStrAddr_ = issStrAddr_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.issOrgName_ = issOrgName_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.issOrgUniName_ = issOrgUniName_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.issPosOffBox_ = issPosOffBox_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.subComName_ = subComName_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.subConName_ = subConName_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.subLoaName_ = subLoaName_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.subStaOrProName_ = subStaOrProName_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.subStrAddr_ = subStrAddr_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.subOrgName_ = subOrgName_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.subOrgUniName_ = subOrgUniName_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.subPosOffBox_ = subPosOffBox_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.valNotBef_ = valNotBef_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.valNotAft_ = valNotAft_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.rSAMod_ = rSAMod_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.rSAExp_ = rSAExp_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.dHPriMod_ = dHPriMod_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.dHPGen_ = dHPGen_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.dHPubKey_ = dHPubKey_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.dSAPubKeyP_ = dSAPubKeyP_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.dSAPubKeyQ_ = dSAPubKeyQ_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.dSAPubKeyG_ = dSAPubKeyG_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.sigAlg_ = sigAlg_;
          to_bitField0_ |= 0x40000000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.sigVal_ = sigVal_;
          to_bitField0_ |= 0x80000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(X509CerInfo result) {
        int from_bitField1_ = bitField1_;
        int to_bitField1_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.authKeyID_ = authKeyID_;
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.subKeyID_ = subKeyID_;
          to_bitField1_ |= 0x00000002;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.keyUsage_ = keyUsage_;
          to_bitField1_ |= 0x00000004;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.priKeyUsaPerNotBef_ = priKeyUsaPerNotBef_;
          to_bitField1_ |= 0x00000008;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.priKeyUsaPerNotAft_ = priKeyUsaPerNotAft_;
          to_bitField1_ |= 0x00000010;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.certPol_ = certPol_;
          to_bitField1_ |= 0x00000020;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.subAltDNS_ = subAltDNS_;
          to_bitField1_ |= 0x00000040;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          result.subAltIP_ = subAltIP_;
          to_bitField1_ |= 0x00000080;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.subAltName_ = subAltName_;
          to_bitField1_ |= 0x00000100;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          result.issAltNameSys_ = issAltNameSys_;
          to_bitField1_ |= 0x00000200;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          result.issAltIP_ = issAltIP_;
          to_bitField1_ |= 0x00000400;
        }
        if (((from_bitField1_ & 0x00000800) != 0)) {
          result.issAltName_ = issAltName_;
          to_bitField1_ |= 0x00000800;
        }
        if (((from_bitField1_ & 0x00001000) != 0)) {
          result.subDirAtt_ = subDirAtt_;
          to_bitField1_ |= 0x00001000;
        }
        if (((from_bitField1_ & 0x00002000) != 0)) {
          result.extKeyUsage_ = extKeyUsage_;
          to_bitField1_ |= 0x00002000;
        }
        if (((from_bitField1_ & 0x00004000) != 0)) {
          result.certRevListSrc_ = certRevListSrc_;
          to_bitField1_ |= 0x00004000;
        }
        if (((from_bitField1_ & 0x00008000) != 0)) {
          result.certAuthInfAccMet_ = certAuthInfAccMet_;
          to_bitField1_ |= 0x00008000;
        }
        if (((from_bitField1_ & 0x00010000) != 0)) {
          result.certAuthInfAccLoc_ = certAuthInfAccLoc_;
          to_bitField1_ |= 0x00010000;
        }
        if (((from_bitField1_ & 0x00020000) != 0)) {
          result.extCnt_ = extCnt_;
          to_bitField1_ |= 0x00020000;
        }
        if (((from_bitField1_ & 0x00040000) != 0)) {
          result.protabname_ = protabname_;
          to_bitField1_ |= 0x00040000;
        }
        if (((from_bitField1_ & 0x00080000) != 0)) {
          result.issuer_ = issuer_;
          to_bitField1_ |= 0x00080000;
        }
        if (((from_bitField1_ & 0x00100000) != 0)) {
          result.subject_ = subject_;
          to_bitField1_ |= 0x00100000;
        }
        if (((from_bitField1_ & 0x00200000) != 0)) {
          result.daysRem_ = daysRem_;
          to_bitField1_ |= 0x00200000;
        }
        if (((from_bitField1_ & 0x00400000) != 0)) {
          result.pubkey_ = pubkey_;
          to_bitField1_ |= 0x00400000;
        }
        if (((from_bitField1_ & 0x00800000) != 0)) {
          result.fpAlg_ = fpAlg_;
          to_bitField1_ |= 0x00800000;
        }
        if (((from_bitField1_ & 0x01000000) != 0)) {
          result.hash_ = hash_;
          to_bitField1_ |= 0x01000000;
        }
        if (((from_bitField1_ & 0x02000000) != 0)) {
          result.extSet_ = extSet_;
          to_bitField1_ |= 0x02000000;
        }
        if (((from_bitField1_ & 0x04000000) != 0)) {
          result.daysTotal_ = daysTotal_;
          to_bitField1_ |= 0x04000000;
        }
        if (((from_bitField1_ & 0x08000000) != 0)) {
          result.subAltDNSCnt_ = subAltDNSCnt_;
          to_bitField1_ |= 0x08000000;
        }
        if (((from_bitField1_ & 0x10000000) != 0)) {
          result.authinfo_ = authinfo_;
          to_bitField1_ |= 0x10000000;
        }
        if (((from_bitField1_ & 0x20000000) != 0)) {
          result.basicConsCA_ = basicConsCA_;
          to_bitField1_ |= 0x20000000;
        }
        if (((from_bitField1_ & 0x40000000) != 0)) {
          result.basicConsPathLen_ = basicConsPathLen_;
          to_bitField1_ |= 0x40000000;
        }
        if (((from_bitField1_ & 0x80000000) != 0)) {
          result.basicCons_ = basicCons_;
          to_bitField1_ |= 0x80000000;
        }
        result.bitField1_ |= to_bitField1_;
      }

      private void buildPartial2(X509CerInfo result) {
        int from_bitField2_ = bitField2_;
        int to_bitField2_ = 0;
        if (((from_bitField2_ & 0x00000001) != 0)) {
          result.keyPur_ = keyPur_;
          to_bitField2_ |= 0x00000001;
        }
        if (((from_bitField2_ & 0x00000002) != 0)) {
          result.certype_ = certype_;
          to_bitField2_ |= 0x00000002;
        }
        if (((from_bitField2_ & 0x00000004) != 0)) {
          result.certFullText_ = certFullText_;
          to_bitField2_ |= 0x00000004;
        }
        if (((from_bitField2_ & 0x00000008) != 0)) {
          result.alternativeIpCount_ = alternativeIpCount_;
          to_bitField2_ |= 0x00000008;
        }
        if (((from_bitField2_ & 0x00000010) != 0)) {
          result.source_ = source_;
          to_bitField2_ |= 0x00000010;
        }
        result.bitField2_ |= to_bitField2_;
      }

      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof X509CerInfo) {
          return mergeFrom((X509CerInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(X509CerInfo other) {
        if (other == X509CerInfo.getDefaultInstance()) return this;
        if (other.hasProtabID()) {
          setProtabID(other.getProtabID());
        }
        if (other.hasVer()) {
          setVer(other.getVer());
        }
        if (other.hasSrvNum()) {
          setSrvNum(other.getSrvNum());
        }
        if (other.hasIssDataLen()) {
          setIssDataLen(other.getIssDataLen());
        }
        if (other.hasIssComName()) {
          setIssComName(other.getIssComName());
        }
        if (other.hasIssConName()) {
          setIssConName(other.getIssConName());
        }
        if (other.hasIssLoaName()) {
          setIssLoaName(other.getIssLoaName());
        }
        if (other.hasIssStaOrProName()) {
          setIssStaOrProName(other.getIssStaOrProName());
        }
        if (other.hasIssStrAddr()) {
          setIssStrAddr(other.getIssStrAddr());
        }
        if (other.hasIssOrgName()) {
          setIssOrgName(other.getIssOrgName());
        }
        if (other.hasIssOrgUniName()) {
          setIssOrgUniName(other.getIssOrgUniName());
        }
        if (other.hasIssPosOffBox()) {
          setIssPosOffBox(other.getIssPosOffBox());
        }
        if (other.hasSubComName()) {
          setSubComName(other.getSubComName());
        }
        if (other.hasSubConName()) {
          setSubConName(other.getSubConName());
        }
        if (other.hasSubLoaName()) {
          setSubLoaName(other.getSubLoaName());
        }
        if (other.hasSubStaOrProName()) {
          setSubStaOrProName(other.getSubStaOrProName());
        }
        if (other.hasSubStrAddr()) {
          setSubStrAddr(other.getSubStrAddr());
        }
        if (other.hasSubOrgName()) {
          setSubOrgName(other.getSubOrgName());
        }
        if (other.hasSubOrgUniName()) {
          setSubOrgUniName(other.getSubOrgUniName());
        }
        if (other.hasSubPosOffBox()) {
          setSubPosOffBox(other.getSubPosOffBox());
        }
        if (other.hasValNotBef()) {
          setValNotBef(other.getValNotBef());
        }
        if (other.hasValNotAft()) {
          setValNotAft(other.getValNotAft());
        }
        if (other.hasRSAMod()) {
          setRSAMod(other.getRSAMod());
        }
        if (other.hasRSAExp()) {
          setRSAExp(other.getRSAExp());
        }
        if (other.hasDHPriMod()) {
          setDHPriMod(other.getDHPriMod());
        }
        if (other.hasDHPGen()) {
          setDHPGen(other.getDHPGen());
        }
        if (other.hasDHPubKey()) {
          setDHPubKey(other.getDHPubKey());
        }
        if (other.hasDSAPubKeyP()) {
          setDSAPubKeyP(other.getDSAPubKeyP());
        }
        if (other.hasDSAPubKeyQ()) {
          setDSAPubKeyQ(other.getDSAPubKeyQ());
        }
        if (other.hasDSAPubKeyG()) {
          setDSAPubKeyG(other.getDSAPubKeyG());
        }
        if (other.hasSigAlg()) {
          setSigAlg(other.getSigAlg());
        }
        if (other.hasSigVal()) {
          setSigVal(other.getSigVal());
        }
        if (other.hasAuthKeyID()) {
          setAuthKeyID(other.getAuthKeyID());
        }
        if (other.hasSubKeyID()) {
          setSubKeyID(other.getSubKeyID());
        }
        if (other.hasKeyUsage()) {
          setKeyUsage(other.getKeyUsage());
        }
        if (other.hasPriKeyUsaPerNotBef()) {
          setPriKeyUsaPerNotBef(other.getPriKeyUsaPerNotBef());
        }
        if (other.hasPriKeyUsaPerNotAft()) {
          setPriKeyUsaPerNotAft(other.getPriKeyUsaPerNotAft());
        }
        if (other.hasCertPol()) {
          setCertPol(other.getCertPol());
        }
        if (other.hasSubAltDNS()) {
          setSubAltDNS(other.getSubAltDNS());
        }
        if (other.hasSubAltIP()) {
          setSubAltIP(other.getSubAltIP());
        }
        if (other.hasSubAltName()) {
          setSubAltName(other.getSubAltName());
        }
        if (other.hasIssAltNameSys()) {
          setIssAltNameSys(other.getIssAltNameSys());
        }
        if (other.hasIssAltIP()) {
          setIssAltIP(other.getIssAltIP());
        }
        if (other.hasIssAltName()) {
          setIssAltName(other.getIssAltName());
        }
        if (other.hasSubDirAtt()) {
          setSubDirAtt(other.getSubDirAtt());
        }
        if (other.hasExtKeyUsage()) {
          setExtKeyUsage(other.getExtKeyUsage());
        }
        if (other.hasCertRevListSrc()) {
          setCertRevListSrc(other.getCertRevListSrc());
        }
        if (other.hasCertAuthInfAccMet()) {
          setCertAuthInfAccMet(other.getCertAuthInfAccMet());
        }
        if (other.hasCertAuthInfAccLoc()) {
          setCertAuthInfAccLoc(other.getCertAuthInfAccLoc());
        }
        if (other.hasExtCnt()) {
          setExtCnt(other.getExtCnt());
        }
        if (other.hasProtabname()) {
          setProtabname(other.getProtabname());
        }
        if (other.hasIssuer()) {
          setIssuer(other.getIssuer());
        }
        if (other.hasSubject()) {
          setSubject(other.getSubject());
        }
        if (other.hasDaysRem()) {
          setDaysRem(other.getDaysRem());
        }
        if (other.hasPubkey()) {
          setPubkey(other.getPubkey());
        }
        if (other.hasFpAlg()) {
          setFpAlg(other.getFpAlg());
        }
        if (other.hasHash()) {
          setHash(other.getHash());
        }
        if (other.hasExtSet()) {
          setExtSet(other.getExtSet());
        }
        if (other.hasDaysTotal()) {
          setDaysTotal(other.getDaysTotal());
        }
        if (other.hasSubAltDNSCnt()) {
          setSubAltDNSCnt(other.getSubAltDNSCnt());
        }
        if (other.hasAuthinfo()) {
          setAuthinfo(other.getAuthinfo());
        }
        if (other.hasBasicConsCA()) {
          setBasicConsCA(other.getBasicConsCA());
        }
        if (other.hasBasicConsPathLen()) {
          setBasicConsPathLen(other.getBasicConsPathLen());
        }
        if (other.hasBasicCons()) {
          setBasicCons(other.getBasicCons());
        }
        if (other.hasKeyPur()) {
          setKeyPur(other.getKeyPur());
        }
        if (other.hasCertype()) {
          setCertype(other.getCertype());
        }
        if (other.hasCertFullText()) {
          setCertFullText(other.getCertFullText());
        }
        if (other.hasAlternativeIpCount()) {
          setAlternativeIpCount(other.getAlternativeIpCount());
        }
        if (other.hasSource()) {
          setSource(other.getSource());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                protabID_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                ver_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                srvNum_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                issDataLen_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                issComName_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                issConName_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                issLoaName_ = input.readBytes();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                issStaOrProName_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                issStrAddr_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                issOrgName_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                issOrgUniName_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                issPosOffBox_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                subComName_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                subConName_ = input.readBytes();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              case 122: {
                subLoaName_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                subStaOrProName_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                subStrAddr_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                subOrgName_ = input.readBytes();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                subOrgUniName_ = input.readBytes();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 162: {
                subPosOffBox_ = input.readBytes();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 168: {
                valNotBef_ = input.readUInt64();
                bitField0_ |= 0x00100000;
                break;
              } // case 168
              case 176: {
                valNotAft_ = input.readUInt64();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              case 186: {
                rSAMod_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                rSAExp_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                dHPriMod_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                dHPGen_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 218: {
                dHPubKey_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 226: {
                dSAPubKeyP_ = input.readBytes();
                bitField0_ |= 0x08000000;
                break;
              } // case 226
              case 234: {
                dSAPubKeyQ_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              case 242: {
                dSAPubKeyG_ = input.readBytes();
                bitField0_ |= 0x20000000;
                break;
              } // case 242
              case 250: {
                sigAlg_ = input.readBytes();
                bitField0_ |= 0x40000000;
                break;
              } // case 250
              case 258: {
                sigVal_ = input.readBytes();
                bitField0_ |= 0x80000000;
                break;
              } // case 258
              case 266: {
                authKeyID_ = input.readBytes();
                bitField1_ |= 0x00000001;
                break;
              } // case 266
              case 274: {
                subKeyID_ = input.readBytes();
                bitField1_ |= 0x00000002;
                break;
              } // case 274
              case 282: {
                keyUsage_ = input.readBytes();
                bitField1_ |= 0x00000004;
                break;
              } // case 282
              case 288: {
                priKeyUsaPerNotBef_ = input.readUInt64();
                bitField1_ |= 0x00000008;
                break;
              } // case 288
              case 296: {
                priKeyUsaPerNotAft_ = input.readUInt64();
                bitField1_ |= 0x00000010;
                break;
              } // case 296
              case 306: {
                certPol_ = input.readBytes();
                bitField1_ |= 0x00000020;
                break;
              } // case 306
              case 314: {
                subAltDNS_ = input.readBytes();
                bitField1_ |= 0x00000040;
                break;
              } // case 314
              case 322: {
                subAltIP_ = input.readBytes();
                bitField1_ |= 0x00000080;
                break;
              } // case 322
              case 330: {
                subAltName_ = input.readBytes();
                bitField1_ |= 0x00000100;
                break;
              } // case 330
              case 338: {
                issAltNameSys_ = input.readBytes();
                bitField1_ |= 0x00000200;
                break;
              } // case 338
              case 346: {
                issAltIP_ = input.readBytes();
                bitField1_ |= 0x00000400;
                break;
              } // case 346
              case 354: {
                issAltName_ = input.readBytes();
                bitField1_ |= 0x00000800;
                break;
              } // case 354
              case 362: {
                subDirAtt_ = input.readBytes();
                bitField1_ |= 0x00001000;
                break;
              } // case 362
              case 370: {
                extKeyUsage_ = input.readBytes();
                bitField1_ |= 0x00002000;
                break;
              } // case 370
              case 378: {
                certRevListSrc_ = input.readBytes();
                bitField1_ |= 0x00004000;
                break;
              } // case 378
              case 386: {
                certAuthInfAccMet_ = input.readBytes();
                bitField1_ |= 0x00008000;
                break;
              } // case 386
              case 394: {
                certAuthInfAccLoc_ = input.readBytes();
                bitField1_ |= 0x00010000;
                break;
              } // case 394
              case 400: {
                extCnt_ = input.readUInt32();
                bitField1_ |= 0x00020000;
                break;
              } // case 400
              case 410: {
                protabname_ = input.readBytes();
                bitField1_ |= 0x00040000;
                break;
              } // case 410
              case 418: {
                issuer_ = input.readBytes();
                bitField1_ |= 0x00080000;
                break;
              } // case 418
              case 426: {
                subject_ = input.readBytes();
                bitField1_ |= 0x00100000;
                break;
              } // case 426
              case 432: {
                daysRem_ = input.readUInt32();
                bitField1_ |= 0x00200000;
                break;
              } // case 432
              case 442: {
                pubkey_ = input.readBytes();
                bitField1_ |= 0x00400000;
                break;
              } // case 442
              case 450: {
                fpAlg_ = input.readBytes();
                bitField1_ |= 0x00800000;
                break;
              } // case 450
              case 458: {
                hash_ = input.readBytes();
                bitField1_ |= 0x01000000;
                break;
              } // case 458
              case 466: {
                extSet_ = input.readBytes();
                bitField1_ |= 0x02000000;
                break;
              } // case 466
              case 472: {
                daysTotal_ = input.readUInt32();
                bitField1_ |= 0x04000000;
                break;
              } // case 472
              case 480: {
                subAltDNSCnt_ = input.readUInt32();
                bitField1_ |= 0x08000000;
                break;
              } // case 480
              case 490: {
                authinfo_ = input.readBytes();
                bitField1_ |= 0x10000000;
                break;
              } // case 490
              case 498: {
                basicConsCA_ = input.readBytes();
                bitField1_ |= 0x20000000;
                break;
              } // case 498
              case 506: {
                basicConsPathLen_ = input.readBytes();
                bitField1_ |= 0x40000000;
                break;
              } // case 506
              case 514: {
                basicCons_ = input.readBytes();
                bitField1_ |= 0x80000000;
                break;
              } // case 514
              case 522: {
                keyPur_ = input.readBytes();
                bitField2_ |= 0x00000001;
                break;
              } // case 522
              case 528: {
                certype_ = input.readUInt32();
                bitField2_ |= 0x00000002;
                break;
              } // case 528
              case 538: {
                certFullText_ = input.readBytes();
                bitField2_ |= 0x00000004;
                break;
              } // case 538
              case 544: {
                alternativeIpCount_ = input.readUInt32();
                bitField2_ |= 0x00000008;
                break;
              } // case 544
              case 552: {
                source_ = input.readUInt32();
                bitField2_ |= 0x00000010;
                break;
              } // case 552
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;

      private long protabID_ ;
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       * @return Whether the protabID field is set.
       */
      @Override
      public boolean hasProtabID() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       * @return The protabID.
       */
      @Override
      public long getProtabID() {
        return protabID_;
      }
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       * @param value The protabID to set.
       * @return This builder for chaining.
       */
      public Builder setProtabID(long value) {

        protabID_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ProtabID = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtabID() {
        bitField0_ = (bitField0_ & ~0x00000001);
        protabID_ = 0L;
        onChanged();
        return this;
      }

      private int ver_ ;
      /**
       * <code>optional uint32 ver = 2;</code>
       * @return Whether the ver field is set.
       */
      @Override
      public boolean hasVer() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 ver = 2;</code>
       * @return The ver.
       */
      @Override
      public int getVer() {
        return ver_;
      }
      /**
       * <code>optional uint32 ver = 2;</code>
       * @param value The ver to set.
       * @return This builder for chaining.
       */
      public Builder setVer(int value) {

        ver_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ver = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVer() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ver_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvNum_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvNum = 3;</code>
       * @return Whether the srvNum field is set.
       */
      @Override
      public boolean hasSrvNum() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes srvNum = 3;</code>
       * @return The srvNum.
       */
      @Override
      public com.google.protobuf.ByteString getSrvNum() {
        return srvNum_;
      }
      /**
       * <code>optional bytes srvNum = 3;</code>
       * @param value The srvNum to set.
       * @return This builder for chaining.
       */
      public Builder setSrvNum(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvNum_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvNum = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        srvNum_ = getDefaultInstance().getSrvNum();
        onChanged();
        return this;
      }

      private int issDataLen_ ;
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       * @return Whether the issDataLen field is set.
       */
      @Override
      public boolean hasIssDataLen() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       * @return The issDataLen.
       */
      @Override
      public int getIssDataLen() {
        return issDataLen_;
      }
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       * @param value The issDataLen to set.
       * @return This builder for chaining.
       */
      public Builder setIssDataLen(int value) {

        issDataLen_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 issDataLen = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssDataLen() {
        bitField0_ = (bitField0_ & ~0x00000008);
        issDataLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issComName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issComName = 5;</code>
       * @return Whether the issComName field is set.
       */
      @Override
      public boolean hasIssComName() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes issComName = 5;</code>
       * @return The issComName.
       */
      @Override
      public com.google.protobuf.ByteString getIssComName() {
        return issComName_;
      }
      /**
       * <code>optional bytes issComName = 5;</code>
       * @param value The issComName to set.
       * @return This builder for chaining.
       */
      public Builder setIssComName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issComName_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issComName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssComName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        issComName_ = getDefaultInstance().getIssComName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issConName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issConName = 6;</code>
       * @return Whether the issConName field is set.
       */
      @Override
      public boolean hasIssConName() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes issConName = 6;</code>
       * @return The issConName.
       */
      @Override
      public com.google.protobuf.ByteString getIssConName() {
        return issConName_;
      }
      /**
       * <code>optional bytes issConName = 6;</code>
       * @param value The issConName to set.
       * @return This builder for chaining.
       */
      public Builder setIssConName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issConName_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issConName = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssConName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        issConName_ = getDefaultInstance().getIssConName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issLoaName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issLoaName = 7;</code>
       * @return Whether the issLoaName field is set.
       */
      @Override
      public boolean hasIssLoaName() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes issLoaName = 7;</code>
       * @return The issLoaName.
       */
      @Override
      public com.google.protobuf.ByteString getIssLoaName() {
        return issLoaName_;
      }
      /**
       * <code>optional bytes issLoaName = 7;</code>
       * @param value The issLoaName to set.
       * @return This builder for chaining.
       */
      public Builder setIssLoaName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issLoaName_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issLoaName = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssLoaName() {
        bitField0_ = (bitField0_ & ~0x00000040);
        issLoaName_ = getDefaultInstance().getIssLoaName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       * @return Whether the issStaOrProName field is set.
       */
      @Override
      public boolean hasIssStaOrProName() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       * @return The issStaOrProName.
       */
      @Override
      public com.google.protobuf.ByteString getIssStaOrProName() {
        return issStaOrProName_;
      }
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       * @param value The issStaOrProName to set.
       * @return This builder for chaining.
       */
      public Builder setIssStaOrProName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issStaOrProName_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issStaOrProName = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssStaOrProName() {
        bitField0_ = (bitField0_ & ~0x00000080);
        issStaOrProName_ = getDefaultInstance().getIssStaOrProName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       * @return Whether the issStrAddr field is set.
       */
      @Override
      public boolean hasIssStrAddr() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       * @return The issStrAddr.
       */
      @Override
      public com.google.protobuf.ByteString getIssStrAddr() {
        return issStrAddr_;
      }
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       * @param value The issStrAddr to set.
       * @return This builder for chaining.
       */
      public Builder setIssStrAddr(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issStrAddr_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issStrAddr = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssStrAddr() {
        bitField0_ = (bitField0_ & ~0x00000100);
        issStrAddr_ = getDefaultInstance().getIssStrAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issOrgName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issOrgName = 10;</code>
       * @return Whether the issOrgName field is set.
       */
      @Override
      public boolean hasIssOrgName() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes issOrgName = 10;</code>
       * @return The issOrgName.
       */
      @Override
      public com.google.protobuf.ByteString getIssOrgName() {
        return issOrgName_;
      }
      /**
       * <code>optional bytes issOrgName = 10;</code>
       * @param value The issOrgName to set.
       * @return This builder for chaining.
       */
      public Builder setIssOrgName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issOrgName_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issOrgName = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssOrgName() {
        bitField0_ = (bitField0_ & ~0x00000200);
        issOrgName_ = getDefaultInstance().getIssOrgName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       * @return Whether the issOrgUniName field is set.
       */
      @Override
      public boolean hasIssOrgUniName() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       * @return The issOrgUniName.
       */
      @Override
      public com.google.protobuf.ByteString getIssOrgUniName() {
        return issOrgUniName_;
      }
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       * @param value The issOrgUniName to set.
       * @return This builder for chaining.
       */
      public Builder setIssOrgUniName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issOrgUniName_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issOrgUniName = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssOrgUniName() {
        bitField0_ = (bitField0_ & ~0x00000400);
        issOrgUniName_ = getDefaultInstance().getIssOrgUniName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       * @return Whether the issPosOffBox field is set.
       */
      @Override
      public boolean hasIssPosOffBox() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       * @return The issPosOffBox.
       */
      @Override
      public com.google.protobuf.ByteString getIssPosOffBox() {
        return issPosOffBox_;
      }
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       * @param value The issPosOffBox to set.
       * @return This builder for chaining.
       */
      public Builder setIssPosOffBox(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issPosOffBox_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issPosOffBox = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssPosOffBox() {
        bitField0_ = (bitField0_ & ~0x00000800);
        issPosOffBox_ = getDefaultInstance().getIssPosOffBox();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subComName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subComName = 13;</code>
       * @return Whether the subComName field is set.
       */
      @Override
      public boolean hasSubComName() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes subComName = 13;</code>
       * @return The subComName.
       */
      @Override
      public com.google.protobuf.ByteString getSubComName() {
        return subComName_;
      }
      /**
       * <code>optional bytes subComName = 13;</code>
       * @param value The subComName to set.
       * @return This builder for chaining.
       */
      public Builder setSubComName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subComName_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subComName = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubComName() {
        bitField0_ = (bitField0_ & ~0x00001000);
        subComName_ = getDefaultInstance().getSubComName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subConName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subConName = 14;</code>
       * @return Whether the subConName field is set.
       */
      @Override
      public boolean hasSubConName() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes subConName = 14;</code>
       * @return The subConName.
       */
      @Override
      public com.google.protobuf.ByteString getSubConName() {
        return subConName_;
      }
      /**
       * <code>optional bytes subConName = 14;</code>
       * @param value The subConName to set.
       * @return This builder for chaining.
       */
      public Builder setSubConName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subConName_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subConName = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubConName() {
        bitField0_ = (bitField0_ & ~0x00002000);
        subConName_ = getDefaultInstance().getSubConName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subLoaName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subLoaName = 15;</code>
       * @return Whether the subLoaName field is set.
       */
      @Override
      public boolean hasSubLoaName() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes subLoaName = 15;</code>
       * @return The subLoaName.
       */
      @Override
      public com.google.protobuf.ByteString getSubLoaName() {
        return subLoaName_;
      }
      /**
       * <code>optional bytes subLoaName = 15;</code>
       * @param value The subLoaName to set.
       * @return This builder for chaining.
       */
      public Builder setSubLoaName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subLoaName_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subLoaName = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubLoaName() {
        bitField0_ = (bitField0_ & ~0x00004000);
        subLoaName_ = getDefaultInstance().getSubLoaName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subStaOrProName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       * @return Whether the subStaOrProName field is set.
       */
      @Override
      public boolean hasSubStaOrProName() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       * @return The subStaOrProName.
       */
      @Override
      public com.google.protobuf.ByteString getSubStaOrProName() {
        return subStaOrProName_;
      }
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       * @param value The subStaOrProName to set.
       * @return This builder for chaining.
       */
      public Builder setSubStaOrProName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subStaOrProName_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subStaOrProName = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubStaOrProName() {
        bitField0_ = (bitField0_ & ~0x00008000);
        subStaOrProName_ = getDefaultInstance().getSubStaOrProName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subStrAddr_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       * @return Whether the subStrAddr field is set.
       */
      @Override
      public boolean hasSubStrAddr() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       * @return The subStrAddr.
       */
      @Override
      public com.google.protobuf.ByteString getSubStrAddr() {
        return subStrAddr_;
      }
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       * @param value The subStrAddr to set.
       * @return This builder for chaining.
       */
      public Builder setSubStrAddr(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subStrAddr_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subStrAddr = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubStrAddr() {
        bitField0_ = (bitField0_ & ~0x00010000);
        subStrAddr_ = getDefaultInstance().getSubStrAddr();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subOrgName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subOrgName = 18;</code>
       * @return Whether the subOrgName field is set.
       */
      @Override
      public boolean hasSubOrgName() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes subOrgName = 18;</code>
       * @return The subOrgName.
       */
      @Override
      public com.google.protobuf.ByteString getSubOrgName() {
        return subOrgName_;
      }
      /**
       * <code>optional bytes subOrgName = 18;</code>
       * @param value The subOrgName to set.
       * @return This builder for chaining.
       */
      public Builder setSubOrgName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subOrgName_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subOrgName = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubOrgName() {
        bitField0_ = (bitField0_ & ~0x00020000);
        subOrgName_ = getDefaultInstance().getSubOrgName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subOrgUniName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       * @return Whether the subOrgUniName field is set.
       */
      @Override
      public boolean hasSubOrgUniName() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       * @return The subOrgUniName.
       */
      @Override
      public com.google.protobuf.ByteString getSubOrgUniName() {
        return subOrgUniName_;
      }
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       * @param value The subOrgUniName to set.
       * @return This builder for chaining.
       */
      public Builder setSubOrgUniName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subOrgUniName_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subOrgUniName = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubOrgUniName() {
        bitField0_ = (bitField0_ & ~0x00040000);
        subOrgUniName_ = getDefaultInstance().getSubOrgUniName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subPosOffBox_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       * @return Whether the subPosOffBox field is set.
       */
      @Override
      public boolean hasSubPosOffBox() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       * @return The subPosOffBox.
       */
      @Override
      public com.google.protobuf.ByteString getSubPosOffBox() {
        return subPosOffBox_;
      }
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       * @param value The subPosOffBox to set.
       * @return This builder for chaining.
       */
      public Builder setSubPosOffBox(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subPosOffBox_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subPosOffBox = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubPosOffBox() {
        bitField0_ = (bitField0_ & ~0x00080000);
        subPosOffBox_ = getDefaultInstance().getSubPosOffBox();
        onChanged();
        return this;
      }

      private long valNotBef_ ;
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       * @return Whether the valNotBef field is set.
       */
      @Override
      public boolean hasValNotBef() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       * @return The valNotBef.
       */
      @Override
      public long getValNotBef() {
        return valNotBef_;
      }
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       * @param value The valNotBef to set.
       * @return This builder for chaining.
       */
      public Builder setValNotBef(long value) {

        valNotBef_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 valNotBef = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearValNotBef() {
        bitField0_ = (bitField0_ & ~0x00100000);
        valNotBef_ = 0L;
        onChanged();
        return this;
      }

      private long valNotAft_ ;
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       * @return Whether the valNotAft field is set.
       */
      @Override
      public boolean hasValNotAft() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       * @return The valNotAft.
       */
      @Override
      public long getValNotAft() {
        return valNotAft_;
      }
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       * @param value The valNotAft to set.
       * @return This builder for chaining.
       */
      public Builder setValNotAft(long value) {

        valNotAft_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 valNotAft = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearValNotAft() {
        bitField0_ = (bitField0_ & ~0x00200000);
        valNotAft_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAMod_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAMod = 23;</code>
       * @return Whether the rSAMod field is set.
       */
      @Override
      public boolean hasRSAMod() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes RSAMod = 23;</code>
       * @return The rSAMod.
       */
      @Override
      public com.google.protobuf.ByteString getRSAMod() {
        return rSAMod_;
      }
      /**
       * <code>optional bytes RSAMod = 23;</code>
       * @param value The rSAMod to set.
       * @return This builder for chaining.
       */
      public Builder setRSAMod(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rSAMod_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAMod = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSAMod() {
        bitField0_ = (bitField0_ & ~0x00400000);
        rSAMod_ = getDefaultInstance().getRSAMod();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAExp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAExp = 24;</code>
       * @return Whether the rSAExp field is set.
       */
      @Override
      public boolean hasRSAExp() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes RSAExp = 24;</code>
       * @return The rSAExp.
       */
      @Override
      public com.google.protobuf.ByteString getRSAExp() {
        return rSAExp_;
      }
      /**
       * <code>optional bytes RSAExp = 24;</code>
       * @param value The rSAExp to set.
       * @return This builder for chaining.
       */
      public Builder setRSAExp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rSAExp_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAExp = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSAExp() {
        bitField0_ = (bitField0_ & ~0x00800000);
        rSAExp_ = getDefaultInstance().getRSAExp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHPriMod_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       * @return Whether the dHPriMod field is set.
       */
      @Override
      public boolean hasDHPriMod() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       * @return The dHPriMod.
       */
      @Override
      public com.google.protobuf.ByteString getDHPriMod() {
        return dHPriMod_;
      }
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       * @param value The dHPriMod to set.
       * @return This builder for chaining.
       */
      public Builder setDHPriMod(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHPriMod_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHPriMod = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHPriMod() {
        bitField0_ = (bitField0_ & ~0x01000000);
        dHPriMod_ = getDefaultInstance().getDHPriMod();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHPGen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHPGen = 26;</code>
       * @return Whether the dHPGen field is set.
       */
      @Override
      public boolean hasDHPGen() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes DHPGen = 26;</code>
       * @return The dHPGen.
       */
      @Override
      public com.google.protobuf.ByteString getDHPGen() {
        return dHPGen_;
      }
      /**
       * <code>optional bytes DHPGen = 26;</code>
       * @param value The dHPGen to set.
       * @return This builder for chaining.
       */
      public Builder setDHPGen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHPGen_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHPGen = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHPGen() {
        bitField0_ = (bitField0_ & ~0x02000000);
        dHPGen_ = getDefaultInstance().getDHPGen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       * @return Whether the dHPubKey field is set.
       */
      @Override
      public boolean hasDHPubKey() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       * @return The dHPubKey.
       */
      @Override
      public com.google.protobuf.ByteString getDHPubKey() {
        return dHPubKey_;
      }
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       * @param value The dHPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHPubKey_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHPubKey = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHPubKey() {
        bitField0_ = (bitField0_ & ~0x04000000);
        dHPubKey_ = getDefaultInstance().getDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dSAPubKeyP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       * @return Whether the dSAPubKeyP field is set.
       */
      @Override
      public boolean hasDSAPubKeyP() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       * @return The dSAPubKeyP.
       */
      @Override
      public com.google.protobuf.ByteString getDSAPubKeyP() {
        return dSAPubKeyP_;
      }
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       * @param value The dSAPubKeyP to set.
       * @return This builder for chaining.
       */
      public Builder setDSAPubKeyP(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dSAPubKeyP_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DSAPubKeyP = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearDSAPubKeyP() {
        bitField0_ = (bitField0_ & ~0x08000000);
        dSAPubKeyP_ = getDefaultInstance().getDSAPubKeyP();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dSAPubKeyQ_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       * @return Whether the dSAPubKeyQ field is set.
       */
      @Override
      public boolean hasDSAPubKeyQ() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       * @return The dSAPubKeyQ.
       */
      @Override
      public com.google.protobuf.ByteString getDSAPubKeyQ() {
        return dSAPubKeyQ_;
      }
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       * @param value The dSAPubKeyQ to set.
       * @return This builder for chaining.
       */
      public Builder setDSAPubKeyQ(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dSAPubKeyQ_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DSAPubKeyQ = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearDSAPubKeyQ() {
        bitField0_ = (bitField0_ & ~0x10000000);
        dSAPubKeyQ_ = getDefaultInstance().getDSAPubKeyQ();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dSAPubKeyG_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       * @return Whether the dSAPubKeyG field is set.
       */
      @Override
      public boolean hasDSAPubKeyG() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       * @return The dSAPubKeyG.
       */
      @Override
      public com.google.protobuf.ByteString getDSAPubKeyG() {
        return dSAPubKeyG_;
      }
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       * @param value The dSAPubKeyG to set.
       * @return This builder for chaining.
       */
      public Builder setDSAPubKeyG(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dSAPubKeyG_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DSAPubKeyG = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearDSAPubKeyG() {
        bitField0_ = (bitField0_ & ~0x20000000);
        dSAPubKeyG_ = getDefaultInstance().getDSAPubKeyG();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigAlg = 31;</code>
       * @return Whether the sigAlg field is set.
       */
      @Override
      public boolean hasSigAlg() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes sigAlg = 31;</code>
       * @return The sigAlg.
       */
      @Override
      public com.google.protobuf.ByteString getSigAlg() {
        return sigAlg_;
      }
      /**
       * <code>optional bytes sigAlg = 31;</code>
       * @param value The sigAlg to set.
       * @return This builder for chaining.
       */
      public Builder setSigAlg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sigAlg_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigAlg = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearSigAlg() {
        bitField0_ = (bitField0_ & ~0x40000000);
        sigAlg_ = getDefaultInstance().getSigAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigVal_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigVal = 32;</code>
       * @return Whether the sigVal field is set.
       */
      @Override
      public boolean hasSigVal() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes sigVal = 32;</code>
       * @return The sigVal.
       */
      @Override
      public com.google.protobuf.ByteString getSigVal() {
        return sigVal_;
      }
      /**
       * <code>optional bytes sigVal = 32;</code>
       * @param value The sigVal to set.
       * @return This builder for chaining.
       */
      public Builder setSigVal(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sigVal_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigVal = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearSigVal() {
        bitField0_ = (bitField0_ & ~0x80000000);
        sigVal_ = getDefaultInstance().getSigVal();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authKeyID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authKeyID = 33;</code>
       * @return Whether the authKeyID field is set.
       */
      @Override
      public boolean hasAuthKeyID() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes authKeyID = 33;</code>
       * @return The authKeyID.
       */
      @Override
      public com.google.protobuf.ByteString getAuthKeyID() {
        return authKeyID_;
      }
      /**
       * <code>optional bytes authKeyID = 33;</code>
       * @param value The authKeyID to set.
       * @return This builder for chaining.
       */
      public Builder setAuthKeyID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        authKeyID_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authKeyID = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthKeyID() {
        bitField1_ = (bitField1_ & ~0x00000001);
        authKeyID_ = getDefaultInstance().getAuthKeyID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subKeyID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subKeyID = 34;</code>
       * @return Whether the subKeyID field is set.
       */
      @Override
      public boolean hasSubKeyID() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes subKeyID = 34;</code>
       * @return The subKeyID.
       */
      @Override
      public com.google.protobuf.ByteString getSubKeyID() {
        return subKeyID_;
      }
      /**
       * <code>optional bytes subKeyID = 34;</code>
       * @param value The subKeyID to set.
       * @return This builder for chaining.
       */
      public Builder setSubKeyID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subKeyID_ = value;
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subKeyID = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubKeyID() {
        bitField1_ = (bitField1_ & ~0x00000002);
        subKeyID_ = getDefaultInstance().getSubKeyID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString keyUsage_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes keyUsage = 35;</code>
       * @return Whether the keyUsage field is set.
       */
      @Override
      public boolean hasKeyUsage() {
        return ((bitField1_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes keyUsage = 35;</code>
       * @return The keyUsage.
       */
      @Override
      public com.google.protobuf.ByteString getKeyUsage() {
        return keyUsage_;
      }
      /**
       * <code>optional bytes keyUsage = 35;</code>
       * @param value The keyUsage to set.
       * @return This builder for chaining.
       */
      public Builder setKeyUsage(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        keyUsage_ = value;
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes keyUsage = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearKeyUsage() {
        bitField1_ = (bitField1_ & ~0x00000004);
        keyUsage_ = getDefaultInstance().getKeyUsage();
        onChanged();
        return this;
      }

      private long priKeyUsaPerNotBef_ ;
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       * @return Whether the priKeyUsaPerNotBef field is set.
       */
      @Override
      public boolean hasPriKeyUsaPerNotBef() {
        return ((bitField1_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       * @return The priKeyUsaPerNotBef.
       */
      @Override
      public long getPriKeyUsaPerNotBef() {
        return priKeyUsaPerNotBef_;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       * @param value The priKeyUsaPerNotBef to set.
       * @return This builder for chaining.
       */
      public Builder setPriKeyUsaPerNotBef(long value) {

        priKeyUsaPerNotBef_ = value;
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotBef = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearPriKeyUsaPerNotBef() {
        bitField1_ = (bitField1_ & ~0x00000008);
        priKeyUsaPerNotBef_ = 0L;
        onChanged();
        return this;
      }

      private long priKeyUsaPerNotAft_ ;
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       * @return Whether the priKeyUsaPerNotAft field is set.
       */
      @Override
      public boolean hasPriKeyUsaPerNotAft() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       * @return The priKeyUsaPerNotAft.
       */
      @Override
      public long getPriKeyUsaPerNotAft() {
        return priKeyUsaPerNotAft_;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       * @param value The priKeyUsaPerNotAft to set.
       * @return This builder for chaining.
       */
      public Builder setPriKeyUsaPerNotAft(long value) {

        priKeyUsaPerNotAft_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 priKeyUsaPerNotAft = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearPriKeyUsaPerNotAft() {
        bitField1_ = (bitField1_ & ~0x00000010);
        priKeyUsaPerNotAft_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certPol_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certPol = 38;</code>
       * @return Whether the certPol field is set.
       */
      @Override
      public boolean hasCertPol() {
        return ((bitField1_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes certPol = 38;</code>
       * @return The certPol.
       */
      @Override
      public com.google.protobuf.ByteString getCertPol() {
        return certPol_;
      }
      /**
       * <code>optional bytes certPol = 38;</code>
       * @param value The certPol to set.
       * @return This builder for chaining.
       */
      public Builder setCertPol(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certPol_ = value;
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certPol = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertPol() {
        bitField1_ = (bitField1_ & ~0x00000020);
        certPol_ = getDefaultInstance().getCertPol();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subAltDNS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       * @return Whether the subAltDNS field is set.
       */
      @Override
      public boolean hasSubAltDNS() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       * @return The subAltDNS.
       */
      @Override
      public com.google.protobuf.ByteString getSubAltDNS() {
        return subAltDNS_;
      }
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       * @param value The subAltDNS to set.
       * @return This builder for chaining.
       */
      public Builder setSubAltDNS(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subAltDNS_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subAltDNS = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubAltDNS() {
        bitField1_ = (bitField1_ & ~0x00000040);
        subAltDNS_ = getDefaultInstance().getSubAltDNS();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subAltIP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subAltIP = 40;</code>
       * @return Whether the subAltIP field is set.
       */
      @Override
      public boolean hasSubAltIP() {
        return ((bitField1_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes subAltIP = 40;</code>
       * @return The subAltIP.
       */
      @Override
      public com.google.protobuf.ByteString getSubAltIP() {
        return subAltIP_;
      }
      /**
       * <code>optional bytes subAltIP = 40;</code>
       * @param value The subAltIP to set.
       * @return This builder for chaining.
       */
      public Builder setSubAltIP(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subAltIP_ = value;
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subAltIP = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubAltIP() {
        bitField1_ = (bitField1_ & ~0x00000080);
        subAltIP_ = getDefaultInstance().getSubAltIP();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subAltName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subAltName = 41;</code>
       * @return Whether the subAltName field is set.
       */
      @Override
      public boolean hasSubAltName() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes subAltName = 41;</code>
       * @return The subAltName.
       */
      @Override
      public com.google.protobuf.ByteString getSubAltName() {
        return subAltName_;
      }
      /**
       * <code>optional bytes subAltName = 41;</code>
       * @param value The subAltName to set.
       * @return This builder for chaining.
       */
      public Builder setSubAltName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subAltName_ = value;
        bitField1_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subAltName = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubAltName() {
        bitField1_ = (bitField1_ & ~0x00000100);
        subAltName_ = getDefaultInstance().getSubAltName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issAltNameSys_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       * @return Whether the issAltNameSys field is set.
       */
      @Override
      public boolean hasIssAltNameSys() {
        return ((bitField1_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       * @return The issAltNameSys.
       */
      @Override
      public com.google.protobuf.ByteString getIssAltNameSys() {
        return issAltNameSys_;
      }
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       * @param value The issAltNameSys to set.
       * @return This builder for chaining.
       */
      public Builder setIssAltNameSys(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issAltNameSys_ = value;
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issAltNameSys = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssAltNameSys() {
        bitField1_ = (bitField1_ & ~0x00000200);
        issAltNameSys_ = getDefaultInstance().getIssAltNameSys();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issAltIP_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issAltIP = 43;</code>
       * @return Whether the issAltIP field is set.
       */
      @Override
      public boolean hasIssAltIP() {
        return ((bitField1_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes issAltIP = 43;</code>
       * @return The issAltIP.
       */
      @Override
      public com.google.protobuf.ByteString getIssAltIP() {
        return issAltIP_;
      }
      /**
       * <code>optional bytes issAltIP = 43;</code>
       * @param value The issAltIP to set.
       * @return This builder for chaining.
       */
      public Builder setIssAltIP(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issAltIP_ = value;
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issAltIP = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssAltIP() {
        bitField1_ = (bitField1_ & ~0x00000400);
        issAltIP_ = getDefaultInstance().getIssAltIP();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issAltName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issAltName = 44;</code>
       * @return Whether the issAltName field is set.
       */
      @Override
      public boolean hasIssAltName() {
        return ((bitField1_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes issAltName = 44;</code>
       * @return The issAltName.
       */
      @Override
      public com.google.protobuf.ByteString getIssAltName() {
        return issAltName_;
      }
      /**
       * <code>optional bytes issAltName = 44;</code>
       * @param value The issAltName to set.
       * @return This builder for chaining.
       */
      public Builder setIssAltName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issAltName_ = value;
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issAltName = 44;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssAltName() {
        bitField1_ = (bitField1_ & ~0x00000800);
        issAltName_ = getDefaultInstance().getIssAltName();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subDirAtt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       * @return Whether the subDirAtt field is set.
       */
      @Override
      public boolean hasSubDirAtt() {
        return ((bitField1_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       * @return The subDirAtt.
       */
      @Override
      public com.google.protobuf.ByteString getSubDirAtt() {
        return subDirAtt_;
      }
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       * @param value The subDirAtt to set.
       * @return This builder for chaining.
       */
      public Builder setSubDirAtt(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subDirAtt_ = value;
        bitField1_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subDirAtt = 45;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubDirAtt() {
        bitField1_ = (bitField1_ & ~0x00001000);
        subDirAtt_ = getDefaultInstance().getSubDirAtt();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extKeyUsage_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       * @return Whether the extKeyUsage field is set.
       */
      @Override
      public boolean hasExtKeyUsage() {
        return ((bitField1_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       * @return The extKeyUsage.
       */
      @Override
      public com.google.protobuf.ByteString getExtKeyUsage() {
        return extKeyUsage_;
      }
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       * @param value The extKeyUsage to set.
       * @return This builder for chaining.
       */
      public Builder setExtKeyUsage(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        extKeyUsage_ = value;
        bitField1_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extKeyUsage = 46;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtKeyUsage() {
        bitField1_ = (bitField1_ & ~0x00002000);
        extKeyUsage_ = getDefaultInstance().getExtKeyUsage();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certRevListSrc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       * @return Whether the certRevListSrc field is set.
       */
      @Override
      public boolean hasCertRevListSrc() {
        return ((bitField1_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       * @return The certRevListSrc.
       */
      @Override
      public com.google.protobuf.ByteString getCertRevListSrc() {
        return certRevListSrc_;
      }
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       * @param value The certRevListSrc to set.
       * @return This builder for chaining.
       */
      public Builder setCertRevListSrc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certRevListSrc_ = value;
        bitField1_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certRevListSrc = 47;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertRevListSrc() {
        bitField1_ = (bitField1_ & ~0x00004000);
        certRevListSrc_ = getDefaultInstance().getCertRevListSrc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certAuthInfAccMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       * @return Whether the certAuthInfAccMet field is set.
       */
      @Override
      public boolean hasCertAuthInfAccMet() {
        return ((bitField1_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       * @return The certAuthInfAccMet.
       */
      @Override
      public com.google.protobuf.ByteString getCertAuthInfAccMet() {
        return certAuthInfAccMet_;
      }
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       * @param value The certAuthInfAccMet to set.
       * @return This builder for chaining.
       */
      public Builder setCertAuthInfAccMet(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certAuthInfAccMet_ = value;
        bitField1_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certAuthInfAccMet = 48;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertAuthInfAccMet() {
        bitField1_ = (bitField1_ & ~0x00008000);
        certAuthInfAccMet_ = getDefaultInstance().getCertAuthInfAccMet();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certAuthInfAccLoc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       * @return Whether the certAuthInfAccLoc field is set.
       */
      @Override
      public boolean hasCertAuthInfAccLoc() {
        return ((bitField1_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       * @return The certAuthInfAccLoc.
       */
      @Override
      public com.google.protobuf.ByteString getCertAuthInfAccLoc() {
        return certAuthInfAccLoc_;
      }
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       * @param value The certAuthInfAccLoc to set.
       * @return This builder for chaining.
       */
      public Builder setCertAuthInfAccLoc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certAuthInfAccLoc_ = value;
        bitField1_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certAuthInfAccLoc = 49;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertAuthInfAccLoc() {
        bitField1_ = (bitField1_ & ~0x00010000);
        certAuthInfAccLoc_ = getDefaultInstance().getCertAuthInfAccLoc();
        onChanged();
        return this;
      }

      private int extCnt_ ;
      /**
       * <code>optional uint32 extCnt = 50;</code>
       * @return Whether the extCnt field is set.
       */
      @Override
      public boolean hasExtCnt() {
        return ((bitField1_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 extCnt = 50;</code>
       * @return The extCnt.
       */
      @Override
      public int getExtCnt() {
        return extCnt_;
      }
      /**
       * <code>optional uint32 extCnt = 50;</code>
       * @param value The extCnt to set.
       * @return This builder for chaining.
       */
      public Builder setExtCnt(int value) {

        extCnt_ = value;
        bitField1_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 extCnt = 50;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtCnt() {
        bitField1_ = (bitField1_ & ~0x00020000);
        extCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString protabname_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes Protabname = 51;</code>
       * @return Whether the protabname field is set.
       */
      @Override
      public boolean hasProtabname() {
        return ((bitField1_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes Protabname = 51;</code>
       * @return The protabname.
       */
      @Override
      public com.google.protobuf.ByteString getProtabname() {
        return protabname_;
      }
      /**
       * <code>optional bytes Protabname = 51;</code>
       * @param value The protabname to set.
       * @return This builder for chaining.
       */
      public Builder setProtabname(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        protabname_ = value;
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes Protabname = 51;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtabname() {
        bitField1_ = (bitField1_ & ~0x00040000);
        protabname_ = getDefaultInstance().getProtabname();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString issuer_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes issuer = 52;</code>
       * @return Whether the issuer field is set.
       */
      @Override
      public boolean hasIssuer() {
        return ((bitField1_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes issuer = 52;</code>
       * @return The issuer.
       */
      @Override
      public com.google.protobuf.ByteString getIssuer() {
        return issuer_;
      }
      /**
       * <code>optional bytes issuer = 52;</code>
       * @param value The issuer to set.
       * @return This builder for chaining.
       */
      public Builder setIssuer(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        issuer_ = value;
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes issuer = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearIssuer() {
        bitField1_ = (bitField1_ & ~0x00080000);
        issuer_ = getDefaultInstance().getIssuer();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString subject_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes subject = 53;</code>
       * @return Whether the subject field is set.
       */
      @Override
      public boolean hasSubject() {
        return ((bitField1_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes subject = 53;</code>
       * @return The subject.
       */
      @Override
      public com.google.protobuf.ByteString getSubject() {
        return subject_;
      }
      /**
       * <code>optional bytes subject = 53;</code>
       * @param value The subject to set.
       * @return This builder for chaining.
       */
      public Builder setSubject(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        subject_ = value;
        bitField1_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes subject = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubject() {
        bitField1_ = (bitField1_ & ~0x00100000);
        subject_ = getDefaultInstance().getSubject();
        onChanged();
        return this;
      }

      private int daysRem_ ;
      /**
       * <code>optional uint32 daysRem = 54;</code>
       * @return Whether the daysRem field is set.
       */
      @java.lang.Override
      public boolean hasDaysRem() {
        return ((bitField1_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 daysRem = 54;</code>
       * @return The daysRem.
       */
      @java.lang.Override
      public int getDaysRem() {
        return daysRem_;
      }
      /**
       * <code>optional uint32 daysRem = 54;</code>
       * @param value The daysRem to set.
       * @return This builder for chaining.
       */
      public Builder setDaysRem(int value) {

        daysRem_ = value;
        bitField1_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 daysRem = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearDaysRem() {
        bitField1_ = (bitField1_ & ~0x00200000);
        daysRem_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString pubkey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes pubkey = 55;</code>
       * @return Whether the pubkey field is set.
       */
      @java.lang.Override
      public boolean hasPubkey() {
        return ((bitField1_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes pubkey = 55;</code>
       * @return The pubkey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPubkey() {
        return pubkey_;
      }
      /**
       * <code>optional bytes pubkey = 55;</code>
       * @param value The pubkey to set.
       * @return This builder for chaining.
       */
      public Builder setPubkey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        pubkey_ = value;
        bitField1_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes pubkey = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearPubkey() {
        bitField1_ = (bitField1_ & ~0x00400000);
        pubkey_ = getDefaultInstance().getPubkey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fpAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fpAlg = 56;</code>
       * @return Whether the fpAlg field is set.
       */
      @java.lang.Override
      public boolean hasFpAlg() {
        return ((bitField1_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes fpAlg = 56;</code>
       * @return The fpAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFpAlg() {
        return fpAlg_;
      }
      /**
       * <code>optional bytes fpAlg = 56;</code>
       * @param value The fpAlg to set.
       * @return This builder for chaining.
       */
      public Builder setFpAlg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fpAlg_ = value;
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fpAlg = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearFpAlg() {
        bitField1_ = (bitField1_ & ~0x00800000);
        fpAlg_ = getDefaultInstance().getFpAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString hash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes hash = 57;</code>
       * @return Whether the hash field is set.
       */
      @java.lang.Override
      public boolean hasHash() {
        return ((bitField1_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes hash = 57;</code>
       * @return The hash.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getHash() {
        return hash_;
      }
      /**
       * <code>optional bytes hash = 57;</code>
       * @param value The hash to set.
       * @return This builder for chaining.
       */
      public Builder setHash(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        hash_ = value;
        bitField1_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes hash = 57;</code>
       * @return This builder for chaining.
       */
      public Builder clearHash() {
        bitField1_ = (bitField1_ & ~0x01000000);
        hash_ = getDefaultInstance().getHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extSet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extSet = 58;</code>
       * @return Whether the extSet field is set.
       */
      @java.lang.Override
      public boolean hasExtSet() {
        return ((bitField1_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes extSet = 58;</code>
       * @return The extSet.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getExtSet() {
        return extSet_;
      }
      /**
       * <code>optional bytes extSet = 58;</code>
       * @param value The extSet to set.
       * @return This builder for chaining.
       */
      public Builder setExtSet(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        extSet_ = value;
        bitField1_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extSet = 58;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtSet() {
        bitField1_ = (bitField1_ & ~0x02000000);
        extSet_ = getDefaultInstance().getExtSet();
        onChanged();
        return this;
      }

      private int daysTotal_ ;
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       * @return Whether the daysTotal field is set.
       */
      @java.lang.Override
      public boolean hasDaysTotal() {
        return ((bitField1_ & 0x04000000) != 0);
      }
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       * @return The daysTotal.
       */
      @java.lang.Override
      public int getDaysTotal() {
        return daysTotal_;
      }
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       * @param value The daysTotal to set.
       * @return This builder for chaining.
       */
      public Builder setDaysTotal(int value) {

        daysTotal_ = value;
        bitField1_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 daysTotal = 59;</code>
       * @return This builder for chaining.
       */
      public Builder clearDaysTotal() {
        bitField1_ = (bitField1_ & ~0x04000000);
        daysTotal_ = 0;
        onChanged();
        return this;
      }

      private int subAltDNSCnt_ ;
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       * @return Whether the subAltDNSCnt field is set.
       */
      @java.lang.Override
      public boolean hasSubAltDNSCnt() {
        return ((bitField1_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       * @return The subAltDNSCnt.
       */
      @java.lang.Override
      public int getSubAltDNSCnt() {
        return subAltDNSCnt_;
      }
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       * @param value The subAltDNSCnt to set.
       * @return This builder for chaining.
       */
      public Builder setSubAltDNSCnt(int value) {

        subAltDNSCnt_ = value;
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 subAltDNSCnt = 60;</code>
       * @return This builder for chaining.
       */
      public Builder clearSubAltDNSCnt() {
        bitField1_ = (bitField1_ & ~0x08000000);
        subAltDNSCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authinfo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authinfo = 61;</code>
       * @return Whether the authinfo field is set.
       */
      @java.lang.Override
      public boolean hasAuthinfo() {
        return ((bitField1_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes authinfo = 61;</code>
       * @return The authinfo.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAuthinfo() {
        return authinfo_;
      }
      /**
       * <code>optional bytes authinfo = 61;</code>
       * @param value The authinfo to set.
       * @return This builder for chaining.
       */
      public Builder setAuthinfo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        authinfo_ = value;
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authinfo = 61;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthinfo() {
        bitField1_ = (bitField1_ & ~0x10000000);
        authinfo_ = getDefaultInstance().getAuthinfo();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString basicConsCA_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       * @return Whether the basicConsCA field is set.
       */
      @java.lang.Override
      public boolean hasBasicConsCA() {
        return ((bitField1_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       * @return The basicConsCA.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBasicConsCA() {
        return basicConsCA_;
      }
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       * @param value The basicConsCA to set.
       * @return This builder for chaining.
       */
      public Builder setBasicConsCA(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        basicConsCA_ = value;
        bitField1_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes basicConsCA = 62;</code>
       * @return This builder for chaining.
       */
      public Builder clearBasicConsCA() {
        bitField1_ = (bitField1_ & ~0x20000000);
        basicConsCA_ = getDefaultInstance().getBasicConsCA();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString basicConsPathLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       * @return Whether the basicConsPathLen field is set.
       */
      @java.lang.Override
      public boolean hasBasicConsPathLen() {
        return ((bitField1_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       * @return The basicConsPathLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBasicConsPathLen() {
        return basicConsPathLen_;
      }
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       * @param value The basicConsPathLen to set.
       * @return This builder for chaining.
       */
      public Builder setBasicConsPathLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        basicConsPathLen_ = value;
        bitField1_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes basicConsPathLen = 63;</code>
       * @return This builder for chaining.
       */
      public Builder clearBasicConsPathLen() {
        bitField1_ = (bitField1_ & ~0x40000000);
        basicConsPathLen_ = getDefaultInstance().getBasicConsPathLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString basicCons_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes basicCons = 64;</code>
       * @return Whether the basicCons field is set.
       */
      @java.lang.Override
      public boolean hasBasicCons() {
        return ((bitField1_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes basicCons = 64;</code>
       * @return The basicCons.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getBasicCons() {
        return basicCons_;
      }
      /**
       * <code>optional bytes basicCons = 64;</code>
       * @param value The basicCons to set.
       * @return This builder for chaining.
       */
      public Builder setBasicCons(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        basicCons_ = value;
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes basicCons = 64;</code>
       * @return This builder for chaining.
       */
      public Builder clearBasicCons() {
        bitField1_ = (bitField1_ & ~0x80000000);
        basicCons_ = getDefaultInstance().getBasicCons();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString keyPur_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes KeyPur = 65;</code>
       * @return Whether the keyPur field is set.
       */
      @java.lang.Override
      public boolean hasKeyPur() {
        return ((bitField2_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes KeyPur = 65;</code>
       * @return The keyPur.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getKeyPur() {
        return keyPur_;
      }
      /**
       * <code>optional bytes KeyPur = 65;</code>
       * @param value The keyPur to set.
       * @return This builder for chaining.
       */
      public Builder setKeyPur(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        keyPur_ = value;
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes KeyPur = 65;</code>
       * @return This builder for chaining.
       */
      public Builder clearKeyPur() {
        bitField2_ = (bitField2_ & ~0x00000001);
        keyPur_ = getDefaultInstance().getKeyPur();
        onChanged();
        return this;
      }

      private int certype_ ;
      /**
       * <code>optional uint32 Certype = 66;</code>
       * @return Whether the certype field is set.
       */
      @java.lang.Override
      public boolean hasCertype() {
        return ((bitField2_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 Certype = 66;</code>
       * @return The certype.
       */
      @java.lang.Override
      public int getCertype() {
        return certype_;
      }
      /**
       * <code>optional uint32 Certype = 66;</code>
       * @param value The certype to set.
       * @return This builder for chaining.
       */
      public Builder setCertype(int value) {

        certype_ = value;
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 Certype = 66;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertype() {
        bitField2_ = (bitField2_ & ~0x00000002);
        certype_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certFullText_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certFullText = 67;</code>
       * @return Whether the certFullText field is set.
       */
      @java.lang.Override
      public boolean hasCertFullText() {
        return ((bitField2_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes certFullText = 67;</code>
       * @return The certFullText.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCertFullText() {
        return certFullText_;
      }
      /**
       * <code>optional bytes certFullText = 67;</code>
       * @param value The certFullText to set.
       * @return This builder for chaining.
       */
      public Builder setCertFullText(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certFullText_ = value;
        bitField2_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certFullText = 67;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertFullText() {
        bitField2_ = (bitField2_ & ~0x00000004);
        certFullText_ = getDefaultInstance().getCertFullText();
        onChanged();
        return this;
      }

      private int alternativeIpCount_ ;
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       * @return Whether the alternativeIpCount field is set.
       */
      @java.lang.Override
      public boolean hasAlternativeIpCount() {
        return ((bitField2_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       * @return The alternativeIpCount.
       */
      @java.lang.Override
      public int getAlternativeIpCount() {
        return alternativeIpCount_;
      }
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       * @param value The alternativeIpCount to set.
       * @return This builder for chaining.
       */
      public Builder setAlternativeIpCount(int value) {

        alternativeIpCount_ = value;
        bitField2_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 alternativeIpCount = 68;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlternativeIpCount() {
        bitField2_ = (bitField2_ & ~0x00000008);
        alternativeIpCount_ = 0;
        onChanged();
        return this;
      }

      private int source_ ;
      /**
       * <code>optional uint32 source = 69;</code>
       * @return Whether the source field is set.
       */
      @java.lang.Override
      public boolean hasSource() {
        return ((bitField2_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 source = 69;</code>
       * @return The source.
       */
      @java.lang.Override
      public int getSource() {
        return source_;
      }
      /**
       * <code>optional uint32 source = 69;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(int value) {

        source_ = value;
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 source = 69;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField2_ = (bitField2_ & ~0x00000010);
        source_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:X509CerInfo)
    }

    // @@protoc_insertion_point(class_scope:X509CerInfo)
    private static final X509CerInfoOuterClass.X509CerInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new X509CerInfoOuterClass.X509CerInfo();
    }

    public static X509CerInfoOuterClass.X509CerInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<X509CerInfo>
        PARSER = new com.google.protobuf.AbstractParser<X509CerInfo>() {
      @java.lang.Override
      public X509CerInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<X509CerInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<X509CerInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public X509CerInfoOuterClass.X509CerInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_X509CerInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_X509CerInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021X509CerInfo.proto\"\331\n\n\013X509CerInfo\022\020\n\010P" +
      "rotabID\030\001 \001(\004\022\013\n\003ver\030\002 \001(\r\022\016\n\006srvNum\030\003 \001" +
      "(\014\022\022\n\nissDataLen\030\004 \001(\r\022\022\n\nissComName\030\005 \001" +
      "(\014\022\022\n\nissConName\030\006 \001(\014\022\022\n\nissLoaName\030\007 \001" +
      "(\014\022\027\n\017issStaOrProName\030\010 \001(\014\022\022\n\nissStrAdd" +
      "r\030\t \001(\014\022\022\n\nissOrgName\030\n \001(\014\022\025\n\rissOrgUni" +
      "Name\030\013 \001(\014\022\024\n\014issPosOffBox\030\014 \001(\014\022\022\n\nsubC" +
      "omName\030\r \001(\014\022\022\n\nsubConName\030\016 \001(\014\022\022\n\nsubL" +
      "oaName\030\017 \001(\014\022\027\n\017subStaOrProName\030\020 \001(\014\022\022\n" +
      "\nsubStrAddr\030\021 \001(\014\022\022\n\nsubOrgName\030\022 \001(\014\022\025\n" +
      "\rsubOrgUniName\030\023 \001(\014\022\024\n\014subPosOffBox\030\024 \001" +
      "(\014\022\021\n\tvalNotBef\030\025 \001(\004\022\021\n\tvalNotAft\030\026 \001(\004" +
      "\022\016\n\006RSAMod\030\027 \001(\014\022\016\n\006RSAExp\030\030 \001(\014\022\020\n\010DHPr" +
      "iMod\030\031 \001(\014\022\016\n\006DHPGen\030\032 \001(\014\022\020\n\010DHPubKey\030\033" +
      " \001(\014\022\022\n\nDSAPubKeyP\030\034 \001(\014\022\022\n\nDSAPubKeyQ\030\035" +
      " \001(\014\022\022\n\nDSAPubKeyG\030\036 \001(\014\022\016\n\006sigAlg\030\037 \001(\014" +
      "\022\016\n\006sigVal\030  \001(\014\022\021\n\tauthKeyID\030! \001(\014\022\020\n\010s" +
      "ubKeyID\030\" \001(\014\022\020\n\010keyUsage\030# \001(\014\022\032\n\022priKe" +
      "yUsaPerNotBef\030$ \001(\004\022\032\n\022priKeyUsaPerNotAf" +
      "t\030% \001(\004\022\017\n\007certPol\030& \001(\014\022\021\n\tsubAltDNS\030\' " +
      "\001(\014\022\020\n\010subAltIP\030( \001(\014\022\022\n\nsubAltName\030) \001(" +
      "\014\022\025\n\rissAltNameSys\030* \001(\014\022\020\n\010issAltIP\030+ \001" +
      "(\014\022\022\n\nissAltName\030, \001(\014\022\021\n\tsubDirAtt\030- \001(" +
      "\014\022\023\n\013extKeyUsage\030. \001(\014\022\026\n\016certRevListSrc" +
      "\030/ \001(\014\022\031\n\021certAuthInfAccMet\0300 \001(\014\022\031\n\021cer" +
      "tAuthInfAccLoc\0301 \001(\014\022\016\n\006extCnt\0302 \001(\r\022\022\n\n" +
      "Protabname\0303 \001(\014\022\016\n\006issuer\0304 \001(\014\022\017\n\007subj" +
      "ect\0305 \001(\014\022\017\n\007daysRem\0306 \001(\r\022\016\n\006pubkey\0307 \001" +
      "(\014\022\r\n\005fpAlg\0308 \001(\014\022\014\n\004hash\0309 \001(\014\022\016\n\006extSe" +
      "t\030: \001(\014\022\021\n\tdaysTotal\030; \001(\r\022\024\n\014subAltDNSC" +
      "nt\030< \001(\r\022\020\n\010authinfo\030= \001(\014\022\023\n\013basicConsC" +
      "A\030> \001(\014\022\030\n\020basicConsPathLen\030? \001(\014\022\021\n\tbas" +
      "icCons\030@ \001(\014\022\016\n\006KeyPur\030A \001(\014\022\017\n\007Certype\030" +
      "B \001(\r\022\024\n\014certFullText\030C \001(\014\022\032\n\022alternati" +
      "veIpCount\030D \001(\r\022\016\n\006source\030E \001(\r"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_X509CerInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_X509CerInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_X509CerInfo_descriptor,
        new java.lang.String[] { "ProtabID", "Ver", "SrvNum", "IssDataLen", "IssComName", "IssConName", "IssLoaName", "IssStaOrProName", "IssStrAddr", "IssOrgName", "IssOrgUniName", "IssPosOffBox", "SubComName", "SubConName", "SubLoaName", "SubStaOrProName", "SubStrAddr", "SubOrgName", "SubOrgUniName", "SubPosOffBox", "ValNotBef", "ValNotAft", "RSAMod", "RSAExp", "DHPriMod", "DHPGen", "DHPubKey", "DSAPubKeyP", "DSAPubKeyQ", "DSAPubKeyG", "SigAlg", "SigVal", "AuthKeyID", "SubKeyID", "KeyUsage", "PriKeyUsaPerNotBef", "PriKeyUsaPerNotAft", "CertPol", "SubAltDNS", "SubAltIP", "SubAltName", "IssAltNameSys", "IssAltIP", "IssAltName", "SubDirAtt", "ExtKeyUsage", "CertRevListSrc", "CertAuthInfAccMet", "CertAuthInfAccLoc", "ExtCnt", "Protabname", "Issuer", "Subject", "DaysRem", "Pubkey", "FpAlg", "Hash", "ExtSet", "DaysTotal", "SubAltDNSCnt", "Authinfo", "BasicConsCA", "BasicConsPathLen", "BasicCons", "KeyPur", "Certype", "CertFullText", "AlternativeIpCount", "Source", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
