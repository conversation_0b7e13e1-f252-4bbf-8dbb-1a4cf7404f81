package com.geeksec.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 4.9	置信度
 */
public enum ConfidenceEnum {

    LOW(1, "低"),
    MEDIUM(2, "中"),
    HIGH(3, "高");

    /**
     * 值
     */
    private Integer code;

    /**
     * 类型
     */
    private String msg;

    ConfidenceEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, ConfidenceEnum> codeEnumMap = new HashMap<>();

    static {
        for (ConfidenceEnum e : ConfidenceEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        ConfidenceEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }
}
