package com.geeksec.transfer.function.process.protocol;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.IotAlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class AlertIotProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {
    private static final Logger logger = LoggerFactory.getLogger(AlertIotProcessFunction.class);

    // TODO
    /**
     * 需要提取的点边关系
     * 点：
     * DEVICE 工控设备
     * 边：
     * attack_to_device
     * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, Row>.Context context, Collector<Row> collector) throws Exception {
        try {
            // 提取IOC 告警日志元数据
            IotAlertTrans iotAlertTrans = AlertTools.createIotAlertTrans(alertLog);
            if (iotAlertTrans != null) {
                List<Row> rows = iotAlertTrans.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing Iot alert log: {}", alertLog.getGuid(), e);
        }
    }
}
