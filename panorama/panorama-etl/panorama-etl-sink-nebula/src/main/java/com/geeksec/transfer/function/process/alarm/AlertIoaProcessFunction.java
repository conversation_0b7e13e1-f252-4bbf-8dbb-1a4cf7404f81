package com.geeksec.transfer.function.process.alarm;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.CryptoAlertTrans;
import com.geeksec.entity.pojo.IoaAlertTrans;
import com.geeksec.entity.pojo.IocAlertTrans;
import com.geeksec.proto.AlertLog;
import com.geeksec.transfer.function.process.protocol.AlertX509ProcessFunction;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertIoaProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {
    private static final Logger logger = LoggerFactory.getLogger(AlertIoaProcessFunction.class);

    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, Row>.Context context, Collector<Row> collector) throws Exception {
        try {
            // 提取IOC 告警日志元数据
            IoaAlertTrans ioaAlertTrans = AlertTools.createIoaAlertTrans(alertLog);
            if (ioaAlertTrans != null) {
                List<Row> rows = ioaAlertTrans.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing Ioa alert log: {}", alertLog.getGuid(), e);
        }
    }
}
