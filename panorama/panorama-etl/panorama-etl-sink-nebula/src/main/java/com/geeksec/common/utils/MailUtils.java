package com.geeksec.common.utils;

import cn.hutool.core.util.StrUtil;
import com.geeksec.entity.po.Email;
import org.apache.commons.validator.routines.EmailValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description：
 */
public class MailUtils {
    private final static Logger logger = LoggerFactory.getLogger(MailUtils.class);

    private static final EmailValidator emailValidator = EmailValidator.getInstance();

    private final static Pattern EMAIL_REGEX_COMLEX = Pattern.compile("((([!#$%&'*+\\-/=?^_`{|}~\\w])|" +
            "([!#$%&'*+\\-/=?^_`{|}~\\w][!#$%&'*+\\-/=?^_`{|}~\\w]*[!#$%&'*+\\-/=?^_`{|}~\\w]))[@]\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*)");
    private final static Pattern EMAIL_REGEX_SIMPLE = Pattern.compile("<\\s*?(\\S*?@\\S*)\\s*>");

    public static List<Email> parseEmailRaw(String emailRaw) {
        List<Email> emailList = new ArrayList<>();

        // 逗号分隔 表示可能有多个收件人
        String[] emailArray = emailRaw.split(",");
        for (String emailStr : emailArray) {
            emailStr = emailStr.trim();
            String name = "";
            String email = "";

            int bracketStart = emailStr.indexOf('<');
            int bracketEnd = emailStr.indexOf('>');

            if (bracketStart != -1 && bracketEnd != -1 && bracketStart < bracketEnd) {
                // 格式为 "Name <<EMAIL>>"
                name = emailStr.substring(0, bracketStart).trim();
                email = emailStr.substring(bracketStart + 1, bracketEnd).trim();
            } else {
                // 格式为纯邮箱地址 "<EMAIL>"
                email = emailStr;
            }

            // 移除名字中的引号
            name = name.replaceAll("['\"]", "").trim();

            if (emailValidator.isValid(email)) {
                Email emailEntity = new Email();
                emailEntity.setEmailAddr(email);
                emailEntity.setUserName(name);
                emailList.add(emailEntity);
            }
        }
        if (emailList.isEmpty()) {
            return new ArrayList<>();
        }
        return emailList;
    }

    /**
     * 判断是否有"<></>"的情况，如果有，前面的部分是用户名
     * @param emailStr
     * @return
     */
    private static int getEmailStartIndex(String emailStr) {
        int startIndex = -1;
        int delimiterIndex = emailStr.indexOf('<');
        if (delimiterIndex >= 0) {
            startIndex = emailStr.indexOf('>', delimiterIndex) + 1;
        }
        return startIndex;
    }

    public static List<HashMap<String, String>> parseEmailRawHashmap(String emailRaw) {
        List<HashMap<String, String>> emailList = new ArrayList<HashMap<String, String>>();

        // 逗号分隔 表示可能有多个收件人
        String[] emailArray = emailRaw.split(",");
        for (String emailStr : emailArray) {
            String name = null;
            String email = null;
            emailStr = emailStr.trim();

            if (emailStr.indexOf("<") > -1 && emailStr.indexOf(">") > -1) {
                // 简单匹配 <<EMAIL>>
                Matcher m = EMAIL_REGEX_SIMPLE.matcher(emailStr);
                if (m.find()) {
                    email = m.group(1);
                    int si = m.start();
                    String rname = emailStr.substring(0, si);
                    name = rname.replaceAll("['\"]", "").trim();
                    // logger.info("simple regex debug");
                    // logger.info(emailStr);
                    // logger.info(name);
                    // logger.info(email);
                } else {
                    // 无法正确匹配
                    // logger.info("simple regex error");
                    // logger.info(emailStr);
                }
            } else {
                // 无<>包围邮箱 可能为有邮箱地址
                // 采用复杂模式进行匹配
                Matcher m = EMAIL_REGEX_COMLEX.matcher(emailStr);
                if (m.find()) {
                    email = m.group(1);
                    int si = m.start();
                    if (si == 0) {
                        name = null;
                        // logger.info("complex regex debug");
                        // logger.info(emailStr);
                        // logger.info("no name found");
                        // logger.info(email);
                    } else {
                        String rname = emailStr.substring(0, si);
                        name = rname.replaceAll("['\"]", "").trim();
                        // logger.info("complex regex debug");
                        // logger.info(emailStr);
                        // logger.info(name);
                        // logger.info(email);
                    }
                } else {
                    // 无法正确匹配
                    // logger.info("complex regex error");
                    // logger.info(emailStr);
                }
            }
            HashMap<String, String> emailMap = new HashMap<String, String>();
            emailMap.put("name", name);
            emailMap.put("email", email);
            emailList.add(emailMap);
        }
        return emailList;
    }
}
