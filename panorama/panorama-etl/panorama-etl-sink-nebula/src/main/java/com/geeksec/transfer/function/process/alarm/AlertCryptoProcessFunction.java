package com.geeksec.transfer.function.process.alarm;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.CryptoAlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertCryptoProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {
    private static final Logger logger = LoggerFactory.getLogger(AlertCryptoProcessFunction.class);

    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, Row>.Context context, Collector<Row> collector) throws Exception {

        try {
            // 提取IOC 告警日志元数据
            CryptoAlertTrans cryptoAlertTrans = AlertTools.createCryptoAlertTrans(alertLog);
            if (cryptoAlertTrans != null) {
                List<Row> rows = cryptoAlertTrans.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing Crypto alert log: {}", alertLog.getGuid(), e);
        }

    }
}
