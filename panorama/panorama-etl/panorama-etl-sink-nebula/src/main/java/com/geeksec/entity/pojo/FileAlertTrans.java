package com.geeksec.entity.pojo;

import com.geeksec.common.utils.MailUtils;
import com.geeksec.config.util.MD5;
import com.geeksec.entity.po.Email;
import com.geeksec.entity.trans.EmailTrans;
import com.geeksec.entity.trans.FileTrans;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.message.FileAlertInfo;
import lombok.Data;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class FileAlertTrans {
    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;
    private FileTrans fileTrans;

    private EmailTrans emailTrans;

    /**
     * 对于文件来说 可能会存在邮件的收发者
     * 发起者统一为单个个体
     * 收件人可能为多个个体
     * */
    private Email emailSender = null;
    private List<Email> receiverList = null;

    // 解析当前告警中的邮件信息
    private void parseMailSenderAndReceiver() {
        List<Email> emailReceiverList = MailUtils.parseEmailRaw(emailTrans.getMailReceiverRaw());
        List<Email> emailSenderList = MailUtils.parseEmailRaw(emailTrans.getMailSenderRaw());

        if(!emailSenderList.isEmpty()){
            emailSender = emailSenderList.get(0);
        }
        if(!emailReceiverList.isEmpty()){
            receiverList = emailReceiverList;
        }
    }

    public List<Row> getAllRows() {
        List<Row> fileRelatedRows = new ArrayList<>();

        parseMailSenderAndReceiver();

        // FILE TAG
        Row fileTagRow = new Row(8);
        fileTagRow.setField(0, "TAG_FILE");
        fileTagRow.setField(1, fileTrans.getFileMd5());
        fileTagRow.setField(2, fileTrans.getFileSha1());
        fileTagRow.setField(3, fileTrans.getFileSha256());
        fileTagRow.setField(4, fileTrans.getFileSha512());
        fileTagRow.setField(5, fileTrans.getFileCrc32());
        fileTagRow.setField(6, fileTrans.getFileSize());
        fileTagRow.setField(7, fileTrans.getFilePath());
        fileRelatedRows.add(fileTagRow);

        // send_file edge
        Row sendFileRow = new Row(4);
        sendFileRow.setField(0, "EDGE_send_file");
        sendFileRow.setField(1, aipAddr);
        sendFileRow.setField(2, fileTrans.getFileMd5());
        sendFileRow.setField(3, 0);
        fileRelatedRows.add(sendFileRow);

        // receive_file edge
        Row receiveFileRow = new Row(4);
        receiveFileRow.setField(0, "EDGE_receive_file");
        receiveFileRow.setField(1, vipAddr);
        receiveFileRow.setField(2, fileTrans.getFileMd5());
        receiveFileRow.setField(3, 0);
        fileRelatedRows.add(receiveFileRow);

        // file_connect_ip edge
        Row fileConnectIpRow = new Row(4);
        fileConnectIpRow.setField(0, "EDGE_file_connect_ip");
        fileConnectIpRow.setField(1, fileTrans.getFileMd5());
        fileConnectIpRow.setField(2, fileTrans.getIocIp());
        fileConnectIpRow.setField(3, 0);
        fileRelatedRows.add(fileConnectIpRow);

        // file_connect_domain edge
        Row fileConnectDomainRow = new Row(4);
        fileConnectDomainRow.setField(0, "EDGE_file_connect_domain");
        fileConnectDomainRow.setField(1, fileTrans.getFileMd5());
        fileConnectDomainRow.setField(2, fileTrans.getIocDomain());
        fileConnectDomainRow.setField(3, 0);
        fileRelatedRows.add(fileConnectDomainRow);

        // file_connect_url edge
        Row fileConnectUrlRow = new Row(4);
        fileConnectUrlRow.setField(0, "EDGE_file_connect_url");
        fileConnectUrlRow.setField(1, fileTrans.getFileMd5());
        fileConnectUrlRow.setField(2, fileTrans.getIocUrl());
        fileConnectUrlRow.setField(3, 0);
        fileRelatedRows.add(fileConnectUrlRow);

        if (emailSender != null && receiverList != null) {
            fileRelatedRows.addAll(getAllMailRelatedRows());
        }

        return fileRelatedRows;
    }

    private List<Row> getAllMailRelatedRows()  {
        List<Row> rows = new ArrayList<>();

        // sender_send_file
        Row senderSendFileRow = new Row(4);
        senderSendFileRow.setField(0, "EDGE_sender_send_file");
        senderSendFileRow.setField(1, MD5.getMd5(emailSender.getEmailAddr()));
        senderSendFileRow.setField(2, emailTrans.getFileMd5());
        senderSendFileRow.setField(3, 0);
        rows.add(senderSendFileRow);

        for (Email remail : this.receiverList) {
            // receiver_receive_file
            Row receiverReceiveFileRow = new Row(4);
            receiverReceiveFileRow.setField(0, "EDGE_receiver_receive_file");
            receiverReceiveFileRow.setField(1, emailTrans.getFileMd5());
            receiverReceiveFileRow.setField(2, MD5.getMd5(remail.getEmailAddr()));
            receiverReceiveFileRow.setField(3, 0);
            rows.add(receiverReceiveFileRow);
        }
        return rows;
    }

}
