package com.geeksec.proto.message;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: PROTO_ALERT_INFO.proto
// Protobuf Java Version: 4.29.4

public final class ProtoAlertInfo {
  private ProtoAlertInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      ProtoAlertInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PROTO_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:PROTO_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     * @return Whether the protoAlarmName field is set.
     */
    boolean hasProtoAlarmName();
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     * @return The protoAlarmName.
     */
    String getProtoAlarmName();
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     * @return The bytes for protoAlarmName.
     */
    com.google.protobuf.ByteString
        getProtoAlarmNameBytes();

    /**
     * <pre>
     * 检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     * @return Whether the protoModelName field is set.
     */
    boolean hasProtoModelName();
    /**
     * <pre>
     * 检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     * @return The protoModelName.
     */
    String getProtoModelName();
    /**
     * <pre>
     * 检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     * @return The bytes for protoModelName.
     */
    com.google.protobuf.ByteString
        getProtoModelNameBytes();

    /**
     * <pre>
     * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     * @return Whether the protoAttackLevel field is set.
     */
    boolean hasProtoAttackLevel();
    /**
     * <pre>
     * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     * @return The protoAttackLevel.
     */
    int getProtoAttackLevel();

    /**
     * <pre>
     * 检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     * @return Whether the protoAlarmPrinciple field is set.
     */
    boolean hasProtoAlarmPrinciple();
    /**
     * <pre>
     * 检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     * @return The protoAlarmPrinciple.
     */
    String getProtoAlarmPrinciple();
    /**
     * <pre>
     * 检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     * @return The bytes for protoAlarmPrinciple.
     */
    com.google.protobuf.ByteString
        getProtoAlarmPrincipleBytes();

    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @return A list containing the protoAlarmReasonKey.
     */
    java.util.List<String>
        getProtoAlarmReasonKeyList();
    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @return The count of protoAlarmReasonKey.
     */
    int getProtoAlarmReasonKeyCount();
    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @param index The index of the element to return.
     * @return The protoAlarmReasonKey at the given index.
     */
    String getProtoAlarmReasonKey(int index);
    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoAlarmReasonKey at the given index.
     */
    com.google.protobuf.ByteString
        getProtoAlarmReasonKeyBytes(int index);

    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @return A list containing the protoAlarmReasonActualValue.
     */
    java.util.List<String>
        getProtoAlarmReasonActualValueList();
    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @return The count of protoAlarmReasonActualValue.
     */
    int getProtoAlarmReasonActualValueCount();
    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @param index The index of the element to return.
     * @return The protoAlarmReasonActualValue at the given index.
     */
    String getProtoAlarmReasonActualValue(int index);
    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoAlarmReasonActualValue at the given index.
     */
    com.google.protobuf.ByteString
        getProtoAlarmReasonActualValueBytes(int index);

    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @return A list containing the protoTargetsName.
     */
    java.util.List<String>
        getProtoTargetsNameList();
    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @return The count of protoTargetsName.
     */
    int getProtoTargetsNameCount();
    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @param index The index of the element to return.
     * @return The protoTargetsName at the given index.
     */
    String getProtoTargetsName(int index);
    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoTargetsName at the given index.
     */
    com.google.protobuf.ByteString
        getProtoTargetsNameBytes(int index);

    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @return A list containing the protoTargetsType.
     */
    java.util.List<String>
        getProtoTargetsTypeList();
    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @return The count of protoTargetsType.
     */
    int getProtoTargetsTypeCount();
    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @param index The index of the element to return.
     * @return The protoTargetsType at the given index.
     */
    String getProtoTargetsType(int index);
    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoTargetsType at the given index.
     */
    com.google.protobuf.ByteString
        getProtoTargetsTypeBytes(int index);

    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @return A list containing the protoMaliousFamilyType.
     */
    java.util.List<String>
        getProtoMaliousFamilyTypeList();
    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @return The count of protoMaliousFamilyType.
     */
    int getProtoMaliousFamilyTypeCount();
    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @param index The index of the element to return.
     * @return The protoMaliousFamilyType at the given index.
     */
    String getProtoMaliousFamilyType(int index);
    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoMaliousFamilyType at the given index.
     */
    com.google.protobuf.ByteString
        getProtoMaliousFamilyTypeBytes(int index);

    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @return A list containing the protoMaliousFamilyName.
     */
    java.util.List<String>
        getProtoMaliousFamilyNameList();
    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @return The count of protoMaliousFamilyName.
     */
    int getProtoMaliousFamilyNameCount();
    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @param index The index of the element to return.
     * @return The protoMaliousFamilyName at the given index.
     */
    String getProtoMaliousFamilyName(int index);
    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoMaliousFamilyName at the given index.
     */
    com.google.protobuf.ByteString
        getProtoMaliousFamilyNameBytes(int index);

    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @return A list containing the protoThreatTag.
     */
    java.util.List<String>
        getProtoThreatTagList();
    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @return The count of protoThreatTag.
     */
    int getProtoThreatTagCount();
    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @param index The index of the element to return.
     * @return The protoThreatTag at the given index.
     */
    String getProtoThreatTag(int index);
    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoThreatTag at the given index.
     */
    com.google.protobuf.ByteString
        getProtoThreatTagBytes(int index);

    /**
     * <pre>
     * 受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     * @return Whether the protoVictimHost field is set.
     */
    boolean hasProtoVictimHost();
    /**
     * <pre>
     * 受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     * @return The protoVictimHost.
     */
    String getProtoVictimHost();
    /**
     * <pre>
     * 受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     * @return The bytes for protoVictimHost.
     */
    com.google.protobuf.ByteString
        getProtoVictimHostBytes();

    /**
     * <pre>
     * 受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     * @return Whether the protoVictimSni field is set.
     */
    boolean hasProtoVictimSni();
    /**
     * <pre>
     * 受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     * @return The protoVictimSni.
     */
    String getProtoVictimSni();
    /**
     * <pre>
     * 受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     * @return The bytes for protoVictimSni.
     */
    com.google.protobuf.ByteString
        getProtoVictimSniBytes();

    /**
     * <pre>
     * 处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     * @return Whether the protoAlarmHandleMethod field is set.
     */
    boolean hasProtoAlarmHandleMethod();
    /**
     * <pre>
     * 处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     * @return The protoAlarmHandleMethod.
     */
    String getProtoAlarmHandleMethod();
    /**
     * <pre>
     * 处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     * @return The bytes for protoAlarmHandleMethod.
     */
    com.google.protobuf.ByteString
        getProtoAlarmHandleMethodBytes();
  }
  /**
   * <pre>
   * 特色协议威胁告警信息
   * </pre>
   *
   * Protobuf type {@code PROTO_ALERT_INFO}
   */
  public static final class PROTO_ALERT_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:PROTO_ALERT_INFO)
      PROTO_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        PROTO_ALERT_INFO.class.getName());
    }
    // Use PROTO_ALERT_INFO.newBuilder() to construct.
    private PROTO_ALERT_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private PROTO_ALERT_INFO() {
      protoAlarmName_ = "";
      protoModelName_ = "";
      protoAlarmPrinciple_ = "";
      protoAlarmReasonKey_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      protoAlarmReasonActualValue_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      protoTargetsName_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      protoTargetsType_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      protoMaliousFamilyType_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      protoMaliousFamilyName_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      protoThreatTag_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      protoVictimHost_ = "";
      protoVictimSni_ = "";
      protoAlarmHandleMethod_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              PROTO_ALERT_INFO.class, Builder.class);
    }

    private int bitField0_;
    public static final int PROTO_ALARM_NAME_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object protoAlarmName_ = "";
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     * @return Whether the protoAlarmName field is set.
     */
    @Override
    public boolean hasProtoAlarmName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     * @return The protoAlarmName.
     */
    @Override
    public String getProtoAlarmName() {
      Object ref = protoAlarmName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoAlarmName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>optional string proto_alarm_name = 1;</code>
     * @return The bytes for protoAlarmName.
     */
    @Override
    public com.google.protobuf.ByteString
        getProtoAlarmNameBytes() {
      Object ref = protoAlarmName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        protoAlarmName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_MODEL_NAME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile Object protoModelName_ = "";
    /**
     * <pre>
     * 检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     * @return Whether the protoModelName field is set.
     */
    @Override
    public boolean hasProtoModelName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     * @return The protoModelName.
     */
    @Override
    public String getProtoModelName() {
      Object ref = protoModelName_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoModelName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 检测模型	
     * </pre>
     *
     * <code>optional string proto_model_name = 2;</code>
     * @return The bytes for protoModelName.
     */
    @Override
    public com.google.protobuf.ByteString
        getProtoModelNameBytes() {
      Object ref = protoModelName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        protoModelName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_ATTACK_LEVEL_FIELD_NUMBER = 3;
    private int protoAttackLevel_ = 0;
    /**
     * <pre>
     * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     * @return Whether the protoAttackLevel field is set.
     */
    @Override
    public boolean hasProtoAttackLevel() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
     * </pre>
     *
     * <code>optional uint32 proto_attack_level = 3;</code>
     * @return The protoAttackLevel.
     */
    @Override
    public int getProtoAttackLevel() {
      return protoAttackLevel_;
    }

    public static final int PROTO_ALARM_PRINCIPLE_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile Object protoAlarmPrinciple_ = "";
    /**
     * <pre>
     * 检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     * @return Whether the protoAlarmPrinciple field is set.
     */
    @Override
    public boolean hasProtoAlarmPrinciple() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     * @return The protoAlarmPrinciple.
     */
    @Override
    public String getProtoAlarmPrinciple() {
      Object ref = protoAlarmPrinciple_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoAlarmPrinciple_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 检测原理	
     * </pre>
     *
     * <code>optional string proto_alarm_principle = 4;</code>
     * @return The bytes for protoAlarmPrinciple.
     */
    @Override
    public com.google.protobuf.ByteString
        getProtoAlarmPrincipleBytes() {
      Object ref = protoAlarmPrinciple_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        protoAlarmPrinciple_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_ALARM_REASON_KEY_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList protoAlarmReasonKey_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @return A list containing the protoAlarmReasonKey.
     */
    public com.google.protobuf.ProtocolStringList
        getProtoAlarmReasonKeyList() {
      return protoAlarmReasonKey_;
    }
    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @return The count of protoAlarmReasonKey.
     */
    public int getProtoAlarmReasonKeyCount() {
      return protoAlarmReasonKey_.size();
    }
    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @param index The index of the element to return.
     * @return The protoAlarmReasonKey at the given index.
     */
    public String getProtoAlarmReasonKey(int index) {
      return protoAlarmReasonKey_.get(index);
    }
    /**
     * <pre>
     * 告警匹配特征	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_key = 5;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoAlarmReasonKey at the given index.
     */
    public com.google.protobuf.ByteString
        getProtoAlarmReasonKeyBytes(int index) {
      return protoAlarmReasonKey_.getByteString(index);
    }

    public static final int PROTO_ALARM_REASON_ACTUAL_VALUE_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList protoAlarmReasonActualValue_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @return A list containing the protoAlarmReasonActualValue.
     */
    public com.google.protobuf.ProtocolStringList
        getProtoAlarmReasonActualValueList() {
      return protoAlarmReasonActualValue_;
    }
    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @return The count of protoAlarmReasonActualValue.
     */
    public int getProtoAlarmReasonActualValueCount() {
      return protoAlarmReasonActualValue_.size();
    }
    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @param index The index of the element to return.
     * @return The protoAlarmReasonActualValue at the given index.
     */
    public String getProtoAlarmReasonActualValue(int index) {
      return protoAlarmReasonActualValue_.get(index);
    }
    /**
     * <pre>
     * 告警特征命中情况	
     * </pre>
     *
     * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoAlarmReasonActualValue at the given index.
     */
    public com.google.protobuf.ByteString
        getProtoAlarmReasonActualValueBytes(int index) {
      return protoAlarmReasonActualValue_.getByteString(index);
    }

    public static final int PROTO_TARGETS_NAME_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList protoTargetsName_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @return A list containing the protoTargetsName.
     */
    public com.google.protobuf.ProtocolStringList
        getProtoTargetsNameList() {
      return protoTargetsName_;
    }
    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @return The count of protoTargetsName.
     */
    public int getProtoTargetsNameCount() {
      return protoTargetsName_.size();
    }
    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @param index The index of the element to return.
     * @return The protoTargetsName at the given index.
     */
    public String getProtoTargetsName(int index) {
      return protoTargetsName_.get(index);
    }
    /**
     * <pre>
     * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
     * </pre>
     *
     * <code>repeated string proto_targets_name = 7;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoTargetsName at the given index.
     */
    public com.google.protobuf.ByteString
        getProtoTargetsNameBytes(int index) {
      return protoTargetsName_.getByteString(index);
    }

    public static final int PROTO_TARGETS_TYPE_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList protoTargetsType_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @return A list containing the protoTargetsType.
     */
    public com.google.protobuf.ProtocolStringList
        getProtoTargetsTypeList() {
      return protoTargetsType_;
    }
    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @return The count of protoTargetsType.
     */
    public int getProtoTargetsTypeCount() {
      return protoTargetsType_.size();
    }
    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @param index The index of the element to return.
     * @return The protoTargetsType at the given index.
     */
    public String getProtoTargetsType(int index) {
      return protoTargetsType_.get(index);
    }
    /**
     * <pre>
     * 告警对象类型	session/ip/domain/cert/finger
     * </pre>
     *
     * <code>repeated string proto_targets_type = 8;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoTargetsType at the given index.
     */
    public com.google.protobuf.ByteString
        getProtoTargetsTypeBytes(int index) {
      return protoTargetsType_.getByteString(index);
    }

    public static final int PROTO_MALIOUS_FAMILY_TYPE_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList protoMaliousFamilyType_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @return A list containing the protoMaliousFamilyType.
     */
    public com.google.protobuf.ProtocolStringList
        getProtoMaliousFamilyTypeList() {
      return protoMaliousFamilyType_;
    }
    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @return The count of protoMaliousFamilyType.
     */
    public int getProtoMaliousFamilyTypeCount() {
      return protoMaliousFamilyType_.size();
    }
    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @param index The index of the element to return.
     * @return The protoMaliousFamilyType at the given index.
     */
    public String getProtoMaliousFamilyType(int index) {
      return protoMaliousFamilyType_.get(index);
    }
    /**
     * <pre>
     * 恶意家族类型	恶意软件/APT组织
     * </pre>
     *
     * <code>repeated string proto_malious_family_type = 9;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoMaliousFamilyType at the given index.
     */
    public com.google.protobuf.ByteString
        getProtoMaliousFamilyTypeBytes(int index) {
      return protoMaliousFamilyType_.getByteString(index);
    }

    public static final int PROTO_MALIOUS_FAMILY_NAME_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList protoMaliousFamilyName_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @return A list containing the protoMaliousFamilyName.
     */
    public com.google.protobuf.ProtocolStringList
        getProtoMaliousFamilyNameList() {
      return protoMaliousFamilyName_;
    }
    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @return The count of protoMaliousFamilyName.
     */
    public int getProtoMaliousFamilyNameCount() {
      return protoMaliousFamilyName_.size();
    }
    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @param index The index of the element to return.
     * @return The protoMaliousFamilyName at the given index.
     */
    public String getProtoMaliousFamilyName(int index) {
      return protoMaliousFamilyName_.get(index);
    }
    /**
     * <pre>
     * 恶意家族名称	apt29/apt32
     * </pre>
     *
     * <code>repeated string proto_malious_family_name = 10;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoMaliousFamilyName at the given index.
     */
    public com.google.protobuf.ByteString
        getProtoMaliousFamilyNameBytes(int index) {
      return protoMaliousFamilyName_.getByteString(index);
    }

    public static final int PROTO_THREAT_TAG_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList protoThreatTag_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @return A list containing the protoThreatTag.
     */
    public com.google.protobuf.ProtocolStringList
        getProtoThreatTagList() {
      return protoThreatTag_;
    }
    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @return The count of protoThreatTag.
     */
    public int getProtoThreatTagCount() {
      return protoThreatTag_.size();
    }
    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @param index The index of the element to return.
     * @return The protoThreatTag at the given index.
     */
    public String getProtoThreatTag(int index) {
      return protoThreatTag_.get(index);
    }
    /**
     * <pre>
     * 威胁标签	"HTTP隐蔽隧道"
     * </pre>
     *
     * <code>repeated string proto_threat_tag = 11;</code>
     * @param index The index of the value to return.
     * @return The bytes of the protoThreatTag at the given index.
     */
    public com.google.protobuf.ByteString
        getProtoThreatTagBytes(int index) {
      return protoThreatTag_.getByteString(index);
    }

    public static final int PROTO_VICTIM_HOST_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile Object protoVictimHost_ = "";
    /**
     * <pre>
     * 受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     * @return Whether the protoVictimHost field is set.
     */
    @Override
    public boolean hasProtoVictimHost() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     * @return The protoVictimHost.
     */
    @Override
    public String getProtoVictimHost() {
      Object ref = protoVictimHost_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoVictimHost_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 受害者HTTP域名	
     * </pre>
     *
     * <code>optional string proto_victim_host = 12;</code>
     * @return The bytes for protoVictimHost.
     */
    @Override
    public com.google.protobuf.ByteString
        getProtoVictimHostBytes() {
      Object ref = protoVictimHost_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        protoVictimHost_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_VICTIM_SNI_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile Object protoVictimSni_ = "";
    /**
     * <pre>
     * 受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     * @return Whether the protoVictimSni field is set.
     */
    @Override
    public boolean hasProtoVictimSni() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     * @return The protoVictimSni.
     */
    @Override
    public String getProtoVictimSni() {
      Object ref = protoVictimSni_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoVictimSni_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 受害者SNI域名	
     * </pre>
     *
     * <code>optional string proto_victim_sni = 13;</code>
     * @return The bytes for protoVictimSni.
     */
    @Override
    public com.google.protobuf.ByteString
        getProtoVictimSniBytes() {
      Object ref = protoVictimSni_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        protoVictimSni_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROTO_ALARM_HANDLE_METHOD_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile Object protoAlarmHandleMethod_ = "";
    /**
     * <pre>
     * 处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     * @return Whether the protoAlarmHandleMethod field is set.
     */
    @Override
    public boolean hasProtoAlarmHandleMethod() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     * @return The protoAlarmHandleMethod.
     */
    @Override
    public String getProtoAlarmHandleMethod() {
      Object ref = protoAlarmHandleMethod_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          protoAlarmHandleMethod_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 处置方法	
     * </pre>
     *
     * <code>optional string proto_alarm_handle_method = 14;</code>
     * @return The bytes for protoAlarmHandleMethod.
     */
    @Override
    public com.google.protobuf.ByteString
        getProtoAlarmHandleMethodBytes() {
      Object ref = protoAlarmHandleMethod_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        protoAlarmHandleMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, protoAlarmName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, protoModelName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, protoAttackLevel_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, protoAlarmPrinciple_);
      }
      for (int i = 0; i < protoAlarmReasonKey_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, protoAlarmReasonKey_.getRaw(i));
      }
      for (int i = 0; i < protoAlarmReasonActualValue_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, protoAlarmReasonActualValue_.getRaw(i));
      }
      for (int i = 0; i < protoTargetsName_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 7, protoTargetsName_.getRaw(i));
      }
      for (int i = 0; i < protoTargetsType_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, protoTargetsType_.getRaw(i));
      }
      for (int i = 0; i < protoMaliousFamilyType_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, protoMaliousFamilyType_.getRaw(i));
      }
      for (int i = 0; i < protoMaliousFamilyName_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, protoMaliousFamilyName_.getRaw(i));
      }
      for (int i = 0; i < protoThreatTag_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, protoThreatTag_.getRaw(i));
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, protoVictimHost_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 13, protoVictimSni_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 14, protoAlarmHandleMethod_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, protoAlarmName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, protoModelName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, protoAttackLevel_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, protoAlarmPrinciple_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoAlarmReasonKey_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoAlarmReasonKey_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoAlarmReasonKeyList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoAlarmReasonActualValue_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoAlarmReasonActualValue_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoAlarmReasonActualValueList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoTargetsName_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoTargetsName_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoTargetsNameList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoTargetsType_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoTargetsType_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoTargetsTypeList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoMaliousFamilyType_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoMaliousFamilyType_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoMaliousFamilyTypeList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoMaliousFamilyName_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoMaliousFamilyName_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoMaliousFamilyNameList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < protoThreatTag_.size(); i++) {
          dataSize += computeStringSizeNoTag(protoThreatTag_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getProtoThreatTagList().size();
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, protoVictimHost_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(13, protoVictimSni_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(14, protoAlarmHandleMethod_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof PROTO_ALERT_INFO)) {
        return super.equals(obj);
      }
      PROTO_ALERT_INFO other = (PROTO_ALERT_INFO) obj;

      if (hasProtoAlarmName() != other.hasProtoAlarmName()) return false;
      if (hasProtoAlarmName()) {
        if (!getProtoAlarmName()
            .equals(other.getProtoAlarmName())) return false;
      }
      if (hasProtoModelName() != other.hasProtoModelName()) return false;
      if (hasProtoModelName()) {
        if (!getProtoModelName()
            .equals(other.getProtoModelName())) return false;
      }
      if (hasProtoAttackLevel() != other.hasProtoAttackLevel()) return false;
      if (hasProtoAttackLevel()) {
        if (getProtoAttackLevel()
            != other.getProtoAttackLevel()) return false;
      }
      if (hasProtoAlarmPrinciple() != other.hasProtoAlarmPrinciple()) return false;
      if (hasProtoAlarmPrinciple()) {
        if (!getProtoAlarmPrinciple()
            .equals(other.getProtoAlarmPrinciple())) return false;
      }
      if (!getProtoAlarmReasonKeyList()
          .equals(other.getProtoAlarmReasonKeyList())) return false;
      if (!getProtoAlarmReasonActualValueList()
          .equals(other.getProtoAlarmReasonActualValueList())) return false;
      if (!getProtoTargetsNameList()
          .equals(other.getProtoTargetsNameList())) return false;
      if (!getProtoTargetsTypeList()
          .equals(other.getProtoTargetsTypeList())) return false;
      if (!getProtoMaliousFamilyTypeList()
          .equals(other.getProtoMaliousFamilyTypeList())) return false;
      if (!getProtoMaliousFamilyNameList()
          .equals(other.getProtoMaliousFamilyNameList())) return false;
      if (!getProtoThreatTagList()
          .equals(other.getProtoThreatTagList())) return false;
      if (hasProtoVictimHost() != other.hasProtoVictimHost()) return false;
      if (hasProtoVictimHost()) {
        if (!getProtoVictimHost()
            .equals(other.getProtoVictimHost())) return false;
      }
      if (hasProtoVictimSni() != other.hasProtoVictimSni()) return false;
      if (hasProtoVictimSni()) {
        if (!getProtoVictimSni()
            .equals(other.getProtoVictimSni())) return false;
      }
      if (hasProtoAlarmHandleMethod() != other.hasProtoAlarmHandleMethod()) return false;
      if (hasProtoAlarmHandleMethod()) {
        if (!getProtoAlarmHandleMethod()
            .equals(other.getProtoAlarmHandleMethod())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasProtoAlarmName()) {
        hash = (37 * hash) + PROTO_ALARM_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmName().hashCode();
      }
      if (hasProtoModelName()) {
        hash = (37 * hash) + PROTO_MODEL_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoModelName().hashCode();
      }
      if (hasProtoAttackLevel()) {
        hash = (37 * hash) + PROTO_ATTACK_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAttackLevel();
      }
      if (hasProtoAlarmPrinciple()) {
        hash = (37 * hash) + PROTO_ALARM_PRINCIPLE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmPrinciple().hashCode();
      }
      if (getProtoAlarmReasonKeyCount() > 0) {
        hash = (37 * hash) + PROTO_ALARM_REASON_KEY_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmReasonKeyList().hashCode();
      }
      if (getProtoAlarmReasonActualValueCount() > 0) {
        hash = (37 * hash) + PROTO_ALARM_REASON_ACTUAL_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmReasonActualValueList().hashCode();
      }
      if (getProtoTargetsNameCount() > 0) {
        hash = (37 * hash) + PROTO_TARGETS_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoTargetsNameList().hashCode();
      }
      if (getProtoTargetsTypeCount() > 0) {
        hash = (37 * hash) + PROTO_TARGETS_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoTargetsTypeList().hashCode();
      }
      if (getProtoMaliousFamilyTypeCount() > 0) {
        hash = (37 * hash) + PROTO_MALIOUS_FAMILY_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getProtoMaliousFamilyTypeList().hashCode();
      }
      if (getProtoMaliousFamilyNameCount() > 0) {
        hash = (37 * hash) + PROTO_MALIOUS_FAMILY_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getProtoMaliousFamilyNameList().hashCode();
      }
      if (getProtoThreatTagCount() > 0) {
        hash = (37 * hash) + PROTO_THREAT_TAG_FIELD_NUMBER;
        hash = (53 * hash) + getProtoThreatTagList().hashCode();
      }
      if (hasProtoVictimHost()) {
        hash = (37 * hash) + PROTO_VICTIM_HOST_FIELD_NUMBER;
        hash = (53 * hash) + getProtoVictimHost().hashCode();
      }
      if (hasProtoVictimSni()) {
        hash = (37 * hash) + PROTO_VICTIM_SNI_FIELD_NUMBER;
        hash = (53 * hash) + getProtoVictimSni().hashCode();
      }
      if (hasProtoAlarmHandleMethod()) {
        hash = (37 * hash) + PROTO_ALARM_HANDLE_METHOD_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlarmHandleMethod().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static PROTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PROTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PROTO_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static PROTO_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static PROTO_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static PROTO_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static PROTO_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static PROTO_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static PROTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(PROTO_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 特色协议威胁告警信息
     * </pre>
     *
     * Protobuf type {@code PROTO_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:PROTO_ALERT_INFO)
        PROTO_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                PROTO_ALERT_INFO.class, Builder.class);
      }

      // Construct using ProtoAlertInfo.PROTO_ALERT_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          BuilderParent parent) {
        super(parent);

      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        protoAlarmName_ = "";
        protoModelName_ = "";
        protoAttackLevel_ = 0;
        protoAlarmPrinciple_ = "";
        protoAlarmReasonKey_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        protoAlarmReasonActualValue_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        protoTargetsName_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        protoTargetsType_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        protoMaliousFamilyType_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        protoMaliousFamilyName_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        protoThreatTag_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        protoVictimHost_ = "";
        protoVictimSni_ = "";
        protoAlarmHandleMethod_ = "";
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return ProtoAlertInfo.internal_static_PROTO_ALERT_INFO_descriptor;
      }

      @Override
      public PROTO_ALERT_INFO getDefaultInstanceForType() {
        return PROTO_ALERT_INFO.getDefaultInstance();
      }

      @Override
      public PROTO_ALERT_INFO build() {
        PROTO_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public PROTO_ALERT_INFO buildPartial() {
        PROTO_ALERT_INFO result = new PROTO_ALERT_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(PROTO_ALERT_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.protoAlarmName_ = protoAlarmName_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.protoModelName_ = protoModelName_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.protoAttackLevel_ = protoAttackLevel_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.protoAlarmPrinciple_ = protoAlarmPrinciple_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          protoAlarmReasonKey_.makeImmutable();
          result.protoAlarmReasonKey_ = protoAlarmReasonKey_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          protoAlarmReasonActualValue_.makeImmutable();
          result.protoAlarmReasonActualValue_ = protoAlarmReasonActualValue_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          protoTargetsName_.makeImmutable();
          result.protoTargetsName_ = protoTargetsName_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          protoTargetsType_.makeImmutable();
          result.protoTargetsType_ = protoTargetsType_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          protoMaliousFamilyType_.makeImmutable();
          result.protoMaliousFamilyType_ = protoMaliousFamilyType_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          protoMaliousFamilyName_.makeImmutable();
          result.protoMaliousFamilyName_ = protoMaliousFamilyName_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          protoThreatTag_.makeImmutable();
          result.protoThreatTag_ = protoThreatTag_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.protoVictimHost_ = protoVictimHost_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.protoVictimSni_ = protoVictimSni_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.protoAlarmHandleMethod_ = protoAlarmHandleMethod_;
          to_bitField0_ |= 0x00000040;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof PROTO_ALERT_INFO) {
          return mergeFrom((PROTO_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(PROTO_ALERT_INFO other) {
        if (other == PROTO_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasProtoAlarmName()) {
          protoAlarmName_ = other.protoAlarmName_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasProtoModelName()) {
          protoModelName_ = other.protoModelName_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasProtoAttackLevel()) {
          setProtoAttackLevel(other.getProtoAttackLevel());
        }
        if (other.hasProtoAlarmPrinciple()) {
          protoAlarmPrinciple_ = other.protoAlarmPrinciple_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (!other.protoAlarmReasonKey_.isEmpty()) {
          if (protoAlarmReasonKey_.isEmpty()) {
            protoAlarmReasonKey_ = other.protoAlarmReasonKey_;
            bitField0_ |= 0x00000010;
          } else {
            ensureProtoAlarmReasonKeyIsMutable();
            protoAlarmReasonKey_.addAll(other.protoAlarmReasonKey_);
          }
          onChanged();
        }
        if (!other.protoAlarmReasonActualValue_.isEmpty()) {
          if (protoAlarmReasonActualValue_.isEmpty()) {
            protoAlarmReasonActualValue_ = other.protoAlarmReasonActualValue_;
            bitField0_ |= 0x00000020;
          } else {
            ensureProtoAlarmReasonActualValueIsMutable();
            protoAlarmReasonActualValue_.addAll(other.protoAlarmReasonActualValue_);
          }
          onChanged();
        }
        if (!other.protoTargetsName_.isEmpty()) {
          if (protoTargetsName_.isEmpty()) {
            protoTargetsName_ = other.protoTargetsName_;
            bitField0_ |= 0x00000040;
          } else {
            ensureProtoTargetsNameIsMutable();
            protoTargetsName_.addAll(other.protoTargetsName_);
          }
          onChanged();
        }
        if (!other.protoTargetsType_.isEmpty()) {
          if (protoTargetsType_.isEmpty()) {
            protoTargetsType_ = other.protoTargetsType_;
            bitField0_ |= 0x00000080;
          } else {
            ensureProtoTargetsTypeIsMutable();
            protoTargetsType_.addAll(other.protoTargetsType_);
          }
          onChanged();
        }
        if (!other.protoMaliousFamilyType_.isEmpty()) {
          if (protoMaliousFamilyType_.isEmpty()) {
            protoMaliousFamilyType_ = other.protoMaliousFamilyType_;
            bitField0_ |= 0x00000100;
          } else {
            ensureProtoMaliousFamilyTypeIsMutable();
            protoMaliousFamilyType_.addAll(other.protoMaliousFamilyType_);
          }
          onChanged();
        }
        if (!other.protoMaliousFamilyName_.isEmpty()) {
          if (protoMaliousFamilyName_.isEmpty()) {
            protoMaliousFamilyName_ = other.protoMaliousFamilyName_;
            bitField0_ |= 0x00000200;
          } else {
            ensureProtoMaliousFamilyNameIsMutable();
            protoMaliousFamilyName_.addAll(other.protoMaliousFamilyName_);
          }
          onChanged();
        }
        if (!other.protoThreatTag_.isEmpty()) {
          if (protoThreatTag_.isEmpty()) {
            protoThreatTag_ = other.protoThreatTag_;
            bitField0_ |= 0x00000400;
          } else {
            ensureProtoThreatTagIsMutable();
            protoThreatTag_.addAll(other.protoThreatTag_);
          }
          onChanged();
        }
        if (other.hasProtoVictimHost()) {
          protoVictimHost_ = other.protoVictimHost_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (other.hasProtoVictimSni()) {
          protoVictimSni_ = other.protoVictimSni_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (other.hasProtoAlarmHandleMethod()) {
          protoAlarmHandleMethod_ = other.protoAlarmHandleMethod_;
          bitField0_ |= 0x00002000;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                protoAlarmName_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                protoModelName_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                protoAttackLevel_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                protoAlarmPrinciple_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureProtoAlarmReasonKeyIsMutable();
                protoAlarmReasonKey_.add(bs);
                break;
              } // case 42
              case 50: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureProtoAlarmReasonActualValueIsMutable();
                protoAlarmReasonActualValue_.add(bs);
                break;
              } // case 50
              case 58: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureProtoTargetsNameIsMutable();
                protoTargetsName_.add(bs);
                break;
              } // case 58
              case 66: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureProtoTargetsTypeIsMutable();
                protoTargetsType_.add(bs);
                break;
              } // case 66
              case 74: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureProtoMaliousFamilyTypeIsMutable();
                protoMaliousFamilyType_.add(bs);
                break;
              } // case 74
              case 82: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureProtoMaliousFamilyNameIsMutable();
                protoMaliousFamilyName_.add(bs);
                break;
              } // case 82
              case 90: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureProtoThreatTagIsMutable();
                protoThreatTag_.add(bs);
                break;
              } // case 90
              case 98: {
                protoVictimHost_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                protoVictimSni_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                protoAlarmHandleMethod_ = input.readBytes();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object protoAlarmName_ = "";
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       * @return Whether the protoAlarmName field is set.
       */
      public boolean hasProtoAlarmName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       * @return The protoAlarmName.
       */
      public java.lang.String getProtoAlarmName() {
        java.lang.Object ref = protoAlarmName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoAlarmName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       * @return The bytes for protoAlarmName.
       */
      public com.google.protobuf.ByteString
          getProtoAlarmNameBytes() {
        java.lang.Object ref = protoAlarmName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoAlarmName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       * @param value The protoAlarmName to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        protoAlarmName_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoAlarmName() {
        protoAlarmName_ = getDefaultInstance().getProtoAlarmName();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>optional string proto_alarm_name = 1;</code>
       * @param value The bytes for protoAlarmName to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        protoAlarmName_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object protoModelName_ = "";
      /**
       * <pre>
       * 检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       * @return Whether the protoModelName field is set.
       */
      public boolean hasProtoModelName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       * @return The protoModelName.
       */
      public java.lang.String getProtoModelName() {
        java.lang.Object ref = protoModelName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoModelName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       * @return The bytes for protoModelName.
       */
      public com.google.protobuf.ByteString
          getProtoModelNameBytes() {
        java.lang.Object ref = protoModelName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoModelName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       * @param value The protoModelName to set.
       * @return This builder for chaining.
       */
      public Builder setProtoModelName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        protoModelName_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoModelName() {
        protoModelName_ = getDefaultInstance().getProtoModelName();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 检测模型	
       * </pre>
       *
       * <code>optional string proto_model_name = 2;</code>
       * @param value The bytes for protoModelName to set.
       * @return This builder for chaining.
       */
      public Builder setProtoModelNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        protoModelName_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private int protoAttackLevel_ ;
      /**
       * <pre>
       * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       * @return Whether the protoAttackLevel field is set.
       */
      @java.lang.Override
      public boolean hasProtoAttackLevel() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       * @return The protoAttackLevel.
       */
      @java.lang.Override
      public int getProtoAttackLevel() {
        return protoAttackLevel_;
      }
      /**
       * <pre>
       * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       * @param value The protoAttackLevel to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAttackLevel(int value) {

        protoAttackLevel_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁评分	低危:61-80/中危:81-90 /高危:91-100 
       * </pre>
       *
       * <code>optional uint32 proto_attack_level = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoAttackLevel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        protoAttackLevel_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object protoAlarmPrinciple_ = "";
      /**
       * <pre>
       * 检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       * @return Whether the protoAlarmPrinciple field is set.
       */
      public boolean hasProtoAlarmPrinciple() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       * @return The protoAlarmPrinciple.
       */
      public java.lang.String getProtoAlarmPrinciple() {
        java.lang.Object ref = protoAlarmPrinciple_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoAlarmPrinciple_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       * @return The bytes for protoAlarmPrinciple.
       */
      public com.google.protobuf.ByteString
          getProtoAlarmPrincipleBytes() {
        java.lang.Object ref = protoAlarmPrinciple_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoAlarmPrinciple_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       * @param value The protoAlarmPrinciple to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmPrinciple(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        protoAlarmPrinciple_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoAlarmPrinciple() {
        protoAlarmPrinciple_ = getDefaultInstance().getProtoAlarmPrinciple();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 检测原理	
       * </pre>
       *
       * <code>optional string proto_alarm_principle = 4;</code>
       * @param value The bytes for protoAlarmPrinciple to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmPrincipleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        protoAlarmPrinciple_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList protoAlarmReasonKey_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureProtoAlarmReasonKeyIsMutable() {
        if (!protoAlarmReasonKey_.isModifiable()) {
          protoAlarmReasonKey_ = new com.google.protobuf.LazyStringArrayList(protoAlarmReasonKey_);
        }
        bitField0_ |= 0x00000010;
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @return A list containing the protoAlarmReasonKey.
       */
      public com.google.protobuf.ProtocolStringList
          getProtoAlarmReasonKeyList() {
        protoAlarmReasonKey_.makeImmutable();
        return protoAlarmReasonKey_;
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @return The count of protoAlarmReasonKey.
       */
      public int getProtoAlarmReasonKeyCount() {
        return protoAlarmReasonKey_.size();
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @param index The index of the element to return.
       * @return The protoAlarmReasonKey at the given index.
       */
      public java.lang.String getProtoAlarmReasonKey(int index) {
        return protoAlarmReasonKey_.get(index);
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @param index The index of the value to return.
       * @return The bytes of the protoAlarmReasonKey at the given index.
       */
      public com.google.protobuf.ByteString
          getProtoAlarmReasonKeyBytes(int index) {
        return protoAlarmReasonKey_.getByteString(index);
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @param index The index to set the value at.
       * @param value The protoAlarmReasonKey to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmReasonKey(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoAlarmReasonKeyIsMutable();
        protoAlarmReasonKey_.set(index, value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @param value The protoAlarmReasonKey to add.
       * @return This builder for chaining.
       */
      public Builder addProtoAlarmReasonKey(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoAlarmReasonKeyIsMutable();
        protoAlarmReasonKey_.add(value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @param values The protoAlarmReasonKey to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtoAlarmReasonKey(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoAlarmReasonKeyIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoAlarmReasonKey_);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoAlarmReasonKey() {
        protoAlarmReasonKey_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000010);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警匹配特征	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_key = 5;</code>
       * @param value The bytes of the protoAlarmReasonKey to add.
       * @return This builder for chaining.
       */
      public Builder addProtoAlarmReasonKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoAlarmReasonKeyIsMutable();
        protoAlarmReasonKey_.add(value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList protoAlarmReasonActualValue_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureProtoAlarmReasonActualValueIsMutable() {
        if (!protoAlarmReasonActualValue_.isModifiable()) {
          protoAlarmReasonActualValue_ = new com.google.protobuf.LazyStringArrayList(protoAlarmReasonActualValue_);
        }
        bitField0_ |= 0x00000020;
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @return A list containing the protoAlarmReasonActualValue.
       */
      public com.google.protobuf.ProtocolStringList
          getProtoAlarmReasonActualValueList() {
        protoAlarmReasonActualValue_.makeImmutable();
        return protoAlarmReasonActualValue_;
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @return The count of protoAlarmReasonActualValue.
       */
      public int getProtoAlarmReasonActualValueCount() {
        return protoAlarmReasonActualValue_.size();
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @param index The index of the element to return.
       * @return The protoAlarmReasonActualValue at the given index.
       */
      public java.lang.String getProtoAlarmReasonActualValue(int index) {
        return protoAlarmReasonActualValue_.get(index);
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @param index The index of the value to return.
       * @return The bytes of the protoAlarmReasonActualValue at the given index.
       */
      public com.google.protobuf.ByteString
          getProtoAlarmReasonActualValueBytes(int index) {
        return protoAlarmReasonActualValue_.getByteString(index);
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @param index The index to set the value at.
       * @param value The protoAlarmReasonActualValue to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmReasonActualValue(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoAlarmReasonActualValueIsMutable();
        protoAlarmReasonActualValue_.set(index, value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @param value The protoAlarmReasonActualValue to add.
       * @return This builder for chaining.
       */
      public Builder addProtoAlarmReasonActualValue(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoAlarmReasonActualValueIsMutable();
        protoAlarmReasonActualValue_.add(value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @param values The protoAlarmReasonActualValue to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtoAlarmReasonActualValue(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoAlarmReasonActualValueIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoAlarmReasonActualValue_);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoAlarmReasonActualValue() {
        protoAlarmReasonActualValue_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000020);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警特征命中情况	
       * </pre>
       *
       * <code>repeated string proto_alarm_reason_actual_value = 6;</code>
       * @param value The bytes of the protoAlarmReasonActualValue to add.
       * @return This builder for chaining.
       */
      public Builder addProtoAlarmReasonActualValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoAlarmReasonActualValueIsMutable();
        protoAlarmReasonActualValue_.add(value);
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList protoTargetsName_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureProtoTargetsNameIsMutable() {
        if (!protoTargetsName_.isModifiable()) {
          protoTargetsName_ = new com.google.protobuf.LazyStringArrayList(protoTargetsName_);
        }
        bitField0_ |= 0x00000040;
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @return A list containing the protoTargetsName.
       */
      public com.google.protobuf.ProtocolStringList
          getProtoTargetsNameList() {
        protoTargetsName_.makeImmutable();
        return protoTargetsName_;
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @return The count of protoTargetsName.
       */
      public int getProtoTargetsNameCount() {
        return protoTargetsName_.size();
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @param index The index of the element to return.
       * @return The protoTargetsName at the given index.
       */
      public java.lang.String getProtoTargetsName(int index) {
        return protoTargetsName_.get(index);
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @param index The index of the value to return.
       * @return The bytes of the protoTargetsName at the given index.
       */
      public com.google.protobuf.ByteString
          getProtoTargetsNameBytes(int index) {
        return protoTargetsName_.getByteString(index);
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @param index The index to set the value at.
       * @param value The protoTargetsName to set.
       * @return This builder for chaining.
       */
      public Builder setProtoTargetsName(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoTargetsNameIsMutable();
        protoTargetsName_.set(index, value);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @param value The protoTargetsName to add.
       * @return This builder for chaining.
       */
      public Builder addProtoTargetsName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoTargetsNameIsMutable();
        protoTargetsName_.add(value);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @param values The protoTargetsName to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtoTargetsName(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoTargetsNameIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoTargetsName_);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoTargetsName() {
        protoTargetsName_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000040);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象名称	Web漏洞利用/漏洞扫描/后门利用
       * </pre>
       *
       * <code>repeated string proto_targets_name = 7;</code>
       * @param value The bytes of the protoTargetsName to add.
       * @return This builder for chaining.
       */
      public Builder addProtoTargetsNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoTargetsNameIsMutable();
        protoTargetsName_.add(value);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList protoTargetsType_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureProtoTargetsTypeIsMutable() {
        if (!protoTargetsType_.isModifiable()) {
          protoTargetsType_ = new com.google.protobuf.LazyStringArrayList(protoTargetsType_);
        }
        bitField0_ |= 0x00000080;
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @return A list containing the protoTargetsType.
       */
      public com.google.protobuf.ProtocolStringList
          getProtoTargetsTypeList() {
        protoTargetsType_.makeImmutable();
        return protoTargetsType_;
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @return The count of protoTargetsType.
       */
      public int getProtoTargetsTypeCount() {
        return protoTargetsType_.size();
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @param index The index of the element to return.
       * @return The protoTargetsType at the given index.
       */
      public java.lang.String getProtoTargetsType(int index) {
        return protoTargetsType_.get(index);
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @param index The index of the value to return.
       * @return The bytes of the protoTargetsType at the given index.
       */
      public com.google.protobuf.ByteString
          getProtoTargetsTypeBytes(int index) {
        return protoTargetsType_.getByteString(index);
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @param index The index to set the value at.
       * @param value The protoTargetsType to set.
       * @return This builder for chaining.
       */
      public Builder setProtoTargetsType(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoTargetsTypeIsMutable();
        protoTargetsType_.set(index, value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @param value The protoTargetsType to add.
       * @return This builder for chaining.
       */
      public Builder addProtoTargetsType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoTargetsTypeIsMutable();
        protoTargetsType_.add(value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @param values The protoTargetsType to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtoTargetsType(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoTargetsTypeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoTargetsType_);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoTargetsType() {
        protoTargetsType_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警对象类型	session/ip/domain/cert/finger
       * </pre>
       *
       * <code>repeated string proto_targets_type = 8;</code>
       * @param value The bytes of the protoTargetsType to add.
       * @return This builder for chaining.
       */
      public Builder addProtoTargetsTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoTargetsTypeIsMutable();
        protoTargetsType_.add(value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList protoMaliousFamilyType_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureProtoMaliousFamilyTypeIsMutable() {
        if (!protoMaliousFamilyType_.isModifiable()) {
          protoMaliousFamilyType_ = new com.google.protobuf.LazyStringArrayList(protoMaliousFamilyType_);
        }
        bitField0_ |= 0x00000100;
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @return A list containing the protoMaliousFamilyType.
       */
      public com.google.protobuf.ProtocolStringList
          getProtoMaliousFamilyTypeList() {
        protoMaliousFamilyType_.makeImmutable();
        return protoMaliousFamilyType_;
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @return The count of protoMaliousFamilyType.
       */
      public int getProtoMaliousFamilyTypeCount() {
        return protoMaliousFamilyType_.size();
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @param index The index of the element to return.
       * @return The protoMaliousFamilyType at the given index.
       */
      public java.lang.String getProtoMaliousFamilyType(int index) {
        return protoMaliousFamilyType_.get(index);
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @param index The index of the value to return.
       * @return The bytes of the protoMaliousFamilyType at the given index.
       */
      public com.google.protobuf.ByteString
          getProtoMaliousFamilyTypeBytes(int index) {
        return protoMaliousFamilyType_.getByteString(index);
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @param index The index to set the value at.
       * @param value The protoMaliousFamilyType to set.
       * @return This builder for chaining.
       */
      public Builder setProtoMaliousFamilyType(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoMaliousFamilyTypeIsMutable();
        protoMaliousFamilyType_.set(index, value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @param value The protoMaliousFamilyType to add.
       * @return This builder for chaining.
       */
      public Builder addProtoMaliousFamilyType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoMaliousFamilyTypeIsMutable();
        protoMaliousFamilyType_.add(value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @param values The protoMaliousFamilyType to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtoMaliousFamilyType(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoMaliousFamilyTypeIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoMaliousFamilyType_);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoMaliousFamilyType() {
        protoMaliousFamilyType_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族类型	恶意软件/APT组织
       * </pre>
       *
       * <code>repeated string proto_malious_family_type = 9;</code>
       * @param value The bytes of the protoMaliousFamilyType to add.
       * @return This builder for chaining.
       */
      public Builder addProtoMaliousFamilyTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoMaliousFamilyTypeIsMutable();
        protoMaliousFamilyType_.add(value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList protoMaliousFamilyName_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureProtoMaliousFamilyNameIsMutable() {
        if (!protoMaliousFamilyName_.isModifiable()) {
          protoMaliousFamilyName_ = new com.google.protobuf.LazyStringArrayList(protoMaliousFamilyName_);
        }
        bitField0_ |= 0x00000200;
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @return A list containing the protoMaliousFamilyName.
       */
      public com.google.protobuf.ProtocolStringList
          getProtoMaliousFamilyNameList() {
        protoMaliousFamilyName_.makeImmutable();
        return protoMaliousFamilyName_;
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @return The count of protoMaliousFamilyName.
       */
      public int getProtoMaliousFamilyNameCount() {
        return protoMaliousFamilyName_.size();
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @param index The index of the element to return.
       * @return The protoMaliousFamilyName at the given index.
       */
      public java.lang.String getProtoMaliousFamilyName(int index) {
        return protoMaliousFamilyName_.get(index);
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @param index The index of the value to return.
       * @return The bytes of the protoMaliousFamilyName at the given index.
       */
      public com.google.protobuf.ByteString
          getProtoMaliousFamilyNameBytes(int index) {
        return protoMaliousFamilyName_.getByteString(index);
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @param index The index to set the value at.
       * @param value The protoMaliousFamilyName to set.
       * @return This builder for chaining.
       */
      public Builder setProtoMaliousFamilyName(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoMaliousFamilyNameIsMutable();
        protoMaliousFamilyName_.set(index, value);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @param value The protoMaliousFamilyName to add.
       * @return This builder for chaining.
       */
      public Builder addProtoMaliousFamilyName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoMaliousFamilyNameIsMutable();
        protoMaliousFamilyName_.add(value);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @param values The protoMaliousFamilyName to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtoMaliousFamilyName(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoMaliousFamilyNameIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoMaliousFamilyName_);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoMaliousFamilyName() {
        protoMaliousFamilyName_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意家族名称	apt29/apt32
       * </pre>
       *
       * <code>repeated string proto_malious_family_name = 10;</code>
       * @param value The bytes of the protoMaliousFamilyName to add.
       * @return This builder for chaining.
       */
      public Builder addProtoMaliousFamilyNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoMaliousFamilyNameIsMutable();
        protoMaliousFamilyName_.add(value);
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList protoThreatTag_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureProtoThreatTagIsMutable() {
        if (!protoThreatTag_.isModifiable()) {
          protoThreatTag_ = new com.google.protobuf.LazyStringArrayList(protoThreatTag_);
        }
        bitField0_ |= 0x00000400;
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @return A list containing the protoThreatTag.
       */
      public com.google.protobuf.ProtocolStringList
          getProtoThreatTagList() {
        protoThreatTag_.makeImmutable();
        return protoThreatTag_;
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @return The count of protoThreatTag.
       */
      public int getProtoThreatTagCount() {
        return protoThreatTag_.size();
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @param index The index of the element to return.
       * @return The protoThreatTag at the given index.
       */
      public java.lang.String getProtoThreatTag(int index) {
        return protoThreatTag_.get(index);
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @param index The index of the value to return.
       * @return The bytes of the protoThreatTag at the given index.
       */
      public com.google.protobuf.ByteString
          getProtoThreatTagBytes(int index) {
        return protoThreatTag_.getByteString(index);
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @param index The index to set the value at.
       * @param value The protoThreatTag to set.
       * @return This builder for chaining.
       */
      public Builder setProtoThreatTag(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoThreatTagIsMutable();
        protoThreatTag_.set(index, value);
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @param value The protoThreatTag to add.
       * @return This builder for chaining.
       */
      public Builder addProtoThreatTag(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoThreatTagIsMutable();
        protoThreatTag_.add(value);
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @param values The protoThreatTag to add.
       * @return This builder for chaining.
       */
      public Builder addAllProtoThreatTag(
          java.lang.Iterable<java.lang.String> values) {
        ensureProtoThreatTagIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, protoThreatTag_);
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoThreatTag() {
        protoThreatTag_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000400);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁标签	"HTTP隐蔽隧道"
       * </pre>
       *
       * <code>repeated string proto_threat_tag = 11;</code>
       * @param value The bytes of the protoThreatTag to add.
       * @return This builder for chaining.
       */
      public Builder addProtoThreatTagBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureProtoThreatTagIsMutable();
        protoThreatTag_.add(value);
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object protoVictimHost_ = "";
      /**
       * <pre>
       * 受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       * @return Whether the protoVictimHost field is set.
       */
      public boolean hasProtoVictimHost() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       * @return The protoVictimHost.
       */
      public java.lang.String getProtoVictimHost() {
        java.lang.Object ref = protoVictimHost_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoVictimHost_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       * @return The bytes for protoVictimHost.
       */
      public com.google.protobuf.ByteString
          getProtoVictimHostBytes() {
        java.lang.Object ref = protoVictimHost_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoVictimHost_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       * @param value The protoVictimHost to set.
       * @return This builder for chaining.
       */
      public Builder setProtoVictimHost(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        protoVictimHost_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoVictimHost() {
        protoVictimHost_ = getDefaultInstance().getProtoVictimHost();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 受害者HTTP域名	
       * </pre>
       *
       * <code>optional string proto_victim_host = 12;</code>
       * @param value The bytes for protoVictimHost to set.
       * @return This builder for chaining.
       */
      public Builder setProtoVictimHostBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        protoVictimHost_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object protoVictimSni_ = "";
      /**
       * <pre>
       * 受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       * @return Whether the protoVictimSni field is set.
       */
      public boolean hasProtoVictimSni() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       * @return The protoVictimSni.
       */
      public java.lang.String getProtoVictimSni() {
        java.lang.Object ref = protoVictimSni_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoVictimSni_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       * @return The bytes for protoVictimSni.
       */
      public com.google.protobuf.ByteString
          getProtoVictimSniBytes() {
        java.lang.Object ref = protoVictimSni_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoVictimSni_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       * @param value The protoVictimSni to set.
       * @return This builder for chaining.
       */
      public Builder setProtoVictimSni(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        protoVictimSni_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoVictimSni() {
        protoVictimSni_ = getDefaultInstance().getProtoVictimSni();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 受害者SNI域名	
       * </pre>
       *
       * <code>optional string proto_victim_sni = 13;</code>
       * @param value The bytes for protoVictimSni to set.
       * @return This builder for chaining.
       */
      public Builder setProtoVictimSniBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        protoVictimSni_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private java.lang.Object protoAlarmHandleMethod_ = "";
      /**
       * <pre>
       * 处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       * @return Whether the protoAlarmHandleMethod field is set.
       */
      public boolean hasProtoAlarmHandleMethod() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       * @return The protoAlarmHandleMethod.
       */
      public java.lang.String getProtoAlarmHandleMethod() {
        java.lang.Object ref = protoAlarmHandleMethod_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            protoAlarmHandleMethod_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       * @return The bytes for protoAlarmHandleMethod.
       */
      public com.google.protobuf.ByteString
          getProtoAlarmHandleMethodBytes() {
        java.lang.Object ref = protoAlarmHandleMethod_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          protoAlarmHandleMethod_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       * @param value The protoAlarmHandleMethod to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmHandleMethod(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        protoAlarmHandleMethod_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtoAlarmHandleMethod() {
        protoAlarmHandleMethod_ = getDefaultInstance().getProtoAlarmHandleMethod();
        bitField0_ = (bitField0_ & ~0x00002000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 处置方法	
       * </pre>
       *
       * <code>optional string proto_alarm_handle_method = 14;</code>
       * @param value The bytes for protoAlarmHandleMethod to set.
       * @return This builder for chaining.
       */
      public Builder setProtoAlarmHandleMethodBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        protoAlarmHandleMethod_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:PROTO_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:PROTO_ALERT_INFO)
    private static final ProtoAlertInfo.PROTO_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ProtoAlertInfo.PROTO_ALERT_INFO();
    }

    public static ProtoAlertInfo.PROTO_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<PROTO_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<PROTO_ALERT_INFO>() {
      @java.lang.Override
      public PROTO_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<PROTO_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PROTO_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public ProtoAlertInfo.PROTO_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_PROTO_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_PROTO_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\026PROTO_ALERT_INFO.proto\"\272\003\n\020PROTO_ALERT" +
      "_INFO\022\030\n\020proto_alarm_name\030\001 \001(\t\022\030\n\020proto" +
      "_model_name\030\002 \001(\t\022\032\n\022proto_attack_level\030" +
      "\003 \001(\r\022\035\n\025proto_alarm_principle\030\004 \001(\t\022\036\n\026" +
      "proto_alarm_reason_key\030\005 \003(\t\022\'\n\037proto_al" +
      "arm_reason_actual_value\030\006 \003(\t\022\032\n\022proto_t" +
      "argets_name\030\007 \003(\t\022\032\n\022proto_targets_type\030" +
      "\010 \003(\t\022!\n\031proto_malious_family_type\030\t \003(\t" +
      "\022!\n\031proto_malious_family_name\030\n \003(\t\022\030\n\020p" +
      "roto_threat_tag\030\013 \003(\t\022\031\n\021proto_victim_ho" +
      "st\030\014 \001(\t\022\030\n\020proto_victim_sni\030\r \001(\t\022!\n\031pr" +
      "oto_alarm_handle_method\030\016 \001(\tB\020B\016ProtoAl" +
      "ertInfo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_PROTO_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_PROTO_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_PROTO_ALERT_INFO_descriptor,
        new java.lang.String[] { "ProtoAlarmName", "ProtoModelName", "ProtoAttackLevel", "ProtoAlarmPrinciple", "ProtoAlarmReasonKey", "ProtoAlarmReasonActualValue", "ProtoTargetsName", "ProtoTargetsType", "ProtoMaliousFamilyType", "ProtoMaliousFamilyName", "ProtoThreatTag", "ProtoVictimHost", "ProtoVictimSni", "ProtoAlarmHandleMethod", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
