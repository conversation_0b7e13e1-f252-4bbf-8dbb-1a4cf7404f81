package com.geeksec.transfer.function.process.protocol;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.HTTPAlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertHttpProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {

    private static final Logger logger = LoggerFactory.getLogger(AlertHttpProcessFunction.class);

    /**
    * 需要提取的点边关系
     * 点：
     * UA: ua_str,STRING,ua字符串;os_name,STRING,用操作系统;device_name,STRING,设备名称
     * URL: url_path,STRING,URL全路径信息
     * DOMAIN: domain_addr,STRING,域名地址
     * 边：
     * http_connect: Client_IP ---> Server_IP, http连接请求
     * client_use_ua: Client_IP ---> UA, 客户端使用UA
     * ua_connect_domain: UA ---> DOMAIN,UA访问域名
     * client_http_connect_domain: Client_IP ---> DOMAIN,客户端访问域名
     * server_http_connect_domain:  Server_IP ---> DOMAIN,服务端部署HTTP服务
     * client_http_connect_url: Client_IP ---> URL,客户端访问URL
    * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, Context ctx, Collector<Row> collector) {
        try {
            HTTPAlertTrans httpAlert = AlertTools.createHttpAlertTrans(alertLog);
            if (httpAlert != null) {
                List<Row> rows = httpAlert.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing Http alert log: {}", alertLog.getGuid(), e);
        }
    }
}