package com.geeksec.entity.pojo;

import com.geeksec.entity.trans.IPTrans;
import lombok.Data;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class IocAlertTrans {

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private String attackId;

    // 别名
    private String aptAlias;
    // 组织名称
    private String aptName;
    // 属于国家
    private String aptCountry;

    public List<Row> getAllRows() {

        // 统一录入IP相关节点&边信息
        List<Row> rows = new ArrayList<>();

        // APT_GROUP TAG
        if(!aptName.isEmpty()){
            Row aptTagRow = new Row(5);
            aptTagRow.setField(0, "TAG_APT_GROUP");
            aptTagRow.setField(1, aptName);
            aptTagRow.setField(2, aptAlias);
            aptTagRow.setField(3, "");
            aptTagRow.setField(4, aptCountry);
            rows.add(aptTagRow);

            // attack_belong_to_apt_group EDGE
            Row attackBelongToAptRow = new Row(4);
            attackBelongToAptRow.setField(0, "EDGE_attack_belong_to_apt_group");
            attackBelongToAptRow.setField(1, attackId);
            attackBelongToAptRow.setField(2, aptName);
            attackBelongToAptRow.setField(3, 0);
            rows.add(attackBelongToAptRow);

            // ip_belong_to_apt EDGE
            Row aipBelongToAptRow = new Row(4);
            aipBelongToAptRow.setField(0, "EDGE_ip_belong_to_apt");
            aipBelongToAptRow.setField(1, aipAddr);
            aipBelongToAptRow.setField(2, aptName);
            aipBelongToAptRow.setField(3, 0);
            rows.add(aipBelongToAptRow);
        }

        return rows;
    }
}

