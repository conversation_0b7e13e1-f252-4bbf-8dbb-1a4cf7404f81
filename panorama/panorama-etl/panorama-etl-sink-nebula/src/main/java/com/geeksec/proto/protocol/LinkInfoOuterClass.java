package com.geeksec.proto.protocol;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: LinkInfo.proto
// Protobuf Java Version: 4.29.4

public final class LinkInfoOuterClass {
  private LinkInfoOuterClass() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      LinkInfoOuterClass.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface LinkInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:LinkInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bytes portInfo = 1;</code>
     * @return Whether the portInfo field is set.
     */
    boolean hasPortInfo();
    /**
     * <code>optional bytes portInfo = 1;</code>
     * @return The portInfo.
     */
    com.google.protobuf.ByteString getPortInfo();

    /**
     * <code>optional bytes portInfoAtt = 2;</code>
     * @return Whether the portInfoAtt field is set.
     */
    boolean hasPortInfoAtt();
    /**
     * <code>optional bytes portInfoAtt = 2;</code>
     * @return The portInfoAtt.
     */
    com.google.protobuf.ByteString getPortInfoAtt();

    /**
     * <code>optional uint32 upPayLen = 3;</code>
     * @return Whether the upPayLen field is set.
     */
    boolean hasUpPayLen();
    /**
     * <code>optional uint32 upPayLen = 3;</code>
     * @return The upPayLen.
     */
    int getUpPayLen();

    /**
     * <code>optional uint64 downPayLen = 4;</code>
     * @return Whether the downPayLen field is set.
     */
    boolean hasDownPayLen();
    /**
     * <code>optional uint64 downPayLen = 4;</code>
     * @return The downPayLen.
     */
    long getDownPayLen();

    /**
     * <code>optional bytes tcpflag = 5;</code>
     * @return Whether the tcpflag field is set.
     */
    boolean hasTcpflag();
    /**
     * <code>optional bytes tcpflag = 5;</code>
     * @return The tcpflag.
     */
    com.google.protobuf.ByteString getTcpflag();

    /**
     * <code>optional uint64 upLinkPktNum = 6;</code>
     * @return Whether the upLinkPktNum field is set.
     */
    boolean hasUpLinkPktNum();
    /**
     * <code>optional uint64 upLinkPktNum = 6;</code>
     * @return The upLinkPktNum.
     */
    long getUpLinkPktNum();

    /**
     * <code>optional uint64 upLinkSize = 7;</code>
     * @return Whether the upLinkSize field is set.
     */
    boolean hasUpLinkSize();
    /**
     * <code>optional uint64 upLinkSize = 7;</code>
     * @return The upLinkSize.
     */
    long getUpLinkSize();

    /**
     * <code>optional uint32 upLinkBigPktLen = 8;</code>
     * @return Whether the upLinkBigPktLen field is set.
     */
    boolean hasUpLinkBigPktLen();
    /**
     * <code>optional uint32 upLinkBigPktLen = 8;</code>
     * @return The upLinkBigPktLen.
     */
    int getUpLinkBigPktLen();

    /**
     * <code>optional uint32 upLinkSmaPktLen = 9;</code>
     * @return Whether the upLinkSmaPktLen field is set.
     */
    boolean hasUpLinkSmaPktLen();
    /**
     * <code>optional uint32 upLinkSmaPktLen = 9;</code>
     * @return The upLinkSmaPktLen.
     */
    int getUpLinkSmaPktLen();

    /**
     * <code>optional uint64 upLinkBigPktInt = 10;</code>
     * @return Whether the upLinkBigPktInt field is set.
     */
    boolean hasUpLinkBigPktInt();
    /**
     * <code>optional uint64 upLinkBigPktInt = 10;</code>
     * @return The upLinkBigPktInt.
     */
    long getUpLinkBigPktInt();

    /**
     * <code>optional uint64 upLinkSmaPktInt = 11;</code>
     * @return Whether the upLinkSmaPktInt field is set.
     */
    boolean hasUpLinkSmaPktInt();
    /**
     * <code>optional uint64 upLinkSmaPktInt = 11;</code>
     * @return The upLinkSmaPktInt.
     */
    long getUpLinkSmaPktInt();

    /**
     * <code>optional uint32 downLinkPktNum = 12;</code>
     * @return Whether the downLinkPktNum field is set.
     */
    boolean hasDownLinkPktNum();
    /**
     * <code>optional uint32 downLinkPktNum = 12;</code>
     * @return The downLinkPktNum.
     */
    int getDownLinkPktNum();

    /**
     * <code>optional uint32 downLinkSize = 13;</code>
     * @return Whether the downLinkSize field is set.
     */
    boolean hasDownLinkSize();
    /**
     * <code>optional uint32 downLinkSize = 13;</code>
     * @return The downLinkSize.
     */
    int getDownLinkSize();

    /**
     * <code>optional uint32 downLinkBigPktLen = 14;</code>
     * @return Whether the downLinkBigPktLen field is set.
     */
    boolean hasDownLinkBigPktLen();
    /**
     * <code>optional uint32 downLinkBigPktLen = 14;</code>
     * @return The downLinkBigPktLen.
     */
    int getDownLinkBigPktLen();

    /**
     * <code>optional uint32 downLinkSmaPktLen = 15;</code>
     * @return Whether the downLinkSmaPktLen field is set.
     */
    boolean hasDownLinkSmaPktLen();
    /**
     * <code>optional uint32 downLinkSmaPktLen = 15;</code>
     * @return The downLinkSmaPktLen.
     */
    int getDownLinkSmaPktLen();

    /**
     * <code>optional uint64 downLinkBigPktInt = 16;</code>
     * @return Whether the downLinkBigPktInt field is set.
     */
    boolean hasDownLinkBigPktInt();
    /**
     * <code>optional uint64 downLinkBigPktInt = 16;</code>
     * @return The downLinkBigPktInt.
     */
    long getDownLinkBigPktInt();

    /**
     * <code>optional uint64 downLinkSmaPktInt = 17;</code>
     * @return Whether the downLinkSmaPktInt field is set.
     */
    boolean hasDownLinkSmaPktInt();
    /**
     * <code>optional uint64 downLinkSmaPktInt = 17;</code>
     * @return The downLinkSmaPktInt.
     */
    long getDownLinkSmaPktInt();

    /**
     * <code>optional uint32 firTtlByCli = 18;</code>
     * @return Whether the firTtlByCli field is set.
     */
    boolean hasFirTtlByCli();
    /**
     * <code>optional uint32 firTtlByCli = 18;</code>
     * @return The firTtlByCli.
     */
    int getFirTtlByCli();

    /**
     * <code>optional uint32 firTtlBySrv = 19;</code>
     * @return Whether the firTtlBySrv field is set.
     */
    boolean hasFirTtlBySrv();
    /**
     * <code>optional uint32 firTtlBySrv = 19;</code>
     * @return The firTtlBySrv.
     */
    int getFirTtlBySrv();

    /**
     * <code>optional uint32 appDirec = 20;</code>
     * @return Whether the appDirec field is set.
     */
    boolean hasAppDirec();
    /**
     * <code>optional uint32 appDirec = 20;</code>
     * @return The appDirec.
     */
    int getAppDirec();

    /**
     * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
     * @return Whether the tcpFlagsFinCnt field is set.
     */
    boolean hasTcpFlagsFinCnt();
    /**
     * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
     * @return The tcpFlagsFinCnt.
     */
    int getTcpFlagsFinCnt();

    /**
     * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
     * @return Whether the tcpFlagsSynCnt field is set.
     */
    boolean hasTcpFlagsSynCnt();
    /**
     * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
     * @return The tcpFlagsSynCnt.
     */
    int getTcpFlagsSynCnt();

    /**
     * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
     * @return Whether the tcpFlagsRstCnt field is set.
     */
    boolean hasTcpFlagsRstCnt();
    /**
     * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
     * @return The tcpFlagsRstCnt.
     */
    int getTcpFlagsRstCnt();

    /**
     * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
     * @return Whether the tcpFlagsPshCnt field is set.
     */
    boolean hasTcpFlagsPshCnt();
    /**
     * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
     * @return The tcpFlagsPshCnt.
     */
    int getTcpFlagsPshCnt();

    /**
     * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
     * @return Whether the tcpFlagsAckCnt field is set.
     */
    boolean hasTcpFlagsAckCnt();
    /**
     * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
     * @return The tcpFlagsAckCnt.
     */
    int getTcpFlagsAckCnt();

    /**
     * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
     * @return Whether the tcpFlagsUrgCnt field is set.
     */
    boolean hasTcpFlagsUrgCnt();
    /**
     * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
     * @return The tcpFlagsUrgCnt.
     */
    int getTcpFlagsUrgCnt();

    /**
     * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
     * @return Whether the tcpFlagsEceCnt field is set.
     */
    boolean hasTcpFlagsEceCnt();
    /**
     * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
     * @return The tcpFlagsEceCnt.
     */
    int getTcpFlagsEceCnt();

    /**
     * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
     * @return Whether the tcpFlagsCwrCnt field is set.
     */
    boolean hasTcpFlagsCwrCnt();
    /**
     * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
     * @return The tcpFlagsCwrCnt.
     */
    int getTcpFlagsCwrCnt();

    /**
     * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
     * @return Whether the tcpFlagsNSCnt field is set.
     */
    boolean hasTcpFlagsNSCnt();
    /**
     * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
     * @return The tcpFlagsNSCnt.
     */
    int getTcpFlagsNSCnt();

    /**
     * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
     * @return Whether the tcpFlagsSynAckCnt field is set.
     */
    boolean hasTcpFlagsSynAckCnt();
    /**
     * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
     * @return The tcpFlagsSynAckCnt.
     */
    int getTcpFlagsSynAckCnt();

    /**
     * <code>optional bytes etags = 31;</code>
     * @return Whether the etags field is set.
     */
    boolean hasEtags();
    /**
     * <code>optional bytes etags = 31;</code>
     * @return The etags.
     */
    com.google.protobuf.ByteString getEtags();

    /**
     * <code>optional bytes ttags = 32;</code>
     * @return Whether the ttags field is set.
     */
    boolean hasTtags();
    /**
     * <code>optional bytes ttags = 32;</code>
     * @return The ttags.
     */
    com.google.protobuf.ByteString getTtags();

    /**
     * <code>optional uint32 upLinkChecksum = 33;</code>
     * @return Whether the upLinkChecksum field is set.
     */
    boolean hasUpLinkChecksum();
    /**
     * <code>optional uint32 upLinkChecksum = 33;</code>
     * @return The upLinkChecksum.
     */
    int getUpLinkChecksum();

    /**
     * <code>optional uint32 downLinkChecksum = 34;</code>
     * @return Whether the downLinkChecksum field is set.
     */
    boolean hasDownLinkChecksum();
    /**
     * <code>optional uint32 downLinkChecksum = 34;</code>
     * @return The downLinkChecksum.
     */
    int getDownLinkChecksum();

    /**
     * <code>optional uint64 upLinkDesBytes = 35;</code>
     * @return Whether the upLinkDesBytes field is set.
     */
    boolean hasUpLinkDesBytes();
    /**
     * <code>optional uint64 upLinkDesBytes = 35;</code>
     * @return The upLinkDesBytes.
     */
    long getUpLinkDesBytes();

    /**
     * <code>optional uint64 downLinkDesBytes = 36;</code>
     * @return Whether the downLinkDesBytes field is set.
     */
    boolean hasDownLinkDesBytes();
    /**
     * <code>optional uint64 downLinkDesBytes = 36;</code>
     * @return The downLinkDesBytes.
     */
    long getDownLinkDesBytes();

    /**
     * <code>optional bytes stream = 37;</code>
     * @return Whether the stream field is set.
     */
    boolean hasStream();
    /**
     * <code>optional bytes stream = 37;</code>
     * @return The stream.
     */
    com.google.protobuf.ByteString getStream();

    /**
     * <code>optional bytes upLinkStream = 38;</code>
     * @return Whether the upLinkStream field is set.
     */
    boolean hasUpLinkStream();
    /**
     * <code>optional bytes upLinkStream = 38;</code>
     * @return The upLinkStream.
     */
    com.google.protobuf.ByteString getUpLinkStream();

    /**
     * <code>optional bytes downLinkStream = 39;</code>
     * @return Whether the downLinkStream field is set.
     */
    boolean hasDownLinkStream();
    /**
     * <code>optional bytes downLinkStream = 39;</code>
     * @return The downLinkStream.
     */
    com.google.protobuf.ByteString getDownLinkStream();

    /**
     * <code>optional bytes trans_payload_hex = 40;</code>
     * @return Whether the transPayloadHex field is set.
     */
    boolean hasTransPayloadHex();
    /**
     * <code>optional bytes trans_payload_hex = 40;</code>
     * @return The transPayloadHex.
     */
    com.google.protobuf.ByteString getTransPayloadHex();

    /**
     * <code>optional bytes upLinkTransPayHex = 41;</code>
     * @return Whether the upLinkTransPayHex field is set.
     */
    boolean hasUpLinkTransPayHex();
    /**
     * <code>optional bytes upLinkTransPayHex = 41;</code>
     * @return The upLinkTransPayHex.
     */
    com.google.protobuf.ByteString getUpLinkTransPayHex();

    /**
     * <code>optional bytes downLinkTransPayHex = 42;</code>
     * @return Whether the downLinkTransPayHex field is set.
     */
    boolean hasDownLinkTransPayHex();
    /**
     * <code>optional bytes downLinkTransPayHex = 42;</code>
     * @return The downLinkTransPayHex.
     */
    com.google.protobuf.ByteString getDownLinkTransPayHex();

    /**
     * <code>repeated uint32 upLinkPayLenSet = 43;</code>
     * @return A list containing the upLinkPayLenSet.
     */
    java.util.List<java.lang.Integer> getUpLinkPayLenSetList();
    /**
     * <code>repeated uint32 upLinkPayLenSet = 43;</code>
     * @return The count of upLinkPayLenSet.
     */
    int getUpLinkPayLenSetCount();
    /**
     * <code>repeated uint32 upLinkPayLenSet = 43;</code>
     * @param index The index of the element to return.
     * @return The upLinkPayLenSet at the given index.
     */
    int getUpLinkPayLenSet(int index);

    /**
     * <code>repeated uint32 downLinkPayLenSet = 44;</code>
     * @return A list containing the downLinkPayLenSet.
     */
    java.util.List<java.lang.Integer> getDownLinkPayLenSetList();
    /**
     * <code>repeated uint32 downLinkPayLenSet = 44;</code>
     * @return The count of downLinkPayLenSet.
     */
    int getDownLinkPayLenSetCount();
    /**
     * <code>repeated uint32 downLinkPayLenSet = 44;</code>
     * @param index The index of the element to return.
     * @return The downLinkPayLenSet at the given index.
     */
    int getDownLinkPayLenSet(int index);

    /**
     * <code>optional uint32 establish = 45;</code>
     * @return Whether the establish field is set.
     */
    boolean hasEstablish();
    /**
     * <code>optional uint32 establish = 45;</code>
     * @return The establish.
     */
    int getEstablish();

    /**
     * <code>optional uint32 upLinkSynSeqNum = 46;</code>
     * @return Whether the upLinkSynSeqNum field is set.
     */
    boolean hasUpLinkSynSeqNum();
    /**
     * <code>optional uint32 upLinkSynSeqNum = 46;</code>
     * @return The upLinkSynSeqNum.
     */
    int getUpLinkSynSeqNum();

    /**
     * <code>optional uint32 downLinkSynSeqNum = 47;</code>
     * @return Whether the downLinkSynSeqNum field is set.
     */
    boolean hasDownLinkSynSeqNum();
    /**
     * <code>optional uint32 downLinkSynSeqNum = 47;</code>
     * @return The downLinkSynSeqNum.
     */
    int getDownLinkSynSeqNum();

    /**
     * <code>optional uint32 upLinkSynTcpWins = 48;</code>
     * @return Whether the upLinkSynTcpWins field is set.
     */
    boolean hasUpLinkSynTcpWins();
    /**
     * <code>optional uint32 upLinkSynTcpWins = 48;</code>
     * @return The upLinkSynTcpWins.
     */
    int getUpLinkSynTcpWins();

    /**
     * <code>optional uint32 downLinkSynTcpWins = 49;</code>
     * @return Whether the downLinkSynTcpWins field is set.
     */
    boolean hasDownLinkSynTcpWins();
    /**
     * <code>optional uint32 downLinkSynTcpWins = 49;</code>
     * @return The downLinkSynTcpWins.
     */
    int getDownLinkSynTcpWins();

    /**
     * <code>optional bytes upLinkTcpOpts = 50;</code>
     * @return Whether the upLinkTcpOpts field is set.
     */
    boolean hasUpLinkTcpOpts();
    /**
     * <code>optional bytes upLinkTcpOpts = 50;</code>
     * @return The upLinkTcpOpts.
     */
    com.google.protobuf.ByteString getUpLinkTcpOpts();

    /**
     * <code>optional bytes downLinkTcpOpts = 51;</code>
     * @return Whether the downLinkTcpOpts field is set.
     */
    boolean hasDownLinkTcpOpts();
    /**
     * <code>optional bytes downLinkTcpOpts = 51;</code>
     * @return The downLinkTcpOpts.
     */
    com.google.protobuf.ByteString getDownLinkTcpOpts();

    /**
     * <code>optional uint64 upSesBytes = 52;</code>
     * @return Whether the upSesBytes field is set.
     */
    boolean hasUpSesBytes();
    /**
     * <code>optional uint64 upSesBytes = 52;</code>
     * @return The upSesBytes.
     */
    long getUpSesBytes();

    /**
     * <code>optional uint64 downSesbytes = 53;</code>
     * @return Whether the downSesbytes field is set.
     */
    boolean hasDownSesbytes();
    /**
     * <code>optional uint64 downSesbytes = 53;</code>
     * @return The downSesbytes.
     */
    long getDownSesbytes();

    /**
     * <code>optional uint64 sesBytes = 54;</code>
     * @return Whether the sesBytes field is set.
     */
    boolean hasSesBytes();
    /**
     * <code>optional uint64 sesBytes = 54;</code>
     * @return The sesBytes.
     */
    long getSesBytes();

    /**
     * <code>optional float sesBytesRatio = 55;</code>
     * @return Whether the sesBytesRatio field is set.
     */
    boolean hasSesBytesRatio();
    /**
     * <code>optional float sesBytesRatio = 55;</code>
     * @return The sesBytesRatio.
     */
    float getSesBytesRatio();

    /**
     * <code>optional float payLenRatio = 56;</code>
     * @return Whether the payLenRatio field is set.
     */
    boolean hasPayLenRatio();
    /**
     * <code>optional float payLenRatio = 56;</code>
     * @return The payLenRatio.
     */
    float getPayLenRatio();

    /**
     * <code>optional bytes ipAsnDestination = 57;</code>
     * @return Whether the ipAsnDestination field is set.
     */
    boolean hasIpAsnDestination();
    /**
     * <code>optional bytes ipAsnDestination = 57;</code>
     * @return The ipAsnDestination.
     */
    com.google.protobuf.ByteString getIpAsnDestination();

    /**
     * <code>optional bytes ipAsnSource = 58;</code>
     * @return Whether the ipAsnSource field is set.
     */
    boolean hasIpAsnSource();
    /**
     * <code>optional bytes ipAsnSource = 58;</code>
     * @return The ipAsnSource.
     */
    com.google.protobuf.ByteString getIpAsnSource();

    /**
     * <code>optional bytes ipBaseProto = 59;</code>
     * @return Whether the ipBaseProto field is set.
     */
    boolean hasIpBaseProto();
    /**
     * <code>optional bytes ipBaseProto = 59;</code>
     * @return The ipBaseProto.
     */
    com.google.protobuf.ByteString getIpBaseProto();

    /**
     * <code>optional uint64 ipBeginTime = 60;</code>
     * @return Whether the ipBeginTime field is set.
     */
    boolean hasIpBeginTime();
    /**
     * <code>optional uint64 ipBeginTime = 60;</code>
     * @return The ipBeginTime.
     */
    long getIpBeginTime();

    /**
     * <code>optional bytes ipCityDestination = 61;</code>
     * @return Whether the ipCityDestination field is set.
     */
    boolean hasIpCityDestination();
    /**
     * <code>optional bytes ipCityDestination = 61;</code>
     * @return The ipCityDestination.
     */
    com.google.protobuf.ByteString getIpCityDestination();

    /**
     * <code>optional bytes ipCitySource = 62;</code>
     * @return Whether the ipCitySource field is set.
     */
    boolean hasIpCitySource();
    /**
     * <code>optional bytes ipCitySource = 62;</code>
     * @return The ipCitySource.
     */
    com.google.protobuf.ByteString getIpCitySource();

    /**
     * <code>optional bytes ipCountryDestination = 63;</code>
     * @return Whether the ipCountryDestination field is set.
     */
    boolean hasIpCountryDestination();
    /**
     * <code>optional bytes ipCountryDestination = 63;</code>
     * @return The ipCountryDestination.
     */
    com.google.protobuf.ByteString getIpCountryDestination();

    /**
     * <code>optional bytes ipCountrySource = 64;</code>
     * @return Whether the ipCountrySource field is set.
     */
    boolean hasIpCountrySource();
    /**
     * <code>optional bytes ipCountrySource = 64;</code>
     * @return The ipCountrySource.
     */
    com.google.protobuf.ByteString getIpCountrySource();

    /**
     * <code>optional uint64 ipDataBytes = 65;</code>
     * @return Whether the ipDataBytes field is set.
     */
    boolean hasIpDataBytes();
    /**
     * <code>optional uint64 ipDataBytes = 65;</code>
     * @return The ipDataBytes.
     */
    long getIpDataBytes();

    /**
     * <code>optional uint64 ipDesiredBytes = 66;</code>
     * @return Whether the ipDesiredBytes field is set.
     */
    boolean hasIpDesiredBytes();
    /**
     * <code>optional uint64 ipDesiredBytes = 66;</code>
     * @return The ipDesiredBytes.
     */
    long getIpDesiredBytes();

    /**
     * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
     * @return Whether the ipDesiredBytesDestination field is set.
     */
    boolean hasIpDesiredBytesDestination();
    /**
     * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
     * @return The ipDesiredBytesDestination.
     */
    long getIpDesiredBytesDestination();

    /**
     * <code>optional uint64 ipDesiredBytesSource = 68;</code>
     * @return Whether the ipDesiredBytesSource field is set.
     */
    boolean hasIpDesiredBytesSource();
    /**
     * <code>optional uint64 ipDesiredBytesSource = 68;</code>
     * @return The ipDesiredBytesSource.
     */
    long getIpDesiredBytesSource();

    /**
     * <code>optional uint32 ipDestination = 69;</code>
     * @return Whether the ipDestination field is set.
     */
    boolean hasIpDestination();
    /**
     * <code>optional uint32 ipDestination = 69;</code>
     * @return The ipDestination.
     */
    int getIpDestination();

    /**
     * <code>optional uint32 ipDuration = 70;</code>
     * @return Whether the ipDuration field is set.
     */
    boolean hasIpDuration();
    /**
     * <code>optional uint32 ipDuration = 70;</code>
     * @return The ipDuration.
     */
    int getIpDuration();

    /**
     * <code>optional uint64 ipEndTime = 71;</code>
     * @return Whether the ipEndTime field is set.
     */
    boolean hasIpEndTime();
    /**
     * <code>optional uint64 ipEndTime = 71;</code>
     * @return The ipEndTime.
     */
    long getIpEndTime();

    /**
     * <code>optional bytes ipIspDestination = 72;</code>
     * @return Whether the ipIspDestination field is set.
     */
    boolean hasIpIspDestination();
    /**
     * <code>optional bytes ipIspDestination = 72;</code>
     * @return The ipIspDestination.
     */
    com.google.protobuf.ByteString getIpIspDestination();

    /**
     * <code>optional bytes ipIspSource = 73;</code>
     * @return Whether the ipIspSource field is set.
     */
    boolean hasIpIspSource();
    /**
     * <code>optional bytes ipIspSource = 73;</code>
     * @return The ipIspSource.
     */
    com.google.protobuf.ByteString getIpIspSource();

    /**
     * <code>optional float ipLatitudeDestination = 74;</code>
     * @return Whether the ipLatitudeDestination field is set.
     */
    boolean hasIpLatitudeDestination();
    /**
     * <code>optional float ipLatitudeDestination = 74;</code>
     * @return The ipLatitudeDestination.
     */
    float getIpLatitudeDestination();

    /**
     * <code>optional float ipLatitudeSource = 75;</code>
     * @return Whether the ipLatitudeSource field is set.
     */
    boolean hasIpLatitudeSource();
    /**
     * <code>optional float ipLatitudeSource = 75;</code>
     * @return The ipLatitudeSource.
     */
    float getIpLatitudeSource();

    /**
     * <code>optional float ipLongitudeDestination = 76;</code>
     * @return Whether the ipLongitudeDestination field is set.
     */
    boolean hasIpLongitudeDestination();
    /**
     * <code>optional float ipLongitudeDestination = 76;</code>
     * @return The ipLongitudeDestination.
     */
    float getIpLongitudeDestination();

    /**
     * <code>optional float ipLongitudeSource = 77;</code>
     * @return Whether the ipLongitudeSource field is set.
     */
    boolean hasIpLongitudeSource();
    /**
     * <code>optional float ipLongitudeSource = 77;</code>
     * @return The ipLongitudeSource.
     */
    float getIpLongitudeSource();

    /**
     * <code>optional uint32 ipPackets = 78;</code>
     * @return Whether the ipPackets field is set.
     */
    boolean hasIpPackets();
    /**
     * <code>optional uint32 ipPackets = 78;</code>
     * @return The ipPackets.
     */
    int getIpPackets();

    /**
     * <code>optional uint32 ipProto = 79;</code>
     * @return Whether the ipProto field is set.
     */
    boolean hasIpProto();
    /**
     * <code>optional uint32 ipProto = 79;</code>
     * @return The ipProto.
     */
    int getIpProto();

    /**
     * <code>optional bytes ipProtoPath = 80;</code>
     * @return Whether the ipProtoPath field is set.
     */
    boolean hasIpProtoPath();
    /**
     * <code>optional bytes ipProtoPath = 80;</code>
     * @return The ipProtoPath.
     */
    com.google.protobuf.ByteString getIpProtoPath();

    /**
     * <code>optional uint32 ipSource = 81;</code>
     * @return Whether the ipSource field is set.
     */
    boolean hasIpSource();
    /**
     * <code>optional uint32 ipSource = 81;</code>
     * @return The ipSource.
     */
    int getIpSource();

    /**
     * <code>optional bytes ipStateDestination = 82;</code>
     * @return Whether the ipStateDestination field is set.
     */
    boolean hasIpStateDestination();
    /**
     * <code>optional bytes ipStateDestination = 82;</code>
     * @return The ipStateDestination.
     */
    com.google.protobuf.ByteString getIpStateDestination();

    /**
     * <code>optional bytes ipStateSource = 83;</code>
     * @return Whether the ipStateSource field is set.
     */
    boolean hasIpStateSource();
    /**
     * <code>optional bytes ipStateSource = 83;</code>
     * @return The ipStateSource.
     */
    com.google.protobuf.ByteString getIpStateSource();

    /**
     * <code>optional bytes ipUpperProto = 84;</code>
     * @return Whether the ipUpperProto field is set.
     */
    boolean hasIpUpperProto();
    /**
     * <code>optional bytes ipUpperProto = 84;</code>
     * @return The ipUpperProto.
     */
    com.google.protobuf.ByteString getIpUpperProto();

    /**
     * <code>optional uint32 ipVersion = 85;</code>
     * @return Whether the ipVersion field is set.
     */
    boolean hasIpVersion();
    /**
     * <code>optional uint32 ipVersion = 85;</code>
     * @return The ipVersion.
     */
    int getIpVersion();

    /**
     * <code>optional bytes ipv6Destination = 86;</code>
     * @return Whether the ipv6Destination field is set.
     */
    boolean hasIpv6Destination();
    /**
     * <code>optional bytes ipv6Destination = 86;</code>
     * @return The ipv6Destination.
     */
    com.google.protobuf.ByteString getIpv6Destination();

    /**
     * <code>optional bytes ipv6Source = 87;</code>
     * @return Whether the ipv6Source field is set.
     */
    boolean hasIpv6Source();
    /**
     * <code>optional bytes ipv6Source = 87;</code>
     * @return The ipv6Source.
     */
    com.google.protobuf.ByteString getIpv6Source();

    /**
     * <code>optional uint32 outIpDestination = 88;</code>
     * @return Whether the outIpDestination field is set.
     */
    boolean hasOutIpDestination();
    /**
     * <code>optional uint32 outIpDestination = 88;</code>
     * @return The outIpDestination.
     */
    int getOutIpDestination();

    /**
     * <code>optional uint32 outIpProto = 89;</code>
     * @return Whether the outIpProto field is set.
     */
    boolean hasOutIpProto();
    /**
     * <code>optional uint32 outIpProto = 89;</code>
     * @return The outIpProto.
     */
    int getOutIpProto();

    /**
     * <code>optional uint32 outIpSource = 90;</code>
     * @return Whether the outIpSource field is set.
     */
    boolean hasOutIpSource();
    /**
     * <code>optional uint32 outIpSource = 90;</code>
     * @return The outIpSource.
     */
    int getOutIpSource();

    /**
     * <code>optional uint32 outIpVersion = 91;</code>
     * @return Whether the outIpVersion field is set.
     */
    boolean hasOutIpVersion();
    /**
     * <code>optional uint32 outIpVersion = 91;</code>
     * @return The outIpVersion.
     */
    int getOutIpVersion();

    /**
     * <code>optional bytes outIpv6Destination = 92;</code>
     * @return Whether the outIpv6Destination field is set.
     */
    boolean hasOutIpv6Destination();
    /**
     * <code>optional bytes outIpv6Destination = 92;</code>
     * @return The outIpv6Destination.
     */
    com.google.protobuf.ByteString getOutIpv6Destination();

    /**
     * <code>optional bytes outIpv6Source = 93;</code>
     * @return Whether the outIpv6Source field is set.
     */
    boolean hasOutIpv6Source();
    /**
     * <code>optional bytes outIpv6Source = 93;</code>
     * @return The outIpv6Source.
     */
    com.google.protobuf.ByteString getOutIpv6Source();

    /**
     * <code>optional uint32 outPortDestination = 94;</code>
     * @return Whether the outPortDestination field is set.
     */
    boolean hasOutPortDestination();
    /**
     * <code>optional uint32 outPortDestination = 94;</code>
     * @return The outPortDestination.
     */
    int getOutPortDestination();

    /**
     * <code>optional uint32 outPortSource = 95;</code>
     * @return Whether the outPortSource field is set.
     */
    boolean hasOutPortSource();
    /**
     * <code>optional uint32 outPortSource = 95;</code>
     * @return The outPortSource.
     */
    int getOutPortSource();

    /**
     * <code>optional uint32 portDestination = 96;</code>
     * @return Whether the portDestination field is set.
     */
    boolean hasPortDestination();
    /**
     * <code>optional uint32 portDestination = 96;</code>
     * @return The portDestination.
     */
    int getPortDestination();

    /**
     * <code>optional uint32 portSource = 97;</code>
     * @return Whether the portSource field is set.
     */
    boolean hasPortSource();
    /**
     * <code>optional uint32 portSource = 97;</code>
     * @return The portSource.
     */
    int getPortSource();

    /**
     * <code>optional uint32 tcpFinished = 98;</code>
     * @return Whether the tcpFinished field is set.
     */
    boolean hasTcpFinished();
    /**
     * <code>optional uint32 tcpFinished = 98;</code>
     * @return The tcpFinished.
     */
    int getTcpFinished();

    /**
     * <code>optional bytes tcpFirstFlag = 99;</code>
     * @return Whether the tcpFirstFlag field is set.
     */
    boolean hasTcpFirstFlag();
    /**
     * <code>optional bytes tcpFirstFlag = 99;</code>
     * @return The tcpFirstFlag.
     */
    com.google.protobuf.ByteString getTcpFirstFlag();

    /**
     * <code>optional bytes tcpFlagsDestination = 100;</code>
     * @return Whether the tcpFlagsDestination field is set.
     */
    boolean hasTcpFlagsDestination();
    /**
     * <code>optional bytes tcpFlagsDestination = 100;</code>
     * @return The tcpFlagsDestination.
     */
    com.google.protobuf.ByteString getTcpFlagsDestination();

    /**
     * <code>optional bytes tcpFlagsSource = 101;</code>
     * @return Whether the tcpFlagsSource field is set.
     */
    boolean hasTcpFlagsSource();
    /**
     * <code>optional bytes tcpFlagsSource = 101;</code>
     * @return The tcpFlagsSource.
     */
    com.google.protobuf.ByteString getTcpFlagsSource();

    /**
     * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
     * @return Whether the transPacketLengthDestinationHighFrequency field is set.
     */
    boolean hasTransPacketLengthDestinationHighFrequency();
    /**
     * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
     * @return The transPacketLengthDestinationHighFrequency.
     */
    int getTransPacketLengthDestinationHighFrequency();

    /**
     * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
     * @return Whether the transPacketLengthSourceHighFrequency field is set.
     */
    boolean hasTransPacketLengthSourceHighFrequency();
    /**
     * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
     * @return The transPacketLengthSourceHighFrequency.
     */
    int getTransPacketLengthSourceHighFrequency();

    /**
     * <code>optional uint32 isEnd = 104;</code>
     * @return Whether the isEnd field is set.
     */
    boolean hasIsEnd();
    /**
     * <code>optional uint32 isEnd = 104;</code>
     * @return The isEnd.
     */
    int getIsEnd();
  }
  /**
   * Protobuf type {@code LinkInfo}
   */
  public static final class LinkInfo extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:LinkInfo)
      LinkInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        LinkInfo.class.getName());
    }
    // Use LinkInfo.newBuilder() to construct.
    private LinkInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private LinkInfo() {
      portInfo_ = com.google.protobuf.ByteString.EMPTY;
      portInfoAtt_ = com.google.protobuf.ByteString.EMPTY;
      tcpflag_ = com.google.protobuf.ByteString.EMPTY;
      etags_ = com.google.protobuf.ByteString.EMPTY;
      ttags_ = com.google.protobuf.ByteString.EMPTY;
      stream_ = com.google.protobuf.ByteString.EMPTY;
      upLinkStream_ = com.google.protobuf.ByteString.EMPTY;
      downLinkStream_ = com.google.protobuf.ByteString.EMPTY;
      transPayloadHex_ = com.google.protobuf.ByteString.EMPTY;
      upLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
      downLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
      upLinkPayLenSet_ = emptyIntList();
      downLinkPayLenSet_ = emptyIntList();
      upLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
      downLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
      ipAsnDestination_ = com.google.protobuf.ByteString.EMPTY;
      ipAsnSource_ = com.google.protobuf.ByteString.EMPTY;
      ipBaseProto_ = com.google.protobuf.ByteString.EMPTY;
      ipCityDestination_ = com.google.protobuf.ByteString.EMPTY;
      ipCitySource_ = com.google.protobuf.ByteString.EMPTY;
      ipCountryDestination_ = com.google.protobuf.ByteString.EMPTY;
      ipCountrySource_ = com.google.protobuf.ByteString.EMPTY;
      ipIspDestination_ = com.google.protobuf.ByteString.EMPTY;
      ipIspSource_ = com.google.protobuf.ByteString.EMPTY;
      ipProtoPath_ = com.google.protobuf.ByteString.EMPTY;
      ipStateDestination_ = com.google.protobuf.ByteString.EMPTY;
      ipStateSource_ = com.google.protobuf.ByteString.EMPTY;
      ipUpperProto_ = com.google.protobuf.ByteString.EMPTY;
      ipv6Destination_ = com.google.protobuf.ByteString.EMPTY;
      ipv6Source_ = com.google.protobuf.ByteString.EMPTY;
      outIpv6Destination_ = com.google.protobuf.ByteString.EMPTY;
      outIpv6Source_ = com.google.protobuf.ByteString.EMPTY;
      tcpFirstFlag_ = com.google.protobuf.ByteString.EMPTY;
      tcpFlagsDestination_ = com.google.protobuf.ByteString.EMPTY;
      tcpFlagsSource_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return LinkInfoOuterClass.internal_static_LinkInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return LinkInfoOuterClass.internal_static_LinkInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              LinkInfoOuterClass.LinkInfo.class, LinkInfoOuterClass.LinkInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    private int bitField3_;
    public static final int PORTINFO_FIELD_NUMBER = 1;
    private com.google.protobuf.ByteString portInfo_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes portInfo = 1;</code>
     * @return Whether the portInfo field is set.
     */
    @java.lang.Override
    public boolean hasPortInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes portInfo = 1;</code>
     * @return The portInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPortInfo() {
      return portInfo_;
    }

    public static final int PORTINFOATT_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString portInfoAtt_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes portInfoAtt = 2;</code>
     * @return Whether the portInfoAtt field is set.
     */
    @java.lang.Override
    public boolean hasPortInfoAtt() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes portInfoAtt = 2;</code>
     * @return The portInfoAtt.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPortInfoAtt() {
      return portInfoAtt_;
    }

    public static final int UPPAYLEN_FIELD_NUMBER = 3;
    private int upPayLen_ = 0;
    /**
     * <code>optional uint32 upPayLen = 3;</code>
     * @return Whether the upPayLen field is set.
     */
    @java.lang.Override
    public boolean hasUpPayLen() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 upPayLen = 3;</code>
     * @return The upPayLen.
     */
    @java.lang.Override
    public int getUpPayLen() {
      return upPayLen_;
    }

    public static final int DOWNPAYLEN_FIELD_NUMBER = 4;
    private long downPayLen_ = 0L;
    /**
     * <code>optional uint64 downPayLen = 4;</code>
     * @return Whether the downPayLen field is set.
     */
    @java.lang.Override
    public boolean hasDownPayLen() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint64 downPayLen = 4;</code>
     * @return The downPayLen.
     */
    @java.lang.Override
    public long getDownPayLen() {
      return downPayLen_;
    }

    public static final int TCPFLAG_FIELD_NUMBER = 5;
    private com.google.protobuf.ByteString tcpflag_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes tcpflag = 5;</code>
     * @return Whether the tcpflag field is set.
     */
    @java.lang.Override
    public boolean hasTcpflag() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes tcpflag = 5;</code>
     * @return The tcpflag.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTcpflag() {
      return tcpflag_;
    }

    public static final int UPLINKPKTNUM_FIELD_NUMBER = 6;
    private long upLinkPktNum_ = 0L;
    /**
     * <code>optional uint64 upLinkPktNum = 6;</code>
     * @return Whether the upLinkPktNum field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkPktNum() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional uint64 upLinkPktNum = 6;</code>
     * @return The upLinkPktNum.
     */
    @java.lang.Override
    public long getUpLinkPktNum() {
      return upLinkPktNum_;
    }

    public static final int UPLINKSIZE_FIELD_NUMBER = 7;
    private long upLinkSize_ = 0L;
    /**
     * <code>optional uint64 upLinkSize = 7;</code>
     * @return Whether the upLinkSize field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkSize() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint64 upLinkSize = 7;</code>
     * @return The upLinkSize.
     */
    @java.lang.Override
    public long getUpLinkSize() {
      return upLinkSize_;
    }

    public static final int UPLINKBIGPKTLEN_FIELD_NUMBER = 8;
    private int upLinkBigPktLen_ = 0;
    /**
     * <code>optional uint32 upLinkBigPktLen = 8;</code>
     * @return Whether the upLinkBigPktLen field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkBigPktLen() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 upLinkBigPktLen = 8;</code>
     * @return The upLinkBigPktLen.
     */
    @java.lang.Override
    public int getUpLinkBigPktLen() {
      return upLinkBigPktLen_;
    }

    public static final int UPLINKSMAPKTLEN_FIELD_NUMBER = 9;
    private int upLinkSmaPktLen_ = 0;
    /**
     * <code>optional uint32 upLinkSmaPktLen = 9;</code>
     * @return Whether the upLinkSmaPktLen field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkSmaPktLen() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint32 upLinkSmaPktLen = 9;</code>
     * @return The upLinkSmaPktLen.
     */
    @java.lang.Override
    public int getUpLinkSmaPktLen() {
      return upLinkSmaPktLen_;
    }

    public static final int UPLINKBIGPKTINT_FIELD_NUMBER = 10;
    private long upLinkBigPktInt_ = 0L;
    /**
     * <code>optional uint64 upLinkBigPktInt = 10;</code>
     * @return Whether the upLinkBigPktInt field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkBigPktInt() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint64 upLinkBigPktInt = 10;</code>
     * @return The upLinkBigPktInt.
     */
    @java.lang.Override
    public long getUpLinkBigPktInt() {
      return upLinkBigPktInt_;
    }

    public static final int UPLINKSMAPKTINT_FIELD_NUMBER = 11;
    private long upLinkSmaPktInt_ = 0L;
    /**
     * <code>optional uint64 upLinkSmaPktInt = 11;</code>
     * @return Whether the upLinkSmaPktInt field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkSmaPktInt() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint64 upLinkSmaPktInt = 11;</code>
     * @return The upLinkSmaPktInt.
     */
    @java.lang.Override
    public long getUpLinkSmaPktInt() {
      return upLinkSmaPktInt_;
    }

    public static final int DOWNLINKPKTNUM_FIELD_NUMBER = 12;
    private int downLinkPktNum_ = 0;
    /**
     * <code>optional uint32 downLinkPktNum = 12;</code>
     * @return Whether the downLinkPktNum field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkPktNum() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 downLinkPktNum = 12;</code>
     * @return The downLinkPktNum.
     */
    @java.lang.Override
    public int getDownLinkPktNum() {
      return downLinkPktNum_;
    }

    public static final int DOWNLINKSIZE_FIELD_NUMBER = 13;
    private int downLinkSize_ = 0;
    /**
     * <code>optional uint32 downLinkSize = 13;</code>
     * @return Whether the downLinkSize field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkSize() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 downLinkSize = 13;</code>
     * @return The downLinkSize.
     */
    @java.lang.Override
    public int getDownLinkSize() {
      return downLinkSize_;
    }

    public static final int DOWNLINKBIGPKTLEN_FIELD_NUMBER = 14;
    private int downLinkBigPktLen_ = 0;
    /**
     * <code>optional uint32 downLinkBigPktLen = 14;</code>
     * @return Whether the downLinkBigPktLen field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkBigPktLen() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional uint32 downLinkBigPktLen = 14;</code>
     * @return The downLinkBigPktLen.
     */
    @java.lang.Override
    public int getDownLinkBigPktLen() {
      return downLinkBigPktLen_;
    }

    public static final int DOWNLINKSMAPKTLEN_FIELD_NUMBER = 15;
    private int downLinkSmaPktLen_ = 0;
    /**
     * <code>optional uint32 downLinkSmaPktLen = 15;</code>
     * @return Whether the downLinkSmaPktLen field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkSmaPktLen() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 downLinkSmaPktLen = 15;</code>
     * @return The downLinkSmaPktLen.
     */
    @java.lang.Override
    public int getDownLinkSmaPktLen() {
      return downLinkSmaPktLen_;
    }

    public static final int DOWNLINKBIGPKTINT_FIELD_NUMBER = 16;
    private long downLinkBigPktInt_ = 0L;
    /**
     * <code>optional uint64 downLinkBigPktInt = 16;</code>
     * @return Whether the downLinkBigPktInt field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkBigPktInt() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional uint64 downLinkBigPktInt = 16;</code>
     * @return The downLinkBigPktInt.
     */
    @java.lang.Override
    public long getDownLinkBigPktInt() {
      return downLinkBigPktInt_;
    }

    public static final int DOWNLINKSMAPKTINT_FIELD_NUMBER = 17;
    private long downLinkSmaPktInt_ = 0L;
    /**
     * <code>optional uint64 downLinkSmaPktInt = 17;</code>
     * @return Whether the downLinkSmaPktInt field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkSmaPktInt() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional uint64 downLinkSmaPktInt = 17;</code>
     * @return The downLinkSmaPktInt.
     */
    @java.lang.Override
    public long getDownLinkSmaPktInt() {
      return downLinkSmaPktInt_;
    }

    public static final int FIRTTLBYCLI_FIELD_NUMBER = 18;
    private int firTtlByCli_ = 0;
    /**
     * <code>optional uint32 firTtlByCli = 18;</code>
     * @return Whether the firTtlByCli field is set.
     */
    @java.lang.Override
    public boolean hasFirTtlByCli() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 firTtlByCli = 18;</code>
     * @return The firTtlByCli.
     */
    @java.lang.Override
    public int getFirTtlByCli() {
      return firTtlByCli_;
    }

    public static final int FIRTTLBYSRV_FIELD_NUMBER = 19;
    private int firTtlBySrv_ = 0;
    /**
     * <code>optional uint32 firTtlBySrv = 19;</code>
     * @return Whether the firTtlBySrv field is set.
     */
    @java.lang.Override
    public boolean hasFirTtlBySrv() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 firTtlBySrv = 19;</code>
     * @return The firTtlBySrv.
     */
    @java.lang.Override
    public int getFirTtlBySrv() {
      return firTtlBySrv_;
    }

    public static final int APPDIREC_FIELD_NUMBER = 20;
    private int appDirec_ = 0;
    /**
     * <code>optional uint32 appDirec = 20;</code>
     * @return Whether the appDirec field is set.
     */
    @java.lang.Override
    public boolean hasAppDirec() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint32 appDirec = 20;</code>
     * @return The appDirec.
     */
    @java.lang.Override
    public int getAppDirec() {
      return appDirec_;
    }

    public static final int TCPFLAGSFINCNT_FIELD_NUMBER = 21;
    private int tcpFlagsFinCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
     * @return Whether the tcpFlagsFinCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsFinCnt() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
     * @return The tcpFlagsFinCnt.
     */
    @java.lang.Override
    public int getTcpFlagsFinCnt() {
      return tcpFlagsFinCnt_;
    }

    public static final int TCPFLAGSSYNCNT_FIELD_NUMBER = 22;
    private int tcpFlagsSynCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
     * @return Whether the tcpFlagsSynCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsSynCnt() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
     * @return The tcpFlagsSynCnt.
     */
    @java.lang.Override
    public int getTcpFlagsSynCnt() {
      return tcpFlagsSynCnt_;
    }

    public static final int TCPFLAGSRSTCNT_FIELD_NUMBER = 23;
    private int tcpFlagsRstCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
     * @return Whether the tcpFlagsRstCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsRstCnt() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
     * @return The tcpFlagsRstCnt.
     */
    @java.lang.Override
    public int getTcpFlagsRstCnt() {
      return tcpFlagsRstCnt_;
    }

    public static final int TCPFLAGSPSHCNT_FIELD_NUMBER = 24;
    private int tcpFlagsPshCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
     * @return Whether the tcpFlagsPshCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsPshCnt() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
     * @return The tcpFlagsPshCnt.
     */
    @java.lang.Override
    public int getTcpFlagsPshCnt() {
      return tcpFlagsPshCnt_;
    }

    public static final int TCPFLAGSACKCNT_FIELD_NUMBER = 25;
    private int tcpFlagsAckCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
     * @return Whether the tcpFlagsAckCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsAckCnt() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
     * @return The tcpFlagsAckCnt.
     */
    @java.lang.Override
    public int getTcpFlagsAckCnt() {
      return tcpFlagsAckCnt_;
    }

    public static final int TCPFLAGSURGCNT_FIELD_NUMBER = 26;
    private int tcpFlagsUrgCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
     * @return Whether the tcpFlagsUrgCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsUrgCnt() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
     * @return The tcpFlagsUrgCnt.
     */
    @java.lang.Override
    public int getTcpFlagsUrgCnt() {
      return tcpFlagsUrgCnt_;
    }

    public static final int TCPFLAGSECECNT_FIELD_NUMBER = 27;
    private int tcpFlagsEceCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
     * @return Whether the tcpFlagsEceCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsEceCnt() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
     * @return The tcpFlagsEceCnt.
     */
    @java.lang.Override
    public int getTcpFlagsEceCnt() {
      return tcpFlagsEceCnt_;
    }

    public static final int TCPFLAGSCWRCNT_FIELD_NUMBER = 28;
    private int tcpFlagsCwrCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
     * @return Whether the tcpFlagsCwrCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsCwrCnt() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
     * @return The tcpFlagsCwrCnt.
     */
    @java.lang.Override
    public int getTcpFlagsCwrCnt() {
      return tcpFlagsCwrCnt_;
    }

    public static final int TCPFLAGSNSCNT_FIELD_NUMBER = 29;
    private int tcpFlagsNSCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
     * @return Whether the tcpFlagsNSCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsNSCnt() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
     * @return The tcpFlagsNSCnt.
     */
    @java.lang.Override
    public int getTcpFlagsNSCnt() {
      return tcpFlagsNSCnt_;
    }

    public static final int TCPFLAGSSYNACKCNT_FIELD_NUMBER = 30;
    private int tcpFlagsSynAckCnt_ = 0;
    /**
     * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
     * @return Whether the tcpFlagsSynAckCnt field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsSynAckCnt() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
     * @return The tcpFlagsSynAckCnt.
     */
    @java.lang.Override
    public int getTcpFlagsSynAckCnt() {
      return tcpFlagsSynAckCnt_;
    }

    public static final int ETAGS_FIELD_NUMBER = 31;
    private com.google.protobuf.ByteString etags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes etags = 31;</code>
     * @return Whether the etags field is set.
     */
    @java.lang.Override
    public boolean hasEtags() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes etags = 31;</code>
     * @return The etags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEtags() {
      return etags_;
    }

    public static final int TTAGS_FIELD_NUMBER = 32;
    private com.google.protobuf.ByteString ttags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ttags = 32;</code>
     * @return Whether the ttags field is set.
     */
    @java.lang.Override
    public boolean hasTtags() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <code>optional bytes ttags = 32;</code>
     * @return The ttags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTtags() {
      return ttags_;
    }

    public static final int UPLINKCHECKSUM_FIELD_NUMBER = 33;
    private int upLinkChecksum_ = 0;
    /**
     * <code>optional uint32 upLinkChecksum = 33;</code>
     * @return Whether the upLinkChecksum field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkChecksum() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 upLinkChecksum = 33;</code>
     * @return The upLinkChecksum.
     */
    @java.lang.Override
    public int getUpLinkChecksum() {
      return upLinkChecksum_;
    }

    public static final int DOWNLINKCHECKSUM_FIELD_NUMBER = 34;
    private int downLinkChecksum_ = 0;
    /**
     * <code>optional uint32 downLinkChecksum = 34;</code>
     * @return Whether the downLinkChecksum field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkChecksum() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 downLinkChecksum = 34;</code>
     * @return The downLinkChecksum.
     */
    @java.lang.Override
    public int getDownLinkChecksum() {
      return downLinkChecksum_;
    }

    public static final int UPLINKDESBYTES_FIELD_NUMBER = 35;
    private long upLinkDesBytes_ = 0L;
    /**
     * <code>optional uint64 upLinkDesBytes = 35;</code>
     * @return Whether the upLinkDesBytes field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkDesBytes() {
      return ((bitField1_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint64 upLinkDesBytes = 35;</code>
     * @return The upLinkDesBytes.
     */
    @java.lang.Override
    public long getUpLinkDesBytes() {
      return upLinkDesBytes_;
    }

    public static final int DOWNLINKDESBYTES_FIELD_NUMBER = 36;
    private long downLinkDesBytes_ = 0L;
    /**
     * <code>optional uint64 downLinkDesBytes = 36;</code>
     * @return Whether the downLinkDesBytes field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkDesBytes() {
      return ((bitField1_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint64 downLinkDesBytes = 36;</code>
     * @return The downLinkDesBytes.
     */
    @java.lang.Override
    public long getDownLinkDesBytes() {
      return downLinkDesBytes_;
    }

    public static final int STREAM_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString stream_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes stream = 37;</code>
     * @return Whether the stream field is set.
     */
    @java.lang.Override
    public boolean hasStream() {
      return ((bitField1_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes stream = 37;</code>
     * @return The stream.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getStream() {
      return stream_;
    }

    public static final int UPLINKSTREAM_FIELD_NUMBER = 38;
    private com.google.protobuf.ByteString upLinkStream_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes upLinkStream = 38;</code>
     * @return Whether the upLinkStream field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkStream() {
      return ((bitField1_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes upLinkStream = 38;</code>
     * @return The upLinkStream.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUpLinkStream() {
      return upLinkStream_;
    }

    public static final int DOWNLINKSTREAM_FIELD_NUMBER = 39;
    private com.google.protobuf.ByteString downLinkStream_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes downLinkStream = 39;</code>
     * @return Whether the downLinkStream field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkStream() {
      return ((bitField1_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes downLinkStream = 39;</code>
     * @return The downLinkStream.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDownLinkStream() {
      return downLinkStream_;
    }

    public static final int TRANS_PAYLOAD_HEX_FIELD_NUMBER = 40;
    private com.google.protobuf.ByteString transPayloadHex_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes trans_payload_hex = 40;</code>
     * @return Whether the transPayloadHex field is set.
     */
    @java.lang.Override
    public boolean hasTransPayloadHex() {
      return ((bitField1_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes trans_payload_hex = 40;</code>
     * @return The transPayloadHex.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTransPayloadHex() {
      return transPayloadHex_;
    }

    public static final int UPLINKTRANSPAYHEX_FIELD_NUMBER = 41;
    private com.google.protobuf.ByteString upLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes upLinkTransPayHex = 41;</code>
     * @return Whether the upLinkTransPayHex field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkTransPayHex() {
      return ((bitField1_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes upLinkTransPayHex = 41;</code>
     * @return The upLinkTransPayHex.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUpLinkTransPayHex() {
      return upLinkTransPayHex_;
    }

    public static final int DOWNLINKTRANSPAYHEX_FIELD_NUMBER = 42;
    private com.google.protobuf.ByteString downLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes downLinkTransPayHex = 42;</code>
     * @return Whether the downLinkTransPayHex field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkTransPayHex() {
      return ((bitField1_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes downLinkTransPayHex = 42;</code>
     * @return The downLinkTransPayHex.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDownLinkTransPayHex() {
      return downLinkTransPayHex_;
    }

    public static final int UPLINKPAYLENSET_FIELD_NUMBER = 43;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList upLinkPayLenSet_ =
        emptyIntList();
    /**
     * <code>repeated uint32 upLinkPayLenSet = 43;</code>
     * @return A list containing the upLinkPayLenSet.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getUpLinkPayLenSetList() {
      return upLinkPayLenSet_;
    }
    /**
     * <code>repeated uint32 upLinkPayLenSet = 43;</code>
     * @return The count of upLinkPayLenSet.
     */
    public int getUpLinkPayLenSetCount() {
      return upLinkPayLenSet_.size();
    }
    /**
     * <code>repeated uint32 upLinkPayLenSet = 43;</code>
     * @param index The index of the element to return.
     * @return The upLinkPayLenSet at the given index.
     */
    public int getUpLinkPayLenSet(int index) {
      return upLinkPayLenSet_.getInt(index);
    }

    public static final int DOWNLINKPAYLENSET_FIELD_NUMBER = 44;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList downLinkPayLenSet_ =
        emptyIntList();
    /**
     * <code>repeated uint32 downLinkPayLenSet = 44;</code>
     * @return A list containing the downLinkPayLenSet.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDownLinkPayLenSetList() {
      return downLinkPayLenSet_;
    }
    /**
     * <code>repeated uint32 downLinkPayLenSet = 44;</code>
     * @return The count of downLinkPayLenSet.
     */
    public int getDownLinkPayLenSetCount() {
      return downLinkPayLenSet_.size();
    }
    /**
     * <code>repeated uint32 downLinkPayLenSet = 44;</code>
     * @param index The index of the element to return.
     * @return The downLinkPayLenSet at the given index.
     */
    public int getDownLinkPayLenSet(int index) {
      return downLinkPayLenSet_.getInt(index);
    }

    public static final int ESTABLISH_FIELD_NUMBER = 45;
    private int establish_ = 0;
    /**
     * <code>optional uint32 establish = 45;</code>
     * @return Whether the establish field is set.
     */
    @java.lang.Override
    public boolean hasEstablish() {
      return ((bitField1_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint32 establish = 45;</code>
     * @return The establish.
     */
    @java.lang.Override
    public int getEstablish() {
      return establish_;
    }

    public static final int UPLINKSYNSEQNUM_FIELD_NUMBER = 46;
    private int upLinkSynSeqNum_ = 0;
    /**
     * <code>optional uint32 upLinkSynSeqNum = 46;</code>
     * @return Whether the upLinkSynSeqNum field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkSynSeqNum() {
      return ((bitField1_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 upLinkSynSeqNum = 46;</code>
     * @return The upLinkSynSeqNum.
     */
    @java.lang.Override
    public int getUpLinkSynSeqNum() {
      return upLinkSynSeqNum_;
    }

    public static final int DOWNLINKSYNSEQNUM_FIELD_NUMBER = 47;
    private int downLinkSynSeqNum_ = 0;
    /**
     * <code>optional uint32 downLinkSynSeqNum = 47;</code>
     * @return Whether the downLinkSynSeqNum field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkSynSeqNum() {
      return ((bitField1_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 downLinkSynSeqNum = 47;</code>
     * @return The downLinkSynSeqNum.
     */
    @java.lang.Override
    public int getDownLinkSynSeqNum() {
      return downLinkSynSeqNum_;
    }

    public static final int UPLINKSYNTCPWINS_FIELD_NUMBER = 48;
    private int upLinkSynTcpWins_ = 0;
    /**
     * <code>optional uint32 upLinkSynTcpWins = 48;</code>
     * @return Whether the upLinkSynTcpWins field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkSynTcpWins() {
      return ((bitField1_ & 0x00002000) != 0);
    }
    /**
     * <code>optional uint32 upLinkSynTcpWins = 48;</code>
     * @return The upLinkSynTcpWins.
     */
    @java.lang.Override
    public int getUpLinkSynTcpWins() {
      return upLinkSynTcpWins_;
    }

    public static final int DOWNLINKSYNTCPWINS_FIELD_NUMBER = 49;
    private int downLinkSynTcpWins_ = 0;
    /**
     * <code>optional uint32 downLinkSynTcpWins = 49;</code>
     * @return Whether the downLinkSynTcpWins field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkSynTcpWins() {
      return ((bitField1_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 downLinkSynTcpWins = 49;</code>
     * @return The downLinkSynTcpWins.
     */
    @java.lang.Override
    public int getDownLinkSynTcpWins() {
      return downLinkSynTcpWins_;
    }

    public static final int UPLINKTCPOPTS_FIELD_NUMBER = 50;
    private com.google.protobuf.ByteString upLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes upLinkTcpOpts = 50;</code>
     * @return Whether the upLinkTcpOpts field is set.
     */
    @java.lang.Override
    public boolean hasUpLinkTcpOpts() {
      return ((bitField1_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes upLinkTcpOpts = 50;</code>
     * @return The upLinkTcpOpts.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getUpLinkTcpOpts() {
      return upLinkTcpOpts_;
    }

    public static final int DOWNLINKTCPOPTS_FIELD_NUMBER = 51;
    private com.google.protobuf.ByteString downLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes downLinkTcpOpts = 51;</code>
     * @return Whether the downLinkTcpOpts field is set.
     */
    @java.lang.Override
    public boolean hasDownLinkTcpOpts() {
      return ((bitField1_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes downLinkTcpOpts = 51;</code>
     * @return The downLinkTcpOpts.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDownLinkTcpOpts() {
      return downLinkTcpOpts_;
    }

    public static final int UPSESBYTES_FIELD_NUMBER = 52;
    private long upSesBytes_ = 0L;
    /**
     * <code>optional uint64 upSesBytes = 52;</code>
     * @return Whether the upSesBytes field is set.
     */
    @java.lang.Override
    public boolean hasUpSesBytes() {
      return ((bitField1_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint64 upSesBytes = 52;</code>
     * @return The upSesBytes.
     */
    @java.lang.Override
    public long getUpSesBytes() {
      return upSesBytes_;
    }

    public static final int DOWNSESBYTES_FIELD_NUMBER = 53;
    private long downSesbytes_ = 0L;
    /**
     * <code>optional uint64 downSesbytes = 53;</code>
     * @return Whether the downSesbytes field is set.
     */
    @java.lang.Override
    public boolean hasDownSesbytes() {
      return ((bitField1_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint64 downSesbytes = 53;</code>
     * @return The downSesbytes.
     */
    @java.lang.Override
    public long getDownSesbytes() {
      return downSesbytes_;
    }

    public static final int SESBYTES_FIELD_NUMBER = 54;
    private long sesBytes_ = 0L;
    /**
     * <code>optional uint64 sesBytes = 54;</code>
     * @return Whether the sesBytes field is set.
     */
    @java.lang.Override
    public boolean hasSesBytes() {
      return ((bitField1_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint64 sesBytes = 54;</code>
     * @return The sesBytes.
     */
    @java.lang.Override
    public long getSesBytes() {
      return sesBytes_;
    }

    public static final int SESBYTESRATIO_FIELD_NUMBER = 55;
    private float sesBytesRatio_ = 0F;
    /**
     * <code>optional float sesBytesRatio = 55;</code>
     * @return Whether the sesBytesRatio field is set.
     */
    @java.lang.Override
    public boolean hasSesBytesRatio() {
      return ((bitField1_ & 0x00100000) != 0);
    }
    /**
     * <code>optional float sesBytesRatio = 55;</code>
     * @return The sesBytesRatio.
     */
    @java.lang.Override
    public float getSesBytesRatio() {
      return sesBytesRatio_;
    }

    public static final int PAYLENRATIO_FIELD_NUMBER = 56;
    private float payLenRatio_ = 0F;
    /**
     * <code>optional float payLenRatio = 56;</code>
     * @return Whether the payLenRatio field is set.
     */
    @java.lang.Override
    public boolean hasPayLenRatio() {
      return ((bitField1_ & 0x00200000) != 0);
    }
    /**
     * <code>optional float payLenRatio = 56;</code>
     * @return The payLenRatio.
     */
    @java.lang.Override
    public float getPayLenRatio() {
      return payLenRatio_;
    }

    public static final int IPASNDESTINATION_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString ipAsnDestination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipAsnDestination = 57;</code>
     * @return Whether the ipAsnDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpAsnDestination() {
      return ((bitField1_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes ipAsnDestination = 57;</code>
     * @return The ipAsnDestination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpAsnDestination() {
      return ipAsnDestination_;
    }

    public static final int IPASNSOURCE_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString ipAsnSource_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipAsnSource = 58;</code>
     * @return Whether the ipAsnSource field is set.
     */
    @java.lang.Override
    public boolean hasIpAsnSource() {
      return ((bitField1_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes ipAsnSource = 58;</code>
     * @return The ipAsnSource.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpAsnSource() {
      return ipAsnSource_;
    }

    public static final int IPBASEPROTO_FIELD_NUMBER = 59;
    private com.google.protobuf.ByteString ipBaseProto_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipBaseProto = 59;</code>
     * @return Whether the ipBaseProto field is set.
     */
    @java.lang.Override
    public boolean hasIpBaseProto() {
      return ((bitField1_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes ipBaseProto = 59;</code>
     * @return The ipBaseProto.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpBaseProto() {
      return ipBaseProto_;
    }

    public static final int IPBEGINTIME_FIELD_NUMBER = 60;
    private long ipBeginTime_ = 0L;
    /**
     * <code>optional uint64 ipBeginTime = 60;</code>
     * @return Whether the ipBeginTime field is set.
     */
    @java.lang.Override
    public boolean hasIpBeginTime() {
      return ((bitField1_ & 0x02000000) != 0);
    }
    /**
     * <code>optional uint64 ipBeginTime = 60;</code>
     * @return The ipBeginTime.
     */
    @java.lang.Override
    public long getIpBeginTime() {
      return ipBeginTime_;
    }

    public static final int IPCITYDESTINATION_FIELD_NUMBER = 61;
    private com.google.protobuf.ByteString ipCityDestination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipCityDestination = 61;</code>
     * @return Whether the ipCityDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpCityDestination() {
      return ((bitField1_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes ipCityDestination = 61;</code>
     * @return The ipCityDestination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpCityDestination() {
      return ipCityDestination_;
    }

    public static final int IPCITYSOURCE_FIELD_NUMBER = 62;
    private com.google.protobuf.ByteString ipCitySource_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipCitySource = 62;</code>
     * @return Whether the ipCitySource field is set.
     */
    @java.lang.Override
    public boolean hasIpCitySource() {
      return ((bitField1_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes ipCitySource = 62;</code>
     * @return The ipCitySource.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpCitySource() {
      return ipCitySource_;
    }

    public static final int IPCOUNTRYDESTINATION_FIELD_NUMBER = 63;
    private com.google.protobuf.ByteString ipCountryDestination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipCountryDestination = 63;</code>
     * @return Whether the ipCountryDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpCountryDestination() {
      return ((bitField1_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes ipCountryDestination = 63;</code>
     * @return The ipCountryDestination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpCountryDestination() {
      return ipCountryDestination_;
    }

    public static final int IPCOUNTRYSOURCE_FIELD_NUMBER = 64;
    private com.google.protobuf.ByteString ipCountrySource_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipCountrySource = 64;</code>
     * @return Whether the ipCountrySource field is set.
     */
    @java.lang.Override
    public boolean hasIpCountrySource() {
      return ((bitField1_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes ipCountrySource = 64;</code>
     * @return The ipCountrySource.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpCountrySource() {
      return ipCountrySource_;
    }

    public static final int IPDATABYTES_FIELD_NUMBER = 65;
    private long ipDataBytes_ = 0L;
    /**
     * <code>optional uint64 ipDataBytes = 65;</code>
     * @return Whether the ipDataBytes field is set.
     */
    @java.lang.Override
    public boolean hasIpDataBytes() {
      return ((bitField1_ & 0x40000000) != 0);
    }
    /**
     * <code>optional uint64 ipDataBytes = 65;</code>
     * @return The ipDataBytes.
     */
    @java.lang.Override
    public long getIpDataBytes() {
      return ipDataBytes_;
    }

    public static final int IPDESIREDBYTES_FIELD_NUMBER = 66;
    private long ipDesiredBytes_ = 0L;
    /**
     * <code>optional uint64 ipDesiredBytes = 66;</code>
     * @return Whether the ipDesiredBytes field is set.
     */
    @java.lang.Override
    public boolean hasIpDesiredBytes() {
      return ((bitField1_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint64 ipDesiredBytes = 66;</code>
     * @return The ipDesiredBytes.
     */
    @java.lang.Override
    public long getIpDesiredBytes() {
      return ipDesiredBytes_;
    }

    public static final int IPDESIREDBYTESDESTINATION_FIELD_NUMBER = 67;
    private long ipDesiredBytesDestination_ = 0L;
    /**
     * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
     * @return Whether the ipDesiredBytesDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpDesiredBytesDestination() {
      return ((bitField2_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
     * @return The ipDesiredBytesDestination.
     */
    @java.lang.Override
    public long getIpDesiredBytesDestination() {
      return ipDesiredBytesDestination_;
    }

    public static final int IPDESIREDBYTESSOURCE_FIELD_NUMBER = 68;
    private long ipDesiredBytesSource_ = 0L;
    /**
     * <code>optional uint64 ipDesiredBytesSource = 68;</code>
     * @return Whether the ipDesiredBytesSource field is set.
     */
    @java.lang.Override
    public boolean hasIpDesiredBytesSource() {
      return ((bitField2_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint64 ipDesiredBytesSource = 68;</code>
     * @return The ipDesiredBytesSource.
     */
    @java.lang.Override
    public long getIpDesiredBytesSource() {
      return ipDesiredBytesSource_;
    }

    public static final int IPDESTINATION_FIELD_NUMBER = 69;
    private int ipDestination_ = 0;
    /**
     * <code>optional uint32 ipDestination = 69;</code>
     * @return Whether the ipDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpDestination() {
      return ((bitField2_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 ipDestination = 69;</code>
     * @return The ipDestination.
     */
    @java.lang.Override
    public int getIpDestination() {
      return ipDestination_;
    }

    public static final int IPDURATION_FIELD_NUMBER = 70;
    private int ipDuration_ = 0;
    /**
     * <code>optional uint32 ipDuration = 70;</code>
     * @return Whether the ipDuration field is set.
     */
    @java.lang.Override
    public boolean hasIpDuration() {
      return ((bitField2_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 ipDuration = 70;</code>
     * @return The ipDuration.
     */
    @java.lang.Override
    public int getIpDuration() {
      return ipDuration_;
    }

    public static final int IPENDTIME_FIELD_NUMBER = 71;
    private long ipEndTime_ = 0L;
    /**
     * <code>optional uint64 ipEndTime = 71;</code>
     * @return Whether the ipEndTime field is set.
     */
    @java.lang.Override
    public boolean hasIpEndTime() {
      return ((bitField2_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint64 ipEndTime = 71;</code>
     * @return The ipEndTime.
     */
    @java.lang.Override
    public long getIpEndTime() {
      return ipEndTime_;
    }

    public static final int IPISPDESTINATION_FIELD_NUMBER = 72;
    private com.google.protobuf.ByteString ipIspDestination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipIspDestination = 72;</code>
     * @return Whether the ipIspDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpIspDestination() {
      return ((bitField2_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes ipIspDestination = 72;</code>
     * @return The ipIspDestination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpIspDestination() {
      return ipIspDestination_;
    }

    public static final int IPISPSOURCE_FIELD_NUMBER = 73;
    private com.google.protobuf.ByteString ipIspSource_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipIspSource = 73;</code>
     * @return Whether the ipIspSource field is set.
     */
    @java.lang.Override
    public boolean hasIpIspSource() {
      return ((bitField2_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes ipIspSource = 73;</code>
     * @return The ipIspSource.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpIspSource() {
      return ipIspSource_;
    }

    public static final int IPLATITUDEDESTINATION_FIELD_NUMBER = 74;
    private float ipLatitudeDestination_ = 0F;
    /**
     * <code>optional float ipLatitudeDestination = 74;</code>
     * @return Whether the ipLatitudeDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpLatitudeDestination() {
      return ((bitField2_ & 0x00000080) != 0);
    }
    /**
     * <code>optional float ipLatitudeDestination = 74;</code>
     * @return The ipLatitudeDestination.
     */
    @java.lang.Override
    public float getIpLatitudeDestination() {
      return ipLatitudeDestination_;
    }

    public static final int IPLATITUDESOURCE_FIELD_NUMBER = 75;
    private float ipLatitudeSource_ = 0F;
    /**
     * <code>optional float ipLatitudeSource = 75;</code>
     * @return Whether the ipLatitudeSource field is set.
     */
    @java.lang.Override
    public boolean hasIpLatitudeSource() {
      return ((bitField2_ & 0x00000100) != 0);
    }
    /**
     * <code>optional float ipLatitudeSource = 75;</code>
     * @return The ipLatitudeSource.
     */
    @java.lang.Override
    public float getIpLatitudeSource() {
      return ipLatitudeSource_;
    }

    public static final int IPLONGITUDEDESTINATION_FIELD_NUMBER = 76;
    private float ipLongitudeDestination_ = 0F;
    /**
     * <code>optional float ipLongitudeDestination = 76;</code>
     * @return Whether the ipLongitudeDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpLongitudeDestination() {
      return ((bitField2_ & 0x00000200) != 0);
    }
    /**
     * <code>optional float ipLongitudeDestination = 76;</code>
     * @return The ipLongitudeDestination.
     */
    @java.lang.Override
    public float getIpLongitudeDestination() {
      return ipLongitudeDestination_;
    }

    public static final int IPLONGITUDESOURCE_FIELD_NUMBER = 77;
    private float ipLongitudeSource_ = 0F;
    /**
     * <code>optional float ipLongitudeSource = 77;</code>
     * @return Whether the ipLongitudeSource field is set.
     */
    @java.lang.Override
    public boolean hasIpLongitudeSource() {
      return ((bitField2_ & 0x00000400) != 0);
    }
    /**
     * <code>optional float ipLongitudeSource = 77;</code>
     * @return The ipLongitudeSource.
     */
    @java.lang.Override
    public float getIpLongitudeSource() {
      return ipLongitudeSource_;
    }

    public static final int IPPACKETS_FIELD_NUMBER = 78;
    private int ipPackets_ = 0;
    /**
     * <code>optional uint32 ipPackets = 78;</code>
     * @return Whether the ipPackets field is set.
     */
    @java.lang.Override
    public boolean hasIpPackets() {
      return ((bitField2_ & 0x00000800) != 0);
    }
    /**
     * <code>optional uint32 ipPackets = 78;</code>
     * @return The ipPackets.
     */
    @java.lang.Override
    public int getIpPackets() {
      return ipPackets_;
    }

    public static final int IPPROTO_FIELD_NUMBER = 79;
    private int ipProto_ = 0;
    /**
     * <code>optional uint32 ipProto = 79;</code>
     * @return Whether the ipProto field is set.
     */
    @java.lang.Override
    public boolean hasIpProto() {
      return ((bitField2_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 ipProto = 79;</code>
     * @return The ipProto.
     */
    @java.lang.Override
    public int getIpProto() {
      return ipProto_;
    }

    public static final int IPPROTOPATH_FIELD_NUMBER = 80;
    private com.google.protobuf.ByteString ipProtoPath_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipProtoPath = 80;</code>
     * @return Whether the ipProtoPath field is set.
     */
    @java.lang.Override
    public boolean hasIpProtoPath() {
      return ((bitField2_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes ipProtoPath = 80;</code>
     * @return The ipProtoPath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpProtoPath() {
      return ipProtoPath_;
    }

    public static final int IPSOURCE_FIELD_NUMBER = 81;
    private int ipSource_ = 0;
    /**
     * <code>optional uint32 ipSource = 81;</code>
     * @return Whether the ipSource field is set.
     */
    @java.lang.Override
    public boolean hasIpSource() {
      return ((bitField2_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 ipSource = 81;</code>
     * @return The ipSource.
     */
    @java.lang.Override
    public int getIpSource() {
      return ipSource_;
    }

    public static final int IPSTATEDESTINATION_FIELD_NUMBER = 82;
    private com.google.protobuf.ByteString ipStateDestination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipStateDestination = 82;</code>
     * @return Whether the ipStateDestination field is set.
     */
    @java.lang.Override
    public boolean hasIpStateDestination() {
      return ((bitField2_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes ipStateDestination = 82;</code>
     * @return The ipStateDestination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpStateDestination() {
      return ipStateDestination_;
    }

    public static final int IPSTATESOURCE_FIELD_NUMBER = 83;
    private com.google.protobuf.ByteString ipStateSource_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipStateSource = 83;</code>
     * @return Whether the ipStateSource field is set.
     */
    @java.lang.Override
    public boolean hasIpStateSource() {
      return ((bitField2_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes ipStateSource = 83;</code>
     * @return The ipStateSource.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpStateSource() {
      return ipStateSource_;
    }

    public static final int IPUPPERPROTO_FIELD_NUMBER = 84;
    private com.google.protobuf.ByteString ipUpperProto_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipUpperProto = 84;</code>
     * @return Whether the ipUpperProto field is set.
     */
    @java.lang.Override
    public boolean hasIpUpperProto() {
      return ((bitField2_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes ipUpperProto = 84;</code>
     * @return The ipUpperProto.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpUpperProto() {
      return ipUpperProto_;
    }

    public static final int IPVERSION_FIELD_NUMBER = 85;
    private int ipVersion_ = 0;
    /**
     * <code>optional uint32 ipVersion = 85;</code>
     * @return Whether the ipVersion field is set.
     */
    @java.lang.Override
    public boolean hasIpVersion() {
      return ((bitField2_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 ipVersion = 85;</code>
     * @return The ipVersion.
     */
    @java.lang.Override
    public int getIpVersion() {
      return ipVersion_;
    }

    public static final int IPV6DESTINATION_FIELD_NUMBER = 86;
    private com.google.protobuf.ByteString ipv6Destination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipv6Destination = 86;</code>
     * @return Whether the ipv6Destination field is set.
     */
    @java.lang.Override
    public boolean hasIpv6Destination() {
      return ((bitField2_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes ipv6Destination = 86;</code>
     * @return The ipv6Destination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpv6Destination() {
      return ipv6Destination_;
    }

    public static final int IPV6SOURCE_FIELD_NUMBER = 87;
    private com.google.protobuf.ByteString ipv6Source_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ipv6Source = 87;</code>
     * @return Whether the ipv6Source field is set.
     */
    @java.lang.Override
    public boolean hasIpv6Source() {
      return ((bitField2_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes ipv6Source = 87;</code>
     * @return The ipv6Source.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getIpv6Source() {
      return ipv6Source_;
    }

    public static final int OUTIPDESTINATION_FIELD_NUMBER = 88;
    private int outIpDestination_ = 0;
    /**
     * <code>optional uint32 outIpDestination = 88;</code>
     * @return Whether the outIpDestination field is set.
     */
    @java.lang.Override
    public boolean hasOutIpDestination() {
      return ((bitField2_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint32 outIpDestination = 88;</code>
     * @return The outIpDestination.
     */
    @java.lang.Override
    public int getOutIpDestination() {
      return outIpDestination_;
    }

    public static final int OUTIPPROTO_FIELD_NUMBER = 89;
    private int outIpProto_ = 0;
    /**
     * <code>optional uint32 outIpProto = 89;</code>
     * @return Whether the outIpProto field is set.
     */
    @java.lang.Override
    public boolean hasOutIpProto() {
      return ((bitField2_ & 0x00400000) != 0);
    }
    /**
     * <code>optional uint32 outIpProto = 89;</code>
     * @return The outIpProto.
     */
    @java.lang.Override
    public int getOutIpProto() {
      return outIpProto_;
    }

    public static final int OUTIPSOURCE_FIELD_NUMBER = 90;
    private int outIpSource_ = 0;
    /**
     * <code>optional uint32 outIpSource = 90;</code>
     * @return Whether the outIpSource field is set.
     */
    @java.lang.Override
    public boolean hasOutIpSource() {
      return ((bitField2_ & 0x00800000) != 0);
    }
    /**
     * <code>optional uint32 outIpSource = 90;</code>
     * @return The outIpSource.
     */
    @java.lang.Override
    public int getOutIpSource() {
      return outIpSource_;
    }

    public static final int OUTIPVERSION_FIELD_NUMBER = 91;
    private int outIpVersion_ = 0;
    /**
     * <code>optional uint32 outIpVersion = 91;</code>
     * @return Whether the outIpVersion field is set.
     */
    @java.lang.Override
    public boolean hasOutIpVersion() {
      return ((bitField2_ & 0x01000000) != 0);
    }
    /**
     * <code>optional uint32 outIpVersion = 91;</code>
     * @return The outIpVersion.
     */
    @java.lang.Override
    public int getOutIpVersion() {
      return outIpVersion_;
    }

    public static final int OUTIPV6DESTINATION_FIELD_NUMBER = 92;
    private com.google.protobuf.ByteString outIpv6Destination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes outIpv6Destination = 92;</code>
     * @return Whether the outIpv6Destination field is set.
     */
    @java.lang.Override
    public boolean hasOutIpv6Destination() {
      return ((bitField2_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes outIpv6Destination = 92;</code>
     * @return The outIpv6Destination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getOutIpv6Destination() {
      return outIpv6Destination_;
    }

    public static final int OUTIPV6SOURCE_FIELD_NUMBER = 93;
    private com.google.protobuf.ByteString outIpv6Source_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes outIpv6Source = 93;</code>
     * @return Whether the outIpv6Source field is set.
     */
    @java.lang.Override
    public boolean hasOutIpv6Source() {
      return ((bitField2_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes outIpv6Source = 93;</code>
     * @return The outIpv6Source.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getOutIpv6Source() {
      return outIpv6Source_;
    }

    public static final int OUTPORTDESTINATION_FIELD_NUMBER = 94;
    private int outPortDestination_ = 0;
    /**
     * <code>optional uint32 outPortDestination = 94;</code>
     * @return Whether the outPortDestination field is set.
     */
    @java.lang.Override
    public boolean hasOutPortDestination() {
      return ((bitField2_ & 0x08000000) != 0);
    }
    /**
     * <code>optional uint32 outPortDestination = 94;</code>
     * @return The outPortDestination.
     */
    @java.lang.Override
    public int getOutPortDestination() {
      return outPortDestination_;
    }

    public static final int OUTPORTSOURCE_FIELD_NUMBER = 95;
    private int outPortSource_ = 0;
    /**
     * <code>optional uint32 outPortSource = 95;</code>
     * @return Whether the outPortSource field is set.
     */
    @java.lang.Override
    public boolean hasOutPortSource() {
      return ((bitField2_ & 0x10000000) != 0);
    }
    /**
     * <code>optional uint32 outPortSource = 95;</code>
     * @return The outPortSource.
     */
    @java.lang.Override
    public int getOutPortSource() {
      return outPortSource_;
    }

    public static final int PORTDESTINATION_FIELD_NUMBER = 96;
    private int portDestination_ = 0;
    /**
     * <code>optional uint32 portDestination = 96;</code>
     * @return Whether the portDestination field is set.
     */
    @java.lang.Override
    public boolean hasPortDestination() {
      return ((bitField2_ & 0x20000000) != 0);
    }
    /**
     * <code>optional uint32 portDestination = 96;</code>
     * @return The portDestination.
     */
    @java.lang.Override
    public int getPortDestination() {
      return portDestination_;
    }

    public static final int PORTSOURCE_FIELD_NUMBER = 97;
    private int portSource_ = 0;
    /**
     * <code>optional uint32 portSource = 97;</code>
     * @return Whether the portSource field is set.
     */
    @java.lang.Override
    public boolean hasPortSource() {
      return ((bitField2_ & 0x40000000) != 0);
    }
    /**
     * <code>optional uint32 portSource = 97;</code>
     * @return The portSource.
     */
    @java.lang.Override
    public int getPortSource() {
      return portSource_;
    }

    public static final int TCPFINISHED_FIELD_NUMBER = 98;
    private int tcpFinished_ = 0;
    /**
     * <code>optional uint32 tcpFinished = 98;</code>
     * @return Whether the tcpFinished field is set.
     */
    @java.lang.Override
    public boolean hasTcpFinished() {
      return ((bitField2_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint32 tcpFinished = 98;</code>
     * @return The tcpFinished.
     */
    @java.lang.Override
    public int getTcpFinished() {
      return tcpFinished_;
    }

    public static final int TCPFIRSTFLAG_FIELD_NUMBER = 99;
    private com.google.protobuf.ByteString tcpFirstFlag_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes tcpFirstFlag = 99;</code>
     * @return Whether the tcpFirstFlag field is set.
     */
    @java.lang.Override
    public boolean hasTcpFirstFlag() {
      return ((bitField3_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes tcpFirstFlag = 99;</code>
     * @return The tcpFirstFlag.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTcpFirstFlag() {
      return tcpFirstFlag_;
    }

    public static final int TCPFLAGSDESTINATION_FIELD_NUMBER = 100;
    private com.google.protobuf.ByteString tcpFlagsDestination_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes tcpFlagsDestination = 100;</code>
     * @return Whether the tcpFlagsDestination field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsDestination() {
      return ((bitField3_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes tcpFlagsDestination = 100;</code>
     * @return The tcpFlagsDestination.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTcpFlagsDestination() {
      return tcpFlagsDestination_;
    }

    public static final int TCPFLAGSSOURCE_FIELD_NUMBER = 101;
    private com.google.protobuf.ByteString tcpFlagsSource_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes tcpFlagsSource = 101;</code>
     * @return Whether the tcpFlagsSource field is set.
     */
    @java.lang.Override
    public boolean hasTcpFlagsSource() {
      return ((bitField3_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes tcpFlagsSource = 101;</code>
     * @return The tcpFlagsSource.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTcpFlagsSource() {
      return tcpFlagsSource_;
    }

    public static final int TRANSPACKETLENGTHDESTINATIONHIGHFREQUENCY_FIELD_NUMBER = 102;
    private int transPacketLengthDestinationHighFrequency_ = 0;
    /**
     * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
     * @return Whether the transPacketLengthDestinationHighFrequency field is set.
     */
    @java.lang.Override
    public boolean hasTransPacketLengthDestinationHighFrequency() {
      return ((bitField3_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
     * @return The transPacketLengthDestinationHighFrequency.
     */
    @java.lang.Override
    public int getTransPacketLengthDestinationHighFrequency() {
      return transPacketLengthDestinationHighFrequency_;
    }

    public static final int TRANSPACKETLENGTHSOURCEHIGHFREQUENCY_FIELD_NUMBER = 103;
    private int transPacketLengthSourceHighFrequency_ = 0;
    /**
     * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
     * @return Whether the transPacketLengthSourceHighFrequency field is set.
     */
    @java.lang.Override
    public boolean hasTransPacketLengthSourceHighFrequency() {
      return ((bitField3_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
     * @return The transPacketLengthSourceHighFrequency.
     */
    @java.lang.Override
    public int getTransPacketLengthSourceHighFrequency() {
      return transPacketLengthSourceHighFrequency_;
    }

    public static final int ISEND_FIELD_NUMBER = 104;
    private int isEnd_ = 0;
    /**
     * <code>optional uint32 isEnd = 104;</code>
     * @return Whether the isEnd field is set.
     */
    @java.lang.Override
    public boolean hasIsEnd() {
      return ((bitField3_ & 0x00000020) != 0);
    }
    /**
     * <code>optional uint32 isEnd = 104;</code>
     * @return The isEnd.
     */
    @java.lang.Override
    public int getIsEnd() {
      return isEnd_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBytes(1, portInfo_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, portInfoAtt_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, upPayLen_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt64(4, downPayLen_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBytes(5, tcpflag_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeUInt64(6, upLinkPktNum_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeUInt64(7, upLinkSize_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeUInt32(8, upLinkBigPktLen_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeUInt32(9, upLinkSmaPktLen_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeUInt64(10, upLinkBigPktInt_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeUInt64(11, upLinkSmaPktInt_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeUInt32(12, downLinkPktNum_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt32(13, downLinkSize_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeUInt32(14, downLinkBigPktLen_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeUInt32(15, downLinkSmaPktLen_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeUInt64(16, downLinkBigPktInt_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeUInt64(17, downLinkSmaPktInt_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeUInt32(18, firTtlByCli_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeUInt32(19, firTtlBySrv_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeUInt32(20, appDirec_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeUInt32(21, tcpFlagsFinCnt_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeUInt32(22, tcpFlagsSynCnt_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeUInt32(23, tcpFlagsRstCnt_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeUInt32(24, tcpFlagsPshCnt_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeUInt32(25, tcpFlagsAckCnt_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeUInt32(26, tcpFlagsUrgCnt_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeUInt32(27, tcpFlagsEceCnt_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeUInt32(28, tcpFlagsCwrCnt_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeUInt32(29, tcpFlagsNSCnt_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeUInt32(30, tcpFlagsSynAckCnt_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeBytes(31, etags_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeBytes(32, ttags_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeUInt32(33, upLinkChecksum_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeUInt32(34, downLinkChecksum_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        output.writeUInt64(35, upLinkDesBytes_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        output.writeUInt64(36, downLinkDesBytes_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        output.writeBytes(37, stream_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        output.writeBytes(38, upLinkStream_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        output.writeBytes(39, downLinkStream_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        output.writeBytes(40, transPayloadHex_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        output.writeBytes(41, upLinkTransPayHex_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        output.writeBytes(42, downLinkTransPayHex_);
      }
      for (int i = 0; i < upLinkPayLenSet_.size(); i++) {
        output.writeUInt32(43, upLinkPayLenSet_.getInt(i));
      }
      for (int i = 0; i < downLinkPayLenSet_.size(); i++) {
        output.writeUInt32(44, downLinkPayLenSet_.getInt(i));
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        output.writeUInt32(45, establish_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        output.writeUInt32(46, upLinkSynSeqNum_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        output.writeUInt32(47, downLinkSynSeqNum_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        output.writeUInt32(48, upLinkSynTcpWins_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        output.writeUInt32(49, downLinkSynTcpWins_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        output.writeBytes(50, upLinkTcpOpts_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        output.writeBytes(51, downLinkTcpOpts_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        output.writeUInt64(52, upSesBytes_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        output.writeUInt64(53, downSesbytes_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        output.writeUInt64(54, sesBytes_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        output.writeFloat(55, sesBytesRatio_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        output.writeFloat(56, payLenRatio_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        output.writeBytes(57, ipAsnDestination_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        output.writeBytes(58, ipAsnSource_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        output.writeBytes(59, ipBaseProto_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        output.writeUInt64(60, ipBeginTime_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        output.writeBytes(61, ipCityDestination_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        output.writeBytes(62, ipCitySource_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        output.writeBytes(63, ipCountryDestination_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        output.writeBytes(64, ipCountrySource_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        output.writeUInt64(65, ipDataBytes_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        output.writeUInt64(66, ipDesiredBytes_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        output.writeUInt64(67, ipDesiredBytesDestination_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        output.writeUInt64(68, ipDesiredBytesSource_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        output.writeUInt32(69, ipDestination_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        output.writeUInt32(70, ipDuration_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        output.writeUInt64(71, ipEndTime_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        output.writeBytes(72, ipIspDestination_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        output.writeBytes(73, ipIspSource_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        output.writeFloat(74, ipLatitudeDestination_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        output.writeFloat(75, ipLatitudeSource_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        output.writeFloat(76, ipLongitudeDestination_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        output.writeFloat(77, ipLongitudeSource_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        output.writeUInt32(78, ipPackets_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        output.writeUInt32(79, ipProto_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        output.writeBytes(80, ipProtoPath_);
      }
      if (((bitField2_ & 0x00004000) != 0)) {
        output.writeUInt32(81, ipSource_);
      }
      if (((bitField2_ & 0x00008000) != 0)) {
        output.writeBytes(82, ipStateDestination_);
      }
      if (((bitField2_ & 0x00010000) != 0)) {
        output.writeBytes(83, ipStateSource_);
      }
      if (((bitField2_ & 0x00020000) != 0)) {
        output.writeBytes(84, ipUpperProto_);
      }
      if (((bitField2_ & 0x00040000) != 0)) {
        output.writeUInt32(85, ipVersion_);
      }
      if (((bitField2_ & 0x00080000) != 0)) {
        output.writeBytes(86, ipv6Destination_);
      }
      if (((bitField2_ & 0x00100000) != 0)) {
        output.writeBytes(87, ipv6Source_);
      }
      if (((bitField2_ & 0x00200000) != 0)) {
        output.writeUInt32(88, outIpDestination_);
      }
      if (((bitField2_ & 0x00400000) != 0)) {
        output.writeUInt32(89, outIpProto_);
      }
      if (((bitField2_ & 0x00800000) != 0)) {
        output.writeUInt32(90, outIpSource_);
      }
      if (((bitField2_ & 0x01000000) != 0)) {
        output.writeUInt32(91, outIpVersion_);
      }
      if (((bitField2_ & 0x02000000) != 0)) {
        output.writeBytes(92, outIpv6Destination_);
      }
      if (((bitField2_ & 0x04000000) != 0)) {
        output.writeBytes(93, outIpv6Source_);
      }
      if (((bitField2_ & 0x08000000) != 0)) {
        output.writeUInt32(94, outPortDestination_);
      }
      if (((bitField2_ & 0x10000000) != 0)) {
        output.writeUInt32(95, outPortSource_);
      }
      if (((bitField2_ & 0x20000000) != 0)) {
        output.writeUInt32(96, portDestination_);
      }
      if (((bitField2_ & 0x40000000) != 0)) {
        output.writeUInt32(97, portSource_);
      }
      if (((bitField2_ & 0x80000000) != 0)) {
        output.writeUInt32(98, tcpFinished_);
      }
      if (((bitField3_ & 0x00000001) != 0)) {
        output.writeBytes(99, tcpFirstFlag_);
      }
      if (((bitField3_ & 0x00000002) != 0)) {
        output.writeBytes(100, tcpFlagsDestination_);
      }
      if (((bitField3_ & 0x00000004) != 0)) {
        output.writeBytes(101, tcpFlagsSource_);
      }
      if (((bitField3_ & 0x00000008) != 0)) {
        output.writeUInt32(102, transPacketLengthDestinationHighFrequency_);
      }
      if (((bitField3_ & 0x00000010) != 0)) {
        output.writeUInt32(103, transPacketLengthSourceHighFrequency_);
      }
      if (((bitField3_ & 0x00000020) != 0)) {
        output.writeUInt32(104, isEnd_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, portInfo_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, portInfoAtt_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, upPayLen_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, downPayLen_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, tcpflag_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, upLinkPktNum_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, upLinkSize_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, upLinkBigPktLen_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, upLinkSmaPktLen_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(10, upLinkBigPktInt_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, upLinkSmaPktInt_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(12, downLinkPktNum_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, downLinkSize_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, downLinkBigPktLen_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, downLinkSmaPktLen_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(16, downLinkBigPktInt_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(17, downLinkSmaPktInt_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, firTtlByCli_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(19, firTtlBySrv_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, appDirec_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(21, tcpFlagsFinCnt_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(22, tcpFlagsSynCnt_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(23, tcpFlagsRstCnt_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(24, tcpFlagsPshCnt_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(25, tcpFlagsAckCnt_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(26, tcpFlagsUrgCnt_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(27, tcpFlagsEceCnt_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, tcpFlagsCwrCnt_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(29, tcpFlagsNSCnt_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(30, tcpFlagsSynAckCnt_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(31, etags_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(32, ttags_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(33, upLinkChecksum_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(34, downLinkChecksum_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(35, upLinkDesBytes_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(36, downLinkDesBytes_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, stream_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(38, upLinkStream_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(39, downLinkStream_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(40, transPayloadHex_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(41, upLinkTransPayHex_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(42, downLinkTransPayHex_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < upLinkPayLenSet_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(upLinkPayLenSet_.getInt(i));
        }
        size += dataSize;
        size += 2 * getUpLinkPayLenSetList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < downLinkPayLenSet_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(downLinkPayLenSet_.getInt(i));
        }
        size += dataSize;
        size += 2 * getDownLinkPayLenSetList().size();
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(45, establish_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(46, upLinkSynSeqNum_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(47, downLinkSynSeqNum_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(48, upLinkSynTcpWins_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(49, downLinkSynTcpWins_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(50, upLinkTcpOpts_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(51, downLinkTcpOpts_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(52, upSesBytes_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(53, downSesbytes_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(54, sesBytes_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(55, sesBytesRatio_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(56, payLenRatio_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, ipAsnDestination_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, ipAsnSource_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(59, ipBaseProto_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(60, ipBeginTime_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(61, ipCityDestination_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(62, ipCitySource_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(63, ipCountryDestination_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(64, ipCountrySource_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(65, ipDataBytes_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(66, ipDesiredBytes_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(67, ipDesiredBytesDestination_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(68, ipDesiredBytesSource_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(69, ipDestination_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(70, ipDuration_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(71, ipEndTime_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(72, ipIspDestination_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(73, ipIspSource_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(74, ipLatitudeDestination_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(75, ipLatitudeSource_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(76, ipLongitudeDestination_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(77, ipLongitudeSource_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(78, ipPackets_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(79, ipProto_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(80, ipProtoPath_);
      }
      if (((bitField2_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(81, ipSource_);
      }
      if (((bitField2_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(82, ipStateDestination_);
      }
      if (((bitField2_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(83, ipStateSource_);
      }
      if (((bitField2_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(84, ipUpperProto_);
      }
      if (((bitField2_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(85, ipVersion_);
      }
      if (((bitField2_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(86, ipv6Destination_);
      }
      if (((bitField2_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(87, ipv6Source_);
      }
      if (((bitField2_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(88, outIpDestination_);
      }
      if (((bitField2_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(89, outIpProto_);
      }
      if (((bitField2_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(90, outIpSource_);
      }
      if (((bitField2_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(91, outIpVersion_);
      }
      if (((bitField2_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(92, outIpv6Destination_);
      }
      if (((bitField2_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(93, outIpv6Source_);
      }
      if (((bitField2_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(94, outPortDestination_);
      }
      if (((bitField2_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(95, outPortSource_);
      }
      if (((bitField2_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(96, portDestination_);
      }
      if (((bitField2_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(97, portSource_);
      }
      if (((bitField2_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(98, tcpFinished_);
      }
      if (((bitField3_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(99, tcpFirstFlag_);
      }
      if (((bitField3_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(100, tcpFlagsDestination_);
      }
      if (((bitField3_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(101, tcpFlagsSource_);
      }
      if (((bitField3_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(102, transPacketLengthDestinationHighFrequency_);
      }
      if (((bitField3_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(103, transPacketLengthSourceHighFrequency_);
      }
      if (((bitField3_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(104, isEnd_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof LinkInfoOuterClass.LinkInfo)) {
        return super.equals(obj);
      }
      LinkInfoOuterClass.LinkInfo other = (LinkInfoOuterClass.LinkInfo) obj;

      if (hasPortInfo() != other.hasPortInfo()) return false;
      if (hasPortInfo()) {
        if (!getPortInfo()
            .equals(other.getPortInfo())) return false;
      }
      if (hasPortInfoAtt() != other.hasPortInfoAtt()) return false;
      if (hasPortInfoAtt()) {
        if (!getPortInfoAtt()
            .equals(other.getPortInfoAtt())) return false;
      }
      if (hasUpPayLen() != other.hasUpPayLen()) return false;
      if (hasUpPayLen()) {
        if (getUpPayLen()
            != other.getUpPayLen()) return false;
      }
      if (hasDownPayLen() != other.hasDownPayLen()) return false;
      if (hasDownPayLen()) {
        if (getDownPayLen()
            != other.getDownPayLen()) return false;
      }
      if (hasTcpflag() != other.hasTcpflag()) return false;
      if (hasTcpflag()) {
        if (!getTcpflag()
            .equals(other.getTcpflag())) return false;
      }
      if (hasUpLinkPktNum() != other.hasUpLinkPktNum()) return false;
      if (hasUpLinkPktNum()) {
        if (getUpLinkPktNum()
            != other.getUpLinkPktNum()) return false;
      }
      if (hasUpLinkSize() != other.hasUpLinkSize()) return false;
      if (hasUpLinkSize()) {
        if (getUpLinkSize()
            != other.getUpLinkSize()) return false;
      }
      if (hasUpLinkBigPktLen() != other.hasUpLinkBigPktLen()) return false;
      if (hasUpLinkBigPktLen()) {
        if (getUpLinkBigPktLen()
            != other.getUpLinkBigPktLen()) return false;
      }
      if (hasUpLinkSmaPktLen() != other.hasUpLinkSmaPktLen()) return false;
      if (hasUpLinkSmaPktLen()) {
        if (getUpLinkSmaPktLen()
            != other.getUpLinkSmaPktLen()) return false;
      }
      if (hasUpLinkBigPktInt() != other.hasUpLinkBigPktInt()) return false;
      if (hasUpLinkBigPktInt()) {
        if (getUpLinkBigPktInt()
            != other.getUpLinkBigPktInt()) return false;
      }
      if (hasUpLinkSmaPktInt() != other.hasUpLinkSmaPktInt()) return false;
      if (hasUpLinkSmaPktInt()) {
        if (getUpLinkSmaPktInt()
            != other.getUpLinkSmaPktInt()) return false;
      }
      if (hasDownLinkPktNum() != other.hasDownLinkPktNum()) return false;
      if (hasDownLinkPktNum()) {
        if (getDownLinkPktNum()
            != other.getDownLinkPktNum()) return false;
      }
      if (hasDownLinkSize() != other.hasDownLinkSize()) return false;
      if (hasDownLinkSize()) {
        if (getDownLinkSize()
            != other.getDownLinkSize()) return false;
      }
      if (hasDownLinkBigPktLen() != other.hasDownLinkBigPktLen()) return false;
      if (hasDownLinkBigPktLen()) {
        if (getDownLinkBigPktLen()
            != other.getDownLinkBigPktLen()) return false;
      }
      if (hasDownLinkSmaPktLen() != other.hasDownLinkSmaPktLen()) return false;
      if (hasDownLinkSmaPktLen()) {
        if (getDownLinkSmaPktLen()
            != other.getDownLinkSmaPktLen()) return false;
      }
      if (hasDownLinkBigPktInt() != other.hasDownLinkBigPktInt()) return false;
      if (hasDownLinkBigPktInt()) {
        if (getDownLinkBigPktInt()
            != other.getDownLinkBigPktInt()) return false;
      }
      if (hasDownLinkSmaPktInt() != other.hasDownLinkSmaPktInt()) return false;
      if (hasDownLinkSmaPktInt()) {
        if (getDownLinkSmaPktInt()
            != other.getDownLinkSmaPktInt()) return false;
      }
      if (hasFirTtlByCli() != other.hasFirTtlByCli()) return false;
      if (hasFirTtlByCli()) {
        if (getFirTtlByCli()
            != other.getFirTtlByCli()) return false;
      }
      if (hasFirTtlBySrv() != other.hasFirTtlBySrv()) return false;
      if (hasFirTtlBySrv()) {
        if (getFirTtlBySrv()
            != other.getFirTtlBySrv()) return false;
      }
      if (hasAppDirec() != other.hasAppDirec()) return false;
      if (hasAppDirec()) {
        if (getAppDirec()
            != other.getAppDirec()) return false;
      }
      if (hasTcpFlagsFinCnt() != other.hasTcpFlagsFinCnt()) return false;
      if (hasTcpFlagsFinCnt()) {
        if (getTcpFlagsFinCnt()
            != other.getTcpFlagsFinCnt()) return false;
      }
      if (hasTcpFlagsSynCnt() != other.hasTcpFlagsSynCnt()) return false;
      if (hasTcpFlagsSynCnt()) {
        if (getTcpFlagsSynCnt()
            != other.getTcpFlagsSynCnt()) return false;
      }
      if (hasTcpFlagsRstCnt() != other.hasTcpFlagsRstCnt()) return false;
      if (hasTcpFlagsRstCnt()) {
        if (getTcpFlagsRstCnt()
            != other.getTcpFlagsRstCnt()) return false;
      }
      if (hasTcpFlagsPshCnt() != other.hasTcpFlagsPshCnt()) return false;
      if (hasTcpFlagsPshCnt()) {
        if (getTcpFlagsPshCnt()
            != other.getTcpFlagsPshCnt()) return false;
      }
      if (hasTcpFlagsAckCnt() != other.hasTcpFlagsAckCnt()) return false;
      if (hasTcpFlagsAckCnt()) {
        if (getTcpFlagsAckCnt()
            != other.getTcpFlagsAckCnt()) return false;
      }
      if (hasTcpFlagsUrgCnt() != other.hasTcpFlagsUrgCnt()) return false;
      if (hasTcpFlagsUrgCnt()) {
        if (getTcpFlagsUrgCnt()
            != other.getTcpFlagsUrgCnt()) return false;
      }
      if (hasTcpFlagsEceCnt() != other.hasTcpFlagsEceCnt()) return false;
      if (hasTcpFlagsEceCnt()) {
        if (getTcpFlagsEceCnt()
            != other.getTcpFlagsEceCnt()) return false;
      }
      if (hasTcpFlagsCwrCnt() != other.hasTcpFlagsCwrCnt()) return false;
      if (hasTcpFlagsCwrCnt()) {
        if (getTcpFlagsCwrCnt()
            != other.getTcpFlagsCwrCnt()) return false;
      }
      if (hasTcpFlagsNSCnt() != other.hasTcpFlagsNSCnt()) return false;
      if (hasTcpFlagsNSCnt()) {
        if (getTcpFlagsNSCnt()
            != other.getTcpFlagsNSCnt()) return false;
      }
      if (hasTcpFlagsSynAckCnt() != other.hasTcpFlagsSynAckCnt()) return false;
      if (hasTcpFlagsSynAckCnt()) {
        if (getTcpFlagsSynAckCnt()
            != other.getTcpFlagsSynAckCnt()) return false;
      }
      if (hasEtags() != other.hasEtags()) return false;
      if (hasEtags()) {
        if (!getEtags()
            .equals(other.getEtags())) return false;
      }
      if (hasTtags() != other.hasTtags()) return false;
      if (hasTtags()) {
        if (!getTtags()
            .equals(other.getTtags())) return false;
      }
      if (hasUpLinkChecksum() != other.hasUpLinkChecksum()) return false;
      if (hasUpLinkChecksum()) {
        if (getUpLinkChecksum()
            != other.getUpLinkChecksum()) return false;
      }
      if (hasDownLinkChecksum() != other.hasDownLinkChecksum()) return false;
      if (hasDownLinkChecksum()) {
        if (getDownLinkChecksum()
            != other.getDownLinkChecksum()) return false;
      }
      if (hasUpLinkDesBytes() != other.hasUpLinkDesBytes()) return false;
      if (hasUpLinkDesBytes()) {
        if (getUpLinkDesBytes()
            != other.getUpLinkDesBytes()) return false;
      }
      if (hasDownLinkDesBytes() != other.hasDownLinkDesBytes()) return false;
      if (hasDownLinkDesBytes()) {
        if (getDownLinkDesBytes()
            != other.getDownLinkDesBytes()) return false;
      }
      if (hasStream() != other.hasStream()) return false;
      if (hasStream()) {
        if (!getStream()
            .equals(other.getStream())) return false;
      }
      if (hasUpLinkStream() != other.hasUpLinkStream()) return false;
      if (hasUpLinkStream()) {
        if (!getUpLinkStream()
            .equals(other.getUpLinkStream())) return false;
      }
      if (hasDownLinkStream() != other.hasDownLinkStream()) return false;
      if (hasDownLinkStream()) {
        if (!getDownLinkStream()
            .equals(other.getDownLinkStream())) return false;
      }
      if (hasTransPayloadHex() != other.hasTransPayloadHex()) return false;
      if (hasTransPayloadHex()) {
        if (!getTransPayloadHex()
            .equals(other.getTransPayloadHex())) return false;
      }
      if (hasUpLinkTransPayHex() != other.hasUpLinkTransPayHex()) return false;
      if (hasUpLinkTransPayHex()) {
        if (!getUpLinkTransPayHex()
            .equals(other.getUpLinkTransPayHex())) return false;
      }
      if (hasDownLinkTransPayHex() != other.hasDownLinkTransPayHex()) return false;
      if (hasDownLinkTransPayHex()) {
        if (!getDownLinkTransPayHex()
            .equals(other.getDownLinkTransPayHex())) return false;
      }
      if (!getUpLinkPayLenSetList()
          .equals(other.getUpLinkPayLenSetList())) return false;
      if (!getDownLinkPayLenSetList()
          .equals(other.getDownLinkPayLenSetList())) return false;
      if (hasEstablish() != other.hasEstablish()) return false;
      if (hasEstablish()) {
        if (getEstablish()
            != other.getEstablish()) return false;
      }
      if (hasUpLinkSynSeqNum() != other.hasUpLinkSynSeqNum()) return false;
      if (hasUpLinkSynSeqNum()) {
        if (getUpLinkSynSeqNum()
            != other.getUpLinkSynSeqNum()) return false;
      }
      if (hasDownLinkSynSeqNum() != other.hasDownLinkSynSeqNum()) return false;
      if (hasDownLinkSynSeqNum()) {
        if (getDownLinkSynSeqNum()
            != other.getDownLinkSynSeqNum()) return false;
      }
      if (hasUpLinkSynTcpWins() != other.hasUpLinkSynTcpWins()) return false;
      if (hasUpLinkSynTcpWins()) {
        if (getUpLinkSynTcpWins()
            != other.getUpLinkSynTcpWins()) return false;
      }
      if (hasDownLinkSynTcpWins() != other.hasDownLinkSynTcpWins()) return false;
      if (hasDownLinkSynTcpWins()) {
        if (getDownLinkSynTcpWins()
            != other.getDownLinkSynTcpWins()) return false;
      }
      if (hasUpLinkTcpOpts() != other.hasUpLinkTcpOpts()) return false;
      if (hasUpLinkTcpOpts()) {
        if (!getUpLinkTcpOpts()
            .equals(other.getUpLinkTcpOpts())) return false;
      }
      if (hasDownLinkTcpOpts() != other.hasDownLinkTcpOpts()) return false;
      if (hasDownLinkTcpOpts()) {
        if (!getDownLinkTcpOpts()
            .equals(other.getDownLinkTcpOpts())) return false;
      }
      if (hasUpSesBytes() != other.hasUpSesBytes()) return false;
      if (hasUpSesBytes()) {
        if (getUpSesBytes()
            != other.getUpSesBytes()) return false;
      }
      if (hasDownSesbytes() != other.hasDownSesbytes()) return false;
      if (hasDownSesbytes()) {
        if (getDownSesbytes()
            != other.getDownSesbytes()) return false;
      }
      if (hasSesBytes() != other.hasSesBytes()) return false;
      if (hasSesBytes()) {
        if (getSesBytes()
            != other.getSesBytes()) return false;
      }
      if (hasSesBytesRatio() != other.hasSesBytesRatio()) return false;
      if (hasSesBytesRatio()) {
        if (java.lang.Float.floatToIntBits(getSesBytesRatio())
            != java.lang.Float.floatToIntBits(
                other.getSesBytesRatio())) return false;
      }
      if (hasPayLenRatio() != other.hasPayLenRatio()) return false;
      if (hasPayLenRatio()) {
        if (java.lang.Float.floatToIntBits(getPayLenRatio())
            != java.lang.Float.floatToIntBits(
                other.getPayLenRatio())) return false;
      }
      if (hasIpAsnDestination() != other.hasIpAsnDestination()) return false;
      if (hasIpAsnDestination()) {
        if (!getIpAsnDestination()
            .equals(other.getIpAsnDestination())) return false;
      }
      if (hasIpAsnSource() != other.hasIpAsnSource()) return false;
      if (hasIpAsnSource()) {
        if (!getIpAsnSource()
            .equals(other.getIpAsnSource())) return false;
      }
      if (hasIpBaseProto() != other.hasIpBaseProto()) return false;
      if (hasIpBaseProto()) {
        if (!getIpBaseProto()
            .equals(other.getIpBaseProto())) return false;
      }
      if (hasIpBeginTime() != other.hasIpBeginTime()) return false;
      if (hasIpBeginTime()) {
        if (getIpBeginTime()
            != other.getIpBeginTime()) return false;
      }
      if (hasIpCityDestination() != other.hasIpCityDestination()) return false;
      if (hasIpCityDestination()) {
        if (!getIpCityDestination()
            .equals(other.getIpCityDestination())) return false;
      }
      if (hasIpCitySource() != other.hasIpCitySource()) return false;
      if (hasIpCitySource()) {
        if (!getIpCitySource()
            .equals(other.getIpCitySource())) return false;
      }
      if (hasIpCountryDestination() != other.hasIpCountryDestination()) return false;
      if (hasIpCountryDestination()) {
        if (!getIpCountryDestination()
            .equals(other.getIpCountryDestination())) return false;
      }
      if (hasIpCountrySource() != other.hasIpCountrySource()) return false;
      if (hasIpCountrySource()) {
        if (!getIpCountrySource()
            .equals(other.getIpCountrySource())) return false;
      }
      if (hasIpDataBytes() != other.hasIpDataBytes()) return false;
      if (hasIpDataBytes()) {
        if (getIpDataBytes()
            != other.getIpDataBytes()) return false;
      }
      if (hasIpDesiredBytes() != other.hasIpDesiredBytes()) return false;
      if (hasIpDesiredBytes()) {
        if (getIpDesiredBytes()
            != other.getIpDesiredBytes()) return false;
      }
      if (hasIpDesiredBytesDestination() != other.hasIpDesiredBytesDestination()) return false;
      if (hasIpDesiredBytesDestination()) {
        if (getIpDesiredBytesDestination()
            != other.getIpDesiredBytesDestination()) return false;
      }
      if (hasIpDesiredBytesSource() != other.hasIpDesiredBytesSource()) return false;
      if (hasIpDesiredBytesSource()) {
        if (getIpDesiredBytesSource()
            != other.getIpDesiredBytesSource()) return false;
      }
      if (hasIpDestination() != other.hasIpDestination()) return false;
      if (hasIpDestination()) {
        if (getIpDestination()
            != other.getIpDestination()) return false;
      }
      if (hasIpDuration() != other.hasIpDuration()) return false;
      if (hasIpDuration()) {
        if (getIpDuration()
            != other.getIpDuration()) return false;
      }
      if (hasIpEndTime() != other.hasIpEndTime()) return false;
      if (hasIpEndTime()) {
        if (getIpEndTime()
            != other.getIpEndTime()) return false;
      }
      if (hasIpIspDestination() != other.hasIpIspDestination()) return false;
      if (hasIpIspDestination()) {
        if (!getIpIspDestination()
            .equals(other.getIpIspDestination())) return false;
      }
      if (hasIpIspSource() != other.hasIpIspSource()) return false;
      if (hasIpIspSource()) {
        if (!getIpIspSource()
            .equals(other.getIpIspSource())) return false;
      }
      if (hasIpLatitudeDestination() != other.hasIpLatitudeDestination()) return false;
      if (hasIpLatitudeDestination()) {
        if (java.lang.Float.floatToIntBits(getIpLatitudeDestination())
            != java.lang.Float.floatToIntBits(
                other.getIpLatitudeDestination())) return false;
      }
      if (hasIpLatitudeSource() != other.hasIpLatitudeSource()) return false;
      if (hasIpLatitudeSource()) {
        if (java.lang.Float.floatToIntBits(getIpLatitudeSource())
            != java.lang.Float.floatToIntBits(
                other.getIpLatitudeSource())) return false;
      }
      if (hasIpLongitudeDestination() != other.hasIpLongitudeDestination()) return false;
      if (hasIpLongitudeDestination()) {
        if (java.lang.Float.floatToIntBits(getIpLongitudeDestination())
            != java.lang.Float.floatToIntBits(
                other.getIpLongitudeDestination())) return false;
      }
      if (hasIpLongitudeSource() != other.hasIpLongitudeSource()) return false;
      if (hasIpLongitudeSource()) {
        if (java.lang.Float.floatToIntBits(getIpLongitudeSource())
            != java.lang.Float.floatToIntBits(
                other.getIpLongitudeSource())) return false;
      }
      if (hasIpPackets() != other.hasIpPackets()) return false;
      if (hasIpPackets()) {
        if (getIpPackets()
            != other.getIpPackets()) return false;
      }
      if (hasIpProto() != other.hasIpProto()) return false;
      if (hasIpProto()) {
        if (getIpProto()
            != other.getIpProto()) return false;
      }
      if (hasIpProtoPath() != other.hasIpProtoPath()) return false;
      if (hasIpProtoPath()) {
        if (!getIpProtoPath()
            .equals(other.getIpProtoPath())) return false;
      }
      if (hasIpSource() != other.hasIpSource()) return false;
      if (hasIpSource()) {
        if (getIpSource()
            != other.getIpSource()) return false;
      }
      if (hasIpStateDestination() != other.hasIpStateDestination()) return false;
      if (hasIpStateDestination()) {
        if (!getIpStateDestination()
            .equals(other.getIpStateDestination())) return false;
      }
      if (hasIpStateSource() != other.hasIpStateSource()) return false;
      if (hasIpStateSource()) {
        if (!getIpStateSource()
            .equals(other.getIpStateSource())) return false;
      }
      if (hasIpUpperProto() != other.hasIpUpperProto()) return false;
      if (hasIpUpperProto()) {
        if (!getIpUpperProto()
            .equals(other.getIpUpperProto())) return false;
      }
      if (hasIpVersion() != other.hasIpVersion()) return false;
      if (hasIpVersion()) {
        if (getIpVersion()
            != other.getIpVersion()) return false;
      }
      if (hasIpv6Destination() != other.hasIpv6Destination()) return false;
      if (hasIpv6Destination()) {
        if (!getIpv6Destination()
            .equals(other.getIpv6Destination())) return false;
      }
      if (hasIpv6Source() != other.hasIpv6Source()) return false;
      if (hasIpv6Source()) {
        if (!getIpv6Source()
            .equals(other.getIpv6Source())) return false;
      }
      if (hasOutIpDestination() != other.hasOutIpDestination()) return false;
      if (hasOutIpDestination()) {
        if (getOutIpDestination()
            != other.getOutIpDestination()) return false;
      }
      if (hasOutIpProto() != other.hasOutIpProto()) return false;
      if (hasOutIpProto()) {
        if (getOutIpProto()
            != other.getOutIpProto()) return false;
      }
      if (hasOutIpSource() != other.hasOutIpSource()) return false;
      if (hasOutIpSource()) {
        if (getOutIpSource()
            != other.getOutIpSource()) return false;
      }
      if (hasOutIpVersion() != other.hasOutIpVersion()) return false;
      if (hasOutIpVersion()) {
        if (getOutIpVersion()
            != other.getOutIpVersion()) return false;
      }
      if (hasOutIpv6Destination() != other.hasOutIpv6Destination()) return false;
      if (hasOutIpv6Destination()) {
        if (!getOutIpv6Destination()
            .equals(other.getOutIpv6Destination())) return false;
      }
      if (hasOutIpv6Source() != other.hasOutIpv6Source()) return false;
      if (hasOutIpv6Source()) {
        if (!getOutIpv6Source()
            .equals(other.getOutIpv6Source())) return false;
      }
      if (hasOutPortDestination() != other.hasOutPortDestination()) return false;
      if (hasOutPortDestination()) {
        if (getOutPortDestination()
            != other.getOutPortDestination()) return false;
      }
      if (hasOutPortSource() != other.hasOutPortSource()) return false;
      if (hasOutPortSource()) {
        if (getOutPortSource()
            != other.getOutPortSource()) return false;
      }
      if (hasPortDestination() != other.hasPortDestination()) return false;
      if (hasPortDestination()) {
        if (getPortDestination()
            != other.getPortDestination()) return false;
      }
      if (hasPortSource() != other.hasPortSource()) return false;
      if (hasPortSource()) {
        if (getPortSource()
            != other.getPortSource()) return false;
      }
      if (hasTcpFinished() != other.hasTcpFinished()) return false;
      if (hasTcpFinished()) {
        if (getTcpFinished()
            != other.getTcpFinished()) return false;
      }
      if (hasTcpFirstFlag() != other.hasTcpFirstFlag()) return false;
      if (hasTcpFirstFlag()) {
        if (!getTcpFirstFlag()
            .equals(other.getTcpFirstFlag())) return false;
      }
      if (hasTcpFlagsDestination() != other.hasTcpFlagsDestination()) return false;
      if (hasTcpFlagsDestination()) {
        if (!getTcpFlagsDestination()
            .equals(other.getTcpFlagsDestination())) return false;
      }
      if (hasTcpFlagsSource() != other.hasTcpFlagsSource()) return false;
      if (hasTcpFlagsSource()) {
        if (!getTcpFlagsSource()
            .equals(other.getTcpFlagsSource())) return false;
      }
      if (hasTransPacketLengthDestinationHighFrequency() != other.hasTransPacketLengthDestinationHighFrequency()) return false;
      if (hasTransPacketLengthDestinationHighFrequency()) {
        if (getTransPacketLengthDestinationHighFrequency()
            != other.getTransPacketLengthDestinationHighFrequency()) return false;
      }
      if (hasTransPacketLengthSourceHighFrequency() != other.hasTransPacketLengthSourceHighFrequency()) return false;
      if (hasTransPacketLengthSourceHighFrequency()) {
        if (getTransPacketLengthSourceHighFrequency()
            != other.getTransPacketLengthSourceHighFrequency()) return false;
      }
      if (hasIsEnd() != other.hasIsEnd()) return false;
      if (hasIsEnd()) {
        if (getIsEnd()
            != other.getIsEnd()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPortInfo()) {
        hash = (37 * hash) + PORTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getPortInfo().hashCode();
      }
      if (hasPortInfoAtt()) {
        hash = (37 * hash) + PORTINFOATT_FIELD_NUMBER;
        hash = (53 * hash) + getPortInfoAtt().hashCode();
      }
      if (hasUpPayLen()) {
        hash = (37 * hash) + UPPAYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getUpPayLen();
      }
      if (hasDownPayLen()) {
        hash = (37 * hash) + DOWNPAYLEN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDownPayLen());
      }
      if (hasTcpflag()) {
        hash = (37 * hash) + TCPFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getTcpflag().hashCode();
      }
      if (hasUpLinkPktNum()) {
        hash = (37 * hash) + UPLINKPKTNUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUpLinkPktNum());
      }
      if (hasUpLinkSize()) {
        hash = (37 * hash) + UPLINKSIZE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUpLinkSize());
      }
      if (hasUpLinkBigPktLen()) {
        hash = (37 * hash) + UPLINKBIGPKTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkBigPktLen();
      }
      if (hasUpLinkSmaPktLen()) {
        hash = (37 * hash) + UPLINKSMAPKTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkSmaPktLen();
      }
      if (hasUpLinkBigPktInt()) {
        hash = (37 * hash) + UPLINKBIGPKTINT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUpLinkBigPktInt());
      }
      if (hasUpLinkSmaPktInt()) {
        hash = (37 * hash) + UPLINKSMAPKTINT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUpLinkSmaPktInt());
      }
      if (hasDownLinkPktNum()) {
        hash = (37 * hash) + DOWNLINKPKTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkPktNum();
      }
      if (hasDownLinkSize()) {
        hash = (37 * hash) + DOWNLINKSIZE_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkSize();
      }
      if (hasDownLinkBigPktLen()) {
        hash = (37 * hash) + DOWNLINKBIGPKTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkBigPktLen();
      }
      if (hasDownLinkSmaPktLen()) {
        hash = (37 * hash) + DOWNLINKSMAPKTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkSmaPktLen();
      }
      if (hasDownLinkBigPktInt()) {
        hash = (37 * hash) + DOWNLINKBIGPKTINT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDownLinkBigPktInt());
      }
      if (hasDownLinkSmaPktInt()) {
        hash = (37 * hash) + DOWNLINKSMAPKTINT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDownLinkSmaPktInt());
      }
      if (hasFirTtlByCli()) {
        hash = (37 * hash) + FIRTTLBYCLI_FIELD_NUMBER;
        hash = (53 * hash) + getFirTtlByCli();
      }
      if (hasFirTtlBySrv()) {
        hash = (37 * hash) + FIRTTLBYSRV_FIELD_NUMBER;
        hash = (53 * hash) + getFirTtlBySrv();
      }
      if (hasAppDirec()) {
        hash = (37 * hash) + APPDIREC_FIELD_NUMBER;
        hash = (53 * hash) + getAppDirec();
      }
      if (hasTcpFlagsFinCnt()) {
        hash = (37 * hash) + TCPFLAGSFINCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsFinCnt();
      }
      if (hasTcpFlagsSynCnt()) {
        hash = (37 * hash) + TCPFLAGSSYNCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsSynCnt();
      }
      if (hasTcpFlagsRstCnt()) {
        hash = (37 * hash) + TCPFLAGSRSTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsRstCnt();
      }
      if (hasTcpFlagsPshCnt()) {
        hash = (37 * hash) + TCPFLAGSPSHCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsPshCnt();
      }
      if (hasTcpFlagsAckCnt()) {
        hash = (37 * hash) + TCPFLAGSACKCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsAckCnt();
      }
      if (hasTcpFlagsUrgCnt()) {
        hash = (37 * hash) + TCPFLAGSURGCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsUrgCnt();
      }
      if (hasTcpFlagsEceCnt()) {
        hash = (37 * hash) + TCPFLAGSECECNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsEceCnt();
      }
      if (hasTcpFlagsCwrCnt()) {
        hash = (37 * hash) + TCPFLAGSCWRCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsCwrCnt();
      }
      if (hasTcpFlagsNSCnt()) {
        hash = (37 * hash) + TCPFLAGSNSCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsNSCnt();
      }
      if (hasTcpFlagsSynAckCnt()) {
        hash = (37 * hash) + TCPFLAGSSYNACKCNT_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsSynAckCnt();
      }
      if (hasEtags()) {
        hash = (37 * hash) + ETAGS_FIELD_NUMBER;
        hash = (53 * hash) + getEtags().hashCode();
      }
      if (hasTtags()) {
        hash = (37 * hash) + TTAGS_FIELD_NUMBER;
        hash = (53 * hash) + getTtags().hashCode();
      }
      if (hasUpLinkChecksum()) {
        hash = (37 * hash) + UPLINKCHECKSUM_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkChecksum();
      }
      if (hasDownLinkChecksum()) {
        hash = (37 * hash) + DOWNLINKCHECKSUM_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkChecksum();
      }
      if (hasUpLinkDesBytes()) {
        hash = (37 * hash) + UPLINKDESBYTES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUpLinkDesBytes());
      }
      if (hasDownLinkDesBytes()) {
        hash = (37 * hash) + DOWNLINKDESBYTES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDownLinkDesBytes());
      }
      if (hasStream()) {
        hash = (37 * hash) + STREAM_FIELD_NUMBER;
        hash = (53 * hash) + getStream().hashCode();
      }
      if (hasUpLinkStream()) {
        hash = (37 * hash) + UPLINKSTREAM_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkStream().hashCode();
      }
      if (hasDownLinkStream()) {
        hash = (37 * hash) + DOWNLINKSTREAM_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkStream().hashCode();
      }
      if (hasTransPayloadHex()) {
        hash = (37 * hash) + TRANS_PAYLOAD_HEX_FIELD_NUMBER;
        hash = (53 * hash) + getTransPayloadHex().hashCode();
      }
      if (hasUpLinkTransPayHex()) {
        hash = (37 * hash) + UPLINKTRANSPAYHEX_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkTransPayHex().hashCode();
      }
      if (hasDownLinkTransPayHex()) {
        hash = (37 * hash) + DOWNLINKTRANSPAYHEX_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkTransPayHex().hashCode();
      }
      if (getUpLinkPayLenSetCount() > 0) {
        hash = (37 * hash) + UPLINKPAYLENSET_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkPayLenSetList().hashCode();
      }
      if (getDownLinkPayLenSetCount() > 0) {
        hash = (37 * hash) + DOWNLINKPAYLENSET_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkPayLenSetList().hashCode();
      }
      if (hasEstablish()) {
        hash = (37 * hash) + ESTABLISH_FIELD_NUMBER;
        hash = (53 * hash) + getEstablish();
      }
      if (hasUpLinkSynSeqNum()) {
        hash = (37 * hash) + UPLINKSYNSEQNUM_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkSynSeqNum();
      }
      if (hasDownLinkSynSeqNum()) {
        hash = (37 * hash) + DOWNLINKSYNSEQNUM_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkSynSeqNum();
      }
      if (hasUpLinkSynTcpWins()) {
        hash = (37 * hash) + UPLINKSYNTCPWINS_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkSynTcpWins();
      }
      if (hasDownLinkSynTcpWins()) {
        hash = (37 * hash) + DOWNLINKSYNTCPWINS_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkSynTcpWins();
      }
      if (hasUpLinkTcpOpts()) {
        hash = (37 * hash) + UPLINKTCPOPTS_FIELD_NUMBER;
        hash = (53 * hash) + getUpLinkTcpOpts().hashCode();
      }
      if (hasDownLinkTcpOpts()) {
        hash = (37 * hash) + DOWNLINKTCPOPTS_FIELD_NUMBER;
        hash = (53 * hash) + getDownLinkTcpOpts().hashCode();
      }
      if (hasUpSesBytes()) {
        hash = (37 * hash) + UPSESBYTES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getUpSesBytes());
      }
      if (hasDownSesbytes()) {
        hash = (37 * hash) + DOWNSESBYTES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getDownSesbytes());
      }
      if (hasSesBytes()) {
        hash = (37 * hash) + SESBYTES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSesBytes());
      }
      if (hasSesBytesRatio()) {
        hash = (37 * hash) + SESBYTESRATIO_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getSesBytesRatio());
      }
      if (hasPayLenRatio()) {
        hash = (37 * hash) + PAYLENRATIO_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getPayLenRatio());
      }
      if (hasIpAsnDestination()) {
        hash = (37 * hash) + IPASNDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpAsnDestination().hashCode();
      }
      if (hasIpAsnSource()) {
        hash = (37 * hash) + IPASNSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getIpAsnSource().hashCode();
      }
      if (hasIpBaseProto()) {
        hash = (37 * hash) + IPBASEPROTO_FIELD_NUMBER;
        hash = (53 * hash) + getIpBaseProto().hashCode();
      }
      if (hasIpBeginTime()) {
        hash = (37 * hash) + IPBEGINTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIpBeginTime());
      }
      if (hasIpCityDestination()) {
        hash = (37 * hash) + IPCITYDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpCityDestination().hashCode();
      }
      if (hasIpCitySource()) {
        hash = (37 * hash) + IPCITYSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getIpCitySource().hashCode();
      }
      if (hasIpCountryDestination()) {
        hash = (37 * hash) + IPCOUNTRYDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpCountryDestination().hashCode();
      }
      if (hasIpCountrySource()) {
        hash = (37 * hash) + IPCOUNTRYSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getIpCountrySource().hashCode();
      }
      if (hasIpDataBytes()) {
        hash = (37 * hash) + IPDATABYTES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIpDataBytes());
      }
      if (hasIpDesiredBytes()) {
        hash = (37 * hash) + IPDESIREDBYTES_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIpDesiredBytes());
      }
      if (hasIpDesiredBytesDestination()) {
        hash = (37 * hash) + IPDESIREDBYTESDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIpDesiredBytesDestination());
      }
      if (hasIpDesiredBytesSource()) {
        hash = (37 * hash) + IPDESIREDBYTESSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIpDesiredBytesSource());
      }
      if (hasIpDestination()) {
        hash = (37 * hash) + IPDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpDestination();
      }
      if (hasIpDuration()) {
        hash = (37 * hash) + IPDURATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpDuration();
      }
      if (hasIpEndTime()) {
        hash = (37 * hash) + IPENDTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIpEndTime());
      }
      if (hasIpIspDestination()) {
        hash = (37 * hash) + IPISPDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpIspDestination().hashCode();
      }
      if (hasIpIspSource()) {
        hash = (37 * hash) + IPISPSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getIpIspSource().hashCode();
      }
      if (hasIpLatitudeDestination()) {
        hash = (37 * hash) + IPLATITUDEDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getIpLatitudeDestination());
      }
      if (hasIpLatitudeSource()) {
        hash = (37 * hash) + IPLATITUDESOURCE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getIpLatitudeSource());
      }
      if (hasIpLongitudeDestination()) {
        hash = (37 * hash) + IPLONGITUDEDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getIpLongitudeDestination());
      }
      if (hasIpLongitudeSource()) {
        hash = (37 * hash) + IPLONGITUDESOURCE_FIELD_NUMBER;
        hash = (53 * hash) + java.lang.Float.floatToIntBits(
            getIpLongitudeSource());
      }
      if (hasIpPackets()) {
        hash = (37 * hash) + IPPACKETS_FIELD_NUMBER;
        hash = (53 * hash) + getIpPackets();
      }
      if (hasIpProto()) {
        hash = (37 * hash) + IPPROTO_FIELD_NUMBER;
        hash = (53 * hash) + getIpProto();
      }
      if (hasIpProtoPath()) {
        hash = (37 * hash) + IPPROTOPATH_FIELD_NUMBER;
        hash = (53 * hash) + getIpProtoPath().hashCode();
      }
      if (hasIpSource()) {
        hash = (37 * hash) + IPSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getIpSource();
      }
      if (hasIpStateDestination()) {
        hash = (37 * hash) + IPSTATEDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpStateDestination().hashCode();
      }
      if (hasIpStateSource()) {
        hash = (37 * hash) + IPSTATESOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getIpStateSource().hashCode();
      }
      if (hasIpUpperProto()) {
        hash = (37 * hash) + IPUPPERPROTO_FIELD_NUMBER;
        hash = (53 * hash) + getIpUpperProto().hashCode();
      }
      if (hasIpVersion()) {
        hash = (37 * hash) + IPVERSION_FIELD_NUMBER;
        hash = (53 * hash) + getIpVersion();
      }
      if (hasIpv6Destination()) {
        hash = (37 * hash) + IPV6DESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getIpv6Destination().hashCode();
      }
      if (hasIpv6Source()) {
        hash = (37 * hash) + IPV6SOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getIpv6Source().hashCode();
      }
      if (hasOutIpDestination()) {
        hash = (37 * hash) + OUTIPDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getOutIpDestination();
      }
      if (hasOutIpProto()) {
        hash = (37 * hash) + OUTIPPROTO_FIELD_NUMBER;
        hash = (53 * hash) + getOutIpProto();
      }
      if (hasOutIpSource()) {
        hash = (37 * hash) + OUTIPSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getOutIpSource();
      }
      if (hasOutIpVersion()) {
        hash = (37 * hash) + OUTIPVERSION_FIELD_NUMBER;
        hash = (53 * hash) + getOutIpVersion();
      }
      if (hasOutIpv6Destination()) {
        hash = (37 * hash) + OUTIPV6DESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getOutIpv6Destination().hashCode();
      }
      if (hasOutIpv6Source()) {
        hash = (37 * hash) + OUTIPV6SOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getOutIpv6Source().hashCode();
      }
      if (hasOutPortDestination()) {
        hash = (37 * hash) + OUTPORTDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getOutPortDestination();
      }
      if (hasOutPortSource()) {
        hash = (37 * hash) + OUTPORTSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getOutPortSource();
      }
      if (hasPortDestination()) {
        hash = (37 * hash) + PORTDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getPortDestination();
      }
      if (hasPortSource()) {
        hash = (37 * hash) + PORTSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getPortSource();
      }
      if (hasTcpFinished()) {
        hash = (37 * hash) + TCPFINISHED_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFinished();
      }
      if (hasTcpFirstFlag()) {
        hash = (37 * hash) + TCPFIRSTFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFirstFlag().hashCode();
      }
      if (hasTcpFlagsDestination()) {
        hash = (37 * hash) + TCPFLAGSDESTINATION_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsDestination().hashCode();
      }
      if (hasTcpFlagsSource()) {
        hash = (37 * hash) + TCPFLAGSSOURCE_FIELD_NUMBER;
        hash = (53 * hash) + getTcpFlagsSource().hashCode();
      }
      if (hasTransPacketLengthDestinationHighFrequency()) {
        hash = (37 * hash) + TRANSPACKETLENGTHDESTINATIONHIGHFREQUENCY_FIELD_NUMBER;
        hash = (53 * hash) + getTransPacketLengthDestinationHighFrequency();
      }
      if (hasTransPacketLengthSourceHighFrequency()) {
        hash = (37 * hash) + TRANSPACKETLENGTHSOURCEHIGHFREQUENCY_FIELD_NUMBER;
        hash = (53 * hash) + getTransPacketLengthSourceHighFrequency();
      }
      if (hasIsEnd()) {
        hash = (37 * hash) + ISEND_FIELD_NUMBER;
        hash = (53 * hash) + getIsEnd();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static LinkInfoOuterClass.LinkInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static LinkInfoOuterClass.LinkInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static LinkInfoOuterClass.LinkInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static LinkInfoOuterClass.LinkInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(LinkInfoOuterClass.LinkInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code LinkInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:LinkInfo)
        LinkInfoOuterClass.LinkInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return LinkInfoOuterClass.internal_static_LinkInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return LinkInfoOuterClass.internal_static_LinkInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                LinkInfoOuterClass.LinkInfo.class, LinkInfoOuterClass.LinkInfo.Builder.class);
      }

      // Construct using LinkInfoOuterClass.LinkInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        bitField2_ = 0;
        bitField3_ = 0;
        portInfo_ = com.google.protobuf.ByteString.EMPTY;
        portInfoAtt_ = com.google.protobuf.ByteString.EMPTY;
        upPayLen_ = 0;
        downPayLen_ = 0L;
        tcpflag_ = com.google.protobuf.ByteString.EMPTY;
        upLinkPktNum_ = 0L;
        upLinkSize_ = 0L;
        upLinkBigPktLen_ = 0;
        upLinkSmaPktLen_ = 0;
        upLinkBigPktInt_ = 0L;
        upLinkSmaPktInt_ = 0L;
        downLinkPktNum_ = 0;
        downLinkSize_ = 0;
        downLinkBigPktLen_ = 0;
        downLinkSmaPktLen_ = 0;
        downLinkBigPktInt_ = 0L;
        downLinkSmaPktInt_ = 0L;
        firTtlByCli_ = 0;
        firTtlBySrv_ = 0;
        appDirec_ = 0;
        tcpFlagsFinCnt_ = 0;
        tcpFlagsSynCnt_ = 0;
        tcpFlagsRstCnt_ = 0;
        tcpFlagsPshCnt_ = 0;
        tcpFlagsAckCnt_ = 0;
        tcpFlagsUrgCnt_ = 0;
        tcpFlagsEceCnt_ = 0;
        tcpFlagsCwrCnt_ = 0;
        tcpFlagsNSCnt_ = 0;
        tcpFlagsSynAckCnt_ = 0;
        etags_ = com.google.protobuf.ByteString.EMPTY;
        ttags_ = com.google.protobuf.ByteString.EMPTY;
        upLinkChecksum_ = 0;
        downLinkChecksum_ = 0;
        upLinkDesBytes_ = 0L;
        downLinkDesBytes_ = 0L;
        stream_ = com.google.protobuf.ByteString.EMPTY;
        upLinkStream_ = com.google.protobuf.ByteString.EMPTY;
        downLinkStream_ = com.google.protobuf.ByteString.EMPTY;
        transPayloadHex_ = com.google.protobuf.ByteString.EMPTY;
        upLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
        downLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
        upLinkPayLenSet_ = emptyIntList();
        downLinkPayLenSet_ = emptyIntList();
        establish_ = 0;
        upLinkSynSeqNum_ = 0;
        downLinkSynSeqNum_ = 0;
        upLinkSynTcpWins_ = 0;
        downLinkSynTcpWins_ = 0;
        upLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
        downLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
        upSesBytes_ = 0L;
        downSesbytes_ = 0L;
        sesBytes_ = 0L;
        sesBytesRatio_ = 0F;
        payLenRatio_ = 0F;
        ipAsnDestination_ = com.google.protobuf.ByteString.EMPTY;
        ipAsnSource_ = com.google.protobuf.ByteString.EMPTY;
        ipBaseProto_ = com.google.protobuf.ByteString.EMPTY;
        ipBeginTime_ = 0L;
        ipCityDestination_ = com.google.protobuf.ByteString.EMPTY;
        ipCitySource_ = com.google.protobuf.ByteString.EMPTY;
        ipCountryDestination_ = com.google.protobuf.ByteString.EMPTY;
        ipCountrySource_ = com.google.protobuf.ByteString.EMPTY;
        ipDataBytes_ = 0L;
        ipDesiredBytes_ = 0L;
        ipDesiredBytesDestination_ = 0L;
        ipDesiredBytesSource_ = 0L;
        ipDestination_ = 0;
        ipDuration_ = 0;
        ipEndTime_ = 0L;
        ipIspDestination_ = com.google.protobuf.ByteString.EMPTY;
        ipIspSource_ = com.google.protobuf.ByteString.EMPTY;
        ipLatitudeDestination_ = 0F;
        ipLatitudeSource_ = 0F;
        ipLongitudeDestination_ = 0F;
        ipLongitudeSource_ = 0F;
        ipPackets_ = 0;
        ipProto_ = 0;
        ipProtoPath_ = com.google.protobuf.ByteString.EMPTY;
        ipSource_ = 0;
        ipStateDestination_ = com.google.protobuf.ByteString.EMPTY;
        ipStateSource_ = com.google.protobuf.ByteString.EMPTY;
        ipUpperProto_ = com.google.protobuf.ByteString.EMPTY;
        ipVersion_ = 0;
        ipv6Destination_ = com.google.protobuf.ByteString.EMPTY;
        ipv6Source_ = com.google.protobuf.ByteString.EMPTY;
        outIpDestination_ = 0;
        outIpProto_ = 0;
        outIpSource_ = 0;
        outIpVersion_ = 0;
        outIpv6Destination_ = com.google.protobuf.ByteString.EMPTY;
        outIpv6Source_ = com.google.protobuf.ByteString.EMPTY;
        outPortDestination_ = 0;
        outPortSource_ = 0;
        portDestination_ = 0;
        portSource_ = 0;
        tcpFinished_ = 0;
        tcpFirstFlag_ = com.google.protobuf.ByteString.EMPTY;
        tcpFlagsDestination_ = com.google.protobuf.ByteString.EMPTY;
        tcpFlagsSource_ = com.google.protobuf.ByteString.EMPTY;
        transPacketLengthDestinationHighFrequency_ = 0;
        transPacketLengthSourceHighFrequency_ = 0;
        isEnd_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return LinkInfoOuterClass.internal_static_LinkInfo_descriptor;
      }

      @java.lang.Override
      public LinkInfoOuterClass.LinkInfo getDefaultInstanceForType() {
        return LinkInfoOuterClass.LinkInfo.getDefaultInstance();
      }

      @java.lang.Override
      public LinkInfoOuterClass.LinkInfo build() {
        LinkInfoOuterClass.LinkInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public LinkInfoOuterClass.LinkInfo buildPartial() {
        LinkInfoOuterClass.LinkInfo result = new LinkInfoOuterClass.LinkInfo(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        if (bitField2_ != 0) { buildPartial2(result); }
        if (bitField3_ != 0) { buildPartial3(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(LinkInfoOuterClass.LinkInfo result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.portInfo_ = portInfo_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.portInfoAtt_ = portInfoAtt_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.upPayLen_ = upPayLen_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.downPayLen_ = downPayLen_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.tcpflag_ = tcpflag_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.upLinkPktNum_ = upLinkPktNum_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.upLinkSize_ = upLinkSize_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.upLinkBigPktLen_ = upLinkBigPktLen_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.upLinkSmaPktLen_ = upLinkSmaPktLen_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.upLinkBigPktInt_ = upLinkBigPktInt_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.upLinkSmaPktInt_ = upLinkSmaPktInt_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.downLinkPktNum_ = downLinkPktNum_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.downLinkSize_ = downLinkSize_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.downLinkBigPktLen_ = downLinkBigPktLen_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.downLinkSmaPktLen_ = downLinkSmaPktLen_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.downLinkBigPktInt_ = downLinkBigPktInt_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.downLinkSmaPktInt_ = downLinkSmaPktInt_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.firTtlByCli_ = firTtlByCli_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.firTtlBySrv_ = firTtlBySrv_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.appDirec_ = appDirec_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.tcpFlagsFinCnt_ = tcpFlagsFinCnt_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.tcpFlagsSynCnt_ = tcpFlagsSynCnt_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.tcpFlagsRstCnt_ = tcpFlagsRstCnt_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.tcpFlagsPshCnt_ = tcpFlagsPshCnt_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.tcpFlagsAckCnt_ = tcpFlagsAckCnt_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.tcpFlagsUrgCnt_ = tcpFlagsUrgCnt_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.tcpFlagsEceCnt_ = tcpFlagsEceCnt_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.tcpFlagsCwrCnt_ = tcpFlagsCwrCnt_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.tcpFlagsNSCnt_ = tcpFlagsNSCnt_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.tcpFlagsSynAckCnt_ = tcpFlagsSynAckCnt_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.etags_ = etags_;
          to_bitField0_ |= 0x40000000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.ttags_ = ttags_;
          to_bitField0_ |= 0x80000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(LinkInfoOuterClass.LinkInfo result) {
        int from_bitField1_ = bitField1_;
        int to_bitField1_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.upLinkChecksum_ = upLinkChecksum_;
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.downLinkChecksum_ = downLinkChecksum_;
          to_bitField1_ |= 0x00000002;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.upLinkDesBytes_ = upLinkDesBytes_;
          to_bitField1_ |= 0x00000004;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.downLinkDesBytes_ = downLinkDesBytes_;
          to_bitField1_ |= 0x00000008;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.stream_ = stream_;
          to_bitField1_ |= 0x00000010;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.upLinkStream_ = upLinkStream_;
          to_bitField1_ |= 0x00000020;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.downLinkStream_ = downLinkStream_;
          to_bitField1_ |= 0x00000040;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          result.transPayloadHex_ = transPayloadHex_;
          to_bitField1_ |= 0x00000080;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.upLinkTransPayHex_ = upLinkTransPayHex_;
          to_bitField1_ |= 0x00000100;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          result.downLinkTransPayHex_ = downLinkTransPayHex_;
          to_bitField1_ |= 0x00000200;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          upLinkPayLenSet_.makeImmutable();
          result.upLinkPayLenSet_ = upLinkPayLenSet_;
        }
        if (((from_bitField1_ & 0x00000800) != 0)) {
          downLinkPayLenSet_.makeImmutable();
          result.downLinkPayLenSet_ = downLinkPayLenSet_;
        }
        if (((from_bitField1_ & 0x00001000) != 0)) {
          result.establish_ = establish_;
          to_bitField1_ |= 0x00000400;
        }
        if (((from_bitField1_ & 0x00002000) != 0)) {
          result.upLinkSynSeqNum_ = upLinkSynSeqNum_;
          to_bitField1_ |= 0x00000800;
        }
        if (((from_bitField1_ & 0x00004000) != 0)) {
          result.downLinkSynSeqNum_ = downLinkSynSeqNum_;
          to_bitField1_ |= 0x00001000;
        }
        if (((from_bitField1_ & 0x00008000) != 0)) {
          result.upLinkSynTcpWins_ = upLinkSynTcpWins_;
          to_bitField1_ |= 0x00002000;
        }
        if (((from_bitField1_ & 0x00010000) != 0)) {
          result.downLinkSynTcpWins_ = downLinkSynTcpWins_;
          to_bitField1_ |= 0x00004000;
        }
        if (((from_bitField1_ & 0x00020000) != 0)) {
          result.upLinkTcpOpts_ = upLinkTcpOpts_;
          to_bitField1_ |= 0x00008000;
        }
        if (((from_bitField1_ & 0x00040000) != 0)) {
          result.downLinkTcpOpts_ = downLinkTcpOpts_;
          to_bitField1_ |= 0x00010000;
        }
        if (((from_bitField1_ & 0x00080000) != 0)) {
          result.upSesBytes_ = upSesBytes_;
          to_bitField1_ |= 0x00020000;
        }
        if (((from_bitField1_ & 0x00100000) != 0)) {
          result.downSesbytes_ = downSesbytes_;
          to_bitField1_ |= 0x00040000;
        }
        if (((from_bitField1_ & 0x00200000) != 0)) {
          result.sesBytes_ = sesBytes_;
          to_bitField1_ |= 0x00080000;
        }
        if (((from_bitField1_ & 0x00400000) != 0)) {
          result.sesBytesRatio_ = sesBytesRatio_;
          to_bitField1_ |= 0x00100000;
        }
        if (((from_bitField1_ & 0x00800000) != 0)) {
          result.payLenRatio_ = payLenRatio_;
          to_bitField1_ |= 0x00200000;
        }
        if (((from_bitField1_ & 0x01000000) != 0)) {
          result.ipAsnDestination_ = ipAsnDestination_;
          to_bitField1_ |= 0x00400000;
        }
        if (((from_bitField1_ & 0x02000000) != 0)) {
          result.ipAsnSource_ = ipAsnSource_;
          to_bitField1_ |= 0x00800000;
        }
        if (((from_bitField1_ & 0x04000000) != 0)) {
          result.ipBaseProto_ = ipBaseProto_;
          to_bitField1_ |= 0x01000000;
        }
        if (((from_bitField1_ & 0x08000000) != 0)) {
          result.ipBeginTime_ = ipBeginTime_;
          to_bitField1_ |= 0x02000000;
        }
        if (((from_bitField1_ & 0x10000000) != 0)) {
          result.ipCityDestination_ = ipCityDestination_;
          to_bitField1_ |= 0x04000000;
        }
        if (((from_bitField1_ & 0x20000000) != 0)) {
          result.ipCitySource_ = ipCitySource_;
          to_bitField1_ |= 0x08000000;
        }
        if (((from_bitField1_ & 0x40000000) != 0)) {
          result.ipCountryDestination_ = ipCountryDestination_;
          to_bitField1_ |= 0x10000000;
        }
        if (((from_bitField1_ & 0x80000000) != 0)) {
          result.ipCountrySource_ = ipCountrySource_;
          to_bitField1_ |= 0x20000000;
        }
        result.bitField1_ |= to_bitField1_;
      }

      private void buildPartial2(LinkInfoOuterClass.LinkInfo result) {
        int from_bitField2_ = bitField2_;
        int to_bitField1_ = 0;
        if (((from_bitField2_ & 0x00000001) != 0)) {
          result.ipDataBytes_ = ipDataBytes_;
          to_bitField1_ |= 0x40000000;
        }
        if (((from_bitField2_ & 0x00000002) != 0)) {
          result.ipDesiredBytes_ = ipDesiredBytes_;
          to_bitField1_ |= 0x80000000;
        }
        int to_bitField2_ = 0;
        if (((from_bitField2_ & 0x00000004) != 0)) {
          result.ipDesiredBytesDestination_ = ipDesiredBytesDestination_;
          to_bitField2_ |= 0x00000001;
        }
        if (((from_bitField2_ & 0x00000008) != 0)) {
          result.ipDesiredBytesSource_ = ipDesiredBytesSource_;
          to_bitField2_ |= 0x00000002;
        }
        if (((from_bitField2_ & 0x00000010) != 0)) {
          result.ipDestination_ = ipDestination_;
          to_bitField2_ |= 0x00000004;
        }
        if (((from_bitField2_ & 0x00000020) != 0)) {
          result.ipDuration_ = ipDuration_;
          to_bitField2_ |= 0x00000008;
        }
        if (((from_bitField2_ & 0x00000040) != 0)) {
          result.ipEndTime_ = ipEndTime_;
          to_bitField2_ |= 0x00000010;
        }
        if (((from_bitField2_ & 0x00000080) != 0)) {
          result.ipIspDestination_ = ipIspDestination_;
          to_bitField2_ |= 0x00000020;
        }
        if (((from_bitField2_ & 0x00000100) != 0)) {
          result.ipIspSource_ = ipIspSource_;
          to_bitField2_ |= 0x00000040;
        }
        if (((from_bitField2_ & 0x00000200) != 0)) {
          result.ipLatitudeDestination_ = ipLatitudeDestination_;
          to_bitField2_ |= 0x00000080;
        }
        if (((from_bitField2_ & 0x00000400) != 0)) {
          result.ipLatitudeSource_ = ipLatitudeSource_;
          to_bitField2_ |= 0x00000100;
        }
        if (((from_bitField2_ & 0x00000800) != 0)) {
          result.ipLongitudeDestination_ = ipLongitudeDestination_;
          to_bitField2_ |= 0x00000200;
        }
        if (((from_bitField2_ & 0x00001000) != 0)) {
          result.ipLongitudeSource_ = ipLongitudeSource_;
          to_bitField2_ |= 0x00000400;
        }
        if (((from_bitField2_ & 0x00002000) != 0)) {
          result.ipPackets_ = ipPackets_;
          to_bitField2_ |= 0x00000800;
        }
        if (((from_bitField2_ & 0x00004000) != 0)) {
          result.ipProto_ = ipProto_;
          to_bitField2_ |= 0x00001000;
        }
        if (((from_bitField2_ & 0x00008000) != 0)) {
          result.ipProtoPath_ = ipProtoPath_;
          to_bitField2_ |= 0x00002000;
        }
        if (((from_bitField2_ & 0x00010000) != 0)) {
          result.ipSource_ = ipSource_;
          to_bitField2_ |= 0x00004000;
        }
        if (((from_bitField2_ & 0x00020000) != 0)) {
          result.ipStateDestination_ = ipStateDestination_;
          to_bitField2_ |= 0x00008000;
        }
        if (((from_bitField2_ & 0x00040000) != 0)) {
          result.ipStateSource_ = ipStateSource_;
          to_bitField2_ |= 0x00010000;
        }
        if (((from_bitField2_ & 0x00080000) != 0)) {
          result.ipUpperProto_ = ipUpperProto_;
          to_bitField2_ |= 0x00020000;
        }
        if (((from_bitField2_ & 0x00100000) != 0)) {
          result.ipVersion_ = ipVersion_;
          to_bitField2_ |= 0x00040000;
        }
        if (((from_bitField2_ & 0x00200000) != 0)) {
          result.ipv6Destination_ = ipv6Destination_;
          to_bitField2_ |= 0x00080000;
        }
        if (((from_bitField2_ & 0x00400000) != 0)) {
          result.ipv6Source_ = ipv6Source_;
          to_bitField2_ |= 0x00100000;
        }
        if (((from_bitField2_ & 0x00800000) != 0)) {
          result.outIpDestination_ = outIpDestination_;
          to_bitField2_ |= 0x00200000;
        }
        if (((from_bitField2_ & 0x01000000) != 0)) {
          result.outIpProto_ = outIpProto_;
          to_bitField2_ |= 0x00400000;
        }
        if (((from_bitField2_ & 0x02000000) != 0)) {
          result.outIpSource_ = outIpSource_;
          to_bitField2_ |= 0x00800000;
        }
        if (((from_bitField2_ & 0x04000000) != 0)) {
          result.outIpVersion_ = outIpVersion_;
          to_bitField2_ |= 0x01000000;
        }
        if (((from_bitField2_ & 0x08000000) != 0)) {
          result.outIpv6Destination_ = outIpv6Destination_;
          to_bitField2_ |= 0x02000000;
        }
        if (((from_bitField2_ & 0x10000000) != 0)) {
          result.outIpv6Source_ = outIpv6Source_;
          to_bitField2_ |= 0x04000000;
        }
        if (((from_bitField2_ & 0x20000000) != 0)) {
          result.outPortDestination_ = outPortDestination_;
          to_bitField2_ |= 0x08000000;
        }
        if (((from_bitField2_ & 0x40000000) != 0)) {
          result.outPortSource_ = outPortSource_;
          to_bitField2_ |= 0x10000000;
        }
        if (((from_bitField2_ & 0x80000000) != 0)) {
          result.portDestination_ = portDestination_;
          to_bitField2_ |= 0x20000000;
        }
        result.bitField1_ |= to_bitField1_;
        result.bitField2_ |= to_bitField2_;
      }

      private void buildPartial3(LinkInfoOuterClass.LinkInfo result) {
        int from_bitField3_ = bitField3_;
        int to_bitField2_ = 0;
        if (((from_bitField3_ & 0x00000001) != 0)) {
          result.portSource_ = portSource_;
          to_bitField2_ |= 0x40000000;
        }
        if (((from_bitField3_ & 0x00000002) != 0)) {
          result.tcpFinished_ = tcpFinished_;
          to_bitField2_ |= 0x80000000;
        }
        int to_bitField3_ = 0;
        if (((from_bitField3_ & 0x00000004) != 0)) {
          result.tcpFirstFlag_ = tcpFirstFlag_;
          to_bitField3_ |= 0x00000001;
        }
        if (((from_bitField3_ & 0x00000008) != 0)) {
          result.tcpFlagsDestination_ = tcpFlagsDestination_;
          to_bitField3_ |= 0x00000002;
        }
        if (((from_bitField3_ & 0x00000010) != 0)) {
          result.tcpFlagsSource_ = tcpFlagsSource_;
          to_bitField3_ |= 0x00000004;
        }
        if (((from_bitField3_ & 0x00000020) != 0)) {
          result.transPacketLengthDestinationHighFrequency_ = transPacketLengthDestinationHighFrequency_;
          to_bitField3_ |= 0x00000008;
        }
        if (((from_bitField3_ & 0x00000040) != 0)) {
          result.transPacketLengthSourceHighFrequency_ = transPacketLengthSourceHighFrequency_;
          to_bitField3_ |= 0x00000010;
        }
        if (((from_bitField3_ & 0x00000080) != 0)) {
          result.isEnd_ = isEnd_;
          to_bitField3_ |= 0x00000020;
        }
        result.bitField2_ |= to_bitField2_;
        result.bitField3_ |= to_bitField3_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof LinkInfoOuterClass.LinkInfo) {
          return mergeFrom((LinkInfoOuterClass.LinkInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(LinkInfoOuterClass.LinkInfo other) {
        if (other == LinkInfoOuterClass.LinkInfo.getDefaultInstance()) return this;
        if (other.hasPortInfo()) {
          setPortInfo(other.getPortInfo());
        }
        if (other.hasPortInfoAtt()) {
          setPortInfoAtt(other.getPortInfoAtt());
        }
        if (other.hasUpPayLen()) {
          setUpPayLen(other.getUpPayLen());
        }
        if (other.hasDownPayLen()) {
          setDownPayLen(other.getDownPayLen());
        }
        if (other.hasTcpflag()) {
          setTcpflag(other.getTcpflag());
        }
        if (other.hasUpLinkPktNum()) {
          setUpLinkPktNum(other.getUpLinkPktNum());
        }
        if (other.hasUpLinkSize()) {
          setUpLinkSize(other.getUpLinkSize());
        }
        if (other.hasUpLinkBigPktLen()) {
          setUpLinkBigPktLen(other.getUpLinkBigPktLen());
        }
        if (other.hasUpLinkSmaPktLen()) {
          setUpLinkSmaPktLen(other.getUpLinkSmaPktLen());
        }
        if (other.hasUpLinkBigPktInt()) {
          setUpLinkBigPktInt(other.getUpLinkBigPktInt());
        }
        if (other.hasUpLinkSmaPktInt()) {
          setUpLinkSmaPktInt(other.getUpLinkSmaPktInt());
        }
        if (other.hasDownLinkPktNum()) {
          setDownLinkPktNum(other.getDownLinkPktNum());
        }
        if (other.hasDownLinkSize()) {
          setDownLinkSize(other.getDownLinkSize());
        }
        if (other.hasDownLinkBigPktLen()) {
          setDownLinkBigPktLen(other.getDownLinkBigPktLen());
        }
        if (other.hasDownLinkSmaPktLen()) {
          setDownLinkSmaPktLen(other.getDownLinkSmaPktLen());
        }
        if (other.hasDownLinkBigPktInt()) {
          setDownLinkBigPktInt(other.getDownLinkBigPktInt());
        }
        if (other.hasDownLinkSmaPktInt()) {
          setDownLinkSmaPktInt(other.getDownLinkSmaPktInt());
        }
        if (other.hasFirTtlByCli()) {
          setFirTtlByCli(other.getFirTtlByCli());
        }
        if (other.hasFirTtlBySrv()) {
          setFirTtlBySrv(other.getFirTtlBySrv());
        }
        if (other.hasAppDirec()) {
          setAppDirec(other.getAppDirec());
        }
        if (other.hasTcpFlagsFinCnt()) {
          setTcpFlagsFinCnt(other.getTcpFlagsFinCnt());
        }
        if (other.hasTcpFlagsSynCnt()) {
          setTcpFlagsSynCnt(other.getTcpFlagsSynCnt());
        }
        if (other.hasTcpFlagsRstCnt()) {
          setTcpFlagsRstCnt(other.getTcpFlagsRstCnt());
        }
        if (other.hasTcpFlagsPshCnt()) {
          setTcpFlagsPshCnt(other.getTcpFlagsPshCnt());
        }
        if (other.hasTcpFlagsAckCnt()) {
          setTcpFlagsAckCnt(other.getTcpFlagsAckCnt());
        }
        if (other.hasTcpFlagsUrgCnt()) {
          setTcpFlagsUrgCnt(other.getTcpFlagsUrgCnt());
        }
        if (other.hasTcpFlagsEceCnt()) {
          setTcpFlagsEceCnt(other.getTcpFlagsEceCnt());
        }
        if (other.hasTcpFlagsCwrCnt()) {
          setTcpFlagsCwrCnt(other.getTcpFlagsCwrCnt());
        }
        if (other.hasTcpFlagsNSCnt()) {
          setTcpFlagsNSCnt(other.getTcpFlagsNSCnt());
        }
        if (other.hasTcpFlagsSynAckCnt()) {
          setTcpFlagsSynAckCnt(other.getTcpFlagsSynAckCnt());
        }
        if (other.hasEtags()) {
          setEtags(other.getEtags());
        }
        if (other.hasTtags()) {
          setTtags(other.getTtags());
        }
        if (other.hasUpLinkChecksum()) {
          setUpLinkChecksum(other.getUpLinkChecksum());
        }
        if (other.hasDownLinkChecksum()) {
          setDownLinkChecksum(other.getDownLinkChecksum());
        }
        if (other.hasUpLinkDesBytes()) {
          setUpLinkDesBytes(other.getUpLinkDesBytes());
        }
        if (other.hasDownLinkDesBytes()) {
          setDownLinkDesBytes(other.getDownLinkDesBytes());
        }
        if (other.hasStream()) {
          setStream(other.getStream());
        }
        if (other.hasUpLinkStream()) {
          setUpLinkStream(other.getUpLinkStream());
        }
        if (other.hasDownLinkStream()) {
          setDownLinkStream(other.getDownLinkStream());
        }
        if (other.hasTransPayloadHex()) {
          setTransPayloadHex(other.getTransPayloadHex());
        }
        if (other.hasUpLinkTransPayHex()) {
          setUpLinkTransPayHex(other.getUpLinkTransPayHex());
        }
        if (other.hasDownLinkTransPayHex()) {
          setDownLinkTransPayHex(other.getDownLinkTransPayHex());
        }
        if (!other.upLinkPayLenSet_.isEmpty()) {
          if (upLinkPayLenSet_.isEmpty()) {
            upLinkPayLenSet_ = other.upLinkPayLenSet_;
            upLinkPayLenSet_.makeImmutable();
            bitField1_ |= 0x00000400;
          } else {
            ensureUpLinkPayLenSetIsMutable();
            upLinkPayLenSet_.addAll(other.upLinkPayLenSet_);
          }
          onChanged();
        }
        if (!other.downLinkPayLenSet_.isEmpty()) {
          if (downLinkPayLenSet_.isEmpty()) {
            downLinkPayLenSet_ = other.downLinkPayLenSet_;
            downLinkPayLenSet_.makeImmutable();
            bitField1_ |= 0x00000800;
          } else {
            ensureDownLinkPayLenSetIsMutable();
            downLinkPayLenSet_.addAll(other.downLinkPayLenSet_);
          }
          onChanged();
        }
        if (other.hasEstablish()) {
          setEstablish(other.getEstablish());
        }
        if (other.hasUpLinkSynSeqNum()) {
          setUpLinkSynSeqNum(other.getUpLinkSynSeqNum());
        }
        if (other.hasDownLinkSynSeqNum()) {
          setDownLinkSynSeqNum(other.getDownLinkSynSeqNum());
        }
        if (other.hasUpLinkSynTcpWins()) {
          setUpLinkSynTcpWins(other.getUpLinkSynTcpWins());
        }
        if (other.hasDownLinkSynTcpWins()) {
          setDownLinkSynTcpWins(other.getDownLinkSynTcpWins());
        }
        if (other.hasUpLinkTcpOpts()) {
          setUpLinkTcpOpts(other.getUpLinkTcpOpts());
        }
        if (other.hasDownLinkTcpOpts()) {
          setDownLinkTcpOpts(other.getDownLinkTcpOpts());
        }
        if (other.hasUpSesBytes()) {
          setUpSesBytes(other.getUpSesBytes());
        }
        if (other.hasDownSesbytes()) {
          setDownSesbytes(other.getDownSesbytes());
        }
        if (other.hasSesBytes()) {
          setSesBytes(other.getSesBytes());
        }
        if (other.hasSesBytesRatio()) {
          setSesBytesRatio(other.getSesBytesRatio());
        }
        if (other.hasPayLenRatio()) {
          setPayLenRatio(other.getPayLenRatio());
        }
        if (other.hasIpAsnDestination()) {
          setIpAsnDestination(other.getIpAsnDestination());
        }
        if (other.hasIpAsnSource()) {
          setIpAsnSource(other.getIpAsnSource());
        }
        if (other.hasIpBaseProto()) {
          setIpBaseProto(other.getIpBaseProto());
        }
        if (other.hasIpBeginTime()) {
          setIpBeginTime(other.getIpBeginTime());
        }
        if (other.hasIpCityDestination()) {
          setIpCityDestination(other.getIpCityDestination());
        }
        if (other.hasIpCitySource()) {
          setIpCitySource(other.getIpCitySource());
        }
        if (other.hasIpCountryDestination()) {
          setIpCountryDestination(other.getIpCountryDestination());
        }
        if (other.hasIpCountrySource()) {
          setIpCountrySource(other.getIpCountrySource());
        }
        if (other.hasIpDataBytes()) {
          setIpDataBytes(other.getIpDataBytes());
        }
        if (other.hasIpDesiredBytes()) {
          setIpDesiredBytes(other.getIpDesiredBytes());
        }
        if (other.hasIpDesiredBytesDestination()) {
          setIpDesiredBytesDestination(other.getIpDesiredBytesDestination());
        }
        if (other.hasIpDesiredBytesSource()) {
          setIpDesiredBytesSource(other.getIpDesiredBytesSource());
        }
        if (other.hasIpDestination()) {
          setIpDestination(other.getIpDestination());
        }
        if (other.hasIpDuration()) {
          setIpDuration(other.getIpDuration());
        }
        if (other.hasIpEndTime()) {
          setIpEndTime(other.getIpEndTime());
        }
        if (other.hasIpIspDestination()) {
          setIpIspDestination(other.getIpIspDestination());
        }
        if (other.hasIpIspSource()) {
          setIpIspSource(other.getIpIspSource());
        }
        if (other.hasIpLatitudeDestination()) {
          setIpLatitudeDestination(other.getIpLatitudeDestination());
        }
        if (other.hasIpLatitudeSource()) {
          setIpLatitudeSource(other.getIpLatitudeSource());
        }
        if (other.hasIpLongitudeDestination()) {
          setIpLongitudeDestination(other.getIpLongitudeDestination());
        }
        if (other.hasIpLongitudeSource()) {
          setIpLongitudeSource(other.getIpLongitudeSource());
        }
        if (other.hasIpPackets()) {
          setIpPackets(other.getIpPackets());
        }
        if (other.hasIpProto()) {
          setIpProto(other.getIpProto());
        }
        if (other.hasIpProtoPath()) {
          setIpProtoPath(other.getIpProtoPath());
        }
        if (other.hasIpSource()) {
          setIpSource(other.getIpSource());
        }
        if (other.hasIpStateDestination()) {
          setIpStateDestination(other.getIpStateDestination());
        }
        if (other.hasIpStateSource()) {
          setIpStateSource(other.getIpStateSource());
        }
        if (other.hasIpUpperProto()) {
          setIpUpperProto(other.getIpUpperProto());
        }
        if (other.hasIpVersion()) {
          setIpVersion(other.getIpVersion());
        }
        if (other.hasIpv6Destination()) {
          setIpv6Destination(other.getIpv6Destination());
        }
        if (other.hasIpv6Source()) {
          setIpv6Source(other.getIpv6Source());
        }
        if (other.hasOutIpDestination()) {
          setOutIpDestination(other.getOutIpDestination());
        }
        if (other.hasOutIpProto()) {
          setOutIpProto(other.getOutIpProto());
        }
        if (other.hasOutIpSource()) {
          setOutIpSource(other.getOutIpSource());
        }
        if (other.hasOutIpVersion()) {
          setOutIpVersion(other.getOutIpVersion());
        }
        if (other.hasOutIpv6Destination()) {
          setOutIpv6Destination(other.getOutIpv6Destination());
        }
        if (other.hasOutIpv6Source()) {
          setOutIpv6Source(other.getOutIpv6Source());
        }
        if (other.hasOutPortDestination()) {
          setOutPortDestination(other.getOutPortDestination());
        }
        if (other.hasOutPortSource()) {
          setOutPortSource(other.getOutPortSource());
        }
        if (other.hasPortDestination()) {
          setPortDestination(other.getPortDestination());
        }
        if (other.hasPortSource()) {
          setPortSource(other.getPortSource());
        }
        if (other.hasTcpFinished()) {
          setTcpFinished(other.getTcpFinished());
        }
        if (other.hasTcpFirstFlag()) {
          setTcpFirstFlag(other.getTcpFirstFlag());
        }
        if (other.hasTcpFlagsDestination()) {
          setTcpFlagsDestination(other.getTcpFlagsDestination());
        }
        if (other.hasTcpFlagsSource()) {
          setTcpFlagsSource(other.getTcpFlagsSource());
        }
        if (other.hasTransPacketLengthDestinationHighFrequency()) {
          setTransPacketLengthDestinationHighFrequency(other.getTransPacketLengthDestinationHighFrequency());
        }
        if (other.hasTransPacketLengthSourceHighFrequency()) {
          setTransPacketLengthSourceHighFrequency(other.getTransPacketLengthSourceHighFrequency());
        }
        if (other.hasIsEnd()) {
          setIsEnd(other.getIsEnd());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                portInfo_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                portInfoAtt_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                upPayLen_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                downPayLen_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                tcpflag_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                upLinkPktNum_ = input.readUInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                upLinkSize_ = input.readUInt64();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                upLinkBigPktLen_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                upLinkSmaPktLen_ = input.readUInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                upLinkBigPktInt_ = input.readUInt64();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 88: {
                upLinkSmaPktInt_ = input.readUInt64();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              case 96: {
                downLinkPktNum_ = input.readUInt32();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              case 104: {
                downLinkSize_ = input.readUInt32();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                downLinkBigPktLen_ = input.readUInt32();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 120: {
                downLinkSmaPktLen_ = input.readUInt32();
                bitField0_ |= 0x00004000;
                break;
              } // case 120
              case 128: {
                downLinkBigPktInt_ = input.readUInt64();
                bitField0_ |= 0x00008000;
                break;
              } // case 128
              case 136: {
                downLinkSmaPktInt_ = input.readUInt64();
                bitField0_ |= 0x00010000;
                break;
              } // case 136
              case 144: {
                firTtlByCli_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 152: {
                firTtlBySrv_ = input.readUInt32();
                bitField0_ |= 0x00040000;
                break;
              } // case 152
              case 160: {
                appDirec_ = input.readUInt32();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              case 168: {
                tcpFlagsFinCnt_ = input.readUInt32();
                bitField0_ |= 0x00100000;
                break;
              } // case 168
              case 176: {
                tcpFlagsSynCnt_ = input.readUInt32();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              case 184: {
                tcpFlagsRstCnt_ = input.readUInt32();
                bitField0_ |= 0x00400000;
                break;
              } // case 184
              case 192: {
                tcpFlagsPshCnt_ = input.readUInt32();
                bitField0_ |= 0x00800000;
                break;
              } // case 192
              case 200: {
                tcpFlagsAckCnt_ = input.readUInt32();
                bitField0_ |= 0x01000000;
                break;
              } // case 200
              case 208: {
                tcpFlagsUrgCnt_ = input.readUInt32();
                bitField0_ |= 0x02000000;
                break;
              } // case 208
              case 216: {
                tcpFlagsEceCnt_ = input.readUInt32();
                bitField0_ |= 0x04000000;
                break;
              } // case 216
              case 224: {
                tcpFlagsCwrCnt_ = input.readUInt32();
                bitField0_ |= 0x08000000;
                break;
              } // case 224
              case 232: {
                tcpFlagsNSCnt_ = input.readUInt32();
                bitField0_ |= 0x10000000;
                break;
              } // case 232
              case 240: {
                tcpFlagsSynAckCnt_ = input.readUInt32();
                bitField0_ |= 0x20000000;
                break;
              } // case 240
              case 250: {
                etags_ = input.readBytes();
                bitField0_ |= 0x40000000;
                break;
              } // case 250
              case 258: {
                ttags_ = input.readBytes();
                bitField0_ |= 0x80000000;
                break;
              } // case 258
              case 264: {
                upLinkChecksum_ = input.readUInt32();
                bitField1_ |= 0x00000001;
                break;
              } // case 264
              case 272: {
                downLinkChecksum_ = input.readUInt32();
                bitField1_ |= 0x00000002;
                break;
              } // case 272
              case 280: {
                upLinkDesBytes_ = input.readUInt64();
                bitField1_ |= 0x00000004;
                break;
              } // case 280
              case 288: {
                downLinkDesBytes_ = input.readUInt64();
                bitField1_ |= 0x00000008;
                break;
              } // case 288
              case 298: {
                stream_ = input.readBytes();
                bitField1_ |= 0x00000010;
                break;
              } // case 298
              case 306: {
                upLinkStream_ = input.readBytes();
                bitField1_ |= 0x00000020;
                break;
              } // case 306
              case 314: {
                downLinkStream_ = input.readBytes();
                bitField1_ |= 0x00000040;
                break;
              } // case 314
              case 322: {
                transPayloadHex_ = input.readBytes();
                bitField1_ |= 0x00000080;
                break;
              } // case 322
              case 330: {
                upLinkTransPayHex_ = input.readBytes();
                bitField1_ |= 0x00000100;
                break;
              } // case 330
              case 338: {
                downLinkTransPayHex_ = input.readBytes();
                bitField1_ |= 0x00000200;
                break;
              } // case 338
              case 344: {
                int v = input.readUInt32();
                ensureUpLinkPayLenSetIsMutable();
                upLinkPayLenSet_.addInt(v);
                break;
              } // case 344
              case 346: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureUpLinkPayLenSetIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  upLinkPayLenSet_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 346
              case 352: {
                int v = input.readUInt32();
                ensureDownLinkPayLenSetIsMutable();
                downLinkPayLenSet_.addInt(v);
                break;
              } // case 352
              case 354: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureDownLinkPayLenSetIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  downLinkPayLenSet_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 354
              case 360: {
                establish_ = input.readUInt32();
                bitField1_ |= 0x00001000;
                break;
              } // case 360
              case 368: {
                upLinkSynSeqNum_ = input.readUInt32();
                bitField1_ |= 0x00002000;
                break;
              } // case 368
              case 376: {
                downLinkSynSeqNum_ = input.readUInt32();
                bitField1_ |= 0x00004000;
                break;
              } // case 376
              case 384: {
                upLinkSynTcpWins_ = input.readUInt32();
                bitField1_ |= 0x00008000;
                break;
              } // case 384
              case 392: {
                downLinkSynTcpWins_ = input.readUInt32();
                bitField1_ |= 0x00010000;
                break;
              } // case 392
              case 402: {
                upLinkTcpOpts_ = input.readBytes();
                bitField1_ |= 0x00020000;
                break;
              } // case 402
              case 410: {
                downLinkTcpOpts_ = input.readBytes();
                bitField1_ |= 0x00040000;
                break;
              } // case 410
              case 416: {
                upSesBytes_ = input.readUInt64();
                bitField1_ |= 0x00080000;
                break;
              } // case 416
              case 424: {
                downSesbytes_ = input.readUInt64();
                bitField1_ |= 0x00100000;
                break;
              } // case 424
              case 432: {
                sesBytes_ = input.readUInt64();
                bitField1_ |= 0x00200000;
                break;
              } // case 432
              case 445: {
                sesBytesRatio_ = input.readFloat();
                bitField1_ |= 0x00400000;
                break;
              } // case 445
              case 453: {
                payLenRatio_ = input.readFloat();
                bitField1_ |= 0x00800000;
                break;
              } // case 453
              case 458: {
                ipAsnDestination_ = input.readBytes();
                bitField1_ |= 0x01000000;
                break;
              } // case 458
              case 466: {
                ipAsnSource_ = input.readBytes();
                bitField1_ |= 0x02000000;
                break;
              } // case 466
              case 474: {
                ipBaseProto_ = input.readBytes();
                bitField1_ |= 0x04000000;
                break;
              } // case 474
              case 480: {
                ipBeginTime_ = input.readUInt64();
                bitField1_ |= 0x08000000;
                break;
              } // case 480
              case 490: {
                ipCityDestination_ = input.readBytes();
                bitField1_ |= 0x10000000;
                break;
              } // case 490
              case 498: {
                ipCitySource_ = input.readBytes();
                bitField1_ |= 0x20000000;
                break;
              } // case 498
              case 506: {
                ipCountryDestination_ = input.readBytes();
                bitField1_ |= 0x40000000;
                break;
              } // case 506
              case 514: {
                ipCountrySource_ = input.readBytes();
                bitField1_ |= 0x80000000;
                break;
              } // case 514
              case 520: {
                ipDataBytes_ = input.readUInt64();
                bitField2_ |= 0x00000001;
                break;
              } // case 520
              case 528: {
                ipDesiredBytes_ = input.readUInt64();
                bitField2_ |= 0x00000002;
                break;
              } // case 528
              case 536: {
                ipDesiredBytesDestination_ = input.readUInt64();
                bitField2_ |= 0x00000004;
                break;
              } // case 536
              case 544: {
                ipDesiredBytesSource_ = input.readUInt64();
                bitField2_ |= 0x00000008;
                break;
              } // case 544
              case 552: {
                ipDestination_ = input.readUInt32();
                bitField2_ |= 0x00000010;
                break;
              } // case 552
              case 560: {
                ipDuration_ = input.readUInt32();
                bitField2_ |= 0x00000020;
                break;
              } // case 560
              case 568: {
                ipEndTime_ = input.readUInt64();
                bitField2_ |= 0x00000040;
                break;
              } // case 568
              case 578: {
                ipIspDestination_ = input.readBytes();
                bitField2_ |= 0x00000080;
                break;
              } // case 578
              case 586: {
                ipIspSource_ = input.readBytes();
                bitField2_ |= 0x00000100;
                break;
              } // case 586
              case 597: {
                ipLatitudeDestination_ = input.readFloat();
                bitField2_ |= 0x00000200;
                break;
              } // case 597
              case 605: {
                ipLatitudeSource_ = input.readFloat();
                bitField2_ |= 0x00000400;
                break;
              } // case 605
              case 613: {
                ipLongitudeDestination_ = input.readFloat();
                bitField2_ |= 0x00000800;
                break;
              } // case 613
              case 621: {
                ipLongitudeSource_ = input.readFloat();
                bitField2_ |= 0x00001000;
                break;
              } // case 621
              case 624: {
                ipPackets_ = input.readUInt32();
                bitField2_ |= 0x00002000;
                break;
              } // case 624
              case 632: {
                ipProto_ = input.readUInt32();
                bitField2_ |= 0x00004000;
                break;
              } // case 632
              case 642: {
                ipProtoPath_ = input.readBytes();
                bitField2_ |= 0x00008000;
                break;
              } // case 642
              case 648: {
                ipSource_ = input.readUInt32();
                bitField2_ |= 0x00010000;
                break;
              } // case 648
              case 658: {
                ipStateDestination_ = input.readBytes();
                bitField2_ |= 0x00020000;
                break;
              } // case 658
              case 666: {
                ipStateSource_ = input.readBytes();
                bitField2_ |= 0x00040000;
                break;
              } // case 666
              case 674: {
                ipUpperProto_ = input.readBytes();
                bitField2_ |= 0x00080000;
                break;
              } // case 674
              case 680: {
                ipVersion_ = input.readUInt32();
                bitField2_ |= 0x00100000;
                break;
              } // case 680
              case 690: {
                ipv6Destination_ = input.readBytes();
                bitField2_ |= 0x00200000;
                break;
              } // case 690
              case 698: {
                ipv6Source_ = input.readBytes();
                bitField2_ |= 0x00400000;
                break;
              } // case 698
              case 704: {
                outIpDestination_ = input.readUInt32();
                bitField2_ |= 0x00800000;
                break;
              } // case 704
              case 712: {
                outIpProto_ = input.readUInt32();
                bitField2_ |= 0x01000000;
                break;
              } // case 712
              case 720: {
                outIpSource_ = input.readUInt32();
                bitField2_ |= 0x02000000;
                break;
              } // case 720
              case 728: {
                outIpVersion_ = input.readUInt32();
                bitField2_ |= 0x04000000;
                break;
              } // case 728
              case 738: {
                outIpv6Destination_ = input.readBytes();
                bitField2_ |= 0x08000000;
                break;
              } // case 738
              case 746: {
                outIpv6Source_ = input.readBytes();
                bitField2_ |= 0x10000000;
                break;
              } // case 746
              case 752: {
                outPortDestination_ = input.readUInt32();
                bitField2_ |= 0x20000000;
                break;
              } // case 752
              case 760: {
                outPortSource_ = input.readUInt32();
                bitField2_ |= 0x40000000;
                break;
              } // case 760
              case 768: {
                portDestination_ = input.readUInt32();
                bitField2_ |= 0x80000000;
                break;
              } // case 768
              case 776: {
                portSource_ = input.readUInt32();
                bitField3_ |= 0x00000001;
                break;
              } // case 776
              case 784: {
                tcpFinished_ = input.readUInt32();
                bitField3_ |= 0x00000002;
                break;
              } // case 784
              case 794: {
                tcpFirstFlag_ = input.readBytes();
                bitField3_ |= 0x00000004;
                break;
              } // case 794
              case 802: {
                tcpFlagsDestination_ = input.readBytes();
                bitField3_ |= 0x00000008;
                break;
              } // case 802
              case 810: {
                tcpFlagsSource_ = input.readBytes();
                bitField3_ |= 0x00000010;
                break;
              } // case 810
              case 816: {
                transPacketLengthDestinationHighFrequency_ = input.readUInt32();
                bitField3_ |= 0x00000020;
                break;
              } // case 816
              case 824: {
                transPacketLengthSourceHighFrequency_ = input.readUInt32();
                bitField3_ |= 0x00000040;
                break;
              } // case 824
              case 832: {
                isEnd_ = input.readUInt32();
                bitField3_ |= 0x00000080;
                break;
              } // case 832
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;
      private int bitField3_;

      private com.google.protobuf.ByteString portInfo_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes portInfo = 1;</code>
       * @return Whether the portInfo field is set.
       */
      @java.lang.Override
      public boolean hasPortInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes portInfo = 1;</code>
       * @return The portInfo.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPortInfo() {
        return portInfo_;
      }
      /**
       * <code>optional bytes portInfo = 1;</code>
       * @param value The portInfo to set.
       * @return This builder for chaining.
       */
      public Builder setPortInfo(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        portInfo_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes portInfo = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPortInfo() {
        bitField0_ = (bitField0_ & ~0x00000001);
        portInfo_ = getDefaultInstance().getPortInfo();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString portInfoAtt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes portInfoAtt = 2;</code>
       * @return Whether the portInfoAtt field is set.
       */
      @java.lang.Override
      public boolean hasPortInfoAtt() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes portInfoAtt = 2;</code>
       * @return The portInfoAtt.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPortInfoAtt() {
        return portInfoAtt_;
      }
      /**
       * <code>optional bytes portInfoAtt = 2;</code>
       * @param value The portInfoAtt to set.
       * @return This builder for chaining.
       */
      public Builder setPortInfoAtt(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        portInfoAtt_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes portInfoAtt = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPortInfoAtt() {
        bitField0_ = (bitField0_ & ~0x00000002);
        portInfoAtt_ = getDefaultInstance().getPortInfoAtt();
        onChanged();
        return this;
      }

      private int upPayLen_ ;
      /**
       * <code>optional uint32 upPayLen = 3;</code>
       * @return Whether the upPayLen field is set.
       */
      @java.lang.Override
      public boolean hasUpPayLen() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 upPayLen = 3;</code>
       * @return The upPayLen.
       */
      @java.lang.Override
      public int getUpPayLen() {
        return upPayLen_;
      }
      /**
       * <code>optional uint32 upPayLen = 3;</code>
       * @param value The upPayLen to set.
       * @return This builder for chaining.
       */
      public Builder setUpPayLen(int value) {

        upPayLen_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 upPayLen = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpPayLen() {
        bitField0_ = (bitField0_ & ~0x00000004);
        upPayLen_ = 0;
        onChanged();
        return this;
      }

      private long downPayLen_ ;
      /**
       * <code>optional uint64 downPayLen = 4;</code>
       * @return Whether the downPayLen field is set.
       */
      @java.lang.Override
      public boolean hasDownPayLen() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint64 downPayLen = 4;</code>
       * @return The downPayLen.
       */
      @java.lang.Override
      public long getDownPayLen() {
        return downPayLen_;
      }
      /**
       * <code>optional uint64 downPayLen = 4;</code>
       * @param value The downPayLen to set.
       * @return This builder for chaining.
       */
      public Builder setDownPayLen(long value) {

        downPayLen_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 downPayLen = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownPayLen() {
        bitField0_ = (bitField0_ & ~0x00000008);
        downPayLen_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString tcpflag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes tcpflag = 5;</code>
       * @return Whether the tcpflag field is set.
       */
      @java.lang.Override
      public boolean hasTcpflag() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes tcpflag = 5;</code>
       * @return The tcpflag.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTcpflag() {
        return tcpflag_;
      }
      /**
       * <code>optional bytes tcpflag = 5;</code>
       * @param value The tcpflag to set.
       * @return This builder for chaining.
       */
      public Builder setTcpflag(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        tcpflag_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes tcpflag = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpflag() {
        bitField0_ = (bitField0_ & ~0x00000010);
        tcpflag_ = getDefaultInstance().getTcpflag();
        onChanged();
        return this;
      }

      private long upLinkPktNum_ ;
      /**
       * <code>optional uint64 upLinkPktNum = 6;</code>
       * @return Whether the upLinkPktNum field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkPktNum() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint64 upLinkPktNum = 6;</code>
       * @return The upLinkPktNum.
       */
      @java.lang.Override
      public long getUpLinkPktNum() {
        return upLinkPktNum_;
      }
      /**
       * <code>optional uint64 upLinkPktNum = 6;</code>
       * @param value The upLinkPktNum to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkPktNum(long value) {

        upLinkPktNum_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 upLinkPktNum = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkPktNum() {
        bitField0_ = (bitField0_ & ~0x00000020);
        upLinkPktNum_ = 0L;
        onChanged();
        return this;
      }

      private long upLinkSize_ ;
      /**
       * <code>optional uint64 upLinkSize = 7;</code>
       * @return Whether the upLinkSize field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkSize() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint64 upLinkSize = 7;</code>
       * @return The upLinkSize.
       */
      @java.lang.Override
      public long getUpLinkSize() {
        return upLinkSize_;
      }
      /**
       * <code>optional uint64 upLinkSize = 7;</code>
       * @param value The upLinkSize to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkSize(long value) {

        upLinkSize_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 upLinkSize = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkSize() {
        bitField0_ = (bitField0_ & ~0x00000040);
        upLinkSize_ = 0L;
        onChanged();
        return this;
      }

      private int upLinkBigPktLen_ ;
      /**
       * <code>optional uint32 upLinkBigPktLen = 8;</code>
       * @return Whether the upLinkBigPktLen field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkBigPktLen() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 upLinkBigPktLen = 8;</code>
       * @return The upLinkBigPktLen.
       */
      @java.lang.Override
      public int getUpLinkBigPktLen() {
        return upLinkBigPktLen_;
      }
      /**
       * <code>optional uint32 upLinkBigPktLen = 8;</code>
       * @param value The upLinkBigPktLen to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkBigPktLen(int value) {

        upLinkBigPktLen_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 upLinkBigPktLen = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkBigPktLen() {
        bitField0_ = (bitField0_ & ~0x00000080);
        upLinkBigPktLen_ = 0;
        onChanged();
        return this;
      }

      private int upLinkSmaPktLen_ ;
      /**
       * <code>optional uint32 upLinkSmaPktLen = 9;</code>
       * @return Whether the upLinkSmaPktLen field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkSmaPktLen() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 upLinkSmaPktLen = 9;</code>
       * @return The upLinkSmaPktLen.
       */
      @java.lang.Override
      public int getUpLinkSmaPktLen() {
        return upLinkSmaPktLen_;
      }
      /**
       * <code>optional uint32 upLinkSmaPktLen = 9;</code>
       * @param value The upLinkSmaPktLen to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkSmaPktLen(int value) {

        upLinkSmaPktLen_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 upLinkSmaPktLen = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkSmaPktLen() {
        bitField0_ = (bitField0_ & ~0x00000100);
        upLinkSmaPktLen_ = 0;
        onChanged();
        return this;
      }

      private long upLinkBigPktInt_ ;
      /**
       * <code>optional uint64 upLinkBigPktInt = 10;</code>
       * @return Whether the upLinkBigPktInt field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkBigPktInt() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint64 upLinkBigPktInt = 10;</code>
       * @return The upLinkBigPktInt.
       */
      @java.lang.Override
      public long getUpLinkBigPktInt() {
        return upLinkBigPktInt_;
      }
      /**
       * <code>optional uint64 upLinkBigPktInt = 10;</code>
       * @param value The upLinkBigPktInt to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkBigPktInt(long value) {

        upLinkBigPktInt_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 upLinkBigPktInt = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkBigPktInt() {
        bitField0_ = (bitField0_ & ~0x00000200);
        upLinkBigPktInt_ = 0L;
        onChanged();
        return this;
      }

      private long upLinkSmaPktInt_ ;
      /**
       * <code>optional uint64 upLinkSmaPktInt = 11;</code>
       * @return Whether the upLinkSmaPktInt field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkSmaPktInt() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional uint64 upLinkSmaPktInt = 11;</code>
       * @return The upLinkSmaPktInt.
       */
      @java.lang.Override
      public long getUpLinkSmaPktInt() {
        return upLinkSmaPktInt_;
      }
      /**
       * <code>optional uint64 upLinkSmaPktInt = 11;</code>
       * @param value The upLinkSmaPktInt to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkSmaPktInt(long value) {

        upLinkSmaPktInt_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 upLinkSmaPktInt = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkSmaPktInt() {
        bitField0_ = (bitField0_ & ~0x00000400);
        upLinkSmaPktInt_ = 0L;
        onChanged();
        return this;
      }

      private int downLinkPktNum_ ;
      /**
       * <code>optional uint32 downLinkPktNum = 12;</code>
       * @return Whether the downLinkPktNum field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkPktNum() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional uint32 downLinkPktNum = 12;</code>
       * @return The downLinkPktNum.
       */
      @java.lang.Override
      public int getDownLinkPktNum() {
        return downLinkPktNum_;
      }
      /**
       * <code>optional uint32 downLinkPktNum = 12;</code>
       * @param value The downLinkPktNum to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkPktNum(int value) {

        downLinkPktNum_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 downLinkPktNum = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkPktNum() {
        bitField0_ = (bitField0_ & ~0x00000800);
        downLinkPktNum_ = 0;
        onChanged();
        return this;
      }

      private int downLinkSize_ ;
      /**
       * <code>optional uint32 downLinkSize = 13;</code>
       * @return Whether the downLinkSize field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkSize() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint32 downLinkSize = 13;</code>
       * @return The downLinkSize.
       */
      @java.lang.Override
      public int getDownLinkSize() {
        return downLinkSize_;
      }
      /**
       * <code>optional uint32 downLinkSize = 13;</code>
       * @param value The downLinkSize to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkSize(int value) {

        downLinkSize_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 downLinkSize = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkSize() {
        bitField0_ = (bitField0_ & ~0x00001000);
        downLinkSize_ = 0;
        onChanged();
        return this;
      }

      private int downLinkBigPktLen_ ;
      /**
       * <code>optional uint32 downLinkBigPktLen = 14;</code>
       * @return Whether the downLinkBigPktLen field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkBigPktLen() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint32 downLinkBigPktLen = 14;</code>
       * @return The downLinkBigPktLen.
       */
      @java.lang.Override
      public int getDownLinkBigPktLen() {
        return downLinkBigPktLen_;
      }
      /**
       * <code>optional uint32 downLinkBigPktLen = 14;</code>
       * @param value The downLinkBigPktLen to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkBigPktLen(int value) {

        downLinkBigPktLen_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 downLinkBigPktLen = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkBigPktLen() {
        bitField0_ = (bitField0_ & ~0x00002000);
        downLinkBigPktLen_ = 0;
        onChanged();
        return this;
      }

      private int downLinkSmaPktLen_ ;
      /**
       * <code>optional uint32 downLinkSmaPktLen = 15;</code>
       * @return Whether the downLinkSmaPktLen field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkSmaPktLen() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 downLinkSmaPktLen = 15;</code>
       * @return The downLinkSmaPktLen.
       */
      @java.lang.Override
      public int getDownLinkSmaPktLen() {
        return downLinkSmaPktLen_;
      }
      /**
       * <code>optional uint32 downLinkSmaPktLen = 15;</code>
       * @param value The downLinkSmaPktLen to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkSmaPktLen(int value) {

        downLinkSmaPktLen_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 downLinkSmaPktLen = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkSmaPktLen() {
        bitField0_ = (bitField0_ & ~0x00004000);
        downLinkSmaPktLen_ = 0;
        onChanged();
        return this;
      }

      private long downLinkBigPktInt_ ;
      /**
       * <code>optional uint64 downLinkBigPktInt = 16;</code>
       * @return Whether the downLinkBigPktInt field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkBigPktInt() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional uint64 downLinkBigPktInt = 16;</code>
       * @return The downLinkBigPktInt.
       */
      @java.lang.Override
      public long getDownLinkBigPktInt() {
        return downLinkBigPktInt_;
      }
      /**
       * <code>optional uint64 downLinkBigPktInt = 16;</code>
       * @param value The downLinkBigPktInt to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkBigPktInt(long value) {

        downLinkBigPktInt_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 downLinkBigPktInt = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkBigPktInt() {
        bitField0_ = (bitField0_ & ~0x00008000);
        downLinkBigPktInt_ = 0L;
        onChanged();
        return this;
      }

      private long downLinkSmaPktInt_ ;
      /**
       * <code>optional uint64 downLinkSmaPktInt = 17;</code>
       * @return Whether the downLinkSmaPktInt field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkSmaPktInt() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint64 downLinkSmaPktInt = 17;</code>
       * @return The downLinkSmaPktInt.
       */
      @java.lang.Override
      public long getDownLinkSmaPktInt() {
        return downLinkSmaPktInt_;
      }
      /**
       * <code>optional uint64 downLinkSmaPktInt = 17;</code>
       * @param value The downLinkSmaPktInt to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkSmaPktInt(long value) {

        downLinkSmaPktInt_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 downLinkSmaPktInt = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkSmaPktInt() {
        bitField0_ = (bitField0_ & ~0x00010000);
        downLinkSmaPktInt_ = 0L;
        onChanged();
        return this;
      }

      private int firTtlByCli_ ;
      /**
       * <code>optional uint32 firTtlByCli = 18;</code>
       * @return Whether the firTtlByCli field is set.
       */
      @java.lang.Override
      public boolean hasFirTtlByCli() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 firTtlByCli = 18;</code>
       * @return The firTtlByCli.
       */
      @java.lang.Override
      public int getFirTtlByCli() {
        return firTtlByCli_;
      }
      /**
       * <code>optional uint32 firTtlByCli = 18;</code>
       * @param value The firTtlByCli to set.
       * @return This builder for chaining.
       */
      public Builder setFirTtlByCli(int value) {

        firTtlByCli_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 firTtlByCli = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearFirTtlByCli() {
        bitField0_ = (bitField0_ & ~0x00020000);
        firTtlByCli_ = 0;
        onChanged();
        return this;
      }

      private int firTtlBySrv_ ;
      /**
       * <code>optional uint32 firTtlBySrv = 19;</code>
       * @return Whether the firTtlBySrv field is set.
       */
      @java.lang.Override
      public boolean hasFirTtlBySrv() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional uint32 firTtlBySrv = 19;</code>
       * @return The firTtlBySrv.
       */
      @java.lang.Override
      public int getFirTtlBySrv() {
        return firTtlBySrv_;
      }
      /**
       * <code>optional uint32 firTtlBySrv = 19;</code>
       * @param value The firTtlBySrv to set.
       * @return This builder for chaining.
       */
      public Builder setFirTtlBySrv(int value) {

        firTtlBySrv_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 firTtlBySrv = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearFirTtlBySrv() {
        bitField0_ = (bitField0_ & ~0x00040000);
        firTtlBySrv_ = 0;
        onChanged();
        return this;
      }

      private int appDirec_ ;
      /**
       * <code>optional uint32 appDirec = 20;</code>
       * @return Whether the appDirec field is set.
       */
      @java.lang.Override
      public boolean hasAppDirec() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint32 appDirec = 20;</code>
       * @return The appDirec.
       */
      @java.lang.Override
      public int getAppDirec() {
        return appDirec_;
      }
      /**
       * <code>optional uint32 appDirec = 20;</code>
       * @param value The appDirec to set.
       * @return This builder for chaining.
       */
      public Builder setAppDirec(int value) {

        appDirec_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 appDirec = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppDirec() {
        bitField0_ = (bitField0_ & ~0x00080000);
        appDirec_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsFinCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
       * @return Whether the tcpFlagsFinCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsFinCnt() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
       * @return The tcpFlagsFinCnt.
       */
      @java.lang.Override
      public int getTcpFlagsFinCnt() {
        return tcpFlagsFinCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
       * @param value The tcpFlagsFinCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsFinCnt(int value) {

        tcpFlagsFinCnt_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsFinCnt = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsFinCnt() {
        bitField0_ = (bitField0_ & ~0x00100000);
        tcpFlagsFinCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsSynCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
       * @return Whether the tcpFlagsSynCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsSynCnt() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
       * @return The tcpFlagsSynCnt.
       */
      @java.lang.Override
      public int getTcpFlagsSynCnt() {
        return tcpFlagsSynCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
       * @param value The tcpFlagsSynCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsSynCnt(int value) {

        tcpFlagsSynCnt_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsSynCnt = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsSynCnt() {
        bitField0_ = (bitField0_ & ~0x00200000);
        tcpFlagsSynCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsRstCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
       * @return Whether the tcpFlagsRstCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsRstCnt() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
       * @return The tcpFlagsRstCnt.
       */
      @java.lang.Override
      public int getTcpFlagsRstCnt() {
        return tcpFlagsRstCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
       * @param value The tcpFlagsRstCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsRstCnt(int value) {

        tcpFlagsRstCnt_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsRstCnt = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsRstCnt() {
        bitField0_ = (bitField0_ & ~0x00400000);
        tcpFlagsRstCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsPshCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
       * @return Whether the tcpFlagsPshCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsPshCnt() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
       * @return The tcpFlagsPshCnt.
       */
      @java.lang.Override
      public int getTcpFlagsPshCnt() {
        return tcpFlagsPshCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
       * @param value The tcpFlagsPshCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsPshCnt(int value) {

        tcpFlagsPshCnt_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsPshCnt = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsPshCnt() {
        bitField0_ = (bitField0_ & ~0x00800000);
        tcpFlagsPshCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsAckCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
       * @return Whether the tcpFlagsAckCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsAckCnt() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
       * @return The tcpFlagsAckCnt.
       */
      @java.lang.Override
      public int getTcpFlagsAckCnt() {
        return tcpFlagsAckCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
       * @param value The tcpFlagsAckCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsAckCnt(int value) {

        tcpFlagsAckCnt_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsAckCnt = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsAckCnt() {
        bitField0_ = (bitField0_ & ~0x01000000);
        tcpFlagsAckCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsUrgCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
       * @return Whether the tcpFlagsUrgCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsUrgCnt() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
       * @return The tcpFlagsUrgCnt.
       */
      @java.lang.Override
      public int getTcpFlagsUrgCnt() {
        return tcpFlagsUrgCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
       * @param value The tcpFlagsUrgCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsUrgCnt(int value) {

        tcpFlagsUrgCnt_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsUrgCnt = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsUrgCnt() {
        bitField0_ = (bitField0_ & ~0x02000000);
        tcpFlagsUrgCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsEceCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
       * @return Whether the tcpFlagsEceCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsEceCnt() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
       * @return The tcpFlagsEceCnt.
       */
      @java.lang.Override
      public int getTcpFlagsEceCnt() {
        return tcpFlagsEceCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
       * @param value The tcpFlagsEceCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsEceCnt(int value) {

        tcpFlagsEceCnt_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsEceCnt = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsEceCnt() {
        bitField0_ = (bitField0_ & ~0x04000000);
        tcpFlagsEceCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsCwrCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
       * @return Whether the tcpFlagsCwrCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsCwrCnt() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
       * @return The tcpFlagsCwrCnt.
       */
      @java.lang.Override
      public int getTcpFlagsCwrCnt() {
        return tcpFlagsCwrCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
       * @param value The tcpFlagsCwrCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsCwrCnt(int value) {

        tcpFlagsCwrCnt_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsCwrCnt = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsCwrCnt() {
        bitField0_ = (bitField0_ & ~0x08000000);
        tcpFlagsCwrCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsNSCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
       * @return Whether the tcpFlagsNSCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsNSCnt() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
       * @return The tcpFlagsNSCnt.
       */
      @java.lang.Override
      public int getTcpFlagsNSCnt() {
        return tcpFlagsNSCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
       * @param value The tcpFlagsNSCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsNSCnt(int value) {

        tcpFlagsNSCnt_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsNSCnt = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsNSCnt() {
        bitField0_ = (bitField0_ & ~0x10000000);
        tcpFlagsNSCnt_ = 0;
        onChanged();
        return this;
      }

      private int tcpFlagsSynAckCnt_ ;
      /**
       * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
       * @return Whether the tcpFlagsSynAckCnt field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsSynAckCnt() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
       * @return The tcpFlagsSynAckCnt.
       */
      @java.lang.Override
      public int getTcpFlagsSynAckCnt() {
        return tcpFlagsSynAckCnt_;
      }
      /**
       * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
       * @param value The tcpFlagsSynAckCnt to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsSynAckCnt(int value) {

        tcpFlagsSynAckCnt_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFlagsSynAckCnt = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsSynAckCnt() {
        bitField0_ = (bitField0_ & ~0x20000000);
        tcpFlagsSynAckCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString etags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes etags = 31;</code>
       * @return Whether the etags field is set.
       */
      @java.lang.Override
      public boolean hasEtags() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes etags = 31;</code>
       * @return The etags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEtags() {
        return etags_;
      }
      /**
       * <code>optional bytes etags = 31;</code>
       * @param value The etags to set.
       * @return This builder for chaining.
       */
      public Builder setEtags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        etags_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes etags = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearEtags() {
        bitField0_ = (bitField0_ & ~0x40000000);
        etags_ = getDefaultInstance().getEtags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ttags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ttags = 32;</code>
       * @return Whether the ttags field is set.
       */
      @java.lang.Override
      public boolean hasTtags() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes ttags = 32;</code>
       * @return The ttags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTtags() {
        return ttags_;
      }
      /**
       * <code>optional bytes ttags = 32;</code>
       * @param value The ttags to set.
       * @return This builder for chaining.
       */
      public Builder setTtags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ttags_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ttags = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearTtags() {
        bitField0_ = (bitField0_ & ~0x80000000);
        ttags_ = getDefaultInstance().getTtags();
        onChanged();
        return this;
      }

      private int upLinkChecksum_ ;
      /**
       * <code>optional uint32 upLinkChecksum = 33;</code>
       * @return Whether the upLinkChecksum field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkChecksum() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 upLinkChecksum = 33;</code>
       * @return The upLinkChecksum.
       */
      @java.lang.Override
      public int getUpLinkChecksum() {
        return upLinkChecksum_;
      }
      /**
       * <code>optional uint32 upLinkChecksum = 33;</code>
       * @param value The upLinkChecksum to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkChecksum(int value) {

        upLinkChecksum_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 upLinkChecksum = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkChecksum() {
        bitField1_ = (bitField1_ & ~0x00000001);
        upLinkChecksum_ = 0;
        onChanged();
        return this;
      }

      private int downLinkChecksum_ ;
      /**
       * <code>optional uint32 downLinkChecksum = 34;</code>
       * @return Whether the downLinkChecksum field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkChecksum() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 downLinkChecksum = 34;</code>
       * @return The downLinkChecksum.
       */
      @java.lang.Override
      public int getDownLinkChecksum() {
        return downLinkChecksum_;
      }
      /**
       * <code>optional uint32 downLinkChecksum = 34;</code>
       * @param value The downLinkChecksum to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkChecksum(int value) {

        downLinkChecksum_ = value;
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 downLinkChecksum = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkChecksum() {
        bitField1_ = (bitField1_ & ~0x00000002);
        downLinkChecksum_ = 0;
        onChanged();
        return this;
      }

      private long upLinkDesBytes_ ;
      /**
       * <code>optional uint64 upLinkDesBytes = 35;</code>
       * @return Whether the upLinkDesBytes field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkDesBytes() {
        return ((bitField1_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint64 upLinkDesBytes = 35;</code>
       * @return The upLinkDesBytes.
       */
      @java.lang.Override
      public long getUpLinkDesBytes() {
        return upLinkDesBytes_;
      }
      /**
       * <code>optional uint64 upLinkDesBytes = 35;</code>
       * @param value The upLinkDesBytes to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkDesBytes(long value) {

        upLinkDesBytes_ = value;
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 upLinkDesBytes = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkDesBytes() {
        bitField1_ = (bitField1_ & ~0x00000004);
        upLinkDesBytes_ = 0L;
        onChanged();
        return this;
      }

      private long downLinkDesBytes_ ;
      /**
       * <code>optional uint64 downLinkDesBytes = 36;</code>
       * @return Whether the downLinkDesBytes field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkDesBytes() {
        return ((bitField1_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint64 downLinkDesBytes = 36;</code>
       * @return The downLinkDesBytes.
       */
      @java.lang.Override
      public long getDownLinkDesBytes() {
        return downLinkDesBytes_;
      }
      /**
       * <code>optional uint64 downLinkDesBytes = 36;</code>
       * @param value The downLinkDesBytes to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkDesBytes(long value) {

        downLinkDesBytes_ = value;
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 downLinkDesBytes = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkDesBytes() {
        bitField1_ = (bitField1_ & ~0x00000008);
        downLinkDesBytes_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString stream_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes stream = 37;</code>
       * @return Whether the stream field is set.
       */
      @java.lang.Override
      public boolean hasStream() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes stream = 37;</code>
       * @return The stream.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getStream() {
        return stream_;
      }
      /**
       * <code>optional bytes stream = 37;</code>
       * @param value The stream to set.
       * @return This builder for chaining.
       */
      public Builder setStream(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        stream_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes stream = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearStream() {
        bitField1_ = (bitField1_ & ~0x00000010);
        stream_ = getDefaultInstance().getStream();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString upLinkStream_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes upLinkStream = 38;</code>
       * @return Whether the upLinkStream field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkStream() {
        return ((bitField1_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes upLinkStream = 38;</code>
       * @return The upLinkStream.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUpLinkStream() {
        return upLinkStream_;
      }
      /**
       * <code>optional bytes upLinkStream = 38;</code>
       * @param value The upLinkStream to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkStream(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        upLinkStream_ = value;
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes upLinkStream = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkStream() {
        bitField1_ = (bitField1_ & ~0x00000020);
        upLinkStream_ = getDefaultInstance().getUpLinkStream();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString downLinkStream_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes downLinkStream = 39;</code>
       * @return Whether the downLinkStream field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkStream() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes downLinkStream = 39;</code>
       * @return The downLinkStream.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDownLinkStream() {
        return downLinkStream_;
      }
      /**
       * <code>optional bytes downLinkStream = 39;</code>
       * @param value The downLinkStream to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkStream(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        downLinkStream_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes downLinkStream = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkStream() {
        bitField1_ = (bitField1_ & ~0x00000040);
        downLinkStream_ = getDefaultInstance().getDownLinkStream();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString transPayloadHex_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes trans_payload_hex = 40;</code>
       * @return Whether the transPayloadHex field is set.
       */
      @java.lang.Override
      public boolean hasTransPayloadHex() {
        return ((bitField1_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes trans_payload_hex = 40;</code>
       * @return The transPayloadHex.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTransPayloadHex() {
        return transPayloadHex_;
      }
      /**
       * <code>optional bytes trans_payload_hex = 40;</code>
       * @param value The transPayloadHex to set.
       * @return This builder for chaining.
       */
      public Builder setTransPayloadHex(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        transPayloadHex_ = value;
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes trans_payload_hex = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransPayloadHex() {
        bitField1_ = (bitField1_ & ~0x00000080);
        transPayloadHex_ = getDefaultInstance().getTransPayloadHex();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString upLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes upLinkTransPayHex = 41;</code>
       * @return Whether the upLinkTransPayHex field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkTransPayHex() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes upLinkTransPayHex = 41;</code>
       * @return The upLinkTransPayHex.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUpLinkTransPayHex() {
        return upLinkTransPayHex_;
      }
      /**
       * <code>optional bytes upLinkTransPayHex = 41;</code>
       * @param value The upLinkTransPayHex to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkTransPayHex(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        upLinkTransPayHex_ = value;
        bitField1_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes upLinkTransPayHex = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkTransPayHex() {
        bitField1_ = (bitField1_ & ~0x00000100);
        upLinkTransPayHex_ = getDefaultInstance().getUpLinkTransPayHex();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString downLinkTransPayHex_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes downLinkTransPayHex = 42;</code>
       * @return Whether the downLinkTransPayHex field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkTransPayHex() {
        return ((bitField1_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes downLinkTransPayHex = 42;</code>
       * @return The downLinkTransPayHex.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDownLinkTransPayHex() {
        return downLinkTransPayHex_;
      }
      /**
       * <code>optional bytes downLinkTransPayHex = 42;</code>
       * @param value The downLinkTransPayHex to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkTransPayHex(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        downLinkTransPayHex_ = value;
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes downLinkTransPayHex = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkTransPayHex() {
        bitField1_ = (bitField1_ & ~0x00000200);
        downLinkTransPayHex_ = getDefaultInstance().getDownLinkTransPayHex();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList upLinkPayLenSet_ = emptyIntList();
      private void ensureUpLinkPayLenSetIsMutable() {
        if (!upLinkPayLenSet_.isModifiable()) {
          upLinkPayLenSet_ = makeMutableCopy(upLinkPayLenSet_);
        }
        bitField1_ |= 0x00000400;
      }
      /**
       * <code>repeated uint32 upLinkPayLenSet = 43;</code>
       * @return A list containing the upLinkPayLenSet.
       */
      public java.util.List<java.lang.Integer>
          getUpLinkPayLenSetList() {
        upLinkPayLenSet_.makeImmutable();
        return upLinkPayLenSet_;
      }
      /**
       * <code>repeated uint32 upLinkPayLenSet = 43;</code>
       * @return The count of upLinkPayLenSet.
       */
      public int getUpLinkPayLenSetCount() {
        return upLinkPayLenSet_.size();
      }
      /**
       * <code>repeated uint32 upLinkPayLenSet = 43;</code>
       * @param index The index of the element to return.
       * @return The upLinkPayLenSet at the given index.
       */
      public int getUpLinkPayLenSet(int index) {
        return upLinkPayLenSet_.getInt(index);
      }
      /**
       * <code>repeated uint32 upLinkPayLenSet = 43;</code>
       * @param index The index to set the value at.
       * @param value The upLinkPayLenSet to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkPayLenSet(
          int index, int value) {

        ensureUpLinkPayLenSetIsMutable();
        upLinkPayLenSet_.setInt(index, value);
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 upLinkPayLenSet = 43;</code>
       * @param value The upLinkPayLenSet to add.
       * @return This builder for chaining.
       */
      public Builder addUpLinkPayLenSet(int value) {

        ensureUpLinkPayLenSetIsMutable();
        upLinkPayLenSet_.addInt(value);
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 upLinkPayLenSet = 43;</code>
       * @param values The upLinkPayLenSet to add.
       * @return This builder for chaining.
       */
      public Builder addAllUpLinkPayLenSet(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureUpLinkPayLenSetIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, upLinkPayLenSet_);
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 upLinkPayLenSet = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkPayLenSet() {
        upLinkPayLenSet_ = emptyIntList();
        bitField1_ = (bitField1_ & ~0x00000400);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList downLinkPayLenSet_ = emptyIntList();
      private void ensureDownLinkPayLenSetIsMutable() {
        if (!downLinkPayLenSet_.isModifiable()) {
          downLinkPayLenSet_ = makeMutableCopy(downLinkPayLenSet_);
        }
        bitField1_ |= 0x00000800;
      }
      /**
       * <code>repeated uint32 downLinkPayLenSet = 44;</code>
       * @return A list containing the downLinkPayLenSet.
       */
      public java.util.List<java.lang.Integer>
          getDownLinkPayLenSetList() {
        downLinkPayLenSet_.makeImmutable();
        return downLinkPayLenSet_;
      }
      /**
       * <code>repeated uint32 downLinkPayLenSet = 44;</code>
       * @return The count of downLinkPayLenSet.
       */
      public int getDownLinkPayLenSetCount() {
        return downLinkPayLenSet_.size();
      }
      /**
       * <code>repeated uint32 downLinkPayLenSet = 44;</code>
       * @param index The index of the element to return.
       * @return The downLinkPayLenSet at the given index.
       */
      public int getDownLinkPayLenSet(int index) {
        return downLinkPayLenSet_.getInt(index);
      }
      /**
       * <code>repeated uint32 downLinkPayLenSet = 44;</code>
       * @param index The index to set the value at.
       * @param value The downLinkPayLenSet to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkPayLenSet(
          int index, int value) {

        ensureDownLinkPayLenSetIsMutable();
        downLinkPayLenSet_.setInt(index, value);
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 downLinkPayLenSet = 44;</code>
       * @param value The downLinkPayLenSet to add.
       * @return This builder for chaining.
       */
      public Builder addDownLinkPayLenSet(int value) {

        ensureDownLinkPayLenSetIsMutable();
        downLinkPayLenSet_.addInt(value);
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 downLinkPayLenSet = 44;</code>
       * @param values The downLinkPayLenSet to add.
       * @return This builder for chaining.
       */
      public Builder addAllDownLinkPayLenSet(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDownLinkPayLenSetIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, downLinkPayLenSet_);
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 downLinkPayLenSet = 44;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkPayLenSet() {
        downLinkPayLenSet_ = emptyIntList();
        bitField1_ = (bitField1_ & ~0x00000800);
        onChanged();
        return this;
      }

      private int establish_ ;
      /**
       * <code>optional uint32 establish = 45;</code>
       * @return Whether the establish field is set.
       */
      @java.lang.Override
      public boolean hasEstablish() {
        return ((bitField1_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint32 establish = 45;</code>
       * @return The establish.
       */
      @java.lang.Override
      public int getEstablish() {
        return establish_;
      }
      /**
       * <code>optional uint32 establish = 45;</code>
       * @param value The establish to set.
       * @return This builder for chaining.
       */
      public Builder setEstablish(int value) {

        establish_ = value;
        bitField1_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 establish = 45;</code>
       * @return This builder for chaining.
       */
      public Builder clearEstablish() {
        bitField1_ = (bitField1_ & ~0x00001000);
        establish_ = 0;
        onChanged();
        return this;
      }

      private int upLinkSynSeqNum_ ;
      /**
       * <code>optional uint32 upLinkSynSeqNum = 46;</code>
       * @return Whether the upLinkSynSeqNum field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkSynSeqNum() {
        return ((bitField1_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint32 upLinkSynSeqNum = 46;</code>
       * @return The upLinkSynSeqNum.
       */
      @java.lang.Override
      public int getUpLinkSynSeqNum() {
        return upLinkSynSeqNum_;
      }
      /**
       * <code>optional uint32 upLinkSynSeqNum = 46;</code>
       * @param value The upLinkSynSeqNum to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkSynSeqNum(int value) {

        upLinkSynSeqNum_ = value;
        bitField1_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 upLinkSynSeqNum = 46;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkSynSeqNum() {
        bitField1_ = (bitField1_ & ~0x00002000);
        upLinkSynSeqNum_ = 0;
        onChanged();
        return this;
      }

      private int downLinkSynSeqNum_ ;
      /**
       * <code>optional uint32 downLinkSynSeqNum = 47;</code>
       * @return Whether the downLinkSynSeqNum field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkSynSeqNum() {
        return ((bitField1_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 downLinkSynSeqNum = 47;</code>
       * @return The downLinkSynSeqNum.
       */
      @java.lang.Override
      public int getDownLinkSynSeqNum() {
        return downLinkSynSeqNum_;
      }
      /**
       * <code>optional uint32 downLinkSynSeqNum = 47;</code>
       * @param value The downLinkSynSeqNum to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkSynSeqNum(int value) {

        downLinkSynSeqNum_ = value;
        bitField1_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 downLinkSynSeqNum = 47;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkSynSeqNum() {
        bitField1_ = (bitField1_ & ~0x00004000);
        downLinkSynSeqNum_ = 0;
        onChanged();
        return this;
      }

      private int upLinkSynTcpWins_ ;
      /**
       * <code>optional uint32 upLinkSynTcpWins = 48;</code>
       * @return Whether the upLinkSynTcpWins field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkSynTcpWins() {
        return ((bitField1_ & 0x00008000) != 0);
      }
      /**
       * <code>optional uint32 upLinkSynTcpWins = 48;</code>
       * @return The upLinkSynTcpWins.
       */
      @java.lang.Override
      public int getUpLinkSynTcpWins() {
        return upLinkSynTcpWins_;
      }
      /**
       * <code>optional uint32 upLinkSynTcpWins = 48;</code>
       * @param value The upLinkSynTcpWins to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkSynTcpWins(int value) {

        upLinkSynTcpWins_ = value;
        bitField1_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 upLinkSynTcpWins = 48;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkSynTcpWins() {
        bitField1_ = (bitField1_ & ~0x00008000);
        upLinkSynTcpWins_ = 0;
        onChanged();
        return this;
      }

      private int downLinkSynTcpWins_ ;
      /**
       * <code>optional uint32 downLinkSynTcpWins = 49;</code>
       * @return Whether the downLinkSynTcpWins field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkSynTcpWins() {
        return ((bitField1_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint32 downLinkSynTcpWins = 49;</code>
       * @return The downLinkSynTcpWins.
       */
      @java.lang.Override
      public int getDownLinkSynTcpWins() {
        return downLinkSynTcpWins_;
      }
      /**
       * <code>optional uint32 downLinkSynTcpWins = 49;</code>
       * @param value The downLinkSynTcpWins to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkSynTcpWins(int value) {

        downLinkSynTcpWins_ = value;
        bitField1_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 downLinkSynTcpWins = 49;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkSynTcpWins() {
        bitField1_ = (bitField1_ & ~0x00010000);
        downLinkSynTcpWins_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString upLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes upLinkTcpOpts = 50;</code>
       * @return Whether the upLinkTcpOpts field is set.
       */
      @java.lang.Override
      public boolean hasUpLinkTcpOpts() {
        return ((bitField1_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes upLinkTcpOpts = 50;</code>
       * @return The upLinkTcpOpts.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getUpLinkTcpOpts() {
        return upLinkTcpOpts_;
      }
      /**
       * <code>optional bytes upLinkTcpOpts = 50;</code>
       * @param value The upLinkTcpOpts to set.
       * @return This builder for chaining.
       */
      public Builder setUpLinkTcpOpts(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        upLinkTcpOpts_ = value;
        bitField1_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes upLinkTcpOpts = 50;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpLinkTcpOpts() {
        bitField1_ = (bitField1_ & ~0x00020000);
        upLinkTcpOpts_ = getDefaultInstance().getUpLinkTcpOpts();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString downLinkTcpOpts_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes downLinkTcpOpts = 51;</code>
       * @return Whether the downLinkTcpOpts field is set.
       */
      @java.lang.Override
      public boolean hasDownLinkTcpOpts() {
        return ((bitField1_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes downLinkTcpOpts = 51;</code>
       * @return The downLinkTcpOpts.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDownLinkTcpOpts() {
        return downLinkTcpOpts_;
      }
      /**
       * <code>optional bytes downLinkTcpOpts = 51;</code>
       * @param value The downLinkTcpOpts to set.
       * @return This builder for chaining.
       */
      public Builder setDownLinkTcpOpts(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        downLinkTcpOpts_ = value;
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes downLinkTcpOpts = 51;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownLinkTcpOpts() {
        bitField1_ = (bitField1_ & ~0x00040000);
        downLinkTcpOpts_ = getDefaultInstance().getDownLinkTcpOpts();
        onChanged();
        return this;
      }

      private long upSesBytes_ ;
      /**
       * <code>optional uint64 upSesBytes = 52;</code>
       * @return Whether the upSesBytes field is set.
       */
      @java.lang.Override
      public boolean hasUpSesBytes() {
        return ((bitField1_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint64 upSesBytes = 52;</code>
       * @return The upSesBytes.
       */
      @java.lang.Override
      public long getUpSesBytes() {
        return upSesBytes_;
      }
      /**
       * <code>optional uint64 upSesBytes = 52;</code>
       * @param value The upSesBytes to set.
       * @return This builder for chaining.
       */
      public Builder setUpSesBytes(long value) {

        upSesBytes_ = value;
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 upSesBytes = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpSesBytes() {
        bitField1_ = (bitField1_ & ~0x00080000);
        upSesBytes_ = 0L;
        onChanged();
        return this;
      }

      private long downSesbytes_ ;
      /**
       * <code>optional uint64 downSesbytes = 53;</code>
       * @return Whether the downSesbytes field is set.
       */
      @java.lang.Override
      public boolean hasDownSesbytes() {
        return ((bitField1_ & 0x00100000) != 0);
      }
      /**
       * <code>optional uint64 downSesbytes = 53;</code>
       * @return The downSesbytes.
       */
      @java.lang.Override
      public long getDownSesbytes() {
        return downSesbytes_;
      }
      /**
       * <code>optional uint64 downSesbytes = 53;</code>
       * @param value The downSesbytes to set.
       * @return This builder for chaining.
       */
      public Builder setDownSesbytes(long value) {

        downSesbytes_ = value;
        bitField1_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 downSesbytes = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearDownSesbytes() {
        bitField1_ = (bitField1_ & ~0x00100000);
        downSesbytes_ = 0L;
        onChanged();
        return this;
      }

      private long sesBytes_ ;
      /**
       * <code>optional uint64 sesBytes = 54;</code>
       * @return Whether the sesBytes field is set.
       */
      @java.lang.Override
      public boolean hasSesBytes() {
        return ((bitField1_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint64 sesBytes = 54;</code>
       * @return The sesBytes.
       */
      @java.lang.Override
      public long getSesBytes() {
        return sesBytes_;
      }
      /**
       * <code>optional uint64 sesBytes = 54;</code>
       * @param value The sesBytes to set.
       * @return This builder for chaining.
       */
      public Builder setSesBytes(long value) {

        sesBytes_ = value;
        bitField1_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 sesBytes = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearSesBytes() {
        bitField1_ = (bitField1_ & ~0x00200000);
        sesBytes_ = 0L;
        onChanged();
        return this;
      }

      private float sesBytesRatio_ ;
      /**
       * <code>optional float sesBytesRatio = 55;</code>
       * @return Whether the sesBytesRatio field is set.
       */
      @java.lang.Override
      public boolean hasSesBytesRatio() {
        return ((bitField1_ & 0x00400000) != 0);
      }
      /**
       * <code>optional float sesBytesRatio = 55;</code>
       * @return The sesBytesRatio.
       */
      @java.lang.Override
      public float getSesBytesRatio() {
        return sesBytesRatio_;
      }
      /**
       * <code>optional float sesBytesRatio = 55;</code>
       * @param value The sesBytesRatio to set.
       * @return This builder for chaining.
       */
      public Builder setSesBytesRatio(float value) {

        sesBytesRatio_ = value;
        bitField1_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional float sesBytesRatio = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearSesBytesRatio() {
        bitField1_ = (bitField1_ & ~0x00400000);
        sesBytesRatio_ = 0F;
        onChanged();
        return this;
      }

      private float payLenRatio_ ;
      /**
       * <code>optional float payLenRatio = 56;</code>
       * @return Whether the payLenRatio field is set.
       */
      @java.lang.Override
      public boolean hasPayLenRatio() {
        return ((bitField1_ & 0x00800000) != 0);
      }
      /**
       * <code>optional float payLenRatio = 56;</code>
       * @return The payLenRatio.
       */
      @java.lang.Override
      public float getPayLenRatio() {
        return payLenRatio_;
      }
      /**
       * <code>optional float payLenRatio = 56;</code>
       * @param value The payLenRatio to set.
       * @return This builder for chaining.
       */
      public Builder setPayLenRatio(float value) {

        payLenRatio_ = value;
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional float payLenRatio = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayLenRatio() {
        bitField1_ = (bitField1_ & ~0x00800000);
        payLenRatio_ = 0F;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipAsnDestination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipAsnDestination = 57;</code>
       * @return Whether the ipAsnDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpAsnDestination() {
        return ((bitField1_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes ipAsnDestination = 57;</code>
       * @return The ipAsnDestination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpAsnDestination() {
        return ipAsnDestination_;
      }
      /**
       * <code>optional bytes ipAsnDestination = 57;</code>
       * @param value The ipAsnDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpAsnDestination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipAsnDestination_ = value;
        bitField1_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipAsnDestination = 57;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpAsnDestination() {
        bitField1_ = (bitField1_ & ~0x01000000);
        ipAsnDestination_ = getDefaultInstance().getIpAsnDestination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipAsnSource_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipAsnSource = 58;</code>
       * @return Whether the ipAsnSource field is set.
       */
      @java.lang.Override
      public boolean hasIpAsnSource() {
        return ((bitField1_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes ipAsnSource = 58;</code>
       * @return The ipAsnSource.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpAsnSource() {
        return ipAsnSource_;
      }
      /**
       * <code>optional bytes ipAsnSource = 58;</code>
       * @param value The ipAsnSource to set.
       * @return This builder for chaining.
       */
      public Builder setIpAsnSource(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipAsnSource_ = value;
        bitField1_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipAsnSource = 58;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpAsnSource() {
        bitField1_ = (bitField1_ & ~0x02000000);
        ipAsnSource_ = getDefaultInstance().getIpAsnSource();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipBaseProto_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipBaseProto = 59;</code>
       * @return Whether the ipBaseProto field is set.
       */
      @java.lang.Override
      public boolean hasIpBaseProto() {
        return ((bitField1_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes ipBaseProto = 59;</code>
       * @return The ipBaseProto.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpBaseProto() {
        return ipBaseProto_;
      }
      /**
       * <code>optional bytes ipBaseProto = 59;</code>
       * @param value The ipBaseProto to set.
       * @return This builder for chaining.
       */
      public Builder setIpBaseProto(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipBaseProto_ = value;
        bitField1_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipBaseProto = 59;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpBaseProto() {
        bitField1_ = (bitField1_ & ~0x04000000);
        ipBaseProto_ = getDefaultInstance().getIpBaseProto();
        onChanged();
        return this;
      }

      private long ipBeginTime_ ;
      /**
       * <code>optional uint64 ipBeginTime = 60;</code>
       * @return Whether the ipBeginTime field is set.
       */
      @java.lang.Override
      public boolean hasIpBeginTime() {
        return ((bitField1_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint64 ipBeginTime = 60;</code>
       * @return The ipBeginTime.
       */
      @java.lang.Override
      public long getIpBeginTime() {
        return ipBeginTime_;
      }
      /**
       * <code>optional uint64 ipBeginTime = 60;</code>
       * @param value The ipBeginTime to set.
       * @return This builder for chaining.
       */
      public Builder setIpBeginTime(long value) {

        ipBeginTime_ = value;
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ipBeginTime = 60;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpBeginTime() {
        bitField1_ = (bitField1_ & ~0x08000000);
        ipBeginTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipCityDestination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipCityDestination = 61;</code>
       * @return Whether the ipCityDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpCityDestination() {
        return ((bitField1_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes ipCityDestination = 61;</code>
       * @return The ipCityDestination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpCityDestination() {
        return ipCityDestination_;
      }
      /**
       * <code>optional bytes ipCityDestination = 61;</code>
       * @param value The ipCityDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpCityDestination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipCityDestination_ = value;
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipCityDestination = 61;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpCityDestination() {
        bitField1_ = (bitField1_ & ~0x10000000);
        ipCityDestination_ = getDefaultInstance().getIpCityDestination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipCitySource_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipCitySource = 62;</code>
       * @return Whether the ipCitySource field is set.
       */
      @java.lang.Override
      public boolean hasIpCitySource() {
        return ((bitField1_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes ipCitySource = 62;</code>
       * @return The ipCitySource.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpCitySource() {
        return ipCitySource_;
      }
      /**
       * <code>optional bytes ipCitySource = 62;</code>
       * @param value The ipCitySource to set.
       * @return This builder for chaining.
       */
      public Builder setIpCitySource(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipCitySource_ = value;
        bitField1_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipCitySource = 62;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpCitySource() {
        bitField1_ = (bitField1_ & ~0x20000000);
        ipCitySource_ = getDefaultInstance().getIpCitySource();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipCountryDestination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipCountryDestination = 63;</code>
       * @return Whether the ipCountryDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpCountryDestination() {
        return ((bitField1_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes ipCountryDestination = 63;</code>
       * @return The ipCountryDestination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpCountryDestination() {
        return ipCountryDestination_;
      }
      /**
       * <code>optional bytes ipCountryDestination = 63;</code>
       * @param value The ipCountryDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpCountryDestination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipCountryDestination_ = value;
        bitField1_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipCountryDestination = 63;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpCountryDestination() {
        bitField1_ = (bitField1_ & ~0x40000000);
        ipCountryDestination_ = getDefaultInstance().getIpCountryDestination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipCountrySource_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipCountrySource = 64;</code>
       * @return Whether the ipCountrySource field is set.
       */
      @java.lang.Override
      public boolean hasIpCountrySource() {
        return ((bitField1_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes ipCountrySource = 64;</code>
       * @return The ipCountrySource.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpCountrySource() {
        return ipCountrySource_;
      }
      /**
       * <code>optional bytes ipCountrySource = 64;</code>
       * @param value The ipCountrySource to set.
       * @return This builder for chaining.
       */
      public Builder setIpCountrySource(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipCountrySource_ = value;
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipCountrySource = 64;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpCountrySource() {
        bitField1_ = (bitField1_ & ~0x80000000);
        ipCountrySource_ = getDefaultInstance().getIpCountrySource();
        onChanged();
        return this;
      }

      private long ipDataBytes_ ;
      /**
       * <code>optional uint64 ipDataBytes = 65;</code>
       * @return Whether the ipDataBytes field is set.
       */
      @java.lang.Override
      public boolean hasIpDataBytes() {
        return ((bitField2_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint64 ipDataBytes = 65;</code>
       * @return The ipDataBytes.
       */
      @java.lang.Override
      public long getIpDataBytes() {
        return ipDataBytes_;
      }
      /**
       * <code>optional uint64 ipDataBytes = 65;</code>
       * @param value The ipDataBytes to set.
       * @return This builder for chaining.
       */
      public Builder setIpDataBytes(long value) {

        ipDataBytes_ = value;
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ipDataBytes = 65;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpDataBytes() {
        bitField2_ = (bitField2_ & ~0x00000001);
        ipDataBytes_ = 0L;
        onChanged();
        return this;
      }

      private long ipDesiredBytes_ ;
      /**
       * <code>optional uint64 ipDesiredBytes = 66;</code>
       * @return Whether the ipDesiredBytes field is set.
       */
      @java.lang.Override
      public boolean hasIpDesiredBytes() {
        return ((bitField2_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint64 ipDesiredBytes = 66;</code>
       * @return The ipDesiredBytes.
       */
      @java.lang.Override
      public long getIpDesiredBytes() {
        return ipDesiredBytes_;
      }
      /**
       * <code>optional uint64 ipDesiredBytes = 66;</code>
       * @param value The ipDesiredBytes to set.
       * @return This builder for chaining.
       */
      public Builder setIpDesiredBytes(long value) {

        ipDesiredBytes_ = value;
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ipDesiredBytes = 66;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpDesiredBytes() {
        bitField2_ = (bitField2_ & ~0x00000002);
        ipDesiredBytes_ = 0L;
        onChanged();
        return this;
      }

      private long ipDesiredBytesDestination_ ;
      /**
       * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
       * @return Whether the ipDesiredBytesDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpDesiredBytesDestination() {
        return ((bitField2_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
       * @return The ipDesiredBytesDestination.
       */
      @java.lang.Override
      public long getIpDesiredBytesDestination() {
        return ipDesiredBytesDestination_;
      }
      /**
       * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
       * @param value The ipDesiredBytesDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpDesiredBytesDestination(long value) {

        ipDesiredBytesDestination_ = value;
        bitField2_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ipDesiredBytesDestination = 67;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpDesiredBytesDestination() {
        bitField2_ = (bitField2_ & ~0x00000004);
        ipDesiredBytesDestination_ = 0L;
        onChanged();
        return this;
      }

      private long ipDesiredBytesSource_ ;
      /**
       * <code>optional uint64 ipDesiredBytesSource = 68;</code>
       * @return Whether the ipDesiredBytesSource field is set.
       */
      @java.lang.Override
      public boolean hasIpDesiredBytesSource() {
        return ((bitField2_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint64 ipDesiredBytesSource = 68;</code>
       * @return The ipDesiredBytesSource.
       */
      @java.lang.Override
      public long getIpDesiredBytesSource() {
        return ipDesiredBytesSource_;
      }
      /**
       * <code>optional uint64 ipDesiredBytesSource = 68;</code>
       * @param value The ipDesiredBytesSource to set.
       * @return This builder for chaining.
       */
      public Builder setIpDesiredBytesSource(long value) {

        ipDesiredBytesSource_ = value;
        bitField2_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ipDesiredBytesSource = 68;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpDesiredBytesSource() {
        bitField2_ = (bitField2_ & ~0x00000008);
        ipDesiredBytesSource_ = 0L;
        onChanged();
        return this;
      }

      private int ipDestination_ ;
      /**
       * <code>optional uint32 ipDestination = 69;</code>
       * @return Whether the ipDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpDestination() {
        return ((bitField2_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 ipDestination = 69;</code>
       * @return The ipDestination.
       */
      @java.lang.Override
      public int getIpDestination() {
        return ipDestination_;
      }
      /**
       * <code>optional uint32 ipDestination = 69;</code>
       * @param value The ipDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpDestination(int value) {

        ipDestination_ = value;
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ipDestination = 69;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpDestination() {
        bitField2_ = (bitField2_ & ~0x00000010);
        ipDestination_ = 0;
        onChanged();
        return this;
      }

      private int ipDuration_ ;
      /**
       * <code>optional uint32 ipDuration = 70;</code>
       * @return Whether the ipDuration field is set.
       */
      @java.lang.Override
      public boolean hasIpDuration() {
        return ((bitField2_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 ipDuration = 70;</code>
       * @return The ipDuration.
       */
      @java.lang.Override
      public int getIpDuration() {
        return ipDuration_;
      }
      /**
       * <code>optional uint32 ipDuration = 70;</code>
       * @param value The ipDuration to set.
       * @return This builder for chaining.
       */
      public Builder setIpDuration(int value) {

        ipDuration_ = value;
        bitField2_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ipDuration = 70;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpDuration() {
        bitField2_ = (bitField2_ & ~0x00000020);
        ipDuration_ = 0;
        onChanged();
        return this;
      }

      private long ipEndTime_ ;
      /**
       * <code>optional uint64 ipEndTime = 71;</code>
       * @return Whether the ipEndTime field is set.
       */
      @java.lang.Override
      public boolean hasIpEndTime() {
        return ((bitField2_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint64 ipEndTime = 71;</code>
       * @return The ipEndTime.
       */
      @java.lang.Override
      public long getIpEndTime() {
        return ipEndTime_;
      }
      /**
       * <code>optional uint64 ipEndTime = 71;</code>
       * @param value The ipEndTime to set.
       * @return This builder for chaining.
       */
      public Builder setIpEndTime(long value) {

        ipEndTime_ = value;
        bitField2_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 ipEndTime = 71;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpEndTime() {
        bitField2_ = (bitField2_ & ~0x00000040);
        ipEndTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipIspDestination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipIspDestination = 72;</code>
       * @return Whether the ipIspDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpIspDestination() {
        return ((bitField2_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes ipIspDestination = 72;</code>
       * @return The ipIspDestination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpIspDestination() {
        return ipIspDestination_;
      }
      /**
       * <code>optional bytes ipIspDestination = 72;</code>
       * @param value The ipIspDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpIspDestination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipIspDestination_ = value;
        bitField2_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipIspDestination = 72;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpIspDestination() {
        bitField2_ = (bitField2_ & ~0x00000080);
        ipIspDestination_ = getDefaultInstance().getIpIspDestination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipIspSource_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipIspSource = 73;</code>
       * @return Whether the ipIspSource field is set.
       */
      @java.lang.Override
      public boolean hasIpIspSource() {
        return ((bitField2_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes ipIspSource = 73;</code>
       * @return The ipIspSource.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpIspSource() {
        return ipIspSource_;
      }
      /**
       * <code>optional bytes ipIspSource = 73;</code>
       * @param value The ipIspSource to set.
       * @return This builder for chaining.
       */
      public Builder setIpIspSource(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipIspSource_ = value;
        bitField2_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipIspSource = 73;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpIspSource() {
        bitField2_ = (bitField2_ & ~0x00000100);
        ipIspSource_ = getDefaultInstance().getIpIspSource();
        onChanged();
        return this;
      }

      private float ipLatitudeDestination_ ;
      /**
       * <code>optional float ipLatitudeDestination = 74;</code>
       * @return Whether the ipLatitudeDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpLatitudeDestination() {
        return ((bitField2_ & 0x00000200) != 0);
      }
      /**
       * <code>optional float ipLatitudeDestination = 74;</code>
       * @return The ipLatitudeDestination.
       */
      @java.lang.Override
      public float getIpLatitudeDestination() {
        return ipLatitudeDestination_;
      }
      /**
       * <code>optional float ipLatitudeDestination = 74;</code>
       * @param value The ipLatitudeDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpLatitudeDestination(float value) {

        ipLatitudeDestination_ = value;
        bitField2_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional float ipLatitudeDestination = 74;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpLatitudeDestination() {
        bitField2_ = (bitField2_ & ~0x00000200);
        ipLatitudeDestination_ = 0F;
        onChanged();
        return this;
      }

      private float ipLatitudeSource_ ;
      /**
       * <code>optional float ipLatitudeSource = 75;</code>
       * @return Whether the ipLatitudeSource field is set.
       */
      @java.lang.Override
      public boolean hasIpLatitudeSource() {
        return ((bitField2_ & 0x00000400) != 0);
      }
      /**
       * <code>optional float ipLatitudeSource = 75;</code>
       * @return The ipLatitudeSource.
       */
      @java.lang.Override
      public float getIpLatitudeSource() {
        return ipLatitudeSource_;
      }
      /**
       * <code>optional float ipLatitudeSource = 75;</code>
       * @param value The ipLatitudeSource to set.
       * @return This builder for chaining.
       */
      public Builder setIpLatitudeSource(float value) {

        ipLatitudeSource_ = value;
        bitField2_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional float ipLatitudeSource = 75;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpLatitudeSource() {
        bitField2_ = (bitField2_ & ~0x00000400);
        ipLatitudeSource_ = 0F;
        onChanged();
        return this;
      }

      private float ipLongitudeDestination_ ;
      /**
       * <code>optional float ipLongitudeDestination = 76;</code>
       * @return Whether the ipLongitudeDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpLongitudeDestination() {
        return ((bitField2_ & 0x00000800) != 0);
      }
      /**
       * <code>optional float ipLongitudeDestination = 76;</code>
       * @return The ipLongitudeDestination.
       */
      @java.lang.Override
      public float getIpLongitudeDestination() {
        return ipLongitudeDestination_;
      }
      /**
       * <code>optional float ipLongitudeDestination = 76;</code>
       * @param value The ipLongitudeDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpLongitudeDestination(float value) {

        ipLongitudeDestination_ = value;
        bitField2_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional float ipLongitudeDestination = 76;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpLongitudeDestination() {
        bitField2_ = (bitField2_ & ~0x00000800);
        ipLongitudeDestination_ = 0F;
        onChanged();
        return this;
      }

      private float ipLongitudeSource_ ;
      /**
       * <code>optional float ipLongitudeSource = 77;</code>
       * @return Whether the ipLongitudeSource field is set.
       */
      @java.lang.Override
      public boolean hasIpLongitudeSource() {
        return ((bitField2_ & 0x00001000) != 0);
      }
      /**
       * <code>optional float ipLongitudeSource = 77;</code>
       * @return The ipLongitudeSource.
       */
      @java.lang.Override
      public float getIpLongitudeSource() {
        return ipLongitudeSource_;
      }
      /**
       * <code>optional float ipLongitudeSource = 77;</code>
       * @param value The ipLongitudeSource to set.
       * @return This builder for chaining.
       */
      public Builder setIpLongitudeSource(float value) {

        ipLongitudeSource_ = value;
        bitField2_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional float ipLongitudeSource = 77;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpLongitudeSource() {
        bitField2_ = (bitField2_ & ~0x00001000);
        ipLongitudeSource_ = 0F;
        onChanged();
        return this;
      }

      private int ipPackets_ ;
      /**
       * <code>optional uint32 ipPackets = 78;</code>
       * @return Whether the ipPackets field is set.
       */
      @java.lang.Override
      public boolean hasIpPackets() {
        return ((bitField2_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint32 ipPackets = 78;</code>
       * @return The ipPackets.
       */
      @java.lang.Override
      public int getIpPackets() {
        return ipPackets_;
      }
      /**
       * <code>optional uint32 ipPackets = 78;</code>
       * @param value The ipPackets to set.
       * @return This builder for chaining.
       */
      public Builder setIpPackets(int value) {

        ipPackets_ = value;
        bitField2_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ipPackets = 78;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpPackets() {
        bitField2_ = (bitField2_ & ~0x00002000);
        ipPackets_ = 0;
        onChanged();
        return this;
      }

      private int ipProto_ ;
      /**
       * <code>optional uint32 ipProto = 79;</code>
       * @return Whether the ipProto field is set.
       */
      @java.lang.Override
      public boolean hasIpProto() {
        return ((bitField2_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 ipProto = 79;</code>
       * @return The ipProto.
       */
      @java.lang.Override
      public int getIpProto() {
        return ipProto_;
      }
      /**
       * <code>optional uint32 ipProto = 79;</code>
       * @param value The ipProto to set.
       * @return This builder for chaining.
       */
      public Builder setIpProto(int value) {

        ipProto_ = value;
        bitField2_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ipProto = 79;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpProto() {
        bitField2_ = (bitField2_ & ~0x00004000);
        ipProto_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipProtoPath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipProtoPath = 80;</code>
       * @return Whether the ipProtoPath field is set.
       */
      @java.lang.Override
      public boolean hasIpProtoPath() {
        return ((bitField2_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes ipProtoPath = 80;</code>
       * @return The ipProtoPath.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpProtoPath() {
        return ipProtoPath_;
      }
      /**
       * <code>optional bytes ipProtoPath = 80;</code>
       * @param value The ipProtoPath to set.
       * @return This builder for chaining.
       */
      public Builder setIpProtoPath(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipProtoPath_ = value;
        bitField2_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipProtoPath = 80;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpProtoPath() {
        bitField2_ = (bitField2_ & ~0x00008000);
        ipProtoPath_ = getDefaultInstance().getIpProtoPath();
        onChanged();
        return this;
      }

      private int ipSource_ ;
      /**
       * <code>optional uint32 ipSource = 81;</code>
       * @return Whether the ipSource field is set.
       */
      @java.lang.Override
      public boolean hasIpSource() {
        return ((bitField2_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint32 ipSource = 81;</code>
       * @return The ipSource.
       */
      @java.lang.Override
      public int getIpSource() {
        return ipSource_;
      }
      /**
       * <code>optional uint32 ipSource = 81;</code>
       * @param value The ipSource to set.
       * @return This builder for chaining.
       */
      public Builder setIpSource(int value) {

        ipSource_ = value;
        bitField2_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ipSource = 81;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpSource() {
        bitField2_ = (bitField2_ & ~0x00010000);
        ipSource_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipStateDestination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipStateDestination = 82;</code>
       * @return Whether the ipStateDestination field is set.
       */
      @java.lang.Override
      public boolean hasIpStateDestination() {
        return ((bitField2_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes ipStateDestination = 82;</code>
       * @return The ipStateDestination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpStateDestination() {
        return ipStateDestination_;
      }
      /**
       * <code>optional bytes ipStateDestination = 82;</code>
       * @param value The ipStateDestination to set.
       * @return This builder for chaining.
       */
      public Builder setIpStateDestination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipStateDestination_ = value;
        bitField2_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipStateDestination = 82;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpStateDestination() {
        bitField2_ = (bitField2_ & ~0x00020000);
        ipStateDestination_ = getDefaultInstance().getIpStateDestination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipStateSource_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipStateSource = 83;</code>
       * @return Whether the ipStateSource field is set.
       */
      @java.lang.Override
      public boolean hasIpStateSource() {
        return ((bitField2_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes ipStateSource = 83;</code>
       * @return The ipStateSource.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpStateSource() {
        return ipStateSource_;
      }
      /**
       * <code>optional bytes ipStateSource = 83;</code>
       * @param value The ipStateSource to set.
       * @return This builder for chaining.
       */
      public Builder setIpStateSource(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipStateSource_ = value;
        bitField2_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipStateSource = 83;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpStateSource() {
        bitField2_ = (bitField2_ & ~0x00040000);
        ipStateSource_ = getDefaultInstance().getIpStateSource();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipUpperProto_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipUpperProto = 84;</code>
       * @return Whether the ipUpperProto field is set.
       */
      @java.lang.Override
      public boolean hasIpUpperProto() {
        return ((bitField2_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes ipUpperProto = 84;</code>
       * @return The ipUpperProto.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpUpperProto() {
        return ipUpperProto_;
      }
      /**
       * <code>optional bytes ipUpperProto = 84;</code>
       * @param value The ipUpperProto to set.
       * @return This builder for chaining.
       */
      public Builder setIpUpperProto(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipUpperProto_ = value;
        bitField2_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipUpperProto = 84;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpUpperProto() {
        bitField2_ = (bitField2_ & ~0x00080000);
        ipUpperProto_ = getDefaultInstance().getIpUpperProto();
        onChanged();
        return this;
      }

      private int ipVersion_ ;
      /**
       * <code>optional uint32 ipVersion = 85;</code>
       * @return Whether the ipVersion field is set.
       */
      @java.lang.Override
      public boolean hasIpVersion() {
        return ((bitField2_ & 0x00100000) != 0);
      }
      /**
       * <code>optional uint32 ipVersion = 85;</code>
       * @return The ipVersion.
       */
      @java.lang.Override
      public int getIpVersion() {
        return ipVersion_;
      }
      /**
       * <code>optional uint32 ipVersion = 85;</code>
       * @param value The ipVersion to set.
       * @return This builder for chaining.
       */
      public Builder setIpVersion(int value) {

        ipVersion_ = value;
        bitField2_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ipVersion = 85;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpVersion() {
        bitField2_ = (bitField2_ & ~0x00100000);
        ipVersion_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipv6Destination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipv6Destination = 86;</code>
       * @return Whether the ipv6Destination field is set.
       */
      @java.lang.Override
      public boolean hasIpv6Destination() {
        return ((bitField2_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes ipv6Destination = 86;</code>
       * @return The ipv6Destination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpv6Destination() {
        return ipv6Destination_;
      }
      /**
       * <code>optional bytes ipv6Destination = 86;</code>
       * @param value The ipv6Destination to set.
       * @return This builder for chaining.
       */
      public Builder setIpv6Destination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipv6Destination_ = value;
        bitField2_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipv6Destination = 86;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpv6Destination() {
        bitField2_ = (bitField2_ & ~0x00200000);
        ipv6Destination_ = getDefaultInstance().getIpv6Destination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ipv6Source_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ipv6Source = 87;</code>
       * @return Whether the ipv6Source field is set.
       */
      @java.lang.Override
      public boolean hasIpv6Source() {
        return ((bitField2_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes ipv6Source = 87;</code>
       * @return The ipv6Source.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getIpv6Source() {
        return ipv6Source_;
      }
      /**
       * <code>optional bytes ipv6Source = 87;</code>
       * @param value The ipv6Source to set.
       * @return This builder for chaining.
       */
      public Builder setIpv6Source(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipv6Source_ = value;
        bitField2_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ipv6Source = 87;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpv6Source() {
        bitField2_ = (bitField2_ & ~0x00400000);
        ipv6Source_ = getDefaultInstance().getIpv6Source();
        onChanged();
        return this;
      }

      private int outIpDestination_ ;
      /**
       * <code>optional uint32 outIpDestination = 88;</code>
       * @return Whether the outIpDestination field is set.
       */
      @java.lang.Override
      public boolean hasOutIpDestination() {
        return ((bitField2_ & 0x00800000) != 0);
      }
      /**
       * <code>optional uint32 outIpDestination = 88;</code>
       * @return The outIpDestination.
       */
      @java.lang.Override
      public int getOutIpDestination() {
        return outIpDestination_;
      }
      /**
       * <code>optional uint32 outIpDestination = 88;</code>
       * @param value The outIpDestination to set.
       * @return This builder for chaining.
       */
      public Builder setOutIpDestination(int value) {

        outIpDestination_ = value;
        bitField2_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outIpDestination = 88;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutIpDestination() {
        bitField2_ = (bitField2_ & ~0x00800000);
        outIpDestination_ = 0;
        onChanged();
        return this;
      }

      private int outIpProto_ ;
      /**
       * <code>optional uint32 outIpProto = 89;</code>
       * @return Whether the outIpProto field is set.
       */
      @java.lang.Override
      public boolean hasOutIpProto() {
        return ((bitField2_ & 0x01000000) != 0);
      }
      /**
       * <code>optional uint32 outIpProto = 89;</code>
       * @return The outIpProto.
       */
      @java.lang.Override
      public int getOutIpProto() {
        return outIpProto_;
      }
      /**
       * <code>optional uint32 outIpProto = 89;</code>
       * @param value The outIpProto to set.
       * @return This builder for chaining.
       */
      public Builder setOutIpProto(int value) {

        outIpProto_ = value;
        bitField2_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outIpProto = 89;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutIpProto() {
        bitField2_ = (bitField2_ & ~0x01000000);
        outIpProto_ = 0;
        onChanged();
        return this;
      }

      private int outIpSource_ ;
      /**
       * <code>optional uint32 outIpSource = 90;</code>
       * @return Whether the outIpSource field is set.
       */
      @java.lang.Override
      public boolean hasOutIpSource() {
        return ((bitField2_ & 0x02000000) != 0);
      }
      /**
       * <code>optional uint32 outIpSource = 90;</code>
       * @return The outIpSource.
       */
      @java.lang.Override
      public int getOutIpSource() {
        return outIpSource_;
      }
      /**
       * <code>optional uint32 outIpSource = 90;</code>
       * @param value The outIpSource to set.
       * @return This builder for chaining.
       */
      public Builder setOutIpSource(int value) {

        outIpSource_ = value;
        bitField2_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outIpSource = 90;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutIpSource() {
        bitField2_ = (bitField2_ & ~0x02000000);
        outIpSource_ = 0;
        onChanged();
        return this;
      }

      private int outIpVersion_ ;
      /**
       * <code>optional uint32 outIpVersion = 91;</code>
       * @return Whether the outIpVersion field is set.
       */
      @java.lang.Override
      public boolean hasOutIpVersion() {
        return ((bitField2_ & 0x04000000) != 0);
      }
      /**
       * <code>optional uint32 outIpVersion = 91;</code>
       * @return The outIpVersion.
       */
      @java.lang.Override
      public int getOutIpVersion() {
        return outIpVersion_;
      }
      /**
       * <code>optional uint32 outIpVersion = 91;</code>
       * @param value The outIpVersion to set.
       * @return This builder for chaining.
       */
      public Builder setOutIpVersion(int value) {

        outIpVersion_ = value;
        bitField2_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outIpVersion = 91;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutIpVersion() {
        bitField2_ = (bitField2_ & ~0x04000000);
        outIpVersion_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString outIpv6Destination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes outIpv6Destination = 92;</code>
       * @return Whether the outIpv6Destination field is set.
       */
      @java.lang.Override
      public boolean hasOutIpv6Destination() {
        return ((bitField2_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes outIpv6Destination = 92;</code>
       * @return The outIpv6Destination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getOutIpv6Destination() {
        return outIpv6Destination_;
      }
      /**
       * <code>optional bytes outIpv6Destination = 92;</code>
       * @param value The outIpv6Destination to set.
       * @return This builder for chaining.
       */
      public Builder setOutIpv6Destination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        outIpv6Destination_ = value;
        bitField2_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes outIpv6Destination = 92;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutIpv6Destination() {
        bitField2_ = (bitField2_ & ~0x08000000);
        outIpv6Destination_ = getDefaultInstance().getOutIpv6Destination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString outIpv6Source_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes outIpv6Source = 93;</code>
       * @return Whether the outIpv6Source field is set.
       */
      @java.lang.Override
      public boolean hasOutIpv6Source() {
        return ((bitField2_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes outIpv6Source = 93;</code>
       * @return The outIpv6Source.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getOutIpv6Source() {
        return outIpv6Source_;
      }
      /**
       * <code>optional bytes outIpv6Source = 93;</code>
       * @param value The outIpv6Source to set.
       * @return This builder for chaining.
       */
      public Builder setOutIpv6Source(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        outIpv6Source_ = value;
        bitField2_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes outIpv6Source = 93;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutIpv6Source() {
        bitField2_ = (bitField2_ & ~0x10000000);
        outIpv6Source_ = getDefaultInstance().getOutIpv6Source();
        onChanged();
        return this;
      }

      private int outPortDestination_ ;
      /**
       * <code>optional uint32 outPortDestination = 94;</code>
       * @return Whether the outPortDestination field is set.
       */
      @java.lang.Override
      public boolean hasOutPortDestination() {
        return ((bitField2_ & 0x20000000) != 0);
      }
      /**
       * <code>optional uint32 outPortDestination = 94;</code>
       * @return The outPortDestination.
       */
      @java.lang.Override
      public int getOutPortDestination() {
        return outPortDestination_;
      }
      /**
       * <code>optional uint32 outPortDestination = 94;</code>
       * @param value The outPortDestination to set.
       * @return This builder for chaining.
       */
      public Builder setOutPortDestination(int value) {

        outPortDestination_ = value;
        bitField2_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outPortDestination = 94;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutPortDestination() {
        bitField2_ = (bitField2_ & ~0x20000000);
        outPortDestination_ = 0;
        onChanged();
        return this;
      }

      private int outPortSource_ ;
      /**
       * <code>optional uint32 outPortSource = 95;</code>
       * @return Whether the outPortSource field is set.
       */
      @java.lang.Override
      public boolean hasOutPortSource() {
        return ((bitField2_ & 0x40000000) != 0);
      }
      /**
       * <code>optional uint32 outPortSource = 95;</code>
       * @return The outPortSource.
       */
      @java.lang.Override
      public int getOutPortSource() {
        return outPortSource_;
      }
      /**
       * <code>optional uint32 outPortSource = 95;</code>
       * @param value The outPortSource to set.
       * @return This builder for chaining.
       */
      public Builder setOutPortSource(int value) {

        outPortSource_ = value;
        bitField2_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 outPortSource = 95;</code>
       * @return This builder for chaining.
       */
      public Builder clearOutPortSource() {
        bitField2_ = (bitField2_ & ~0x40000000);
        outPortSource_ = 0;
        onChanged();
        return this;
      }

      private int portDestination_ ;
      /**
       * <code>optional uint32 portDestination = 96;</code>
       * @return Whether the portDestination field is set.
       */
      @java.lang.Override
      public boolean hasPortDestination() {
        return ((bitField2_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 portDestination = 96;</code>
       * @return The portDestination.
       */
      @java.lang.Override
      public int getPortDestination() {
        return portDestination_;
      }
      /**
       * <code>optional uint32 portDestination = 96;</code>
       * @param value The portDestination to set.
       * @return This builder for chaining.
       */
      public Builder setPortDestination(int value) {

        portDestination_ = value;
        bitField2_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 portDestination = 96;</code>
       * @return This builder for chaining.
       */
      public Builder clearPortDestination() {
        bitField2_ = (bitField2_ & ~0x80000000);
        portDestination_ = 0;
        onChanged();
        return this;
      }

      private int portSource_ ;
      /**
       * <code>optional uint32 portSource = 97;</code>
       * @return Whether the portSource field is set.
       */
      @java.lang.Override
      public boolean hasPortSource() {
        return ((bitField3_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 portSource = 97;</code>
       * @return The portSource.
       */
      @java.lang.Override
      public int getPortSource() {
        return portSource_;
      }
      /**
       * <code>optional uint32 portSource = 97;</code>
       * @param value The portSource to set.
       * @return This builder for chaining.
       */
      public Builder setPortSource(int value) {

        portSource_ = value;
        bitField3_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 portSource = 97;</code>
       * @return This builder for chaining.
       */
      public Builder clearPortSource() {
        bitField3_ = (bitField3_ & ~0x00000001);
        portSource_ = 0;
        onChanged();
        return this;
      }

      private int tcpFinished_ ;
      /**
       * <code>optional uint32 tcpFinished = 98;</code>
       * @return Whether the tcpFinished field is set.
       */
      @java.lang.Override
      public boolean hasTcpFinished() {
        return ((bitField3_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 tcpFinished = 98;</code>
       * @return The tcpFinished.
       */
      @java.lang.Override
      public int getTcpFinished() {
        return tcpFinished_;
      }
      /**
       * <code>optional uint32 tcpFinished = 98;</code>
       * @param value The tcpFinished to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFinished(int value) {

        tcpFinished_ = value;
        bitField3_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 tcpFinished = 98;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFinished() {
        bitField3_ = (bitField3_ & ~0x00000002);
        tcpFinished_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString tcpFirstFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes tcpFirstFlag = 99;</code>
       * @return Whether the tcpFirstFlag field is set.
       */
      @java.lang.Override
      public boolean hasTcpFirstFlag() {
        return ((bitField3_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes tcpFirstFlag = 99;</code>
       * @return The tcpFirstFlag.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTcpFirstFlag() {
        return tcpFirstFlag_;
      }
      /**
       * <code>optional bytes tcpFirstFlag = 99;</code>
       * @param value The tcpFirstFlag to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFirstFlag(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        tcpFirstFlag_ = value;
        bitField3_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes tcpFirstFlag = 99;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFirstFlag() {
        bitField3_ = (bitField3_ & ~0x00000004);
        tcpFirstFlag_ = getDefaultInstance().getTcpFirstFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString tcpFlagsDestination_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes tcpFlagsDestination = 100;</code>
       * @return Whether the tcpFlagsDestination field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsDestination() {
        return ((bitField3_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes tcpFlagsDestination = 100;</code>
       * @return The tcpFlagsDestination.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTcpFlagsDestination() {
        return tcpFlagsDestination_;
      }
      /**
       * <code>optional bytes tcpFlagsDestination = 100;</code>
       * @param value The tcpFlagsDestination to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsDestination(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        tcpFlagsDestination_ = value;
        bitField3_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes tcpFlagsDestination = 100;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsDestination() {
        bitField3_ = (bitField3_ & ~0x00000008);
        tcpFlagsDestination_ = getDefaultInstance().getTcpFlagsDestination();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString tcpFlagsSource_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes tcpFlagsSource = 101;</code>
       * @return Whether the tcpFlagsSource field is set.
       */
      @java.lang.Override
      public boolean hasTcpFlagsSource() {
        return ((bitField3_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes tcpFlagsSource = 101;</code>
       * @return The tcpFlagsSource.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTcpFlagsSource() {
        return tcpFlagsSource_;
      }
      /**
       * <code>optional bytes tcpFlagsSource = 101;</code>
       * @param value The tcpFlagsSource to set.
       * @return This builder for chaining.
       */
      public Builder setTcpFlagsSource(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        tcpFlagsSource_ = value;
        bitField3_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes tcpFlagsSource = 101;</code>
       * @return This builder for chaining.
       */
      public Builder clearTcpFlagsSource() {
        bitField3_ = (bitField3_ & ~0x00000010);
        tcpFlagsSource_ = getDefaultInstance().getTcpFlagsSource();
        onChanged();
        return this;
      }

      private int transPacketLengthDestinationHighFrequency_ ;
      /**
       * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
       * @return Whether the transPacketLengthDestinationHighFrequency field is set.
       */
      @java.lang.Override
      public boolean hasTransPacketLengthDestinationHighFrequency() {
        return ((bitField3_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
       * @return The transPacketLengthDestinationHighFrequency.
       */
      @java.lang.Override
      public int getTransPacketLengthDestinationHighFrequency() {
        return transPacketLengthDestinationHighFrequency_;
      }
      /**
       * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
       * @param value The transPacketLengthDestinationHighFrequency to set.
       * @return This builder for chaining.
       */
      public Builder setTransPacketLengthDestinationHighFrequency(int value) {

        transPacketLengthDestinationHighFrequency_ = value;
        bitField3_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 transPacketLengthDestinationHighFrequency = 102;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransPacketLengthDestinationHighFrequency() {
        bitField3_ = (bitField3_ & ~0x00000020);
        transPacketLengthDestinationHighFrequency_ = 0;
        onChanged();
        return this;
      }

      private int transPacketLengthSourceHighFrequency_ ;
      /**
       * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
       * @return Whether the transPacketLengthSourceHighFrequency field is set.
       */
      @java.lang.Override
      public boolean hasTransPacketLengthSourceHighFrequency() {
        return ((bitField3_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
       * @return The transPacketLengthSourceHighFrequency.
       */
      @java.lang.Override
      public int getTransPacketLengthSourceHighFrequency() {
        return transPacketLengthSourceHighFrequency_;
      }
      /**
       * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
       * @param value The transPacketLengthSourceHighFrequency to set.
       * @return This builder for chaining.
       */
      public Builder setTransPacketLengthSourceHighFrequency(int value) {

        transPacketLengthSourceHighFrequency_ = value;
        bitField3_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 transPacketLengthSourceHighFrequency = 103;</code>
       * @return This builder for chaining.
       */
      public Builder clearTransPacketLengthSourceHighFrequency() {
        bitField3_ = (bitField3_ & ~0x00000040);
        transPacketLengthSourceHighFrequency_ = 0;
        onChanged();
        return this;
      }

      private int isEnd_ ;
      /**
       * <code>optional uint32 isEnd = 104;</code>
       * @return Whether the isEnd field is set.
       */
      @java.lang.Override
      public boolean hasIsEnd() {
        return ((bitField3_ & 0x00000080) != 0);
      }
      /**
       * <code>optional uint32 isEnd = 104;</code>
       * @return The isEnd.
       */
      @java.lang.Override
      public int getIsEnd() {
        return isEnd_;
      }
      /**
       * <code>optional uint32 isEnd = 104;</code>
       * @param value The isEnd to set.
       * @return This builder for chaining.
       */
      public Builder setIsEnd(int value) {

        isEnd_ = value;
        bitField3_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 isEnd = 104;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsEnd() {
        bitField3_ = (bitField3_ & ~0x00000080);
        isEnd_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:LinkInfo)
    }

    // @@protoc_insertion_point(class_scope:LinkInfo)
    private static final LinkInfoOuterClass.LinkInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new LinkInfoOuterClass.LinkInfo();
    }

    public static LinkInfoOuterClass.LinkInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<LinkInfo>
        PARSER = new com.google.protobuf.AbstractParser<LinkInfo>() {
      @java.lang.Override
      public LinkInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<LinkInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LinkInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public LinkInfoOuterClass.LinkInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_LinkInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_LinkInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016LinkInfo.proto\"\272\023\n\010LinkInfo\022\020\n\010portInf" +
      "o\030\001 \001(\014\022\023\n\013portInfoAtt\030\002 \001(\014\022\020\n\010upPayLen" +
      "\030\003 \001(\r\022\022\n\ndownPayLen\030\004 \001(\004\022\017\n\007tcpflag\030\005 " +
      "\001(\014\022\024\n\014upLinkPktNum\030\006 \001(\004\022\022\n\nupLinkSize\030" +
      "\007 \001(\004\022\027\n\017upLinkBigPktLen\030\010 \001(\r\022\027\n\017upLink" +
      "SmaPktLen\030\t \001(\r\022\027\n\017upLinkBigPktInt\030\n \001(\004" +
      "\022\027\n\017upLinkSmaPktInt\030\013 \001(\004\022\026\n\016downLinkPkt" +
      "Num\030\014 \001(\r\022\024\n\014downLinkSize\030\r \001(\r\022\031\n\021downL" +
      "inkBigPktLen\030\016 \001(\r\022\031\n\021downLinkSmaPktLen\030" +
      "\017 \001(\r\022\031\n\021downLinkBigPktInt\030\020 \001(\004\022\031\n\021down" +
      "LinkSmaPktInt\030\021 \001(\004\022\023\n\013firTtlByCli\030\022 \001(\r" +
      "\022\023\n\013firTtlBySrv\030\023 \001(\r\022\020\n\010appDirec\030\024 \001(\r\022" +
      "\026\n\016tcpFlagsFinCnt\030\025 \001(\r\022\026\n\016tcpFlagsSynCn" +
      "t\030\026 \001(\r\022\026\n\016tcpFlagsRstCnt\030\027 \001(\r\022\026\n\016tcpFl" +
      "agsPshCnt\030\030 \001(\r\022\026\n\016tcpFlagsAckCnt\030\031 \001(\r\022" +
      "\026\n\016tcpFlagsUrgCnt\030\032 \001(\r\022\026\n\016tcpFlagsEceCn" +
      "t\030\033 \001(\r\022\026\n\016tcpFlagsCwrCnt\030\034 \001(\r\022\025\n\rtcpFl" +
      "agsNSCnt\030\035 \001(\r\022\031\n\021tcpFlagsSynAckCnt\030\036 \001(" +
      "\r\022\r\n\005etags\030\037 \001(\014\022\r\n\005ttags\030  \001(\014\022\026\n\016upLin" +
      "kChecksum\030! \001(\r\022\030\n\020downLinkChecksum\030\" \001(" +
      "\r\022\026\n\016upLinkDesBytes\030# \001(\004\022\030\n\020downLinkDes" +
      "Bytes\030$ \001(\004\022\016\n\006stream\030% \001(\014\022\024\n\014upLinkStr" +
      "eam\030& \001(\014\022\026\n\016downLinkStream\030\' \001(\014\022\031\n\021tra" +
      "ns_payload_hex\030( \001(\014\022\031\n\021upLinkTransPayHe" +
      "x\030) \001(\014\022\033\n\023downLinkTransPayHex\030* \001(\014\022\027\n\017" +
      "upLinkPayLenSet\030+ \003(\r\022\031\n\021downLinkPayLenS" +
      "et\030, \003(\r\022\021\n\testablish\030- \001(\r\022\027\n\017upLinkSyn" +
      "SeqNum\030. \001(\r\022\031\n\021downLinkSynSeqNum\030/ \001(\r\022" +
      "\030\n\020upLinkSynTcpWins\0300 \001(\r\022\032\n\022downLinkSyn" +
      "TcpWins\0301 \001(\r\022\025\n\rupLinkTcpOpts\0302 \001(\014\022\027\n\017" +
      "downLinkTcpOpts\0303 \001(\014\022\022\n\nupSesBytes\0304 \001(" +
      "\004\022\024\n\014downSesbytes\0305 \001(\004\022\020\n\010sesBytes\0306 \001(" +
      "\004\022\025\n\rsesBytesRatio\0307 \001(\002\022\023\n\013payLenRatio\030" +
      "8 \001(\002\022\030\n\020ipAsnDestination\0309 \001(\014\022\023\n\013ipAsn" +
      "Source\030: \001(\014\022\023\n\013ipBaseProto\030; \001(\014\022\023\n\013ipB" +
      "eginTime\030< \001(\004\022\031\n\021ipCityDestination\030= \001(" +
      "\014\022\024\n\014ipCitySource\030> \001(\014\022\034\n\024ipCountryDest" +
      "ination\030? \001(\014\022\027\n\017ipCountrySource\030@ \001(\014\022\023" +
      "\n\013ipDataBytes\030A \001(\004\022\026\n\016ipDesiredBytes\030B " +
      "\001(\004\022!\n\031ipDesiredBytesDestination\030C \001(\004\022\034" +
      "\n\024ipDesiredBytesSource\030D \001(\004\022\025\n\ripDestin" +
      "ation\030E \001(\r\022\022\n\nipDuration\030F \001(\r\022\021\n\tipEnd" +
      "Time\030G \001(\004\022\030\n\020ipIspDestination\030H \001(\014\022\023\n\013" +
      "ipIspSource\030I \001(\014\022\035\n\025ipLatitudeDestinati" +
      "on\030J \001(\002\022\030\n\020ipLatitudeSource\030K \001(\002\022\036\n\026ip" +
      "LongitudeDestination\030L \001(\002\022\031\n\021ipLongitud" +
      "eSource\030M \001(\002\022\021\n\tipPackets\030N \001(\r\022\017\n\007ipPr" +
      "oto\030O \001(\r\022\023\n\013ipProtoPath\030P \001(\014\022\020\n\010ipSour" +
      "ce\030Q \001(\r\022\032\n\022ipStateDestination\030R \001(\014\022\025\n\r" +
      "ipStateSource\030S \001(\014\022\024\n\014ipUpperProto\030T \001(" +
      "\014\022\021\n\tipVersion\030U \001(\r\022\027\n\017ipv6Destination\030" +
      "V \001(\014\022\022\n\nipv6Source\030W \001(\014\022\030\n\020outIpDestin" +
      "ation\030X \001(\r\022\022\n\noutIpProto\030Y \001(\r\022\023\n\013outIp" +
      "Source\030Z \001(\r\022\024\n\014outIpVersion\030[ \001(\r\022\032\n\022ou" +
      "tIpv6Destination\030\\ \001(\014\022\025\n\routIpv6Source\030" +
      "] \001(\014\022\032\n\022outPortDestination\030^ \001(\r\022\025\n\rout" +
      "PortSource\030_ \001(\r\022\027\n\017portDestination\030` \001(" +
      "\r\022\022\n\nportSource\030a \001(\r\022\023\n\013tcpFinished\030b \001" +
      "(\r\022\024\n\014tcpFirstFlag\030c \001(\014\022\033\n\023tcpFlagsDest" +
      "ination\030d \001(\014\022\026\n\016tcpFlagsSource\030e \001(\014\0221\n" +
      ")transPacketLengthDestinationHighFrequen" +
      "cy\030f \001(\r\022,\n$transPacketLengthSourceHighF" +
      "requency\030g \001(\r\022\r\n\005isEnd\030h \001(\r"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_LinkInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_LinkInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_LinkInfo_descriptor,
        new java.lang.String[] { "PortInfo", "PortInfoAtt", "UpPayLen", "DownPayLen", "Tcpflag", "UpLinkPktNum", "UpLinkSize", "UpLinkBigPktLen", "UpLinkSmaPktLen", "UpLinkBigPktInt", "UpLinkSmaPktInt", "DownLinkPktNum", "DownLinkSize", "DownLinkBigPktLen", "DownLinkSmaPktLen", "DownLinkBigPktInt", "DownLinkSmaPktInt", "FirTtlByCli", "FirTtlBySrv", "AppDirec", "TcpFlagsFinCnt", "TcpFlagsSynCnt", "TcpFlagsRstCnt", "TcpFlagsPshCnt", "TcpFlagsAckCnt", "TcpFlagsUrgCnt", "TcpFlagsEceCnt", "TcpFlagsCwrCnt", "TcpFlagsNSCnt", "TcpFlagsSynAckCnt", "Etags", "Ttags", "UpLinkChecksum", "DownLinkChecksum", "UpLinkDesBytes", "DownLinkDesBytes", "Stream", "UpLinkStream", "DownLinkStream", "TransPayloadHex", "UpLinkTransPayHex", "DownLinkTransPayHex", "UpLinkPayLenSet", "DownLinkPayLenSet", "Establish", "UpLinkSynSeqNum", "DownLinkSynSeqNum", "UpLinkSynTcpWins", "DownLinkSynTcpWins", "UpLinkTcpOpts", "DownLinkTcpOpts", "UpSesBytes", "DownSesbytes", "SesBytes", "SesBytesRatio", "PayLenRatio", "IpAsnDestination", "IpAsnSource", "IpBaseProto", "IpBeginTime", "IpCityDestination", "IpCitySource", "IpCountryDestination", "IpCountrySource", "IpDataBytes", "IpDesiredBytes", "IpDesiredBytesDestination", "IpDesiredBytesSource", "IpDestination", "IpDuration", "IpEndTime", "IpIspDestination", "IpIspSource", "IpLatitudeDestination", "IpLatitudeSource", "IpLongitudeDestination", "IpLongitudeSource", "IpPackets", "IpProto", "IpProtoPath", "IpSource", "IpStateDestination", "IpStateSource", "IpUpperProto", "IpVersion", "Ipv6Destination", "Ipv6Source", "OutIpDestination", "OutIpProto", "OutIpSource", "OutIpVersion", "OutIpv6Destination", "OutIpv6Source", "OutPortDestination", "OutPortSource", "PortDestination", "PortSource", "TcpFinished", "TcpFirstFlag", "TcpFlagsDestination", "TcpFlagsSource", "TransPacketLengthDestinationHighFrequency", "TransPacketLengthSourceHighFrequency", "IsEnd", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
