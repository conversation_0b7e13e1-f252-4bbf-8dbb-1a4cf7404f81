package com.geeksec.common.constant;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;


/**
 * <AUTHOR>
 */
public class PanoramaOutPutTagConstant {

    /**
     * 标准待处理多规则类型告警日志输出标签
     */
    public static final OutputTag<AlertLog.ALERT_LOG> IOC_ALERT_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOC_ALERT_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOA_ALERT_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOA_ALERT_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> SANDBOX_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("SANDBOX_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> EMAIL_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("EMAIL_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> CRYPTO_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("CRYPTO_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOT_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOT_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));

    public static final OutputTag<AlertLog.ALERT_LOG> HTTP_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("HTTP_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> DNS_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("DNS_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> SSL_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("SSL_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> X509_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("X509_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));


    /**
     * 节点分流OutPutTag
     */
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_IP = new OutputTag<>("TAG_IP", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_DOMAIN = new OutputTag<>("TAG_DOMAIN", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_CERT = new OutputTag<>("TAG_CERT", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_ATTACK = new OutputTag<>("TAG_ATTACK", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_URL = new OutputTag<>("TAG_URL", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_ORG = new OutputTag<>("TAG_ORG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_UA = new OutputTag<>("TAG_UA", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_APT_GROUP = new OutputTag<>("TAG_APT_GROUP", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_EMAIL = new OutputTag<>("TAG_EMAIL", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_MAIL = new OutputTag<>("TAG_MAIL", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_FILE = new OutputTag<>("TAG_FILE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_SSLFINGER = new OutputTag<>("TAG_SSLFINGER", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_APP = new OutputTag<>("TAG_APP", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_DEVICE = new OutputTag<>("TAG_DEVICE", TypeInformation.of(Row.class));

    /**
     * 关联关系分流OutPutTag
     */
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_make_attack = new OutputTag<>("EDGE_make_attack", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_attack_to = new OutputTag<>("EDGE_attack_to", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_ip_belong_to_apt = new OutputTag<>("EDGE_ip_belong_to_apt", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_domain_belong_to_apt = new OutputTag<>("EDGE_domain_belong_to_apt", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_ip_belong_to_org = new OutputTag<>("EDGE_ip_belong_to_org", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_http_connect = new OutputTag<>("EDGE_http_connect", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_use_ua = new OutputTag<>("EDGE_client_use_ua", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_ua_connect_domain = new OutputTag<>("EDGE_ua_connect_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_http_connect_domain = new OutputTag<>("EDGE_client_http_connect_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_server_http_connect_domain = new OutputTag<>("EDGE_server_http_connect_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_http_connect_url = new OutputTag<>("EDGE_client_http_connect_url", TypeInformation.of(Row.class));


    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_query_domain = new OutputTag<>("EDGE_client_query_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_query_dns_server = new OutputTag<>("EDGE_client_query_dns_server", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_dns_server_resolves_domain = new OutputTag<>("EDGE_dns_server_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_parse_to = new OutputTag<>("EDGE_parse_to", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_use_cert = new OutputTag<>("EDGE_client_use_cert", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_server_use_cert = new OutputTag<>("EDGE_server_use_cert", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_cert_belong_to_org = new OutputTag<>("EDGE_cert_belong_to_org", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_cert_connect_sni = new OutputTag<>("EDGE_cert_connect_sni", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_cert_connect_sslfinger = new OutputTag<>("EDGE_cert_connect_sslfinger", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_use_sslfinger = new OutputTag<>("EDGE_client_use_sslfinger", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_server_use_sslfinger = new OutputTag<>("EDGE_server_use_sslfinger", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_sslfinger_connect_sni = new OutputTag<>("EDGE_sslfinger_connect_sni", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_send_mail = new OutputTag<>("EDGE_send_mail", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_receive_mail = new OutputTag<>("EDGE_receive_mail", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_include_file = new OutputTag<>("EDGE_include_file", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_send_file = new OutputTag<>("EDGE_send_file", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_receive_file = new OutputTag<>("EDGE_receive_file", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_file_connect_ip = new OutputTag<>("EDGE_file_connect_ip", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_file_connect_domain = new OutputTag<>("EDGE_file_connect_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_file_connect_url = new OutputTag<>("EDGE_file_connect_url ", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_sender_send_file = new OutputTag<>("EDGE_sender_send_file ", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_receiver_receive_file = new OutputTag<>("EDGE_receiver_receive_file ", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_attack_belong_to_apt_group = new OutputTag<>("EDGE_attack_belong_to_apt_group", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_app_connect_cert = new OutputTag<>("EDGE_app_connect_cert", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_use_app = new OutputTag<>("EDGE_client_use_app", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_app_belong_to_server = new OutputTag<>("EDGE_app_belong_to_server", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_attack_to_device = new OutputTag<>("EDGE_attack_to_device", TypeInformation.of(Row.class));
}


