package com.geeksec.entity.pojo;

import com.geeksec.common.utils.DomainValidator;
import com.geeksec.config.util.MD5;
import com.geeksec.entity.po.UaParseInfo;
import com.geeksec.entity.trans.HTTPTrans;
import com.geeksec.entity.trans.IPTrans;
import lombok.Data;
import org.apache.flink.types.Row;
import ua_parser.Parser;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class HTTPAlertTrans {
    private static final String HTTP_SCHEME = "http://";

    private static final Parser parser = new Parser();

    private long attackTime;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private String aptName;

    private List<HTTPTrans> http;

    /**
     * uri STRING, # 统一资源标识符
     * host STRING,    # 主机名
     * cookie STRING, # cookie
     * agent STRING,    # UA
     * referer STRING, # 引用URL
     * xff STRING, # X-Forwarded-For头
     * req_data # 请求数据
     * resp_data # 相应数据
     * method FIXED_STRING(10), # 请求方法
     * status INT16,    # 状态码
     * content_type STRING,
    * */
    private Row getHttpConnectRow(HTTPTrans httpTrans) {
        Row row = new Row(15);
        row.setField(0, "EDGE_http_connect");
        row.setField(1, sip.getIPAddr());
        row.setField(2, dip.getIPAddr());
        row.setField(3, attackTime);
        row.setField(4, httpTrans.getUri());
        row.setField(5, httpTrans.getHost().toLowerCase());
        row.setField(6, httpTrans.getCookie());
        row.setField(7, httpTrans.getAgent());
        row.setField(8, httpTrans.getReferer());
        row.setField(9, httpTrans.getXffIp());
        row.setField(10, httpTrans.getReqbody());
        row.setField(11, httpTrans.getRespbody());
        row.setField(12, httpTrans.getMethod());
        row.setField(13, httpTrans.getStatus());
        row.setField(14, httpTrans.getContentType());
        return row;
    }

    /**
     * ua_str STRING #ua字符串
     * os_name # 使用操作系统
     * device_name # 设备名称
    * */
    private Row getUaRow(String ua,String uaMd5,UaParseInfo uaParseInfo) throws UnsupportedEncodingException {
        Row row = new Row(5);
        row.setField(0, "TAG_UA");
        // vid
        row.setField(1, uaMd5);
        row.setField(2, ua);
        row.setField(3, uaParseInfo.getOsName());
        row.setField(4, uaParseInfo.getDeviceName());
        return row;
    }


    /**
    *  Client_IP ---> UA
     * 客户端使用UA
    * */
    private Row getClientUseUaRow(String uaMd5) {
        // client_use_ua Edge
        Row row = new Row(4);
        row.setField(0, "EDGE_client_use_ua");
        row.setField(1, sip.getIPAddr());
        row.setField(2, uaMd5);
        row.setField(3, 0);
        return row;
    }

    /**
     * UA ---> DOMAIN
     * UA访问域名
    * */
    private Row getUaConnectDomainRow(String uaMd5,HTTPTrans httpTrans) {
        Row row = new Row(4);
        row.setField(0, "EDGE_ua_connect_domain");
        row.setField(1, uaMd5);
        row.setField(2, MD5.getMd5(httpTrans.getHost().toLowerCase()));
        row.setField(3, 0);
        return row;
    }


    private Row getDomainRow(HTTPTrans httpTrans) {
        // Domain TAG
        Row row = new Row(3);
        row.setField(0, "TAG_DOMAIN");
        String domain = httpTrans.getHost().toLowerCase();
        // vid
        row.setField(1, MD5.getMd5(domain));
        row.setField(2, domain);
        return row;
    }

    /**
     * Client_IP ---> DOMAIN
     * 客户端访问域名
    * */
    private Row getClientDomainRow(HTTPTrans httpTrans) {
        Row row = new Row(4);
        row.setField(0, "EDGE_client_http_connect_domain");
        row.setField(1, sip.getIPAddr());
        row.setField(2, MD5.getMd5(httpTrans.getHost().toLowerCase()));
        row.setField(3, 0);
        return row;
    }

    /**
     * Server_IP ---> DOMAIN
     * 服务端部署HTTP服务
    * */
    private Row getServerDomainRow(HTTPTrans httpTrans) {
        Row row = new Row(4);
        row.setField(0, "EDGE_server_http_connect_domain");
        row.setField(1, dip.getIPAddr());
        row.setField(2, MD5.getMd5(httpTrans.getHost().toLowerCase()));
        row.setField(3, 0);
        return row;
    }

    public List<Row> getAllRows() throws UnsupportedEncodingException {

        // 进行初始化HTTP元数据解析
        List<Row> rows = new ArrayList<>();


        List<HTTPTrans> httpTrans = this.http;
        for(HTTPTrans httpTrans1:httpTrans){

            String ua = httpTrans1.getAgent();
            String uaMd5 = MD5.getMd5(ua);
            UaParseInfo uaParseInfo = UaParseInfo.init(ua, parser);

            rows.add(getUaRow(ua,uaMd5,uaParseInfo));
            rows.add(getClientUseUaRow(uaMd5));

            // 判断其主站host为域名才进行相关的点边操作
            String host = httpTrans1.getHost();
            if (DomainValidator.isValidDomain(host)) {
                rows.add(getDomainRow(httpTrans1));
                rows.add(getHttpConnectRow(httpTrans1));
                rows.add(getUaConnectDomainRow(uaMd5,httpTrans1));
                rows.add(getClientDomainRow(httpTrans1));
                rows.add(getServerDomainRow(httpTrans1));

                String url = HTTP_SCHEME + host + httpTrans1.getUri();

                rows.addAll(getUrlRow(url,httpTrans1));

                if(aptName != null && !aptName.isEmpty()){
                    Row row = new Row(4);
                    row.setField(0, "EDGE_domain_belong_to_apt");
                    row.setField(1, MD5.getMd5(host));
                    row.setField(2, aptName);
                    row.setField(3, 0);
                    rows.add(row);
                }
            }
        }
        return rows;
    }

    private List<Row> getUrlRow(String url, HTTPTrans httpTrans1) {
        List<Row> urlRows = new ArrayList<>();

        // url TAG
        Row tagRow = new Row(4);
        tagRow.setField(0, "TAG_URL");
        tagRow.setField(1, MD5.getMd5(url));
        tagRow.setField(2, url);
        tagRow.setField(3, httpTrans1.getUri());
        urlRows.add(tagRow);

        //
        Row urlEdgeRow = new Row(4);
        urlEdgeRow.setField(0, "EDGE_client_http_connect_url");
        urlEdgeRow.setField(1, sip.getIPAddr());
        urlEdgeRow.setField(2, MD5.getMd5(url));
        urlEdgeRow.setField(3, 0);
        urlRows.add(urlEdgeRow);

        return urlRows;
    }
}
