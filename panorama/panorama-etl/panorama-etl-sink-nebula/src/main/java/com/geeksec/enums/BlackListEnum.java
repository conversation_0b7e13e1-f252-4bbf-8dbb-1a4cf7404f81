package com.geeksec.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 黑名单属性
 * <p>
 *     文档注释:
 *     >0:在黑名单内
 * </p>
 *
 */
public enum BlackListEnum {

    NOT_BLACK(0, "非黑名单"),
    IP(0x0001, "黑IP"),
    DNS(0x0002, "黑域名"),
    TROJAN(0x0004, "木马"),
    WEB_SHELL(0x0008, "webshell"),
    URL(0x0010, "黑URL"),
    TRANSIT_MACHINE_TESTING(0x0020, "中转机检测"),
    TROJAN_LINE_DETECTION(0x0040, "木马上线检测"),
    TROJAN_CONNECTION_DETECTION(0x0080, "木马回连检测"),
    EMAIL(0x0100, "黑邮箱");

    /**
     * 值
     */
    private Integer code;

    /**
     * 类型
     */
    private String msg;

    BlackListEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, BlackListEnum> codeEnumMap = new HashMap<>();

    static {
        for (BlackListEnum e : BlackListEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        BlackListEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }

}
