package com.geeksec.entity.pojo;

import cn.hutool.json.JSONObject;
import com.geeksec.entity.trans.IPTrans;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AttackAlertTrans {
    private String attackId;
    private long attackTime;
    private String killChain;
    private String vendorId;
    private String threatType;
    private String confidence;
    private String appProto;
    private int detectType;
    private String pcapFile;

    // 攻击五元组：aip_aPort_app_vPort_vip
    private String attackTuple5;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;


    public List<Row> getAllRows() {

        List<Row> attackRows = new ArrayList<>();

        String[] pcapFileArray = pcapFile.split("/");
        String pcapName = pcapFileArray[pcapFileArray.length-1];

        // ATTACK TAG
        Row attackRow = new Row(12);
        String attackId = this.attackId;
        attackRow.setField(0, "TAG_ATTACK");
        attackRow.setField(1, attackId);
        attackRow.setField(2, attackTime);
        attackRow.setField(3, attackTuple5);
        attackRow.setField(4, threatType);
        attackRow.setField(5, killChain);
        attackRow.setField(6, confidence);
        attackRow.setField(7, vendorId);
        attackRow.setField(8, detectType);
        attackRow.setField(9, pcapFile);
        attackRow.setField(10, pcapName);
        attackRow.setField(11, StringUtils.EMPTY);
        attackRows.add(attackRow);

        // make_attack
        Row marow = new Row(4);
        marow.setField(0, "EDGE_make_attack");
        marow.setField(1, aipAddr);
        marow.setField(2, attackId);
        marow.setField(3, attackTime);
        attackRows.add(marow);

        // attack_to
        Row atrow = new Row(4);
        atrow.setField(0, "EDGE_attack_to");
        atrow.setField(1, attackId);
        atrow.setField(2, vipAddr);
        atrow.setField(3, attackTime);
        attackRows.add(atrow);

        return attackRows;
    }
}
