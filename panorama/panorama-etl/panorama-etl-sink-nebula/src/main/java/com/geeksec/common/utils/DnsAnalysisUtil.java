package com.geeksec.common.utils;

import com.geeksec.config.util.IpNetUtils;
import com.geeksec.entity.po.DnsParseResult;
import com.geeksec.entity.trans.DNSTrans;
import com.geeksec.enums.DnsTypeEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class DnsAnalysisUtil {

    /**
     * 判断单一的DNS协议元数据是否是最终解析
     * 主判断方法
     * */
    public static boolean isFinalResolution(DNSTrans dnsTrans) {
        // 1. 直接解析结果判断
        if (hasDirectResolution(dnsTrans)) {
            return true;
        }

        // 2. CNAME链处理
        if (hasValidCnameChain(dnsTrans)) {
            return true;
        }

        // 3. 权威应答处理
        return hasAuthoritativeAnswer(dnsTrans);
    }


    // 检查直接解析结果
    private static boolean hasDirectResolution(DNSTrans dnsTrans) {
        List<String> answerTypes = dnsTrans.getAnswerTypes();

        // 检查A记录
        boolean hasA = answerTypes.contains(DnsTypeEnum.A.getType()) &&
                dnsTrans.getAipCnt() != null &&
                dnsTrans.getAipCnt() > 0;

        // 检查AAAA记录
        boolean hasAAAA = answerTypes.contains(DnsTypeEnum.AAAA.getType()) &&
                dnsTrans.getIpv6() != null &&
                !dnsTrans.getIpv6().isEmpty();

        // 检查MX/TXT等特殊记录
        boolean hasSpecialRecord = answerTypes.stream()
                .anyMatch(type ->
                        type.equals(DnsTypeEnum.MX.getType()) ||
                                type.equals(DnsTypeEnum.TXT.getType()));

        return hasA || hasAAAA || hasSpecialRecord;
    }

    // 验证CNAME链有效性
    private static boolean hasValidCnameChain(DNSTrans dnsTrans) {
        if (dnsTrans.getCname() == null ||
                dnsTrans.getCname().isEmpty()) {
            return false;
        }

        // 检查附加记录中的解析
        if (isValidAdditionalRecord(dnsTrans)) {
            return true;
        }

        // 检查授权记录是否指向其他NS
        return dnsTrans.getAuthCnt() == null ||
                dnsTrans.getAuthCnt() == 0;
    }

    // 验证附加记录（Glue记录）
    private static boolean isValidAdditionalRecord(DNSTrans dnsTrans) {
        Integer addAnsType = dnsTrans.getAddAnsType();
        if (addAnsType == null) {
            return false;
        }

        String type = DnsTypeEnum.getTypeByCode(addAnsType);
        return DnsTypeEnum.A.getType().equals(type) ||
                DnsTypeEnum.AAAA.getType().equals(type);
    }

    // 处理权威应答
    private static boolean hasAuthoritativeAnswer(DNSTrans dnsTrans) {
        // SOA记录判断
        boolean hasSOA = dnsTrans.getAnswerTypes().contains(DnsTypeEnum.SOA.getType());

        // NS记录判断
        boolean hasNS = dnsTrans.getNsHostCnt() != null &&
                dnsTrans.getNsHostCnt() > 0;

        /*
         * 权威应答逻辑：
         * 1. 存在SOA记录 → 最终应答
         * 2. 只有NS记录且无附加IP → 需要进一步查询
         */
        return hasSOA || (hasNS && !hasValidAdditionalNS(dnsTrans));
    }

    // 验证NS记录的附加IP
    private static boolean hasValidAdditionalNS(DNSTrans dnsTrans) {
        return dnsTrans.getNsIpCnt() != null &&
                dnsTrans.getNsIpCnt() > 0 &&
                dnsTrans.getNsIp() != null &&
                !dnsTrans.getNsIp().isEmpty();
    }
    // 类型解析逻辑优化
    private static String resolveQtype(DNSTrans trans) {
        return DnsTypeEnum.getTypeByCode(trans.getQueType());
    }

    private static String resolveAnswerType(DNSTrans trans, String ipSource) {
        // 根据不同的IP来源确定类型
        switch (ipSource) {
            case "DIRECT_A":
                return DnsTypeEnum.A.getType();
            case "DIRECT_AAAA":
                return DnsTypeEnum.AAAA.getType();
            case "CNAME_GLUE":
                return DnsTypeEnum.getTypeByCode(trans.getAddAnsType());
            case "AUTHORITY_NS":
                return DnsTypeEnum.NS.getType();
            default:
                return String.join(",", trans.getAnswerTypes());
        }
    }

    public static List<DnsParseResult> analyzeDnsTransactions(List<DNSTrans> transactions, String dnsServerAddr) {
        return transactions.stream()
                .flatMap(trans -> processSingleTransaction(trans, dnsServerAddr).stream())
                .collect(Collectors.toList());
    }


    private static List<DnsParseResult> processSingleTransaction(DNSTrans trans,String dnsServerAddr) {
        List<DnsParseResult> results = new ArrayList<>();
        String domain = trans.getQuery();
        boolean isFinal = isFinalResolution(trans);
        String qType = resolveQtype(trans);

        // 收集所有IP及其来源类型
        Map<String, String> ipSources = new LinkedHashMap<>();

        // 填充IP来源
        populateDirectIps(trans, ipSources);
        populateCnameGlue(trans, ipSources);
        populateAuthorityIps(trans, ipSources);

        // 特殊记录处理
        handleSpecialRecords(trans, ipSources);

        // 构建结果对象
        ipSources.forEach((ip, source) -> {
            results.add(new DnsParseResult(
                    domain,
                    ip,
                    dnsServerAddr,
                    isFinal,
                    resolveAnswerType(trans, source),
                    qType
            ));
        });

        return results;
    }

    // 填充直接解析IP（带类型标识）
    private static void populateDirectIps(DNSTrans trans, Map<String, String> ipSources) {
        if (trans.getAip() != null) {
            trans.getAip().stream()
                    .map(IpNetUtils::convertIntToIpAddress)
                    .forEach(ip ->
                    ipSources.put(ip, "DIRECT_A"));
        }

        if (trans.getIpv6() != null) {
            trans.getIpv6().forEach(ip ->
                    ipSources.put(ip, "DIRECT_AAAA"));
        }
    }

    // 填充CNAME附加记录
    private static void populateCnameGlue(DNSTrans trans, Map<String, String> ipSources) {
        if (trans.getAddAnsRes() != null && trans.getAddAnsType() != null) {
            ipSources.put(trans.getAddAnsRes(), "CNAME_GLUE");
        }
    }

    // 填充权威记录
    private static void populateAuthorityIps(DNSTrans trans, Map<String, String> ipSources) {
        if (trans.getNsIp() != null) {
            trans.getNsIp()
                    .stream()
                    .map(IpNetUtils::convertIntToIpAddress)
                    .forEach(ip ->
                    ipSources.put(ip, "AUTHORITY_NS"));
        }
    }

    // 处理特殊记录类型
    private static void handleSpecialRecords(DNSTrans trans, Map<String, String> ipSources) {
        if (trans.getAnswerTypes().contains(DnsTypeEnum.MX.getType())) {
            ipSources.put("MX_RECORD", "SPECIAL_MX");
        }
        if (trans.getAnswerTypes().contains(DnsTypeEnum.TXT.getType())) {
            ipSources.put("TXT_RECORD", "SPECIAL_TXT");
        }
    }
}
