package com.geeksec.entity.pojo;

import com.geeksec.config.util.MD5;
import com.geeksec.entity.trans.IPTrans;
import lombok.Data;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class IotAlertTrans {

	private IPTrans sip;
	private IPTrans dip;
	private String aipAddr;
	private String vipAddr;
	private String attackId;

	// 设备/软件 厂商
	private String iotVendor;
	// 设备/软件 类型
	private String iotDeviceType;
	// 设备型号 / 软件版本
	private String iotModel;

	public List<Row> getAllRows() {

		// 统一录入IP相关节点&边信息
		List<Row> rows = new ArrayList<>();

		if(!iotVendor.isEmpty() || !iotDeviceType.isEmpty() || !iotModel.isEmpty()) {
			// DEVICE TAG
			String deviceTmp = String.format("%s_%s_%s", iotVendor, iotDeviceType, iotModel);
			Row deviceTagRow = new Row(5);
			deviceTagRow.setField(0, "TAG_APT_GROUP");
			deviceTagRow.setField(1, MD5.getMd5(deviceTmp));
			deviceTagRow.setField(2, iotVendor);
			deviceTagRow.setField(3, iotDeviceType);
			deviceTagRow.setField(4, iotModel);
			rows.add(deviceTagRow);

			// attack_to_device EDGE
			Row attackToDevice = new Row(4);
			attackToDevice.setField(0, "EDGE_attack_to_device");
			attackToDevice.setField(1, attackId);
			attackToDevice.setField(2, MD5.getMd5(deviceTmp));
			attackToDevice.setField(3, 0);
			rows.add(attackToDevice);
		}

		return rows;
	}
}
