package com.geeksec.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

class TrieNode {
    Map<Integer, TrieNode> children = new HashMap<>();
    boolean isEndOfCidr = false;
}

class CidrTrie {
    private final TrieNode root = new TrieNode();

    private int[] ipToIntArray(InetAddress inet) {
        byte[] bytes = inet.getAddress();
        int[] intArray = new int[bytes.length];
        for (int i = 0; i < bytes.length; i++) {
            intArray[i] = bytes[i] & 0xFF;
        }
        return intArray;
    }

    public void insert(String cidr) throws UnknownHostException {
        String[] parts = cidr.split("/");
        InetAddress inetAddress = InetAddress.getByName(parts[0]);
        int prefixLength = parts.length > 1 ? Integer.parseInt(parts[1]) :
                (inetAddress instanceof java.net.Inet4Address ? 32 : 128);

        int[] ipArray = ipToIntArray(inetAddress);
        TrieNode node = root;

        for (int i = 0; i < ipArray.length && prefixLength > 0; i++) {
            int remainingBits = Math.min(8, prefixLength);
            for (int j = 7; j >= 8 - remainingBits; j--) {
                int bit = (ipArray[i] >> j) & 1;
                node = node.children.computeIfAbsent(bit, k -> new TrieNode());
            }
            prefixLength -= remainingBits;
        }
        node.isEndOfCidr = true;
    }

    public boolean search(InetAddress inetAddress) {
        int[] ipArray = ipToIntArray(inetAddress);
        TrieNode node = root;

        for (int i = 0; i < ipArray.length; i++) {
            for (int j = 7; j >= 0; j--) {
                int bit = (ipArray[i] >> j) & 1;
                if (node.children.containsKey(bit)) {
                    node = node.children.get(bit);
                    if (node.isEndOfCidr) {
                        return true;
                    }
                } else {
                    return node.isEndOfCidr;
                }
            }
        }
        return node.isEndOfCidr;
    }
}

public class IpMatcher {

    private final static Logger logger = LoggerFactory.getLogger(IpMatcher.class);

    private final CidrTrie trie = new CidrTrie();
    private Set<String> cidrSet;

    // 初始化的时候进行一次从redis中的加载
    public IpMatcher(String redisHost, Integer redisPort)  {
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            this.cidrSet = jedis.smembers("ip_cidr_set");
            for (String cidr : cidrSet) {
                addCidr(cidr);
            }
            logger.info("Loaded {} CIDRs from Redis", cidrSet.size());
        } catch (Exception e) {
            logger.error("初始化CIDR IP 地址过滤器失败", e);
        }
    }

    public void addCidr(String cidr) {
        try {
            trie.insert(cidr);
            logger.debug("Added CIDR: {}", cidr);
        } catch (UnknownHostException e) {
            logger.error("Failed to add CIDR: {}", cidr, e);
        }
    }

    public boolean isIpInAnyCidr(String ip) {
        try {
            InetAddress inetAddress = InetAddress.getByName(ip);
            boolean result = trie.search(inetAddress);
            logger.debug("IP: {} matched: {}", ip, result);
            return result;
        } catch (UnknownHostException e) {
            logger.error("Failed to check IP: {}", ip, e);
            return false;
        }
    }

    public void printLoadedCidrs() {
        logger.info("Loaded CIDRs: {}", cidrSet);
    }
}