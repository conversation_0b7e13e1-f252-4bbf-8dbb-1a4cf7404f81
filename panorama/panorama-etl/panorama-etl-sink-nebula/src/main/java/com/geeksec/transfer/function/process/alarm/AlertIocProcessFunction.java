package com.geeksec.transfer.function.process.alarm;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.FileAlertTrans;
import com.geeksec.entity.pojo.IocAlertTrans;
import com.geeksec.entity.pojo.X509AlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertIocProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {

    private static final Logger logger = LoggerFactory.getLogger(AlertIocProcessFunction.class);

    /**
     *
     * 需要提取的点边关系
     * 点：
     * APT_GROUP
     * 边：
     * attack_belong_to_apt_group，ATTACK---> APT_GROUP
     * ip_belong_to_apt
    * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, Row>.Context context, Collector<Row> collector) throws Exception {
        try {
            // 提取IOC 告警日志元数据
            IocAlertTrans iocAlertTrans = AlertTools.createIocAlertTrans(alertLog);
            if (iocAlertTrans != null) {
                List<Row> rows = iocAlertTrans.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing Ioc alert log: {}", alertLog.getGuid(), e);
        }
    }
}
