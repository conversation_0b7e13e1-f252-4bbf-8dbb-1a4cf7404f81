package com.geeksec.entity.trans;

import lombok.Data;

/**
 * @author: jerryzhou
 * @date: 2024/7/22 20:45
 * @Description:
 **/
@Data
public class FileTrans {
    // 文件类型
    private String fileType;

    // 文件大小
    private Integer fileSize;

    // 文件md5值
    private String fileMd5;
    private String fileSha1;
    private String fileSha256;
    private String fileSha512;
    private String fileCrc32;

    // 文件路径
    private String filePath;

    // 关联IOC IP
    private String iocIp;

    // 关联IOC DOMAIN
    private String iocDomain;

    // 关联IOC URL
    private String iocUrl;

    // 沙箱报告路径
    private String sandboxReportUrl;

}
