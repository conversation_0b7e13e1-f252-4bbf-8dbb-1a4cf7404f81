package com.geeksec.transfer.function.process.protocol;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.common.utils.FileUtil;
import com.geeksec.entity.pojo.SslAlertTrans;
import com.geeksec.entity.pojo.X509AlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

/**
 * @author: jerryzhou
 * @date: 2024/9/20 10:03
 * @Description:
 **/
public class AlertX509ProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {

    private static final Logger logger = LoggerFactory.getLogger(AlertX509ProcessFunction.class);

    /**
     * 按照国家分类的公司、企业后缀知识库
     * */
    public static Map<String,List<String>> COUNTRY_COMPANY_SUFFIX_MAP = new HashMap<>();

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        InputStream countryCompanySuffixMapStream = this.getClass().getClassLoader().getResourceAsStream("companySuffixesByCountry.csv");
        BufferedReader countryCompanySuffixMapBuffer = new BufferedReader(new InputStreamReader(countryCompanySuffixMapStream));
        try {
            COUNTRY_COMPANY_SUFFIX_MAP = FileUtil.loadCountryCompanySuffixMap(countryCompanySuffixMapBuffer);
        } catch (Exception e) {
            throw new IOException("file load fail ==>", e);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
    }
    
    /**
     * 需要提取的点边关系
     * 点：
     * CERT
     * ORG
     * 边：
     * client_use_cert
     * server_use_cert
     * cert_belong_to_org
     * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, Row>.Context context, Collector<Row> collector) throws Exception {
        try {
            // 提取X509证书告警日志元数据
            X509AlertTrans x509Alert = AlertTools.createX509AlertTrans(alertLog);
            if (x509Alert != null) {
                List<Row> rows = x509Alert.getAllRows();
                for (Row row : rows) {
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing X509 alert log: {}", alertLog.getGuid(), e);
        }
    }
}
