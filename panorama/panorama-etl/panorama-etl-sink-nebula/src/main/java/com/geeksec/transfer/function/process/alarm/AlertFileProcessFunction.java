package com.geeksec.transfer.function.process.alarm;

import com.geeksec.common.utils.AlertTools;
import com.geeksec.entity.pojo.FileAlertTrans;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AlertFileProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG, Row> {

    private static final Logger logger = LoggerFactory.getLogger(AlertFileProcessFunction.class);

    /**
     * 需要提取的点边关系
     * 点：
     * FILE
     * 边：
     * send_file
     * receive_file
     * file_connect_ip
     * file_connect_domain
     * file_connect_url
     * sender_send_file
     * receiver_receive_file
     * */
    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, Context ctx, Collector<Row> collector) {
        try {
            FileAlertTrans fileAlert = AlertTools.createFileAlertTrans(alertLog);
            if (fileAlert != null) {
                List<Row> rows = fileAlert.getAllRows();
                for (Row row : rows){
                    collector.collect(row);
                }
            }
        } catch (Exception e) {
            logger.error("Error processing file alert log: {}", alertLog.getGuid(), e);
        }
    }
}