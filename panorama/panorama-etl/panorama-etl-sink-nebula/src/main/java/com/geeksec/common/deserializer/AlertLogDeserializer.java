package com.geeksec.common.deserializer;

import com.geeksec.proto.AlertLog;
import org.apache.commons.lang3.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * @author: jerryzhou
 * @date: 2024/7/17 17:33
 * @Description:
 **/
public class AlertLogDeserializer implements KafkaRecordDeserializationSchema<AlertLog.ALERT_LOG> {
    private static final Logger logger = LoggerFactory.getLogger(AlertLogDeserializer.class);

    @Override
    public TypeInformation<AlertLog.ALERT_LOG> getProducedType() {
        return TypeInformation.of(AlertLog.ALERT_LOG.class);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> consumerRecord, Collector<AlertLog.ALERT_LOG> collector) throws IOException {
        byte[] values = consumerRecord.value();
        if (values != null && values.length != 0){
            try {
                collector.collect(AlertLog.ALERT_LOG.parseFrom(values));
            } catch (Exception e) {
                throw new SerializationException("Error when serializing AlertLog byte[] " + e);
            }
        }
    }
}
