package com.geeksec.entity.po;

public class DnsParseResult {
    private final String domain;
    private final String serverIp;
    private final String dnsServer;
    private final boolean finalParse;
    private final String answerType;
    private final String dnsType;

    public DnsParseResult(String domain, String serverIp,
                          String dnsServer, boolean finalParse, String answerType, String dnsType) {
        this.domain = domain;
        this.serverIp = serverIp;
        this.dnsServer = dnsServer;
        this.finalParse = finalParse;
        this.answerType = answerType != null ? answerType : "UNKNOWN";
        this.dnsType = dnsType != null ? dnsType : "UNKNOWN";
    }

    // Getters
    public String getDomain() { return domain; }
    public String getServerIp() { return serverIp; }
    public String getDnsServer() { return dnsServer; }
    public boolean isFinalParse() { return finalParse; }
    public String getAnswerType() { return answerType; }
    public String getDnsType() { return dnsType; }
}
