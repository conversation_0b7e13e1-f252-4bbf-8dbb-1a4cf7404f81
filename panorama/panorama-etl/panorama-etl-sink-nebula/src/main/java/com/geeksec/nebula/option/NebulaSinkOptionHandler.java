package com.geeksec.nebula.option;

import cn.hutool.core.util.StrUtil;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.nebula.connection.NebulaClientOptions;
import org.apache.flink.connector.nebula.connection.NebulaGraphConnectionProvider;
import org.apache.flink.connector.nebula.connection.NebulaMetaConnectionProvider;
import org.apache.flink.connector.nebula.sink.NebulaEdgeBatchOutputFormat;
import org.apache.flink.connector.nebula.sink.NebulaVertexBatchOutputFormat;
import org.apache.flink.connector.nebula.statement.EdgeExecutionOptions;
import org.apache.flink.connector.nebula.statement.VertexExecutionOptions;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @Description：Nebula Sink入库配置入口
 */
public class NebulaSinkOptionHandler {

    public static String NEBULA_GRAPH_ADDR;
    public static String NEBULA_META_ADDR;
    private static String NEBULA_GRAPH_SPACE_NAME;
    private static Integer NEBULA_BATCH_SINK_TAG_NUM;
    private static Integer NEBULA_BATCH_SINK_EDGE_NUM;

    public static NebulaGraphConnectionProvider graphConnectionProvider = null;
    public static NebulaMetaConnectionProvider metaConnectionProvider = null;

    /**
     * 通过外置任务的参数进行修改
     *
     * @param config
     */
    public static void init(ParameterTool config) {
        NEBULA_GRAPH_ADDR = config.get("nebula.graph.addr");
        NEBULA_META_ADDR = config.get("nebula.meta.addr");
        NEBULA_GRAPH_SPACE_NAME = config.get("nebula.space.name");
        NEBULA_BATCH_SINK_TAG_NUM = config.getInt("nebula.vertex.batch.sink.num");
        NEBULA_BATCH_SINK_EDGE_NUM = config.getInt("nebula.edge.batch.sink.num");

        // Nebula Conn通用配置
        NebulaClientOptions nebulaClientOptions = new NebulaClientOptions.NebulaClientOptionsBuilder()
                .setGraphAddress(NEBULA_GRAPH_ADDR)
                .setMetaAddress(NEBULA_META_ADDR)
                .build();
        graphConnectionProvider = new NebulaGraphConnectionProvider(nebulaClientOptions);
        metaConnectionProvider = new NebulaMetaConnectionProvider(nebulaClientOptions);
    }

    public static NebulaVertexBatchOutputFormat handleVertexFormat(String sinkTagType) {
        switch (sinkTagType) {
            case "TAG_IP":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getIpTagExecutionOptions());
            case "TAG_DOMAIN":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getDomainTagExecutionOptions());
            case "TAG_CERT":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getCertTagExecutionOptions());
            case "TAG_ATTACK":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getAttackTagExecutionOptions());
            case "TAG_URL":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getUrlTagExecutionOptions());
            case "TAG_ORG":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getOrgTagExecutionOptions());
            case "TAG_UA":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getUaTagExecutionOptions());
            case "TAG_APT_GROUP":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getAptGroupTagExecutionOptions());
            case "TAG_EMAIL":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getEmailTagExecutionOptions());
            case "TAG_MAIL":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getMailTagExecutionOptions());
            case "TAG_FILE":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getFileTagExecutionOptions());
            case "TAG_SSLFINGER":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getSslFingerTagExecutionOptions());
            case "TAG_APP":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getAppTagExecutionOptions());
            case "TAG_DEVICE":
                return new NebulaVertexBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getDeviceTagExecutionOptions());
            default:
                return null;
        }
    }


    public static NebulaEdgeBatchOutputFormat handleEdgeFormat(String sinkEdgeType) {
        switch (sinkEdgeType) {
            case "EDGE_make_attack":
            case "EDGE_app_belong_to_server":
            case "EDGE_client_use_app":

            case "EDGE_app_connect_cert":

            case "EDGE_attack_belong_to_apt_group":
            case "EDGE_file_connect_url":
            case "EDGE_file_connect_domain":
            case "EDGE_file_connect_ip":
            case "EDGE_receive_file":
            case "EDGE_sender_send_file":
            case "EDGE_receiver_receive_file":

            case "EDGE_send_file":
            case "EDGE_receive_mail":

            case "EDGE_send_mail":
            case "EDGE_sslfinger_connect_sni":
            case "EDGE_server_use_sslfinger":
            case "EDGE_client_use_sslfinger":
            case "EDGE_cert_connect_sslfinger":

            case "EDGE_cert_connect_sni":
            case "EDGE_cert_belong_to_org":
            case "EDGE_server_use_cert":

            case "EDGE_client_use_cert":
            case "EDGE_client_http_connect_url":
            case "EDGE_server_http_connect_domain":
            case "EDGE_client_http_connect_domain":
            case "EDGE_ua_connect_domain":
            case "EDGE_client_use_ua":

            case "EDGE_domain_belong_to_apt":
            case "EDGE_ip_belong_to_org":
            case "EDGE_ip_belong_to_apt":
            case "EDGE_attack_to":
            case "EDGE_attack_to_device":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getNonePropEdgeExecutionOptions(sinkEdgeType));

            case "EDGE_http_connect":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getHttpConnectExecutionOptions());
            case "EDGE_client_query_domain":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getClientQueryDomainExecutionOptions());
            case "EDGE_client_query_dns_server":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getClientQueryDnsServerExecutionOptions());
            case "EDGE_dns_server_resolves_domain":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getDnsServerResolvesDomainExecutionOptions());
            case "EDGE_parse_to":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getParseToExecutionOptions());
            case "EDGE_include_file":
                return new NebulaEdgeBatchOutputFormat(graphConnectionProvider, metaConnectionProvider, getIncludeFileExecutionOptions());
            default:
                return null;
        }
    }

    private static VertexExecutionOptions getIpTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("IP")
                .setIdIndex(1) // vid
                .setFields(Arrays.asList("ip_addr", "version", "city", "country", "stat", "longitude", "latitude", "ISP", "asn", "org"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getUrlTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("URL")
                .setIdIndex(1) // vid
                .setFields(Arrays.asList("url_path","uri"))
                .setPositions(Arrays.asList(2,3))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getDomainTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("DOMAIN")
                .setIdIndex(1)
                .setFields(Collections.singletonList("domain_addr"))
                .setPositions(Collections.singletonList(2))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getCertTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("CERT")
                .setIdIndex(1)
                .setFields(Arrays.asList("finger_print", "issuer", "subject", "algorithm_id", "fp_alg", "not_before", "not_after", "version"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getOrgTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("ORG")
                .setIdIndex(1)
                .setFields(Collections.singletonList("org_name"))
                .setPositions(Collections.singletonList(2))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getUaTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("UA")
                .setIdIndex(1)
                .setFields(Arrays.asList("ua_str","os_name","device_name"))
                .setPositions(Arrays.asList(2,3,4))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }


    private static VertexExecutionOptions getAptGroupTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("APT_GROUP")
                .setIdIndex(1)
                .setFields(Arrays.asList("apt_name", "apt_alias", "apt_desc", "apt_country"))
                .setPositions(Arrays.asList(1, 2, 3, 4))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getAttackTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("ATTACK")
                .setIdIndex(1)
                .setFields(Arrays.asList("attack_id", "attack_time", "aip_aport_app_vport_vip",
                        "threat_type", "kill_chain", "confidence", "vendor_id", "detect_type", "pcap_file", "pcap_name", "labels"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .setBatchIntervalMs(5000)
                .build();
    }

    private static VertexExecutionOptions getMailTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("MAIL")
                .setIdIndex(1)
                .setFields(Arrays.asList("subject", "industry", "intents"))
                .setPositions(Arrays.asList(2, 3, 4))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getEmailTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("EMAIL")
                .setIdIndex(1)
                .setFields(Arrays.asList("email_addr", "user_name"))
                .setPositions(Arrays.asList(2, 3))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getFileTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("FILE")
                .setIdIndex(1)
                .setFields(Arrays.asList("file_md5", "file_sha1", "file_sha256",
                        "file_sha512", "file_crc32", "file_size", "file_path"))
                .setPositions(Arrays.asList(1, 2, 3, 4, 5, 6, 7))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static VertexExecutionOptions getSslFingerTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("SSLFINGER")
                .setIdIndex(1)
                .setFields(Collections.singletonList("ja3_hash"))
                .setPositions(Collections.singletonList(1))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }


    private static VertexExecutionOptions getAppTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("APP")
                .setIdIndex(1)
                .setFields(Arrays.asList("app_name", "app_type_id", "app_type",
                        "app_class_id", "app_class", "is_encrypted"))
                .setPositions(Arrays.asList(2, 3, 4, 5, 6, 7))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }


    private static VertexExecutionOptions getDeviceTagExecutionOptions() {
        return new VertexExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setTag("DEVICE")
                .setIdIndex(1)
                .setFields(Arrays.asList("device_md5", "vendor", "device_type",
                        "model"))
                .setPositions(Arrays.asList(1, 2, 3, 4))
                .setBatchSize(NEBULA_BATCH_SINK_TAG_NUM)
                .build();
    }

    private static EdgeExecutionOptions getClientQueryDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setEdge("client_query_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("dns_type", "answer_type"))
                .setPositions(Arrays.asList(4, 5))
                .setBatchSize(NEBULA_BATCH_SINK_EDGE_NUM)
                .build();
    }

    private static EdgeExecutionOptions getClientQueryDnsServerExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setEdge("client_query_dns_server")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("dns_type", "answer_type"))
                .setPositions(Arrays.asList(4, 5))
                .setBatchSize(NEBULA_BATCH_SINK_EDGE_NUM)
                .build();
    }

    private static EdgeExecutionOptions getDnsServerResolvesDomainExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setEdge("dns_server_resolves_domain")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("dns_type", "answer_type"))
                .setPositions(Arrays.asList(4, 5))
                .setBatchSize(NEBULA_BATCH_SINK_EDGE_NUM)
                .build();
    }

    private static EdgeExecutionOptions getParseToExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setEdge("parse_to")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("final_parse","dns_server"))
                .setPositions(Arrays.asList(4,5))
                .setBatchSize(NEBULA_BATCH_SINK_EDGE_NUM)
                .build();
    }

    private static EdgeExecutionOptions getNonePropEdgeExecutionOptions(String sinkType){
        // 获取sinkType "EDGE_"后的内容"
        String edgeType = StrUtil.EMPTY;
        String[] sinkTypeArr = sinkType.split("_");
        if (sinkTypeArr.length > 1) {
            edgeType = String.join("_", Arrays.copyOfRange(sinkTypeArr, 1, sinkTypeArr.length));
        }
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setEdge(edgeType)
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.emptyList())
                .setPositions(Collections.emptyList())
                .setBatchSize(NEBULA_BATCH_SINK_EDGE_NUM)
                .build();
    }

    private static EdgeExecutionOptions getHttpConnectExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setEdge("http_connect")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Arrays.asList("uri", "host", "cookie", "agent", "referer", "xff", "req_data",
                        "response_data", "method", "status", "content_type"))
                .setPositions(Arrays.asList(4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14))
                .setBatchSize(NEBULA_BATCH_SINK_EDGE_NUM)
                .build();
    }

    private static EdgeExecutionOptions getIncludeFileExecutionOptions() {
        return new EdgeExecutionOptions.ExecutionOptionBuilder()
                .setGraphSpace(NEBULA_GRAPH_SPACE_NAME)
                .setEdge("include_file")
                .setSrcIndex(1)
                .setDstIndex(2)
                .setRankIndex(3)
                .setFields(Collections.singletonList("file_name"))
                .setPositions(Collections.singletonList(4))
                .setBatchSize(NEBULA_BATCH_SINK_EDGE_NUM)
                .build();
    }
}
