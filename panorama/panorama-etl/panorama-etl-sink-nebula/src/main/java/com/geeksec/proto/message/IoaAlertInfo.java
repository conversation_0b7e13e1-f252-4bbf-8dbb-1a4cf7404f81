package com.geeksec.proto.message;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: IOA_ALERT_INFO.proto
// Protobuf Java Version: 4.29.4

public final class IoaAlertInfo {
  private IoaAlertInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      IoaAlertInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IOA_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IOA_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     * @return Whether the ioaStreamId field is set.
     */
    boolean hasIoaStreamId();
    /**
     * <pre>
     * 流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     * @return The ioaStreamId.
     */
    long getIoaStreamId();

    /**
     * <pre>
     * 任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     * @return Whether the ioaTask field is set.
     */
    boolean hasIoaTask();
    /**
     * <pre>
     * 任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     * @return The ioaTask.
     */
    long getIoaTask();

    /**
     * <pre>
     * 规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     * @return Whether the ioaRule field is set.
     */
    boolean hasIoaRule();
    /**
     * <pre>
     * 规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     * @return The ioaRule.
     */
    long getIoaRule();

    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     * @return Whether the ioaName field is set.
     */
    boolean hasIoaName();
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     * @return The ioaName.
     */
    java.lang.String getIoaName();
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     * @return The bytes for ioaName.
     */
    com.google.protobuf.ByteString
        getIoaNameBytes();

    /**
     * <pre>
     * 规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     * @return Whether the ioaValue field is set.
     */
    boolean hasIoaValue();
    /**
     * <pre>
     * 规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     * @return The ioaValue.
     */
    java.lang.String getIoaValue();
    /**
     * <pre>
     * 规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     * @return The bytes for ioaValue.
     */
    com.google.protobuf.ByteString
        getIoaValueBytes();

    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     * @return Whether the ioaRefer field is set.
     */
    boolean hasIoaRefer();
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     * @return The ioaRefer.
     */
    java.lang.String getIoaRefer();
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     * @return The bytes for ioaRefer.
     */
    com.google.protobuf.ByteString
        getIoaReferBytes();

    /**
     * <pre>
     * 规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     * @return Whether the ioaVersion field is set.
     */
    boolean hasIoaVersion();
    /**
     * <pre>
     * 规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     * @return The ioaVersion.
     */
    java.lang.String getIoaVersion();
    /**
     * <pre>
     * 规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     * @return The bytes for ioaVersion.
     */
    com.google.protobuf.ByteString
        getIoaVersionBytes();

    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     * @return Whether the ioaVul field is set.
     */
    boolean hasIoaVul();
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     * @return The ioaVul.
     */
    java.lang.String getIoaVul();
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     * @return The bytes for ioaVul.
     */
    com.google.protobuf.ByteString
        getIoaVulBytes();

    /**
     * <pre>
     * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     * @return Whether the ioaDirection field is set.
     */
    boolean hasIoaDirection();
    /**
     * <pre>
     * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     * @return The ioaDirection.
     */
    java.lang.String getIoaDirection();
    /**
     * <pre>
     * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     * @return The bytes for ioaDirection.
     */
    com.google.protobuf.ByteString
        getIoaDirectionBytes();

    /**
     * <pre>
     * 攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     * @return Whether the ioaAttackResult field is set.
     */
    boolean hasIoaAttackResult();
    /**
     * <pre>
     * 攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     * @return The ioaAttackResult.
     */
    java.lang.String getIoaAttackResult();
    /**
     * <pre>
     * 攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     * @return The bytes for ioaAttackResult.
     */
    com.google.protobuf.ByteString
        getIoaAttackResultBytes();

    /**
     * <pre>
     * 代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     * @return Whether the ioaCodeLanguage field is set.
     */
    boolean hasIoaCodeLanguage();
    /**
     * <pre>
     * 代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     * @return The ioaCodeLanguage.
     */
    java.lang.String getIoaCodeLanguage();
    /**
     * <pre>
     * 代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     * @return The bytes for ioaCodeLanguage.
     */
    com.google.protobuf.ByteString
        getIoaCodeLanguageBytes();

    /**
     * <pre>
     * 影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     * @return Whether the ioaAffectedProduct field is set.
     */
    boolean hasIoaAffectedProduct();
    /**
     * <pre>
     * 影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     * @return The ioaAffectedProduct.
     */
    java.lang.String getIoaAffectedProduct();
    /**
     * <pre>
     * 影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     * @return The bytes for ioaAffectedProduct.
     */
    com.google.protobuf.ByteString
        getIoaAffectedProductBytes();

    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     * @return Whether the ioaMaliciousFamily field is set.
     */
    boolean hasIoaMaliciousFamily();
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     * @return The ioaMaliciousFamily.
     */
    java.lang.String getIoaMaliciousFamily();
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     * @return The bytes for ioaMaliciousFamily.
     */
    com.google.protobuf.ByteString
        getIoaMaliciousFamilyBytes();

    /**
     * <pre>
     * APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     * @return Whether the ioaAptCampaign field is set.
     */
    boolean hasIoaAptCampaign();
    /**
     * <pre>
     * APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     * @return The ioaAptCampaign.
     */
    java.lang.String getIoaAptCampaign();
    /**
     * <pre>
     * APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     * @return The bytes for ioaAptCampaign.
     */
    com.google.protobuf.ByteString
        getIoaAptCampaignBytes();

    /**
     * <pre>
     * 威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     * @return Whether the ioaDetailInfo field is set.
     */
    boolean hasIoaDetailInfo();
    /**
     * <pre>
     * 威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     * @return The ioaDetailInfo.
     */
    java.lang.String getIoaDetailInfo();
    /**
     * <pre>
     * 威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     * @return The bytes for ioaDetailInfo.
     */
    com.google.protobuf.ByteString
        getIoaDetailInfoBytes();
  }
  /**
   * <pre>
   * 攻击利用告警信息
   * </pre>
   *
   * Protobuf type {@code IOA_ALERT_INFO}
   */
  public static final class IOA_ALERT_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:IOA_ALERT_INFO)
      IOA_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        IOA_ALERT_INFO.class.getName());
    }
    // Use IOA_ALERT_INFO.newBuilder() to construct.
    private IOA_ALERT_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private IOA_ALERT_INFO() {
      ioaName_ = "";
      ioaValue_ = "";
      ioaRefer_ = "";
      ioaVersion_ = "";
      ioaVul_ = "";
      ioaDirection_ = "";
      ioaAttackResult_ = "";
      ioaCodeLanguage_ = "";
      ioaAffectedProduct_ = "";
      ioaMaliciousFamily_ = "";
      ioaAptCampaign_ = "";
      ioaDetailInfo_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IoaAlertInfo.internal_static_IOA_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IoaAlertInfo.internal_static_IOA_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IoaAlertInfo.IOA_ALERT_INFO.class, IoaAlertInfo.IOA_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int IOA_STREAM_ID_FIELD_NUMBER = 1;
    private long ioaStreamId_ = 0L;
    /**
     * <pre>
     * 流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     * @return Whether the ioaStreamId field is set.
     */
    @java.lang.Override
    public boolean hasIoaStreamId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 流ID	
     * </pre>
     *
     * <code>required uint64 ioa_stream_id = 1;</code>
     * @return The ioaStreamId.
     */
    @java.lang.Override
    public long getIoaStreamId() {
      return ioaStreamId_;
    }

    public static final int IOA_TASK_FIELD_NUMBER = 2;
    private long ioaTask_ = 0L;
    /**
     * <pre>
     * 任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     * @return Whether the ioaTask field is set.
     */
    @java.lang.Override
    public boolean hasIoaTask() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 任务号	
     * </pre>
     *
     * <code>optional uint64 ioa_task = 2;</code>
     * @return The ioaTask.
     */
    @java.lang.Override
    public long getIoaTask() {
      return ioaTask_;
    }

    public static final int IOA_RULE_FIELD_NUMBER = 3;
    private long ioaRule_ = 0L;
    /**
     * <pre>
     * 规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     * @return Whether the ioaRule field is set.
     */
    @java.lang.Override
    public boolean hasIoaRule() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 规则号	
     * </pre>
     *
     * <code>optional uint64 ioa_rule = 3;</code>
     * @return The ioaRule.
     */
    @java.lang.Override
    public long getIoaRule() {
      return ioaRule_;
    }

    public static final int IOA_NAME_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaName_ = "";
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     * @return Whether the ioaName field is set.
     */
    @java.lang.Override
    public boolean hasIoaName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     * @return The ioaName.
     */
    @java.lang.Override
    public java.lang.String getIoaName() {
      java.lang.Object ref = ioaName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string ioa_name = 4;</code>
     * @return The bytes for ioaName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaNameBytes() {
      java.lang.Object ref = ioaName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_VALUE_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaValue_ = "";
    /**
     * <pre>
     * 规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     * @return Whether the ioaValue field is set.
     */
    @java.lang.Override
    public boolean hasIoaValue() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     * @return The ioaValue.
     */
    @java.lang.Override
    public java.lang.String getIoaValue() {
      java.lang.Object ref = ioaValue_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaValue_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 规则内容	SDX规则语法描述
     * </pre>
     *
     * <code>required string ioa_value = 5;</code>
     * @return The bytes for ioaValue.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaValueBytes() {
      java.lang.Object ref = ioaValue_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_REFER_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaRefer_ = "";
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     * @return Whether the ioaRefer field is set.
     */
    @java.lang.Override
    public boolean hasIoaRefer() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     * @return The ioaRefer.
     */
    @java.lang.Override
    public java.lang.String getIoaRefer() {
      java.lang.Object ref = ioaRefer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaRefer_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string ioa_refer = 6;</code>
     * @return The bytes for ioaRefer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaReferBytes() {
      java.lang.Object ref = ioaRefer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaRefer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_VERSION_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaVersion_ = "";
    /**
     * <pre>
     * 规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     * @return Whether the ioaVersion field is set.
     */
    @java.lang.Override
    public boolean hasIoaVersion() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     * @return The ioaVersion.
     */
    @java.lang.Override
    public java.lang.String getIoaVersion() {
      java.lang.Object ref = ioaVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaVersion_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 规则版本	
     * </pre>
     *
     * <code>required string ioa_version = 7;</code>
     * @return The bytes for ioaVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaVersionBytes() {
      java.lang.Object ref = ioaVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_VUL_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaVul_ = "";
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     * @return Whether the ioaVul field is set.
     */
    @java.lang.Override
    public boolean hasIoaVul() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     * @return The ioaVul.
     */
    @java.lang.Override
    public java.lang.String getIoaVul() {
      java.lang.Object ref = ioaVul_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaVul_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioa_vul = 8;</code>
     * @return The bytes for ioaVul.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaVulBytes() {
      java.lang.Object ref = ioaVul_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaVul_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_DIRECTION_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaDirection_ = "";
    /**
     * <pre>
     * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     * @return Whether the ioaDirection field is set.
     */
    @java.lang.Override
    public boolean hasIoaDirection() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     * @return The ioaDirection.
     */
    @java.lang.Override
    public java.lang.String getIoaDirection() {
      java.lang.Object ref = ioaDirection_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaDirection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
     * </pre>
     *
     * <code>required string ioa_direction = 9;</code>
     * @return The bytes for ioaDirection.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaDirectionBytes() {
      java.lang.Object ref = ioaDirection_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaDirection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_ATTACK_RESULT_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaAttackResult_ = "";
    /**
     * <pre>
     * 攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     * @return Whether the ioaAttackResult field is set.
     */
    @java.lang.Override
    public boolean hasIoaAttackResult() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     * @return The ioaAttackResult.
     */
    @java.lang.Override
    public java.lang.String getIoaAttackResult() {
      java.lang.Object ref = ioaAttackResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaAttackResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 攻击结果	企图/成功/失败/失陷
     * </pre>
     *
     * <code>required string ioa_attack_result = 10;</code>
     * @return The bytes for ioaAttackResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaAttackResultBytes() {
      java.lang.Object ref = ioaAttackResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaAttackResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_CODE_LANGUAGE_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaCodeLanguage_ = "";
    /**
     * <pre>
     * 代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     * @return Whether the ioaCodeLanguage field is set.
     */
    @java.lang.Override
    public boolean hasIoaCodeLanguage() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     * @return The ioaCodeLanguage.
     */
    @java.lang.Override
    public java.lang.String getIoaCodeLanguage() {
      java.lang.Object ref = ioaCodeLanguage_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaCodeLanguage_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 代码语言	
     * </pre>
     *
     * <code>optional string ioa_code_language = 11;</code>
     * @return The bytes for ioaCodeLanguage.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaCodeLanguageBytes() {
      java.lang.Object ref = ioaCodeLanguage_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaCodeLanguage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_AFFECTED_PRODUCT_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaAffectedProduct_ = "";
    /**
     * <pre>
     * 影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     * @return Whether the ioaAffectedProduct field is set.
     */
    @java.lang.Override
    public boolean hasIoaAffectedProduct() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     * @return The ioaAffectedProduct.
     */
    @java.lang.Override
    public java.lang.String getIoaAffectedProduct() {
      java.lang.Object ref = ioaAffectedProduct_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaAffectedProduct_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 影响平台	受影响APP/系统/应用
     * </pre>
     *
     * <code>optional string ioa_affected_product = 12;</code>
     * @return The bytes for ioaAffectedProduct.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaAffectedProductBytes() {
      java.lang.Object ref = ioaAffectedProduct_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaAffectedProduct_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_MALICIOUS_FAMILY_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaMaliciousFamily_ = "";
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     * @return Whether the ioaMaliciousFamily field is set.
     */
    @java.lang.Override
    public boolean hasIoaMaliciousFamily() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     * @return The ioaMaliciousFamily.
     */
    @java.lang.Override
    public java.lang.String getIoaMaliciousFamily() {
      java.lang.Object ref = ioaMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaMaliciousFamily_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioa_malicious_family = 13;</code>
     * @return The bytes for ioaMaliciousFamily.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaMaliciousFamilyBytes() {
      java.lang.Object ref = ioaMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaMaliciousFamily_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_APT_CAMPAIGN_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaAptCampaign_ = "";
    /**
     * <pre>
     * APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     * @return Whether the ioaAptCampaign field is set.
     */
    @java.lang.Override
    public boolean hasIoaAptCampaign() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     * @return The ioaAptCampaign.
     */
    @java.lang.Override
    public java.lang.String getIoaAptCampaign() {
      java.lang.Object ref = ioaAptCampaign_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaAptCampaign_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * APT组织名称	
     * </pre>
     *
     * <code>optional string ioa_apt_campaign = 14;</code>
     * @return The bytes for ioaAptCampaign.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaAptCampaignBytes() {
      java.lang.Object ref = ioaAptCampaign_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaAptCampaign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOA_DETAIL_INFO_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile java.lang.Object ioaDetailInfo_ = "";
    /**
     * <pre>
     * 威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     * @return Whether the ioaDetailInfo field is set.
     */
    @java.lang.Override
    public boolean hasIoaDetailInfo() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     * @return The ioaDetailInfo.
     */
    @java.lang.Override
    public java.lang.String getIoaDetailInfo() {
      java.lang.Object ref = ioaDetailInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ioaDetailInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁详情描述
     * </pre>
     *
     * <code>required string ioa_detail_info = 15;</code>
     * @return The bytes for ioaDetailInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIoaDetailInfoBytes() {
      java.lang.Object ref = ioaDetailInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ioaDetailInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIoaStreamId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaRefer()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaVersion()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaDirection()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaAttackResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIoaDetailInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt64(1, ioaStreamId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt64(2, ioaTask_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt64(3, ioaRule_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, ioaName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, ioaValue_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, ioaRefer_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 7, ioaVersion_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, ioaVul_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, ioaDirection_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, ioaAttackResult_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, ioaCodeLanguage_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, ioaAffectedProduct_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 13, ioaMaliciousFamily_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 14, ioaAptCampaign_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 15, ioaDetailInfo_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, ioaStreamId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, ioaTask_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, ioaRule_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, ioaName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, ioaValue_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, ioaRefer_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(7, ioaVersion_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, ioaVul_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(9, ioaDirection_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, ioaAttackResult_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, ioaCodeLanguage_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, ioaAffectedProduct_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(13, ioaMaliciousFamily_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(14, ioaAptCampaign_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(15, ioaDetailInfo_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IoaAlertInfo.IOA_ALERT_INFO)) {
        return super.equals(obj);
      }
      IoaAlertInfo.IOA_ALERT_INFO other = (IoaAlertInfo.IOA_ALERT_INFO) obj;

      if (hasIoaStreamId() != other.hasIoaStreamId()) return false;
      if (hasIoaStreamId()) {
        if (getIoaStreamId()
            != other.getIoaStreamId()) return false;
      }
      if (hasIoaTask() != other.hasIoaTask()) return false;
      if (hasIoaTask()) {
        if (getIoaTask()
            != other.getIoaTask()) return false;
      }
      if (hasIoaRule() != other.hasIoaRule()) return false;
      if (hasIoaRule()) {
        if (getIoaRule()
            != other.getIoaRule()) return false;
      }
      if (hasIoaName() != other.hasIoaName()) return false;
      if (hasIoaName()) {
        if (!getIoaName()
            .equals(other.getIoaName())) return false;
      }
      if (hasIoaValue() != other.hasIoaValue()) return false;
      if (hasIoaValue()) {
        if (!getIoaValue()
            .equals(other.getIoaValue())) return false;
      }
      if (hasIoaRefer() != other.hasIoaRefer()) return false;
      if (hasIoaRefer()) {
        if (!getIoaRefer()
            .equals(other.getIoaRefer())) return false;
      }
      if (hasIoaVersion() != other.hasIoaVersion()) return false;
      if (hasIoaVersion()) {
        if (!getIoaVersion()
            .equals(other.getIoaVersion())) return false;
      }
      if (hasIoaVul() != other.hasIoaVul()) return false;
      if (hasIoaVul()) {
        if (!getIoaVul()
            .equals(other.getIoaVul())) return false;
      }
      if (hasIoaDirection() != other.hasIoaDirection()) return false;
      if (hasIoaDirection()) {
        if (!getIoaDirection()
            .equals(other.getIoaDirection())) return false;
      }
      if (hasIoaAttackResult() != other.hasIoaAttackResult()) return false;
      if (hasIoaAttackResult()) {
        if (!getIoaAttackResult()
            .equals(other.getIoaAttackResult())) return false;
      }
      if (hasIoaCodeLanguage() != other.hasIoaCodeLanguage()) return false;
      if (hasIoaCodeLanguage()) {
        if (!getIoaCodeLanguage()
            .equals(other.getIoaCodeLanguage())) return false;
      }
      if (hasIoaAffectedProduct() != other.hasIoaAffectedProduct()) return false;
      if (hasIoaAffectedProduct()) {
        if (!getIoaAffectedProduct()
            .equals(other.getIoaAffectedProduct())) return false;
      }
      if (hasIoaMaliciousFamily() != other.hasIoaMaliciousFamily()) return false;
      if (hasIoaMaliciousFamily()) {
        if (!getIoaMaliciousFamily()
            .equals(other.getIoaMaliciousFamily())) return false;
      }
      if (hasIoaAptCampaign() != other.hasIoaAptCampaign()) return false;
      if (hasIoaAptCampaign()) {
        if (!getIoaAptCampaign()
            .equals(other.getIoaAptCampaign())) return false;
      }
      if (hasIoaDetailInfo() != other.hasIoaDetailInfo()) return false;
      if (hasIoaDetailInfo()) {
        if (!getIoaDetailInfo()
            .equals(other.getIoaDetailInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIoaStreamId()) {
        hash = (37 * hash) + IOA_STREAM_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIoaStreamId());
      }
      if (hasIoaTask()) {
        hash = (37 * hash) + IOA_TASK_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIoaTask());
      }
      if (hasIoaRule()) {
        hash = (37 * hash) + IOA_RULE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIoaRule());
      }
      if (hasIoaName()) {
        hash = (37 * hash) + IOA_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getIoaName().hashCode();
      }
      if (hasIoaValue()) {
        hash = (37 * hash) + IOA_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getIoaValue().hashCode();
      }
      if (hasIoaRefer()) {
        hash = (37 * hash) + IOA_REFER_FIELD_NUMBER;
        hash = (53 * hash) + getIoaRefer().hashCode();
      }
      if (hasIoaVersion()) {
        hash = (37 * hash) + IOA_VERSION_FIELD_NUMBER;
        hash = (53 * hash) + getIoaVersion().hashCode();
      }
      if (hasIoaVul()) {
        hash = (37 * hash) + IOA_VUL_FIELD_NUMBER;
        hash = (53 * hash) + getIoaVul().hashCode();
      }
      if (hasIoaDirection()) {
        hash = (37 * hash) + IOA_DIRECTION_FIELD_NUMBER;
        hash = (53 * hash) + getIoaDirection().hashCode();
      }
      if (hasIoaAttackResult()) {
        hash = (37 * hash) + IOA_ATTACK_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAttackResult().hashCode();
      }
      if (hasIoaCodeLanguage()) {
        hash = (37 * hash) + IOA_CODE_LANGUAGE_FIELD_NUMBER;
        hash = (53 * hash) + getIoaCodeLanguage().hashCode();
      }
      if (hasIoaAffectedProduct()) {
        hash = (37 * hash) + IOA_AFFECTED_PRODUCT_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAffectedProduct().hashCode();
      }
      if (hasIoaMaliciousFamily()) {
        hash = (37 * hash) + IOA_MALICIOUS_FAMILY_FIELD_NUMBER;
        hash = (53 * hash) + getIoaMaliciousFamily().hashCode();
      }
      if (hasIoaAptCampaign()) {
        hash = (37 * hash) + IOA_APT_CAMPAIGN_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAptCampaign().hashCode();
      }
      if (hasIoaDetailInfo()) {
        hash = (37 * hash) + IOA_DETAIL_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIoaDetailInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static IoaAlertInfo.IOA_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static IoaAlertInfo.IOA_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IoaAlertInfo.IOA_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IoaAlertInfo.IOA_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 攻击利用告警信息
     * </pre>
     *
     * Protobuf type {@code IOA_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IOA_ALERT_INFO)
        IoaAlertInfo.IOA_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IoaAlertInfo.internal_static_IOA_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IoaAlertInfo.internal_static_IOA_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IoaAlertInfo.IOA_ALERT_INFO.class, IoaAlertInfo.IOA_ALERT_INFO.Builder.class);
      }

      // Construct using IoaAlertInfo.IOA_ALERT_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        ioaStreamId_ = 0L;
        ioaTask_ = 0L;
        ioaRule_ = 0L;
        ioaName_ = "";
        ioaValue_ = "";
        ioaRefer_ = "";
        ioaVersion_ = "";
        ioaVul_ = "";
        ioaDirection_ = "";
        ioaAttackResult_ = "";
        ioaCodeLanguage_ = "";
        ioaAffectedProduct_ = "";
        ioaMaliciousFamily_ = "";
        ioaAptCampaign_ = "";
        ioaDetailInfo_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IoaAlertInfo.internal_static_IOA_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public IoaAlertInfo.IOA_ALERT_INFO getDefaultInstanceForType() {
        return IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IoaAlertInfo.IOA_ALERT_INFO build() {
        IoaAlertInfo.IOA_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IoaAlertInfo.IOA_ALERT_INFO buildPartial() {
        IoaAlertInfo.IOA_ALERT_INFO result = new IoaAlertInfo.IOA_ALERT_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(IoaAlertInfo.IOA_ALERT_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ioaStreamId_ = ioaStreamId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ioaTask_ = ioaTask_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.ioaRule_ = ioaRule_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.ioaName_ = ioaName_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.ioaValue_ = ioaValue_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.ioaRefer_ = ioaRefer_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.ioaVersion_ = ioaVersion_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.ioaVul_ = ioaVul_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.ioaDirection_ = ioaDirection_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.ioaAttackResult_ = ioaAttackResult_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.ioaCodeLanguage_ = ioaCodeLanguage_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.ioaAffectedProduct_ = ioaAffectedProduct_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.ioaMaliciousFamily_ = ioaMaliciousFamily_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.ioaAptCampaign_ = ioaAptCampaign_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.ioaDetailInfo_ = ioaDetailInfo_;
          to_bitField0_ |= 0x00004000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IoaAlertInfo.IOA_ALERT_INFO) {
          return mergeFrom((IoaAlertInfo.IOA_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IoaAlertInfo.IOA_ALERT_INFO other) {
        if (other == IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasIoaStreamId()) {
          setIoaStreamId(other.getIoaStreamId());
        }
        if (other.hasIoaTask()) {
          setIoaTask(other.getIoaTask());
        }
        if (other.hasIoaRule()) {
          setIoaRule(other.getIoaRule());
        }
        if (other.hasIoaName()) {
          ioaName_ = other.ioaName_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (other.hasIoaValue()) {
          ioaValue_ = other.ioaValue_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.hasIoaRefer()) {
          ioaRefer_ = other.ioaRefer_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (other.hasIoaVersion()) {
          ioaVersion_ = other.ioaVersion_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (other.hasIoaVul()) {
          ioaVul_ = other.ioaVul_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (other.hasIoaDirection()) {
          ioaDirection_ = other.ioaDirection_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (other.hasIoaAttackResult()) {
          ioaAttackResult_ = other.ioaAttackResult_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (other.hasIoaCodeLanguage()) {
          ioaCodeLanguage_ = other.ioaCodeLanguage_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (other.hasIoaAffectedProduct()) {
          ioaAffectedProduct_ = other.ioaAffectedProduct_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (other.hasIoaMaliciousFamily()) {
          ioaMaliciousFamily_ = other.ioaMaliciousFamily_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (other.hasIoaAptCampaign()) {
          ioaAptCampaign_ = other.ioaAptCampaign_;
          bitField0_ |= 0x00002000;
          onChanged();
        }
        if (other.hasIoaDetailInfo()) {
          ioaDetailInfo_ = other.ioaDetailInfo_;
          bitField0_ |= 0x00004000;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIoaStreamId()) {
          return false;
        }
        if (!hasIoaName()) {
          return false;
        }
        if (!hasIoaValue()) {
          return false;
        }
        if (!hasIoaRefer()) {
          return false;
        }
        if (!hasIoaVersion()) {
          return false;
        }
        if (!hasIoaDirection()) {
          return false;
        }
        if (!hasIoaAttackResult()) {
          return false;
        }
        if (!hasIoaDetailInfo()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                ioaStreamId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                ioaTask_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                ioaRule_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                ioaName_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                ioaValue_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                ioaRefer_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                ioaVersion_ = input.readBytes();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                ioaVul_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                ioaDirection_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                ioaAttackResult_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                ioaCodeLanguage_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                ioaAffectedProduct_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                ioaMaliciousFamily_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                ioaAptCampaign_ = input.readBytes();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              case 122: {
                ioaDetailInfo_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long ioaStreamId_ ;
      /**
       * <pre>
       * 流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       * @return Whether the ioaStreamId field is set.
       */
      @java.lang.Override
      public boolean hasIoaStreamId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       * @return The ioaStreamId.
       */
      @java.lang.Override
      public long getIoaStreamId() {
        return ioaStreamId_;
      }
      /**
       * <pre>
       * 流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       * @param value The ioaStreamId to set.
       * @return This builder for chaining.
       */
      public Builder setIoaStreamId(long value) {

        ioaStreamId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 流ID	
       * </pre>
       *
       * <code>required uint64 ioa_stream_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaStreamId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ioaStreamId_ = 0L;
        onChanged();
        return this;
      }

      private long ioaTask_ ;
      /**
       * <pre>
       * 任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       * @return Whether the ioaTask field is set.
       */
      @java.lang.Override
      public boolean hasIoaTask() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       * @return The ioaTask.
       */
      @java.lang.Override
      public long getIoaTask() {
        return ioaTask_;
      }
      /**
       * <pre>
       * 任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       * @param value The ioaTask to set.
       * @return This builder for chaining.
       */
      public Builder setIoaTask(long value) {

        ioaTask_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务号	
       * </pre>
       *
       * <code>optional uint64 ioa_task = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaTask() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ioaTask_ = 0L;
        onChanged();
        return this;
      }

      private long ioaRule_ ;
      /**
       * <pre>
       * 规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       * @return Whether the ioaRule field is set.
       */
      @java.lang.Override
      public boolean hasIoaRule() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       * @return The ioaRule.
       */
      @java.lang.Override
      public long getIoaRule() {
        return ioaRule_;
      }
      /**
       * <pre>
       * 规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       * @param value The ioaRule to set.
       * @return This builder for chaining.
       */
      public Builder setIoaRule(long value) {

        ioaRule_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 规则号	
       * </pre>
       *
       * <code>optional uint64 ioa_rule = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaRule() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ioaRule_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object ioaName_ = "";
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       * @return Whether the ioaName field is set.
       */
      public boolean hasIoaName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       * @return The ioaName.
       */
      public java.lang.String getIoaName() {
        java.lang.Object ref = ioaName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       * @return The bytes for ioaName.
       */
      public com.google.protobuf.ByteString
          getIoaNameBytes() {
        java.lang.Object ref = ioaName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       * @param value The ioaName to set.
       * @return This builder for chaining.
       */
      public Builder setIoaName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaName_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaName() {
        ioaName_ = getDefaultInstance().getIoaName();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string ioa_name = 4;</code>
       * @param value The bytes for ioaName to set.
       * @return This builder for chaining.
       */
      public Builder setIoaNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaName_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object ioaValue_ = "";
      /**
       * <pre>
       * 规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       * @return Whether the ioaValue field is set.
       */
      public boolean hasIoaValue() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       * @return The ioaValue.
       */
      public java.lang.String getIoaValue() {
        java.lang.Object ref = ioaValue_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaValue_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       * @return The bytes for ioaValue.
       */
      public com.google.protobuf.ByteString
          getIoaValueBytes() {
        java.lang.Object ref = ioaValue_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaValue_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       * @param value The ioaValue to set.
       * @return This builder for chaining.
       */
      public Builder setIoaValue(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaValue_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaValue() {
        ioaValue_ = getDefaultInstance().getIoaValue();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 规则内容	SDX规则语法描述
       * </pre>
       *
       * <code>required string ioa_value = 5;</code>
       * @param value The bytes for ioaValue to set.
       * @return This builder for chaining.
       */
      public Builder setIoaValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaValue_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object ioaRefer_ = "";
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       * @return Whether the ioaRefer field is set.
       */
      public boolean hasIoaRefer() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       * @return The ioaRefer.
       */
      public java.lang.String getIoaRefer() {
        java.lang.Object ref = ioaRefer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaRefer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       * @return The bytes for ioaRefer.
       */
      public com.google.protobuf.ByteString
          getIoaReferBytes() {
        java.lang.Object ref = ioaRefer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaRefer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       * @param value The ioaRefer to set.
       * @return This builder for chaining.
       */
      public Builder setIoaRefer(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaRefer_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaRefer() {
        ioaRefer_ = getDefaultInstance().getIoaRefer();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string ioa_refer = 6;</code>
       * @param value The bytes for ioaRefer to set.
       * @return This builder for chaining.
       */
      public Builder setIoaReferBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaRefer_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private java.lang.Object ioaVersion_ = "";
      /**
       * <pre>
       * 规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       * @return Whether the ioaVersion field is set.
       */
      public boolean hasIoaVersion() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       * @return The ioaVersion.
       */
      public java.lang.String getIoaVersion() {
        java.lang.Object ref = ioaVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaVersion_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       * @return The bytes for ioaVersion.
       */
      public com.google.protobuf.ByteString
          getIoaVersionBytes() {
        java.lang.Object ref = ioaVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       * @param value The ioaVersion to set.
       * @return This builder for chaining.
       */
      public Builder setIoaVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaVersion_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaVersion() {
        ioaVersion_ = getDefaultInstance().getIoaVersion();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 规则版本	
       * </pre>
       *
       * <code>required string ioa_version = 7;</code>
       * @param value The bytes for ioaVersion to set.
       * @return This builder for chaining.
       */
      public Builder setIoaVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaVersion_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.lang.Object ioaVul_ = "";
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       * @return Whether the ioaVul field is set.
       */
      public boolean hasIoaVul() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       * @return The ioaVul.
       */
      public java.lang.String getIoaVul() {
        java.lang.Object ref = ioaVul_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaVul_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       * @return The bytes for ioaVul.
       */
      public com.google.protobuf.ByteString
          getIoaVulBytes() {
        java.lang.Object ref = ioaVul_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaVul_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       * @param value The ioaVul to set.
       * @return This builder for chaining.
       */
      public Builder setIoaVul(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaVul_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaVul() {
        ioaVul_ = getDefaultInstance().getIoaVul();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioa_vul = 8;</code>
       * @param value The bytes for ioaVul to set.
       * @return This builder for chaining.
       */
      public Builder setIoaVulBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaVul_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private java.lang.Object ioaDirection_ = "";
      /**
       * <pre>
       * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       * @return Whether the ioaDirection field is set.
       */
      public boolean hasIoaDirection() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       * @return The ioaDirection.
       */
      public java.lang.String getIoaDirection() {
        java.lang.Object ref = ioaDirection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaDirection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       * @return The bytes for ioaDirection.
       */
      public com.google.protobuf.ByteString
          getIoaDirectionBytes() {
        java.lang.Object ref = ioaDirection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaDirection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       * @param value The ioaDirection to set.
       * @return This builder for chaining.
       */
      public Builder setIoaDirection(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaDirection_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaDirection() {
        ioaDirection_ = getDefaultInstance().getIoaDirection();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击方向	cts/stc/to_client/from_server/from_client/to_server
       * </pre>
       *
       * <code>required string ioa_direction = 9;</code>
       * @param value The bytes for ioaDirection to set.
       * @return This builder for chaining.
       */
      public Builder setIoaDirectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaDirection_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object ioaAttackResult_ = "";
      /**
       * <pre>
       * 攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       * @return Whether the ioaAttackResult field is set.
       */
      public boolean hasIoaAttackResult() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       * @return The ioaAttackResult.
       */
      public java.lang.String getIoaAttackResult() {
        java.lang.Object ref = ioaAttackResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaAttackResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       * @return The bytes for ioaAttackResult.
       */
      public com.google.protobuf.ByteString
          getIoaAttackResultBytes() {
        java.lang.Object ref = ioaAttackResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaAttackResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       * @param value The ioaAttackResult to set.
       * @return This builder for chaining.
       */
      public Builder setIoaAttackResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaAttackResult_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaAttackResult() {
        ioaAttackResult_ = getDefaultInstance().getIoaAttackResult();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击结果	企图/成功/失败/失陷
       * </pre>
       *
       * <code>required string ioa_attack_result = 10;</code>
       * @param value The bytes for ioaAttackResult to set.
       * @return This builder for chaining.
       */
      public Builder setIoaAttackResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaAttackResult_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object ioaCodeLanguage_ = "";
      /**
       * <pre>
       * 代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       * @return Whether the ioaCodeLanguage field is set.
       */
      public boolean hasIoaCodeLanguage() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       * @return The ioaCodeLanguage.
       */
      public java.lang.String getIoaCodeLanguage() {
        java.lang.Object ref = ioaCodeLanguage_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaCodeLanguage_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       * @return The bytes for ioaCodeLanguage.
       */
      public com.google.protobuf.ByteString
          getIoaCodeLanguageBytes() {
        java.lang.Object ref = ioaCodeLanguage_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaCodeLanguage_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       * @param value The ioaCodeLanguage to set.
       * @return This builder for chaining.
       */
      public Builder setIoaCodeLanguage(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaCodeLanguage_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaCodeLanguage() {
        ioaCodeLanguage_ = getDefaultInstance().getIoaCodeLanguage();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 代码语言	
       * </pre>
       *
       * <code>optional string ioa_code_language = 11;</code>
       * @param value The bytes for ioaCodeLanguage to set.
       * @return This builder for chaining.
       */
      public Builder setIoaCodeLanguageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaCodeLanguage_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object ioaAffectedProduct_ = "";
      /**
       * <pre>
       * 影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       * @return Whether the ioaAffectedProduct field is set.
       */
      public boolean hasIoaAffectedProduct() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       * @return The ioaAffectedProduct.
       */
      public java.lang.String getIoaAffectedProduct() {
        java.lang.Object ref = ioaAffectedProduct_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaAffectedProduct_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       * @return The bytes for ioaAffectedProduct.
       */
      public com.google.protobuf.ByteString
          getIoaAffectedProductBytes() {
        java.lang.Object ref = ioaAffectedProduct_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaAffectedProduct_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       * @param value The ioaAffectedProduct to set.
       * @return This builder for chaining.
       */
      public Builder setIoaAffectedProduct(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaAffectedProduct_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaAffectedProduct() {
        ioaAffectedProduct_ = getDefaultInstance().getIoaAffectedProduct();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 影响平台	受影响APP/系统/应用
       * </pre>
       *
       * <code>optional string ioa_affected_product = 12;</code>
       * @param value The bytes for ioaAffectedProduct to set.
       * @return This builder for chaining.
       */
      public Builder setIoaAffectedProductBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaAffectedProduct_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object ioaMaliciousFamily_ = "";
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       * @return Whether the ioaMaliciousFamily field is set.
       */
      public boolean hasIoaMaliciousFamily() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       * @return The ioaMaliciousFamily.
       */
      public java.lang.String getIoaMaliciousFamily() {
        java.lang.Object ref = ioaMaliciousFamily_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaMaliciousFamily_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       * @return The bytes for ioaMaliciousFamily.
       */
      public com.google.protobuf.ByteString
          getIoaMaliciousFamilyBytes() {
        java.lang.Object ref = ioaMaliciousFamily_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaMaliciousFamily_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       * @param value The ioaMaliciousFamily to set.
       * @return This builder for chaining.
       */
      public Builder setIoaMaliciousFamily(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaMaliciousFamily_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaMaliciousFamily() {
        ioaMaliciousFamily_ = getDefaultInstance().getIoaMaliciousFamily();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioa_malicious_family = 13;</code>
       * @param value The bytes for ioaMaliciousFamily to set.
       * @return This builder for chaining.
       */
      public Builder setIoaMaliciousFamilyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaMaliciousFamily_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private java.lang.Object ioaAptCampaign_ = "";
      /**
       * <pre>
       * APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       * @return Whether the ioaAptCampaign field is set.
       */
      public boolean hasIoaAptCampaign() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       * @return The ioaAptCampaign.
       */
      public java.lang.String getIoaAptCampaign() {
        java.lang.Object ref = ioaAptCampaign_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaAptCampaign_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       * @return The bytes for ioaAptCampaign.
       */
      public com.google.protobuf.ByteString
          getIoaAptCampaignBytes() {
        java.lang.Object ref = ioaAptCampaign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaAptCampaign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       * @param value The ioaAptCampaign to set.
       * @return This builder for chaining.
       */
      public Builder setIoaAptCampaign(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaAptCampaign_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaAptCampaign() {
        ioaAptCampaign_ = getDefaultInstance().getIoaAptCampaign();
        bitField0_ = (bitField0_ & ~0x00002000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT组织名称	
       * </pre>
       *
       * <code>optional string ioa_apt_campaign = 14;</code>
       * @param value The bytes for ioaAptCampaign to set.
       * @return This builder for chaining.
       */
      public Builder setIoaAptCampaignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaAptCampaign_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }

      private java.lang.Object ioaDetailInfo_ = "";
      /**
       * <pre>
       * 威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       * @return Whether the ioaDetailInfo field is set.
       */
      public boolean hasIoaDetailInfo() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       * @return The ioaDetailInfo.
       */
      public java.lang.String getIoaDetailInfo() {
        java.lang.Object ref = ioaDetailInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ioaDetailInfo_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       * @return The bytes for ioaDetailInfo.
       */
      public com.google.protobuf.ByteString
          getIoaDetailInfoBytes() {
        java.lang.Object ref = ioaDetailInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ioaDetailInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       * @param value The ioaDetailInfo to set.
       * @return This builder for chaining.
       */
      public Builder setIoaDetailInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ioaDetailInfo_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearIoaDetailInfo() {
        ioaDetailInfo_ = getDefaultInstance().getIoaDetailInfo();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详情描述
       * </pre>
       *
       * <code>required string ioa_detail_info = 15;</code>
       * @param value The bytes for ioaDetailInfo to set.
       * @return This builder for chaining.
       */
      public Builder setIoaDetailInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ioaDetailInfo_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:IOA_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:IOA_ALERT_INFO)
    private static final IoaAlertInfo.IOA_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IoaAlertInfo.IOA_ALERT_INFO();
    }

    public static IoaAlertInfo.IOA_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IOA_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IOA_ALERT_INFO>() {
      @java.lang.Override
      public IOA_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IOA_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IOA_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IoaAlertInfo.IOA_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IOA_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_IOA_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024IOA_ALERT_INFO.proto\"\345\002\n\016IOA_ALERT_INF" +
      "O\022\025\n\rioa_stream_id\030\001 \002(\004\022\020\n\010ioa_task\030\002 \001" +
      "(\004\022\020\n\010ioa_rule\030\003 \001(\004\022\020\n\010ioa_name\030\004 \002(\t\022\021" +
      "\n\tioa_value\030\005 \002(\t\022\021\n\tioa_refer\030\006 \002(\t\022\023\n\013" +
      "ioa_version\030\007 \002(\t\022\017\n\007ioa_vul\030\010 \001(\t\022\025\n\rio" +
      "a_direction\030\t \002(\t\022\031\n\021ioa_attack_result\030\n" +
      " \002(\t\022\031\n\021ioa_code_language\030\013 \001(\t\022\034\n\024ioa_a" +
      "ffected_product\030\014 \001(\t\022\034\n\024ioa_malicious_f" +
      "amily\030\r \001(\t\022\030\n\020ioa_apt_campaign\030\016 \001(\t\022\027\n" +
      "\017ioa_detail_info\030\017 \002(\tB\016B\014IoaAlertInfo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_IOA_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IOA_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_IOA_ALERT_INFO_descriptor,
        new java.lang.String[] { "IoaStreamId", "IoaTask", "IoaRule", "IoaName", "IoaValue", "IoaRefer", "IoaVersion", "IoaVul", "IoaDirection", "IoaAttackResult", "IoaCodeLanguage", "IoaAffectedProduct", "IoaMaliciousFamily", "IoaAptCampaign", "IoaDetailInfo", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
