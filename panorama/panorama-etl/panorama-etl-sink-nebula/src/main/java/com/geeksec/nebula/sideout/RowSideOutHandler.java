package com.geeksec.nebula.sideout;

import com.geeksec.common.constant.PanoramaOutPutTagConstant;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.types.Row;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 * @Description：ROW 分流选择器
 */
public class RowSideOutHandler {
    public static SingleOutputStreamOperator<Row> handleRowSideOutPutTag(DataStream<Row> rowDataStream) {
        return rowDataStream.process(new ProcessFunction<Row, Row>() {
            @Override
            public void processElement(Row row, Context ctx, Collector<Row> collector) throws Exception {
                String type = row.getField(0).toString(); // 获取sink Row 类型
                switch (type) {
                    case "TAG_IP":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_IP, row);
                        break;
                    case "TAG_DOMAIN":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_DOMAIN, row);
                        break;
                    case "TAG_CERT":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_CERT, row);
                        break;
                    case "TAG_ATTACK":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_ATTACK, row);
                        break;
                    case "TAG_URL":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_URL, row);
                        break;
                    case "TAG_ORG":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_ORG, row);
                        break;
                    case "TAG_UA":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_UA, row);
                        break;
                    case "TAG_APT_GROUP":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_APT_GROUP, row);
                        break;
                    case "TAG_EMAIL":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_EMAIL, row);
                        break;
                    case "TAG_MAIL":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_MAIL, row);
                        break;
                    case "TAG_FILE":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_FILE, row);
                        break;
                    case "TAG_SSLFINGER":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_SSLFINGER, row);
                        break;
                    case "TAG_APP":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_APP, row);
                        break;
                    case "TAG_DEVICE":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_TAG_DEVICE, row);
                        break;

                    case "EDGE_make_attack":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_make_attack, row);
                        break;
                    case "EDGE_attack_to":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_attack_to, row);
                        break;
                    case "EDGE_ip_belong_to_apt":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_ip_belong_to_apt, row);
                        break;
                    case "EDGE_domain_belong_to_apt":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_domain_belong_to_apt, row);
                        break;
                    case "EDGE_ip_belong_to_org":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_ip_belong_to_org, row);
                        break;

                    case "EDGE_http_connect":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_http_connect, row);
                        break;
                    case "EDGE_client_use_ua":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_ua, row);
                        break;
                    case "EDGE_ua_connect_domain":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_ua_connect_domain, row);
                        break;
                    case "EDGE_client_http_connect_domain":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_http_connect_domain, row);
                        break;
                    case "EDGE_server_http_connect_domain":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_server_http_connect_domain, row);
                        break;
                    case "EDGE_client_http_connect_url":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_http_connect_url, row);
                        break;

                    case "EDGE_client_query_domain":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_query_domain, row);
                        break;
                    case "EDGE_client_query_dns_server":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_query_dns_server, row);
                        break;
                    case "EDGE_dns_server_resolves_domain":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_dns_server_resolves_domain, row);
                        break;
                    case "EDGE_parse_to":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_parse_to, row);
                        break;

                    case "EDGE_client_use_cert":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_cert, row);
                        break;
                    case "EDGE_server_use_cert":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_server_use_cert, row);
                        break;
                    case "EDGE_cert_belong_to_org":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_cert_belong_to_org, row);
                        break;

                    case "EDGE_cert_connect_sni":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_cert_connect_sni, row);
                        break;
                    case "EDGE_cert_connect_sslfinger":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_cert_connect_sslfinger, row);
                        break;
                    case "EDGE_client_use_sslfinger":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_sslfinger, row);
                        break;
                    case "EDGE_server_use_sslfinger":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_server_use_sslfinger, row);
                        break;
                    case "EDGE_sslfinger_connect_sni":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_sslfinger_connect_sni, row);
                        break;

                    case "EDGE_send_mail":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_send_mail, row);
                        break;
                    case "EDGE_receive_mail":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_receive_mail, row);
                        break;
                    case "EDGE_include_file":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_include_file, row);
                        break;

                    case "EDGE_send_file":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_send_file, row);
                        break;
                    case "EDGE_receive_file":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_receive_file, row);
                        break;
                    case "EDGE_file_connect_ip":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_file_connect_ip, row);
                        break;
                    case "EDGE_file_connect_domain":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_file_connect_domain, row);
                        break;
                    case "EDGE_file_connect_url":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_file_connect_url, row);
                        break;
                    case "EDGE_sender_send_file":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_sender_send_file, row);
                        break;
                    case "EDGE_receiver_receive_file":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_receiver_receive_file, row);
                        break;

                    case "EDGE_attack_belong_to_apt_group":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_attack_belong_to_apt_group, row);
                        break;

                    case "EDGE_app_connect_cert":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_app_connect_cert, row);
                        break;
                    case "EDGE_client_use_app":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_client_use_app, row);
                        break;
                    case "EDGE_app_belong_to_server":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_app_belong_to_server, row);
                        break;
                    case "EDGE_attack_to_device":
                        ctx.output(PanoramaOutPutTagConstant.ALARM_OUTPUT_EDGE_attack_to_device, row);
                        break;
                    default:
                        break;
                }
            }
        });
    }
}
