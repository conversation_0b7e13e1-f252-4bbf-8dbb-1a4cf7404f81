package com.geeksec.entity.pojo;

import cn.hutool.core.util.StrUtil;
import com.geeksec.common.utils.MailUtils;
import com.geeksec.config.util.MD5;
import com.geeksec.entity.po.Email;
import com.geeksec.entity.trans.EmailTrans;
import com.geeksec.entity.trans.IPTrans;
import lombok.Data;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class EmailAlertTrans {
    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;
    private long attackTime;

    private String mailId;

    private EmailTrans emailTrans;

    /**
     * 对于文件来说 可能会存在邮件的收发者
     * 发起者统一为单个个体
     * 收件人可能为多个个体
     * */
    private Email emailSender = null;
    private List<Email> receiverList = null;

    public List<Row> getAllRows() {
        List<Row> rows = new ArrayList<>();

        parseMailSenderAndReceiver();

        rows.add(getMailRow());

        rows.add(getIncludeFileRow());

        if (emailSender != null && receiverList != null) {
            rows.addAll(getAllMailRelatedRows());
        }

        return rows;
    }

    // 解析当前告警中的邮件信息
    private void parseMailSenderAndReceiver() {
        List<Email> emailReceiverList = MailUtils.parseEmailRaw(emailTrans.getMailReceiverRaw());
        List<Email> emailSenderList = MailUtils.parseEmailRaw(emailTrans.getMailSenderRaw());

        if(!emailSenderList.isEmpty()){
            emailSender = emailSenderList.get(0);
        }
        if(!emailReceiverList.isEmpty()){
            receiverList = emailReceiverList;
        }
    }

    /**
     * MAIL 邮件信息节点录入
     *
     * @return
     */
    private Row getMailRow() {
        Row row = new Row(5);
        row.setField(0, "TAG_MAIL");

        row.setField(1, mailId);
        row.setField(2, emailTrans.getMailSubject());
        row.setField(3, emailTrans.getIndustry());
        row.setField(4, emailTrans.getIntent());
        return row;
    }

    private Row getIncludeFileRow() {
        Row row = new Row(5);
        row.setField(0, "EDGE_include_file");
        row.setField(1, mailId);
        row.setField(2, emailTrans.getFileMd5());
        row.setField(3, 0);
        row.setField(4, emailTrans.getFileMd5());
        return row;
    }

    /**
     * EMAIL 邮箱信息节点&边关系录入
     *
     * @return
     */
    private List<Row> getAllMailRelatedRows() {
        List<Row> rows = new ArrayList<>();

        // 发送者信息(邮箱地址作为VID)
        Row senderRow = new Row(4);
        senderRow.setField(0, "TAG_EMAIL");
        senderRow.setField(1, MD5.getMd5(emailSender.getEmailAddr()));
        senderRow.setField(2, emailSender.getEmailAddr());
        senderRow.setField(3, emailSender.getUserName());
        rows.add(senderRow);

        // 发起mail的关系 send_mail
        Row writeMailRow = new Row(4);
        writeMailRow.setField(0, "EDGE_send_mail");
        writeMailRow.setField(1, MD5.getMd5(emailSender.getEmailAddr()));
        writeMailRow.setField(2, mailId);
        writeMailRow.setField(3, 0);
        rows.add(writeMailRow);

        // sender_send_file
        Row senderSendFileRow = new Row(4);
        senderSendFileRow.setField(0, "EDGE_sender_send_file");
        senderSendFileRow.setField(1, MD5.getMd5(emailSender.getEmailAddr()));
        senderSendFileRow.setField(2, emailTrans.getFileMd5());
        senderSendFileRow.setField(3, 0);
        rows.add(senderSendFileRow);

        // Receiver EMAIL
        for (Email remail : this.receiverList) {
            Row receiverRow = new Row(4);
            receiverRow.setField(0, "TAG_EMAIL");
            receiverRow.setField(1, MD5.getMd5(remail.getEmailAddr()));
            receiverRow.setField(2, remail.getEmailAddr());
            receiverRow.setField(3, remail.getUserName());
            rows.add(receiverRow);

            Row mailToRow = new Row(4);
            mailToRow.setField(0, "EDGE_receive_mail");
            mailToRow.setField(1, mailId);
            mailToRow.setField(2, MD5.getMd5(remail.getEmailAddr()));
            mailToRow.setField(3, 0);
            rows.add(mailToRow);

            // receiver_receive_file
            Row receiverReceiveFileRow = new Row(4);
            receiverReceiveFileRow.setField(0, "EDGE_receiver_receive_file");
            receiverReceiveFileRow.setField(1, emailTrans.getFileMd5());
            receiverReceiveFileRow.setField(2, MD5.getMd5(remail.getEmailAddr()));
            receiverReceiveFileRow.setField(3, 0);
            rows.add(receiverReceiveFileRow);
        }

        return rows;

    }
}
