package com.geeksec.entity.po;

import lombok.Data;
import ua_parser.Client;
import ua_parser.Parser;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Data
public class UaParseInfo {

    private String osName;

    private String deviceName;

    private String applicationName;

    private String userAgentKey;

    // 初始化示例
    public static UaParseInfo init(String userAgent, Parser uaParser) throws UnsupportedEncodingException {
        UaParseInfo uaParseInfo = new UaParseInfo();
        Client client = uaParser.parse(userAgent);

        // 提取操作系统信息
        String osFamily = client.os.family != null ? client.os.family : "Unknown OS";
        String osMajor = client.os.major != null ? client.os.major : "";
        String osMinor = client.os.minor != null ? client.os.minor : "";
        String osPatch = client.os.patch != null ? client.os.patch : "";

        // 拼接操作系统版本（如 "14.6.1"）
        String osVersion = Stream.of(osMajor, osMinor, osPatch)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.joining("."));

        // 合并设备信息和操作系统信息
        String osName = osFamily + (osVersion.isEmpty() ? "" : " " + osVersion);
        // 提取设备信息
        String deviceName = client.device.family != null ? client.device.family : "Unknown";

        // applicationName 应用程序需要使用URL decode进行解码
        String applicationName = URLDecoder.decode(client.userAgent.family, "UTF-8");

        String userAgentKey = applicationName + "_" + osName + "_" + deviceName;

        uaParseInfo.setOsName(osName);
        uaParseInfo.setDeviceName(deviceName);
        uaParseInfo.setApplicationName(applicationName);
        uaParseInfo.setUserAgentKey(userAgentKey);
        return uaParseInfo;
    }

}
