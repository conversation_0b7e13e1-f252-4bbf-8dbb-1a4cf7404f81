package com.geeksec.proto.message;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: FILE_ALERT_INFO.proto
// Protobuf Java Version: 4.29.4

public final class FileAlertInfo {
  private FileAlertInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      FileAlertInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FILE_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:FILE_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     * @return Whether the fileMd5 field is set.
     */
    boolean hasFileMd5();
    /**
     * <pre>
     * 文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     * @return The fileMd5.
     */
    String getFileMd5();
    /**
     * <pre>
     * 文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     * @return The bytes for fileMd5.
     */
    com.google.protobuf.ByteString
        getFileMd5Bytes();

    /**
     * <pre>
     * 文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     * @return Whether the fileSha1 field is set.
     */
    boolean hasFileSha1();
    /**
     * <pre>
     * 文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     * @return The fileSha1.
     */
    String getFileSha1();
    /**
     * <pre>
     * 文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     * @return The bytes for fileSha1.
     */
    com.google.protobuf.ByteString
        getFileSha1Bytes();

    /**
     * <pre>
     * 文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     * @return Whether the fileSha256 field is set.
     */
    boolean hasFileSha256();
    /**
     * <pre>
     * 文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     * @return The fileSha256.
     */
    String getFileSha256();
    /**
     * <pre>
     * 文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     * @return The bytes for fileSha256.
     */
    com.google.protobuf.ByteString
        getFileSha256Bytes();

    /**
     * <pre>
     * 文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     * @return Whether the fileSha512 field is set.
     */
    boolean hasFileSha512();
    /**
     * <pre>
     * 文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     * @return The fileSha512.
     */
    String getFileSha512();
    /**
     * <pre>
     * 文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     * @return The bytes for fileSha512.
     */
    com.google.protobuf.ByteString
        getFileSha512Bytes();

    /**
     * <pre>
     * 文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     * @return Whether the fileCrc32 field is set.
     */
    boolean hasFileCrc32();
    /**
     * <pre>
     * 文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     * @return The fileCrc32.
     */
    String getFileCrc32();
    /**
     * <pre>
     * 文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     * @return The bytes for fileCrc32.
     */
    com.google.protobuf.ByteString
        getFileCrc32Bytes();

    /**
     * <pre>
     * 文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     * @return Whether the fileSsdeep field is set.
     */
    boolean hasFileSsdeep();
    /**
     * <pre>
     * 文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     * @return The fileSsdeep.
     */
    String getFileSsdeep();
    /**
     * <pre>
     * 文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     * @return The bytes for fileSsdeep.
     */
    com.google.protobuf.ByteString
        getFileSsdeepBytes();

    /**
     * <pre>
     * 文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     * @return Whether the fileSize field is set.
     */
    boolean hasFileSize();
    /**
     * <pre>
     * 文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     * @return The fileSize.
     */
    int getFileSize();

    /**
     * <pre>
     * 文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     * @return Whether the fileType field is set.
     */
    boolean hasFileType();
    /**
     * <pre>
     * 文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     * @return The fileType.
     */
    String getFileType();
    /**
     * <pre>
     * 文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     * @return The bytes for fileType.
     */
    com.google.protobuf.ByteString
        getFileTypeBytes();

    /**
     * <pre>
     * 文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     * @return Whether the fileOffsetHashMd5 field is set.
     */
    boolean hasFileOffsetHashMd5();
    /**
     * <pre>
     * 文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     * @return The fileOffsetHashMd5.
     */
    String getFileOffsetHashMd5();
    /**
     * <pre>
     * 文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     * @return The bytes for fileOffsetHashMd5.
     */
    com.google.protobuf.ByteString
        getFileOffsetHashMd5Bytes();

    /**
     * <pre>
     * 文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     * @return Whether the fileOffsetHashChunkSize field is set.
     */
    boolean hasFileOffsetHashChunkSize();
    /**
     * <pre>
     * 文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     * @return The fileOffsetHashChunkSize.
     */
    int getFileOffsetHashChunkSize();

    /**
     * <pre>
     * HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     * @return Whether the fileHashResult field is set.
     */
    boolean hasFileHashResult();
    /**
     * <pre>
     * HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     * @return The fileHashResult.
     */
    String getFileHashResult();
    /**
     * <pre>
     * HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     * @return The bytes for fileHashResult.
     */
    com.google.protobuf.ByteString
        getFileHashResultBytes();

    /**
     * <pre>
     * AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     * @return Whether the fileAvResult field is set.
     */
    boolean hasFileAvResult();
    /**
     * <pre>
     * AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     * @return The fileAvResult.
     */
    String getFileAvResult();
    /**
     * <pre>
     * AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     * @return The bytes for fileAvResult.
     */
    com.google.protobuf.ByteString
        getFileAvResultBytes();

    /**
     * <pre>
     * 外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     * @return Whether the fileExAvName field is set.
     */
    boolean hasFileExAvName();
    /**
     * <pre>
     * 外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     * @return The fileExAvName.
     */
    String getFileExAvName();
    /**
     * <pre>
     * 外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     * @return The bytes for fileExAvName.
     */
    com.google.protobuf.ByteString
        getFileExAvNameBytes();

    /**
     * <pre>
     * 外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     * @return Whether the fileExAvResult field is set.
     */
    boolean hasFileExAvResult();
    /**
     * <pre>
     * 外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     * @return The fileExAvResult.
     */
    String getFileExAvResult();
    /**
     * <pre>
     * 外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     * @return The bytes for fileExAvResult.
     */
    com.google.protobuf.ByteString
        getFileExAvResultBytes();

    /**
     * <pre>
     * yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     * @return Whether the fileYaraRuleName field is set.
     */
    boolean hasFileYaraRuleName();
    /**
     * <pre>
     * yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     * @return The fileYaraRuleName.
     */
    String getFileYaraRuleName();
    /**
     * <pre>
     * yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     * @return The bytes for fileYaraRuleName.
     */
    com.google.protobuf.ByteString
        getFileYaraRuleNameBytes();

    /**
     * <pre>
     * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     * @return Whether the fileYaraThreatLevel field is set.
     */
    boolean hasFileYaraThreatLevel();
    /**
     * <pre>
     * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     * @return The fileYaraThreatLevel.
     */
    int getFileYaraThreatLevel();

    /**
     * <pre>
     * DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     * @return Whether the fileDde field is set.
     */
    boolean hasFileDde();
    /**
     * <pre>
     * DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     * @return The fileDde.
     */
    String getFileDde();
    /**
     * <pre>
     * DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     * @return The bytes for fileDde.
     */
    com.google.protobuf.ByteString
        getFileDdeBytes();

    /**
     * <pre>
     * 沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     * @return Whether the filePlatform field is set.
     */
    boolean hasFilePlatform();
    /**
     * <pre>
     * 沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     * @return The filePlatform.
     */
    String getFilePlatform();
    /**
     * <pre>
     * 沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     * @return The bytes for filePlatform.
     */
    com.google.protobuf.ByteString
        getFilePlatformBytes();

    /**
     * <pre>
     * 威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     * @return Whether the fileMlDetectModel field is set.
     */
    boolean hasFileMlDetectModel();
    /**
     * <pre>
     * 威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     * @return The fileMlDetectModel.
     */
    String getFileMlDetectModel();
    /**
     * <pre>
     * 威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     * @return The bytes for fileMlDetectModel.
     */
    com.google.protobuf.ByteString
        getFileMlDetectModelBytes();

    /**
     * <pre>
     * 威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     * @return Whether the fileMlPrecision field is set.
     */
    boolean hasFileMlPrecision();
    /**
     * <pre>
     * 威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     * @return The fileMlPrecision.
     */
    String getFileMlPrecision();
    /**
     * <pre>
     * 威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     * @return The bytes for fileMlPrecision.
     */
    com.google.protobuf.ByteString
        getFileMlPrecisionBytes();

    /**
     * <pre>
     * 威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     * @return Whether the fileMlClassModel field is set.
     */
    boolean hasFileMlClassModel();
    /**
     * <pre>
     * 威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     * @return The fileMlClassModel.
     */
    String getFileMlClassModel();
    /**
     * <pre>
     * 威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     * @return The bytes for fileMlClassModel.
     */
    com.google.protobuf.ByteString
        getFileMlClassModelBytes();

    /**
     * <pre>
     * 威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     * @return Whether the fileMlPrediction field is set.
     */
    boolean hasFileMlPrediction();
    /**
     * <pre>
     * 威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     * @return The fileMlPrediction.
     */
    double getFileMlPrediction();

    /**
     * <pre>
     * IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     * @return Whether the fileIocIp field is set.
     */
    boolean hasFileIocIp();
    /**
     * <pre>
     * IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     * @return The fileIocIp.
     */
    String getFileIocIp();
    /**
     * <pre>
     * IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     * @return The bytes for fileIocIp.
     */
    com.google.protobuf.ByteString
        getFileIocIpBytes();

    /**
     * <pre>
     * DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     * @return Whether the fileIocDns field is set.
     */
    boolean hasFileIocDns();
    /**
     * <pre>
     * DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     * @return The fileIocDns.
     */
    String getFileIocDns();
    /**
     * <pre>
     * DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     * @return The bytes for fileIocDns.
     */
    com.google.protobuf.ByteString
        getFileIocDnsBytes();

    /**
     * <pre>
     * URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     * @return Whether the fileIocUrl field is set.
     */
    boolean hasFileIocUrl();
    /**
     * <pre>
     * URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     * @return The fileIocUrl.
     */
    String getFileIocUrl();
    /**
     * <pre>
     * URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     * @return The bytes for fileIocUrl.
     */
    com.google.protobuf.ByteString
        getFileIocUrlBytes();

    /**
     * <pre>
     * 文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     * @return Whether the filePath field is set.
     */
    boolean hasFilePath();
    /**
     * <pre>
     * 文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     * @return The filePath.
     */
    String getFilePath();
    /**
     * <pre>
     * 文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     * @return The bytes for filePath.
     */
    com.google.protobuf.ByteString
        getFilePathBytes();

    /**
     * <pre>
     * 沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     * @return Whether the sandboxReportUrl field is set.
     */
    boolean hasSandboxReportUrl();
    /**
     * <pre>
     * 沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     * @return The sandboxReportUrl.
     */
    String getSandboxReportUrl();
    /**
     * <pre>
     * 沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     * @return The bytes for sandboxReportUrl.
     */
    com.google.protobuf.ByteString
        getSandboxReportUrlBytes();

    /**
     * <pre>
     * 文件名
     * </pre>
     *
     * <code>required string file_name = 28;</code>
     * @return Whether the fileName field is set.
     */
    boolean hasFileName();
    /**
     * <pre>
     * 文件名
     * </pre>
     *
     * <code>required string file_name = 28;</code>
     * @return The fileName.
     */
    String getFileName();
    /**
     * <pre>
     * 文件名
     * </pre>
     *
     * <code>required string file_name = 28;</code>
     * @return The bytes for fileName.
     */
    com.google.protobuf.ByteString
        getFileNameBytes();

    /**
     * <pre>
     * 发件人
     * </pre>
     *
     * <code>optional string file_email_sender = 29;</code>
     * @return Whether the fileEmailSender field is set.
     */
    boolean hasFileEmailSender();
    /**
     * <pre>
     * 发件人
     * </pre>
     *
     * <code>optional string file_email_sender = 29;</code>
     * @return The fileEmailSender.
     */
    String getFileEmailSender();
    /**
     * <pre>
     * 发件人
     * </pre>
     *
     * <code>optional string file_email_sender = 29;</code>
     * @return The bytes for fileEmailSender.
     */
    com.google.protobuf.ByteString
        getFileEmailSenderBytes();

    /**
     * <pre>
     * 收件人
     * </pre>
     *
     * <code>optional string file_email_receiver = 30;</code>
     * @return Whether the fileEmailReceiver field is set.
     */
    boolean hasFileEmailReceiver();
    /**
     * <pre>
     * 收件人
     * </pre>
     *
     * <code>optional string file_email_receiver = 30;</code>
     * @return The fileEmailReceiver.
     */
    String getFileEmailReceiver();
    /**
     * <pre>
     * 收件人
     * </pre>
     *
     * <code>optional string file_email_receiver = 30;</code>
     * @return The bytes for fileEmailReceiver.
     */
    com.google.protobuf.ByteString
        getFileEmailReceiverBytes();

    /**
     * <pre>
     * 邮件主题
     * </pre>
     *
     * <code>optional string file_email_subject = 31;</code>
     * @return Whether the fileEmailSubject field is set.
     */
    boolean hasFileEmailSubject();
    /**
     * <pre>
     * 邮件主题
     * </pre>
     *
     * <code>optional string file_email_subject = 31;</code>
     * @return The fileEmailSubject.
     */
    String getFileEmailSubject();
    /**
     * <pre>
     * 邮件主题
     * </pre>
     *
     * <code>optional string file_email_subject = 31;</code>
     * @return The bytes for fileEmailSubject.
     */
    com.google.protobuf.ByteString
        getFileEmailSubjectBytes();
  }
  /**
   * <pre>
   * 文件告警信息
   * </pre>
   *
   * Protobuf type {@code FILE_ALERT_INFO}
   */
  public static final class FILE_ALERT_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:FILE_ALERT_INFO)
      FILE_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        FILE_ALERT_INFO.class.getName());
    }
    // Use FILE_ALERT_INFO.newBuilder() to construct.
    private FILE_ALERT_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private FILE_ALERT_INFO() {
      fileMd5_ = "";
      fileSha1_ = "";
      fileSha256_ = "";
      fileSha512_ = "";
      fileCrc32_ = "";
      fileSsdeep_ = "";
      fileType_ = "";
      fileOffsetHashMd5_ = "";
      fileHashResult_ = "";
      fileAvResult_ = "";
      fileExAvName_ = "";
      fileExAvResult_ = "";
      fileYaraRuleName_ = "";
      fileDde_ = "";
      filePlatform_ = "";
      fileMlDetectModel_ = "";
      fileMlPrecision_ = "";
      fileMlClassModel_ = "";
      fileIocIp_ = "";
      fileIocDns_ = "";
      fileIocUrl_ = "";
      filePath_ = "";
      sandboxReportUrl_ = "";
      fileName_ = "";
      fileEmailSender_ = "";
      fileEmailReceiver_ = "";
      fileEmailSubject_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return FileAlertInfo.internal_static_FILE_ALERT_INFO_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return FileAlertInfo.internal_static_FILE_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              FILE_ALERT_INFO.class, Builder.class);
    }

    private int bitField0_;
    public static final int FILE_MD5_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object fileMd5_ = "";
    /**
     * <pre>
     * 文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     * @return Whether the fileMd5 field is set.
     */
    @Override
    public boolean hasFileMd5() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     * @return The fileMd5.
     */
    @Override
    public String getFileMd5() {
      Object ref = fileMd5_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMd5_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件MD5	
     * </pre>
     *
     * <code>required string file_md5 = 1;</code>
     * @return The bytes for fileMd5.
     */
    @Override
    public com.google.protobuf.ByteString
        getFileMd5Bytes() {
      Object ref = fileMd5_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        fileMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SHA1_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile Object fileSha1_ = "";
    /**
     * <pre>
     * 文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     * @return Whether the fileSha1 field is set.
     */
    @Override
    public boolean hasFileSha1() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     * @return The fileSha1.
     */
    @Override
    public String getFileSha1() {
      Object ref = fileSha1_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSha1_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件SHA1	
     * </pre>
     *
     * <code>required string file_sha1 = 2;</code>
     * @return The bytes for fileSha1.
     */
    @Override
    public com.google.protobuf.ByteString
        getFileSha1Bytes() {
      Object ref = fileSha1_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        fileSha1_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SHA256_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileSha256_ = "";
    /**
     * <pre>
     * 文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     * @return Whether the fileSha256 field is set.
     */
    @java.lang.Override
    public boolean hasFileSha256() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     * @return The fileSha256.
     */
    @java.lang.Override
    public java.lang.String getFileSha256() {
      java.lang.Object ref = fileSha256_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSha256_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件SHA256	
     * </pre>
     *
     * <code>required string file_sha256 = 3;</code>
     * @return The bytes for fileSha256.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileSha256Bytes() {
      java.lang.Object ref = fileSha256_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileSha256_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SHA512_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileSha512_ = "";
    /**
     * <pre>
     * 文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     * @return Whether the fileSha512 field is set.
     */
    @java.lang.Override
    public boolean hasFileSha512() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     * @return The fileSha512.
     */
    @java.lang.Override
    public java.lang.String getFileSha512() {
      java.lang.Object ref = fileSha512_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSha512_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件SHA512	
     * </pre>
     *
     * <code>required string file_sha512 = 4;</code>
     * @return The bytes for fileSha512.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileSha512Bytes() {
      java.lang.Object ref = fileSha512_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileSha512_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_CRC32_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileCrc32_ = "";
    /**
     * <pre>
     * 文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     * @return Whether the fileCrc32 field is set.
     */
    @java.lang.Override
    public boolean hasFileCrc32() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     * @return The fileCrc32.
     */
    @java.lang.Override
    public java.lang.String getFileCrc32() {
      java.lang.Object ref = fileCrc32_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileCrc32_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件CRC32	
     * </pre>
     *
     * <code>required string file_crc32 = 5;</code>
     * @return The bytes for fileCrc32.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileCrc32Bytes() {
      java.lang.Object ref = fileCrc32_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileCrc32_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SSDEEP_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileSsdeep_ = "";
    /**
     * <pre>
     * 文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     * @return Whether the fileSsdeep field is set.
     */
    @java.lang.Override
    public boolean hasFileSsdeep() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     * @return The fileSsdeep.
     */
    @java.lang.Override
    public java.lang.String getFileSsdeep() {
      java.lang.Object ref = fileSsdeep_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileSsdeep_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件SSDeep	
     * </pre>
     *
     * <code>required string file_ssdeep = 6;</code>
     * @return The bytes for fileSsdeep.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileSsdeepBytes() {
      java.lang.Object ref = fileSsdeep_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileSsdeep_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_SIZE_FIELD_NUMBER = 7;
    private int fileSize_ = 0;
    /**
     * <pre>
     * 文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     * @return Whether the fileSize field is set.
     */
    @java.lang.Override
    public boolean hasFileSize() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 文件大小	
     * </pre>
     *
     * <code>required uint32 file_size = 7;</code>
     * @return The fileSize.
     */
    @java.lang.Override
    public int getFileSize() {
      return fileSize_;
    }

    public static final int FILE_TYPE_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileType_ = "";
    /**
     * <pre>
     * 文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     * @return Whether the fileType field is set.
     */
    @java.lang.Override
    public boolean hasFileType() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     * @return The fileType.
     */
    @java.lang.Override
    public java.lang.String getFileType() {
      java.lang.Object ref = fileType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件结构签名	
     * </pre>
     *
     * <code>required string file_type = 8;</code>
     * @return The bytes for fileType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileTypeBytes() {
      java.lang.Object ref = fileType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_OFFSET_HASH_MD5_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileOffsetHashMd5_ = "";
    /**
     * <pre>
     * 文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     * @return Whether the fileOffsetHashMd5 field is set.
     */
    @java.lang.Override
    public boolean hasFileOffsetHashMd5() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     * @return The fileOffsetHashMd5.
     */
    @java.lang.Override
    public java.lang.String getFileOffsetHashMd5() {
      java.lang.Object ref = fileOffsetHashMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileOffsetHashMd5_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件偏移HASH	
     * </pre>
     *
     * <code>required string file_offset_hash_md5 = 9;</code>
     * @return The bytes for fileOffsetHashMd5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileOffsetHashMd5Bytes() {
      java.lang.Object ref = fileOffsetHashMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileOffsetHashMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_OFFSET_HASH_CHUNK_SIZE_FIELD_NUMBER = 10;
    private int fileOffsetHashChunkSize_ = 0;
    /**
     * <pre>
     * 文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     * @return Whether the fileOffsetHashChunkSize field is set.
     */
    @java.lang.Override
    public boolean hasFileOffsetHashChunkSize() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 文件偏移HASH位置	
     * </pre>
     *
     * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
     * @return The fileOffsetHashChunkSize.
     */
    @java.lang.Override
    public int getFileOffsetHashChunkSize() {
      return fileOffsetHashChunkSize_;
    }

    public static final int FILE_HASH_RESULT_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileHashResult_ = "";
    /**
     * <pre>
     * HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     * @return Whether the fileHashResult field is set.
     */
    @java.lang.Override
    public boolean hasFileHashResult() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     * @return The fileHashResult.
     */
    @java.lang.Override
    public java.lang.String getFileHashResult() {
      java.lang.Object ref = fileHashResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileHashResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * HASH检测结果	
     * </pre>
     *
     * <code>required string file_hash_result = 11;</code>
     * @return The bytes for fileHashResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileHashResultBytes() {
      java.lang.Object ref = fileHashResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileHashResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_AV_RESULT_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileAvResult_ = "";
    /**
     * <pre>
     * AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     * @return Whether the fileAvResult field is set.
     */
    @java.lang.Override
    public boolean hasFileAvResult() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     * @return The fileAvResult.
     */
    @java.lang.Override
    public java.lang.String getFileAvResult() {
      java.lang.Object ref = fileAvResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileAvResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * AV检测结果	
     * </pre>
     *
     * <code>required string file_av_result = 12;</code>
     * @return The bytes for fileAvResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileAvResultBytes() {
      java.lang.Object ref = fileAvResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileAvResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_EX_AV_NAME_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileExAvName_ = "";
    /**
     * <pre>
     * 外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     * @return Whether the fileExAvName field is set.
     */
    @java.lang.Override
    public boolean hasFileExAvName() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     * @return The fileExAvName.
     */
    @java.lang.Override
    public java.lang.String getFileExAvName() {
      java.lang.Object ref = fileExAvName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileExAvName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 外部AV名称	
     * </pre>
     *
     * <code>required string file_ex_av_name = 13;</code>
     * @return The bytes for fileExAvName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileExAvNameBytes() {
      java.lang.Object ref = fileExAvName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileExAvName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_EX_AV_RESULT_FIELD_NUMBER = 14;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileExAvResult_ = "";
    /**
     * <pre>
     * 外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     * @return Whether the fileExAvResult field is set.
     */
    @java.lang.Override
    public boolean hasFileExAvResult() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     * @return The fileExAvResult.
     */
    @java.lang.Override
    public java.lang.String getFileExAvResult() {
      java.lang.Object ref = fileExAvResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileExAvResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 外部AV检测结果	
     * </pre>
     *
     * <code>required string file_ex_av_result = 14;</code>
     * @return The bytes for fileExAvResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileExAvResultBytes() {
      java.lang.Object ref = fileExAvResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileExAvResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_YARA_RULE_NAME_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileYaraRuleName_ = "";
    /**
     * <pre>
     * yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     * @return Whether the fileYaraRuleName field is set.
     */
    @java.lang.Override
    public boolean hasFileYaraRuleName() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     * @return The fileYaraRuleName.
     */
    @java.lang.Override
    public java.lang.String getFileYaraRuleName() {
      java.lang.Object ref = fileYaraRuleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileYaraRuleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * yara规则名	
     * </pre>
     *
     * <code>required string file_yara_rule_name = 15;</code>
     * @return The bytes for fileYaraRuleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileYaraRuleNameBytes() {
      java.lang.Object ref = fileYaraRuleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileYaraRuleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_YARA_THREAT_LEVEL_FIELD_NUMBER = 16;
    private int fileYaraThreatLevel_ = 0;
    /**
     * <pre>
     * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     * @return Whether the fileYaraThreatLevel field is set.
     */
    @java.lang.Override
    public boolean hasFileYaraThreatLevel() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
     * </pre>
     *
     * <code>required uint32 file_yara_threat_level = 16;</code>
     * @return The fileYaraThreatLevel.
     */
    @java.lang.Override
    public int getFileYaraThreatLevel() {
      return fileYaraThreatLevel_;
    }

    public static final int FILE_DDE_FIELD_NUMBER = 17;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileDde_ = "";
    /**
     * <pre>
     * DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     * @return Whether the fileDde field is set.
     */
    @java.lang.Override
    public boolean hasFileDde() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     * @return The fileDde.
     */
    @java.lang.Override
    public java.lang.String getFileDde() {
      java.lang.Object ref = fileDde_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileDde_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * DDE内容	
     * </pre>
     *
     * <code>optional string file_dde = 17;</code>
     * @return The bytes for fileDde.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileDdeBytes() {
      java.lang.Object ref = fileDde_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileDde_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_PLATFORM_FIELD_NUMBER = 18;
    @SuppressWarnings("serial")
    private volatile java.lang.Object filePlatform_ = "";
    /**
     * <pre>
     * 沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     * @return Whether the filePlatform field is set.
     */
    @java.lang.Override
    public boolean hasFilePlatform() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * 沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     * @return The filePlatform.
     */
    @java.lang.Override
    public java.lang.String getFilePlatform() {
      java.lang.Object ref = filePlatform_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          filePlatform_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 沙箱系统环境	
     * </pre>
     *
     * <code>required string file_platform = 18;</code>
     * @return The bytes for filePlatform.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFilePlatformBytes() {
      java.lang.Object ref = filePlatform_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        filePlatform_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_DETECT_MODEL_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileMlDetectModel_ = "";
    /**
     * <pre>
     * 威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     * @return Whether the fileMlDetectModel field is set.
     */
    @java.lang.Override
    public boolean hasFileMlDetectModel() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * 威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     * @return The fileMlDetectModel.
     */
    @java.lang.Override
    public java.lang.String getFileMlDetectModel() {
      java.lang.Object ref = fileMlDetectModel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMlDetectModel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁检测模型名称	
     * </pre>
     *
     * <code>required string file_ml_detect_model = 19;</code>
     * @return The bytes for fileMlDetectModel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileMlDetectModelBytes() {
      java.lang.Object ref = fileMlDetectModel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileMlDetectModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_PRECISION_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileMlPrecision_ = "";
    /**
     * <pre>
     * 威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     * @return Whether the fileMlPrecision field is set.
     */
    @java.lang.Override
    public boolean hasFileMlPrecision() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     * @return The fileMlPrecision.
     */
    @java.lang.Override
    public java.lang.String getFileMlPrecision() {
      java.lang.Object ref = fileMlPrecision_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMlPrecision_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁检测模型置信度	百分数字符串
     * </pre>
     *
     * <code>required string file_ml_precision = 20;</code>
     * @return The bytes for fileMlPrecision.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileMlPrecisionBytes() {
      java.lang.Object ref = fileMlPrecision_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileMlPrecision_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_CLASS_MODEL_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileMlClassModel_ = "";
    /**
     * <pre>
     * 威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     * @return Whether the fileMlClassModel field is set.
     */
    @java.lang.Override
    public boolean hasFileMlClassModel() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     * @return The fileMlClassModel.
     */
    @java.lang.Override
    public java.lang.String getFileMlClassModel() {
      java.lang.Object ref = fileMlClassModel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileMlClassModel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁分类模型名称	
     * </pre>
     *
     * <code>required string file_ml_class_model = 21;</code>
     * @return The bytes for fileMlClassModel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileMlClassModelBytes() {
      java.lang.Object ref = fileMlClassModel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileMlClassModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_ML_PREDICTION_FIELD_NUMBER = 22;
    private double fileMlPrediction_ = 0D;
    /**
     * <pre>
     * 威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     * @return Whether the fileMlPrediction field is set.
     */
    @java.lang.Override
    public boolean hasFileMlPrediction() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 威胁分类模型置信度	百分数小数
     * </pre>
     *
     * <code>required double file_ml_prediction = 22;</code>
     * @return The fileMlPrediction.
     */
    @java.lang.Override
    public double getFileMlPrediction() {
      return fileMlPrediction_;
    }

    public static final int FILE_IOC_IP_FIELD_NUMBER = 23;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileIocIp_ = "";
    /**
     * <pre>
     * IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     * @return Whether the fileIocIp field is set.
     */
    @java.lang.Override
    public boolean hasFileIocIp() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     * @return The fileIocIp.
     */
    @java.lang.Override
    public java.lang.String getFileIocIp() {
      java.lang.Object ref = fileIocIp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileIocIp_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IP IOC	
     * </pre>
     *
     * <code>optional string file_ioc_ip = 23;</code>
     * @return The bytes for fileIocIp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileIocIpBytes() {
      java.lang.Object ref = fileIocIp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileIocIp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_IOC_DNS_FIELD_NUMBER = 24;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileIocDns_ = "";
    /**
     * <pre>
     * DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     * @return Whether the fileIocDns field is set.
     */
    @java.lang.Override
    public boolean hasFileIocDns() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     * @return The fileIocDns.
     */
    @java.lang.Override
    public java.lang.String getFileIocDns() {
      java.lang.Object ref = fileIocDns_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileIocDns_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * DNS IOC	
     * </pre>
     *
     * <code>optional string file_ioc_dns = 24;</code>
     * @return The bytes for fileIocDns.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileIocDnsBytes() {
      java.lang.Object ref = fileIocDns_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileIocDns_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_IOC_URL_FIELD_NUMBER = 25;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileIocUrl_ = "";
    /**
     * <pre>
     * URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     * @return Whether the fileIocUrl field is set.
     */
    @java.lang.Override
    public boolean hasFileIocUrl() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <pre>
     * URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     * @return The fileIocUrl.
     */
    @java.lang.Override
    public java.lang.String getFileIocUrl() {
      java.lang.Object ref = fileIocUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileIocUrl_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * URL IOC	
     * </pre>
     *
     * <code>optional string file_ioc_url = 25;</code>
     * @return The bytes for fileIocUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileIocUrlBytes() {
      java.lang.Object ref = fileIocUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileIocUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_PATH_FIELD_NUMBER = 26;
    @SuppressWarnings("serial")
    private volatile java.lang.Object filePath_ = "";
    /**
     * <pre>
     * 文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     * @return Whether the filePath field is set.
     */
    @java.lang.Override
    public boolean hasFilePath() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <pre>
     * 文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     * @return The filePath.
     */
    @java.lang.Override
    public java.lang.String getFilePath() {
      java.lang.Object ref = filePath_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          filePath_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件路径	
     * </pre>
     *
     * <code>required string file_path = 26;</code>
     * @return The bytes for filePath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFilePathBytes() {
      java.lang.Object ref = filePath_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        filePath_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SANDBOX_REPORT_URL_FIELD_NUMBER = 27;
    @SuppressWarnings("serial")
    private volatile java.lang.Object sandboxReportUrl_ = "";
    /**
     * <pre>
     * 沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     * @return Whether the sandboxReportUrl field is set.
     */
    @java.lang.Override
    public boolean hasSandboxReportUrl() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <pre>
     * 沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     * @return The sandboxReportUrl.
     */
    @java.lang.Override
    public java.lang.String getSandboxReportUrl() {
      java.lang.Object ref = sandboxReportUrl_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sandboxReportUrl_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 沙箱报告路径
     * </pre>
     *
     * <code>required string sandbox_report_url = 27;</code>
     * @return The bytes for sandboxReportUrl.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSandboxReportUrlBytes() {
      java.lang.Object ref = sandboxReportUrl_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sandboxReportUrl_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_NAME_FIELD_NUMBER = 28;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileName_ = "";
    /**
     * <pre>
     * 文件名
     * </pre>
     *
     * <code>required string file_name = 28;</code>
     * @return Whether the fileName field is set.
     */
    @java.lang.Override
    public boolean hasFileName() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <pre>
     * 文件名
     * </pre>
     *
     * <code>required string file_name = 28;</code>
     * @return The fileName.
     */
    @java.lang.Override
    public java.lang.String getFileName() {
      java.lang.Object ref = fileName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 文件名
     * </pre>
     *
     * <code>required string file_name = 28;</code>
     * @return The bytes for fileName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileNameBytes() {
      java.lang.Object ref = fileName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_EMAIL_SENDER_FIELD_NUMBER = 29;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileEmailSender_ = "";
    /**
     * <pre>
     * 发件人
     * </pre>
     *
     * <code>optional string file_email_sender = 29;</code>
     * @return Whether the fileEmailSender field is set.
     */
    @java.lang.Override
    public boolean hasFileEmailSender() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <pre>
     * 发件人
     * </pre>
     *
     * <code>optional string file_email_sender = 29;</code>
     * @return The fileEmailSender.
     */
    @java.lang.Override
    public java.lang.String getFileEmailSender() {
      java.lang.Object ref = fileEmailSender_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileEmailSender_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 发件人
     * </pre>
     *
     * <code>optional string file_email_sender = 29;</code>
     * @return The bytes for fileEmailSender.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileEmailSenderBytes() {
      java.lang.Object ref = fileEmailSender_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileEmailSender_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_EMAIL_RECEIVER_FIELD_NUMBER = 30;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileEmailReceiver_ = "";
    /**
     * <pre>
     * 收件人
     * </pre>
     *
     * <code>optional string file_email_receiver = 30;</code>
     * @return Whether the fileEmailReceiver field is set.
     */
    @java.lang.Override
    public boolean hasFileEmailReceiver() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <pre>
     * 收件人
     * </pre>
     *
     * <code>optional string file_email_receiver = 30;</code>
     * @return The fileEmailReceiver.
     */
    @java.lang.Override
    public java.lang.String getFileEmailReceiver() {
      java.lang.Object ref = fileEmailReceiver_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileEmailReceiver_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 收件人
     * </pre>
     *
     * <code>optional string file_email_receiver = 30;</code>
     * @return The bytes for fileEmailReceiver.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileEmailReceiverBytes() {
      java.lang.Object ref = fileEmailReceiver_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileEmailReceiver_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FILE_EMAIL_SUBJECT_FIELD_NUMBER = 31;
    @SuppressWarnings("serial")
    private volatile java.lang.Object fileEmailSubject_ = "";
    /**
     * <pre>
     * 邮件主题
     * </pre>
     *
     * <code>optional string file_email_subject = 31;</code>
     * @return Whether the fileEmailSubject field is set.
     */
    @java.lang.Override
    public boolean hasFileEmailSubject() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <pre>
     * 邮件主题
     * </pre>
     *
     * <code>optional string file_email_subject = 31;</code>
     * @return The fileEmailSubject.
     */
    @java.lang.Override
    public java.lang.String getFileEmailSubject() {
      java.lang.Object ref = fileEmailSubject_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          fileEmailSubject_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 邮件主题
     * </pre>
     *
     * <code>optional string file_email_subject = 31;</code>
     * @return The bytes for fileEmailSubject.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFileEmailSubjectBytes() {
      java.lang.Object ref = fileEmailSubject_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        fileEmailSubject_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasFileMd5()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSha1()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSha256()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSha512()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileCrc32()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSsdeep()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileSize()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileOffsetHashMd5()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileOffsetHashChunkSize()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileHashResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileAvResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileExAvName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileExAvResult()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileYaraRuleName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileYaraThreatLevel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFilePlatform()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlDetectModel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlPrecision()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlClassModel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileMlPrediction()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFilePath()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSandboxReportUrl()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFileName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, fileMd5_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, fileSha1_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, fileSha256_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, fileSha512_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, fileCrc32_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, fileSsdeep_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeUInt32(7, fileSize_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, fileType_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, fileOffsetHashMd5_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeUInt32(10, fileOffsetHashChunkSize_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, fileHashResult_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, fileAvResult_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 13, fileExAvName_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 14, fileExAvResult_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 15, fileYaraRuleName_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeUInt32(16, fileYaraThreatLevel_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 17, fileDde_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 18, filePlatform_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 19, fileMlDetectModel_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 20, fileMlPrecision_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 21, fileMlClassModel_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeDouble(22, fileMlPrediction_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 23, fileIocIp_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 24, fileIocDns_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 25, fileIocUrl_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 26, filePath_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 27, sandboxReportUrl_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 28, fileName_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 29, fileEmailSender_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 30, fileEmailReceiver_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 31, fileEmailSubject_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, fileMd5_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, fileSha1_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, fileSha256_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, fileSha512_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, fileCrc32_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, fileSsdeep_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, fileSize_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, fileType_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(9, fileOffsetHashMd5_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, fileOffsetHashChunkSize_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, fileHashResult_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, fileAvResult_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(13, fileExAvName_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(14, fileExAvResult_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(15, fileYaraRuleName_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, fileYaraThreatLevel_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(17, fileDde_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(18, filePlatform_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(19, fileMlDetectModel_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(20, fileMlPrecision_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(21, fileMlClassModel_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(22, fileMlPrediction_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(23, fileIocIp_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(24, fileIocDns_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(25, fileIocUrl_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(26, filePath_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(27, sandboxReportUrl_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(28, fileName_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(29, fileEmailSender_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(30, fileEmailReceiver_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(31, fileEmailSubject_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof FileAlertInfo.FILE_ALERT_INFO)) {
        return super.equals(obj);
      }
      FileAlertInfo.FILE_ALERT_INFO other = (FileAlertInfo.FILE_ALERT_INFO) obj;

      if (hasFileMd5() != other.hasFileMd5()) return false;
      if (hasFileMd5()) {
        if (!getFileMd5()
            .equals(other.getFileMd5())) return false;
      }
      if (hasFileSha1() != other.hasFileSha1()) return false;
      if (hasFileSha1()) {
        if (!getFileSha1()
            .equals(other.getFileSha1())) return false;
      }
      if (hasFileSha256() != other.hasFileSha256()) return false;
      if (hasFileSha256()) {
        if (!getFileSha256()
            .equals(other.getFileSha256())) return false;
      }
      if (hasFileSha512() != other.hasFileSha512()) return false;
      if (hasFileSha512()) {
        if (!getFileSha512()
            .equals(other.getFileSha512())) return false;
      }
      if (hasFileCrc32() != other.hasFileCrc32()) return false;
      if (hasFileCrc32()) {
        if (!getFileCrc32()
            .equals(other.getFileCrc32())) return false;
      }
      if (hasFileSsdeep() != other.hasFileSsdeep()) return false;
      if (hasFileSsdeep()) {
        if (!getFileSsdeep()
            .equals(other.getFileSsdeep())) return false;
      }
      if (hasFileSize() != other.hasFileSize()) return false;
      if (hasFileSize()) {
        if (getFileSize()
            != other.getFileSize()) return false;
      }
      if (hasFileType() != other.hasFileType()) return false;
      if (hasFileType()) {
        if (!getFileType()
            .equals(other.getFileType())) return false;
      }
      if (hasFileOffsetHashMd5() != other.hasFileOffsetHashMd5()) return false;
      if (hasFileOffsetHashMd5()) {
        if (!getFileOffsetHashMd5()
            .equals(other.getFileOffsetHashMd5())) return false;
      }
      if (hasFileOffsetHashChunkSize() != other.hasFileOffsetHashChunkSize()) return false;
      if (hasFileOffsetHashChunkSize()) {
        if (getFileOffsetHashChunkSize()
            != other.getFileOffsetHashChunkSize()) return false;
      }
      if (hasFileHashResult() != other.hasFileHashResult()) return false;
      if (hasFileHashResult()) {
        if (!getFileHashResult()
            .equals(other.getFileHashResult())) return false;
      }
      if (hasFileAvResult() != other.hasFileAvResult()) return false;
      if (hasFileAvResult()) {
        if (!getFileAvResult()
            .equals(other.getFileAvResult())) return false;
      }
      if (hasFileExAvName() != other.hasFileExAvName()) return false;
      if (hasFileExAvName()) {
        if (!getFileExAvName()
            .equals(other.getFileExAvName())) return false;
      }
      if (hasFileExAvResult() != other.hasFileExAvResult()) return false;
      if (hasFileExAvResult()) {
        if (!getFileExAvResult()
            .equals(other.getFileExAvResult())) return false;
      }
      if (hasFileYaraRuleName() != other.hasFileYaraRuleName()) return false;
      if (hasFileYaraRuleName()) {
        if (!getFileYaraRuleName()
            .equals(other.getFileYaraRuleName())) return false;
      }
      if (hasFileYaraThreatLevel() != other.hasFileYaraThreatLevel()) return false;
      if (hasFileYaraThreatLevel()) {
        if (getFileYaraThreatLevel()
            != other.getFileYaraThreatLevel()) return false;
      }
      if (hasFileDde() != other.hasFileDde()) return false;
      if (hasFileDde()) {
        if (!getFileDde()
            .equals(other.getFileDde())) return false;
      }
      if (hasFilePlatform() != other.hasFilePlatform()) return false;
      if (hasFilePlatform()) {
        if (!getFilePlatform()
            .equals(other.getFilePlatform())) return false;
      }
      if (hasFileMlDetectModel() != other.hasFileMlDetectModel()) return false;
      if (hasFileMlDetectModel()) {
        if (!getFileMlDetectModel()
            .equals(other.getFileMlDetectModel())) return false;
      }
      if (hasFileMlPrecision() != other.hasFileMlPrecision()) return false;
      if (hasFileMlPrecision()) {
        if (!getFileMlPrecision()
            .equals(other.getFileMlPrecision())) return false;
      }
      if (hasFileMlClassModel() != other.hasFileMlClassModel()) return false;
      if (hasFileMlClassModel()) {
        if (!getFileMlClassModel()
            .equals(other.getFileMlClassModel())) return false;
      }
      if (hasFileMlPrediction() != other.hasFileMlPrediction()) return false;
      if (hasFileMlPrediction()) {
        if (java.lang.Double.doubleToLongBits(getFileMlPrediction())
            != java.lang.Double.doubleToLongBits(
                other.getFileMlPrediction())) return false;
      }
      if (hasFileIocIp() != other.hasFileIocIp()) return false;
      if (hasFileIocIp()) {
        if (!getFileIocIp()
            .equals(other.getFileIocIp())) return false;
      }
      if (hasFileIocDns() != other.hasFileIocDns()) return false;
      if (hasFileIocDns()) {
        if (!getFileIocDns()
            .equals(other.getFileIocDns())) return false;
      }
      if (hasFileIocUrl() != other.hasFileIocUrl()) return false;
      if (hasFileIocUrl()) {
        if (!getFileIocUrl()
            .equals(other.getFileIocUrl())) return false;
      }
      if (hasFilePath() != other.hasFilePath()) return false;
      if (hasFilePath()) {
        if (!getFilePath()
            .equals(other.getFilePath())) return false;
      }
      if (hasSandboxReportUrl() != other.hasSandboxReportUrl()) return false;
      if (hasSandboxReportUrl()) {
        if (!getSandboxReportUrl()
            .equals(other.getSandboxReportUrl())) return false;
      }
      if (hasFileName() != other.hasFileName()) return false;
      if (hasFileName()) {
        if (!getFileName()
            .equals(other.getFileName())) return false;
      }
      if (hasFileEmailSender() != other.hasFileEmailSender()) return false;
      if (hasFileEmailSender()) {
        if (!getFileEmailSender()
            .equals(other.getFileEmailSender())) return false;
      }
      if (hasFileEmailReceiver() != other.hasFileEmailReceiver()) return false;
      if (hasFileEmailReceiver()) {
        if (!getFileEmailReceiver()
            .equals(other.getFileEmailReceiver())) return false;
      }
      if (hasFileEmailSubject() != other.hasFileEmailSubject()) return false;
      if (hasFileEmailSubject()) {
        if (!getFileEmailSubject()
            .equals(other.getFileEmailSubject())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasFileMd5()) {
        hash = (37 * hash) + FILE_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getFileMd5().hashCode();
      }
      if (hasFileSha1()) {
        hash = (37 * hash) + FILE_SHA1_FIELD_NUMBER;
        hash = (53 * hash) + getFileSha1().hashCode();
      }
      if (hasFileSha256()) {
        hash = (37 * hash) + FILE_SHA256_FIELD_NUMBER;
        hash = (53 * hash) + getFileSha256().hashCode();
      }
      if (hasFileSha512()) {
        hash = (37 * hash) + FILE_SHA512_FIELD_NUMBER;
        hash = (53 * hash) + getFileSha512().hashCode();
      }
      if (hasFileCrc32()) {
        hash = (37 * hash) + FILE_CRC32_FIELD_NUMBER;
        hash = (53 * hash) + getFileCrc32().hashCode();
      }
      if (hasFileSsdeep()) {
        hash = (37 * hash) + FILE_SSDEEP_FIELD_NUMBER;
        hash = (53 * hash) + getFileSsdeep().hashCode();
      }
      if (hasFileSize()) {
        hash = (37 * hash) + FILE_SIZE_FIELD_NUMBER;
        hash = (53 * hash) + getFileSize();
      }
      if (hasFileType()) {
        hash = (37 * hash) + FILE_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getFileType().hashCode();
      }
      if (hasFileOffsetHashMd5()) {
        hash = (37 * hash) + FILE_OFFSET_HASH_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getFileOffsetHashMd5().hashCode();
      }
      if (hasFileOffsetHashChunkSize()) {
        hash = (37 * hash) + FILE_OFFSET_HASH_CHUNK_SIZE_FIELD_NUMBER;
        hash = (53 * hash) + getFileOffsetHashChunkSize();
      }
      if (hasFileHashResult()) {
        hash = (37 * hash) + FILE_HASH_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getFileHashResult().hashCode();
      }
      if (hasFileAvResult()) {
        hash = (37 * hash) + FILE_AV_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getFileAvResult().hashCode();
      }
      if (hasFileExAvName()) {
        hash = (37 * hash) + FILE_EX_AV_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getFileExAvName().hashCode();
      }
      if (hasFileExAvResult()) {
        hash = (37 * hash) + FILE_EX_AV_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getFileExAvResult().hashCode();
      }
      if (hasFileYaraRuleName()) {
        hash = (37 * hash) + FILE_YARA_RULE_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getFileYaraRuleName().hashCode();
      }
      if (hasFileYaraThreatLevel()) {
        hash = (37 * hash) + FILE_YARA_THREAT_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getFileYaraThreatLevel();
      }
      if (hasFileDde()) {
        hash = (37 * hash) + FILE_DDE_FIELD_NUMBER;
        hash = (53 * hash) + getFileDde().hashCode();
      }
      if (hasFilePlatform()) {
        hash = (37 * hash) + FILE_PLATFORM_FIELD_NUMBER;
        hash = (53 * hash) + getFilePlatform().hashCode();
      }
      if (hasFileMlDetectModel()) {
        hash = (37 * hash) + FILE_ML_DETECT_MODEL_FIELD_NUMBER;
        hash = (53 * hash) + getFileMlDetectModel().hashCode();
      }
      if (hasFileMlPrecision()) {
        hash = (37 * hash) + FILE_ML_PRECISION_FIELD_NUMBER;
        hash = (53 * hash) + getFileMlPrecision().hashCode();
      }
      if (hasFileMlClassModel()) {
        hash = (37 * hash) + FILE_ML_CLASS_MODEL_FIELD_NUMBER;
        hash = (53 * hash) + getFileMlClassModel().hashCode();
      }
      if (hasFileMlPrediction()) {
        hash = (37 * hash) + FILE_ML_PREDICTION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            java.lang.Double.doubleToLongBits(getFileMlPrediction()));
      }
      if (hasFileIocIp()) {
        hash = (37 * hash) + FILE_IOC_IP_FIELD_NUMBER;
        hash = (53 * hash) + getFileIocIp().hashCode();
      }
      if (hasFileIocDns()) {
        hash = (37 * hash) + FILE_IOC_DNS_FIELD_NUMBER;
        hash = (53 * hash) + getFileIocDns().hashCode();
      }
      if (hasFileIocUrl()) {
        hash = (37 * hash) + FILE_IOC_URL_FIELD_NUMBER;
        hash = (53 * hash) + getFileIocUrl().hashCode();
      }
      if (hasFilePath()) {
        hash = (37 * hash) + FILE_PATH_FIELD_NUMBER;
        hash = (53 * hash) + getFilePath().hashCode();
      }
      if (hasSandboxReportUrl()) {
        hash = (37 * hash) + SANDBOX_REPORT_URL_FIELD_NUMBER;
        hash = (53 * hash) + getSandboxReportUrl().hashCode();
      }
      if (hasFileName()) {
        hash = (37 * hash) + FILE_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getFileName().hashCode();
      }
      if (hasFileEmailSender()) {
        hash = (37 * hash) + FILE_EMAIL_SENDER_FIELD_NUMBER;
        hash = (53 * hash) + getFileEmailSender().hashCode();
      }
      if (hasFileEmailReceiver()) {
        hash = (37 * hash) + FILE_EMAIL_RECEIVER_FIELD_NUMBER;
        hash = (53 * hash) + getFileEmailReceiver().hashCode();
      }
      if (hasFileEmailSubject()) {
        hash = (37 * hash) + FILE_EMAIL_SUBJECT_FIELD_NUMBER;
        hash = (53 * hash) + getFileEmailSubject().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static FileAlertInfo.FILE_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static FileAlertInfo.FILE_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static FileAlertInfo.FILE_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(FileAlertInfo.FILE_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 文件告警信息
     * </pre>
     *
     * Protobuf type {@code FILE_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:FILE_ALERT_INFO)
        FileAlertInfo.FILE_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return FileAlertInfo.internal_static_FILE_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return FileAlertInfo.internal_static_FILE_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                FileAlertInfo.FILE_ALERT_INFO.class, FileAlertInfo.FILE_ALERT_INFO.Builder.class);
      }

      // Construct using FileAlertInfo.FILE_ALERT_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        fileMd5_ = "";
        fileSha1_ = "";
        fileSha256_ = "";
        fileSha512_ = "";
        fileCrc32_ = "";
        fileSsdeep_ = "";
        fileSize_ = 0;
        fileType_ = "";
        fileOffsetHashMd5_ = "";
        fileOffsetHashChunkSize_ = 0;
        fileHashResult_ = "";
        fileAvResult_ = "";
        fileExAvName_ = "";
        fileExAvResult_ = "";
        fileYaraRuleName_ = "";
        fileYaraThreatLevel_ = 0;
        fileDde_ = "";
        filePlatform_ = "";
        fileMlDetectModel_ = "";
        fileMlPrecision_ = "";
        fileMlClassModel_ = "";
        fileMlPrediction_ = 0D;
        fileIocIp_ = "";
        fileIocDns_ = "";
        fileIocUrl_ = "";
        filePath_ = "";
        sandboxReportUrl_ = "";
        fileName_ = "";
        fileEmailSender_ = "";
        fileEmailReceiver_ = "";
        fileEmailSubject_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return FileAlertInfo.internal_static_FILE_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public FileAlertInfo.FILE_ALERT_INFO getDefaultInstanceForType() {
        return FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public FileAlertInfo.FILE_ALERT_INFO build() {
        FileAlertInfo.FILE_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public FileAlertInfo.FILE_ALERT_INFO buildPartial() {
        FileAlertInfo.FILE_ALERT_INFO result = new FileAlertInfo.FILE_ALERT_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(FileAlertInfo.FILE_ALERT_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.fileMd5_ = fileMd5_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.fileSha1_ = fileSha1_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.fileSha256_ = fileSha256_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.fileSha512_ = fileSha512_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.fileCrc32_ = fileCrc32_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.fileSsdeep_ = fileSsdeep_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.fileSize_ = fileSize_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.fileType_ = fileType_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.fileOffsetHashMd5_ = fileOffsetHashMd5_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.fileOffsetHashChunkSize_ = fileOffsetHashChunkSize_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.fileHashResult_ = fileHashResult_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.fileAvResult_ = fileAvResult_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.fileExAvName_ = fileExAvName_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.fileExAvResult_ = fileExAvResult_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.fileYaraRuleName_ = fileYaraRuleName_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.fileYaraThreatLevel_ = fileYaraThreatLevel_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.fileDde_ = fileDde_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.filePlatform_ = filePlatform_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.fileMlDetectModel_ = fileMlDetectModel_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.fileMlPrecision_ = fileMlPrecision_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.fileMlClassModel_ = fileMlClassModel_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.fileMlPrediction_ = fileMlPrediction_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.fileIocIp_ = fileIocIp_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.fileIocDns_ = fileIocDns_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.fileIocUrl_ = fileIocUrl_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.filePath_ = filePath_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.sandboxReportUrl_ = sandboxReportUrl_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.fileName_ = fileName_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.fileEmailSender_ = fileEmailSender_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.fileEmailReceiver_ = fileEmailReceiver_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.fileEmailSubject_ = fileEmailSubject_;
          to_bitField0_ |= 0x40000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof FileAlertInfo.FILE_ALERT_INFO) {
          return mergeFrom((FileAlertInfo.FILE_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(FileAlertInfo.FILE_ALERT_INFO other) {
        if (other == FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasFileMd5()) {
          fileMd5_ = other.fileMd5_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasFileSha1()) {
          fileSha1_ = other.fileSha1_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasFileSha256()) {
          fileSha256_ = other.fileSha256_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasFileSha512()) {
          fileSha512_ = other.fileSha512_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (other.hasFileCrc32()) {
          fileCrc32_ = other.fileCrc32_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.hasFileSsdeep()) {
          fileSsdeep_ = other.fileSsdeep_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (other.hasFileSize()) {
          setFileSize(other.getFileSize());
        }
        if (other.hasFileType()) {
          fileType_ = other.fileType_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (other.hasFileOffsetHashMd5()) {
          fileOffsetHashMd5_ = other.fileOffsetHashMd5_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (other.hasFileOffsetHashChunkSize()) {
          setFileOffsetHashChunkSize(other.getFileOffsetHashChunkSize());
        }
        if (other.hasFileHashResult()) {
          fileHashResult_ = other.fileHashResult_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (other.hasFileAvResult()) {
          fileAvResult_ = other.fileAvResult_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (other.hasFileExAvName()) {
          fileExAvName_ = other.fileExAvName_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (other.hasFileExAvResult()) {
          fileExAvResult_ = other.fileExAvResult_;
          bitField0_ |= 0x00002000;
          onChanged();
        }
        if (other.hasFileYaraRuleName()) {
          fileYaraRuleName_ = other.fileYaraRuleName_;
          bitField0_ |= 0x00004000;
          onChanged();
        }
        if (other.hasFileYaraThreatLevel()) {
          setFileYaraThreatLevel(other.getFileYaraThreatLevel());
        }
        if (other.hasFileDde()) {
          fileDde_ = other.fileDde_;
          bitField0_ |= 0x00010000;
          onChanged();
        }
        if (other.hasFilePlatform()) {
          filePlatform_ = other.filePlatform_;
          bitField0_ |= 0x00020000;
          onChanged();
        }
        if (other.hasFileMlDetectModel()) {
          fileMlDetectModel_ = other.fileMlDetectModel_;
          bitField0_ |= 0x00040000;
          onChanged();
        }
        if (other.hasFileMlPrecision()) {
          fileMlPrecision_ = other.fileMlPrecision_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (other.hasFileMlClassModel()) {
          fileMlClassModel_ = other.fileMlClassModel_;
          bitField0_ |= 0x00100000;
          onChanged();
        }
        if (other.hasFileMlPrediction()) {
          setFileMlPrediction(other.getFileMlPrediction());
        }
        if (other.hasFileIocIp()) {
          fileIocIp_ = other.fileIocIp_;
          bitField0_ |= 0x00400000;
          onChanged();
        }
        if (other.hasFileIocDns()) {
          fileIocDns_ = other.fileIocDns_;
          bitField0_ |= 0x00800000;
          onChanged();
        }
        if (other.hasFileIocUrl()) {
          fileIocUrl_ = other.fileIocUrl_;
          bitField0_ |= 0x01000000;
          onChanged();
        }
        if (other.hasFilePath()) {
          filePath_ = other.filePath_;
          bitField0_ |= 0x02000000;
          onChanged();
        }
        if (other.hasSandboxReportUrl()) {
          sandboxReportUrl_ = other.sandboxReportUrl_;
          bitField0_ |= 0x04000000;
          onChanged();
        }
        if (other.hasFileName()) {
          fileName_ = other.fileName_;
          bitField0_ |= 0x08000000;
          onChanged();
        }
        if (other.hasFileEmailSender()) {
          fileEmailSender_ = other.fileEmailSender_;
          bitField0_ |= 0x10000000;
          onChanged();
        }
        if (other.hasFileEmailReceiver()) {
          fileEmailReceiver_ = other.fileEmailReceiver_;
          bitField0_ |= 0x20000000;
          onChanged();
        }
        if (other.hasFileEmailSubject()) {
          fileEmailSubject_ = other.fileEmailSubject_;
          bitField0_ |= 0x40000000;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasFileMd5()) {
          return false;
        }
        if (!hasFileSha1()) {
          return false;
        }
        if (!hasFileSha256()) {
          return false;
        }
        if (!hasFileSha512()) {
          return false;
        }
        if (!hasFileCrc32()) {
          return false;
        }
        if (!hasFileSsdeep()) {
          return false;
        }
        if (!hasFileSize()) {
          return false;
        }
        if (!hasFileType()) {
          return false;
        }
        if (!hasFileOffsetHashMd5()) {
          return false;
        }
        if (!hasFileOffsetHashChunkSize()) {
          return false;
        }
        if (!hasFileHashResult()) {
          return false;
        }
        if (!hasFileAvResult()) {
          return false;
        }
        if (!hasFileExAvName()) {
          return false;
        }
        if (!hasFileExAvResult()) {
          return false;
        }
        if (!hasFileYaraRuleName()) {
          return false;
        }
        if (!hasFileYaraThreatLevel()) {
          return false;
        }
        if (!hasFilePlatform()) {
          return false;
        }
        if (!hasFileMlDetectModel()) {
          return false;
        }
        if (!hasFileMlPrecision()) {
          return false;
        }
        if (!hasFileMlClassModel()) {
          return false;
        }
        if (!hasFileMlPrediction()) {
          return false;
        }
        if (!hasFilePath()) {
          return false;
        }
        if (!hasSandboxReportUrl()) {
          return false;
        }
        if (!hasFileName()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                fileMd5_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                fileSha1_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                fileSha256_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                fileSha512_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                fileCrc32_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                fileSsdeep_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 56: {
                fileSize_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 66: {
                fileType_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                fileOffsetHashMd5_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 80: {
                fileOffsetHashChunkSize_ = input.readUInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 90: {
                fileHashResult_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                fileAvResult_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                fileExAvName_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 114: {
                fileExAvResult_ = input.readBytes();
                bitField0_ |= 0x00002000;
                break;
              } // case 114
              case 122: {
                fileYaraRuleName_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 128: {
                fileYaraThreatLevel_ = input.readUInt32();
                bitField0_ |= 0x00008000;
                break;
              } // case 128
              case 138: {
                fileDde_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                filePlatform_ = input.readBytes();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                fileMlDetectModel_ = input.readBytes();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 162: {
                fileMlPrecision_ = input.readBytes();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 170: {
                fileMlClassModel_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 177: {
                fileMlPrediction_ = input.readDouble();
                bitField0_ |= 0x00200000;
                break;
              } // case 177
              case 186: {
                fileIocIp_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                fileIocDns_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                fileIocUrl_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                filePath_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 218: {
                sandboxReportUrl_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 226: {
                fileName_ = input.readBytes();
                bitField0_ |= 0x08000000;
                break;
              } // case 226
              case 234: {
                fileEmailSender_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              case 242: {
                fileEmailReceiver_ = input.readBytes();
                bitField0_ |= 0x20000000;
                break;
              } // case 242
              case 250: {
                fileEmailSubject_ = input.readBytes();
                bitField0_ |= 0x40000000;
                break;
              } // case 250
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object fileMd5_ = "";
      /**
       * <pre>
       * 文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       * @return Whether the fileMd5 field is set.
       */
      public boolean hasFileMd5() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       * @return The fileMd5.
       */
      public java.lang.String getFileMd5() {
        java.lang.Object ref = fileMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMd5_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       * @return The bytes for fileMd5.
       */
      public com.google.protobuf.ByteString
          getFileMd5Bytes() {
        java.lang.Object ref = fileMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       * @param value The fileMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setFileMd5(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileMd5_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileMd5() {
        fileMd5_ = getDefaultInstance().getFileMd5();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件MD5	
       * </pre>
       *
       * <code>required string file_md5 = 1;</code>
       * @param value The bytes for fileMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setFileMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileMd5_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object fileSha1_ = "";
      /**
       * <pre>
       * 文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       * @return Whether the fileSha1 field is set.
       */
      public boolean hasFileSha1() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       * @return The fileSha1.
       */
      public java.lang.String getFileSha1() {
        java.lang.Object ref = fileSha1_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSha1_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       * @return The bytes for fileSha1.
       */
      public com.google.protobuf.ByteString
          getFileSha1Bytes() {
        java.lang.Object ref = fileSha1_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSha1_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       * @param value The fileSha1 to set.
       * @return This builder for chaining.
       */
      public Builder setFileSha1(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileSha1_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileSha1() {
        fileSha1_ = getDefaultInstance().getFileSha1();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SHA1	
       * </pre>
       *
       * <code>required string file_sha1 = 2;</code>
       * @param value The bytes for fileSha1 to set.
       * @return This builder for chaining.
       */
      public Builder setFileSha1Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileSha1_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object fileSha256_ = "";
      /**
       * <pre>
       * 文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       * @return Whether the fileSha256 field is set.
       */
      public boolean hasFileSha256() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       * @return The fileSha256.
       */
      public java.lang.String getFileSha256() {
        java.lang.Object ref = fileSha256_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSha256_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       * @return The bytes for fileSha256.
       */
      public com.google.protobuf.ByteString
          getFileSha256Bytes() {
        java.lang.Object ref = fileSha256_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSha256_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       * @param value The fileSha256 to set.
       * @return This builder for chaining.
       */
      public Builder setFileSha256(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileSha256_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileSha256() {
        fileSha256_ = getDefaultInstance().getFileSha256();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SHA256	
       * </pre>
       *
       * <code>required string file_sha256 = 3;</code>
       * @param value The bytes for fileSha256 to set.
       * @return This builder for chaining.
       */
      public Builder setFileSha256Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileSha256_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object fileSha512_ = "";
      /**
       * <pre>
       * 文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       * @return Whether the fileSha512 field is set.
       */
      public boolean hasFileSha512() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       * @return The fileSha512.
       */
      public java.lang.String getFileSha512() {
        java.lang.Object ref = fileSha512_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSha512_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       * @return The bytes for fileSha512.
       */
      public com.google.protobuf.ByteString
          getFileSha512Bytes() {
        java.lang.Object ref = fileSha512_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSha512_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       * @param value The fileSha512 to set.
       * @return This builder for chaining.
       */
      public Builder setFileSha512(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileSha512_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileSha512() {
        fileSha512_ = getDefaultInstance().getFileSha512();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SHA512	
       * </pre>
       *
       * <code>required string file_sha512 = 4;</code>
       * @param value The bytes for fileSha512 to set.
       * @return This builder for chaining.
       */
      public Builder setFileSha512Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileSha512_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object fileCrc32_ = "";
      /**
       * <pre>
       * 文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       * @return Whether the fileCrc32 field is set.
       */
      public boolean hasFileCrc32() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       * @return The fileCrc32.
       */
      public java.lang.String getFileCrc32() {
        java.lang.Object ref = fileCrc32_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileCrc32_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       * @return The bytes for fileCrc32.
       */
      public com.google.protobuf.ByteString
          getFileCrc32Bytes() {
        java.lang.Object ref = fileCrc32_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileCrc32_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       * @param value The fileCrc32 to set.
       * @return This builder for chaining.
       */
      public Builder setFileCrc32(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileCrc32_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileCrc32() {
        fileCrc32_ = getDefaultInstance().getFileCrc32();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件CRC32	
       * </pre>
       *
       * <code>required string file_crc32 = 5;</code>
       * @param value The bytes for fileCrc32 to set.
       * @return This builder for chaining.
       */
      public Builder setFileCrc32Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileCrc32_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object fileSsdeep_ = "";
      /**
       * <pre>
       * 文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       * @return Whether the fileSsdeep field is set.
       */
      public boolean hasFileSsdeep() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       * @return The fileSsdeep.
       */
      public java.lang.String getFileSsdeep() {
        java.lang.Object ref = fileSsdeep_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileSsdeep_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       * @return The bytes for fileSsdeep.
       */
      public com.google.protobuf.ByteString
          getFileSsdeepBytes() {
        java.lang.Object ref = fileSsdeep_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileSsdeep_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       * @param value The fileSsdeep to set.
       * @return This builder for chaining.
       */
      public Builder setFileSsdeep(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileSsdeep_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileSsdeep() {
        fileSsdeep_ = getDefaultInstance().getFileSsdeep();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件SSDeep	
       * </pre>
       *
       * <code>required string file_ssdeep = 6;</code>
       * @param value The bytes for fileSsdeep to set.
       * @return This builder for chaining.
       */
      public Builder setFileSsdeepBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileSsdeep_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private int fileSize_ ;
      /**
       * <pre>
       * 文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       * @return Whether the fileSize field is set.
       */
      @java.lang.Override
      public boolean hasFileSize() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       * @return The fileSize.
       */
      @java.lang.Override
      public int getFileSize() {
        return fileSize_;
      }
      /**
       * <pre>
       * 文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       * @param value The fileSize to set.
       * @return This builder for chaining.
       */
      public Builder setFileSize(int value) {

        fileSize_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件大小	
       * </pre>
       *
       * <code>required uint32 file_size = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileSize() {
        bitField0_ = (bitField0_ & ~0x00000040);
        fileSize_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fileType_ = "";
      /**
       * <pre>
       * 文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       * @return Whether the fileType field is set.
       */
      public boolean hasFileType() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       * @return The fileType.
       */
      public java.lang.String getFileType() {
        java.lang.Object ref = fileType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       * @return The bytes for fileType.
       */
      public com.google.protobuf.ByteString
          getFileTypeBytes() {
        java.lang.Object ref = fileType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       * @param value The fileType to set.
       * @return This builder for chaining.
       */
      public Builder setFileType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileType_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileType() {
        fileType_ = getDefaultInstance().getFileType();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件结构签名	
       * </pre>
       *
       * <code>required string file_type = 8;</code>
       * @param value The bytes for fileType to set.
       * @return This builder for chaining.
       */
      public Builder setFileTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileType_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private java.lang.Object fileOffsetHashMd5_ = "";
      /**
       * <pre>
       * 文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       * @return Whether the fileOffsetHashMd5 field is set.
       */
      public boolean hasFileOffsetHashMd5() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       * @return The fileOffsetHashMd5.
       */
      public java.lang.String getFileOffsetHashMd5() {
        java.lang.Object ref = fileOffsetHashMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileOffsetHashMd5_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       * @return The bytes for fileOffsetHashMd5.
       */
      public com.google.protobuf.ByteString
          getFileOffsetHashMd5Bytes() {
        java.lang.Object ref = fileOffsetHashMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileOffsetHashMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       * @param value The fileOffsetHashMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setFileOffsetHashMd5(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileOffsetHashMd5_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileOffsetHashMd5() {
        fileOffsetHashMd5_ = getDefaultInstance().getFileOffsetHashMd5();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件偏移HASH	
       * </pre>
       *
       * <code>required string file_offset_hash_md5 = 9;</code>
       * @param value The bytes for fileOffsetHashMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setFileOffsetHashMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileOffsetHashMd5_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private int fileOffsetHashChunkSize_ ;
      /**
       * <pre>
       * 文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       * @return Whether the fileOffsetHashChunkSize field is set.
       */
      @java.lang.Override
      public boolean hasFileOffsetHashChunkSize() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       * @return The fileOffsetHashChunkSize.
       */
      @java.lang.Override
      public int getFileOffsetHashChunkSize() {
        return fileOffsetHashChunkSize_;
      }
      /**
       * <pre>
       * 文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       * @param value The fileOffsetHashChunkSize to set.
       * @return This builder for chaining.
       */
      public Builder setFileOffsetHashChunkSize(int value) {

        fileOffsetHashChunkSize_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件偏移HASH位置	
       * </pre>
       *
       * <code>required uint32 file_offset_hash_chunk_size = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileOffsetHashChunkSize() {
        bitField0_ = (bitField0_ & ~0x00000200);
        fileOffsetHashChunkSize_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fileHashResult_ = "";
      /**
       * <pre>
       * HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       * @return Whether the fileHashResult field is set.
       */
      public boolean hasFileHashResult() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       * @return The fileHashResult.
       */
      public java.lang.String getFileHashResult() {
        java.lang.Object ref = fileHashResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileHashResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       * @return The bytes for fileHashResult.
       */
      public com.google.protobuf.ByteString
          getFileHashResultBytes() {
        java.lang.Object ref = fileHashResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileHashResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       * @param value The fileHashResult to set.
       * @return This builder for chaining.
       */
      public Builder setFileHashResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileHashResult_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileHashResult() {
        fileHashResult_ = getDefaultInstance().getFileHashResult();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * HASH检测结果	
       * </pre>
       *
       * <code>required string file_hash_result = 11;</code>
       * @param value The bytes for fileHashResult to set.
       * @return This builder for chaining.
       */
      public Builder setFileHashResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileHashResult_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object fileAvResult_ = "";
      /**
       * <pre>
       * AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       * @return Whether the fileAvResult field is set.
       */
      public boolean hasFileAvResult() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       * @return The fileAvResult.
       */
      public java.lang.String getFileAvResult() {
        java.lang.Object ref = fileAvResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileAvResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       * @return The bytes for fileAvResult.
       */
      public com.google.protobuf.ByteString
          getFileAvResultBytes() {
        java.lang.Object ref = fileAvResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileAvResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       * @param value The fileAvResult to set.
       * @return This builder for chaining.
       */
      public Builder setFileAvResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileAvResult_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileAvResult() {
        fileAvResult_ = getDefaultInstance().getFileAvResult();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * AV检测结果	
       * </pre>
       *
       * <code>required string file_av_result = 12;</code>
       * @param value The bytes for fileAvResult to set.
       * @return This builder for chaining.
       */
      public Builder setFileAvResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileAvResult_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object fileExAvName_ = "";
      /**
       * <pre>
       * 外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       * @return Whether the fileExAvName field is set.
       */
      public boolean hasFileExAvName() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       * @return The fileExAvName.
       */
      public java.lang.String getFileExAvName() {
        java.lang.Object ref = fileExAvName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileExAvName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       * @return The bytes for fileExAvName.
       */
      public com.google.protobuf.ByteString
          getFileExAvNameBytes() {
        java.lang.Object ref = fileExAvName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileExAvName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       * @param value The fileExAvName to set.
       * @return This builder for chaining.
       */
      public Builder setFileExAvName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileExAvName_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileExAvName() {
        fileExAvName_ = getDefaultInstance().getFileExAvName();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 外部AV名称	
       * </pre>
       *
       * <code>required string file_ex_av_name = 13;</code>
       * @param value The bytes for fileExAvName to set.
       * @return This builder for chaining.
       */
      public Builder setFileExAvNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileExAvName_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private java.lang.Object fileExAvResult_ = "";
      /**
       * <pre>
       * 外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       * @return Whether the fileExAvResult field is set.
       */
      public boolean hasFileExAvResult() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       * @return The fileExAvResult.
       */
      public java.lang.String getFileExAvResult() {
        java.lang.Object ref = fileExAvResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileExAvResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       * @return The bytes for fileExAvResult.
       */
      public com.google.protobuf.ByteString
          getFileExAvResultBytes() {
        java.lang.Object ref = fileExAvResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileExAvResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       * @param value The fileExAvResult to set.
       * @return This builder for chaining.
       */
      public Builder setFileExAvResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileExAvResult_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileExAvResult() {
        fileExAvResult_ = getDefaultInstance().getFileExAvResult();
        bitField0_ = (bitField0_ & ~0x00002000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 外部AV检测结果	
       * </pre>
       *
       * <code>required string file_ex_av_result = 14;</code>
       * @param value The bytes for fileExAvResult to set.
       * @return This builder for chaining.
       */
      public Builder setFileExAvResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileExAvResult_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }

      private java.lang.Object fileYaraRuleName_ = "";
      /**
       * <pre>
       * yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       * @return Whether the fileYaraRuleName field is set.
       */
      public boolean hasFileYaraRuleName() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       * @return The fileYaraRuleName.
       */
      public java.lang.String getFileYaraRuleName() {
        java.lang.Object ref = fileYaraRuleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileYaraRuleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       * @return The bytes for fileYaraRuleName.
       */
      public com.google.protobuf.ByteString
          getFileYaraRuleNameBytes() {
        java.lang.Object ref = fileYaraRuleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileYaraRuleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       * @param value The fileYaraRuleName to set.
       * @return This builder for chaining.
       */
      public Builder setFileYaraRuleName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileYaraRuleName_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileYaraRuleName() {
        fileYaraRuleName_ = getDefaultInstance().getFileYaraRuleName();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * yara规则名	
       * </pre>
       *
       * <code>required string file_yara_rule_name = 15;</code>
       * @param value The bytes for fileYaraRuleName to set.
       * @return This builder for chaining.
       */
      public Builder setFileYaraRuleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileYaraRuleName_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      private int fileYaraThreatLevel_ ;
      /**
       * <pre>
       * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       * @return Whether the fileYaraThreatLevel field is set.
       */
      @java.lang.Override
      public boolean hasFileYaraThreatLevel() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       * @return The fileYaraThreatLevel.
       */
      @java.lang.Override
      public int getFileYaraThreatLevel() {
        return fileYaraThreatLevel_;
      }
      /**
       * <pre>
       * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       * @param value The fileYaraThreatLevel to set.
       * @return This builder for chaining.
       */
      public Builder setFileYaraThreatLevel(int value) {

        fileYaraThreatLevel_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * yara规则威胁等级	1：正常，2：低危，3：中危，4：高危
       * </pre>
       *
       * <code>required uint32 file_yara_threat_level = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileYaraThreatLevel() {
        bitField0_ = (bitField0_ & ~0x00008000);
        fileYaraThreatLevel_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object fileDde_ = "";
      /**
       * <pre>
       * DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       * @return Whether the fileDde field is set.
       */
      public boolean hasFileDde() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       * @return The fileDde.
       */
      public java.lang.String getFileDde() {
        java.lang.Object ref = fileDde_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileDde_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       * @return The bytes for fileDde.
       */
      public com.google.protobuf.ByteString
          getFileDdeBytes() {
        java.lang.Object ref = fileDde_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileDde_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       * @param value The fileDde to set.
       * @return This builder for chaining.
       */
      public Builder setFileDde(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileDde_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileDde() {
        fileDde_ = getDefaultInstance().getFileDde();
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DDE内容	
       * </pre>
       *
       * <code>optional string file_dde = 17;</code>
       * @param value The bytes for fileDde to set.
       * @return This builder for chaining.
       */
      public Builder setFileDdeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileDde_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }

      private java.lang.Object filePlatform_ = "";
      /**
       * <pre>
       * 沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       * @return Whether the filePlatform field is set.
       */
      public boolean hasFilePlatform() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       * @return The filePlatform.
       */
      public java.lang.String getFilePlatform() {
        java.lang.Object ref = filePlatform_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            filePlatform_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       * @return The bytes for filePlatform.
       */
      public com.google.protobuf.ByteString
          getFilePlatformBytes() {
        java.lang.Object ref = filePlatform_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          filePlatform_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       * @param value The filePlatform to set.
       * @return This builder for chaining.
       */
      public Builder setFilePlatform(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        filePlatform_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearFilePlatform() {
        filePlatform_ = getDefaultInstance().getFilePlatform();
        bitField0_ = (bitField0_ & ~0x00020000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 沙箱系统环境	
       * </pre>
       *
       * <code>required string file_platform = 18;</code>
       * @param value The bytes for filePlatform to set.
       * @return This builder for chaining.
       */
      public Builder setFilePlatformBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        filePlatform_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }

      private java.lang.Object fileMlDetectModel_ = "";
      /**
       * <pre>
       * 威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       * @return Whether the fileMlDetectModel field is set.
       */
      public boolean hasFileMlDetectModel() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       * @return The fileMlDetectModel.
       */
      public java.lang.String getFileMlDetectModel() {
        java.lang.Object ref = fileMlDetectModel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMlDetectModel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       * @return The bytes for fileMlDetectModel.
       */
      public com.google.protobuf.ByteString
          getFileMlDetectModelBytes() {
        java.lang.Object ref = fileMlDetectModel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMlDetectModel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       * @param value The fileMlDetectModel to set.
       * @return This builder for chaining.
       */
      public Builder setFileMlDetectModel(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileMlDetectModel_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileMlDetectModel() {
        fileMlDetectModel_ = getDefaultInstance().getFileMlDetectModel();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁检测模型名称	
       * </pre>
       *
       * <code>required string file_ml_detect_model = 19;</code>
       * @param value The bytes for fileMlDetectModel to set.
       * @return This builder for chaining.
       */
      public Builder setFileMlDetectModelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileMlDetectModel_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }

      private java.lang.Object fileMlPrecision_ = "";
      /**
       * <pre>
       * 威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       * @return Whether the fileMlPrecision field is set.
       */
      public boolean hasFileMlPrecision() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       * @return The fileMlPrecision.
       */
      public java.lang.String getFileMlPrecision() {
        java.lang.Object ref = fileMlPrecision_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMlPrecision_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       * @return The bytes for fileMlPrecision.
       */
      public com.google.protobuf.ByteString
          getFileMlPrecisionBytes() {
        java.lang.Object ref = fileMlPrecision_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMlPrecision_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       * @param value The fileMlPrecision to set.
       * @return This builder for chaining.
       */
      public Builder setFileMlPrecision(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileMlPrecision_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileMlPrecision() {
        fileMlPrecision_ = getDefaultInstance().getFileMlPrecision();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁检测模型置信度	百分数字符串
       * </pre>
       *
       * <code>required string file_ml_precision = 20;</code>
       * @param value The bytes for fileMlPrecision to set.
       * @return This builder for chaining.
       */
      public Builder setFileMlPrecisionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileMlPrecision_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private java.lang.Object fileMlClassModel_ = "";
      /**
       * <pre>
       * 威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       * @return Whether the fileMlClassModel field is set.
       */
      public boolean hasFileMlClassModel() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       * @return The fileMlClassModel.
       */
      public java.lang.String getFileMlClassModel() {
        java.lang.Object ref = fileMlClassModel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileMlClassModel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       * @return The bytes for fileMlClassModel.
       */
      public com.google.protobuf.ByteString
          getFileMlClassModelBytes() {
        java.lang.Object ref = fileMlClassModel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileMlClassModel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       * @param value The fileMlClassModel to set.
       * @return This builder for chaining.
       */
      public Builder setFileMlClassModel(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileMlClassModel_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileMlClassModel() {
        fileMlClassModel_ = getDefaultInstance().getFileMlClassModel();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁分类模型名称	
       * </pre>
       *
       * <code>required string file_ml_class_model = 21;</code>
       * @param value The bytes for fileMlClassModel to set.
       * @return This builder for chaining.
       */
      public Builder setFileMlClassModelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileMlClassModel_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }

      private double fileMlPrediction_ ;
      /**
       * <pre>
       * 威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       * @return Whether the fileMlPrediction field is set.
       */
      @java.lang.Override
      public boolean hasFileMlPrediction() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       * @return The fileMlPrediction.
       */
      @java.lang.Override
      public double getFileMlPrediction() {
        return fileMlPrediction_;
      }
      /**
       * <pre>
       * 威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       * @param value The fileMlPrediction to set.
       * @return This builder for chaining.
       */
      public Builder setFileMlPrediction(double value) {

        fileMlPrediction_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁分类模型置信度	百分数小数
       * </pre>
       *
       * <code>required double file_ml_prediction = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileMlPrediction() {
        bitField0_ = (bitField0_ & ~0x00200000);
        fileMlPrediction_ = 0D;
        onChanged();
        return this;
      }

      private java.lang.Object fileIocIp_ = "";
      /**
       * <pre>
       * IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       * @return Whether the fileIocIp field is set.
       */
      public boolean hasFileIocIp() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       * @return The fileIocIp.
       */
      public java.lang.String getFileIocIp() {
        java.lang.Object ref = fileIocIp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileIocIp_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       * @return The bytes for fileIocIp.
       */
      public com.google.protobuf.ByteString
          getFileIocIpBytes() {
        java.lang.Object ref = fileIocIp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileIocIp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       * @param value The fileIocIp to set.
       * @return This builder for chaining.
       */
      public Builder setFileIocIp(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileIocIp_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileIocIp() {
        fileIocIp_ = getDefaultInstance().getFileIocIp();
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IP IOC	
       * </pre>
       *
       * <code>optional string file_ioc_ip = 23;</code>
       * @param value The bytes for fileIocIp to set.
       * @return This builder for chaining.
       */
      public Builder setFileIocIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileIocIp_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }

      private java.lang.Object fileIocDns_ = "";
      /**
       * <pre>
       * DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       * @return Whether the fileIocDns field is set.
       */
      public boolean hasFileIocDns() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       * @return The fileIocDns.
       */
      public java.lang.String getFileIocDns() {
        java.lang.Object ref = fileIocDns_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileIocDns_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       * @return The bytes for fileIocDns.
       */
      public com.google.protobuf.ByteString
          getFileIocDnsBytes() {
        java.lang.Object ref = fileIocDns_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileIocDns_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       * @param value The fileIocDns to set.
       * @return This builder for chaining.
       */
      public Builder setFileIocDns(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileIocDns_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileIocDns() {
        fileIocDns_ = getDefaultInstance().getFileIocDns();
        bitField0_ = (bitField0_ & ~0x00800000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DNS IOC	
       * </pre>
       *
       * <code>optional string file_ioc_dns = 24;</code>
       * @param value The bytes for fileIocDns to set.
       * @return This builder for chaining.
       */
      public Builder setFileIocDnsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileIocDns_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }

      private java.lang.Object fileIocUrl_ = "";
      /**
       * <pre>
       * URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       * @return Whether the fileIocUrl field is set.
       */
      public boolean hasFileIocUrl() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       * @return The fileIocUrl.
       */
      public java.lang.String getFileIocUrl() {
        java.lang.Object ref = fileIocUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileIocUrl_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       * @return The bytes for fileIocUrl.
       */
      public com.google.protobuf.ByteString
          getFileIocUrlBytes() {
        java.lang.Object ref = fileIocUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileIocUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       * @param value The fileIocUrl to set.
       * @return This builder for chaining.
       */
      public Builder setFileIocUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileIocUrl_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileIocUrl() {
        fileIocUrl_ = getDefaultInstance().getFileIocUrl();
        bitField0_ = (bitField0_ & ~0x01000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * URL IOC	
       * </pre>
       *
       * <code>optional string file_ioc_url = 25;</code>
       * @param value The bytes for fileIocUrl to set.
       * @return This builder for chaining.
       */
      public Builder setFileIocUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileIocUrl_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }

      private java.lang.Object filePath_ = "";
      /**
       * <pre>
       * 文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       * @return Whether the filePath field is set.
       */
      public boolean hasFilePath() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <pre>
       * 文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       * @return The filePath.
       */
      public java.lang.String getFilePath() {
        java.lang.Object ref = filePath_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            filePath_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       * @return The bytes for filePath.
       */
      public com.google.protobuf.ByteString
          getFilePathBytes() {
        java.lang.Object ref = filePath_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          filePath_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       * @param value The filePath to set.
       * @return This builder for chaining.
       */
      public Builder setFilePath(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        filePath_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearFilePath() {
        filePath_ = getDefaultInstance().getFilePath();
        bitField0_ = (bitField0_ & ~0x02000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件路径	
       * </pre>
       *
       * <code>required string file_path = 26;</code>
       * @param value The bytes for filePath to set.
       * @return This builder for chaining.
       */
      public Builder setFilePathBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        filePath_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }

      private java.lang.Object sandboxReportUrl_ = "";
      /**
       * <pre>
       * 沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       * @return Whether the sandboxReportUrl field is set.
       */
      public boolean hasSandboxReportUrl() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <pre>
       * 沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       * @return The sandboxReportUrl.
       */
      public java.lang.String getSandboxReportUrl() {
        java.lang.Object ref = sandboxReportUrl_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sandboxReportUrl_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       * @return The bytes for sandboxReportUrl.
       */
      public com.google.protobuf.ByteString
          getSandboxReportUrlBytes() {
        java.lang.Object ref = sandboxReportUrl_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sandboxReportUrl_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       * @param value The sandboxReportUrl to set.
       * @return This builder for chaining.
       */
      public Builder setSandboxReportUrl(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        sandboxReportUrl_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearSandboxReportUrl() {
        sandboxReportUrl_ = getDefaultInstance().getSandboxReportUrl();
        bitField0_ = (bitField0_ & ~0x04000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 沙箱报告路径
       * </pre>
       *
       * <code>required string sandbox_report_url = 27;</code>
       * @param value The bytes for sandboxReportUrl to set.
       * @return This builder for chaining.
       */
      public Builder setSandboxReportUrlBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sandboxReportUrl_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }

      private java.lang.Object fileName_ = "";
      /**
       * <pre>
       * 文件名
       * </pre>
       *
       * <code>required string file_name = 28;</code>
       * @return Whether the fileName field is set.
       */
      public boolean hasFileName() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <pre>
       * 文件名
       * </pre>
       *
       * <code>required string file_name = 28;</code>
       * @return The fileName.
       */
      public java.lang.String getFileName() {
        java.lang.Object ref = fileName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 文件名
       * </pre>
       *
       * <code>required string file_name = 28;</code>
       * @return The bytes for fileName.
       */
      public com.google.protobuf.ByteString
          getFileNameBytes() {
        java.lang.Object ref = fileName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 文件名
       * </pre>
       *
       * <code>required string file_name = 28;</code>
       * @param value The fileName to set.
       * @return This builder for chaining.
       */
      public Builder setFileName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileName_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件名
       * </pre>
       *
       * <code>required string file_name = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileName() {
        fileName_ = getDefaultInstance().getFileName();
        bitField0_ = (bitField0_ & ~0x08000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件名
       * </pre>
       *
       * <code>required string file_name = 28;</code>
       * @param value The bytes for fileName to set.
       * @return This builder for chaining.
       */
      public Builder setFileNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileName_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }

      private java.lang.Object fileEmailSender_ = "";
      /**
       * <pre>
       * 发件人
       * </pre>
       *
       * <code>optional string file_email_sender = 29;</code>
       * @return Whether the fileEmailSender field is set.
       */
      public boolean hasFileEmailSender() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <pre>
       * 发件人
       * </pre>
       *
       * <code>optional string file_email_sender = 29;</code>
       * @return The fileEmailSender.
       */
      public java.lang.String getFileEmailSender() {
        java.lang.Object ref = fileEmailSender_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileEmailSender_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 发件人
       * </pre>
       *
       * <code>optional string file_email_sender = 29;</code>
       * @return The bytes for fileEmailSender.
       */
      public com.google.protobuf.ByteString
          getFileEmailSenderBytes() {
        java.lang.Object ref = fileEmailSender_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileEmailSender_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 发件人
       * </pre>
       *
       * <code>optional string file_email_sender = 29;</code>
       * @param value The fileEmailSender to set.
       * @return This builder for chaining.
       */
      public Builder setFileEmailSender(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileEmailSender_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发件人
       * </pre>
       *
       * <code>optional string file_email_sender = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileEmailSender() {
        fileEmailSender_ = getDefaultInstance().getFileEmailSender();
        bitField0_ = (bitField0_ & ~0x10000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发件人
       * </pre>
       *
       * <code>optional string file_email_sender = 29;</code>
       * @param value The bytes for fileEmailSender to set.
       * @return This builder for chaining.
       */
      public Builder setFileEmailSenderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileEmailSender_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }

      private java.lang.Object fileEmailReceiver_ = "";
      /**
       * <pre>
       * 收件人
       * </pre>
       *
       * <code>optional string file_email_receiver = 30;</code>
       * @return Whether the fileEmailReceiver field is set.
       */
      public boolean hasFileEmailReceiver() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <pre>
       * 收件人
       * </pre>
       *
       * <code>optional string file_email_receiver = 30;</code>
       * @return The fileEmailReceiver.
       */
      public java.lang.String getFileEmailReceiver() {
        java.lang.Object ref = fileEmailReceiver_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileEmailReceiver_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 收件人
       * </pre>
       *
       * <code>optional string file_email_receiver = 30;</code>
       * @return The bytes for fileEmailReceiver.
       */
      public com.google.protobuf.ByteString
          getFileEmailReceiverBytes() {
        java.lang.Object ref = fileEmailReceiver_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileEmailReceiver_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 收件人
       * </pre>
       *
       * <code>optional string file_email_receiver = 30;</code>
       * @param value The fileEmailReceiver to set.
       * @return This builder for chaining.
       */
      public Builder setFileEmailReceiver(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileEmailReceiver_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收件人
       * </pre>
       *
       * <code>optional string file_email_receiver = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileEmailReceiver() {
        fileEmailReceiver_ = getDefaultInstance().getFileEmailReceiver();
        bitField0_ = (bitField0_ & ~0x20000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收件人
       * </pre>
       *
       * <code>optional string file_email_receiver = 30;</code>
       * @param value The bytes for fileEmailReceiver to set.
       * @return This builder for chaining.
       */
      public Builder setFileEmailReceiverBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileEmailReceiver_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }

      private java.lang.Object fileEmailSubject_ = "";
      /**
       * <pre>
       * 邮件主题
       * </pre>
       *
       * <code>optional string file_email_subject = 31;</code>
       * @return Whether the fileEmailSubject field is set.
       */
      public boolean hasFileEmailSubject() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <pre>
       * 邮件主题
       * </pre>
       *
       * <code>optional string file_email_subject = 31;</code>
       * @return The fileEmailSubject.
       */
      public java.lang.String getFileEmailSubject() {
        java.lang.Object ref = fileEmailSubject_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            fileEmailSubject_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 邮件主题
       * </pre>
       *
       * <code>optional string file_email_subject = 31;</code>
       * @return The bytes for fileEmailSubject.
       */
      public com.google.protobuf.ByteString
          getFileEmailSubjectBytes() {
        java.lang.Object ref = fileEmailSubject_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          fileEmailSubject_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 邮件主题
       * </pre>
       *
       * <code>optional string file_email_subject = 31;</code>
       * @param value The fileEmailSubject to set.
       * @return This builder for chaining.
       */
      public Builder setFileEmailSubject(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        fileEmailSubject_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件主题
       * </pre>
       *
       * <code>optional string file_email_subject = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileEmailSubject() {
        fileEmailSubject_ = getDefaultInstance().getFileEmailSubject();
        bitField0_ = (bitField0_ & ~0x40000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件主题
       * </pre>
       *
       * <code>optional string file_email_subject = 31;</code>
       * @param value The bytes for fileEmailSubject to set.
       * @return This builder for chaining.
       */
      public Builder setFileEmailSubjectBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fileEmailSubject_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:FILE_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:FILE_ALERT_INFO)
    private static final FileAlertInfo.FILE_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new FileAlertInfo.FILE_ALERT_INFO();
    }

    public static FileAlertInfo.FILE_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FILE_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<FILE_ALERT_INFO>() {
      @java.lang.Override
      public FILE_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<FILE_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FILE_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public FileAlertInfo.FILE_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_FILE_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_FILE_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025FILE_ALERT_INFO.proto\"\207\006\n\017FILE_ALERT_I" +
      "NFO\022\020\n\010file_md5\030\001 \002(\t\022\021\n\tfile_sha1\030\002 \002(\t" +
      "\022\023\n\013file_sha256\030\003 \002(\t\022\023\n\013file_sha512\030\004 \002" +
      "(\t\022\022\n\nfile_crc32\030\005 \002(\t\022\023\n\013file_ssdeep\030\006 " +
      "\002(\t\022\021\n\tfile_size\030\007 \002(\r\022\021\n\tfile_type\030\010 \002(" +
      "\t\022\034\n\024file_offset_hash_md5\030\t \002(\t\022#\n\033file_" +
      "offset_hash_chunk_size\030\n \002(\r\022\030\n\020file_has" +
      "h_result\030\013 \002(\t\022\026\n\016file_av_result\030\014 \002(\t\022\027" +
      "\n\017file_ex_av_name\030\r \002(\t\022\031\n\021file_ex_av_re" +
      "sult\030\016 \002(\t\022\033\n\023file_yara_rule_name\030\017 \002(\t\022" +
      "\036\n\026file_yara_threat_level\030\020 \002(\r\022\020\n\010file_" +
      "dde\030\021 \001(\t\022\025\n\rfile_platform\030\022 \002(\t\022\034\n\024file" +
      "_ml_detect_model\030\023 \002(\t\022\031\n\021file_ml_precis" +
      "ion\030\024 \002(\t\022\033\n\023file_ml_class_model\030\025 \002(\t\022\032" +
      "\n\022file_ml_prediction\030\026 \002(\001\022\023\n\013file_ioc_i" +
      "p\030\027 \001(\t\022\024\n\014file_ioc_dns\030\030 \001(\t\022\024\n\014file_io" +
      "c_url\030\031 \001(\t\022\021\n\tfile_path\030\032 \002(\t\022\032\n\022sandbo" +
      "x_report_url\030\033 \002(\t\022\021\n\tfile_name\030\034 \002(\t\022\031\n" +
      "\021file_email_sender\030\035 \001(\t\022\033\n\023file_email_r" +
      "eceiver\030\036 \001(\t\022\032\n\022file_email_subject\030\037 \001(" +
      "\tB\017B\rFileAlertInfo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_FILE_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_FILE_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_FILE_ALERT_INFO_descriptor,
        new java.lang.String[] { "FileMd5", "FileSha1", "FileSha256", "FileSha512", "FileCrc32", "FileSsdeep", "FileSize", "FileType", "FileOffsetHashMd5", "FileOffsetHashChunkSize", "FileHashResult", "FileAvResult", "FileExAvName", "FileExAvResult", "FileYaraRuleName", "FileYaraThreatLevel", "FileDde", "FilePlatform", "FileMlDetectModel", "FileMlPrecision", "FileMlClassModel", "FileMlPrediction", "FileIocIp", "FileIocDns", "FileIocUrl", "FilePath", "SandboxReportUrl", "FileName", "FileEmailSender", "FileEmailReceiver", "FileEmailSubject", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
