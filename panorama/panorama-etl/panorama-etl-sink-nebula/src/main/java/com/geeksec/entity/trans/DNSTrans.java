package com.geeksec.entity.trans;

import lombok.Data;

import java.util.List;

@Data
public class DNSTrans {
    // 查询的域名
    private String query;

    // 回答类型
    private List<String>  answerTypes;

    // CNAME 记录
    private List<String> cname;

    // CNAME 记录数量
    private Integer cnameCnt;

    // 回答记录数量
    private Integer answerCnt;

    // 授权记录数量
    private Integer authCnt;

    // 附加资源记录
    private Integer addRrs;

    // 附加记录数量
    private Integer addCnt;

    // 附加回答类型
    private Integer addAnsType;

    // 附加回答资源记录
    private String addAnsRes;

    // 请求类型
    private int queType;

    // DNS 标志
    private Integer flags;

    // IPv6 地址
    private List<String> ipv6;

    // IP 地址 (A 记录)
    private List<Integer> aip;

    // IP 地址 (A 记录) 数量
    private Integer aipCnt;

    // IP 地址 (A 记录) 的 ASN
    private List<String> aipAsn;

    // IP 地址 (A 记录) 的国家
    private List<String> aipCountry;

    // NS 主机名
    private List<String> nsHost;

    // NS 主机名数量
    private Integer nsHostCnt;

    // NS IP 地址
    private List<Integer> nsIp;

    // NS IP 地址数量
    private Integer nsIpCnt;

    // NS IP 地址的 ASN
    private List<String> nsAsn;

    // NS IP 地址的国家
    private List<String> nsCountry;

    // MX 主机名
    private List<String> mxHost;

    // MX 主机名数量
    private Integer mxHostCnt;

    // MX IP 地址
    private List<String> mxIp;

    // MX IP 地址数量
    private Integer mxIpCnt;

    // MX IP 地址的 ASN
    private List<String> mxAsn;

    // MX IP 地址的国家
    private List<String> mxCountry;
}
