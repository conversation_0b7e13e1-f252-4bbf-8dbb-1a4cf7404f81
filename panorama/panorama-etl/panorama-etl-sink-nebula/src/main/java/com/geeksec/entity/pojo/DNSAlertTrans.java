package com.geeksec.entity.pojo;

import com.geeksec.common.utils.DnsAnalysisUtil;
import com.geeksec.entity.po.DnsParseResult;
import com.geeksec.config.util.MD5;
import com.geeksec.entity.trans.DNSTrans;
import com.geeksec.entity.trans.IPTrans;
import lombok.Data;
import org.apache.flink.types.Row;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Data
public class DNSAlertTrans {

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;
    private String dnsServerAddr;

    // DNS请求返回数据有多个
    private List<DNSTrans> dns;

    private String aptName;


    private List<Row> getDomainListRow() {
        Set<String> domainSet = new LinkedHashSet<>();

        for (DNSTrans dnsTrans : dns) {
            // Query 询问域名
            domainSet.add(dnsTrans.getQuery());

            // CNAME 别名
            if (dnsTrans.getCnameCnt() > 0) {
                List<String> cNameList = dnsTrans.getCname();
                domainSet.addAll(cNameList);
            }

            // NS 服务器
            List<String> nsHost = dnsTrans.getNsHost();
            if (!nsHost.isEmpty()) {
                domainSet.addAll(nsHost);
            }
        }

        return domainSet.stream()
                .map(domainAddr -> {
                    Row domainRow = new Row(3);
                    domainRow.setField(0, "TAG_DOMAIN");
                    domainRow.setField(1, MD5.getMd5(domainAddr));
                    domainRow.setField(2, domainAddr);
                    return domainRow;
                })
                .collect(Collectors.toList());
    }


    private List<Row> getAllDnsQueryRelationRows() {
        List<Row> rows = new ArrayList<>();

        List<DnsParseResult> dnsParseResults = DnsAnalysisUtil.analyzeDnsTransactions(dns,dnsServerAddr);

        // 循环多个DNS请求数据
        for (DnsParseResult dnsParseResult : dnsParseResults) {

            String domain = dnsParseResult.getDomain();
            String domainMd5  = MD5.getMd5(domain);

            Row parseToRow = new Row(6);
            parseToRow.setField(0, "EDGE_parse_to");
            parseToRow.setField(1, domainMd5);
            parseToRow.setField(2, dnsParseResult.getServerIp());
            parseToRow.setField(3, 0);
            if (dnsParseResult.isFinalParse()) {
                parseToRow.setField(4, true);
            } else {
                parseToRow.setField(4, false);
            }
            parseToRow.setField(5, dnsParseResult.getDnsServer());
            rows.add(parseToRow);

            Row c2srow = new Row(6);
            c2srow.setField(0, "EDGE_client_query_dns_server");
            c2srow.setField(1, vipAddr);
            c2srow.setField(2, dnsServerAddr);
            c2srow.setField(3, 0);
            c2srow.setField(4, dnsParseResult.getDnsType());
            c2srow.setField(5, dnsParseResult.getAnswerType());
            rows.add(c2srow);

            Row s2drow = new Row(6);
            s2drow.setField(0, "EDGE_dns_server_resolves_domain");
            s2drow.setField(1, dnsServerAddr);
            s2drow.setField(2, domainMd5);
            s2drow.setField(3, 0);
            s2drow.setField(4, dnsParseResult.getDnsType());
            s2drow.setField(5, dnsParseResult.getAnswerType());
            rows.add(s2drow);

            // client_query_domain 客户端请求域名解析
            Row c2drow = new Row(6);
            c2drow.setField(0, "EDGE_client_query_domain");
            c2drow.setField(1, vipAddr);
            c2drow.setField(2, domainMd5);
            c2drow.setField(3, 0);
            c2drow.setField(4, dnsParseResult.getDnsType());
            c2drow.setField(5, dnsParseResult.getAnswerType());
            rows.add(c2drow);

            // domain_belong_to_apt edge
            if(aptName != null && !aptName.isEmpty()){
                Row row = new Row(4);
                row.setField(0, "EDGE_domain_belong_to_apt");
                row.setField(1, domainMd5);
                row.setField(2, aptName);
                row.setField(3, 0);
                rows.add(row);
            }
        }
        return rows;
    }

    public List<Row> getAllRows() {

        List<Row> rows = new ArrayList<>(getDomainListRow());

        List<Row> dnsQueryRows = getAllDnsQueryRelationRows();
        rows.addAll(dnsQueryRows);

        return rows;
    }
}
