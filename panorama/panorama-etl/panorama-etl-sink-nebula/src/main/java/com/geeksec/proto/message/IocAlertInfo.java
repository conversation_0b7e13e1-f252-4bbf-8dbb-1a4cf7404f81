package com.geeksec.proto.message;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: IOC_ALERT_INFO.proto
// Protobuf Java Version: 4.29.4

public final class IocAlertInfo {
  private IocAlertInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      IocAlertInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IOC_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IOC_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     * @return Whether the iocId field is set.
     */
    boolean hasIocId();
    /**
     * <pre>
     * IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     * @return The iocId.
     */
    java.lang.String getIocId();
    /**
     * <pre>
     * IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     * @return The bytes for iocId.
     */
    com.google.protobuf.ByteString
        getIocIdBytes();

    /**
     * <pre>
     * IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     * @return Whether the iocValue field is set.
     */
    boolean hasIocValue();
    /**
     * <pre>
     * IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     * @return The iocValue.
     */
    java.lang.String getIocValue();
    /**
     * <pre>
     * IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     * @return The bytes for iocValue.
     */
    com.google.protobuf.ByteString
        getIocValueBytes();

    /**
     * <pre>
     * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     * @return Whether the iocCategory field is set.
     */
    boolean hasIocCategory();
    /**
     * <pre>
     * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     * @return The iocCategory.
     */
    java.lang.String getIocCategory();
    /**
     * <pre>
     * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     * @return The bytes for iocCategory.
     */
    com.google.protobuf.ByteString
        getIocCategoryBytes();

    /**
     * <pre>
     * IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     * @return Whether the iocPublicDate field is set.
     */
    boolean hasIocPublicDate();
    /**
     * <pre>
     * IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     * @return The iocPublicDate.
     */
    long getIocPublicDate();

    /**
     * <pre>
     * IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     * @return Whether the iocAlertName field is set.
     */
    boolean hasIocAlertName();
    /**
     * <pre>
     * IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     * @return The iocAlertName.
     */
    java.lang.String getIocAlertName();
    /**
     * <pre>
     * IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     * @return The bytes for iocAlertName.
     */
    com.google.protobuf.ByteString
        getIocAlertNameBytes();

    /**
     * <pre>
     * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     * @return Whether the iocCurrentStatus field is set.
     */
    boolean hasIocCurrentStatus();
    /**
     * <pre>
     * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     * @return The iocCurrentStatus.
     */
    java.lang.String getIocCurrentStatus();
    /**
     * <pre>
     * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     * @return The bytes for iocCurrentStatus.
     */
    com.google.protobuf.ByteString
        getIocCurrentStatusBytes();

    /**
     * <pre>
     * IOC热点状态	True/False
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     * @return Whether the iocHot field is set.
     */
    boolean hasIocHot();
    /**
     * <pre>
     * IOC热点状态	True/False
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     * @return The iocHot.
     */
    boolean getIocHot();

    /**
     * <pre>
     * 首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     * @return Whether the iocFirstSeen field is set.
     */
    boolean hasIocFirstSeen();
    /**
     * <pre>
     * 首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     * @return The iocFirstSeen.
     */
    java.lang.String getIocFirstSeen();
    /**
     * <pre>
     * 首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     * @return The bytes for iocFirstSeen.
     */
    com.google.protobuf.ByteString
        getIocFirstSeenBytes();

    /**
     * <pre>
     * 最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     * @return Whether the iocLastDetection field is set.
     */
    boolean hasIocLastDetection();
    /**
     * <pre>
     * 最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     * @return The iocLastDetection.
     */
    java.lang.String getIocLastDetection();
    /**
     * <pre>
     * 最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     * @return The bytes for iocLastDetection.
     */
    com.google.protobuf.ByteString
        getIocLastDetectionBytes();

    /**
     * <pre>
     * 参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     * @return Whether the iocRefer field is set.
     */
    boolean hasIocRefer();
    /**
     * <pre>
     * 参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     * @return The iocRefer.
     */
    java.lang.String getIocRefer();
    /**
     * <pre>
     * 参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     * @return The bytes for iocRefer.
     */
    com.google.protobuf.ByteString
        getIocReferBytes();

    /**
     * <pre>
     * 报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     * @return Whether the iocReportData field is set.
     */
    boolean hasIocReportData();
    /**
     * <pre>
     * 报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     * @return The iocReportData.
     */
    java.lang.String getIocReportData();
    /**
     * <pre>
     * 报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     * @return The bytes for iocReportData.
     */
    com.google.protobuf.ByteString
        getIocReportDataBytes();

    /**
     * <pre>
     * 报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     * @return Whether the iocReportVendor field is set.
     */
    boolean hasIocReportVendor();
    /**
     * <pre>
     * 报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     * @return The iocReportVendor.
     */
    java.lang.String getIocReportVendor();
    /**
     * <pre>
     * 报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     * @return The bytes for iocReportVendor.
     */
    com.google.protobuf.ByteString
        getIocReportVendorBytes();

    /**
     * <pre>
     * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     * @return Whether the iocType field is set.
     */
    boolean hasIocType();
    /**
     * <pre>
     * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     * @return The iocType.
     */
    java.lang.String getIocType();
    /**
     * <pre>
     * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     * @return The bytes for iocType.
     */
    com.google.protobuf.ByteString
        getIocTypeBytes();

    /**
     * <pre>
     * 定向攻击标识	True/False
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     * @return Whether the iocTargeted field is set.
     */
    boolean hasIocTargeted();
    /**
     * <pre>
     * 定向攻击标识	True/False
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     * @return The iocTargeted.
     */
    boolean getIocTargeted();

    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     * @return Whether the iocMaliciousFamily field is set.
     */
    boolean hasIocMaliciousFamily();
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     * @return The iocMaliciousFamily.
     */
    java.lang.String getIocMaliciousFamily();
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     * @return The bytes for iocMaliciousFamily.
     */
    com.google.protobuf.ByteString
        getIocMaliciousFamilyBytes();

    /**
     * <pre>
     * APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     * @return Whether the iocAptCampaign field is set.
     */
    boolean hasIocAptCampaign();
    /**
     * <pre>
     * APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     * @return The iocAptCampaign.
     */
    java.lang.String getIocAptCampaign();
    /**
     * <pre>
     * APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     * @return The bytes for iocAptCampaign.
     */
    com.google.protobuf.ByteString
        getIocAptCampaignBytes();

    /**
     * <pre>
     * APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     * @return Whether the iocAptAlias field is set.
     */
    boolean hasIocAptAlias();
    /**
     * <pre>
     * APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     * @return The iocAptAlias.
     */
    java.lang.String getIocAptAlias();
    /**
     * <pre>
     * APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     * @return The bytes for iocAptAlias.
     */
    com.google.protobuf.ByteString
        getIocAptAliasBytes();

    /**
     * <pre>
     * APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     * @return Whether the iocAptCountry field is set.
     */
    boolean hasIocAptCountry();
    /**
     * <pre>
     * APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     * @return The iocAptCountry.
     */
    java.lang.String getIocAptCountry();
    /**
     * <pre>
     * APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     * @return The bytes for iocAptCountry.
     */
    com.google.protobuf.ByteString
        getIocAptCountryBytes();

    /**
     * <pre>
     * APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     * @return Whether the iocAptMission field is set.
     */
    boolean hasIocAptMission();
    /**
     * <pre>
     * APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     * @return The iocAptMission.
     */
    java.lang.String getIocAptMission();
    /**
     * <pre>
     * APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     * @return The bytes for iocAptMission.
     */
    com.google.protobuf.ByteString
        getIocAptMissionBytes();

    /**
     * <pre>
     * 远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     * @return Whether the iocRat field is set.
     */
    boolean hasIocRat();
    /**
     * <pre>
     * 远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     * @return The iocRat.
     */
    java.lang.String getIocRat();
    /**
     * <pre>
     * 远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     * @return The bytes for iocRat.
     */
    com.google.protobuf.ByteString
        getIocRatBytes();

    /**
     * <pre>
     * 攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     * @return Whether the iocAttackMethod field is set.
     */
    boolean hasIocAttackMethod();
    /**
     * <pre>
     * 攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     * @return The iocAttackMethod.
     */
    java.lang.String getIocAttackMethod();
    /**
     * <pre>
     * 攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     * @return The bytes for iocAttackMethod.
     */
    com.google.protobuf.ByteString
        getIocAttackMethodBytes();

    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     * @return Whether the iocVul field is set.
     */
    boolean hasIocVul();
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     * @return The iocVul.
     */
    java.lang.String getIocVul();
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     * @return The bytes for iocVul.
     */
    com.google.protobuf.ByteString
        getIocVulBytes();

    /**
     * <pre>
     * 影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     * @return Whether the iocAffectedSector field is set.
     */
    boolean hasIocAffectedSector();
    /**
     * <pre>
     * 影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     * @return The iocAffectedSector.
     */
    java.lang.String getIocAffectedSector();
    /**
     * <pre>
     * 影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     * @return The bytes for iocAffectedSector.
     */
    com.google.protobuf.ByteString
        getIocAffectedSectorBytes();

    /**
     * <pre>
     * 影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     * @return Whether the iocAffectedProduct field is set.
     */
    boolean hasIocAffectedProduct();
    /**
     * <pre>
     * 影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     * @return The iocAffectedProduct.
     */
    java.lang.String getIocAffectedProduct();
    /**
     * <pre>
     * 影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     * @return The bytes for iocAffectedProduct.
     */
    com.google.protobuf.ByteString
        getIocAffectedProductBytes();

    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     * @return Whether the iocDetailInfo field is set.
     */
    boolean hasIocDetailInfo();
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     * @return The iocDetailInfo.
     */
    java.lang.String getIocDetailInfo();
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     * @return The bytes for iocDetailInfo.
     */
    com.google.protobuf.ByteString
        getIocDetailInfoBytes();
  }
  /**
   * Protobuf type {@code IOC_ALERT_INFO}
   */
  public static final class IOC_ALERT_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:IOC_ALERT_INFO)
      IOC_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        IOC_ALERT_INFO.class.getName());
    }
    // Use IOC_ALERT_INFO.newBuilder() to construct.
    private IOC_ALERT_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private IOC_ALERT_INFO() {
      iocId_ = "";
      iocValue_ = "";
      iocCategory_ = "";
      iocAlertName_ = "";
      iocCurrentStatus_ = "";
      iocFirstSeen_ = "";
      iocLastDetection_ = "";
      iocRefer_ = "";
      iocReportData_ = "";
      iocReportVendor_ = "";
      iocType_ = "";
      iocMaliciousFamily_ = "";
      iocAptCampaign_ = "";
      iocAptAlias_ = "";
      iocAptCountry_ = "";
      iocAptMission_ = "";
      iocRat_ = "";
      iocAttackMethod_ = "";
      iocVul_ = "";
      iocAffectedSector_ = "";
      iocAffectedProduct_ = "";
      iocDetailInfo_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IocAlertInfo.internal_static_IOC_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IocAlertInfo.internal_static_IOC_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IocAlertInfo.IOC_ALERT_INFO.class, IocAlertInfo.IOC_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int IOC_ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocId_ = "";
    /**
     * <pre>
     * IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     * @return Whether the iocId field is set.
     */
    @java.lang.Override
    public boolean hasIocId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     * @return The iocId.
     */
    @java.lang.Override
    public java.lang.String getIocId() {
      java.lang.Object ref = iocId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IOC编号	
     * </pre>
     *
     * <code>required string ioc_id = 1;</code>
     * @return The bytes for iocId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocIdBytes() {
      java.lang.Object ref = iocId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_VALUE_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocValue_ = "";
    /**
     * <pre>
     * IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     * @return Whether the iocValue field is set.
     */
    @java.lang.Override
    public boolean hasIocValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     * @return The iocValue.
     */
    @java.lang.Override
    public java.lang.String getIocValue() {
      java.lang.Object ref = iocValue_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocValue_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IOC内容	
     * </pre>
     *
     * <code>required string ioc_value = 2;</code>
     * @return The bytes for iocValue.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocValueBytes() {
      java.lang.Object ref = iocValue_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_CATEGORY_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocCategory_ = "";
    /**
     * <pre>
     * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     * @return Whether the iocCategory field is set.
     */
    @java.lang.Override
    public boolean hasIocCategory() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     * @return The iocCategory.
     */
    @java.lang.Override
    public java.lang.String getIocCategory() {
      java.lang.Object ref = iocCategory_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocCategory_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
     * </pre>
     *
     * <code>required string ioc_category = 3;</code>
     * @return The bytes for iocCategory.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocCategoryBytes() {
      java.lang.Object ref = iocCategory_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocCategory_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_PUBLIC_DATE_FIELD_NUMBER = 4;
    private long iocPublicDate_ = 0L;
    /**
     * <pre>
     * IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     * @return Whether the iocPublicDate field is set.
     */
    @java.lang.Override
    public boolean hasIocPublicDate() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * IOC发布时间	
     * </pre>
     *
     * <code>required uint64 ioc_public_date = 4;</code>
     * @return The iocPublicDate.
     */
    @java.lang.Override
    public long getIocPublicDate() {
      return iocPublicDate_;
    }

    public static final int IOC_ALERT_NAME_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAlertName_ = "";
    /**
     * <pre>
     * IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     * @return Whether the iocAlertName field is set.
     */
    @java.lang.Override
    public boolean hasIocAlertName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     * @return The iocAlertName.
     */
    @java.lang.Override
    public java.lang.String getIocAlertName() {
      java.lang.Object ref = iocAlertName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAlertName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IOC告警名称	
     * </pre>
     *
     * <code>required string ioc_alert_name = 5;</code>
     * @return The bytes for iocAlertName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAlertNameBytes() {
      java.lang.Object ref = iocAlertName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAlertName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_CURRENT_STATUS_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocCurrentStatus_ = "";
    /**
     * <pre>
     * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     * @return Whether the iocCurrentStatus field is set.
     */
    @java.lang.Override
    public boolean hasIocCurrentStatus() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     * @return The iocCurrentStatus.
     */
    @java.lang.Override
    public java.lang.String getIocCurrentStatus() {
      java.lang.Object ref = iocCurrentStatus_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocCurrentStatus_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
     * </pre>
     *
     * <code>required string ioc_current_status = 6;</code>
     * @return The bytes for iocCurrentStatus.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocCurrentStatusBytes() {
      java.lang.Object ref = iocCurrentStatus_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocCurrentStatus_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_HOT_FIELD_NUMBER = 7;
    private boolean iocHot_ = false;
    /**
     * <pre>
     * IOC热点状态	True/False
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     * @return Whether the iocHot field is set.
     */
    @java.lang.Override
    public boolean hasIocHot() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * IOC热点状态	True/False
     * </pre>
     *
     * <code>optional bool ioc_hot = 7;</code>
     * @return The iocHot.
     */
    @java.lang.Override
    public boolean getIocHot() {
      return iocHot_;
    }

    public static final int IOC_FIRST_SEEN_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocFirstSeen_ = "";
    /**
     * <pre>
     * 首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     * @return Whether the iocFirstSeen field is set.
     */
    @java.lang.Override
    public boolean hasIocFirstSeen() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     * @return The iocFirstSeen.
     */
    @java.lang.Override
    public java.lang.String getIocFirstSeen() {
      java.lang.Object ref = iocFirstSeen_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocFirstSeen_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 首次发现时间	情报的首次发现时间
     * </pre>
     *
     * <code>optional string ioc_first_seen = 8;</code>
     * @return The bytes for iocFirstSeen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocFirstSeenBytes() {
      java.lang.Object ref = iocFirstSeen_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocFirstSeen_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_LAST_DETECTION_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocLastDetection_ = "";
    /**
     * <pre>
     * 最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     * @return Whether the iocLastDetection field is set.
     */
    @java.lang.Override
    public boolean hasIocLastDetection() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     * @return The iocLastDetection.
     */
    @java.lang.Override
    public java.lang.String getIocLastDetection() {
      java.lang.Object ref = iocLastDetection_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocLastDetection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 最近检测时间	最后一次检测到攻击的时间
     * </pre>
     *
     * <code>required string ioc_last_detection = 9;</code>
     * @return The bytes for iocLastDetection.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocLastDetectionBytes() {
      java.lang.Object ref = iocLastDetection_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocLastDetection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_REFER_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocRefer_ = "";
    /**
     * <pre>
     * 参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     * @return Whether the iocRefer field is set.
     */
    @java.lang.Override
    public boolean hasIocRefer() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     * @return The iocRefer.
     */
    @java.lang.Override
    public java.lang.String getIocRefer() {
      java.lang.Object ref = iocRefer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocRefer_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 参考文档报告	
     * </pre>
     *
     * <code>optional string ioc_refer = 10;</code>
     * @return The bytes for iocRefer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocReferBytes() {
      java.lang.Object ref = iocRefer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocRefer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_REPORT_DATA_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocReportData_ = "";
    /**
     * <pre>
     * 报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     * @return Whether the iocReportData field is set.
     */
    @java.lang.Override
    public boolean hasIocReportData() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     * @return The iocReportData.
     */
    @java.lang.Override
    public java.lang.String getIocReportData() {
      java.lang.Object ref = iocReportData_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocReportData_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 报告发布时间	
     * </pre>
     *
     * <code>optional string ioc_report_data = 11;</code>
     * @return The bytes for iocReportData.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocReportDataBytes() {
      java.lang.Object ref = iocReportData_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocReportData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_REPORT_VENDOR_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocReportVendor_ = "";
    /**
     * <pre>
     * 报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     * @return Whether the iocReportVendor field is set.
     */
    @java.lang.Override
    public boolean hasIocReportVendor() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     * @return The iocReportVendor.
     */
    @java.lang.Override
    public java.lang.String getIocReportVendor() {
      java.lang.Object ref = iocReportVendor_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocReportVendor_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 报告发布厂商	
     * </pre>
     *
     * <code>optional string ioc_report_vendor = 12;</code>
     * @return The bytes for iocReportVendor.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocReportVendorBytes() {
      java.lang.Object ref = iocReportVendor_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocReportVendor_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_TYPE_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocType_ = "";
    /**
     * <pre>
     * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     * @return Whether the iocType field is set.
     */
    @java.lang.Override
    public boolean hasIocType() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     * @return The iocType.
     */
    @java.lang.Override
    public java.lang.String getIocType() {
      java.lang.Object ref = iocType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
     * </pre>
     *
     * <code>required string ioc_type = 13;</code>
     * @return The bytes for iocType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocTypeBytes() {
      java.lang.Object ref = iocType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_TARGETED_FIELD_NUMBER = 14;
    private boolean iocTargeted_ = false;
    /**
     * <pre>
     * 定向攻击标识	True/False
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     * @return Whether the iocTargeted field is set.
     */
    @java.lang.Override
    public boolean hasIocTargeted() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 定向攻击标识	True/False
     * </pre>
     *
     * <code>required bool ioc_targeted = 14;</code>
     * @return The iocTargeted.
     */
    @java.lang.Override
    public boolean getIocTargeted() {
      return iocTargeted_;
    }

    public static final int IOC_MALICIOUS_FAMILY_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocMaliciousFamily_ = "";
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     * @return Whether the iocMaliciousFamily field is set.
     */
    @java.lang.Override
    public boolean hasIocMaliciousFamily() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     * @return The iocMaliciousFamily.
     */
    @java.lang.Override
    public java.lang.String getIocMaliciousFamily() {
      java.lang.Object ref = iocMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocMaliciousFamily_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 恶意代码家族	
     * </pre>
     *
     * <code>optional string ioc_malicious_family = 15;</code>
     * @return The bytes for iocMaliciousFamily.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocMaliciousFamilyBytes() {
      java.lang.Object ref = iocMaliciousFamily_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocMaliciousFamily_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_CAMPAIGN_FIELD_NUMBER = 16;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAptCampaign_ = "";
    /**
     * <pre>
     * APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     * @return Whether the iocAptCampaign field is set.
     */
    @java.lang.Override
    public boolean hasIocAptCampaign() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     * @return The iocAptCampaign.
     */
    @java.lang.Override
    public java.lang.String getIocAptCampaign() {
      java.lang.Object ref = iocAptCampaign_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptCampaign_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * APT组织名称	对应actor、primary_name
     * </pre>
     *
     * <code>optional string ioc_apt_campaign = 16;</code>
     * @return The bytes for iocAptCampaign.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAptCampaignBytes() {
      java.lang.Object ref = iocAptCampaign_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptCampaign_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_ALIAS_FIELD_NUMBER = 17;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAptAlias_ = "";
    /**
     * <pre>
     * APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     * @return Whether the iocAptAlias field is set.
     */
    @java.lang.Override
    public boolean hasIocAptAlias() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     * @return The iocAptAlias.
     */
    @java.lang.Override
    public java.lang.String getIocAptAlias() {
      java.lang.Object ref = iocAptAlias_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptAlias_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * APT组织别名	
     * </pre>
     *
     * <code>optional string ioc_apt_alias = 17;</code>
     * @return The bytes for iocAptAlias.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAptAliasBytes() {
      java.lang.Object ref = iocAptAlias_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptAlias_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_COUNTRY_FIELD_NUMBER = 18;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAptCountry_ = "";
    /**
     * <pre>
     * APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     * @return Whether the iocAptCountry field is set.
     */
    @java.lang.Override
    public boolean hasIocAptCountry() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     * @return The iocAptCountry.
     */
    @java.lang.Override
    public java.lang.String getIocAptCountry() {
      java.lang.Object ref = iocAptCountry_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptCountry_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * APT所属国家	
     * </pre>
     *
     * <code>optional string ioc_apt_country = 18;</code>
     * @return The bytes for iocAptCountry.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAptCountryBytes() {
      java.lang.Object ref = iocAptCountry_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptCountry_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_APT_MISSION_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAptMission_ = "";
    /**
     * <pre>
     * APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     * @return Whether the iocAptMission field is set.
     */
    @java.lang.Override
    public boolean hasIocAptMission() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     * @return The iocAptMission.
     */
    @java.lang.Override
    public java.lang.String getIocAptMission() {
      java.lang.Object ref = iocAptMission_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAptMission_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * APT行动名称	
     * </pre>
     *
     * <code>optional string ioc_apt_mission = 19;</code>
     * @return The bytes for iocAptMission.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAptMissionBytes() {
      java.lang.Object ref = iocAptMission_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAptMission_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_RAT_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocRat_ = "";
    /**
     * <pre>
     * 远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     * @return Whether the iocRat field is set.
     */
    @java.lang.Override
    public boolean hasIocRat() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     * @return The iocRat.
     */
    @java.lang.Override
    public java.lang.String getIocRat() {
      java.lang.Object ref = iocRat_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocRat_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 远控工具	
     * </pre>
     *
     * <code>optional string ioc_rat = 20;</code>
     * @return The bytes for iocRat.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocRatBytes() {
      java.lang.Object ref = iocRat_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocRat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_ATTACK_METHOD_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAttackMethod_ = "";
    /**
     * <pre>
     * 攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     * @return Whether the iocAttackMethod field is set.
     */
    @java.lang.Override
    public boolean hasIocAttackMethod() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     * @return The iocAttackMethod.
     */
    @java.lang.Override
    public java.lang.String getIocAttackMethod() {
      java.lang.Object ref = iocAttackMethod_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAttackMethod_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 攻击手法	WEB攻击渗透、…
     * </pre>
     *
     * <code>optional string ioc_attack_method = 21;</code>
     * @return The bytes for iocAttackMethod.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAttackMethodBytes() {
      java.lang.Object ref = iocAttackMethod_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAttackMethod_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_VUL_FIELD_NUMBER = 22;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocVul_ = "";
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     * @return Whether the iocVul field is set.
     */
    @java.lang.Override
    public boolean hasIocVul() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     * @return The iocVul.
     */
    @java.lang.Override
    public java.lang.String getIocVul() {
      java.lang.Object ref = iocVul_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocVul_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 关联漏洞	攻击者所用到的漏洞
     * </pre>
     *
     * <code>optional string ioc_vul = 22;</code>
     * @return The bytes for iocVul.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocVulBytes() {
      java.lang.Object ref = iocVul_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocVul_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_AFFECTED_SECTOR_FIELD_NUMBER = 23;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAffectedSector_ = "";
    /**
     * <pre>
     * 影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     * @return Whether the iocAffectedSector field is set.
     */
    @java.lang.Override
    public boolean hasIocAffectedSector() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * 影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     * @return The iocAffectedSector.
     */
    @java.lang.Override
    public java.lang.String getIocAffectedSector() {
      java.lang.Object ref = iocAffectedSector_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAffectedSector_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 影响行业	
     * </pre>
     *
     * <code>optional string ioc_affected_sector = 23;</code>
     * @return The bytes for iocAffectedSector.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAffectedSectorBytes() {
      java.lang.Object ref = iocAffectedSector_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAffectedSector_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_AFFECTED_PRODUCT_FIELD_NUMBER = 24;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocAffectedProduct_ = "";
    /**
     * <pre>
     * 影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     * @return Whether the iocAffectedProduct field is set.
     */
    @java.lang.Override
    public boolean hasIocAffectedProduct() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * 影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     * @return The iocAffectedProduct.
     */
    @java.lang.Override
    public java.lang.String getIocAffectedProduct() {
      java.lang.Object ref = iocAffectedProduct_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocAffectedProduct_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 影响平台	
     * </pre>
     *
     * <code>optional string ioc_affected_product = 24;</code>
     * @return The bytes for iocAffectedProduct.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocAffectedProductBytes() {
      java.lang.Object ref = iocAffectedProduct_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocAffectedProduct_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_DETAIL_INFO_FIELD_NUMBER = 25;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iocDetailInfo_ = "";
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     * @return Whether the iocDetailInfo field is set.
     */
    @java.lang.Override
    public boolean hasIocDetailInfo() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     * @return The iocDetailInfo.
     */
    @java.lang.Override
    public java.lang.String getIocDetailInfo() {
      java.lang.Object ref = iocDetailInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iocDetailInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
     * </pre>
     *
     * <code>required string ioc_detail_info = 25;</code>
     * @return The bytes for iocDetailInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIocDetailInfoBytes() {
      java.lang.Object ref = iocDetailInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iocDetailInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIocId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocCategory()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocPublicDate()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocAlertName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocCurrentStatus()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocLastDetection()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocTargeted()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIocDetailInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, iocId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, iocValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, iocCategory_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt64(4, iocPublicDate_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, iocAlertName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, iocCurrentStatus_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeBool(7, iocHot_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, iocFirstSeen_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, iocLastDetection_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, iocRefer_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, iocReportData_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, iocReportVendor_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 13, iocType_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeBool(14, iocTargeted_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 15, iocMaliciousFamily_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 16, iocAptCampaign_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 17, iocAptAlias_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 18, iocAptCountry_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 19, iocAptMission_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 20, iocRat_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 21, iocAttackMethod_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 22, iocVul_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 23, iocAffectedSector_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 24, iocAffectedProduct_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 25, iocDetailInfo_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, iocId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, iocValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, iocCategory_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, iocPublicDate_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, iocAlertName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, iocCurrentStatus_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(7, iocHot_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, iocFirstSeen_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(9, iocLastDetection_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, iocRefer_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, iocReportData_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, iocReportVendor_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(13, iocType_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(14, iocTargeted_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(15, iocMaliciousFamily_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(16, iocAptCampaign_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(17, iocAptAlias_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(18, iocAptCountry_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(19, iocAptMission_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(20, iocRat_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(21, iocAttackMethod_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(22, iocVul_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(23, iocAffectedSector_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(24, iocAffectedProduct_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(25, iocDetailInfo_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IocAlertInfo.IOC_ALERT_INFO)) {
        return super.equals(obj);
      }
      IocAlertInfo.IOC_ALERT_INFO other = (IocAlertInfo.IOC_ALERT_INFO) obj;

      if (hasIocId() != other.hasIocId()) return false;
      if (hasIocId()) {
        if (!getIocId()
            .equals(other.getIocId())) return false;
      }
      if (hasIocValue() != other.hasIocValue()) return false;
      if (hasIocValue()) {
        if (!getIocValue()
            .equals(other.getIocValue())) return false;
      }
      if (hasIocCategory() != other.hasIocCategory()) return false;
      if (hasIocCategory()) {
        if (!getIocCategory()
            .equals(other.getIocCategory())) return false;
      }
      if (hasIocPublicDate() != other.hasIocPublicDate()) return false;
      if (hasIocPublicDate()) {
        if (getIocPublicDate()
            != other.getIocPublicDate()) return false;
      }
      if (hasIocAlertName() != other.hasIocAlertName()) return false;
      if (hasIocAlertName()) {
        if (!getIocAlertName()
            .equals(other.getIocAlertName())) return false;
      }
      if (hasIocCurrentStatus() != other.hasIocCurrentStatus()) return false;
      if (hasIocCurrentStatus()) {
        if (!getIocCurrentStatus()
            .equals(other.getIocCurrentStatus())) return false;
      }
      if (hasIocHot() != other.hasIocHot()) return false;
      if (hasIocHot()) {
        if (getIocHot()
            != other.getIocHot()) return false;
      }
      if (hasIocFirstSeen() != other.hasIocFirstSeen()) return false;
      if (hasIocFirstSeen()) {
        if (!getIocFirstSeen()
            .equals(other.getIocFirstSeen())) return false;
      }
      if (hasIocLastDetection() != other.hasIocLastDetection()) return false;
      if (hasIocLastDetection()) {
        if (!getIocLastDetection()
            .equals(other.getIocLastDetection())) return false;
      }
      if (hasIocRefer() != other.hasIocRefer()) return false;
      if (hasIocRefer()) {
        if (!getIocRefer()
            .equals(other.getIocRefer())) return false;
      }
      if (hasIocReportData() != other.hasIocReportData()) return false;
      if (hasIocReportData()) {
        if (!getIocReportData()
            .equals(other.getIocReportData())) return false;
      }
      if (hasIocReportVendor() != other.hasIocReportVendor()) return false;
      if (hasIocReportVendor()) {
        if (!getIocReportVendor()
            .equals(other.getIocReportVendor())) return false;
      }
      if (hasIocType() != other.hasIocType()) return false;
      if (hasIocType()) {
        if (!getIocType()
            .equals(other.getIocType())) return false;
      }
      if (hasIocTargeted() != other.hasIocTargeted()) return false;
      if (hasIocTargeted()) {
        if (getIocTargeted()
            != other.getIocTargeted()) return false;
      }
      if (hasIocMaliciousFamily() != other.hasIocMaliciousFamily()) return false;
      if (hasIocMaliciousFamily()) {
        if (!getIocMaliciousFamily()
            .equals(other.getIocMaliciousFamily())) return false;
      }
      if (hasIocAptCampaign() != other.hasIocAptCampaign()) return false;
      if (hasIocAptCampaign()) {
        if (!getIocAptCampaign()
            .equals(other.getIocAptCampaign())) return false;
      }
      if (hasIocAptAlias() != other.hasIocAptAlias()) return false;
      if (hasIocAptAlias()) {
        if (!getIocAptAlias()
            .equals(other.getIocAptAlias())) return false;
      }
      if (hasIocAptCountry() != other.hasIocAptCountry()) return false;
      if (hasIocAptCountry()) {
        if (!getIocAptCountry()
            .equals(other.getIocAptCountry())) return false;
      }
      if (hasIocAptMission() != other.hasIocAptMission()) return false;
      if (hasIocAptMission()) {
        if (!getIocAptMission()
            .equals(other.getIocAptMission())) return false;
      }
      if (hasIocRat() != other.hasIocRat()) return false;
      if (hasIocRat()) {
        if (!getIocRat()
            .equals(other.getIocRat())) return false;
      }
      if (hasIocAttackMethod() != other.hasIocAttackMethod()) return false;
      if (hasIocAttackMethod()) {
        if (!getIocAttackMethod()
            .equals(other.getIocAttackMethod())) return false;
      }
      if (hasIocVul() != other.hasIocVul()) return false;
      if (hasIocVul()) {
        if (!getIocVul()
            .equals(other.getIocVul())) return false;
      }
      if (hasIocAffectedSector() != other.hasIocAffectedSector()) return false;
      if (hasIocAffectedSector()) {
        if (!getIocAffectedSector()
            .equals(other.getIocAffectedSector())) return false;
      }
      if (hasIocAffectedProduct() != other.hasIocAffectedProduct()) return false;
      if (hasIocAffectedProduct()) {
        if (!getIocAffectedProduct()
            .equals(other.getIocAffectedProduct())) return false;
      }
      if (hasIocDetailInfo() != other.hasIocDetailInfo()) return false;
      if (hasIocDetailInfo()) {
        if (!getIocDetailInfo()
            .equals(other.getIocDetailInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIocId()) {
        hash = (37 * hash) + IOC_ID_FIELD_NUMBER;
        hash = (53 * hash) + getIocId().hashCode();
      }
      if (hasIocValue()) {
        hash = (37 * hash) + IOC_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getIocValue().hashCode();
      }
      if (hasIocCategory()) {
        hash = (37 * hash) + IOC_CATEGORY_FIELD_NUMBER;
        hash = (53 * hash) + getIocCategory().hashCode();
      }
      if (hasIocPublicDate()) {
        hash = (37 * hash) + IOC_PUBLIC_DATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getIocPublicDate());
      }
      if (hasIocAlertName()) {
        hash = (37 * hash) + IOC_ALERT_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getIocAlertName().hashCode();
      }
      if (hasIocCurrentStatus()) {
        hash = (37 * hash) + IOC_CURRENT_STATUS_FIELD_NUMBER;
        hash = (53 * hash) + getIocCurrentStatus().hashCode();
      }
      if (hasIocHot()) {
        hash = (37 * hash) + IOC_HOT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIocHot());
      }
      if (hasIocFirstSeen()) {
        hash = (37 * hash) + IOC_FIRST_SEEN_FIELD_NUMBER;
        hash = (53 * hash) + getIocFirstSeen().hashCode();
      }
      if (hasIocLastDetection()) {
        hash = (37 * hash) + IOC_LAST_DETECTION_FIELD_NUMBER;
        hash = (53 * hash) + getIocLastDetection().hashCode();
      }
      if (hasIocRefer()) {
        hash = (37 * hash) + IOC_REFER_FIELD_NUMBER;
        hash = (53 * hash) + getIocRefer().hashCode();
      }
      if (hasIocReportData()) {
        hash = (37 * hash) + IOC_REPORT_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getIocReportData().hashCode();
      }
      if (hasIocReportVendor()) {
        hash = (37 * hash) + IOC_REPORT_VENDOR_FIELD_NUMBER;
        hash = (53 * hash) + getIocReportVendor().hashCode();
      }
      if (hasIocType()) {
        hash = (37 * hash) + IOC_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIocType().hashCode();
      }
      if (hasIocTargeted()) {
        hash = (37 * hash) + IOC_TARGETED_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIocTargeted());
      }
      if (hasIocMaliciousFamily()) {
        hash = (37 * hash) + IOC_MALICIOUS_FAMILY_FIELD_NUMBER;
        hash = (53 * hash) + getIocMaliciousFamily().hashCode();
      }
      if (hasIocAptCampaign()) {
        hash = (37 * hash) + IOC_APT_CAMPAIGN_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptCampaign().hashCode();
      }
      if (hasIocAptAlias()) {
        hash = (37 * hash) + IOC_APT_ALIAS_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptAlias().hashCode();
      }
      if (hasIocAptCountry()) {
        hash = (37 * hash) + IOC_APT_COUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptCountry().hashCode();
      }
      if (hasIocAptMission()) {
        hash = (37 * hash) + IOC_APT_MISSION_FIELD_NUMBER;
        hash = (53 * hash) + getIocAptMission().hashCode();
      }
      if (hasIocRat()) {
        hash = (37 * hash) + IOC_RAT_FIELD_NUMBER;
        hash = (53 * hash) + getIocRat().hashCode();
      }
      if (hasIocAttackMethod()) {
        hash = (37 * hash) + IOC_ATTACK_METHOD_FIELD_NUMBER;
        hash = (53 * hash) + getIocAttackMethod().hashCode();
      }
      if (hasIocVul()) {
        hash = (37 * hash) + IOC_VUL_FIELD_NUMBER;
        hash = (53 * hash) + getIocVul().hashCode();
      }
      if (hasIocAffectedSector()) {
        hash = (37 * hash) + IOC_AFFECTED_SECTOR_FIELD_NUMBER;
        hash = (53 * hash) + getIocAffectedSector().hashCode();
      }
      if (hasIocAffectedProduct()) {
        hash = (37 * hash) + IOC_AFFECTED_PRODUCT_FIELD_NUMBER;
        hash = (53 * hash) + getIocAffectedProduct().hashCode();
      }
      if (hasIocDetailInfo()) {
        hash = (37 * hash) + IOC_DETAIL_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIocDetailInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static IocAlertInfo.IOC_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static IocAlertInfo.IOC_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IocAlertInfo.IOC_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IocAlertInfo.IOC_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IOC_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IOC_ALERT_INFO)
        IocAlertInfo.IOC_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IocAlertInfo.internal_static_IOC_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IocAlertInfo.internal_static_IOC_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IocAlertInfo.IOC_ALERT_INFO.class, IocAlertInfo.IOC_ALERT_INFO.Builder.class);
      }

      // Construct using IocAlertInfo.IOC_ALERT_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        iocId_ = "";
        iocValue_ = "";
        iocCategory_ = "";
        iocPublicDate_ = 0L;
        iocAlertName_ = "";
        iocCurrentStatus_ = "";
        iocHot_ = false;
        iocFirstSeen_ = "";
        iocLastDetection_ = "";
        iocRefer_ = "";
        iocReportData_ = "";
        iocReportVendor_ = "";
        iocType_ = "";
        iocTargeted_ = false;
        iocMaliciousFamily_ = "";
        iocAptCampaign_ = "";
        iocAptAlias_ = "";
        iocAptCountry_ = "";
        iocAptMission_ = "";
        iocRat_ = "";
        iocAttackMethod_ = "";
        iocVul_ = "";
        iocAffectedSector_ = "";
        iocAffectedProduct_ = "";
        iocDetailInfo_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IocAlertInfo.internal_static_IOC_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public IocAlertInfo.IOC_ALERT_INFO getDefaultInstanceForType() {
        return IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IocAlertInfo.IOC_ALERT_INFO build() {
        IocAlertInfo.IOC_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IocAlertInfo.IOC_ALERT_INFO buildPartial() {
        IocAlertInfo.IOC_ALERT_INFO result = new IocAlertInfo.IOC_ALERT_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(IocAlertInfo.IOC_ALERT_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.iocId_ = iocId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.iocValue_ = iocValue_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.iocCategory_ = iocCategory_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.iocPublicDate_ = iocPublicDate_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.iocAlertName_ = iocAlertName_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.iocCurrentStatus_ = iocCurrentStatus_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.iocHot_ = iocHot_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.iocFirstSeen_ = iocFirstSeen_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.iocLastDetection_ = iocLastDetection_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.iocRefer_ = iocRefer_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.iocReportData_ = iocReportData_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.iocReportVendor_ = iocReportVendor_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.iocType_ = iocType_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.iocTargeted_ = iocTargeted_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.iocMaliciousFamily_ = iocMaliciousFamily_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.iocAptCampaign_ = iocAptCampaign_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.iocAptAlias_ = iocAptAlias_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.iocAptCountry_ = iocAptCountry_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.iocAptMission_ = iocAptMission_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.iocRat_ = iocRat_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.iocAttackMethod_ = iocAttackMethod_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.iocVul_ = iocVul_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.iocAffectedSector_ = iocAffectedSector_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.iocAffectedProduct_ = iocAffectedProduct_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.iocDetailInfo_ = iocDetailInfo_;
          to_bitField0_ |= 0x01000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IocAlertInfo.IOC_ALERT_INFO) {
          return mergeFrom((IocAlertInfo.IOC_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IocAlertInfo.IOC_ALERT_INFO other) {
        if (other == IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasIocId()) {
          iocId_ = other.iocId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasIocValue()) {
          iocValue_ = other.iocValue_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasIocCategory()) {
          iocCategory_ = other.iocCategory_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasIocPublicDate()) {
          setIocPublicDate(other.getIocPublicDate());
        }
        if (other.hasIocAlertName()) {
          iocAlertName_ = other.iocAlertName_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.hasIocCurrentStatus()) {
          iocCurrentStatus_ = other.iocCurrentStatus_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (other.hasIocHot()) {
          setIocHot(other.getIocHot());
        }
        if (other.hasIocFirstSeen()) {
          iocFirstSeen_ = other.iocFirstSeen_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (other.hasIocLastDetection()) {
          iocLastDetection_ = other.iocLastDetection_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (other.hasIocRefer()) {
          iocRefer_ = other.iocRefer_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (other.hasIocReportData()) {
          iocReportData_ = other.iocReportData_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (other.hasIocReportVendor()) {
          iocReportVendor_ = other.iocReportVendor_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (other.hasIocType()) {
          iocType_ = other.iocType_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (other.hasIocTargeted()) {
          setIocTargeted(other.getIocTargeted());
        }
        if (other.hasIocMaliciousFamily()) {
          iocMaliciousFamily_ = other.iocMaliciousFamily_;
          bitField0_ |= 0x00004000;
          onChanged();
        }
        if (other.hasIocAptCampaign()) {
          iocAptCampaign_ = other.iocAptCampaign_;
          bitField0_ |= 0x00008000;
          onChanged();
        }
        if (other.hasIocAptAlias()) {
          iocAptAlias_ = other.iocAptAlias_;
          bitField0_ |= 0x00010000;
          onChanged();
        }
        if (other.hasIocAptCountry()) {
          iocAptCountry_ = other.iocAptCountry_;
          bitField0_ |= 0x00020000;
          onChanged();
        }
        if (other.hasIocAptMission()) {
          iocAptMission_ = other.iocAptMission_;
          bitField0_ |= 0x00040000;
          onChanged();
        }
        if (other.hasIocRat()) {
          iocRat_ = other.iocRat_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (other.hasIocAttackMethod()) {
          iocAttackMethod_ = other.iocAttackMethod_;
          bitField0_ |= 0x00100000;
          onChanged();
        }
        if (other.hasIocVul()) {
          iocVul_ = other.iocVul_;
          bitField0_ |= 0x00200000;
          onChanged();
        }
        if (other.hasIocAffectedSector()) {
          iocAffectedSector_ = other.iocAffectedSector_;
          bitField0_ |= 0x00400000;
          onChanged();
        }
        if (other.hasIocAffectedProduct()) {
          iocAffectedProduct_ = other.iocAffectedProduct_;
          bitField0_ |= 0x00800000;
          onChanged();
        }
        if (other.hasIocDetailInfo()) {
          iocDetailInfo_ = other.iocDetailInfo_;
          bitField0_ |= 0x01000000;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIocId()) {
          return false;
        }
        if (!hasIocValue()) {
          return false;
        }
        if (!hasIocCategory()) {
          return false;
        }
        if (!hasIocPublicDate()) {
          return false;
        }
        if (!hasIocAlertName()) {
          return false;
        }
        if (!hasIocCurrentStatus()) {
          return false;
        }
        if (!hasIocLastDetection()) {
          return false;
        }
        if (!hasIocType()) {
          return false;
        }
        if (!hasIocTargeted()) {
          return false;
        }
        if (!hasIocDetailInfo()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                iocId_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                iocValue_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                iocCategory_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                iocPublicDate_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                iocAlertName_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                iocCurrentStatus_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 56: {
                iocHot_ = input.readBool();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 66: {
                iocFirstSeen_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                iocLastDetection_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                iocRefer_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                iocReportData_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                iocReportVendor_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                iocType_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 112: {
                iocTargeted_ = input.readBool();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 122: {
                iocMaliciousFamily_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                iocAptCampaign_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                iocAptAlias_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                iocAptCountry_ = input.readBytes();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                iocAptMission_ = input.readBytes();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 162: {
                iocRat_ = input.readBytes();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 170: {
                iocAttackMethod_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 178: {
                iocVul_ = input.readBytes();
                bitField0_ |= 0x00200000;
                break;
              } // case 178
              case 186: {
                iocAffectedSector_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                iocAffectedProduct_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                iocDetailInfo_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object iocId_ = "";
      /**
       * <pre>
       * IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       * @return Whether the iocId field is set.
       */
      public boolean hasIocId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       * @return The iocId.
       */
      public java.lang.String getIocId() {
        java.lang.Object ref = iocId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       * @return The bytes for iocId.
       */
      public com.google.protobuf.ByteString
          getIocIdBytes() {
        java.lang.Object ref = iocId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       * @param value The iocId to set.
       * @return This builder for chaining.
       */
      public Builder setIocId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocId() {
        iocId_ = getDefaultInstance().getIocId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC编号	
       * </pre>
       *
       * <code>required string ioc_id = 1;</code>
       * @param value The bytes for iocId to set.
       * @return This builder for chaining.
       */
      public Builder setIocIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object iocValue_ = "";
      /**
       * <pre>
       * IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       * @return Whether the iocValue field is set.
       */
      public boolean hasIocValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       * @return The iocValue.
       */
      public java.lang.String getIocValue() {
        java.lang.Object ref = iocValue_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocValue_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       * @return The bytes for iocValue.
       */
      public com.google.protobuf.ByteString
          getIocValueBytes() {
        java.lang.Object ref = iocValue_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocValue_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       * @param value The iocValue to set.
       * @return This builder for chaining.
       */
      public Builder setIocValue(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocValue_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocValue() {
        iocValue_ = getDefaultInstance().getIocValue();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC内容	
       * </pre>
       *
       * <code>required string ioc_value = 2;</code>
       * @param value The bytes for iocValue to set.
       * @return This builder for chaining.
       */
      public Builder setIocValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocValue_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object iocCategory_ = "";
      /**
       * <pre>
       * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       * @return Whether the iocCategory field is set.
       */
      public boolean hasIocCategory() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       * @return The iocCategory.
       */
      public java.lang.String getIocCategory() {
        java.lang.Object ref = iocCategory_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocCategory_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       * @return The bytes for iocCategory.
       */
      public com.google.protobuf.ByteString
          getIocCategoryBytes() {
        java.lang.Object ref = iocCategory_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocCategory_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       * @param value The iocCategory to set.
       * @return This builder for chaining.
       */
      public Builder setIocCategory(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocCategory_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocCategory() {
        iocCategory_ = getDefaultInstance().getIocCategory();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC策略	IP_PORT、DOMAIN、URL、HASH、TPD…
       * </pre>
       *
       * <code>required string ioc_category = 3;</code>
       * @param value The bytes for iocCategory to set.
       * @return This builder for chaining.
       */
      public Builder setIocCategoryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocCategory_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private long iocPublicDate_ ;
      /**
       * <pre>
       * IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       * @return Whether the iocPublicDate field is set.
       */
      @java.lang.Override
      public boolean hasIocPublicDate() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       * @return The iocPublicDate.
       */
      @java.lang.Override
      public long getIocPublicDate() {
        return iocPublicDate_;
      }
      /**
       * <pre>
       * IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       * @param value The iocPublicDate to set.
       * @return This builder for chaining.
       */
      public Builder setIocPublicDate(long value) {

        iocPublicDate_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC发布时间	
       * </pre>
       *
       * <code>required uint64 ioc_public_date = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocPublicDate() {
        bitField0_ = (bitField0_ & ~0x00000008);
        iocPublicDate_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object iocAlertName_ = "";
      /**
       * <pre>
       * IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       * @return Whether the iocAlertName field is set.
       */
      public boolean hasIocAlertName() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       * @return The iocAlertName.
       */
      public java.lang.String getIocAlertName() {
        java.lang.Object ref = iocAlertName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAlertName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       * @return The bytes for iocAlertName.
       */
      public com.google.protobuf.ByteString
          getIocAlertNameBytes() {
        java.lang.Object ref = iocAlertName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAlertName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       * @param value The iocAlertName to set.
       * @return This builder for chaining.
       */
      public Builder setIocAlertName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAlertName_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAlertName() {
        iocAlertName_ = getDefaultInstance().getIocAlertName();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC告警名称	
       * </pre>
       *
       * <code>required string ioc_alert_name = 5;</code>
       * @param value The bytes for iocAlertName to set.
       * @return This builder for chaining.
       */
      public Builder setIocAlertNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAlertName_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object iocCurrentStatus_ = "";
      /**
       * <pre>
       * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       * @return Whether the iocCurrentStatus field is set.
       */
      public boolean hasIocCurrentStatus() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       * @return The iocCurrentStatus.
       */
      public java.lang.String getIocCurrentStatus() {
        java.lang.Object ref = iocCurrentStatus_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocCurrentStatus_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       * @return The bytes for iocCurrentStatus.
       */
      public com.google.protobuf.ByteString
          getIocCurrentStatusBytes() {
        java.lang.Object ref = iocCurrentStatus_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocCurrentStatus_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       * @param value The iocCurrentStatus to set.
       * @return This builder for chaining.
       */
      public Builder setIocCurrentStatus(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocCurrentStatus_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocCurrentStatus() {
        iocCurrentStatus_ = getDefaultInstance().getIocCurrentStatus();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC当前状态	"active/inactive/sinkhole/unknown具体含义为：1.active即活跃:当前观察到此IOC的活动2.inactive即非活跃：当前此IOC处于不活动状态，如休眠期等；3.sinkhole：表示此IOC（域名类）处于黑洞状态，或接管状态4.unknown：当前没有观察到此IOC的状态，此IOC依然是有效的威胁"
       * </pre>
       *
       * <code>required string ioc_current_status = 6;</code>
       * @param value The bytes for iocCurrentStatus to set.
       * @return This builder for chaining.
       */
      public Builder setIocCurrentStatusBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocCurrentStatus_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private boolean iocHot_ ;
      /**
       * <pre>
       * IOC热点状态	True/False
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       * @return Whether the iocHot field is set.
       */
      @java.lang.Override
      public boolean hasIocHot() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * IOC热点状态	True/False
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       * @return The iocHot.
       */
      @java.lang.Override
      public boolean getIocHot() {
        return iocHot_;
      }
      /**
       * <pre>
       * IOC热点状态	True/False
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       * @param value The iocHot to set.
       * @return This builder for chaining.
       */
      public Builder setIocHot(boolean value) {

        iocHot_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC热点状态	True/False
       * </pre>
       *
       * <code>optional bool ioc_hot = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocHot() {
        bitField0_ = (bitField0_ & ~0x00000040);
        iocHot_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object iocFirstSeen_ = "";
      /**
       * <pre>
       * 首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       * @return Whether the iocFirstSeen field is set.
       */
      public boolean hasIocFirstSeen() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       * @return The iocFirstSeen.
       */
      public java.lang.String getIocFirstSeen() {
        java.lang.Object ref = iocFirstSeen_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocFirstSeen_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       * @return The bytes for iocFirstSeen.
       */
      public com.google.protobuf.ByteString
          getIocFirstSeenBytes() {
        java.lang.Object ref = iocFirstSeen_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocFirstSeen_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       * @param value The iocFirstSeen to set.
       * @return This builder for chaining.
       */
      public Builder setIocFirstSeen(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocFirstSeen_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocFirstSeen() {
        iocFirstSeen_ = getDefaultInstance().getIocFirstSeen();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 首次发现时间	情报的首次发现时间
       * </pre>
       *
       * <code>optional string ioc_first_seen = 8;</code>
       * @param value The bytes for iocFirstSeen to set.
       * @return This builder for chaining.
       */
      public Builder setIocFirstSeenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocFirstSeen_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private java.lang.Object iocLastDetection_ = "";
      /**
       * <pre>
       * 最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       * @return Whether the iocLastDetection field is set.
       */
      public boolean hasIocLastDetection() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       * @return The iocLastDetection.
       */
      public java.lang.String getIocLastDetection() {
        java.lang.Object ref = iocLastDetection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocLastDetection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       * @return The bytes for iocLastDetection.
       */
      public com.google.protobuf.ByteString
          getIocLastDetectionBytes() {
        java.lang.Object ref = iocLastDetection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocLastDetection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       * @param value The iocLastDetection to set.
       * @return This builder for chaining.
       */
      public Builder setIocLastDetection(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocLastDetection_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocLastDetection() {
        iocLastDetection_ = getDefaultInstance().getIocLastDetection();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最近检测时间	最后一次检测到攻击的时间
       * </pre>
       *
       * <code>required string ioc_last_detection = 9;</code>
       * @param value The bytes for iocLastDetection to set.
       * @return This builder for chaining.
       */
      public Builder setIocLastDetectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocLastDetection_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object iocRefer_ = "";
      /**
       * <pre>
       * 参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       * @return Whether the iocRefer field is set.
       */
      public boolean hasIocRefer() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       * @return The iocRefer.
       */
      public java.lang.String getIocRefer() {
        java.lang.Object ref = iocRefer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocRefer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       * @return The bytes for iocRefer.
       */
      public com.google.protobuf.ByteString
          getIocReferBytes() {
        java.lang.Object ref = iocRefer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocRefer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       * @param value The iocRefer to set.
       * @return This builder for chaining.
       */
      public Builder setIocRefer(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocRefer_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocRefer() {
        iocRefer_ = getDefaultInstance().getIocRefer();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 参考文档报告	
       * </pre>
       *
       * <code>optional string ioc_refer = 10;</code>
       * @param value The bytes for iocRefer to set.
       * @return This builder for chaining.
       */
      public Builder setIocReferBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocRefer_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object iocReportData_ = "";
      /**
       * <pre>
       * 报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       * @return Whether the iocReportData field is set.
       */
      public boolean hasIocReportData() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       * @return The iocReportData.
       */
      public java.lang.String getIocReportData() {
        java.lang.Object ref = iocReportData_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocReportData_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       * @return The bytes for iocReportData.
       */
      public com.google.protobuf.ByteString
          getIocReportDataBytes() {
        java.lang.Object ref = iocReportData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocReportData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       * @param value The iocReportData to set.
       * @return This builder for chaining.
       */
      public Builder setIocReportData(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocReportData_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocReportData() {
        iocReportData_ = getDefaultInstance().getIocReportData();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报告发布时间	
       * </pre>
       *
       * <code>optional string ioc_report_data = 11;</code>
       * @param value The bytes for iocReportData to set.
       * @return This builder for chaining.
       */
      public Builder setIocReportDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocReportData_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object iocReportVendor_ = "";
      /**
       * <pre>
       * 报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       * @return Whether the iocReportVendor field is set.
       */
      public boolean hasIocReportVendor() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       * @return The iocReportVendor.
       */
      public java.lang.String getIocReportVendor() {
        java.lang.Object ref = iocReportVendor_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocReportVendor_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       * @return The bytes for iocReportVendor.
       */
      public com.google.protobuf.ByteString
          getIocReportVendorBytes() {
        java.lang.Object ref = iocReportVendor_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocReportVendor_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       * @param value The iocReportVendor to set.
       * @return This builder for chaining.
       */
      public Builder setIocReportVendor(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocReportVendor_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocReportVendor() {
        iocReportVendor_ = getDefaultInstance().getIocReportVendor();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 报告发布厂商	
       * </pre>
       *
       * <code>optional string ioc_report_vendor = 12;</code>
       * @param value The bytes for iocReportVendor to set.
       * @return This builder for chaining.
       */
      public Builder setIocReportVendorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocReportVendor_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object iocType_ = "";
      /**
       * <pre>
       * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       * @return Whether the iocType field is set.
       */
      public boolean hasIocType() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       * @return The iocType.
       */
      public java.lang.String getIocType() {
        java.lang.Object ref = iocType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       * @return The bytes for iocType.
       */
      public com.google.protobuf.ByteString
          getIocTypeBytes() {
        java.lang.Object ref = iocType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       * @param value The iocType to set.
       * @return This builder for chaining.
       */
      public Builder setIocType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocType_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocType() {
        iocType_ = getDefaultInstance().getIocType();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IOC类型	"General：混合功能远控端；Connect：受控后上报配置信息，用于上线和命令控制分离的场景；Download：下载恶意软件组件；C2：命令控制通道；Dataleak：连接数据放置功能的服务器。"
       * </pre>
       *
       * <code>required string ioc_type = 13;</code>
       * @param value The bytes for iocType to set.
       * @return This builder for chaining.
       */
      public Builder setIocTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocType_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private boolean iocTargeted_ ;
      /**
       * <pre>
       * 定向攻击标识	True/False
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       * @return Whether the iocTargeted field is set.
       */
      @java.lang.Override
      public boolean hasIocTargeted() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 定向攻击标识	True/False
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       * @return The iocTargeted.
       */
      @java.lang.Override
      public boolean getIocTargeted() {
        return iocTargeted_;
      }
      /**
       * <pre>
       * 定向攻击标识	True/False
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       * @param value The iocTargeted to set.
       * @return This builder for chaining.
       */
      public Builder setIocTargeted(boolean value) {

        iocTargeted_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 定向攻击标识	True/False
       * </pre>
       *
       * <code>required bool ioc_targeted = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocTargeted() {
        bitField0_ = (bitField0_ & ~0x00002000);
        iocTargeted_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object iocMaliciousFamily_ = "";
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       * @return Whether the iocMaliciousFamily field is set.
       */
      public boolean hasIocMaliciousFamily() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       * @return The iocMaliciousFamily.
       */
      public java.lang.String getIocMaliciousFamily() {
        java.lang.Object ref = iocMaliciousFamily_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocMaliciousFamily_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       * @return The bytes for iocMaliciousFamily.
       */
      public com.google.protobuf.ByteString
          getIocMaliciousFamilyBytes() {
        java.lang.Object ref = iocMaliciousFamily_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocMaliciousFamily_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       * @param value The iocMaliciousFamily to set.
       * @return This builder for chaining.
       */
      public Builder setIocMaliciousFamily(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocMaliciousFamily_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocMaliciousFamily() {
        iocMaliciousFamily_ = getDefaultInstance().getIocMaliciousFamily();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 恶意代码家族	
       * </pre>
       *
       * <code>optional string ioc_malicious_family = 15;</code>
       * @param value The bytes for iocMaliciousFamily to set.
       * @return This builder for chaining.
       */
      public Builder setIocMaliciousFamilyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocMaliciousFamily_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptCampaign_ = "";
      /**
       * <pre>
       * APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       * @return Whether the iocAptCampaign field is set.
       */
      public boolean hasIocAptCampaign() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       * @return The iocAptCampaign.
       */
      public java.lang.String getIocAptCampaign() {
        java.lang.Object ref = iocAptCampaign_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptCampaign_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       * @return The bytes for iocAptCampaign.
       */
      public com.google.protobuf.ByteString
          getIocAptCampaignBytes() {
        java.lang.Object ref = iocAptCampaign_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptCampaign_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       * @param value The iocAptCampaign to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptCampaign(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptCampaign_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAptCampaign() {
        iocAptCampaign_ = getDefaultInstance().getIocAptCampaign();
        bitField0_ = (bitField0_ & ~0x00008000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT组织名称	对应actor、primary_name
       * </pre>
       *
       * <code>optional string ioc_apt_campaign = 16;</code>
       * @param value The bytes for iocAptCampaign to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptCampaignBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptCampaign_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptAlias_ = "";
      /**
       * <pre>
       * APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       * @return Whether the iocAptAlias field is set.
       */
      public boolean hasIocAptAlias() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       * @return The iocAptAlias.
       */
      public java.lang.String getIocAptAlias() {
        java.lang.Object ref = iocAptAlias_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptAlias_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       * @return The bytes for iocAptAlias.
       */
      public com.google.protobuf.ByteString
          getIocAptAliasBytes() {
        java.lang.Object ref = iocAptAlias_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptAlias_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       * @param value The iocAptAlias to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptAlias(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptAlias_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAptAlias() {
        iocAptAlias_ = getDefaultInstance().getIocAptAlias();
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT组织别名	
       * </pre>
       *
       * <code>optional string ioc_apt_alias = 17;</code>
       * @param value The bytes for iocAptAlias to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptAliasBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptAlias_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptCountry_ = "";
      /**
       * <pre>
       * APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       * @return Whether the iocAptCountry field is set.
       */
      public boolean hasIocAptCountry() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       * @return The iocAptCountry.
       */
      public java.lang.String getIocAptCountry() {
        java.lang.Object ref = iocAptCountry_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptCountry_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       * @return The bytes for iocAptCountry.
       */
      public com.google.protobuf.ByteString
          getIocAptCountryBytes() {
        java.lang.Object ref = iocAptCountry_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptCountry_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       * @param value The iocAptCountry to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptCountry(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptCountry_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAptCountry() {
        iocAptCountry_ = getDefaultInstance().getIocAptCountry();
        bitField0_ = (bitField0_ & ~0x00020000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT所属国家	
       * </pre>
       *
       * <code>optional string ioc_apt_country = 18;</code>
       * @param value The bytes for iocAptCountry to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptCountryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptCountry_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }

      private java.lang.Object iocAptMission_ = "";
      /**
       * <pre>
       * APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       * @return Whether the iocAptMission field is set.
       */
      public boolean hasIocAptMission() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       * @return The iocAptMission.
       */
      public java.lang.String getIocAptMission() {
        java.lang.Object ref = iocAptMission_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAptMission_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       * @return The bytes for iocAptMission.
       */
      public com.google.protobuf.ByteString
          getIocAptMissionBytes() {
        java.lang.Object ref = iocAptMission_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAptMission_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       * @param value The iocAptMission to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptMission(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptMission_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAptMission() {
        iocAptMission_ = getDefaultInstance().getIocAptMission();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * APT行动名称	
       * </pre>
       *
       * <code>optional string ioc_apt_mission = 19;</code>
       * @param value The bytes for iocAptMission to set.
       * @return This builder for chaining.
       */
      public Builder setIocAptMissionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAptMission_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }

      private java.lang.Object iocRat_ = "";
      /**
       * <pre>
       * 远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       * @return Whether the iocRat field is set.
       */
      public boolean hasIocRat() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       * @return The iocRat.
       */
      public java.lang.String getIocRat() {
        java.lang.Object ref = iocRat_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocRat_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       * @return The bytes for iocRat.
       */
      public com.google.protobuf.ByteString
          getIocRatBytes() {
        java.lang.Object ref = iocRat_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocRat_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       * @param value The iocRat to set.
       * @return This builder for chaining.
       */
      public Builder setIocRat(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocRat_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocRat() {
        iocRat_ = getDefaultInstance().getIocRat();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 远控工具	
       * </pre>
       *
       * <code>optional string ioc_rat = 20;</code>
       * @param value The bytes for iocRat to set.
       * @return This builder for chaining.
       */
      public Builder setIocRatBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocRat_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private java.lang.Object iocAttackMethod_ = "";
      /**
       * <pre>
       * 攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       * @return Whether the iocAttackMethod field is set.
       */
      public boolean hasIocAttackMethod() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       * @return The iocAttackMethod.
       */
      public java.lang.String getIocAttackMethod() {
        java.lang.Object ref = iocAttackMethod_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAttackMethod_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       * @return The bytes for iocAttackMethod.
       */
      public com.google.protobuf.ByteString
          getIocAttackMethodBytes() {
        java.lang.Object ref = iocAttackMethod_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAttackMethod_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       * @param value The iocAttackMethod to set.
       * @return This builder for chaining.
       */
      public Builder setIocAttackMethod(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAttackMethod_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAttackMethod() {
        iocAttackMethod_ = getDefaultInstance().getIocAttackMethod();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击手法	WEB攻击渗透、…
       * </pre>
       *
       * <code>optional string ioc_attack_method = 21;</code>
       * @param value The bytes for iocAttackMethod to set.
       * @return This builder for chaining.
       */
      public Builder setIocAttackMethodBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAttackMethod_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }

      private java.lang.Object iocVul_ = "";
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       * @return Whether the iocVul field is set.
       */
      public boolean hasIocVul() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       * @return The iocVul.
       */
      public java.lang.String getIocVul() {
        java.lang.Object ref = iocVul_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocVul_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       * @return The bytes for iocVul.
       */
      public com.google.protobuf.ByteString
          getIocVulBytes() {
        java.lang.Object ref = iocVul_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocVul_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       * @param value The iocVul to set.
       * @return This builder for chaining.
       */
      public Builder setIocVul(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocVul_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocVul() {
        iocVul_ = getDefaultInstance().getIocVul();
        bitField0_ = (bitField0_ & ~0x00200000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联漏洞	攻击者所用到的漏洞
       * </pre>
       *
       * <code>optional string ioc_vul = 22;</code>
       * @param value The bytes for iocVul to set.
       * @return This builder for chaining.
       */
      public Builder setIocVulBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocVul_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }

      private java.lang.Object iocAffectedSector_ = "";
      /**
       * <pre>
       * 影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       * @return Whether the iocAffectedSector field is set.
       */
      public boolean hasIocAffectedSector() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       * @return The iocAffectedSector.
       */
      public java.lang.String getIocAffectedSector() {
        java.lang.Object ref = iocAffectedSector_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAffectedSector_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       * @return The bytes for iocAffectedSector.
       */
      public com.google.protobuf.ByteString
          getIocAffectedSectorBytes() {
        java.lang.Object ref = iocAffectedSector_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAffectedSector_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       * @param value The iocAffectedSector to set.
       * @return This builder for chaining.
       */
      public Builder setIocAffectedSector(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAffectedSector_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAffectedSector() {
        iocAffectedSector_ = getDefaultInstance().getIocAffectedSector();
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 影响行业	
       * </pre>
       *
       * <code>optional string ioc_affected_sector = 23;</code>
       * @param value The bytes for iocAffectedSector to set.
       * @return This builder for chaining.
       */
      public Builder setIocAffectedSectorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAffectedSector_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }

      private java.lang.Object iocAffectedProduct_ = "";
      /**
       * <pre>
       * 影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       * @return Whether the iocAffectedProduct field is set.
       */
      public boolean hasIocAffectedProduct() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       * @return The iocAffectedProduct.
       */
      public java.lang.String getIocAffectedProduct() {
        java.lang.Object ref = iocAffectedProduct_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocAffectedProduct_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       * @return The bytes for iocAffectedProduct.
       */
      public com.google.protobuf.ByteString
          getIocAffectedProductBytes() {
        java.lang.Object ref = iocAffectedProduct_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocAffectedProduct_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       * @param value The iocAffectedProduct to set.
       * @return This builder for chaining.
       */
      public Builder setIocAffectedProduct(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocAffectedProduct_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocAffectedProduct() {
        iocAffectedProduct_ = getDefaultInstance().getIocAffectedProduct();
        bitField0_ = (bitField0_ & ~0x00800000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 影响平台	
       * </pre>
       *
       * <code>optional string ioc_affected_product = 24;</code>
       * @param value The bytes for iocAffectedProduct to set.
       * @return This builder for chaining.
       */
      public Builder setIocAffectedProductBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocAffectedProduct_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }

      private java.lang.Object iocDetailInfo_ = "";
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       * @return Whether the iocDetailInfo field is set.
       */
      public boolean hasIocDetailInfo() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       * @return The iocDetailInfo.
       */
      public java.lang.String getIocDetailInfo() {
        java.lang.Object ref = iocDetailInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iocDetailInfo_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       * @return The bytes for iocDetailInfo.
       */
      public com.google.protobuf.ByteString
          getIocDetailInfoBytes() {
        java.lang.Object ref = iocDetailInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iocDetailInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       * @param value The iocDetailInfo to set.
       * @return This builder for chaining.
       */
      public Builder setIocDetailInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iocDetailInfo_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearIocDetailInfo() {
        iocDetailInfo_ = getDefaultInstance().getIocDetailInfo();
        bitField0_ = (bitField0_ & ~0x01000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案："
       * </pre>
       *
       * <code>required string ioc_detail_info = 25;</code>
       * @param value The bytes for iocDetailInfo to set.
       * @return This builder for chaining.
       */
      public Builder setIocDetailInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iocDetailInfo_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:IOC_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:IOC_ALERT_INFO)
    private static final IocAlertInfo.IOC_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IocAlertInfo.IOC_ALERT_INFO();
    }

    public static IocAlertInfo.IOC_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IOC_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IOC_ALERT_INFO>() {
      @java.lang.Override
      public IOC_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IOC_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IOC_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IocAlertInfo.IOC_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IOC_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_IOC_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024IOC_ALERT_INFO.proto\"\334\004\n\016IOC_ALERT_INF" +
      "O\022\016\n\006ioc_id\030\001 \002(\t\022\021\n\tioc_value\030\002 \002(\t\022\024\n\014" +
      "ioc_category\030\003 \002(\t\022\027\n\017ioc_public_date\030\004 " +
      "\002(\004\022\026\n\016ioc_alert_name\030\005 \002(\t\022\032\n\022ioc_curre" +
      "nt_status\030\006 \002(\t\022\017\n\007ioc_hot\030\007 \001(\010\022\026\n\016ioc_" +
      "first_seen\030\010 \001(\t\022\032\n\022ioc_last_detection\030\t" +
      " \002(\t\022\021\n\tioc_refer\030\n \001(\t\022\027\n\017ioc_report_da" +
      "ta\030\013 \001(\t\022\031\n\021ioc_report_vendor\030\014 \001(\t\022\020\n\010i" +
      "oc_type\030\r \002(\t\022\024\n\014ioc_targeted\030\016 \002(\010\022\034\n\024i" +
      "oc_malicious_family\030\017 \001(\t\022\030\n\020ioc_apt_cam" +
      "paign\030\020 \001(\t\022\025\n\rioc_apt_alias\030\021 \001(\t\022\027\n\017io" +
      "c_apt_country\030\022 \001(\t\022\027\n\017ioc_apt_mission\030\023" +
      " \001(\t\022\017\n\007ioc_rat\030\024 \001(\t\022\031\n\021ioc_attack_meth" +
      "od\030\025 \001(\t\022\017\n\007ioc_vul\030\026 \001(\t\022\033\n\023ioc_affecte" +
      "d_sector\030\027 \001(\t\022\034\n\024ioc_affected_product\030\030" +
      " \001(\t\022\027\n\017ioc_detail_info\030\031 \002(\tB\016B\014IocAler" +
      "tInfo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_IOC_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IOC_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_IOC_ALERT_INFO_descriptor,
        new java.lang.String[] { "IocId", "IocValue", "IocCategory", "IocPublicDate", "IocAlertName", "IocCurrentStatus", "IocHot", "IocFirstSeen", "IocLastDetection", "IocRefer", "IocReportData", "IocReportVendor", "IocType", "IocTargeted", "IocMaliciousFamily", "IocAptCampaign", "IocAptAlias", "IocAptCountry", "IocAptMission", "IocRat", "IocAttackMethod", "IocVul", "IocAffectedSector", "IocAffectedProduct", "IocDetailInfo", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
