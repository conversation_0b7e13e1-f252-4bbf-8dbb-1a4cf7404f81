﻿# Create Space 
CREATE SPACE `apt_target_judgment_graph` (partition_num = 1, replica_factor = 1, charset = utf8, collate = utf8_bin, vid_type = FIXED_STRING(200)) comment = 'APT威胁画像图谱';
:sleep 20;
USE `apt_target_judgment_graph`;

# Create Tag: 
CREATE TAG `APP` ( `app_name` string NULL COMMENT "应用名称", `app_type_id` int16 NULL COMMENT "应用类型ID", `app_type` string NULL COMMENT "应用类型", `app_class_id` int16 NULL COMMENT "应用分类ID", `app_class` string NULL COMMENT "应用分类", `is_encrypted` bool NOT NULL DEFAULT false COMMENT "是否加密") ttl_duration = 0, ttl_col = "", comment = "应用信息";
CREATE TAG `APT_GROUP` ( `apt_name` fixed_string(60) NULL COMMENT "APT名称", `apt_alias` string NULL COMMENT "组织别名", `apt_desc` string NULL COMMENT "APT详细描述", `apt_country` fixed_string(50) NULL COMMENT "APT所属国家") ttl_duration = 0, ttl_col = "";
CREATE TAG `ATTACK` ( `attack_id` string NULL COMMENT "告警唯一ID(guid)", `attack_time` timestamp NULL COMMENT "告警时间(毫秒级)", `threat_type` string NULL COMMENT "威胁类型", `kill_chain` string NULL COMMENT "杀伤链标签", `confidence` string NULL COMMENT "置信度", `vendor_id` string NULL COMMENT "供应商ID", `detect_type` string NULL COMMENT "检测类型", `aip_aport_app_vport_vip` string NULL COMMENT "五元组", `pcap_file` string NULL COMMENT "原始文件路径", `labels` string NULL COMMENT "关联标签ID集合", `pcap_name` string NULL COMMENT "原始文件名") ttl_duration = 0, ttl_col = "";
CREATE TAG `CERT` ( `cert_hash` string NULL COMMENT "证书哈希值", `issuer` string NULL COMMENT "颁发者", `subject` string NULL COMMENT "使用者", `algorithm_id` string NULL COMMENT "证书使用算法", `fp_alg` string NULL COMMENT "指纹算法", `not_before` datetime NULL COMMENT "证书生效日期", `not_after` datetime NULL COMMENT "证书失效日期", `version` string NULL COMMENT "证书版本") ttl_duration = 0, ttl_col = "";
CREATE TAG `DEVICE` ( `device_md5` string NULL COMMENT "设备MD5", `vendor` string NULL COMMENT "设备/软件厂商", `device_type` string NULL COMMENT "设备/软件类型", `model` string NULL COMMENT "设备型号/软件版本") ttl_duration = 0, ttl_col = "";
CREATE TAG `DOMAIN` ( `domain_addr` string NULL COMMENT "域名地址") ttl_duration = 0, ttl_col = "";
CREATE TAG `EMAIL` ( `email_addr` string NULL COMMENT "邮箱地址", `user_name` string NULL COMMENT "用户名称") ttl_duration = 0, ttl_col = "";
CREATE TAG `FILE` ( `file_md5` fixed_string(50) NULL COMMENT "文件MD5", `file_sha1` string NULL COMMENT "文件SHA1", `file_sha256` string NULL COMMENT "文件SHA256", `file_sha512` string NULL COMMENT "文件SHA512", `file_crc32` string NULL COMMENT "文件CRC32", `file_size` int64 NULL COMMENT "文件大小", `file_path` string NULL COMMENT "文件路径") ttl_duration = 0, ttl_col = "";
CREATE TAG `IP` ( `ip_addr` string NULL COMMENT "IP地址", `version` fixed_string(5) NULL COMMENT "IP版本号", `city` fixed_string(20) NULL COMMENT "所属城市", `country` fixed_string(20) NULL COMMENT "所属国家", `stat` fixed_string(20) NULL COMMENT "省份、州名", `longitude` double NULL COMMENT "经度", `latitude` double NULL COMMENT "纬度", `ISP` string NULL COMMENT "运营商", `asn` string NULL COMMENT "AS信息", `org` string NULL COMMENT "所属机构名") ttl_duration = 0, ttl_col = "";
CREATE TAG `LABEL` ( `label_id` int32 NULL COMMENT "标签ID", `label_name` string NULL COMMENT "标签名称") ttl_duration = 0, ttl_col = "";
CREATE TAG `MAIL` ( `subject` string NULL COMMENT "主题", `industry` string NULL COMMENT "所属行业", `intents` string NULL COMMENT "邮件意图") ttl_duration = 0, ttl_col = "";
CREATE TAG `ORG` ( `org_name` string NULL COMMENT "组织机构名称") ttl_duration = 0, ttl_col = "";
CREATE TAG `SSLFINGER` ( `ja3_hash` string NULL COMMENT "JA3指纹hash") ttl_duration = 0, ttl_col = "";
CREATE TAG `UA` ( `ua_str` string NULL COMMENT "ua字符串", `os_name` string NULL COMMENT "使用操作系统", `device_name` string NULL COMMENT "设备名称") ttl_duration = 0, ttl_col = "";
CREATE TAG `URL` ( `url_path` string NULL COMMENT "URL全路径信息", `uri` string NULL COMMENT "uri信息") ttl_duration = 0, ttl_col = "";

# Create Edge:
CREATE EDGE `app_belong_to_server` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `app_connect_cert` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `apt_judge_domain` ( `model_name` string NULL COMMENT "使用模型名称") ttl_duration = 0, ttl_col = "";
CREATE EDGE `apt_judge_ip` ( `model_name` string NULL COMMENT "模型名称") ttl_duration = 0, ttl_col = "", comment = "模型研判关联域名";
CREATE EDGE `attack_belong_to_apt_group` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `attack_to` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `attack_to_device` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `cert_belong_to_org` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `cert_connect_sni` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `cert_connect_sslfinger` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_http_connect_domain` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_http_connect_url` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_query_dns_server` ( `dns_type` string NULL COMMENT "DNS查询类型", `answer_type` string NULL COMMENT "DNS应答类型") ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_query_domain` ( `dns_type` string NULL COMMENT "DNS查询类型", `answer_type` string NULL COMMENT "DNS应答类型") ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_use_app` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_use_cert` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_use_sslfinger` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `client_use_ua` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `dns_server_resolves_domain` ( `dns_type` string NULL COMMENT "DNS查询类型", `answer_type` string NULL COMMENT "DNS应答类型") ttl_duration = 0, ttl_col = "";
CREATE EDGE `domain_belong_to_apt` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `file_connect_domain` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `file_connect_ip` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `file_connect_url` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `http_connect` ( `uri` string NULL COMMENT "统一资源标识符", `host` string NULL COMMENT "主机名", `cookie` string NULL COMMENT "cookie", `agent` string NULL COMMENT "UA", `referer` string NULL COMMENT "引用URL", `xff` string NULL COMMENT "X-Forwarded-For头", `req_data` string NULL COMMENT "请求数据", `response_data` string NULL COMMENT "响应数据", `method` fixed_string(10) NULL COMMENT "请求方法", `status` int16 NULL COMMENT "状态码", `content_type` string NULL COMMENT "内容类型") ttl_duration = 0, ttl_col = "";
CREATE EDGE `include_file` ( `file_name` string NULL COMMENT "包含的文件名") ttl_duration = 0, ttl_col = "";
CREATE EDGE `ip_belong_to_apt` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `ip_belong_to_org` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `make_attack` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `parse_to` ( `dns_server` string NULL COMMENT "解析服务器IP", `final_parse` bool NULL COMMENT "是否为最终解析") ttl_duration = 0, ttl_col = "";
CREATE EDGE `receive_file` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `receive_mail` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `send_file` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `send_mail` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `server_http_connect_domain` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `server_use_cert` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `server_use_sslfinger` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `sslfinger_connect_sni` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `ua_connect_domain` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `sender_send_file` () ttl_duration = 0, ttl_col = "";
CREATE EDGE `receiver_receive_file` () ttl_duration = 0, ttl_col = "";
:sleep 20;

# Create Index:
CREATE TAG INDEX `apt_name_index` ON `APT_GROUP` ( `apt_name`(60));