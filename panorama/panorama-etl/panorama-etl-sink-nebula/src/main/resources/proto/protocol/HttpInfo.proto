syntax = "proto2";

message HttpInfo
{
	optional bytes    host                        = 1;    // 主机名/域名
	optional bytes    uri                         = 2;    // 统一资源标识符
	optional bytes    varConEnc                   = 3;    // 变体内容编码
	optional bytes    authInfo                    = 4;    // 认证信息
	optional bytes    conEncByCli                 = 5;    // 客户端的内容编码方式
	optional bytes    conLan                      = 6;    // 内容语言
	optional uint32   conLenByCli                 = 7;    // 客户端发送的内容长度
	optional bytes    conURL                      = 8;    // 内容URL
	optional bytes    conMD5                      = 9;    // 内容的MD5值
	optional bytes    conType                     = 10;   // 内容类型(Content-Type)
	optional bytes    cookie                      = 11;   // Cookie信息
	optional bytes    cookie2                     = 12;   // Cookie2信息
	optional bytes    date                        = 13;   // 日期
	optional bytes    from                        = 14;   // 发送者
	optional bytes    loc                         = 15;   // 位置(Location)
	optional bytes    proAuthen                   = 16;   // 代理认证
	optional bytes    proAuthor                   = 17;   // 代理授权
	optional bytes    refURL                      = 18;   // 引用URL(Referer)
	optional bytes    srv                         = 19;   // 服务器信息
	optional uint32   srvCnt                      = 20;   // 服务器计数
	optional bytes    setCookieKey                = 21;   // Set-Cookie键
	optional bytes    setCookieVal                = 22;   // Set-Cookie值
	optional bytes    traEnc                      = 23;   // 传输编码(Transfer-Encoding)
	optional bytes    usrAge                      = 24;   // 用户代理(User-Agent)
	optional bytes    via                         = 25;   // 经过的代理服务器
	optional bytes    xForFor                     = 26;   // X-Forwarded-For头
	optional uint32   statCode                    = 27;   // HTTP状态码
	optional bytes    met                         = 28;   // HTTP方法(GET/POST等)
	optional bytes    srvAge                      = 29;   // 服务器Age头
	optional bytes    proAuth                     = 30;   // 代理授权
	optional bytes    xPowBy                      = 31;   // X-Powered-By头
	optional bytes    extHdrs                     = 32;   // 额外的HTTP头
	optional bytes    rangeofCli                  = 33;   // 客户端请求的Range
	optional uint32   viaCnt                      = 34;   // Via头计数
	optional uint32   statCodeCnt                 = 35;   // 状态码计数
	optional bytes    reqVer                      = 36;   // 请求的HTTP版本
	optional bytes    reqHead                     = 37;   // 请求头
	optional uint32   reqHeadMd5                  = 38;   // 请求头的MD5值
	optional bytes    cacConUp                    = 39;   // 上行缓存控制(Cache-Control)
	optional bytes    conUp                       = 40;   // 上行连接头(Connection)
	optional bytes    praUp                       = 41;   // 上行Pragma头
	optional bytes    upg                         = 42;   // Upgrade头
	optional bytes    accChaUp                    = 43;   // 上行Accept-Charset
	optional bytes    acctRanUp                   = 44;   // 上行Accept-Range
	optional bytes    ifMat                       = 45;   // If-Match头
	optional bytes    ifModSin                    = 46;   // If-Modified-Since
	optional bytes    ifNonMat                    = 47;   // If-None-Match
	optional bytes    ifRan                       = 48;   // If-Range
	optional uint64   ifUnModSin                  = 49;   // If-Unmodified-Since
	optional uint32   maxFor                      = 50;   // Max-Forwards
	optional bytes    te                          = 51;   // TE头(Transfer-Encoding扩展)
	optional bytes    cacConDown                  = 52;   // 下行缓存控制(Cache-Control)
	optional bytes    conDown                     = 53;   // 下行连接头(Connection)
	optional bytes    praDown                     = 54;   // 下行Pragma头
	optional bytes    trail                       = 55;   // Trailer头
	optional bytes    accRanDown                  = 56;   // 下行Accept-Range
	optional bytes    eTag                        = 57;   // ETag头
	optional bytes    retAft                      = 58;   // Retry-After头
	optional bytes    wwwAuth                     = 59;   // WWW-Authenticate头
	optional bytes    refresh                     = 60;   // Refresh头
	optional bytes    conTypDown                  = 61;   // 下行内容类型
	optional bytes    allow                       = 62;   // Allow头
	optional uint64   expires                     = 63;   // 过期时间
	optional uint64   lasMod                      = 64;   // 最后修改时间
	optional bytes    accChaDown                  = 65;   // 下行Accept-Charset
	optional bytes    httpRelKey                  = 66;   // HTTP相关键
	optional bytes    httpEmbPro                  = 67;   // HTTP嵌入协议
	optional bytes    fullTextHeader              = 68;   // 完整文本头
	optional uint32   fullTextLen                 = 69;   // 完整文本长度
	optional bytes    fileName                    = 70;   // 文件名
	optional bytes    contDown                    = 71;   // 下行内容
	optional uint32   reqVerCnt                   = 72;   // 请求版本计数
	optional uint32   metCnt                      = 73;   // 方法计数
	optional uint32   reqHeadCnt                  = 74;   // 请求头计数
	optional bytes    accByCli                    = 75;   // 客户端Accept头
	optional bytes    accLanByCli                 = 76;   // 客户端Accept-Language
	optional bytes    accEncByCli                 = 77;   // 客户端Accept-Encoding
	optional uint32   authCnt                     = 78;   // 认证计数
	optional uint32   hostCnt                     = 79;   // 主机计数
	optional uint32   uriCnt                      = 80;   // URI计数
	optional bytes    uriPath                     = 81;   // URI路径
	optional uint32   uriPathCnt                  = 82;   // URI路径计数
	repeated bytes    uriKey                      = 83;   // URI键
	optional uint32   uriKeyCnt                   = 84;   // URI键计数
	optional bytes    uriSearch                   = 85;   // URI搜索部分
	optional uint32   usrAgeCnt                   = 86;   // 用户代理计数
	optional bytes    user                        = 87;   // 用户信息
	optional uint32   userCnt                     = 88;   // 用户计数
	optional bytes    reqBody                     = 89;   // 请求体
	optional bytes    reqBodyN                    = 90;   // 请求体N
	optional bytes    conMD5ByCli                 = 91;   // 客户端内容MD5
	repeated bytes    cookieKey                   = 92;   // Cookie键
	optional uint32   cookieKeyCnt                = 93;   // Cookie键计数
	optional bytes    imei                        = 94;   // IMEI(国际移动设备识别码)
	optional bytes    imsi                        = 95;   // IMSI(国际移动用户识别码)
	optional uint32   xForForCnt                  = 96;   // X-Forwarded-For计数
	optional bytes    respVer                     = 97;   // 响应HTTP版本
	optional uint32   respVerCnt                  = 98;   // 响应版本计数
	optional bytes    respHead                    = 99;   // 响应头
	optional bytes    respHeadMd5                 = 100;  // 响应头MD5
	optional uint32   respHeadCnt                 = 101;  // 响应头计数
	optional bytes    respBody                    = 102;  // 响应体
	optional bytes    respBodyN                   = 103;  // 响应体N
	optional bytes    conMD5BySrv                 = 104;  // 服务器内容MD5
	optional uint32   conEncBySrv                 = 105;  // 服务器内容编码
	optional bytes    Location                    = 106;  // 位置头
	optional bytes    xSinHol                     = 107;  // X-Single-Header
	optional uint32   conEncBySrvCnt              = 108;  // 服务器内容编码计数
	optional uint32   conLenSrv                   = 109;  // 服务器内容长度
	optional bytes    conDispUp                   = 110;  // 上行内容处置(Content-Disposition)
	optional bytes    conDispDown                 = 111;  // 下行内容处置
	optional bytes    authUser                    = 112;  // 认证用户
	optional uint32   authUserCount               = 113;  // 认证用户计数
	optional uint32   bodyServerMd5Count          = 114;  // 服务器响应体MD5计数
	optional bytes    contentDispositionClient    = 115;  // 客户端内容处置
	optional bytes    contentDispositionServer    = 116;  // 服务器内容处置
	optional bytes    filePath                    = 117;  // 文件路径
	optional bytes    setCookie                   = 118;  // Set-Cookie头
	optional bytes    title                       = 119;  // 标题
}