syntax = "proto2";

message CommonInfo
{
	optional	bytes		srcMacOui			=	1;
	optional	bytes		dstMacOui			=	2;
	optional	bytes		lineName1			=	3;
	optional	bytes		lineName2			=	4;
	optional	uint64		begTime				=	5;
	optional	bytes		tagsRule			=	6;
	optional	uint64		endTime				=	7;
	optional	uint32		comDur				=	8;
	optional	bytes		meanID				=	9;
	optional	bytes		siteID				=	10;
	optional	bytes		unitID				=	11;
	optional	bytes		taskID				=	12;
	optional	uint64		guid				=	13;
	optional	uint64		stortime			=	14;
	optional	bytes		mdsecdeg			=	15;
	optional	bytes		filesecdeg			=	16;
	optional	bytes		secdegpro			=	17;
	optional	uint32		commipVer			=	18;
	optional	uint64		commsrcAddr			=	19;
	optional	uint64		commdstAddr			=	20;
	optional	uint32		commsrcPort			=	21;
	optional	uint32		commdstPort			=	22;
	optional	uint32		commprotNum			=	23;
	optional	bytes		commsrcAddrV6		=	24;
	optional	bytes		commdstAddrV6		=	25;
	optional	bytes		commprotInfo 		=	26;
	optional	bytes		commprotType		=	27;
	optional	bytes		commprotName		=	28;
	optional	uint32		commmulRouFlag		=	29;
	optional	uint32		commintFlag			=	30;
	optional	uint32		commstrDirec		=	31;
	optional	uint32		commpktNum			=	32;
	optional	uint64		commpayLen			=	33;
	optional	uint64		commstreamId		=	34;
	optional	bytes		commetags			=	35;
	optional	bytes		commttags			=	36;
	optional	bytes		commatags			=	37;
	optional	bytes		commutags			=	38;
	optional	uint32		commlable1			=	39;
	optional	uint32		commlable2			=	40;
	optional	uint32		commlable3			=	41;
	optional	uint32		commlable4			=	42;
	optional	uint32		commvlanID1			=	43;
	optional	uint32		commvlanID2			=	44;
	optional	bytes		commsrcMac			=	45;
	optional	bytes		commdstMac			=	46;
	optional	uint32		commtunnelID		=	47;
	optional	bytes		commsrcCountry		=	48;
	optional	bytes		commsrcState		=	49;
	optional	bytes		commsrcCity			=	50;
	optional	float		commsrcLongitude	=	51;
	optional	float		commsrcLatitude		=	52;
	optional	bytes		commsrcISP			=	53;
	optional	uint32		commsrcASN			=	54;
	optional	bytes		commdstCountry		=	55;
	optional	bytes		commdstState		=	56;
	optional	bytes		commdstCity			=	57;
	optional	float		commdstLongitude	=	58;
	optional	float		commdstLatitude		=	59;
	optional	bytes		commdstISP			=	60;
	optional	uint32		commdstASN			=	61;
	optional	uint32		outAddrType			=	62;
	optional	uint32		outSrcAddr			=	63;
	optional	uint32		outDstAddr			=	64;
	optional	bytes		outer_ipv6_src		=	65;
	optional	bytes		outer_ipv6_dst		=	66;
	optional	uint32		outSrcPort			=	67;
	optional	uint32		outDstPort			=	68;
	optional	uint32		outTransProto		=	69;
	optional	uint64		captureTime			=	70;
}
