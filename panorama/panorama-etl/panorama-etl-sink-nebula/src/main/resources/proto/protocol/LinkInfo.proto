syntax = "proto2";

message LinkInfo
{
	optional	bytes	portInfo			=	1;
	optional	bytes	portInfoAtt			=	2;
	optional	uint32	upPayLen			=	3;
	optional	uint64	downPayLen			=	4;
	optional	bytes	tcpflag				=	5;
	optional	uint64	upLinkPktNum		=	6;
	optional	uint64	upLinkSize			=	7;
	optional	uint32	upLinkBigPktLen		=	8;
	optional	uint32	upLinkSmaPktLen		=	9;
	optional	uint64	upLinkBigPktInt		=	10;
	optional	uint64	upLinkSmaPktInt		=	11;
	optional	uint32	downLinkPktNum		=	12;
	optional	uint32	downLinkSize		=	13;
	optional	uint32	downLinkBigPktLen	=	14;
	optional	uint32	downLinkSmaPktLen	=	15;
	optional	uint64	downLinkBigPktInt	=	16;
	optional	uint64	downLinkSmaPktInt	=	17;
	optional	uint32	firTtlByCli			=	18;
	optional	uint32	firTtlBySrv			=	19;
	optional	uint32	appDirec			=	20;
	optional	uint32	tcpFlagsFinCnt		=	21;
	optional	uint32	tcpFlagsSynCnt		=	22;
	optional	uint32	tcpFlagsRstCnt		=	23;
	optional	uint32	tcpFlagsPshCnt		=	24;
	optional	uint32	tcpFlagsAckCnt		=	25;
	optional	uint32	tcpFlagsUrgCnt		=	26;
	optional	uint32	tcpFlagsEceCnt		=	27;
	optional	uint32	tcpFlagsCwrCnt		=	28;
	optional	uint32	tcpFlagsNSCnt		=	29;
	optional	uint32	tcpFlagsSynAckCnt	=	30;
	optional	bytes	etags				=	31;
	optional	bytes	ttags				=	32;
	optional	uint32	upLinkChecksum		=	33;
	optional	uint32	downLinkChecksum	=	34;
	optional	uint64	upLinkDesBytes		=	35;
	optional	uint64	downLinkDesBytes	=	36;
	optional	bytes	stream				=	37;
	optional	bytes	upLinkStream		=	38;
	optional	bytes	downLinkStream		=	39;
	optional	bytes	trans_payload_hex	=	40;
	optional	bytes	upLinkTransPayHex	=	41;
	optional	bytes	downLinkTransPayHex	=	42;
	repeated	uint32	upLinkPayLenSet		=	43;
	repeated	uint32	downLinkPayLenSet	=	44;
	optional	uint32	establish			=	45;
	optional	uint32	upLinkSynSeqNum		=	46;
	optional	uint32	downLinkSynSeqNum	=	47;
	optional	uint32	upLinkSynTcpWins	=	48;
	optional	uint32	downLinkSynTcpWins	=	49;
	optional	bytes	upLinkTcpOpts		=	50;
	optional	bytes	downLinkTcpOpts		=	51;
	optional	uint64	upSesBytes			=	52;
	optional	uint64	downSesbytes		=	53;
	optional	uint64	sesBytes			=	54;
	optional	float	sesBytesRatio		=	55;
	optional	float	payLenRatio			=	56;
	optional	bytes	ipAsnDestination	=	57;
	optional	bytes	ipAsnSource			=	58;
	optional	bytes	ipBaseProto			=	59;
	optional	uint64	ipBeginTime			=	60;
	optional	bytes	ipCityDestination	=	61;
	optional	bytes	ipCitySource		=	62;
	optional	bytes	ipCountryDestination =	63;
	optional	bytes	ipCountrySource		=	64;
	optional	uint64	ipDataBytes			=	65;
	optional	uint64	ipDesiredBytes		=	66;
	optional	uint64	ipDesiredBytesDestination	=	67;
	optional	uint64	ipDesiredBytesSource	=	68;
	optional	uint32	ipDestination		=	69;
	optional	uint32	ipDuration	=	70;
	optional	uint64	ipEndTime	=	71;
	optional	bytes	ipIspDestination	=	72;
	optional	bytes	ipIspSource	=	73;
	optional	float	ipLatitudeDestination	=	74;
	optional	float	ipLatitudeSource	=	75;
	optional	float	ipLongitudeDestination	=	76;
	optional	float	ipLongitudeSource	=	77;
	optional	uint32	ipPackets	=	78;
	optional	uint32	ipProto	=	79;
	optional	bytes	ipProtoPath	=	80;
	optional	uint32	ipSource	=	81;
	optional	bytes	ipStateDestination	=	82;
	optional	bytes	ipStateSource	=	83;
	optional	bytes	ipUpperProto	=	84;
	optional	uint32	ipVersion	=	85;
	optional	bytes	ipv6Destination	=	86;
	optional	bytes	ipv6Source	=	87;
	optional	uint32	outIpDestination	=	88;
	optional	uint32	outIpProto	=	89;
	optional	uint32	outIpSource	=	90;
	optional	uint32	outIpVersion	=	91;
	optional	bytes	outIpv6Destination	=	92;
	optional	bytes	outIpv6Source	=	93;
	optional	uint32	outPortDestination	=	94;
	optional	uint32	outPortSource	=	95;
	optional	uint32	portDestination	=	96;
	optional	uint32	portSource	=	97;
	optional	uint32	tcpFinished	=	98;
	optional	bytes	tcpFirstFlag	=	99;
	optional	bytes	tcpFlagsDestination	=	100;
	optional	bytes	tcpFlagsSource	=	101;
	optional	uint32	transPacketLengthDestinationHighFrequency	=	102;
	optional	uint32	transPacketLengthSourceHighFrequency	=	103;
	optional	uint32	isEnd	=	104;
}
