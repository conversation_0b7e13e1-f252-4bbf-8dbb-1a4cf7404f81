syntax = "proto2";

message EMAILInfo
{
    repeated		bytes		attType		    =   1;
    optional		uint32		attTypeCnt		=   2;
    repeated		bytes		attFileName		=   3;
    optional		uint32		attFileNameCnt	=   4;
    repeated		uint64		attConSize		=   5;
    repeated		bytes		attMD5		    =   6;
    optional		uint32		attMD5Cnt		=   7;
    optional		uint32		authRelt		=   8;
    repeated		bytes		BCC		        =   9;
    optional		bytes		body		    =   10;
    optional		bytes		bodyTexCha		=   11;
    optional		bytes		bodyTraEnc		=   12;
    optional		bytes		bodyMD5		    =   13;
    repeated		bytes		bodyType		=   14;
    optional		uint32		bodyTypeCnt		=   15;
    optional		bytes		bodyURL		    =   16;
    optional		uint32		bodyURLCnt		=   17;
    optional		uint32		bodyLen		    =   18;
    repeated		bytes		ByAsn		    =   19;
    repeated		bytes		ByCountry		=   20;
    repeated		bytes		ByDom		    =   21;
    optional		uint32		ByDomCnt		=   22;
    optional		bytes		ByIP		    =   23;
    optional		uint32		ByIpCnt		    =   24;
    repeated		bytes		CC		        =   25;
    repeated		bytes		CCAli		    =   26;
    repeated		bytes		Command		    =   27;
    optional		uint32		count		    =   28;
    optional		bytes		content		    =   29;
    repeated		bytes		conType		    =   30;
    optional		uint32		conTypeCnt		=   31;
    optional		uint32		date		    =   32;
    optional		bytes		deliveredTo		=   33;
    repeated		bytes		FromAsn		    =   34;
    repeated		bytes		FromCountry		=   35;
    repeated		bytes		FromDom		    =   36;
    optional		uint32		FromDomCnt		=   37;
    repeated		uint32		FromIp		    =   38;
    optional		uint32		FromIpCnt		=   39;
    repeated		bytes		headSet		    =   40;
    optional		uint32		headSetCnt		=   41;
    optional		bytes		host		    =   42;
    optional		bytes		name		    =   43;
    optional		bytes		os		        =   44;
    optional		bytes		osVer		    =   45;
    optional		bytes		vendor	    	=   46;
    optional		bytes		ver		        =   47;
    optional		bytes		emaInd		    =   48;
    optional		bytes		login		    =   49;
    optional		bytes		loginsrv		=   50;
    repeated		bytes		mailFrom		=   51;
    repeated		bytes		mailFromDom		=   52;
    optional		bytes		mailFromDomCnt	=   53;
    optional		bytes		mimeVer		    =   54;
    optional		uint32		mimeVerCnt		=   55;
    repeated		bytes		msgID		    =   56;
    optional		uint32		msgIDCnt		=   57;
    optional		bytes		emaProtType		=   58;
    optional		bytes		pwd		        =   59;
    repeated		bytes		rcptTo		    =   60;
    repeated		bytes		rcptToDom		=   61;
    optional		uint32		rcptToDomCnt	=   62;
    optional		bytes		repTo		    =   63;
    repeated		bytes		received		=   64;
    repeated		bytes		rcvrEmail		=   65;
    repeated		bytes		rcvrAli		    =   66;
    optional		uint32		rcvrAliCnt		=   67;
    optional		uint32		rcvrEmailCnt	=   68;
    repeated		bytes		rcvrDom		    =   69;
    optional		bytes		resentSrvAge	=   70;
    optional		uint32		resentDate		=   71;
    optional		bytes		resentFrom		=   72;
    repeated		bytes		resentTo		=   73;
    optional		bytes		senderEmail		=   74;
    optional		bytes		senderAli		=   75;
    optional		bytes		senderDom		=   76;
    optional		bytes		SMTPSrv		    =   77;
    optional		bytes		SMTPSrvAge		=   78;
    optional		bytes		receivedSPF		=   79;
    optional		uint32		startTLS		=   80;
    optional		bytes		subj		    =   81;
    optional		uint32		subj_cnt		=   82;
    optional		bytes		usrAge		    =   83;
    optional		bytes		emailVersion	=   84;
    optional		bytes		rcvWit		    =   85;
    optional		bytes		xMai		    =   86;
    optional		uint32		xMaiCnt		    =   87;
    optional		uint32		xOriIP		    =   88;
    optional		uint32		senderEmailCnt	=   89;
    optional		bytes		fw		        =   90;
    optional		bytes		befw		    =   91;
    optional		uint64		rcvDate		    =   92;
    optional		bytes		rcvSrvAge		=   93;
    optional		bytes		conTraEnc		=   94;
    optional		bytes		conTexCha		=   95;
    optional		bytes		realFrom		=   96;
    optional		bytes		realTo		    =   97;
    optional		bytes		emaActRep		=   98;
    optional		bytes		rcvFromName		=   99;
    optional		bytes		errType		    =   100;
    optional		bytes		contentWithHtml	=   101;
    optional		bytes		charset		    =   102;
    optional		uint32		contentLen		=   103;
    optional		bytes		isBeFw		    =   104;
    optional		bytes		attachmentPath	=   105;
    optional		bytes		banner		    =   106;
    repeated		bytes		senderSoftware	=   107;

}