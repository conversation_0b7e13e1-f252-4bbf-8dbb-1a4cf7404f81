syntax = "proto2";

message X509CerInfo
{
	optional	uint64		ProtabID				=	1;
	optional	uint32		ver						=	2;
	optional	bytes		srvNum					=	3;
	optional	uint32		issDataLen				=	4;
	optional	bytes		issComName				=	5;
	optional	bytes		issConName				=	6;
	optional	bytes		issLoaName				=	7;
	optional	bytes		issStaOrProName			=	8;
	optional	bytes		issStrAddr				=	9;
	optional	bytes		issOrgName				=	10;
	optional	bytes		issOrgUniName			=	11;
	optional	bytes		issPosOffBox			=	12;
	optional	bytes		subComName				=	13;
	optional	bytes		subConName				=	14;
	optional	bytes		subLoaName				=	15;
	optional	bytes		subStaOrProName			=	16;
	optional	bytes		subStrAddr				=	17;
	optional	bytes		subOrgName				=	18;
	optional	bytes		subOrgUniName			=	19;
	optional	bytes		subPosOffBox			=	20;
	optional	uint64		valNotBef				=	21;
	optional	uint64		valNotAft				=	22;
	optional	bytes		RSAMod					=	23;
	optional	bytes		RSAExp					=	24;
	optional	bytes		DHPriMod				=	25;
	optional	bytes		DHPGen					=	26;
	optional	bytes		DHPubKey				=	27;
	optional	bytes		DSAPubKeyP				=	28;
	optional	bytes		DSAPubKeyQ				=	29;
	optional	bytes		DSAPubKeyG				=	30;
	optional	bytes		sigAlg					=	31;
	optional	bytes		sigVal					=	32;
	optional	bytes		authKeyID				=	33;
	optional	bytes		subKeyID				=	34;
	optional	bytes		keyUsage				=	35;
	optional	uint64		priKeyUsaPerNotBef		=	36;
	optional	uint64		priKeyUsaPerNotAft		=	37;
	optional	bytes		certPol					=	38;
	optional	bytes		subAltDNS				=	39;
	optional	bytes		subAltIP				=	40;
	optional	bytes		subAltName				=	41;
	optional	bytes		issAltNameSys			=	42;
	optional	bytes		issAltIP				=	43;
	optional	bytes		issAltName				=	44;
	optional	bytes		subDirAtt				=	45;
	optional	bytes		extKeyUsage				=	46;
	optional	bytes		certRevListSrc			=	47;
	optional	bytes		certAuthInfAccMet		=	48;
	optional	bytes		certAuthInfAccLoc		=	49;
	optional	uint32		extCnt					=	50;
	optional	bytes		Protabname				=	51;
	optional	bytes		issuer					=	52;
	optional	bytes		subject					=	53;
	optional	uint32		daysRem					=	54;
	optional	bytes		pubkey					=	55;
	optional	bytes		fpAlg					=	56;
	optional	bytes		hash					=	57;
	optional	bytes		extSet					=	58;
	optional	uint32		daysTotal				=	59;
	optional	uint32		subAltDNSCnt			=	60;
	optional	bytes		authinfo				=	61;
	optional	bytes		basicConsCA				=	62;
	optional	bytes		basicConsPathLen		=	63;
	optional	bytes		basicCons				=	64;
	optional	bytes		KeyPur					=	65;
	optional	uint32		Certype					=	66;
	optional	bytes		certFullText			=	67;
	optional	uint32		alternativeIpCount		=	68;
	optional	uint32		source					=	69;
}
