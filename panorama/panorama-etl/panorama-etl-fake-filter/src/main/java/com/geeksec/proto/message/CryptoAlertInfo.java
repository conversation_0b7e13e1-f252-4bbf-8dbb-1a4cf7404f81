package com.geeksec.proto.message;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: CRYPTO_ALERT_INFO.proto
// Protobuf Java Version: 4.29.4

public final class CryptoAlertInfo {
  private CryptoAlertInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      CryptoAlertInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CRYPTO_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:CRYPTO_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     * @return Whether the cryptoStreamId field is set.
     */
    boolean hasCryptoStreamId();
    /**
     * <pre>
     * 流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     * @return The cryptoStreamId.
     */
    long getCryptoStreamId();

    /**
     * <pre>
     * 加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     * @return Whether the cryptoEncrypted field is set.
     */
    boolean hasCryptoEncrypted();
    /**
     * <pre>
     * 加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     * @return The cryptoEncrypted.
     */
    boolean getCryptoEncrypted();

    /**
     * <pre>
     * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     * @return Whether the cryptoAppName field is set.
     */
    boolean hasCryptoAppName();
    /**
     * <pre>
     * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     * @return The cryptoAppName.
     */
    java.lang.String getCryptoAppName();
    /**
     * <pre>
     * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     * @return The bytes for cryptoAppName.
     */
    com.google.protobuf.ByteString
        getCryptoAppNameBytes();

    /**
     * <pre>
     * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     * @return Whether the cryptoAppTypeId field is set.
     */
    boolean hasCryptoAppTypeId();
    /**
     * <pre>
     * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     * @return The cryptoAppTypeId.
     */
    int getCryptoAppTypeId();

    /**
     * <pre>
     * 应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     * @return Whether the cryptoAppType field is set.
     */
    boolean hasCryptoAppType();
    /**
     * <pre>
     * 应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     * @return The cryptoAppType.
     */
    java.lang.String getCryptoAppType();
    /**
     * <pre>
     * 应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     * @return The bytes for cryptoAppType.
     */
    com.google.protobuf.ByteString
        getCryptoAppTypeBytes();

    /**
     * <pre>
     * 应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     * @return Whether the cryptoAppClassId field is set.
     */
    boolean hasCryptoAppClassId();
    /**
     * <pre>
     * 应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     * @return The cryptoAppClassId.
     */
    int getCryptoAppClassId();

    /**
     * <pre>
     * 应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     * @return Whether the cryptoAppClass field is set.
     */
    boolean hasCryptoAppClass();
    /**
     * <pre>
     * 应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     * @return The cryptoAppClass.
     */
    java.lang.String getCryptoAppClass();
    /**
     * <pre>
     * 应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     * @return The bytes for cryptoAppClass.
     */
    com.google.protobuf.ByteString
        getCryptoAppClassBytes();

    /**
     * <pre>
     * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     * @return Whether the cryptoActionType field is set.
     */
    boolean hasCryptoActionType();
    /**
     * <pre>
     * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     * @return The cryptoActionType.
     */
    java.lang.String getCryptoActionType();
    /**
     * <pre>
     * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     * @return The bytes for cryptoActionType.
     */
    com.google.protobuf.ByteString
        getCryptoActionTypeBytes();

    /**
     * <pre>
     * 客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     * @return Whether the assetIdClient field is set.
     */
    boolean hasAssetIdClient();
    /**
     * <pre>
     * 客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     * @return The assetIdClient.
     */
    int getAssetIdClient();

    /**
     * <pre>
     * 服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     * @return Whether the assetIdServer field is set.
     */
    boolean hasAssetIdServer();
    /**
     * <pre>
     * 服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     * @return The assetIdServer.
     */
    int getAssetIdServer();

    /**
     * <pre>
     * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     * @return Whether the cryptoRiskName field is set.
     */
    boolean hasCryptoRiskName();
    /**
     * <pre>
     * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     * @return The cryptoRiskName.
     */
    java.lang.String getCryptoRiskName();
    /**
     * <pre>
     * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     * @return The bytes for cryptoRiskName.
     */
    com.google.protobuf.ByteString
        getCryptoRiskNameBytes();

    /**
     * <pre>
     * 风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     * @return Whether the cryptoRiskLevel field is set.
     */
    boolean hasCryptoRiskLevel();
    /**
     * <pre>
     * 风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     * @return The cryptoRiskLevel.
     */
    java.lang.String getCryptoRiskLevel();
    /**
     * <pre>
     * 风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     * @return The bytes for cryptoRiskLevel.
     */
    com.google.protobuf.ByteString
        getCryptoRiskLevelBytes();

    /**
     * <pre>
     * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     * @return Whether the cryptoCertFingerprint field is set.
     */
    boolean hasCryptoCertFingerprint();
    /**
     * <pre>
     * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     * @return The cryptoCertFingerprint.
     */
    java.lang.String getCryptoCertFingerprint();
    /**
     * <pre>
     * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     * @return The bytes for cryptoCertFingerprint.
     */
    com.google.protobuf.ByteString
        getCryptoCertFingerprintBytes();

    /**
     * <pre>
     * 威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     * @return Whether the cryptoRuleId field is set.
     */
    boolean hasCryptoRuleId();
    /**
     * <pre>
     * 威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     * @return The cryptoRuleId.
     */
    long getCryptoRuleId();

    /**
     * <pre>
     * 威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     * @return Whether the cryptoRuleType field is set.
     */
    boolean hasCryptoRuleType();
    /**
     * <pre>
     * 威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     * @return The cryptoRuleType.
     */
    java.lang.String getCryptoRuleType();
    /**
     * <pre>
     * 威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     * @return The bytes for cryptoRuleType.
     */
    com.google.protobuf.ByteString
        getCryptoRuleTypeBytes();

    /**
     * <pre>
     * 威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     * @return Whether the cryptoThreatSubtype field is set.
     */
    boolean hasCryptoThreatSubtype();
    /**
     * <pre>
     * 威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     * @return The cryptoThreatSubtype.
     */
    java.lang.String getCryptoThreatSubtype();
    /**
     * <pre>
     * 威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     * @return The bytes for cryptoThreatSubtype.
     */
    com.google.protobuf.ByteString
        getCryptoThreatSubtypeBytes();

    /**
     * <pre>
     * 威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     * @return Whether the cryptoThreatLevel field is set.
     */
    boolean hasCryptoThreatLevel();
    /**
     * <pre>
     * 威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     * @return The cryptoThreatLevel.
     */
    java.lang.String getCryptoThreatLevel();
    /**
     * <pre>
     * 威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     * @return The bytes for cryptoThreatLevel.
     */
    com.google.protobuf.ByteString
        getCryptoThreatLevelBytes();

    /**
     * <pre>
     * 威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     * @return Whether the cryptoThreatFamily field is set.
     */
    boolean hasCryptoThreatFamily();
    /**
     * <pre>
     * 威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     * @return The cryptoThreatFamily.
     */
    java.lang.String getCryptoThreatFamily();
    /**
     * <pre>
     * 威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     * @return The bytes for cryptoThreatFamily.
     */
    com.google.protobuf.ByteString
        getCryptoThreatFamilyBytes();

    /**
     * <pre>
     * 威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     * @return Whether the cryptoThreatGroup field is set.
     */
    boolean hasCryptoThreatGroup();
    /**
     * <pre>
     * 威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     * @return The cryptoThreatGroup.
     */
    java.lang.String getCryptoThreatGroup();
    /**
     * <pre>
     * 威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     * @return The bytes for cryptoThreatGroup.
     */
    com.google.protobuf.ByteString
        getCryptoThreatGroupBytes();

    /**
     * <pre>
     * 威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     * @return Whether the cryptoThreatDirection field is set.
     */
    boolean hasCryptoThreatDirection();
    /**
     * <pre>
     * 威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     * @return The cryptoThreatDirection.
     */
    java.lang.String getCryptoThreatDirection();
    /**
     * <pre>
     * 威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     * @return The bytes for cryptoThreatDirection.
     */
    com.google.protobuf.ByteString
        getCryptoThreatDirectionBytes();

    /**
     * <pre>
     * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     * @return Whether the cryptoThreatDescription field is set.
     */
    boolean hasCryptoThreatDescription();
    /**
     * <pre>
     * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     * @return The cryptoThreatDescription.
     */
    java.lang.String getCryptoThreatDescription();
    /**
     * <pre>
     * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     * @return The bytes for cryptoThreatDescription.
     */
    com.google.protobuf.ByteString
        getCryptoThreatDescriptionBytes();

    /**
     * <pre>
     * 攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     * @return Whether the cryptoDirection field is set.
     */
    boolean hasCryptoDirection();
    /**
     * <pre>
     * 攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     * @return The cryptoDirection.
     */
    java.lang.String getCryptoDirection();
    /**
     * <pre>
     * 攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     * @return The bytes for cryptoDirection.
     */
    com.google.protobuf.ByteString
        getCryptoDirectionBytes();

    /**
     * <pre>
     * 研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     * @return Whether the cryptoDetectionState field is set.
     */
    boolean hasCryptoDetectionState();
    /**
     * <pre>
     * 研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     * @return The cryptoDetectionState.
     */
    java.lang.String getCryptoDetectionState();
    /**
     * <pre>
     * 研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     * @return The bytes for cryptoDetectionState.
     */
    com.google.protobuf.ByteString
        getCryptoDetectionStateBytes();

    /**
     * <pre>
     * 研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     * @return Whether the cryptoDetectionDescribe field is set.
     */
    boolean hasCryptoDetectionDescribe();
    /**
     * <pre>
     * 研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     * @return The cryptoDetectionDescribe.
     */
    java.lang.String getCryptoDetectionDescribe();
    /**
     * <pre>
     * 研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     * @return The bytes for cryptoDetectionDescribe.
     */
    com.google.protobuf.ByteString
        getCryptoDetectionDescribeBytes();

    /**
     * <pre>
     * 握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     * @return Whether the cryptoHandResult field is set.
     */
    boolean hasCryptoHandResult();
    /**
     * <pre>
     * 握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     * @return The cryptoHandResult.
     */
    java.lang.String getCryptoHandResult();
    /**
     * <pre>
     * 握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     * @return The bytes for cryptoHandResult.
     */
    com.google.protobuf.ByteString
        getCryptoHandResultBytes();

    /**
     * <pre>
     * 流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     * @return Whether the cryptoFlowResult field is set.
     */
    boolean hasCryptoFlowResult();
    /**
     * <pre>
     * 流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     * @return The cryptoFlowResult.
     */
    java.lang.String getCryptoFlowResult();
    /**
     * <pre>
     * 流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     * @return The bytes for cryptoFlowResult.
     */
    com.google.protobuf.ByteString
        getCryptoFlowResultBytes();

    /**
     * <pre>
     * 证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     * @return Whether the cryptoCertResult field is set.
     */
    boolean hasCryptoCertResult();
    /**
     * <pre>
     * 证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     * @return The cryptoCertResult.
     */
    java.lang.String getCryptoCertResult();
    /**
     * <pre>
     * 证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     * @return The bytes for cryptoCertResult.
     */
    com.google.protobuf.ByteString
        getCryptoCertResultBytes();

    /**
     * <pre>
     * DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     * @return Whether the cryptoDomainResult field is set.
     */
    boolean hasCryptoDomainResult();
    /**
     * <pre>
     * DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     * @return The cryptoDomainResult.
     */
    java.lang.String getCryptoDomainResult();
    /**
     * <pre>
     * DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     * @return The bytes for cryptoDomainResult.
     */
    com.google.protobuf.ByteString
        getCryptoDomainResultBytes();

    /**
     * <pre>
     * 综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     * @return Whether the cryptoResult field is set.
     */
    boolean hasCryptoResult();
    /**
     * <pre>
     * 综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     * @return The cryptoResult.
     */
    java.lang.String getCryptoResult();
    /**
     * <pre>
     * 综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     * @return The bytes for cryptoResult.
     */
    com.google.protobuf.ByteString
        getCryptoResultBytes();
  }
  /**
   * <pre>
   * 密数据异常告警信息	
   * </pre>
   *
   * Protobuf type {@code CRYPTO_ALERT_INFO}
   */
  public static final class CRYPTO_ALERT_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:CRYPTO_ALERT_INFO)
      CRYPTO_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        CRYPTO_ALERT_INFO.class.getName());
    }
    // Use CRYPTO_ALERT_INFO.newBuilder() to construct.
    private CRYPTO_ALERT_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CRYPTO_ALERT_INFO() {
      cryptoAppName_ = "";
      cryptoAppType_ = "";
      cryptoAppClass_ = "";
      cryptoActionType_ = "";
      cryptoRiskName_ = "";
      cryptoRiskLevel_ = "";
      cryptoCertFingerprint_ = "";
      cryptoRuleType_ = "";
      cryptoThreatSubtype_ = "";
      cryptoThreatLevel_ = "";
      cryptoThreatFamily_ = "";
      cryptoThreatGroup_ = "";
      cryptoThreatDirection_ = "";
      cryptoThreatDescription_ = "";
      cryptoDirection_ = "";
      cryptoDetectionState_ = "";
      cryptoDetectionDescribe_ = "";
      cryptoHandResult_ = "";
      cryptoFlowResult_ = "";
      cryptoCertResult_ = "";
      cryptoDomainResult_ = "";
      cryptoResult_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              CryptoAlertInfo.CRYPTO_ALERT_INFO.class, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int CRYPTO_STREAM_ID_FIELD_NUMBER = 1;
    private long cryptoStreamId_ = 0L;
    /**
     * <pre>
     * 流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     * @return Whether the cryptoStreamId field is set.
     */
    @java.lang.Override
    public boolean hasCryptoStreamId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 流ID	7214956298192818176
     * </pre>
     *
     * <code>optional uint64 crypto_stream_id = 1;</code>
     * @return The cryptoStreamId.
     */
    @java.lang.Override
    public long getCryptoStreamId() {
      return cryptoStreamId_;
    }

    public static final int CRYPTO_ENCRYPTED_FIELD_NUMBER = 2;
    private boolean cryptoEncrypted_ = false;
    /**
     * <pre>
     * 加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     * @return Whether the cryptoEncrypted field is set.
     */
    @java.lang.Override
    public boolean hasCryptoEncrypted() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 加密标识	0: 非加密应用；1: 加密应用
     * </pre>
     *
     * <code>optional bool crypto_encrypted = 2;</code>
     * @return The cryptoEncrypted.
     */
    @java.lang.Override
    public boolean getCryptoEncrypted() {
      return cryptoEncrypted_;
    }

    public static final int CRYPTO_APP_NAME_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoAppName_ = "";
    /**
     * <pre>
     * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     * @return Whether the cryptoAppName field is set.
     */
    @java.lang.Override
    public boolean hasCryptoAppName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     * @return The cryptoAppName.
     */
    @java.lang.Override
    public java.lang.String getCryptoAppName() {
      java.lang.Object ref = cryptoAppName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoAppName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
     * </pre>
     *
     * <code>optional string crypto_app_name = 3;</code>
     * @return The bytes for cryptoAppName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoAppNameBytes() {
      java.lang.Object ref = cryptoAppName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoAppName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_APP_TYPE_ID_FIELD_NUMBER = 4;
    private int cryptoAppTypeId_ = 0;
    /**
     * <pre>
     * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     * @return Whether the cryptoAppTypeId field is set.
     */
    @java.lang.Override
    public boolean hasCryptoAppTypeId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
     * </pre>
     *
     * <code>optional uint32 crypto_app_type_id = 4;</code>
     * @return The cryptoAppTypeId.
     */
    @java.lang.Override
    public int getCryptoAppTypeId() {
      return cryptoAppTypeId_;
    }

    public static final int CRYPTO_APP_TYPE_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoAppType_ = "";
    /**
     * <pre>
     * 应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     * @return Whether the cryptoAppType field is set.
     */
    @java.lang.Override
    public boolean hasCryptoAppType() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     * @return The cryptoAppType.
     */
    @java.lang.Override
    public java.lang.String getCryptoAppType() {
      java.lang.Object ref = cryptoAppType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoAppType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 应用类型	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_type = 5;</code>
     * @return The bytes for cryptoAppType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoAppTypeBytes() {
      java.lang.Object ref = cryptoAppType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoAppType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_APP_CLASS_ID_FIELD_NUMBER = 6;
    private int cryptoAppClassId_ = 0;
    /**
     * <pre>
     * 应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     * @return Whether the cryptoAppClassId field is set.
     */
    @java.lang.Override
    public boolean hasCryptoAppClassId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 应用分类ID	
     * </pre>
     *
     * <code>optional uint32 crypto_app_class_id = 6;</code>
     * @return The cryptoAppClassId.
     */
    @java.lang.Override
    public int getCryptoAppClassId() {
      return cryptoAppClassId_;
    }

    public static final int CRYPTO_APP_CLASS_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoAppClass_ = "";
    /**
     * <pre>
     * 应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     * @return Whether the cryptoAppClass field is set.
     */
    @java.lang.Override
    public boolean hasCryptoAppClass() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     * @return The cryptoAppClass.
     */
    @java.lang.Override
    public java.lang.String getCryptoAppClass() {
      java.lang.Object ref = cryptoAppClass_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoAppClass_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 应用分类	"云服务"
     * </pre>
     *
     * <code>optional string crypto_app_class = 7;</code>
     * @return The bytes for cryptoAppClass.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoAppClassBytes() {
      java.lang.Object ref = cryptoAppClass_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoAppClass_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_ACTION_TYPE_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoActionType_ = "";
    /**
     * <pre>
     * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     * @return Whether the cryptoActionType field is set.
     */
    @java.lang.Override
    public boolean hasCryptoActionType() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     * @return The cryptoActionType.
     */
    @java.lang.Override
    public java.lang.String getCryptoActionType() {
      java.lang.Object ref = cryptoActionType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoActionType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
     * </pre>
     *
     * <code>optional string crypto_action_type = 8;</code>
     * @return The bytes for cryptoActionType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoActionTypeBytes() {
      java.lang.Object ref = cryptoActionType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoActionType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ASSET_ID_CLIENT_FIELD_NUMBER = 9;
    private int assetIdClient_ = 0;
    /**
     * <pre>
     * 客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     * @return Whether the assetIdClient field is set.
     */
    @java.lang.Override
    public boolean hasAssetIdClient() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 客户端资产对应标识符	58
     * </pre>
     *
     * <code>optional uint32 asset_id_client = 9;</code>
     * @return The assetIdClient.
     */
    @java.lang.Override
    public int getAssetIdClient() {
      return assetIdClient_;
    }

    public static final int ASSET_ID_SERVER_FIELD_NUMBER = 10;
    private int assetIdServer_ = 0;
    /**
     * <pre>
     * 服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     * @return Whether the assetIdServer field is set.
     */
    @java.lang.Override
    public boolean hasAssetIdServer() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 服务端资产对应标识符	0
     * </pre>
     *
     * <code>optional uint32 asset_id_server = 10;</code>
     * @return The assetIdServer.
     */
    @java.lang.Override
    public int getAssetIdServer() {
      return assetIdServer_;
    }

    public static final int CRYPTO_RISK_NAME_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoRiskName_ = "";
    /**
     * <pre>
     * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     * @return Whether the cryptoRiskName field is set.
     */
    @java.lang.Override
    public boolean hasCryptoRiskName() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     * @return The cryptoRiskName.
     */
    @java.lang.Override
    public java.lang.String getCryptoRiskName() {
      java.lang.Object ref = cryptoRiskName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoRiskName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
     * </pre>
     *
     * <code>optional string crypto_risk_name = 11;</code>
     * @return The bytes for cryptoRiskName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoRiskNameBytes() {
      java.lang.Object ref = cryptoRiskName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoRiskName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_RISK_LEVEL_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoRiskLevel_ = "";
    /**
     * <pre>
     * 风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     * @return Whether the cryptoRiskLevel field is set.
     */
    @java.lang.Override
    public boolean hasCryptoRiskLevel() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     * @return The cryptoRiskLevel.
     */
    @java.lang.Override
    public java.lang.String getCryptoRiskLevel() {
      java.lang.Object ref = cryptoRiskLevel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoRiskLevel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 风险等级	"低"
     * </pre>
     *
     * <code>optional string crypto_risk_level = 12;</code>
     * @return The bytes for cryptoRiskLevel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoRiskLevelBytes() {
      java.lang.Object ref = cryptoRiskLevel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoRiskLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_CERT_FINGERPRINT_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoCertFingerprint_ = "";
    /**
     * <pre>
     * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     * @return Whether the cryptoCertFingerprint field is set.
     */
    @java.lang.Override
    public boolean hasCryptoCertFingerprint() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     * @return The cryptoCertFingerprint.
     */
    @java.lang.Override
    public java.lang.String getCryptoCertFingerprint() {
      java.lang.Object ref = cryptoCertFingerprint_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoCertFingerprint_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
     * </pre>
     *
     * <code>optional string crypto_cert_fingerprint = 13;</code>
     * @return The bytes for cryptoCertFingerprint.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoCertFingerprintBytes() {
      java.lang.Object ref = cryptoCertFingerprint_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoCertFingerprint_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_RULE_ID_FIELD_NUMBER = 14;
    private long cryptoRuleId_ = 0L;
    /**
     * <pre>
     * 威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     * @return Whether the cryptoRuleId field is set.
     */
    @java.lang.Override
    public boolean hasCryptoRuleId() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 威胁规则 ID	
     * </pre>
     *
     * <code>optional uint64 crypto_rule_id = 14;</code>
     * @return The cryptoRuleId.
     */
    @java.lang.Override
    public long getCryptoRuleId() {
      return cryptoRuleId_;
    }

    public static final int CRYPTO_RULE_TYPE_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoRuleType_ = "";
    /**
     * <pre>
     * 威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     * @return Whether the cryptoRuleType field is set.
     */
    @java.lang.Override
    public boolean hasCryptoRuleType() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     * @return The cryptoRuleType.
     */
    @java.lang.Override
    public java.lang.String getCryptoRuleType() {
      java.lang.Object ref = cryptoRuleType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoRuleType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁规则类型	"标准化规则"
     * </pre>
     *
     * <code>optional string crypto_rule_type = 15;</code>
     * @return The bytes for cryptoRuleType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoRuleTypeBytes() {
      java.lang.Object ref = cryptoRuleType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoRuleType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_SUBTYPE_FIELD_NUMBER = 16;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoThreatSubtype_ = "";
    /**
     * <pre>
     * 威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     * @return Whether the cryptoThreatSubtype field is set.
     */
    @java.lang.Override
    public boolean hasCryptoThreatSubtype() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     * @return The cryptoThreatSubtype.
     */
    @java.lang.Override
    public java.lang.String getCryptoThreatSubtype() {
      java.lang.Object ref = cryptoThreatSubtype_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatSubtype_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁标签	"TCP 隧道"
     * </pre>
     *
     * <code>optional string crypto_threat_subtype = 16;</code>
     * @return The bytes for cryptoThreatSubtype.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoThreatSubtypeBytes() {
      java.lang.Object ref = cryptoThreatSubtype_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoThreatSubtype_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_LEVEL_FIELD_NUMBER = 17;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoThreatLevel_ = "";
    /**
     * <pre>
     * 威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     * @return Whether the cryptoThreatLevel field is set.
     */
    @java.lang.Override
    public boolean hasCryptoThreatLevel() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     * @return The cryptoThreatLevel.
     */
    @java.lang.Override
    public java.lang.String getCryptoThreatLevel() {
      java.lang.Object ref = cryptoThreatLevel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatLevel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁等级	分为高危、中危和低危
     * </pre>
     *
     * <code>optional string crypto_threat_level = 17;</code>
     * @return The bytes for cryptoThreatLevel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoThreatLevelBytes() {
      java.lang.Object ref = cryptoThreatLevel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoThreatLevel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_FAMILY_FIELD_NUMBER = 18;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoThreatFamily_ = "";
    /**
     * <pre>
     * 威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     * @return Whether the cryptoThreatFamily field is set.
     */
    @java.lang.Override
    public boolean hasCryptoThreatFamily() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * 威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     * @return The cryptoThreatFamily.
     */
    @java.lang.Override
    public java.lang.String getCryptoThreatFamily() {
      java.lang.Object ref = cryptoThreatFamily_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatFamily_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁所属家族	"CobaltStrike"
     * </pre>
     *
     * <code>optional string crypto_threat_family = 18;</code>
     * @return The bytes for cryptoThreatFamily.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoThreatFamilyBytes() {
      java.lang.Object ref = cryptoThreatFamily_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoThreatFamily_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_GROUP_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoThreatGroup_ = "";
    /**
     * <pre>
     * 威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     * @return Whether the cryptoThreatGroup field is set.
     */
    @java.lang.Override
    public boolean hasCryptoThreatGroup() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * 威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     * @return The cryptoThreatGroup.
     */
    @java.lang.Override
    public java.lang.String getCryptoThreatGroup() {
      java.lang.Object ref = cryptoThreatGroup_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatGroup_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁组织	"Sidewinder 响尾蛇"
     * </pre>
     *
     * <code>optional string crypto_threat_group = 19;</code>
     * @return The bytes for cryptoThreatGroup.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoThreatGroupBytes() {
      java.lang.Object ref = cryptoThreatGroup_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoThreatGroup_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_DIRECTION_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoThreatDirection_ = "";
    /**
     * <pre>
     * 威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     * @return Whether the cryptoThreatDirection field is set.
     */
    @java.lang.Override
    public boolean hasCryptoThreatDirection() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     * @return The cryptoThreatDirection.
     */
    @java.lang.Override
    public java.lang.String getCryptoThreatDirection() {
      java.lang.Object ref = cryptoThreatDirection_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatDirection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁方向	"俄罗斯"
     * </pre>
     *
     * <code>optional string crypto_threat_direction = 20;</code>
     * @return The bytes for cryptoThreatDirection.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoThreatDirectionBytes() {
      java.lang.Object ref = cryptoThreatDirection_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoThreatDirection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_THREAT_DESCRIPTION_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoThreatDescription_ = "";
    /**
     * <pre>
     * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     * @return Whether the cryptoThreatDescription field is set.
     */
    @java.lang.Override
    public boolean hasCryptoThreatDescription() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     * @return The cryptoThreatDescription.
     */
    @java.lang.Override
    public java.lang.String getCryptoThreatDescription() {
      java.lang.Object ref = cryptoThreatDescription_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoThreatDescription_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
     * </pre>
     *
     * <code>optional string crypto_threat_description = 21;</code>
     * @return The bytes for cryptoThreatDescription.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoThreatDescriptionBytes() {
      java.lang.Object ref = cryptoThreatDescription_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoThreatDescription_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DIRECTION_FIELD_NUMBER = 22;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoDirection_ = "";
    /**
     * <pre>
     * 攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     * @return Whether the cryptoDirection field is set.
     */
    @java.lang.Override
    public boolean hasCryptoDirection() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     * @return The cryptoDirection.
     */
    @java.lang.Override
    public java.lang.String getCryptoDirection() {
      java.lang.Object ref = cryptoDirection_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDirection_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 攻击方向	入联风险、横向风险、出联风险
     * </pre>
     *
     * <code>optional string crypto_direction = 22;</code>
     * @return The bytes for cryptoDirection.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoDirectionBytes() {
      java.lang.Object ref = cryptoDirection_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoDirection_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DETECTION_STATE_FIELD_NUMBER = 23;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoDetectionState_ = "";
    /**
     * <pre>
     * 研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     * @return Whether the cryptoDetectionState field is set.
     */
    @java.lang.Override
    public boolean hasCryptoDetectionState() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * 研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     * @return The cryptoDetectionState.
     */
    @java.lang.Override
    public java.lang.String getCryptoDetectionState() {
      java.lang.Object ref = cryptoDetectionState_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDetectionState_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 研判状态	未研判、误报、攻击行为、攻击成功、未知
     * </pre>
     *
     * <code>optional string crypto_detection_state = 23;</code>
     * @return The bytes for cryptoDetectionState.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoDetectionStateBytes() {
      java.lang.Object ref = cryptoDetectionState_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoDetectionState_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DETECTION_DESCRIBE_FIELD_NUMBER = 24;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoDetectionDescribe_ = "";
    /**
     * <pre>
     * 研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     * @return Whether the cryptoDetectionDescribe field is set.
     */
    @java.lang.Override
    public boolean hasCryptoDetectionDescribe() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * 研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     * @return The cryptoDetectionDescribe.
     */
    @java.lang.Override
    public java.lang.String getCryptoDetectionDescribe() {
      java.lang.Object ref = cryptoDetectionDescribe_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDetectionDescribe_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 研判原因	"Suricata"
     * </pre>
     *
     * <code>optional string crypto_detection_describe = 24;</code>
     * @return The bytes for cryptoDetectionDescribe.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoDetectionDescribeBytes() {
      java.lang.Object ref = cryptoDetectionDescribe_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoDetectionDescribe_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_HAND_RESULT_FIELD_NUMBER = 25;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoHandResult_ = "";
    /**
     * <pre>
     * 握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     * @return Whether the cryptoHandResult field is set.
     */
    @java.lang.Override
    public boolean hasCryptoHandResult() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <pre>
     * 握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     * @return The cryptoHandResult.
     */
    @java.lang.Override
    public java.lang.String getCryptoHandResult() {
      java.lang.Object ref = cryptoHandResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoHandResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 握手评分	"100"
     * </pre>
     *
     * <code>optional string crypto_hand_result = 25;</code>
     * @return The bytes for cryptoHandResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoHandResultBytes() {
      java.lang.Object ref = cryptoHandResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoHandResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_FLOW_RESULT_FIELD_NUMBER = 26;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoFlowResult_ = "";
    /**
     * <pre>
     * 流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     * @return Whether the cryptoFlowResult field is set.
     */
    @java.lang.Override
    public boolean hasCryptoFlowResult() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <pre>
     * 流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     * @return The cryptoFlowResult.
     */
    @java.lang.Override
    public java.lang.String getCryptoFlowResult() {
      java.lang.Object ref = cryptoFlowResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoFlowResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 流模型评分	"100"
     * </pre>
     *
     * <code>optional string crypto_flow_result = 26;</code>
     * @return The bytes for cryptoFlowResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoFlowResultBytes() {
      java.lang.Object ref = cryptoFlowResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoFlowResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_CERT_RESULT_FIELD_NUMBER = 27;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoCertResult_ = "";
    /**
     * <pre>
     * 证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     * @return Whether the cryptoCertResult field is set.
     */
    @java.lang.Override
    public boolean hasCryptoCertResult() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <pre>
     * 证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     * @return The cryptoCertResult.
     */
    @java.lang.Override
    public java.lang.String getCryptoCertResult() {
      java.lang.Object ref = cryptoCertResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoCertResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 证书评分	"88"
     * </pre>
     *
     * <code>optional string crypto_cert_result = 27;</code>
     * @return The bytes for cryptoCertResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoCertResultBytes() {
      java.lang.Object ref = cryptoCertResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoCertResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_DOMAIN_RESULT_FIELD_NUMBER = 28;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoDomainResult_ = "";
    /**
     * <pre>
     * DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     * @return Whether the cryptoDomainResult field is set.
     */
    @java.lang.Override
    public boolean hasCryptoDomainResult() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <pre>
     * DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     * @return The cryptoDomainResult.
     */
    @java.lang.Override
    public java.lang.String getCryptoDomainResult() {
      java.lang.Object ref = cryptoDomainResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoDomainResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * DNS评分	"81"
     * </pre>
     *
     * <code>optional string crypto_domain_result = 28;</code>
     * @return The bytes for cryptoDomainResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoDomainResultBytes() {
      java.lang.Object ref = cryptoDomainResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoDomainResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CRYPTO_RESULT_FIELD_NUMBER = 29;
    @SuppressWarnings("serial")
    private volatile java.lang.Object cryptoResult_ = "";
    /**
     * <pre>
     * 综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     * @return Whether the cryptoResult field is set.
     */
    @java.lang.Override
    public boolean hasCryptoResult() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <pre>
     * 综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     * @return The cryptoResult.
     */
    @java.lang.Override
    public java.lang.String getCryptoResult() {
      java.lang.Object ref = cryptoResult_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cryptoResult_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 综合评分	"95"
     * </pre>
     *
     * <code>optional string crypto_result = 29;</code>
     * @return The bytes for cryptoResult.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCryptoResultBytes() {
      java.lang.Object ref = cryptoResult_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cryptoResult_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt64(1, cryptoStreamId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, cryptoEncrypted_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, cryptoAppName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(4, cryptoAppTypeId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, cryptoAppType_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeUInt32(6, cryptoAppClassId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 7, cryptoAppClass_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, cryptoActionType_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeUInt32(9, assetIdClient_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeUInt32(10, assetIdServer_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, cryptoRiskName_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, cryptoRiskLevel_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 13, cryptoCertFingerprint_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeUInt64(14, cryptoRuleId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 15, cryptoRuleType_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 16, cryptoThreatSubtype_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 17, cryptoThreatLevel_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 18, cryptoThreatFamily_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 19, cryptoThreatGroup_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 20, cryptoThreatDirection_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 21, cryptoThreatDescription_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 22, cryptoDirection_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 23, cryptoDetectionState_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 24, cryptoDetectionDescribe_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 25, cryptoHandResult_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 26, cryptoFlowResult_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 27, cryptoCertResult_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 28, cryptoDomainResult_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 29, cryptoResult_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, cryptoStreamId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, cryptoEncrypted_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, cryptoAppName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, cryptoAppTypeId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, cryptoAppType_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, cryptoAppClassId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(7, cryptoAppClass_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, cryptoActionType_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, assetIdClient_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, assetIdServer_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, cryptoRiskName_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, cryptoRiskLevel_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(13, cryptoCertFingerprint_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, cryptoRuleId_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(15, cryptoRuleType_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(16, cryptoThreatSubtype_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(17, cryptoThreatLevel_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(18, cryptoThreatFamily_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(19, cryptoThreatGroup_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(20, cryptoThreatDirection_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(21, cryptoThreatDescription_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(22, cryptoDirection_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(23, cryptoDetectionState_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(24, cryptoDetectionDescribe_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(25, cryptoHandResult_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(26, cryptoFlowResult_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(27, cryptoCertResult_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(28, cryptoDomainResult_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(29, cryptoResult_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof CryptoAlertInfo.CRYPTO_ALERT_INFO)) {
        return super.equals(obj);
      }
      CryptoAlertInfo.CRYPTO_ALERT_INFO other = (CryptoAlertInfo.CRYPTO_ALERT_INFO) obj;

      if (hasCryptoStreamId() != other.hasCryptoStreamId()) return false;
      if (hasCryptoStreamId()) {
        if (getCryptoStreamId()
            != other.getCryptoStreamId()) return false;
      }
      if (hasCryptoEncrypted() != other.hasCryptoEncrypted()) return false;
      if (hasCryptoEncrypted()) {
        if (getCryptoEncrypted()
            != other.getCryptoEncrypted()) return false;
      }
      if (hasCryptoAppName() != other.hasCryptoAppName()) return false;
      if (hasCryptoAppName()) {
        if (!getCryptoAppName()
            .equals(other.getCryptoAppName())) return false;
      }
      if (hasCryptoAppTypeId() != other.hasCryptoAppTypeId()) return false;
      if (hasCryptoAppTypeId()) {
        if (getCryptoAppTypeId()
            != other.getCryptoAppTypeId()) return false;
      }
      if (hasCryptoAppType() != other.hasCryptoAppType()) return false;
      if (hasCryptoAppType()) {
        if (!getCryptoAppType()
            .equals(other.getCryptoAppType())) return false;
      }
      if (hasCryptoAppClassId() != other.hasCryptoAppClassId()) return false;
      if (hasCryptoAppClassId()) {
        if (getCryptoAppClassId()
            != other.getCryptoAppClassId()) return false;
      }
      if (hasCryptoAppClass() != other.hasCryptoAppClass()) return false;
      if (hasCryptoAppClass()) {
        if (!getCryptoAppClass()
            .equals(other.getCryptoAppClass())) return false;
      }
      if (hasCryptoActionType() != other.hasCryptoActionType()) return false;
      if (hasCryptoActionType()) {
        if (!getCryptoActionType()
            .equals(other.getCryptoActionType())) return false;
      }
      if (hasAssetIdClient() != other.hasAssetIdClient()) return false;
      if (hasAssetIdClient()) {
        if (getAssetIdClient()
            != other.getAssetIdClient()) return false;
      }
      if (hasAssetIdServer() != other.hasAssetIdServer()) return false;
      if (hasAssetIdServer()) {
        if (getAssetIdServer()
            != other.getAssetIdServer()) return false;
      }
      if (hasCryptoRiskName() != other.hasCryptoRiskName()) return false;
      if (hasCryptoRiskName()) {
        if (!getCryptoRiskName()
            .equals(other.getCryptoRiskName())) return false;
      }
      if (hasCryptoRiskLevel() != other.hasCryptoRiskLevel()) return false;
      if (hasCryptoRiskLevel()) {
        if (!getCryptoRiskLevel()
            .equals(other.getCryptoRiskLevel())) return false;
      }
      if (hasCryptoCertFingerprint() != other.hasCryptoCertFingerprint()) return false;
      if (hasCryptoCertFingerprint()) {
        if (!getCryptoCertFingerprint()
            .equals(other.getCryptoCertFingerprint())) return false;
      }
      if (hasCryptoRuleId() != other.hasCryptoRuleId()) return false;
      if (hasCryptoRuleId()) {
        if (getCryptoRuleId()
            != other.getCryptoRuleId()) return false;
      }
      if (hasCryptoRuleType() != other.hasCryptoRuleType()) return false;
      if (hasCryptoRuleType()) {
        if (!getCryptoRuleType()
            .equals(other.getCryptoRuleType())) return false;
      }
      if (hasCryptoThreatSubtype() != other.hasCryptoThreatSubtype()) return false;
      if (hasCryptoThreatSubtype()) {
        if (!getCryptoThreatSubtype()
            .equals(other.getCryptoThreatSubtype())) return false;
      }
      if (hasCryptoThreatLevel() != other.hasCryptoThreatLevel()) return false;
      if (hasCryptoThreatLevel()) {
        if (!getCryptoThreatLevel()
            .equals(other.getCryptoThreatLevel())) return false;
      }
      if (hasCryptoThreatFamily() != other.hasCryptoThreatFamily()) return false;
      if (hasCryptoThreatFamily()) {
        if (!getCryptoThreatFamily()
            .equals(other.getCryptoThreatFamily())) return false;
      }
      if (hasCryptoThreatGroup() != other.hasCryptoThreatGroup()) return false;
      if (hasCryptoThreatGroup()) {
        if (!getCryptoThreatGroup()
            .equals(other.getCryptoThreatGroup())) return false;
      }
      if (hasCryptoThreatDirection() != other.hasCryptoThreatDirection()) return false;
      if (hasCryptoThreatDirection()) {
        if (!getCryptoThreatDirection()
            .equals(other.getCryptoThreatDirection())) return false;
      }
      if (hasCryptoThreatDescription() != other.hasCryptoThreatDescription()) return false;
      if (hasCryptoThreatDescription()) {
        if (!getCryptoThreatDescription()
            .equals(other.getCryptoThreatDescription())) return false;
      }
      if (hasCryptoDirection() != other.hasCryptoDirection()) return false;
      if (hasCryptoDirection()) {
        if (!getCryptoDirection()
            .equals(other.getCryptoDirection())) return false;
      }
      if (hasCryptoDetectionState() != other.hasCryptoDetectionState()) return false;
      if (hasCryptoDetectionState()) {
        if (!getCryptoDetectionState()
            .equals(other.getCryptoDetectionState())) return false;
      }
      if (hasCryptoDetectionDescribe() != other.hasCryptoDetectionDescribe()) return false;
      if (hasCryptoDetectionDescribe()) {
        if (!getCryptoDetectionDescribe()
            .equals(other.getCryptoDetectionDescribe())) return false;
      }
      if (hasCryptoHandResult() != other.hasCryptoHandResult()) return false;
      if (hasCryptoHandResult()) {
        if (!getCryptoHandResult()
            .equals(other.getCryptoHandResult())) return false;
      }
      if (hasCryptoFlowResult() != other.hasCryptoFlowResult()) return false;
      if (hasCryptoFlowResult()) {
        if (!getCryptoFlowResult()
            .equals(other.getCryptoFlowResult())) return false;
      }
      if (hasCryptoCertResult() != other.hasCryptoCertResult()) return false;
      if (hasCryptoCertResult()) {
        if (!getCryptoCertResult()
            .equals(other.getCryptoCertResult())) return false;
      }
      if (hasCryptoDomainResult() != other.hasCryptoDomainResult()) return false;
      if (hasCryptoDomainResult()) {
        if (!getCryptoDomainResult()
            .equals(other.getCryptoDomainResult())) return false;
      }
      if (hasCryptoResult() != other.hasCryptoResult()) return false;
      if (hasCryptoResult()) {
        if (!getCryptoResult()
            .equals(other.getCryptoResult())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCryptoStreamId()) {
        hash = (37 * hash) + CRYPTO_STREAM_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCryptoStreamId());
      }
      if (hasCryptoEncrypted()) {
        hash = (37 * hash) + CRYPTO_ENCRYPTED_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getCryptoEncrypted());
      }
      if (hasCryptoAppName()) {
        hash = (37 * hash) + CRYPTO_APP_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppName().hashCode();
      }
      if (hasCryptoAppTypeId()) {
        hash = (37 * hash) + CRYPTO_APP_TYPE_ID_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppTypeId();
      }
      if (hasCryptoAppType()) {
        hash = (37 * hash) + CRYPTO_APP_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppType().hashCode();
      }
      if (hasCryptoAppClassId()) {
        hash = (37 * hash) + CRYPTO_APP_CLASS_ID_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppClassId();
      }
      if (hasCryptoAppClass()) {
        hash = (37 * hash) + CRYPTO_APP_CLASS_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAppClass().hashCode();
      }
      if (hasCryptoActionType()) {
        hash = (37 * hash) + CRYPTO_ACTION_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoActionType().hashCode();
      }
      if (hasAssetIdClient()) {
        hash = (37 * hash) + ASSET_ID_CLIENT_FIELD_NUMBER;
        hash = (53 * hash) + getAssetIdClient();
      }
      if (hasAssetIdServer()) {
        hash = (37 * hash) + ASSET_ID_SERVER_FIELD_NUMBER;
        hash = (53 * hash) + getAssetIdServer();
      }
      if (hasCryptoRiskName()) {
        hash = (37 * hash) + CRYPTO_RISK_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoRiskName().hashCode();
      }
      if (hasCryptoRiskLevel()) {
        hash = (37 * hash) + CRYPTO_RISK_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoRiskLevel().hashCode();
      }
      if (hasCryptoCertFingerprint()) {
        hash = (37 * hash) + CRYPTO_CERT_FINGERPRINT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoCertFingerprint().hashCode();
      }
      if (hasCryptoRuleId()) {
        hash = (37 * hash) + CRYPTO_RULE_ID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCryptoRuleId());
      }
      if (hasCryptoRuleType()) {
        hash = (37 * hash) + CRYPTO_RULE_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoRuleType().hashCode();
      }
      if (hasCryptoThreatSubtype()) {
        hash = (37 * hash) + CRYPTO_THREAT_SUBTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatSubtype().hashCode();
      }
      if (hasCryptoThreatLevel()) {
        hash = (37 * hash) + CRYPTO_THREAT_LEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatLevel().hashCode();
      }
      if (hasCryptoThreatFamily()) {
        hash = (37 * hash) + CRYPTO_THREAT_FAMILY_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatFamily().hashCode();
      }
      if (hasCryptoThreatGroup()) {
        hash = (37 * hash) + CRYPTO_THREAT_GROUP_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatGroup().hashCode();
      }
      if (hasCryptoThreatDirection()) {
        hash = (37 * hash) + CRYPTO_THREAT_DIRECTION_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatDirection().hashCode();
      }
      if (hasCryptoThreatDescription()) {
        hash = (37 * hash) + CRYPTO_THREAT_DESCRIPTION_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoThreatDescription().hashCode();
      }
      if (hasCryptoDirection()) {
        hash = (37 * hash) + CRYPTO_DIRECTION_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDirection().hashCode();
      }
      if (hasCryptoDetectionState()) {
        hash = (37 * hash) + CRYPTO_DETECTION_STATE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDetectionState().hashCode();
      }
      if (hasCryptoDetectionDescribe()) {
        hash = (37 * hash) + CRYPTO_DETECTION_DESCRIBE_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDetectionDescribe().hashCode();
      }
      if (hasCryptoHandResult()) {
        hash = (37 * hash) + CRYPTO_HAND_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoHandResult().hashCode();
      }
      if (hasCryptoFlowResult()) {
        hash = (37 * hash) + CRYPTO_FLOW_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoFlowResult().hashCode();
      }
      if (hasCryptoCertResult()) {
        hash = (37 * hash) + CRYPTO_CERT_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoCertResult().hashCode();
      }
      if (hasCryptoDomainResult()) {
        hash = (37 * hash) + CRYPTO_DOMAIN_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoDomainResult().hashCode();
      }
      if (hasCryptoResult()) {
        hash = (37 * hash) + CRYPTO_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoResult().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static CryptoAlertInfo.CRYPTO_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(CryptoAlertInfo.CRYPTO_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 密数据异常告警信息	
     * </pre>
     *
     * Protobuf type {@code CRYPTO_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:CRYPTO_ALERT_INFO)
        CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                CryptoAlertInfo.CRYPTO_ALERT_INFO.class, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder.class);
      }

      // Construct using CryptoAlertInfo.CRYPTO_ALERT_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        cryptoStreamId_ = 0L;
        cryptoEncrypted_ = false;
        cryptoAppName_ = "";
        cryptoAppTypeId_ = 0;
        cryptoAppType_ = "";
        cryptoAppClassId_ = 0;
        cryptoAppClass_ = "";
        cryptoActionType_ = "";
        assetIdClient_ = 0;
        assetIdServer_ = 0;
        cryptoRiskName_ = "";
        cryptoRiskLevel_ = "";
        cryptoCertFingerprint_ = "";
        cryptoRuleId_ = 0L;
        cryptoRuleType_ = "";
        cryptoThreatSubtype_ = "";
        cryptoThreatLevel_ = "";
        cryptoThreatFamily_ = "";
        cryptoThreatGroup_ = "";
        cryptoThreatDirection_ = "";
        cryptoThreatDescription_ = "";
        cryptoDirection_ = "";
        cryptoDetectionState_ = "";
        cryptoDetectionDescribe_ = "";
        cryptoHandResult_ = "";
        cryptoFlowResult_ = "";
        cryptoCertResult_ = "";
        cryptoDomainResult_ = "";
        cryptoResult_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return CryptoAlertInfo.internal_static_CRYPTO_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public CryptoAlertInfo.CRYPTO_ALERT_INFO getDefaultInstanceForType() {
        return CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public CryptoAlertInfo.CRYPTO_ALERT_INFO build() {
        CryptoAlertInfo.CRYPTO_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public CryptoAlertInfo.CRYPTO_ALERT_INFO buildPartial() {
        CryptoAlertInfo.CRYPTO_ALERT_INFO result = new CryptoAlertInfo.CRYPTO_ALERT_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(CryptoAlertInfo.CRYPTO_ALERT_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cryptoStreamId_ = cryptoStreamId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.cryptoEncrypted_ = cryptoEncrypted_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.cryptoAppName_ = cryptoAppName_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.cryptoAppTypeId_ = cryptoAppTypeId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.cryptoAppType_ = cryptoAppType_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.cryptoAppClassId_ = cryptoAppClassId_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.cryptoAppClass_ = cryptoAppClass_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.cryptoActionType_ = cryptoActionType_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.assetIdClient_ = assetIdClient_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.assetIdServer_ = assetIdServer_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.cryptoRiskName_ = cryptoRiskName_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.cryptoRiskLevel_ = cryptoRiskLevel_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.cryptoCertFingerprint_ = cryptoCertFingerprint_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.cryptoRuleId_ = cryptoRuleId_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.cryptoRuleType_ = cryptoRuleType_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.cryptoThreatSubtype_ = cryptoThreatSubtype_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.cryptoThreatLevel_ = cryptoThreatLevel_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.cryptoThreatFamily_ = cryptoThreatFamily_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.cryptoThreatGroup_ = cryptoThreatGroup_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.cryptoThreatDirection_ = cryptoThreatDirection_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.cryptoThreatDescription_ = cryptoThreatDescription_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.cryptoDirection_ = cryptoDirection_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.cryptoDetectionState_ = cryptoDetectionState_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.cryptoDetectionDescribe_ = cryptoDetectionDescribe_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.cryptoHandResult_ = cryptoHandResult_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.cryptoFlowResult_ = cryptoFlowResult_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.cryptoCertResult_ = cryptoCertResult_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.cryptoDomainResult_ = cryptoDomainResult_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.cryptoResult_ = cryptoResult_;
          to_bitField0_ |= 0x10000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof CryptoAlertInfo.CRYPTO_ALERT_INFO) {
          return mergeFrom((CryptoAlertInfo.CRYPTO_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(CryptoAlertInfo.CRYPTO_ALERT_INFO other) {
        if (other == CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasCryptoStreamId()) {
          setCryptoStreamId(other.getCryptoStreamId());
        }
        if (other.hasCryptoEncrypted()) {
          setCryptoEncrypted(other.getCryptoEncrypted());
        }
        if (other.hasCryptoAppName()) {
          cryptoAppName_ = other.cryptoAppName_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasCryptoAppTypeId()) {
          setCryptoAppTypeId(other.getCryptoAppTypeId());
        }
        if (other.hasCryptoAppType()) {
          cryptoAppType_ = other.cryptoAppType_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.hasCryptoAppClassId()) {
          setCryptoAppClassId(other.getCryptoAppClassId());
        }
        if (other.hasCryptoAppClass()) {
          cryptoAppClass_ = other.cryptoAppClass_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (other.hasCryptoActionType()) {
          cryptoActionType_ = other.cryptoActionType_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (other.hasAssetIdClient()) {
          setAssetIdClient(other.getAssetIdClient());
        }
        if (other.hasAssetIdServer()) {
          setAssetIdServer(other.getAssetIdServer());
        }
        if (other.hasCryptoRiskName()) {
          cryptoRiskName_ = other.cryptoRiskName_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (other.hasCryptoRiskLevel()) {
          cryptoRiskLevel_ = other.cryptoRiskLevel_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        if (other.hasCryptoCertFingerprint()) {
          cryptoCertFingerprint_ = other.cryptoCertFingerprint_;
          bitField0_ |= 0x00001000;
          onChanged();
        }
        if (other.hasCryptoRuleId()) {
          setCryptoRuleId(other.getCryptoRuleId());
        }
        if (other.hasCryptoRuleType()) {
          cryptoRuleType_ = other.cryptoRuleType_;
          bitField0_ |= 0x00004000;
          onChanged();
        }
        if (other.hasCryptoThreatSubtype()) {
          cryptoThreatSubtype_ = other.cryptoThreatSubtype_;
          bitField0_ |= 0x00008000;
          onChanged();
        }
        if (other.hasCryptoThreatLevel()) {
          cryptoThreatLevel_ = other.cryptoThreatLevel_;
          bitField0_ |= 0x00010000;
          onChanged();
        }
        if (other.hasCryptoThreatFamily()) {
          cryptoThreatFamily_ = other.cryptoThreatFamily_;
          bitField0_ |= 0x00020000;
          onChanged();
        }
        if (other.hasCryptoThreatGroup()) {
          cryptoThreatGroup_ = other.cryptoThreatGroup_;
          bitField0_ |= 0x00040000;
          onChanged();
        }
        if (other.hasCryptoThreatDirection()) {
          cryptoThreatDirection_ = other.cryptoThreatDirection_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (other.hasCryptoThreatDescription()) {
          cryptoThreatDescription_ = other.cryptoThreatDescription_;
          bitField0_ |= 0x00100000;
          onChanged();
        }
        if (other.hasCryptoDirection()) {
          cryptoDirection_ = other.cryptoDirection_;
          bitField0_ |= 0x00200000;
          onChanged();
        }
        if (other.hasCryptoDetectionState()) {
          cryptoDetectionState_ = other.cryptoDetectionState_;
          bitField0_ |= 0x00400000;
          onChanged();
        }
        if (other.hasCryptoDetectionDescribe()) {
          cryptoDetectionDescribe_ = other.cryptoDetectionDescribe_;
          bitField0_ |= 0x00800000;
          onChanged();
        }
        if (other.hasCryptoHandResult()) {
          cryptoHandResult_ = other.cryptoHandResult_;
          bitField0_ |= 0x01000000;
          onChanged();
        }
        if (other.hasCryptoFlowResult()) {
          cryptoFlowResult_ = other.cryptoFlowResult_;
          bitField0_ |= 0x02000000;
          onChanged();
        }
        if (other.hasCryptoCertResult()) {
          cryptoCertResult_ = other.cryptoCertResult_;
          bitField0_ |= 0x04000000;
          onChanged();
        }
        if (other.hasCryptoDomainResult()) {
          cryptoDomainResult_ = other.cryptoDomainResult_;
          bitField0_ |= 0x08000000;
          onChanged();
        }
        if (other.hasCryptoResult()) {
          cryptoResult_ = other.cryptoResult_;
          bitField0_ |= 0x10000000;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                cryptoStreamId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                cryptoEncrypted_ = input.readBool();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                cryptoAppName_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                cryptoAppTypeId_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                cryptoAppType_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                cryptoAppClassId_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 58: {
                cryptoAppClass_ = input.readBytes();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                cryptoActionType_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 72: {
                assetIdClient_ = input.readUInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                assetIdServer_ = input.readUInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 90: {
                cryptoRiskName_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                cryptoRiskLevel_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                cryptoCertFingerprint_ = input.readBytes();
                bitField0_ |= 0x00001000;
                break;
              } // case 106
              case 112: {
                cryptoRuleId_ = input.readUInt64();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 122: {
                cryptoRuleType_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                cryptoThreatSubtype_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                cryptoThreatLevel_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                cryptoThreatFamily_ = input.readBytes();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                cryptoThreatGroup_ = input.readBytes();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 162: {
                cryptoThreatDirection_ = input.readBytes();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 170: {
                cryptoThreatDescription_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 178: {
                cryptoDirection_ = input.readBytes();
                bitField0_ |= 0x00200000;
                break;
              } // case 178
              case 186: {
                cryptoDetectionState_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                cryptoDetectionDescribe_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                cryptoHandResult_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                cryptoFlowResult_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 218: {
                cryptoCertResult_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 226: {
                cryptoDomainResult_ = input.readBytes();
                bitField0_ |= 0x08000000;
                break;
              } // case 226
              case 234: {
                cryptoResult_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long cryptoStreamId_ ;
      /**
       * <pre>
       * 流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       * @return Whether the cryptoStreamId field is set.
       */
      @java.lang.Override
      public boolean hasCryptoStreamId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       * @return The cryptoStreamId.
       */
      @java.lang.Override
      public long getCryptoStreamId() {
        return cryptoStreamId_;
      }
      /**
       * <pre>
       * 流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       * @param value The cryptoStreamId to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoStreamId(long value) {

        cryptoStreamId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 流ID	7214956298192818176
       * </pre>
       *
       * <code>optional uint64 crypto_stream_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoStreamId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cryptoStreamId_ = 0L;
        onChanged();
        return this;
      }

      private boolean cryptoEncrypted_ ;
      /**
       * <pre>
       * 加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       * @return Whether the cryptoEncrypted field is set.
       */
      @java.lang.Override
      public boolean hasCryptoEncrypted() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       * @return The cryptoEncrypted.
       */
      @java.lang.Override
      public boolean getCryptoEncrypted() {
        return cryptoEncrypted_;
      }
      /**
       * <pre>
       * 加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       * @param value The cryptoEncrypted to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoEncrypted(boolean value) {

        cryptoEncrypted_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加密标识	0: 非加密应用；1: 加密应用
       * </pre>
       *
       * <code>optional bool crypto_encrypted = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoEncrypted() {
        bitField0_ = (bitField0_ & ~0x00000002);
        cryptoEncrypted_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoAppName_ = "";
      /**
       * <pre>
       * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       * @return Whether the cryptoAppName field is set.
       */
      public boolean hasCryptoAppName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       * @return The cryptoAppName.
       */
      public java.lang.String getCryptoAppName() {
        java.lang.Object ref = cryptoAppName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoAppName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       * @return The bytes for cryptoAppName.
       */
      public com.google.protobuf.ByteString
          getCryptoAppNameBytes() {
        java.lang.Object ref = cryptoAppName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoAppName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       * @param value The cryptoAppName to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoAppName_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoAppName() {
        cryptoAppName_ = getDefaultInstance().getCryptoAppName();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用名称	如高铁管家、高途、高德地图、驾考精灵、驾校宝典、驾校一点通、驴迹导游等
       * </pre>
       *
       * <code>optional string crypto_app_name = 3;</code>
       * @param value The bytes for cryptoAppName to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoAppName_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private int cryptoAppTypeId_ ;
      /**
       * <pre>
       * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       * @return Whether the cryptoAppTypeId field is set.
       */
      @java.lang.Override
      public boolean hasCryptoAppTypeId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       * @return The cryptoAppTypeId.
       */
      @java.lang.Override
      public int getCryptoAppTypeId() {
        return cryptoAppTypeId_;
      }
      /**
       * <pre>
       * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       * @param value The cryptoAppTypeId to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppTypeId(int value) {

        cryptoAppTypeId_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用类型ID	应用类型 ID，1-100 为通用应用类型，101-150 为敏感应用类型，151-250 为行业专属，251 以后为自定义应用
       * </pre>
       *
       * <code>optional uint32 crypto_app_type_id = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoAppTypeId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        cryptoAppTypeId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoAppType_ = "";
      /**
       * <pre>
       * 应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       * @return Whether the cryptoAppType field is set.
       */
      public boolean hasCryptoAppType() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       * @return The cryptoAppType.
       */
      public java.lang.String getCryptoAppType() {
        java.lang.Object ref = cryptoAppType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoAppType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       * @return The bytes for cryptoAppType.
       */
      public com.google.protobuf.ByteString
          getCryptoAppTypeBytes() {
        java.lang.Object ref = cryptoAppType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoAppType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       * @param value The cryptoAppType to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoAppType_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoAppType() {
        cryptoAppType_ = getDefaultInstance().getCryptoAppType();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用类型	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_type = 5;</code>
       * @param value The bytes for cryptoAppType to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoAppType_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private int cryptoAppClassId_ ;
      /**
       * <pre>
       * 应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       * @return Whether the cryptoAppClassId field is set.
       */
      @java.lang.Override
      public boolean hasCryptoAppClassId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       * @return The cryptoAppClassId.
       */
      @java.lang.Override
      public int getCryptoAppClassId() {
        return cryptoAppClassId_;
      }
      /**
       * <pre>
       * 应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       * @param value The cryptoAppClassId to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppClassId(int value) {

        cryptoAppClassId_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用分类ID	
       * </pre>
       *
       * <code>optional uint32 crypto_app_class_id = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoAppClassId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cryptoAppClassId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoAppClass_ = "";
      /**
       * <pre>
       * 应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       * @return Whether the cryptoAppClass field is set.
       */
      public boolean hasCryptoAppClass() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       * @return The cryptoAppClass.
       */
      public java.lang.String getCryptoAppClass() {
        java.lang.Object ref = cryptoAppClass_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoAppClass_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       * @return The bytes for cryptoAppClass.
       */
      public com.google.protobuf.ByteString
          getCryptoAppClassBytes() {
        java.lang.Object ref = cryptoAppClass_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoAppClass_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       * @param value The cryptoAppClass to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppClass(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoAppClass_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoAppClass() {
        cryptoAppClass_ = getDefaultInstance().getCryptoAppClass();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用分类	"云服务"
       * </pre>
       *
       * <code>optional string crypto_app_class = 7;</code>
       * @param value The bytes for cryptoAppClass to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoAppClassBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoAppClass_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoActionType_ = "";
      /**
       * <pre>
       * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       * @return Whether the cryptoActionType field is set.
       */
      public boolean hasCryptoActionType() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       * @return The cryptoActionType.
       */
      public java.lang.String getCryptoActionType() {
        java.lang.Object ref = cryptoActionType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoActionType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       * @return The bytes for cryptoActionType.
       */
      public com.google.protobuf.ByteString
          getCryptoActionTypeBytes() {
        java.lang.Object ref = cryptoActionType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoActionType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       * @param value The cryptoActionType to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoActionType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoActionType_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoActionType() {
        cryptoActionType_ = getDefaultInstance().getCryptoActionType();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 交互规则类型	如：点击支持、点击 VIP 模块、浏览、检索、检查更新、查地图、柚子街
       * </pre>
       *
       * <code>optional string crypto_action_type = 8;</code>
       * @param value The bytes for cryptoActionType to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoActionTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoActionType_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private int assetIdClient_ ;
      /**
       * <pre>
       * 客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       * @return Whether the assetIdClient field is set.
       */
      @java.lang.Override
      public boolean hasAssetIdClient() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       * @return The assetIdClient.
       */
      @java.lang.Override
      public int getAssetIdClient() {
        return assetIdClient_;
      }
      /**
       * <pre>
       * 客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       * @param value The assetIdClient to set.
       * @return This builder for chaining.
       */
      public Builder setAssetIdClient(int value) {

        assetIdClient_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端资产对应标识符	58
       * </pre>
       *
       * <code>optional uint32 asset_id_client = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssetIdClient() {
        bitField0_ = (bitField0_ & ~0x00000100);
        assetIdClient_ = 0;
        onChanged();
        return this;
      }

      private int assetIdServer_ ;
      /**
       * <pre>
       * 服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       * @return Whether the assetIdServer field is set.
       */
      @java.lang.Override
      public boolean hasAssetIdServer() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       * @return The assetIdServer.
       */
      @java.lang.Override
      public int getAssetIdServer() {
        return assetIdServer_;
      }
      /**
       * <pre>
       * 服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       * @param value The assetIdServer to set.
       * @return This builder for chaining.
       */
      public Builder setAssetIdServer(int value) {

        assetIdServer_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 服务端资产对应标识符	0
       * </pre>
       *
       * <code>optional uint32 asset_id_server = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearAssetIdServer() {
        bitField0_ = (bitField0_ & ~0x00000200);
        assetIdServer_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoRiskName_ = "";
      /**
       * <pre>
       * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       * @return Whether the cryptoRiskName field is set.
       */
      public boolean hasCryptoRiskName() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       * @return The cryptoRiskName.
       */
      public java.lang.String getCryptoRiskName() {
        java.lang.Object ref = cryptoRiskName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoRiskName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       * @return The bytes for cryptoRiskName.
       */
      public com.google.protobuf.ByteString
          getCryptoRiskNameBytes() {
        java.lang.Object ref = cryptoRiskName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoRiskName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       * @param value The cryptoRiskName to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoRiskName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoRiskName_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoRiskName() {
        cryptoRiskName_ = getDefaultInstance().getCryptoRiskName();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常标签	"('服务端证书链校验失败', '服务端叶子证书密钥用法异常数字签名', '服务端叶子证书增强密钥用法异常', '支持无认证加密套件', '支持弱加密加密套件', '支持弱摘要加密套件', '冗余密钥交换', '缺失密钥交换', '使用压缩')"
       * </pre>
       *
       * <code>optional string crypto_risk_name = 11;</code>
       * @param value The bytes for cryptoRiskName to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoRiskNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoRiskName_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoRiskLevel_ = "";
      /**
       * <pre>
       * 风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       * @return Whether the cryptoRiskLevel field is set.
       */
      public boolean hasCryptoRiskLevel() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       * @return The cryptoRiskLevel.
       */
      public java.lang.String getCryptoRiskLevel() {
        java.lang.Object ref = cryptoRiskLevel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoRiskLevel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       * @return The bytes for cryptoRiskLevel.
       */
      public com.google.protobuf.ByteString
          getCryptoRiskLevelBytes() {
        java.lang.Object ref = cryptoRiskLevel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoRiskLevel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       * @param value The cryptoRiskLevel to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoRiskLevel(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoRiskLevel_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoRiskLevel() {
        cryptoRiskLevel_ = getDefaultInstance().getCryptoRiskLevel();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 风险等级	"低"
       * </pre>
       *
       * <code>optional string crypto_risk_level = 12;</code>
       * @param value The bytes for cryptoRiskLevel to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoRiskLevelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoRiskLevel_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoCertFingerprint_ = "";
      /**
       * <pre>
       * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       * @return Whether the cryptoCertFingerprint field is set.
       */
      public boolean hasCryptoCertFingerprint() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       * @return The cryptoCertFingerprint.
       */
      public java.lang.String getCryptoCertFingerprint() {
        java.lang.Object ref = cryptoCertFingerprint_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoCertFingerprint_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       * @return The bytes for cryptoCertFingerprint.
       */
      public com.google.protobuf.ByteString
          getCryptoCertFingerprintBytes() {
        java.lang.Object ref = cryptoCertFingerprint_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoCertFingerprint_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       * @param value The cryptoCertFingerprint to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoCertFingerprint(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoCertFingerprint_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoCertFingerprint() {
        cryptoCertFingerprint_ = getDefaultInstance().getCryptoCertFingerprint();
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 证书指纹	"79CBC71FAE5C3D630ACC92A6F1BC77C083108DD6"
       * </pre>
       *
       * <code>optional string crypto_cert_fingerprint = 13;</code>
       * @param value The bytes for cryptoCertFingerprint to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoCertFingerprintBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoCertFingerprint_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }

      private long cryptoRuleId_ ;
      /**
       * <pre>
       * 威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       * @return Whether the cryptoRuleId field is set.
       */
      @java.lang.Override
      public boolean hasCryptoRuleId() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       * @return The cryptoRuleId.
       */
      @java.lang.Override
      public long getCryptoRuleId() {
        return cryptoRuleId_;
      }
      /**
       * <pre>
       * 威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       * @param value The cryptoRuleId to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoRuleId(long value) {

        cryptoRuleId_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁规则 ID	
       * </pre>
       *
       * <code>optional uint64 crypto_rule_id = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoRuleId() {
        bitField0_ = (bitField0_ & ~0x00002000);
        cryptoRuleId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoRuleType_ = "";
      /**
       * <pre>
       * 威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       * @return Whether the cryptoRuleType field is set.
       */
      public boolean hasCryptoRuleType() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       * @return The cryptoRuleType.
       */
      public java.lang.String getCryptoRuleType() {
        java.lang.Object ref = cryptoRuleType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoRuleType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       * @return The bytes for cryptoRuleType.
       */
      public com.google.protobuf.ByteString
          getCryptoRuleTypeBytes() {
        java.lang.Object ref = cryptoRuleType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoRuleType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       * @param value The cryptoRuleType to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoRuleType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoRuleType_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoRuleType() {
        cryptoRuleType_ = getDefaultInstance().getCryptoRuleType();
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁规则类型	"标准化规则"
       * </pre>
       *
       * <code>optional string crypto_rule_type = 15;</code>
       * @param value The bytes for cryptoRuleType to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoRuleTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoRuleType_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatSubtype_ = "";
      /**
       * <pre>
       * 威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       * @return Whether the cryptoThreatSubtype field is set.
       */
      public boolean hasCryptoThreatSubtype() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       * @return The cryptoThreatSubtype.
       */
      public java.lang.String getCryptoThreatSubtype() {
        java.lang.Object ref = cryptoThreatSubtype_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatSubtype_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       * @return The bytes for cryptoThreatSubtype.
       */
      public com.google.protobuf.ByteString
          getCryptoThreatSubtypeBytes() {
        java.lang.Object ref = cryptoThreatSubtype_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatSubtype_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       * @param value The cryptoThreatSubtype to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatSubtype(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatSubtype_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoThreatSubtype() {
        cryptoThreatSubtype_ = getDefaultInstance().getCryptoThreatSubtype();
        bitField0_ = (bitField0_ & ~0x00008000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁标签	"TCP 隧道"
       * </pre>
       *
       * <code>optional string crypto_threat_subtype = 16;</code>
       * @param value The bytes for cryptoThreatSubtype to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatSubtypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatSubtype_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatLevel_ = "";
      /**
       * <pre>
       * 威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       * @return Whether the cryptoThreatLevel field is set.
       */
      public boolean hasCryptoThreatLevel() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       * @return The cryptoThreatLevel.
       */
      public java.lang.String getCryptoThreatLevel() {
        java.lang.Object ref = cryptoThreatLevel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatLevel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       * @return The bytes for cryptoThreatLevel.
       */
      public com.google.protobuf.ByteString
          getCryptoThreatLevelBytes() {
        java.lang.Object ref = cryptoThreatLevel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatLevel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       * @param value The cryptoThreatLevel to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatLevel(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatLevel_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoThreatLevel() {
        cryptoThreatLevel_ = getDefaultInstance().getCryptoThreatLevel();
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁等级	分为高危、中危和低危
       * </pre>
       *
       * <code>optional string crypto_threat_level = 17;</code>
       * @param value The bytes for cryptoThreatLevel to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatLevelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatLevel_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatFamily_ = "";
      /**
       * <pre>
       * 威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       * @return Whether the cryptoThreatFamily field is set.
       */
      public boolean hasCryptoThreatFamily() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       * @return The cryptoThreatFamily.
       */
      public java.lang.String getCryptoThreatFamily() {
        java.lang.Object ref = cryptoThreatFamily_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatFamily_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       * @return The bytes for cryptoThreatFamily.
       */
      public com.google.protobuf.ByteString
          getCryptoThreatFamilyBytes() {
        java.lang.Object ref = cryptoThreatFamily_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatFamily_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       * @param value The cryptoThreatFamily to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatFamily(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatFamily_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoThreatFamily() {
        cryptoThreatFamily_ = getDefaultInstance().getCryptoThreatFamily();
        bitField0_ = (bitField0_ & ~0x00020000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁所属家族	"CobaltStrike"
       * </pre>
       *
       * <code>optional string crypto_threat_family = 18;</code>
       * @param value The bytes for cryptoThreatFamily to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatFamilyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatFamily_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatGroup_ = "";
      /**
       * <pre>
       * 威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       * @return Whether the cryptoThreatGroup field is set.
       */
      public boolean hasCryptoThreatGroup() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       * @return The cryptoThreatGroup.
       */
      public java.lang.String getCryptoThreatGroup() {
        java.lang.Object ref = cryptoThreatGroup_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatGroup_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       * @return The bytes for cryptoThreatGroup.
       */
      public com.google.protobuf.ByteString
          getCryptoThreatGroupBytes() {
        java.lang.Object ref = cryptoThreatGroup_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatGroup_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       * @param value The cryptoThreatGroup to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatGroup(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatGroup_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoThreatGroup() {
        cryptoThreatGroup_ = getDefaultInstance().getCryptoThreatGroup();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁组织	"Sidewinder 响尾蛇"
       * </pre>
       *
       * <code>optional string crypto_threat_group = 19;</code>
       * @param value The bytes for cryptoThreatGroup to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatGroupBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatGroup_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatDirection_ = "";
      /**
       * <pre>
       * 威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       * @return Whether the cryptoThreatDirection field is set.
       */
      public boolean hasCryptoThreatDirection() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       * @return The cryptoThreatDirection.
       */
      public java.lang.String getCryptoThreatDirection() {
        java.lang.Object ref = cryptoThreatDirection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatDirection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       * @return The bytes for cryptoThreatDirection.
       */
      public com.google.protobuf.ByteString
          getCryptoThreatDirectionBytes() {
        java.lang.Object ref = cryptoThreatDirection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatDirection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       * @param value The cryptoThreatDirection to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatDirection(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatDirection_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoThreatDirection() {
        cryptoThreatDirection_ = getDefaultInstance().getCryptoThreatDirection();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁方向	"俄罗斯"
       * </pre>
       *
       * <code>optional string crypto_threat_direction = 20;</code>
       * @param value The bytes for cryptoThreatDirection to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatDirectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatDirection_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoThreatDescription_ = "";
      /**
       * <pre>
       * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       * @return Whether the cryptoThreatDescription field is set.
       */
      public boolean hasCryptoThreatDescription() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       * @return The cryptoThreatDescription.
       */
      public java.lang.String getCryptoThreatDescription() {
        java.lang.Object ref = cryptoThreatDescription_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoThreatDescription_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       * @return The bytes for cryptoThreatDescription.
       */
      public com.google.protobuf.ByteString
          getCryptoThreatDescriptionBytes() {
        java.lang.Object ref = cryptoThreatDescription_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoThreatDescription_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       * @param value The cryptoThreatDescription to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatDescription(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatDescription_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoThreatDescription() {
        cryptoThreatDescription_ = getDefaultInstance().getCryptoThreatDescription();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详细描述	"一系列对TLS加密协议的扫描攻击，此类攻击通常针对HTTPS服务，攻击者使用的黑客工具为W3af.1.6.49_2019_kali2018_64"
       * </pre>
       *
       * <code>optional string crypto_threat_description = 21;</code>
       * @param value The bytes for cryptoThreatDescription to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoThreatDescriptionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoThreatDescription_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDirection_ = "";
      /**
       * <pre>
       * 攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       * @return Whether the cryptoDirection field is set.
       */
      public boolean hasCryptoDirection() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       * @return The cryptoDirection.
       */
      public java.lang.String getCryptoDirection() {
        java.lang.Object ref = cryptoDirection_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDirection_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       * @return The bytes for cryptoDirection.
       */
      public com.google.protobuf.ByteString
          getCryptoDirectionBytes() {
        java.lang.Object ref = cryptoDirection_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDirection_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       * @param value The cryptoDirection to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDirection(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDirection_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoDirection() {
        cryptoDirection_ = getDefaultInstance().getCryptoDirection();
        bitField0_ = (bitField0_ & ~0x00200000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击方向	入联风险、横向风险、出联风险
       * </pre>
       *
       * <code>optional string crypto_direction = 22;</code>
       * @param value The bytes for cryptoDirection to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDirectionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDirection_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDetectionState_ = "";
      /**
       * <pre>
       * 研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       * @return Whether the cryptoDetectionState field is set.
       */
      public boolean hasCryptoDetectionState() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       * @return The cryptoDetectionState.
       */
      public java.lang.String getCryptoDetectionState() {
        java.lang.Object ref = cryptoDetectionState_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDetectionState_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       * @return The bytes for cryptoDetectionState.
       */
      public com.google.protobuf.ByteString
          getCryptoDetectionStateBytes() {
        java.lang.Object ref = cryptoDetectionState_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDetectionState_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       * @param value The cryptoDetectionState to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDetectionState(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDetectionState_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoDetectionState() {
        cryptoDetectionState_ = getDefaultInstance().getCryptoDetectionState();
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 研判状态	未研判、误报、攻击行为、攻击成功、未知
       * </pre>
       *
       * <code>optional string crypto_detection_state = 23;</code>
       * @param value The bytes for cryptoDetectionState to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDetectionStateBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDetectionState_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDetectionDescribe_ = "";
      /**
       * <pre>
       * 研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       * @return Whether the cryptoDetectionDescribe field is set.
       */
      public boolean hasCryptoDetectionDescribe() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       * @return The cryptoDetectionDescribe.
       */
      public java.lang.String getCryptoDetectionDescribe() {
        java.lang.Object ref = cryptoDetectionDescribe_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDetectionDescribe_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       * @return The bytes for cryptoDetectionDescribe.
       */
      public com.google.protobuf.ByteString
          getCryptoDetectionDescribeBytes() {
        java.lang.Object ref = cryptoDetectionDescribe_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDetectionDescribe_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       * @param value The cryptoDetectionDescribe to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDetectionDescribe(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDetectionDescribe_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoDetectionDescribe() {
        cryptoDetectionDescribe_ = getDefaultInstance().getCryptoDetectionDescribe();
        bitField0_ = (bitField0_ & ~0x00800000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 研判原因	"Suricata"
       * </pre>
       *
       * <code>optional string crypto_detection_describe = 24;</code>
       * @param value The bytes for cryptoDetectionDescribe to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDetectionDescribeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDetectionDescribe_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoHandResult_ = "";
      /**
       * <pre>
       * 握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       * @return Whether the cryptoHandResult field is set.
       */
      public boolean hasCryptoHandResult() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * 握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       * @return The cryptoHandResult.
       */
      public java.lang.String getCryptoHandResult() {
        java.lang.Object ref = cryptoHandResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoHandResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       * @return The bytes for cryptoHandResult.
       */
      public com.google.protobuf.ByteString
          getCryptoHandResultBytes() {
        java.lang.Object ref = cryptoHandResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoHandResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       * @param value The cryptoHandResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoHandResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoHandResult_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoHandResult() {
        cryptoHandResult_ = getDefaultInstance().getCryptoHandResult();
        bitField0_ = (bitField0_ & ~0x01000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 握手评分	"100"
       * </pre>
       *
       * <code>optional string crypto_hand_result = 25;</code>
       * @param value The bytes for cryptoHandResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoHandResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoHandResult_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoFlowResult_ = "";
      /**
       * <pre>
       * 流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       * @return Whether the cryptoFlowResult field is set.
       */
      public boolean hasCryptoFlowResult() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <pre>
       * 流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       * @return The cryptoFlowResult.
       */
      public java.lang.String getCryptoFlowResult() {
        java.lang.Object ref = cryptoFlowResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoFlowResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       * @return The bytes for cryptoFlowResult.
       */
      public com.google.protobuf.ByteString
          getCryptoFlowResultBytes() {
        java.lang.Object ref = cryptoFlowResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoFlowResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       * @param value The cryptoFlowResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoFlowResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoFlowResult_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoFlowResult() {
        cryptoFlowResult_ = getDefaultInstance().getCryptoFlowResult();
        bitField0_ = (bitField0_ & ~0x02000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 流模型评分	"100"
       * </pre>
       *
       * <code>optional string crypto_flow_result = 26;</code>
       * @param value The bytes for cryptoFlowResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoFlowResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoFlowResult_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoCertResult_ = "";
      /**
       * <pre>
       * 证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       * @return Whether the cryptoCertResult field is set.
       */
      public boolean hasCryptoCertResult() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <pre>
       * 证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       * @return The cryptoCertResult.
       */
      public java.lang.String getCryptoCertResult() {
        java.lang.Object ref = cryptoCertResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoCertResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       * @return The bytes for cryptoCertResult.
       */
      public com.google.protobuf.ByteString
          getCryptoCertResultBytes() {
        java.lang.Object ref = cryptoCertResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoCertResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       * @param value The cryptoCertResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoCertResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoCertResult_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoCertResult() {
        cryptoCertResult_ = getDefaultInstance().getCryptoCertResult();
        bitField0_ = (bitField0_ & ~0x04000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 证书评分	"88"
       * </pre>
       *
       * <code>optional string crypto_cert_result = 27;</code>
       * @param value The bytes for cryptoCertResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoCertResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoCertResult_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoDomainResult_ = "";
      /**
       * <pre>
       * DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       * @return Whether the cryptoDomainResult field is set.
       */
      public boolean hasCryptoDomainResult() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <pre>
       * DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       * @return The cryptoDomainResult.
       */
      public java.lang.String getCryptoDomainResult() {
        java.lang.Object ref = cryptoDomainResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoDomainResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       * @return The bytes for cryptoDomainResult.
       */
      public com.google.protobuf.ByteString
          getCryptoDomainResultBytes() {
        java.lang.Object ref = cryptoDomainResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoDomainResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       * @param value The cryptoDomainResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDomainResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDomainResult_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoDomainResult() {
        cryptoDomainResult_ = getDefaultInstance().getCryptoDomainResult();
        bitField0_ = (bitField0_ & ~0x08000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * DNS评分	"81"
       * </pre>
       *
       * <code>optional string crypto_domain_result = 28;</code>
       * @param value The bytes for cryptoDomainResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoDomainResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoDomainResult_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }

      private java.lang.Object cryptoResult_ = "";
      /**
       * <pre>
       * 综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       * @return Whether the cryptoResult field is set.
       */
      public boolean hasCryptoResult() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <pre>
       * 综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       * @return The cryptoResult.
       */
      public java.lang.String getCryptoResult() {
        java.lang.Object ref = cryptoResult_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            cryptoResult_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       * @return The bytes for cryptoResult.
       */
      public com.google.protobuf.ByteString
          getCryptoResultBytes() {
        java.lang.Object ref = cryptoResult_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cryptoResult_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       * @param value The cryptoResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoResult(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoResult_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearCryptoResult() {
        cryptoResult_ = getDefaultInstance().getCryptoResult();
        bitField0_ = (bitField0_ & ~0x10000000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 综合评分	"95"
       * </pre>
       *
       * <code>optional string crypto_result = 29;</code>
       * @param value The bytes for cryptoResult to set.
       * @return This builder for chaining.
       */
      public Builder setCryptoResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cryptoResult_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:CRYPTO_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:CRYPTO_ALERT_INFO)
    private static final CryptoAlertInfo.CRYPTO_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new CryptoAlertInfo.CRYPTO_ALERT_INFO();
    }

    public static CryptoAlertInfo.CRYPTO_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CRYPTO_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<CRYPTO_ALERT_INFO>() {
      @java.lang.Override
      public CRYPTO_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CRYPTO_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CRYPTO_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public CryptoAlertInfo.CRYPTO_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_CRYPTO_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027CRYPTO_ALERT_INFO.proto\"\303\006\n\021CRYPTO_ALE" +
      "RT_INFO\022\030\n\020crypto_stream_id\030\001 \001(\004\022\030\n\020cry" +
      "pto_encrypted\030\002 \001(\010\022\027\n\017crypto_app_name\030\003" +
      " \001(\t\022\032\n\022crypto_app_type_id\030\004 \001(\r\022\027\n\017cryp" +
      "to_app_type\030\005 \001(\t\022\033\n\023crypto_app_class_id" +
      "\030\006 \001(\r\022\030\n\020crypto_app_class\030\007 \001(\t\022\032\n\022cryp" +
      "to_action_type\030\010 \001(\t\022\027\n\017asset_id_client\030" +
      "\t \001(\r\022\027\n\017asset_id_server\030\n \001(\r\022\030\n\020crypto" +
      "_risk_name\030\013 \001(\t\022\031\n\021crypto_risk_level\030\014 " +
      "\001(\t\022\037\n\027crypto_cert_fingerprint\030\r \001(\t\022\026\n\016" +
      "crypto_rule_id\030\016 \001(\004\022\030\n\020crypto_rule_type" +
      "\030\017 \001(\t\022\035\n\025crypto_threat_subtype\030\020 \001(\t\022\033\n" +
      "\023crypto_threat_level\030\021 \001(\t\022\034\n\024crypto_thr" +
      "eat_family\030\022 \001(\t\022\033\n\023crypto_threat_group\030" +
      "\023 \001(\t\022\037\n\027crypto_threat_direction\030\024 \001(\t\022!" +
      "\n\031crypto_threat_description\030\025 \001(\t\022\030\n\020cry" +
      "pto_direction\030\026 \001(\t\022\036\n\026crypto_detection_" +
      "state\030\027 \001(\t\022!\n\031crypto_detection_describe" +
      "\030\030 \001(\t\022\032\n\022crypto_hand_result\030\031 \001(\t\022\032\n\022cr" +
      "ypto_flow_result\030\032 \001(\t\022\032\n\022crypto_cert_re" +
      "sult\030\033 \001(\t\022\034\n\024crypto_domain_result\030\034 \001(\t" +
      "\022\025\n\rcrypto_result\030\035 \001(\tB\021B\017CryptoAlertIn" +
      "fo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_CRYPTO_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_CRYPTO_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_CRYPTO_ALERT_INFO_descriptor,
        new java.lang.String[] { "CryptoStreamId", "CryptoEncrypted", "CryptoAppName", "CryptoAppTypeId", "CryptoAppType", "CryptoAppClassId", "CryptoAppClass", "CryptoActionType", "AssetIdClient", "AssetIdServer", "CryptoRiskName", "CryptoRiskLevel", "CryptoCertFingerprint", "CryptoRuleId", "CryptoRuleType", "CryptoThreatSubtype", "CryptoThreatLevel", "CryptoThreatFamily", "CryptoThreatGroup", "CryptoThreatDirection", "CryptoThreatDescription", "CryptoDirection", "CryptoDetectionState", "CryptoDetectionDescribe", "CryptoHandResult", "CryptoFlowResult", "CryptoCertResult", "CryptoDomainResult", "CryptoResult", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
