package com.geeksec.common.constant;

import com.geeksec.entity.trans.InputCount;
import com.geeksec.proto.AlertLog;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.types.Row;
import org.apache.flink.util.OutputTag;


/**
 * <AUTHOR>
 */
public class AlertLogDeserializer {

    /**
     * 标准待处理多规则类型告警日志输出标签
     */
//    public static final OutputTag<AlertLog.ALERT_LOG> IOC_ALERT_DNS_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOC_ALERT_DNS", TypeInformation.of(AlertLog.ALERT_LOG.class));
//    public static final OutputTag<AlertLog.ALERT_LOG> IOC_ALERT_HTTP_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOC_ALERT_HTTP", TypeInformation.of(AlertLog.ALERT_LOG.class));
//    public static final OutputTag<AlertLog.ALERT_LOG> IOC_ALERT_X509_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOC_ALERT_X509", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOC_ALERT_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOC_ALERT_X509", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOA_ALERT_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOA_ALERT_X509", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> SANDBOX_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("SANDBOX_ALERT", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> EMAIL_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("EMAIL_ALERT", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> CRYPTO_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("CRYPTO_ALERT", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOT_ALERT_INFO_OUTPUT_TAG = new OutputTag<AlertLog.ALERT_LOG>("IOT_ALERT", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<InputCount> ALERT_COUNT_OUTPUT_TAG = new OutputTag<InputCount>("ALERT_COUNT_OUTPUT", TypeInformation.of(InputCount.class));

    /**
     * 节点分流OutPutTag
     */
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_IP = new OutputTag<>("TAG_IP", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_ORG = new OutputTag<>("TAG_ORG", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_CERT = new OutputTag<>("TAG_CERT", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_UA = new OutputTag<>("TAG_UA", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_OS = new OutputTag<>("TAG_OS", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_DEVICE = new OutputTag<>("TAG_DEVICE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_DOMAIN = new OutputTag<>("TAG_DOMAIN", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_APT_GROUP = new OutputTag<>("TAG_APT_GROUP", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_ATTACK = new OutputTag<>("TAG_ATTACK", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_MAIL = new OutputTag<>("TAG_MAIL", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_EMAIL = new OutputTag<>("TAG_EMAIL", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_ATTACH_FILE = new OutputTag<>("TAG_ATTACH_FILE", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_MALICIOUS_FAMILY = new OutputTag<>("TAG_MALICIOUS_FAMILY", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_TAG_LABEL = new OutputTag<>("TAG_LABEL", TypeInformation.of(Row.class));

    /**
     * 关联关系分流OutPutTag
     */
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_ip_belong_to_org = new OutputTag<>("EDGE_ip_belong_to_org", TypeInformation.of(Row.class));
    //    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_domain_belong_to_org = new OutputTag<>("EDGE_domain_belong_to_org", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_ua_belong_to_os = new OutputTag<>("EDGE_ua_belong_to_os", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_ua_belong_to_device = new OutputTag<>("EDGE_ua_belong_to_device", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_domain_belong_to_apt = new OutputTag<>("EDGE_domain_belong_to_apt", TypeInformation.of(Row.class));
//    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_email_belong_to_org = new OutputTag<>("EDGE_email_belong_to_org", TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_use_cert = new OutputTag<>("EDGE_client_use_cert", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_cert_issuer_to = new OutputTag<>("EDGE_cert_issuer_to", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_cert_subject_to = new OutputTag<>("EDGE_cert_subject_to", TypeInformation.of(Row.class));


    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_http_connect = new OutputTag<>("EDGE_http_connect", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_use_ua = new OutputTag<>("EDGE_client_use_ua", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_ua_connect_domain = new OutputTag<>("EDGE_ua_connect_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_http_connect_domain = new OutputTag<>("EDGE_client_http_connect_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_server_http_connect_domain = new OutputTag<>("EDGE_server_http_connect_domain", TypeInformation.of(Row.class));
    //    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_dns_query = new OutputTag<>("EDGE_dns_query", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_query_domain = new OutputTag<>("EDGE_client_query_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_client_query_dns_server = new OutputTag<>("EDGE_client_query_dns_server", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_dns_server_domain = new OutputTag<>("EDGE_dns_server_domain", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_parse_to = new OutputTag<>("EDGE_parse_to", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_make_attack = new OutputTag<>("EDGE_make_attack", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_attack_to = new OutputTag<>("EDGE_attack_to", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_make_attack_to = new OutputTag<>("EDGE_make_attack_to", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_has_label = new OutputTag<>("EDGE_has_label", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_apt_use_malicious = new OutputTag<>("EDGE_apt_use_malicious",TypeInformation.of(Row.class));

    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_write_mail = new OutputTag<>("EDGE_write_mail", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_mail_to = new OutputTag<>("EDGE_mail_to", TypeInformation.of(Row.class));
    public static final OutputTag<Row> ALARM_OUTPUT_EDGE_include_file = new OutputTag<>("EDGE_include_file", TypeInformation.of(Row.class));
}


