package com.geeksec.entity.trans;

import lombok.Data;

/**
 * @author: jerryzhou
 * @date: 2024/7/22 20:45
 * @Description:
 **/
@Data
public class FileTrans {
    // 文件名
    private String fileName;

    // 文件类型
    private String fileType;

    // 文件大小
    private Integer fileSize;

    // 文件md5值
    private String fileMd5;

    // 首次发现时间
    private Long firstSeen;

    // 网络活动行为
    private String networkBehavior;

    // 关联IP、域名
    private String associatedIp;

    // 是否恶意
    private Boolean malicious;

    // 恶意家族名称
    private String maliciousFamily;

    // 是否定向攻击
    private Boolean targeted;

    // APT团伙名称
    private String campaign;

    // 关联值
    private String associatedValue;

    // 邮件发送者,邮件附件发送者(Raw 原始)
    private String mailSenderRaw;

    // 邮件接收者,邮件附件接收者(Raw 原始)
    private String mailReceiverRaw;

    // 邮件主题
    private String mailSubject;

}
