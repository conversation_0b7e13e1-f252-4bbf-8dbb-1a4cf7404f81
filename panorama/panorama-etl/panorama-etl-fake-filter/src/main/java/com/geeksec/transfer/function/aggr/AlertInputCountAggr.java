package com.geeksec.transfer.function.aggr;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.common.functions.AggregateFunction;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class AlertInputCountAggr implements AggregateFunction<AlertLog.ALERT_LOG, Map<Integer,Integer>, Map<Integer,Integer>> {
    @Override
    public Map<Integer,Integer> createAccumulator() {
        return new ConcurrentHashMap<>();
    }

    @Override
    public Map<Integer,Integer> add(AlertLog.ALERT_LOG alertLog, Map<Integer,Integer> map) {
        int detectType = alertLog.getDetectType();
        map.put(detectType, map.getOrDefault(detectType, 0) + 1);
        return map;
    }

    @Override
    public Map<Integer,Integer> getResult(Map<Integer,Integer> map) {
        return map;
    }

    @Override
    public Map<Integer,Integer> merge(Map<Integer,Integer> map, Map<Integer,Integer> acc1) {
        map.keySet().forEach(key -> map.put(key, map.getOrDefault(key, 0) + acc1.getOrDefault(key, 0)));
        return map;
    }
}
