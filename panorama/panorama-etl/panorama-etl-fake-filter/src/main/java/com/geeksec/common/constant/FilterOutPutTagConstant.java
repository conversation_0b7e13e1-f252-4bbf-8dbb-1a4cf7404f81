package com.geeksec.common.constant;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * <AUTHOR>
 */
public class FilterOutPutTagConstant {

    public static final OutputTag<AlertLog.ALERT_LOG> IP_FILTER_CONTINUE =
            new OutputTag<>("IP_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IP_FILTER_TERMINATE =
            new OutputTag<>("IP_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> PROTOCOL_FILTER_TERMINATE =
            new OutputTag<AlertLog.ALERT_LOG>("PROTOCOL_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> PROTOCOL_FILTER_CONTINUE =
            new OutputTag<AlertLog.ALERT_LOG>("PROTOCOL_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOC_FILTER_CONTINUE =
            new OutputTag<AlertLog.ALERT_LOG>("IOC_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOC_FILTER_TERMINATE =
            new OutputTag<AlertLog.ALERT_LOG>("IOC_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOA_FILTER_CONTINUE =
            new OutputTag<AlertLog.ALERT_LOG>("IOA_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOA_FILTER_TERMINATE =
            new OutputTag<AlertLog.ALERT_LOG>("IOA_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> EMAIL_FILTER_CONTINUE =
            new OutputTag<AlertLog.ALERT_LOG>("EMAIL_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> EMAIL_FILTER_TERMINATE =
            new OutputTag<AlertLog.ALERT_LOG>("EMAIL_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> CRYPTO_FILTER_CONTINUE =
            new OutputTag<AlertLog.ALERT_LOG>("CRYPTO_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> CRYPTO_FILTER_TERMINATE =
            new OutputTag<AlertLog.ALERT_LOG>("CRYPTO_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> SANDBOX_FILTER_CONTINUE =
            new OutputTag<AlertLog.ALERT_LOG>("SANDBOX_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> SANDBOX_FILTER_TERMINATE =
            new OutputTag<AlertLog.ALERT_LOG>("SANDBOX_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOT_FILTER_CONTINUE =
            new OutputTag<AlertLog.ALERT_LOG>("IOT_FILTER_CONTINUE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOT_FILTER_TERMINATE =
            new OutputTag<AlertLog.ALERT_LOG>("IOT_FILTER_TERMINATE", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> ALERT_LOG_AGGR =
            new OutputTag<AlertLog.ALERT_LOG>("ALERT_LOG_AGGR", TypeInformation.of(AlertLog.ALERT_LOG.class));
}
