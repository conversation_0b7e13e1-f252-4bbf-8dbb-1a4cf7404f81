package com.geeksec.proto.message;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: IIOT_ALERT_INFO.proto
// Protobuf Java Version: 4.29.4

public final class IiotAlertInfo {
  private IiotAlertInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      IiotAlertInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IIOT_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IIOT_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     * @return Whether the iiotAlertType field is set.
     */
    boolean hasIiotAlertType();
    /**
     * <pre>
     * 告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     * @return The iiotAlertType.
     */
    int getIiotAlertType();

    /**
     * <pre>
     * 告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     * @return Whether the iiotRuleId field is set.
     */
    boolean hasIiotRuleId();
    /**
     * <pre>
     * 告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     * @return The iiotRuleId.
     */
    int getIiotRuleId();

    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     * @return Whether the iiotName field is set.
     */
    boolean hasIiotName();
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     * @return The iiotName.
     */
    java.lang.String getIiotName();
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     * @return The bytes for iiotName.
     */
    com.google.protobuf.ByteString
        getIiotNameBytes();

    /**
     * <pre>
     * 协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     * @return Whether the iiotAnalysis field is set.
     */
    boolean hasIiotAnalysis();
    /**
     * <pre>
     * 协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     * @return The iiotAnalysis.
     */
    java.lang.String getIiotAnalysis();
    /**
     * <pre>
     * 协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     * @return The bytes for iiotAnalysis.
     */
    com.google.protobuf.ByteString
        getIiotAnalysisBytes();

    /**
     * <pre>
     * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     * @return Whether the iiotAbnormalType field is set.
     */
    boolean hasIiotAbnormalType();
    /**
     * <pre>
     * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     * @return The iiotAbnormalType.
     */
    int getIiotAbnormalType();

    /**
     * <pre>
     * 关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     * @return Whether the iiotActionType field is set.
     */
    boolean hasIiotActionType();
    /**
     * <pre>
     * 关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     * @return The iiotActionType.
     */
    int getIiotActionType();

    /**
     * <pre>
     * 漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     * @return Whether the iiotVul field is set.
     */
    boolean hasIiotVul();
    /**
     * <pre>
     * 漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     * @return The iiotVul.
     */
    java.lang.String getIiotVul();
    /**
     * <pre>
     * 漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     * @return The bytes for iiotVul.
     */
    com.google.protobuf.ByteString
        getIiotVulBytes();

    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     * @return Whether the iiotRefer field is set.
     */
    boolean hasIiotRefer();
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     * @return The iiotRefer.
     */
    java.lang.String getIiotRefer();
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     * @return The bytes for iiotRefer.
     */
    com.google.protobuf.ByteString
        getIiotReferBytes();

    /**
     * <pre>
     * 设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     * @return Whether the iiotVendor field is set.
     */
    boolean hasIiotVendor();
    /**
     * <pre>
     * 设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     * @return The iiotVendor.
     */
    java.lang.String getIiotVendor();
    /**
     * <pre>
     * 设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     * @return The bytes for iiotVendor.
     */
    com.google.protobuf.ByteString
        getIiotVendorBytes();

    /**
     * <pre>
     * 设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     * @return Whether the iiotDeviceType field is set.
     */
    boolean hasIiotDeviceType();
    /**
     * <pre>
     * 设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     * @return The iiotDeviceType.
     */
    java.lang.String getIiotDeviceType();
    /**
     * <pre>
     * 设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     * @return The bytes for iiotDeviceType.
     */
    com.google.protobuf.ByteString
        getIiotDeviceTypeBytes();

    /**
     * <pre>
     * 设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     * @return Whether the iiotModel field is set.
     */
    boolean hasIiotModel();
    /**
     * <pre>
     * 设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     * @return The iiotModel.
     */
    java.lang.String getIiotModel();
    /**
     * <pre>
     * 设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     * @return The bytes for iiotModel.
     */
    com.google.protobuf.ByteString
        getIiotModelBytes();

    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     * @return Whether the iiotDetailInfo field is set.
     */
    boolean hasIiotDetailInfo();
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     * @return The iiotDetailInfo.
     */
    java.lang.String getIiotDetailInfo();
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     * @return The bytes for iiotDetailInfo.
     */
    com.google.protobuf.ByteString
        getIiotDetailInfoBytes();
  }
  /**
   * <pre>
   * 工业物联网告警信息	
   * </pre>
   *
   * Protobuf type {@code IIOT_ALERT_INFO}
   */
  public static final class IIOT_ALERT_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:IIOT_ALERT_INFO)
      IIOT_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        IIOT_ALERT_INFO.class.getName());
    }
    // Use IIOT_ALERT_INFO.newBuilder() to construct.
    private IIOT_ALERT_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private IIOT_ALERT_INFO() {
      iiotName_ = "";
      iiotAnalysis_ = "";
      iiotVul_ = "";
      iiotRefer_ = "";
      iiotVendor_ = "";
      iiotDeviceType_ = "";
      iiotModel_ = "";
      iiotDetailInfo_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IiotAlertInfo.IIOT_ALERT_INFO.class, IiotAlertInfo.IIOT_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int IIOT_ALERT_TYPE_FIELD_NUMBER = 1;
    private int iiotAlertType_ = 0;
    /**
     * <pre>
     * 告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     * @return Whether the iiotAlertType field is set.
     */
    @java.lang.Override
    public boolean hasIiotAlertType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 告警类型	0：其他；1：ics；2：iot
     * </pre>
     *
     * <code>required int32 iiot_alert_type = 1;</code>
     * @return The iiotAlertType.
     */
    @java.lang.Override
    public int getIiotAlertType() {
      return iiotAlertType_;
    }

    public static final int IIOT_RULE_ID_FIELD_NUMBER = 2;
    private int iiotRuleId_ = 0;
    /**
     * <pre>
     * 告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     * @return Whether the iiotRuleId field is set.
     */
    @java.lang.Override
    public boolean hasIiotRuleId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 告警规则ID	
     * </pre>
     *
     * <code>required int32 iiot_rule_id = 2;</code>
     * @return The iiotRuleId.
     */
    @java.lang.Override
    public int getIiotRuleId() {
      return iiotRuleId_;
    }

    public static final int IIOT_NAME_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotName_ = "";
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     * @return Whether the iiotName field is set.
     */
    @java.lang.Override
    public boolean hasIiotName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     * @return The iiotName.
     */
    @java.lang.Override
    public java.lang.String getIiotName() {
      java.lang.Object ref = iiotName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 告警名称	
     * </pre>
     *
     * <code>required string iiot_name = 3;</code>
     * @return The bytes for iiotName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotNameBytes() {
      java.lang.Object ref = iiotName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_ANALYSIS_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotAnalysis_ = "";
    /**
     * <pre>
     * 协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     * @return Whether the iiotAnalysis field is set.
     */
    @java.lang.Override
    public boolean hasIiotAnalysis() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     * @return The iiotAnalysis.
     */
    @java.lang.Override
    public java.lang.String getIiotAnalysis() {
      java.lang.Object ref = iiotAnalysis_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotAnalysis_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 协议解析概述	
     * </pre>
     *
     * <code>optional string iiot_analysis = 4;</code>
     * @return The bytes for iiotAnalysis.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotAnalysisBytes() {
      java.lang.Object ref = iiotAnalysis_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotAnalysis_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_ABNORMAL_TYPE_FIELD_NUMBER = 5;
    private int iiotAbnormalType_ = 0;
    /**
     * <pre>
     * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     * @return Whether the iiotAbnormalType field is set.
     */
    @java.lang.Override
    public boolean hasIiotAbnormalType() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
     * </pre>
     *
     * <code>required int32 iiot_abnormal_type = 5;</code>
     * @return The iiotAbnormalType.
     */
    @java.lang.Override
    public int getIiotAbnormalType() {
      return iiotAbnormalType_;
    }

    public static final int IIOT_ACTION_TYPE_FIELD_NUMBER = 6;
    private int iiotActionType_ = 0;
    /**
     * <pre>
     * 关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     * @return Whether the iiotActionType field is set.
     */
    @java.lang.Override
    public boolean hasIiotActionType() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 关键操作类型	
     * </pre>
     *
     * <code>required int32 iiot_action_type = 6;</code>
     * @return The iiotActionType.
     */
    @java.lang.Override
    public int getIiotActionType() {
      return iiotActionType_;
    }

    public static final int IIOT_VUL_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotVul_ = "";
    /**
     * <pre>
     * 漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     * @return Whether the iiotVul field is set.
     */
    @java.lang.Override
    public boolean hasIiotVul() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     * @return The iiotVul.
     */
    @java.lang.Override
    public java.lang.String getIiotVul() {
      java.lang.Object ref = iiotVul_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotVul_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 漏洞号	
     * </pre>
     *
     * <code>optional string iiot_vul = 7;</code>
     * @return The bytes for iiotVul.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotVulBytes() {
      java.lang.Object ref = iiotVul_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotVul_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_REFER_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotRefer_ = "";
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     * @return Whether the iiotRefer field is set.
     */
    @java.lang.Override
    public boolean hasIiotRefer() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     * @return The iiotRefer.
     */
    @java.lang.Override
    public java.lang.String getIiotRefer() {
      java.lang.Object ref = iiotRefer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotRefer_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 引用文档	参考文档
     * </pre>
     *
     * <code>required string iiot_refer = 8;</code>
     * @return The bytes for iiotRefer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotReferBytes() {
      java.lang.Object ref = iiotRefer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotRefer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_VENDOR_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotVendor_ = "";
    /**
     * <pre>
     * 设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     * @return Whether the iiotVendor field is set.
     */
    @java.lang.Override
    public boolean hasIiotVendor() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     * @return The iiotVendor.
     */
    @java.lang.Override
    public java.lang.String getIiotVendor() {
      java.lang.Object ref = iiotVendor_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotVendor_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 设备/软件厂商	
     * </pre>
     *
     * <code>optional string iiot_vendor = 9;</code>
     * @return The bytes for iiotVendor.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotVendorBytes() {
      java.lang.Object ref = iiotVendor_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotVendor_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_DEVICE_TYPE_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotDeviceType_ = "";
    /**
     * <pre>
     * 设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     * @return Whether the iiotDeviceType field is set.
     */
    @java.lang.Override
    public boolean hasIiotDeviceType() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     * @return The iiotDeviceType.
     */
    @java.lang.Override
    public java.lang.String getIiotDeviceType() {
      java.lang.Object ref = iiotDeviceType_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotDeviceType_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 设备/软件类型	
     * </pre>
     *
     * <code>optional string iiot_device_type = 10;</code>
     * @return The bytes for iiotDeviceType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotDeviceTypeBytes() {
      java.lang.Object ref = iiotDeviceType_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotDeviceType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_MODEL_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotModel_ = "";
    /**
     * <pre>
     * 设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     * @return Whether the iiotModel field is set.
     */
    @java.lang.Override
    public boolean hasIiotModel() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     * @return The iiotModel.
     */
    @java.lang.Override
    public java.lang.String getIiotModel() {
      java.lang.Object ref = iiotModel_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotModel_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 设备型号/软件版本	
     * </pre>
     *
     * <code>optional string iiot_model = 11;</code>
     * @return The bytes for iiotModel.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotModelBytes() {
      java.lang.Object ref = iiotModel_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotModel_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IIOT_DETAIL_INFO_FIELD_NUMBER = 12;
    @SuppressWarnings("serial")
    private volatile java.lang.Object iiotDetailInfo_ = "";
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     * @return Whether the iiotDetailInfo field is set.
     */
    @java.lang.Override
    public boolean hasIiotDetailInfo() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     * @return The iiotDetailInfo.
     */
    @java.lang.Override
    public java.lang.String getIiotDetailInfo() {
      java.lang.Object ref = iiotDetailInfo_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          iiotDetailInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
     * </pre>
     *
     * <code>required string iiot_detail_info = 12;</code>
     * @return The bytes for iiotDetailInfo.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIiotDetailInfoBytes() {
      java.lang.Object ref = iiotDetailInfo_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        iiotDetailInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIiotAlertType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotRuleId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotAbnormalType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotActionType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotRefer()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIiotDetailInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, iiotAlertType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, iiotRuleId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, iiotName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, iiotAnalysis_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, iiotAbnormalType_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, iiotActionType_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 7, iiotVul_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, iiotRefer_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, iiotVendor_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, iiotDeviceType_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, iiotModel_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 12, iiotDetailInfo_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, iiotAlertType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, iiotRuleId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, iiotName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, iiotAnalysis_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, iiotAbnormalType_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, iiotActionType_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(7, iiotVul_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, iiotRefer_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(9, iiotVendor_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, iiotDeviceType_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, iiotModel_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(12, iiotDetailInfo_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IiotAlertInfo.IIOT_ALERT_INFO)) {
        return super.equals(obj);
      }
      IiotAlertInfo.IIOT_ALERT_INFO other = (IiotAlertInfo.IIOT_ALERT_INFO) obj;

      if (hasIiotAlertType() != other.hasIiotAlertType()) return false;
      if (hasIiotAlertType()) {
        if (getIiotAlertType()
            != other.getIiotAlertType()) return false;
      }
      if (hasIiotRuleId() != other.hasIiotRuleId()) return false;
      if (hasIiotRuleId()) {
        if (getIiotRuleId()
            != other.getIiotRuleId()) return false;
      }
      if (hasIiotName() != other.hasIiotName()) return false;
      if (hasIiotName()) {
        if (!getIiotName()
            .equals(other.getIiotName())) return false;
      }
      if (hasIiotAnalysis() != other.hasIiotAnalysis()) return false;
      if (hasIiotAnalysis()) {
        if (!getIiotAnalysis()
            .equals(other.getIiotAnalysis())) return false;
      }
      if (hasIiotAbnormalType() != other.hasIiotAbnormalType()) return false;
      if (hasIiotAbnormalType()) {
        if (getIiotAbnormalType()
            != other.getIiotAbnormalType()) return false;
      }
      if (hasIiotActionType() != other.hasIiotActionType()) return false;
      if (hasIiotActionType()) {
        if (getIiotActionType()
            != other.getIiotActionType()) return false;
      }
      if (hasIiotVul() != other.hasIiotVul()) return false;
      if (hasIiotVul()) {
        if (!getIiotVul()
            .equals(other.getIiotVul())) return false;
      }
      if (hasIiotRefer() != other.hasIiotRefer()) return false;
      if (hasIiotRefer()) {
        if (!getIiotRefer()
            .equals(other.getIiotRefer())) return false;
      }
      if (hasIiotVendor() != other.hasIiotVendor()) return false;
      if (hasIiotVendor()) {
        if (!getIiotVendor()
            .equals(other.getIiotVendor())) return false;
      }
      if (hasIiotDeviceType() != other.hasIiotDeviceType()) return false;
      if (hasIiotDeviceType()) {
        if (!getIiotDeviceType()
            .equals(other.getIiotDeviceType())) return false;
      }
      if (hasIiotModel() != other.hasIiotModel()) return false;
      if (hasIiotModel()) {
        if (!getIiotModel()
            .equals(other.getIiotModel())) return false;
      }
      if (hasIiotDetailInfo() != other.hasIiotDetailInfo()) return false;
      if (hasIiotDetailInfo()) {
        if (!getIiotDetailInfo()
            .equals(other.getIiotDetailInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIiotAlertType()) {
        hash = (37 * hash) + IIOT_ALERT_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAlertType();
      }
      if (hasIiotRuleId()) {
        hash = (37 * hash) + IIOT_RULE_ID_FIELD_NUMBER;
        hash = (53 * hash) + getIiotRuleId();
      }
      if (hasIiotName()) {
        hash = (37 * hash) + IIOT_NAME_FIELD_NUMBER;
        hash = (53 * hash) + getIiotName().hashCode();
      }
      if (hasIiotAnalysis()) {
        hash = (37 * hash) + IIOT_ANALYSIS_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAnalysis().hashCode();
      }
      if (hasIiotAbnormalType()) {
        hash = (37 * hash) + IIOT_ABNORMAL_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAbnormalType();
      }
      if (hasIiotActionType()) {
        hash = (37 * hash) + IIOT_ACTION_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotActionType();
      }
      if (hasIiotVul()) {
        hash = (37 * hash) + IIOT_VUL_FIELD_NUMBER;
        hash = (53 * hash) + getIiotVul().hashCode();
      }
      if (hasIiotRefer()) {
        hash = (37 * hash) + IIOT_REFER_FIELD_NUMBER;
        hash = (53 * hash) + getIiotRefer().hashCode();
      }
      if (hasIiotVendor()) {
        hash = (37 * hash) + IIOT_VENDOR_FIELD_NUMBER;
        hash = (53 * hash) + getIiotVendor().hashCode();
      }
      if (hasIiotDeviceType()) {
        hash = (37 * hash) + IIOT_DEVICE_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getIiotDeviceType().hashCode();
      }
      if (hasIiotModel()) {
        hash = (37 * hash) + IIOT_MODEL_FIELD_NUMBER;
        hash = (53 * hash) + getIiotModel().hashCode();
      }
      if (hasIiotDetailInfo()) {
        hash = (37 * hash) + IIOT_DETAIL_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIiotDetailInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static IiotAlertInfo.IIOT_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static IiotAlertInfo.IIOT_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IiotAlertInfo.IIOT_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IiotAlertInfo.IIOT_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 工业物联网告警信息	
     * </pre>
     *
     * Protobuf type {@code IIOT_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IIOT_ALERT_INFO)
        IiotAlertInfo.IIOT_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IiotAlertInfo.IIOT_ALERT_INFO.class, IiotAlertInfo.IIOT_ALERT_INFO.Builder.class);
      }

      // Construct using IiotAlertInfo.IIOT_ALERT_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        iiotAlertType_ = 0;
        iiotRuleId_ = 0;
        iiotName_ = "";
        iiotAnalysis_ = "";
        iiotAbnormalType_ = 0;
        iiotActionType_ = 0;
        iiotVul_ = "";
        iiotRefer_ = "";
        iiotVendor_ = "";
        iiotDeviceType_ = "";
        iiotModel_ = "";
        iiotDetailInfo_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IiotAlertInfo.internal_static_IIOT_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public IiotAlertInfo.IIOT_ALERT_INFO getDefaultInstanceForType() {
        return IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IiotAlertInfo.IIOT_ALERT_INFO build() {
        IiotAlertInfo.IIOT_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IiotAlertInfo.IIOT_ALERT_INFO buildPartial() {
        IiotAlertInfo.IIOT_ALERT_INFO result = new IiotAlertInfo.IIOT_ALERT_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(IiotAlertInfo.IIOT_ALERT_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.iiotAlertType_ = iiotAlertType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.iiotRuleId_ = iiotRuleId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.iiotName_ = iiotName_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.iiotAnalysis_ = iiotAnalysis_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.iiotAbnormalType_ = iiotAbnormalType_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.iiotActionType_ = iiotActionType_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.iiotVul_ = iiotVul_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.iiotRefer_ = iiotRefer_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.iiotVendor_ = iiotVendor_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.iiotDeviceType_ = iiotDeviceType_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.iiotModel_ = iiotModel_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.iiotDetailInfo_ = iiotDetailInfo_;
          to_bitField0_ |= 0x00000800;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IiotAlertInfo.IIOT_ALERT_INFO) {
          return mergeFrom((IiotAlertInfo.IIOT_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IiotAlertInfo.IIOT_ALERT_INFO other) {
        if (other == IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasIiotAlertType()) {
          setIiotAlertType(other.getIiotAlertType());
        }
        if (other.hasIiotRuleId()) {
          setIiotRuleId(other.getIiotRuleId());
        }
        if (other.hasIiotName()) {
          iiotName_ = other.iiotName_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasIiotAnalysis()) {
          iiotAnalysis_ = other.iiotAnalysis_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (other.hasIiotAbnormalType()) {
          setIiotAbnormalType(other.getIiotAbnormalType());
        }
        if (other.hasIiotActionType()) {
          setIiotActionType(other.getIiotActionType());
        }
        if (other.hasIiotVul()) {
          iiotVul_ = other.iiotVul_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (other.hasIiotRefer()) {
          iiotRefer_ = other.iiotRefer_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (other.hasIiotVendor()) {
          iiotVendor_ = other.iiotVendor_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (other.hasIiotDeviceType()) {
          iiotDeviceType_ = other.iiotDeviceType_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (other.hasIiotModel()) {
          iiotModel_ = other.iiotModel_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        if (other.hasIiotDetailInfo()) {
          iiotDetailInfo_ = other.iiotDetailInfo_;
          bitField0_ |= 0x00000800;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIiotAlertType()) {
          return false;
        }
        if (!hasIiotRuleId()) {
          return false;
        }
        if (!hasIiotName()) {
          return false;
        }
        if (!hasIiotAbnormalType()) {
          return false;
        }
        if (!hasIiotActionType()) {
          return false;
        }
        if (!hasIiotRefer()) {
          return false;
        }
        if (!hasIiotDetailInfo()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                iiotAlertType_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                iiotRuleId_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                iiotName_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                iiotAnalysis_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                iiotAbnormalType_ = input.readInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                iiotActionType_ = input.readInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 58: {
                iiotVul_ = input.readBytes();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                iiotRefer_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                iiotVendor_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                iiotDeviceType_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                iiotModel_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              case 98: {
                iiotDetailInfo_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int iiotAlertType_ ;
      /**
       * <pre>
       * 告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       * @return Whether the iiotAlertType field is set.
       */
      @java.lang.Override
      public boolean hasIiotAlertType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       * @return The iiotAlertType.
       */
      @java.lang.Override
      public int getIiotAlertType() {
        return iiotAlertType_;
      }
      /**
       * <pre>
       * 告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       * @param value The iiotAlertType to set.
       * @return This builder for chaining.
       */
      public Builder setIiotAlertType(int value) {

        iiotAlertType_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警类型	0：其他；1：ics；2：iot
       * </pre>
       *
       * <code>required int32 iiot_alert_type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotAlertType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        iiotAlertType_ = 0;
        onChanged();
        return this;
      }

      private int iiotRuleId_ ;
      /**
       * <pre>
       * 告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       * @return Whether the iiotRuleId field is set.
       */
      @java.lang.Override
      public boolean hasIiotRuleId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       * @return The iiotRuleId.
       */
      @java.lang.Override
      public int getIiotRuleId() {
        return iiotRuleId_;
      }
      /**
       * <pre>
       * 告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       * @param value The iiotRuleId to set.
       * @return This builder for chaining.
       */
      public Builder setIiotRuleId(int value) {

        iiotRuleId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警规则ID	
       * </pre>
       *
       * <code>required int32 iiot_rule_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotRuleId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        iiotRuleId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object iiotName_ = "";
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       * @return Whether the iiotName field is set.
       */
      public boolean hasIiotName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       * @return The iiotName.
       */
      public java.lang.String getIiotName() {
        java.lang.Object ref = iiotName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       * @return The bytes for iiotName.
       */
      public com.google.protobuf.ByteString
          getIiotNameBytes() {
        java.lang.Object ref = iiotName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       * @param value The iiotName to set.
       * @return This builder for chaining.
       */
      public Builder setIiotName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotName_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotName() {
        iiotName_ = getDefaultInstance().getIiotName();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警名称	
       * </pre>
       *
       * <code>required string iiot_name = 3;</code>
       * @param value The bytes for iiotName to set.
       * @return This builder for chaining.
       */
      public Builder setIiotNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotName_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object iiotAnalysis_ = "";
      /**
       * <pre>
       * 协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       * @return Whether the iiotAnalysis field is set.
       */
      public boolean hasIiotAnalysis() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       * @return The iiotAnalysis.
       */
      public java.lang.String getIiotAnalysis() {
        java.lang.Object ref = iiotAnalysis_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotAnalysis_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       * @return The bytes for iiotAnalysis.
       */
      public com.google.protobuf.ByteString
          getIiotAnalysisBytes() {
        java.lang.Object ref = iiotAnalysis_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotAnalysis_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       * @param value The iiotAnalysis to set.
       * @return This builder for chaining.
       */
      public Builder setIiotAnalysis(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotAnalysis_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotAnalysis() {
        iiotAnalysis_ = getDefaultInstance().getIiotAnalysis();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 协议解析概述	
       * </pre>
       *
       * <code>optional string iiot_analysis = 4;</code>
       * @param value The bytes for iiotAnalysis to set.
       * @return This builder for chaining.
       */
      public Builder setIiotAnalysisBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotAnalysis_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private int iiotAbnormalType_ ;
      /**
       * <pre>
       * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       * @return Whether the iiotAbnormalType field is set.
       */
      @java.lang.Override
      public boolean hasIiotAbnormalType() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       * @return The iiotAbnormalType.
       */
      @java.lang.Override
      public int getIiotAbnormalType() {
        return iiotAbnormalType_;
      }
      /**
       * <pre>
       * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       * @param value The iiotAbnormalType to set.
       * @return This builder for chaining.
       */
      public Builder setIiotAbnormalType(int value) {

        iiotAbnormalType_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常规约类型	"1、字段标识错误2、取值超出范围 3、关联取值错误4、内部长度错误5、包总长度错误6、数据编码错误7、单元数据错误8、数据校验错误"
       * </pre>
       *
       * <code>required int32 iiot_abnormal_type = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotAbnormalType() {
        bitField0_ = (bitField0_ & ~0x00000010);
        iiotAbnormalType_ = 0;
        onChanged();
        return this;
      }

      private int iiotActionType_ ;
      /**
       * <pre>
       * 关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       * @return Whether the iiotActionType field is set.
       */
      @java.lang.Override
      public boolean hasIiotActionType() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       * @return The iiotActionType.
       */
      @java.lang.Override
      public int getIiotActionType() {
        return iiotActionType_;
      }
      /**
       * <pre>
       * 关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       * @param value The iiotActionType to set.
       * @return This builder for chaining.
       */
      public Builder setIiotActionType(int value) {

        iiotActionType_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关键操作类型	
       * </pre>
       *
       * <code>required int32 iiot_action_type = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotActionType() {
        bitField0_ = (bitField0_ & ~0x00000020);
        iiotActionType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object iiotVul_ = "";
      /**
       * <pre>
       * 漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       * @return Whether the iiotVul field is set.
       */
      public boolean hasIiotVul() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       * @return The iiotVul.
       */
      public java.lang.String getIiotVul() {
        java.lang.Object ref = iiotVul_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotVul_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       * @return The bytes for iiotVul.
       */
      public com.google.protobuf.ByteString
          getIiotVulBytes() {
        java.lang.Object ref = iiotVul_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotVul_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       * @param value The iiotVul to set.
       * @return This builder for chaining.
       */
      public Builder setIiotVul(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotVul_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotVul() {
        iiotVul_ = getDefaultInstance().getIiotVul();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 漏洞号	
       * </pre>
       *
       * <code>optional string iiot_vul = 7;</code>
       * @param value The bytes for iiotVul to set.
       * @return This builder for chaining.
       */
      public Builder setIiotVulBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotVul_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private java.lang.Object iiotRefer_ = "";
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       * @return Whether the iiotRefer field is set.
       */
      public boolean hasIiotRefer() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       * @return The iiotRefer.
       */
      public java.lang.String getIiotRefer() {
        java.lang.Object ref = iiotRefer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotRefer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       * @return The bytes for iiotRefer.
       */
      public com.google.protobuf.ByteString
          getIiotReferBytes() {
        java.lang.Object ref = iiotRefer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotRefer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       * @param value The iiotRefer to set.
       * @return This builder for chaining.
       */
      public Builder setIiotRefer(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotRefer_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotRefer() {
        iiotRefer_ = getDefaultInstance().getIiotRefer();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 引用文档	参考文档
       * </pre>
       *
       * <code>required string iiot_refer = 8;</code>
       * @param value The bytes for iiotRefer to set.
       * @return This builder for chaining.
       */
      public Builder setIiotReferBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotRefer_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private java.lang.Object iiotVendor_ = "";
      /**
       * <pre>
       * 设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       * @return Whether the iiotVendor field is set.
       */
      public boolean hasIiotVendor() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       * @return The iiotVendor.
       */
      public java.lang.String getIiotVendor() {
        java.lang.Object ref = iiotVendor_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotVendor_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       * @return The bytes for iiotVendor.
       */
      public com.google.protobuf.ByteString
          getIiotVendorBytes() {
        java.lang.Object ref = iiotVendor_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotVendor_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       * @param value The iiotVendor to set.
       * @return This builder for chaining.
       */
      public Builder setIiotVendor(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotVendor_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotVendor() {
        iiotVendor_ = getDefaultInstance().getIiotVendor();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备/软件厂商	
       * </pre>
       *
       * <code>optional string iiot_vendor = 9;</code>
       * @param value The bytes for iiotVendor to set.
       * @return This builder for chaining.
       */
      public Builder setIiotVendorBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotVendor_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object iiotDeviceType_ = "";
      /**
       * <pre>
       * 设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       * @return Whether the iiotDeviceType field is set.
       */
      public boolean hasIiotDeviceType() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       * @return The iiotDeviceType.
       */
      public java.lang.String getIiotDeviceType() {
        java.lang.Object ref = iiotDeviceType_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotDeviceType_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       * @return The bytes for iiotDeviceType.
       */
      public com.google.protobuf.ByteString
          getIiotDeviceTypeBytes() {
        java.lang.Object ref = iiotDeviceType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotDeviceType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       * @param value The iiotDeviceType to set.
       * @return This builder for chaining.
       */
      public Builder setIiotDeviceType(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotDeviceType_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotDeviceType() {
        iiotDeviceType_ = getDefaultInstance().getIiotDeviceType();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备/软件类型	
       * </pre>
       *
       * <code>optional string iiot_device_type = 10;</code>
       * @param value The bytes for iiotDeviceType to set.
       * @return This builder for chaining.
       */
      public Builder setIiotDeviceTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotDeviceType_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object iiotModel_ = "";
      /**
       * <pre>
       * 设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       * @return Whether the iiotModel field is set.
       */
      public boolean hasIiotModel() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       * @return The iiotModel.
       */
      public java.lang.String getIiotModel() {
        java.lang.Object ref = iiotModel_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotModel_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       * @return The bytes for iiotModel.
       */
      public com.google.protobuf.ByteString
          getIiotModelBytes() {
        java.lang.Object ref = iiotModel_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotModel_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       * @param value The iiotModel to set.
       * @return This builder for chaining.
       */
      public Builder setIiotModel(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotModel_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotModel() {
        iiotModel_ = getDefaultInstance().getIiotModel();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 设备型号/软件版本	
       * </pre>
       *
       * <code>optional string iiot_model = 11;</code>
       * @param value The bytes for iiotModel to set.
       * @return This builder for chaining.
       */
      public Builder setIiotModelBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotModel_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      private java.lang.Object iiotDetailInfo_ = "";
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       * @return Whether the iiotDetailInfo field is set.
       */
      public boolean hasIiotDetailInfo() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       * @return The iiotDetailInfo.
       */
      public java.lang.String getIiotDetailInfo() {
        java.lang.Object ref = iiotDetailInfo_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            iiotDetailInfo_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       * @return The bytes for iiotDetailInfo.
       */
      public com.google.protobuf.ByteString
          getIiotDetailInfoBytes() {
        java.lang.Object ref = iiotDetailInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          iiotDetailInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       * @param value The iiotDetailInfo to set.
       * @return This builder for chaining.
       */
      public Builder setIiotDetailInfo(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        iiotDetailInfo_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearIiotDetailInfo() {
        iiotDetailInfo_ = getDefaultInstance().getIiotDetailInfo();
        bitField0_ = (bitField0_ & ~0x00000800);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁详情描述	"威胁详情：漏洞详情：修复方案:"
       * </pre>
       *
       * <code>required string iiot_detail_info = 12;</code>
       * @param value The bytes for iiotDetailInfo to set.
       * @return This builder for chaining.
       */
      public Builder setIiotDetailInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        iiotDetailInfo_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:IIOT_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:IIOT_ALERT_INFO)
    private static final IiotAlertInfo.IIOT_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IiotAlertInfo.IIOT_ALERT_INFO();
    }

    public static IiotAlertInfo.IIOT_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IIOT_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IIOT_ALERT_INFO>() {
      @java.lang.Override
      public IIOT_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IIOT_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IIOT_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IiotAlertInfo.IIOT_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IIOT_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_IIOT_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025IIOT_ALERT_INFO.proto\"\243\002\n\017IIOT_ALERT_I" +
      "NFO\022\027\n\017iiot_alert_type\030\001 \002(\005\022\024\n\014iiot_rul" +
      "e_id\030\002 \002(\005\022\021\n\tiiot_name\030\003 \002(\t\022\025\n\riiot_an" +
      "alysis\030\004 \001(\t\022\032\n\022iiot_abnormal_type\030\005 \002(\005" +
      "\022\030\n\020iiot_action_type\030\006 \002(\005\022\020\n\010iiot_vul\030\007" +
      " \001(\t\022\022\n\niiot_refer\030\010 \002(\t\022\023\n\013iiot_vendor\030" +
      "\t \001(\t\022\030\n\020iiot_device_type\030\n \001(\t\022\022\n\niiot_" +
      "model\030\013 \001(\t\022\030\n\020iiot_detail_info\030\014 \002(\tB\017B" +
      "\rIiotAlertInfo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_IIOT_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IIOT_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_IIOT_ALERT_INFO_descriptor,
        new java.lang.String[] { "IiotAlertType", "IiotRuleId", "IiotName", "IiotAnalysis", "IiotAbnormalType", "IiotActionType", "IiotVul", "IiotRefer", "IiotVendor", "IiotDeviceType", "IiotModel", "IiotDetailInfo", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
