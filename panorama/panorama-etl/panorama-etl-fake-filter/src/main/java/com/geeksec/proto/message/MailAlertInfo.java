package com.geeksec.proto.message;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: MAIL_ALERT_INFO.proto
// Protobuf Java Version: 4.29.4

public final class MailAlertInfo {
  private MailAlertInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      MailAlertInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MAIL_ALERT_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:MAIL_ALERT_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     * @return Whether the emailSender field is set.
     */
    boolean hasEmailSender();
    /**
     * <pre>
     * 发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     * @return The emailSender.
     */
    java.lang.String getEmailSender();
    /**
     * <pre>
     * 发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     * @return The bytes for emailSender.
     */
    com.google.protobuf.ByteString
        getEmailSenderBytes();

    /**
     * <pre>
     * 收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     * @return Whether the emailReceiver field is set.
     */
    boolean hasEmailReceiver();
    /**
     * <pre>
     * 收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     * @return The emailReceiver.
     */
    java.lang.String getEmailReceiver();
    /**
     * <pre>
     * 收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     * @return The bytes for emailReceiver.
     */
    com.google.protobuf.ByteString
        getEmailReceiverBytes();

    /**
     * <pre>
     * 邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     * @return Whether the emailSubject field is set.
     */
    boolean hasEmailSubject();
    /**
     * <pre>
     * 邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     * @return The emailSubject.
     */
    java.lang.String getEmailSubject();
    /**
     * <pre>
     * 邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     * @return The bytes for emailSubject.
     */
    com.google.protobuf.ByteString
        getEmailSubjectBytes();

    /**
     * <pre>
     * 邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     * @return Whether the emailContent field is set.
     */
    boolean hasEmailContent();
    /**
     * <pre>
     * 邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     * @return The emailContent.
     */
    java.lang.String getEmailContent();
    /**
     * <pre>
     * 邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     * @return The bytes for emailContent.
     */
    com.google.protobuf.ByteString
        getEmailContentBytes();

    /**
     * <pre>
     * 关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     * @return Whether the emailAttachmentMd5 field is set.
     */
    boolean hasEmailAttachmentMd5();
    /**
     * <pre>
     * 关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     * @return The emailAttachmentMd5.
     */
    java.lang.String getEmailAttachmentMd5();
    /**
     * <pre>
     * 关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     * @return The bytes for emailAttachmentMd5.
     */
    com.google.protobuf.ByteString
        getEmailAttachmentMd5Bytes();

    /**
     * <pre>
     * 关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     * @return Whether the emailAttachmentResult field is set.
     */
    boolean hasEmailAttachmentResult();
    /**
     * <pre>
     * 关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     * @return The emailAttachmentResult.
     */
    long getEmailAttachmentResult();

    /**
     * <pre>
     * 所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     * @return Whether the emailIndustry field is set.
     */
    boolean hasEmailIndustry();
    /**
     * <pre>
     * 所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     * @return The emailIndustry.
     */
    java.lang.String getEmailIndustry();
    /**
     * <pre>
     * 所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     * @return The bytes for emailIndustry.
     */
    com.google.protobuf.ByteString
        getEmailIndustryBytes();

    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @return A list containing the emailIntents.
     */
    java.util.List<java.lang.String>
        getEmailIntentsList();
    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @return The count of emailIntents.
     */
    int getEmailIntentsCount();
    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @param index The index of the element to return.
     * @return The emailIntents at the given index.
     */
    java.lang.String getEmailIntents(int index);
    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @param index The index of the value to return.
     * @return The bytes of the emailIntents at the given index.
     */
    com.google.protobuf.ByteString
        getEmailIntentsBytes(int index);

    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @return A list containing the emailAnomalyTags.
     */
    java.util.List<java.lang.String>
        getEmailAnomalyTagsList();
    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @return The count of emailAnomalyTags.
     */
    int getEmailAnomalyTagsCount();
    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @param index The index of the element to return.
     * @return The emailAnomalyTags at the given index.
     */
    java.lang.String getEmailAnomalyTags(int index);
    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @param index The index of the value to return.
     * @return The bytes of the emailAnomalyTags at the given index.
     */
    com.google.protobuf.ByteString
        getEmailAnomalyTagsBytes(int index);

    /**
     * <pre>
     * 告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     * @return Whether the emailAlertReason field is set.
     */
    boolean hasEmailAlertReason();
    /**
     * <pre>
     * 告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     * @return The emailAlertReason.
     */
    java.lang.String getEmailAlertReason();
    /**
     * <pre>
     * 告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     * @return The bytes for emailAlertReason.
     */
    com.google.protobuf.ByteString
        getEmailAlertReasonBytes();
  }
  /**
   * Protobuf type {@code MAIL_ALERT_INFO}
   */
  public static final class MAIL_ALERT_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:MAIL_ALERT_INFO)
      MAIL_ALERT_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        MAIL_ALERT_INFO.class.getName());
    }
    // Use MAIL_ALERT_INFO.newBuilder() to construct.
    private MAIL_ALERT_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MAIL_ALERT_INFO() {
      emailSender_ = "";
      emailReceiver_ = "";
      emailSubject_ = "";
      emailContent_ = "";
      emailAttachmentMd5_ = "";
      emailIndustry_ = "";
      emailIntents_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      emailAnomalyTags_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      emailAlertReason_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailAlertInfo.internal_static_MAIL_ALERT_INFO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailAlertInfo.internal_static_MAIL_ALERT_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              MailAlertInfo.MAIL_ALERT_INFO.class, MailAlertInfo.MAIL_ALERT_INFO.Builder.class);
    }

    private int bitField0_;
    public static final int EMAIL_SENDER_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object emailSender_ = "";
    /**
     * <pre>
     * 发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     * @return Whether the emailSender field is set.
     */
    @java.lang.Override
    public boolean hasEmailSender() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     * @return The emailSender.
     */
    @java.lang.Override
    public java.lang.String getEmailSender() {
      java.lang.Object ref = emailSender_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailSender_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 发件人	
     * </pre>
     *
     * <code>optional string email_sender = 1;</code>
     * @return The bytes for emailSender.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getEmailSenderBytes() {
      java.lang.Object ref = emailSender_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailSender_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_RECEIVER_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object emailReceiver_ = "";
    /**
     * <pre>
     * 收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     * @return Whether the emailReceiver field is set.
     */
    @java.lang.Override
    public boolean hasEmailReceiver() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     * @return The emailReceiver.
     */
    @java.lang.Override
    public java.lang.String getEmailReceiver() {
      java.lang.Object ref = emailReceiver_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailReceiver_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 收件人	
     * </pre>
     *
     * <code>optional string email_receiver = 2;</code>
     * @return The bytes for emailReceiver.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getEmailReceiverBytes() {
      java.lang.Object ref = emailReceiver_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailReceiver_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_SUBJECT_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object emailSubject_ = "";
    /**
     * <pre>
     * 邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     * @return Whether the emailSubject field is set.
     */
    @java.lang.Override
    public boolean hasEmailSubject() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     * @return The emailSubject.
     */
    @java.lang.Override
    public java.lang.String getEmailSubject() {
      java.lang.Object ref = emailSubject_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailSubject_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 邮件主题	
     * </pre>
     *
     * <code>optional string email_subject = 3;</code>
     * @return The bytes for emailSubject.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getEmailSubjectBytes() {
      java.lang.Object ref = emailSubject_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailSubject_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_CONTENT_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object emailContent_ = "";
    /**
     * <pre>
     * 邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     * @return Whether the emailContent field is set.
     */
    @java.lang.Override
    public boolean hasEmailContent() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     * @return The emailContent.
     */
    @java.lang.Override
    public java.lang.String getEmailContent() {
      java.lang.Object ref = emailContent_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailContent_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 邮件正文	
     * </pre>
     *
     * <code>optional string email_content = 4;</code>
     * @return The bytes for emailContent.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getEmailContentBytes() {
      java.lang.Object ref = emailContent_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailContent_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_ATTACHMENT_MD5_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object emailAttachmentMd5_ = "";
    /**
     * <pre>
     * 关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     * @return Whether the emailAttachmentMd5 field is set.
     */
    @java.lang.Override
    public boolean hasEmailAttachmentMd5() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     * @return The emailAttachmentMd5.
     */
    @java.lang.Override
    public java.lang.String getEmailAttachmentMd5() {
      java.lang.Object ref = emailAttachmentMd5_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailAttachmentMd5_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 关联附件	
     * </pre>
     *
     * <code>optional string email_attachment_md5 = 5;</code>
     * @return The bytes for emailAttachmentMd5.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getEmailAttachmentMd5Bytes() {
      java.lang.Object ref = emailAttachmentMd5_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailAttachmentMd5_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_ATTACHMENT_RESULT_FIELD_NUMBER = 6;
    private long emailAttachmentResult_ = 0L;
    /**
     * <pre>
     * 关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     * @return Whether the emailAttachmentResult field is set.
     */
    @java.lang.Override
    public boolean hasEmailAttachmentResult() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 关联日志	
     * </pre>
     *
     * <code>optional uint64 email_attachment_result = 6;</code>
     * @return The emailAttachmentResult.
     */
    @java.lang.Override
    public long getEmailAttachmentResult() {
      return emailAttachmentResult_;
    }

    public static final int EMAIL_INDUSTRY_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object emailIndustry_ = "";
    /**
     * <pre>
     * 所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     * @return Whether the emailIndustry field is set.
     */
    @java.lang.Override
    public boolean hasEmailIndustry() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     * @return The emailIndustry.
     */
    @java.lang.Override
    public java.lang.String getEmailIndustry() {
      java.lang.Object ref = emailIndustry_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailIndustry_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 所属行业	军事、外交、金融、通信、基建
     * </pre>
     *
     * <code>optional string email_industry = 7;</code>
     * @return The bytes for emailIndustry.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getEmailIndustryBytes() {
      java.lang.Object ref = emailIndustry_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailIndustry_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EMAIL_INTENTS_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList emailIntents_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @return A list containing the emailIntents.
     */
    public com.google.protobuf.ProtocolStringList
        getEmailIntentsList() {
      return emailIntents_;
    }
    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @return The count of emailIntents.
     */
    public int getEmailIntentsCount() {
      return emailIntents_.size();
    }
    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @param index The index of the element to return.
     * @return The emailIntents at the given index.
     */
    public java.lang.String getEmailIntents(int index) {
      return emailIntents_.get(index);
    }
    /**
     * <pre>
     * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
     * </pre>
     *
     * <code>repeated string email_intents = 8;</code>
     * @param index The index of the value to return.
     * @return The bytes of the emailIntents at the given index.
     */
    public com.google.protobuf.ByteString
        getEmailIntentsBytes(int index) {
      return emailIntents_.getByteString(index);
    }

    public static final int EMAIL_ANOMALY_TAGS_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private com.google.protobuf.LazyStringArrayList emailAnomalyTags_ =
        com.google.protobuf.LazyStringArrayList.emptyList();
    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @return A list containing the emailAnomalyTags.
     */
    public com.google.protobuf.ProtocolStringList
        getEmailAnomalyTagsList() {
      return emailAnomalyTags_;
    }
    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @return The count of emailAnomalyTags.
     */
    public int getEmailAnomalyTagsCount() {
      return emailAnomalyTags_.size();
    }
    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @param index The index of the element to return.
     * @return The emailAnomalyTags at the given index.
     */
    public java.lang.String getEmailAnomalyTags(int index) {
      return emailAnomalyTags_.get(index);
    }
    /**
     * <pre>
     * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
     * </pre>
     *
     * <code>repeated string email_anomaly_tags = 9;</code>
     * @param index The index of the value to return.
     * @return The bytes of the emailAnomalyTags at the given index.
     */
    public com.google.protobuf.ByteString
        getEmailAnomalyTagsBytes(int index) {
      return emailAnomalyTags_.getByteString(index);
    }

    public static final int EMAIL_ALERT_REASON_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object emailAlertReason_ = "";
    /**
     * <pre>
     * 告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     * @return Whether the emailAlertReason field is set.
     */
    @java.lang.Override
    public boolean hasEmailAlertReason() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     * @return The emailAlertReason.
     */
    @java.lang.Override
    public java.lang.String getEmailAlertReason() {
      java.lang.Object ref = emailAlertReason_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          emailAlertReason_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 告警原因	
     * </pre>
     *
     * <code>optional string email_alert_reason = 10;</code>
     * @return The bytes for emailAlertReason.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getEmailAlertReasonBytes() {
      java.lang.Object ref = emailAlertReason_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        emailAlertReason_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, emailSender_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, emailReceiver_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, emailSubject_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, emailContent_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, emailAttachmentMd5_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeUInt64(6, emailAttachmentResult_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 7, emailIndustry_);
      }
      for (int i = 0; i < emailIntents_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, emailIntents_.getRaw(i));
      }
      for (int i = 0; i < emailAnomalyTags_.size(); i++) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, emailAnomalyTags_.getRaw(i));
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, emailAlertReason_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, emailSender_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, emailReceiver_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, emailSubject_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, emailContent_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, emailAttachmentMd5_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, emailAttachmentResult_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(7, emailIndustry_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < emailIntents_.size(); i++) {
          dataSize += computeStringSizeNoTag(emailIntents_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getEmailIntentsList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < emailAnomalyTags_.size(); i++) {
          dataSize += computeStringSizeNoTag(emailAnomalyTags_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getEmailAnomalyTagsList().size();
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, emailAlertReason_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof MailAlertInfo.MAIL_ALERT_INFO)) {
        return super.equals(obj);
      }
      MailAlertInfo.MAIL_ALERT_INFO other = (MailAlertInfo.MAIL_ALERT_INFO) obj;

      if (hasEmailSender() != other.hasEmailSender()) return false;
      if (hasEmailSender()) {
        if (!getEmailSender()
            .equals(other.getEmailSender())) return false;
      }
      if (hasEmailReceiver() != other.hasEmailReceiver()) return false;
      if (hasEmailReceiver()) {
        if (!getEmailReceiver()
            .equals(other.getEmailReceiver())) return false;
      }
      if (hasEmailSubject() != other.hasEmailSubject()) return false;
      if (hasEmailSubject()) {
        if (!getEmailSubject()
            .equals(other.getEmailSubject())) return false;
      }
      if (hasEmailContent() != other.hasEmailContent()) return false;
      if (hasEmailContent()) {
        if (!getEmailContent()
            .equals(other.getEmailContent())) return false;
      }
      if (hasEmailAttachmentMd5() != other.hasEmailAttachmentMd5()) return false;
      if (hasEmailAttachmentMd5()) {
        if (!getEmailAttachmentMd5()
            .equals(other.getEmailAttachmentMd5())) return false;
      }
      if (hasEmailAttachmentResult() != other.hasEmailAttachmentResult()) return false;
      if (hasEmailAttachmentResult()) {
        if (getEmailAttachmentResult()
            != other.getEmailAttachmentResult()) return false;
      }
      if (hasEmailIndustry() != other.hasEmailIndustry()) return false;
      if (hasEmailIndustry()) {
        if (!getEmailIndustry()
            .equals(other.getEmailIndustry())) return false;
      }
      if (!getEmailIntentsList()
          .equals(other.getEmailIntentsList())) return false;
      if (!getEmailAnomalyTagsList()
          .equals(other.getEmailAnomalyTagsList())) return false;
      if (hasEmailAlertReason() != other.hasEmailAlertReason()) return false;
      if (hasEmailAlertReason()) {
        if (!getEmailAlertReason()
            .equals(other.getEmailAlertReason())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEmailSender()) {
        hash = (37 * hash) + EMAIL_SENDER_FIELD_NUMBER;
        hash = (53 * hash) + getEmailSender().hashCode();
      }
      if (hasEmailReceiver()) {
        hash = (37 * hash) + EMAIL_RECEIVER_FIELD_NUMBER;
        hash = (53 * hash) + getEmailReceiver().hashCode();
      }
      if (hasEmailSubject()) {
        hash = (37 * hash) + EMAIL_SUBJECT_FIELD_NUMBER;
        hash = (53 * hash) + getEmailSubject().hashCode();
      }
      if (hasEmailContent()) {
        hash = (37 * hash) + EMAIL_CONTENT_FIELD_NUMBER;
        hash = (53 * hash) + getEmailContent().hashCode();
      }
      if (hasEmailAttachmentMd5()) {
        hash = (37 * hash) + EMAIL_ATTACHMENT_MD5_FIELD_NUMBER;
        hash = (53 * hash) + getEmailAttachmentMd5().hashCode();
      }
      if (hasEmailAttachmentResult()) {
        hash = (37 * hash) + EMAIL_ATTACHMENT_RESULT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEmailAttachmentResult());
      }
      if (hasEmailIndustry()) {
        hash = (37 * hash) + EMAIL_INDUSTRY_FIELD_NUMBER;
        hash = (53 * hash) + getEmailIndustry().hashCode();
      }
      if (getEmailIntentsCount() > 0) {
        hash = (37 * hash) + EMAIL_INTENTS_FIELD_NUMBER;
        hash = (53 * hash) + getEmailIntentsList().hashCode();
      }
      if (getEmailAnomalyTagsCount() > 0) {
        hash = (37 * hash) + EMAIL_ANOMALY_TAGS_FIELD_NUMBER;
        hash = (53 * hash) + getEmailAnomalyTagsList().hashCode();
      }
      if (hasEmailAlertReason()) {
        hash = (37 * hash) + EMAIL_ALERT_REASON_FIELD_NUMBER;
        hash = (53 * hash) + getEmailAlertReason().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static MailAlertInfo.MAIL_ALERT_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static MailAlertInfo.MAIL_ALERT_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static MailAlertInfo.MAIL_ALERT_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(MailAlertInfo.MAIL_ALERT_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code MAIL_ALERT_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:MAIL_ALERT_INFO)
        MailAlertInfo.MAIL_ALERT_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailAlertInfo.internal_static_MAIL_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailAlertInfo.internal_static_MAIL_ALERT_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                MailAlertInfo.MAIL_ALERT_INFO.class, MailAlertInfo.MAIL_ALERT_INFO.Builder.class);
      }

      // Construct using MailAlertInfo.MAIL_ALERT_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        emailSender_ = "";
        emailReceiver_ = "";
        emailSubject_ = "";
        emailContent_ = "";
        emailAttachmentMd5_ = "";
        emailAttachmentResult_ = 0L;
        emailIndustry_ = "";
        emailIntents_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        emailAnomalyTags_ =
            com.google.protobuf.LazyStringArrayList.emptyList();
        emailAlertReason_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailAlertInfo.internal_static_MAIL_ALERT_INFO_descriptor;
      }

      @java.lang.Override
      public MailAlertInfo.MAIL_ALERT_INFO getDefaultInstanceForType() {
        return MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public MailAlertInfo.MAIL_ALERT_INFO build() {
        MailAlertInfo.MAIL_ALERT_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public MailAlertInfo.MAIL_ALERT_INFO buildPartial() {
        MailAlertInfo.MAIL_ALERT_INFO result = new MailAlertInfo.MAIL_ALERT_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(MailAlertInfo.MAIL_ALERT_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.emailSender_ = emailSender_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.emailReceiver_ = emailReceiver_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.emailSubject_ = emailSubject_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.emailContent_ = emailContent_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.emailAttachmentMd5_ = emailAttachmentMd5_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.emailAttachmentResult_ = emailAttachmentResult_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.emailIndustry_ = emailIndustry_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          emailIntents_.makeImmutable();
          result.emailIntents_ = emailIntents_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          emailAnomalyTags_.makeImmutable();
          result.emailAnomalyTags_ = emailAnomalyTags_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.emailAlertReason_ = emailAlertReason_;
          to_bitField0_ |= 0x00000080;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof MailAlertInfo.MAIL_ALERT_INFO) {
          return mergeFrom((MailAlertInfo.MAIL_ALERT_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(MailAlertInfo.MAIL_ALERT_INFO other) {
        if (other == MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance()) return this;
        if (other.hasEmailSender()) {
          emailSender_ = other.emailSender_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasEmailReceiver()) {
          emailReceiver_ = other.emailReceiver_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasEmailSubject()) {
          emailSubject_ = other.emailSubject_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasEmailContent()) {
          emailContent_ = other.emailContent_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (other.hasEmailAttachmentMd5()) {
          emailAttachmentMd5_ = other.emailAttachmentMd5_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.hasEmailAttachmentResult()) {
          setEmailAttachmentResult(other.getEmailAttachmentResult());
        }
        if (other.hasEmailIndustry()) {
          emailIndustry_ = other.emailIndustry_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (!other.emailIntents_.isEmpty()) {
          if (emailIntents_.isEmpty()) {
            emailIntents_ = other.emailIntents_;
            bitField0_ |= 0x00000080;
          } else {
            ensureEmailIntentsIsMutable();
            emailIntents_.addAll(other.emailIntents_);
          }
          onChanged();
        }
        if (!other.emailAnomalyTags_.isEmpty()) {
          if (emailAnomalyTags_.isEmpty()) {
            emailAnomalyTags_ = other.emailAnomalyTags_;
            bitField0_ |= 0x00000100;
          } else {
            ensureEmailAnomalyTagsIsMutable();
            emailAnomalyTags_.addAll(other.emailAnomalyTags_);
          }
          onChanged();
        }
        if (other.hasEmailAlertReason()) {
          emailAlertReason_ = other.emailAlertReason_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                emailSender_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                emailReceiver_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                emailSubject_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                emailContent_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                emailAttachmentMd5_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                emailAttachmentResult_ = input.readUInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 58: {
                emailIndustry_ = input.readBytes();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureEmailIntentsIsMutable();
                emailIntents_.add(bs);
                break;
              } // case 66
              case 74: {
                com.google.protobuf.ByteString bs = input.readBytes();
                ensureEmailAnomalyTagsIsMutable();
                emailAnomalyTags_.add(bs);
                break;
              } // case 74
              case 82: {
                emailAlertReason_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object emailSender_ = "";
      /**
       * <pre>
       * 发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       * @return Whether the emailSender field is set.
       */
      public boolean hasEmailSender() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       * @return The emailSender.
       */
      public java.lang.String getEmailSender() {
        java.lang.Object ref = emailSender_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailSender_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       * @return The bytes for emailSender.
       */
      public com.google.protobuf.ByteString
          getEmailSenderBytes() {
        java.lang.Object ref = emailSender_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailSender_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       * @param value The emailSender to set.
       * @return This builder for chaining.
       */
      public Builder setEmailSender(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        emailSender_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailSender() {
        emailSender_ = getDefaultInstance().getEmailSender();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发件人	
       * </pre>
       *
       * <code>optional string email_sender = 1;</code>
       * @param value The bytes for emailSender to set.
       * @return This builder for chaining.
       */
      public Builder setEmailSenderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailSender_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object emailReceiver_ = "";
      /**
       * <pre>
       * 收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       * @return Whether the emailReceiver field is set.
       */
      public boolean hasEmailReceiver() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       * @return The emailReceiver.
       */
      public java.lang.String getEmailReceiver() {
        java.lang.Object ref = emailReceiver_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailReceiver_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       * @return The bytes for emailReceiver.
       */
      public com.google.protobuf.ByteString
          getEmailReceiverBytes() {
        java.lang.Object ref = emailReceiver_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailReceiver_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       * @param value The emailReceiver to set.
       * @return This builder for chaining.
       */
      public Builder setEmailReceiver(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        emailReceiver_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailReceiver() {
        emailReceiver_ = getDefaultInstance().getEmailReceiver();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收件人	
       * </pre>
       *
       * <code>optional string email_receiver = 2;</code>
       * @param value The bytes for emailReceiver to set.
       * @return This builder for chaining.
       */
      public Builder setEmailReceiverBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailReceiver_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object emailSubject_ = "";
      /**
       * <pre>
       * 邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       * @return Whether the emailSubject field is set.
       */
      public boolean hasEmailSubject() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       * @return The emailSubject.
       */
      public java.lang.String getEmailSubject() {
        java.lang.Object ref = emailSubject_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailSubject_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       * @return The bytes for emailSubject.
       */
      public com.google.protobuf.ByteString
          getEmailSubjectBytes() {
        java.lang.Object ref = emailSubject_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailSubject_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       * @param value The emailSubject to set.
       * @return This builder for chaining.
       */
      public Builder setEmailSubject(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        emailSubject_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailSubject() {
        emailSubject_ = getDefaultInstance().getEmailSubject();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件主题	
       * </pre>
       *
       * <code>optional string email_subject = 3;</code>
       * @param value The bytes for emailSubject to set.
       * @return This builder for chaining.
       */
      public Builder setEmailSubjectBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailSubject_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object emailContent_ = "";
      /**
       * <pre>
       * 邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       * @return Whether the emailContent field is set.
       */
      public boolean hasEmailContent() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       * @return The emailContent.
       */
      public java.lang.String getEmailContent() {
        java.lang.Object ref = emailContent_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailContent_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       * @return The bytes for emailContent.
       */
      public com.google.protobuf.ByteString
          getEmailContentBytes() {
        java.lang.Object ref = emailContent_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailContent_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       * @param value The emailContent to set.
       * @return This builder for chaining.
       */
      public Builder setEmailContent(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        emailContent_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailContent() {
        emailContent_ = getDefaultInstance().getEmailContent();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件正文	
       * </pre>
       *
       * <code>optional string email_content = 4;</code>
       * @param value The bytes for emailContent to set.
       * @return This builder for chaining.
       */
      public Builder setEmailContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailContent_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object emailAttachmentMd5_ = "";
      /**
       * <pre>
       * 关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       * @return Whether the emailAttachmentMd5 field is set.
       */
      public boolean hasEmailAttachmentMd5() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       * @return The emailAttachmentMd5.
       */
      public java.lang.String getEmailAttachmentMd5() {
        java.lang.Object ref = emailAttachmentMd5_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailAttachmentMd5_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       * @return The bytes for emailAttachmentMd5.
       */
      public com.google.protobuf.ByteString
          getEmailAttachmentMd5Bytes() {
        java.lang.Object ref = emailAttachmentMd5_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailAttachmentMd5_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       * @param value The emailAttachmentMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setEmailAttachmentMd5(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        emailAttachmentMd5_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailAttachmentMd5() {
        emailAttachmentMd5_ = getDefaultInstance().getEmailAttachmentMd5();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联附件	
       * </pre>
       *
       * <code>optional string email_attachment_md5 = 5;</code>
       * @param value The bytes for emailAttachmentMd5 to set.
       * @return This builder for chaining.
       */
      public Builder setEmailAttachmentMd5Bytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailAttachmentMd5_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private long emailAttachmentResult_ ;
      /**
       * <pre>
       * 关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       * @return Whether the emailAttachmentResult field is set.
       */
      @java.lang.Override
      public boolean hasEmailAttachmentResult() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       * @return The emailAttachmentResult.
       */
      @java.lang.Override
      public long getEmailAttachmentResult() {
        return emailAttachmentResult_;
      }
      /**
       * <pre>
       * 关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       * @param value The emailAttachmentResult to set.
       * @return This builder for chaining.
       */
      public Builder setEmailAttachmentResult(long value) {

        emailAttachmentResult_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 关联日志	
       * </pre>
       *
       * <code>optional uint64 email_attachment_result = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailAttachmentResult() {
        bitField0_ = (bitField0_ & ~0x00000020);
        emailAttachmentResult_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object emailIndustry_ = "";
      /**
       * <pre>
       * 所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       * @return Whether the emailIndustry field is set.
       */
      public boolean hasEmailIndustry() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       * @return The emailIndustry.
       */
      public java.lang.String getEmailIndustry() {
        java.lang.Object ref = emailIndustry_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailIndustry_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       * @return The bytes for emailIndustry.
       */
      public com.google.protobuf.ByteString
          getEmailIndustryBytes() {
        java.lang.Object ref = emailIndustry_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailIndustry_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       * @param value The emailIndustry to set.
       * @return This builder for chaining.
       */
      public Builder setEmailIndustry(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        emailIndustry_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailIndustry() {
        emailIndustry_ = getDefaultInstance().getEmailIndustry();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属行业	军事、外交、金融、通信、基建
       * </pre>
       *
       * <code>optional string email_industry = 7;</code>
       * @param value The bytes for emailIndustry to set.
       * @return This builder for chaining.
       */
      public Builder setEmailIndustryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailIndustry_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList emailIntents_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureEmailIntentsIsMutable() {
        if (!emailIntents_.isModifiable()) {
          emailIntents_ = new com.google.protobuf.LazyStringArrayList(emailIntents_);
        }
        bitField0_ |= 0x00000080;
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @return A list containing the emailIntents.
       */
      public com.google.protobuf.ProtocolStringList
          getEmailIntentsList() {
        emailIntents_.makeImmutable();
        return emailIntents_;
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @return The count of emailIntents.
       */
      public int getEmailIntentsCount() {
        return emailIntents_.size();
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @param index The index of the element to return.
       * @return The emailIntents at the given index.
       */
      public java.lang.String getEmailIntents(int index) {
        return emailIntents_.get(index);
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @param index The index of the value to return.
       * @return The bytes of the emailIntents at the given index.
       */
      public com.google.protobuf.ByteString
          getEmailIntentsBytes(int index) {
        return emailIntents_.getByteString(index);
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @param index The index to set the value at.
       * @param value The emailIntents to set.
       * @return This builder for chaining.
       */
      public Builder setEmailIntents(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureEmailIntentsIsMutable();
        emailIntents_.set(index, value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @param value The emailIntents to add.
       * @return This builder for chaining.
       */
      public Builder addEmailIntents(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureEmailIntentsIsMutable();
        emailIntents_.add(value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @param values The emailIntents to add.
       * @return This builder for chaining.
       */
      public Builder addAllEmailIntents(
          java.lang.Iterable<java.lang.String> values) {
        ensureEmailIntentsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, emailIntents_);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailIntents() {
        emailIntents_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000080);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件意图	打开附件、打开链接、扫二维码、密码解压、填写个人信息
       * </pre>
       *
       * <code>repeated string email_intents = 8;</code>
       * @param value The bytes of the emailIntents to add.
       * @return This builder for chaining.
       */
      public Builder addEmailIntentsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureEmailIntentsIsMutable();
        emailIntents_.add(value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringArrayList emailAnomalyTags_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
      private void ensureEmailAnomalyTagsIsMutable() {
        if (!emailAnomalyTags_.isModifiable()) {
          emailAnomalyTags_ = new com.google.protobuf.LazyStringArrayList(emailAnomalyTags_);
        }
        bitField0_ |= 0x00000100;
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @return A list containing the emailAnomalyTags.
       */
      public com.google.protobuf.ProtocolStringList
          getEmailAnomalyTagsList() {
        emailAnomalyTags_.makeImmutable();
        return emailAnomalyTags_;
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @return The count of emailAnomalyTags.
       */
      public int getEmailAnomalyTagsCount() {
        return emailAnomalyTags_.size();
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @param index The index of the element to return.
       * @return The emailAnomalyTags at the given index.
       */
      public java.lang.String getEmailAnomalyTags(int index) {
        return emailAnomalyTags_.get(index);
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @param index The index of the value to return.
       * @return The bytes of the emailAnomalyTags at the given index.
       */
      public com.google.protobuf.ByteString
          getEmailAnomalyTagsBytes(int index) {
        return emailAnomalyTags_.getByteString(index);
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @param index The index to set the value at.
       * @param value The emailAnomalyTags to set.
       * @return This builder for chaining.
       */
      public Builder setEmailAnomalyTags(
          int index, java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureEmailAnomalyTagsIsMutable();
        emailAnomalyTags_.set(index, value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @param value The emailAnomalyTags to add.
       * @return This builder for chaining.
       */
      public Builder addEmailAnomalyTags(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ensureEmailAnomalyTagsIsMutable();
        emailAnomalyTags_.add(value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @param values The emailAnomalyTags to add.
       * @return This builder for chaining.
       */
      public Builder addAllEmailAnomalyTags(
          java.lang.Iterable<java.lang.String> values) {
        ensureEmailAnomalyTagsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, emailAnomalyTags_);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailAnomalyTags() {
        emailAnomalyTags_ =
          com.google.protobuf.LazyStringArrayList.emptyList();
        bitField0_ = (bitField0_ & ~0x00000100);;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常标签	异地登录、异常时间登录、定期登录、内部服务异常、UI不匹配、歧义重复
       * </pre>
       *
       * <code>repeated string email_anomaly_tags = 9;</code>
       * @param value The bytes of the emailAnomalyTags to add.
       * @return This builder for chaining.
       */
      public Builder addEmailAnomalyTagsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureEmailAnomalyTagsIsMutable();
        emailAnomalyTags_.add(value);
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object emailAlertReason_ = "";
      /**
       * <pre>
       * 告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       * @return Whether the emailAlertReason field is set.
       */
      public boolean hasEmailAlertReason() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       * @return The emailAlertReason.
       */
      public java.lang.String getEmailAlertReason() {
        java.lang.Object ref = emailAlertReason_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            emailAlertReason_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       * @return The bytes for emailAlertReason.
       */
      public com.google.protobuf.ByteString
          getEmailAlertReasonBytes() {
        java.lang.Object ref = emailAlertReason_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          emailAlertReason_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       * @param value The emailAlertReason to set.
       * @return This builder for chaining.
       */
      public Builder setEmailAlertReason(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        emailAlertReason_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearEmailAlertReason() {
        emailAlertReason_ = getDefaultInstance().getEmailAlertReason();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警原因	
       * </pre>
       *
       * <code>optional string email_alert_reason = 10;</code>
       * @param value The bytes for emailAlertReason to set.
       * @return This builder for chaining.
       */
      public Builder setEmailAlertReasonBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        emailAlertReason_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:MAIL_ALERT_INFO)
    }

    // @@protoc_insertion_point(class_scope:MAIL_ALERT_INFO)
    private static final MailAlertInfo.MAIL_ALERT_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new MailAlertInfo.MAIL_ALERT_INFO();
    }

    public static MailAlertInfo.MAIL_ALERT_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MAIL_ALERT_INFO>
        PARSER = new com.google.protobuf.AbstractParser<MAIL_ALERT_INFO>() {
      @java.lang.Override
      public MAIL_ALERT_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MAIL_ALERT_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MAIL_ALERT_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public MailAlertInfo.MAIL_ALERT_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_MAIL_ALERT_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_MAIL_ALERT_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\025MAIL_ALERT_INFO.proto\"\223\002\n\017MAIL_ALERT_I" +
      "NFO\022\024\n\014email_sender\030\001 \001(\t\022\026\n\016email_recei" +
      "ver\030\002 \001(\t\022\025\n\remail_subject\030\003 \001(\t\022\025\n\remai" +
      "l_content\030\004 \001(\t\022\034\n\024email_attachment_md5\030" +
      "\005 \001(\t\022\037\n\027email_attachment_result\030\006 \001(\004\022\026" +
      "\n\016email_industry\030\007 \001(\t\022\025\n\remail_intents\030" +
      "\010 \003(\t\022\032\n\022email_anomaly_tags\030\t \003(\t\022\032\n\022ema" +
      "il_alert_reason\030\n \001(\tB\017B\rMailAlertInfo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_MAIL_ALERT_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_MAIL_ALERT_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_MAIL_ALERT_INFO_descriptor,
        new java.lang.String[] { "EmailSender", "EmailReceiver", "EmailSubject", "EmailContent", "EmailAttachmentMd5", "EmailAttachmentResult", "EmailIndustry", "EmailIntents", "EmailAnomalyTags", "EmailAlertReason", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
