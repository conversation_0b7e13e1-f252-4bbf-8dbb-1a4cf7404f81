package com.geeksec.entity.trans;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Data
public class SslTrans {
    private Integer conType;                  // optional uint32 conType = 1
    private Integer aleLev;                   // optional uint32 aleLev = 2
    private Integer aleDes;                   // optional uint32 aleDes = 3
    private Integer handShaType;              // optional uint32 handShaType = 4
    private Integer cliVer;                   // optional uint32 cliVer = 5
    private Long cliGmtUniTime;               // optional uint64 cliGMTUniTime = 6
    private String cliRand;                   // optional bytes cliRand = 7
    private String cliSesId;                  // optional bytes cliSesID = 8
    private String cliCipSui;                 // optional bytes cliCipSui = 9
    private String cliComMet;                 // optional bytes cliComMet = 10
    private Integer srvVer;                   // optional uint32 srvVer = 11
    private String srvName;                   // optional bytes srvName = 12
    private Integer srvNameAttr;              // optional uint32 srvNameAttr = 13
    private Long srvGmtUniTime;               // optional uint64 srvGMTUniTime = 14
    private String srvRand;                   // optional bytes srvRand = 15
    private String srvSesId;                  // optional bytes srvSesID = 16
    private String srvComprMet;               // optional bytes srvComprMet = 17
    private Integer srvCertLen;               // optional uint32 srvCertLen = 18
    private Integer certResType;              // optional uint32 certResType = 19
    private Integer cliCertLen;               // optional uint32 cliCertLen = 20
    private String rsaModOfSrvKeyExc;         // optional bytes RSAModOfSrvKeyExc = 21
    private Long rsaExpOfSrvKeyExc;           // optional uint64 RSAExpOfSrvKeyExc = 22
    private String dhModOfSrvKeyExc;          // optional bytes DHModOfSrvKeyExc = 23
    private String dhGenOfSrvKeyExc;          // optional bytes DHGenOfSrvKeyExc = 24
    private String srvDhPubKey;               // optional bytes srvDHPubKey = 25
    private String preMasKeyEncryByRsa;       // optional bytes preMasKeyEncryByRSA = 26
    private String cliDhPubKey;               // optional bytes cliDHPubKey = 27
    private Integer extTypeInSsl;             // optional uint32 extTypeInSSL = 28
    private Integer cliEllCurPoiFor;          // optional uint32 cliEllCurPoiFor = 29
    private Integer cliEllCur;                // optional uint32 cliEllCur = 30
    private Integer srvEllCurPoiFor;          // optional uint32 srvEllCurPoiFor = 31
    private Integer srvEllCur;                // optional uint32 srvEllCur = 32
    private String srvEllCurDhPubKey;         // optional bytes srvEllCurDHPubKey = 33
    private String cliEllCurDhPubKey;         // optional bytes cliEllCurDHPubKey = 34
    private Long srvGmtUniTime2;              // optional uint64 srvGMTUni_Time = 35 (renamed to avoid conflict)
    private Integer cliExtCnt;                // optional uint32 cliExtCnt = 36
    private Integer srvExtCnt;                // optional uint32 srvExtCnt = 37
    private Integer cliHandSkLen;             // optional uint32 cliHandSkLen = 38
    private Integer srvHandSkLen;             // optional uint32 srvHandSkLen = 39
    private String cliExt;                    // optional bytes cliExt = 40
    private String srvExt;                    // optional bytes srvExt = 41
    private Integer cliExtGrease;             // optional uint32 cliExtGrease = 42
    private String cliJa3;                    // optional bytes cliJA3 = 43
    private String srvJa3;                    // optional bytes srvJA3 = 44
    private String cliSessTicket;             // optional bytes cliSessTicket = 45
    private String srvSessTicket;             // optional bytes srvSessTicket = 46
    private Integer authTag;                  // optional uint32 AuthTag = 47
    private Integer cliCertCnt;               // optional uint32 cliCertCnt = 48
    private Integer srvCertCnt;               // optional uint32 srvCertCnt = 49
    private String ecGroupsCli;               // optional bytes ecGroupsCli = 50
    private String ecPoiForByServ;            // optional bytes ecPoiForByServ = 51
    private String etags;                     // optional bytes etags = 52
    private String ttags;                     // optional bytes ttags = 53
    private String cliSesIdLen;               // optional bytes cliSesIDLen = 54
    private String srvSesIdLen;               // optional bytes srvSesIDLen =55
    private String srvKeyExcLen;              // optional bytes srvKeyExcLen = 56
    private String ecdhCurType;               // optional bytes ECDHCurType = 57
    private String ecdhSig;                   // optional bytes ECDHSig = 58
    private String dhePLen;                   // optional bytes DHEPLen = 59
    private String dheGLen;                   // optional bytes DHEGLen = 60
    private String cliKeyExcLen;              // optional bytes cliKeyExcLen = 61
    private String encPubKey;                 // optional bytes encPubKey = 62
    private String encPubKeyLen;              // optional bytes encPubKeyLen = 63
    private String cliExtLen;                 // optional bytes cliExtLen = 64
    private String srvExtLen;                 // optional bytes srvExtLen = 65
    private String ecdhPubKeyLen;             // optional bytes ECDHPubKeyLen = 66
    private String namType;                   // optional bytes namType = 67
    private String namLen;                    // optional bytes namLen = 68
    private String ticDat;                    // optional bytes ticDat = 69
    private String srvCipSui;                 // optional bytes srvCipSui = 70
    private Integer cipSuiNum;                // optional uint32 cipSuiNum = 71
    private String ecdhSigHash;               // optional bytes ECDHSigHash = 72
    private String dheSigHash;                // optional bytes DHESigHash = 73
    private String rsaSigHash;                // optional bytes RSASigHash = 74
    private String greaseFlag;                // optional bytes greaseFlag = 75
    private String rsaModLen;                 // optional bytes RSAModLen = 76
    private String rsaExpLen;                 // optional bytes RSAExpLen = 77
    private String rsaSig;                    // optional bytes RSASig = 78
    private String dheSig;                    // optional bytes DHESig = 79
    private String dhePubKeyLen;              // optional bytes DHEPubKeyLen = 80
    private String dhePubKey;                 // optional bytes DHEPubKey = 81
    private String sigAlgType;                // optional bytes SigAlgType = 82
    private String sigAlg;                    // optional bytes sigAlg = 83
    private String sigHashAlg;                // optional bytes SigHashAlg = 84
    private String joy;                       // optional bytes JOY = 85
    private String joys;                      // optional bytes JOYS = 86
    private String starttls;                  // optional bytes STARTTLS = 87
    private String certNonFlag;               // optional bytes certNonFlag = 88
    private String joyFp;                     // optional bytes JoyFp = 89
    private String certIntactFlag;            // optional bytes certIntactFlag = 90
    private String certPath;                  // optional bytes certPath = 91
    private String sessSecFlag;               // optional bytes sessSecFlag = 92
    private String fullText;                  // optional bytes fullText = 93
    private String cliCertHashes;             // optional bytes cliCertHashes = 94
    private String srvCertHashes;             // optional bytes srvCertHashes = 95
    private Integer cliCertNum;               // optional uint32 cliCertNum = 96
    private Integer srvCertNum;               // optional uint32 srvCertNum = 97
    private Integer certExist;                // optional uint32 certExist = 98
    private String extendEcGroupsClient;      // optional bytes extendEcGroupsClient = 99
    private Integer leafCertDaysRemaining;    // optional uint32 leafCertDaysRemaining = 100

}
