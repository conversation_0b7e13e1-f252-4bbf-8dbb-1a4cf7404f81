package com.geeksec.transfer.function.process;

import com.geeksec.common.constant.DetectTypeConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;

import java.util.HashMap;
import java.util.Map;

import static com.geeksec.common.utils.UpdateCountUtils.updateCountKeys;
import static com.geeksec.common.utils.UpdateCountUtils.updateCountZet;

@Slf4j
public class AlertFilterCountProcess extends ProcessWindowFunction<Integer, Integer, Integer, TimeWindow> {

    public static final String FILTER_ALERT_COUNT_TODAY = "FilterAlertCount:Today";

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void process(Integer integer, ProcessWindowFunction<Integer, Integer, Integer, TimeWindow>.Context context, Iterable<Integer> elements, Collector<Integer> out) throws Exception {
        if (elements.iterator().hasNext()) {
            Integer count = elements.iterator().next();
            Map<Integer, Integer> countMap = new HashMap<>();
            countMap.put(DetectTypeConstant.FILTER_ALERT_INFO, count);
            // 更新最近一小时过滤统计数据
            boolean updatedCountZet = updateCountZet(countMap);
            if (updatedCountZet) {
                log.info("update filter zset success");
            } else {
                log.info("update filter zset failed");
            }
            // 更新今日过滤统计数据
            updateCountKeys(count, FILTER_ALERT_COUNT_TODAY);
        }
    }
}
