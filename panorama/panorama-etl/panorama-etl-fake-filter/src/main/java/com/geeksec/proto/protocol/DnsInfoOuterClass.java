package com.geeksec.proto.protocol;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: DnsInfo.proto
// Protobuf Java Version: 4.29.4

public final class DnsInfoOuterClass {
  private DnsInfoOuterClass() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      DnsInfoOuterClass.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DnsInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:DnsInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 addCnt = 1;</code>
     * @return Whether the addCnt field is set.
     */
    boolean hasAddCnt();
    /**
     * <code>optional uint32 addCnt = 1;</code>
     * @return The addCnt.
     */
    int getAddCnt();

    /**
     * <code>repeated uint32 aip = 2;</code>
     * @return A list containing the aip.
     */
    java.util.List<java.lang.Integer> getAipList();
    /**
     * <code>repeated uint32 aip = 2;</code>
     * @return The count of aip.
     */
    int getAipCount();
    /**
     * <code>repeated uint32 aip = 2;</code>
     * @param index The index of the element to return.
     * @return The aip at the given index.
     */
    int getAip(int index);

    /**
     * <code>repeated bytes aipAsn = 3;</code>
     * @return A list containing the aipAsn.
     */
    java.util.List<com.google.protobuf.ByteString> getAipAsnList();
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     * @return The count of aipAsn.
     */
    int getAipAsnCount();
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     * @param index The index of the element to return.
     * @return The aipAsn at the given index.
     */
    com.google.protobuf.ByteString getAipAsn(int index);

    /**
     * <code>optional uint32 aipCnt = 4;</code>
     * @return Whether the aipCnt field is set.
     */
    boolean hasAipCnt();
    /**
     * <code>optional uint32 aipCnt = 4;</code>
     * @return The aipCnt.
     */
    int getAipCnt();

    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     * @return A list containing the aIpv6.
     */
    java.util.List<com.google.protobuf.ByteString> getAIpv6List();
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     * @return The count of aIpv6.
     */
    int getAIpv6Count();
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     * @param index The index of the element to return.
     * @return The aIpv6 at the given index.
     */
    com.google.protobuf.ByteString getAIpv6(int index);

    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     * @return Whether the aIpv6Cnt field is set.
     */
    boolean hasAIpv6Cnt();
    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     * @return The aIpv6Cnt.
     */
    int getAIpv6Cnt();

    /**
     * <code>repeated bytes aipCountry = 7;</code>
     * @return A list containing the aipCountry.
     */
    java.util.List<com.google.protobuf.ByteString> getAipCountryList();
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     * @return The count of aipCountry.
     */
    int getAipCountryCount();
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     * @param index The index of the element to return.
     * @return The aipCountry at the given index.
     */
    com.google.protobuf.ByteString getAipCountry(int index);

    /**
     * <code>repeated bytes ansCname = 8;</code>
     * @return A list containing the ansCname.
     */
    java.util.List<com.google.protobuf.ByteString> getAnsCnameList();
    /**
     * <code>repeated bytes ansCname = 8;</code>
     * @return The count of ansCname.
     */
    int getAnsCnameCount();
    /**
     * <code>repeated bytes ansCname = 8;</code>
     * @param index The index of the element to return.
     * @return The ansCname at the given index.
     */
    com.google.protobuf.ByteString getAnsCname(int index);

    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     * @return Whether the ansCnameCnt field is set.
     */
    boolean hasAnsCnameCnt();
    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     * @return The ansCnameCnt.
     */
    int getAnsCnameCnt();

    /**
     * <code>optional uint32 ansCnt = 10;</code>
     * @return Whether the ansCnt field is set.
     */
    boolean hasAnsCnt();
    /**
     * <code>optional uint32 ansCnt = 10;</code>
     * @return The ansCnt.
     */
    int getAnsCnt();

    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     * @return A list containing the ansIPv6.
     */
    java.util.List<com.google.protobuf.ByteString> getAnsIPv6List();
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     * @return The count of ansIPv6.
     */
    int getAnsIPv6Count();
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     * @param index The index of the element to return.
     * @return The ansIPv6 at the given index.
     */
    com.google.protobuf.ByteString getAnsIPv6(int index);

    /**
     * <code>optional bytes ansQue = 12;</code>
     * @return Whether the ansQue field is set.
     */
    boolean hasAnsQue();
    /**
     * <code>optional bytes ansQue = 12;</code>
     * @return The ansQue.
     */
    com.google.protobuf.ByteString getAnsQue();

    /**
     * <code>repeated bytes ansTypes = 13;</code>
     * @return A list containing the ansTypes.
     */
    java.util.List<com.google.protobuf.ByteString> getAnsTypesList();
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     * @return The count of ansTypes.
     */
    int getAnsTypesCount();
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     * @param index The index of the element to return.
     * @return The ansTypes at the given index.
     */
    com.google.protobuf.ByteString getAnsTypes(int index);

    /**
     * <code>optional uint32 autCnt = 14;</code>
     * @return Whether the autCnt field is set.
     */
    boolean hasAutCnt();
    /**
     * <code>optional uint32 autCnt = 14;</code>
     * @return The autCnt.
     */
    int getAutCnt();

    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     * @return A list containing the mxIpAsn.
     */
    java.util.List<com.google.protobuf.ByteString> getMxIpAsnList();
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     * @return The count of mxIpAsn.
     */
    int getMxIpAsnCount();
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     * @param index The index of the element to return.
     * @return The mxIpAsn at the given index.
     */
    com.google.protobuf.ByteString getMxIpAsn(int index);

    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     * @return A list containing the mxIpCountry.
     */
    java.util.List<com.google.protobuf.ByteString> getMxIpCountryList();
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     * @return The count of mxIpCountry.
     */
    int getMxIpCountryCount();
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     * @param index The index of the element to return.
     * @return The mxIpCountry at the given index.
     */
    com.google.protobuf.ByteString getMxIpCountry(int index);

    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     * @return A list containing the mailSrvHost.
     */
    java.util.List<com.google.protobuf.ByteString> getMailSrvHostList();
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     * @return The count of mailSrvHost.
     */
    int getMailSrvHostCount();
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     * @param index The index of the element to return.
     * @return The mailSrvHost at the given index.
     */
    com.google.protobuf.ByteString getMailSrvHost(int index);

    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     * @return Whether the mailSrvHostcnt field is set.
     */
    boolean hasMailSrvHostcnt();
    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     * @return The mailSrvHostcnt.
     */
    int getMailSrvHostcnt();

    /**
     * <code>repeated uint32 mailSrvIp = 19;</code>
     * @return A list containing the mailSrvIp.
     */
    java.util.List<java.lang.Integer> getMailSrvIpList();
    /**
     * <code>repeated uint32 mailSrvIp = 19;</code>
     * @return The count of mailSrvIp.
     */
    int getMailSrvIpCount();
    /**
     * <code>repeated uint32 mailSrvIp = 19;</code>
     * @param index The index of the element to return.
     * @return The mailSrvIp at the given index.
     */
    int getMailSrvIp(int index);

    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     * @return Whether the mailSrvIPCnt field is set.
     */
    boolean hasMailSrvIPCnt();
    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     * @return The mailSrvIPCnt.
     */
    int getMailSrvIPCnt();

    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     * @return A list containing the nameSrvAsn.
     */
    java.util.List<com.google.protobuf.ByteString> getNameSrvAsnList();
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     * @return The count of nameSrvAsn.
     */
    int getNameSrvAsnCount();
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     * @param index The index of the element to return.
     * @return The nameSrvAsn at the given index.
     */
    com.google.protobuf.ByteString getNameSrvAsn(int index);

    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     * @return A list containing the nameSrvCountry.
     */
    java.util.List<com.google.protobuf.ByteString> getNameSrvCountryList();
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     * @return The count of nameSrvCountry.
     */
    int getNameSrvCountryCount();
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     * @param index The index of the element to return.
     * @return The nameSrvCountry at the given index.
     */
    com.google.protobuf.ByteString getNameSrvCountry(int index);

    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     * @return A list containing the nameSrvHost.
     */
    java.util.List<com.google.protobuf.ByteString> getNameSrvHostList();
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     * @return The count of nameSrvHost.
     */
    int getNameSrvHostCount();
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     * @param index The index of the element to return.
     * @return The nameSrvHost at the given index.
     */
    com.google.protobuf.ByteString getNameSrvHost(int index);

    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     * @return Whether the nameSrvHostCnt field is set.
     */
    boolean hasNameSrvHostCnt();
    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     * @return The nameSrvHostCnt.
     */
    int getNameSrvHostCnt();

    /**
     * <code>repeated uint32 nsIp = 25;</code>
     * @return A list containing the nsIp.
     */
    java.util.List<java.lang.Integer> getNsIpList();
    /**
     * <code>repeated uint32 nsIp = 25;</code>
     * @return The count of nsIp.
     */
    int getNsIpCount();
    /**
     * <code>repeated uint32 nsIp = 25;</code>
     * @param index The index of the element to return.
     * @return The nsIp at the given index.
     */
    int getNsIp(int index);

    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     * @return Whether the nsIpCnt field is set.
     */
    boolean hasNsIpCnt();
    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     * @return The nsIpCnt.
     */
    int getNsIpCnt();

    /**
     * <code>optional bytes ansName = 27;</code>
     * @return Whether the ansName field is set.
     */
    boolean hasAnsName();
    /**
     * <code>optional bytes ansName = 27;</code>
     * @return The ansName.
     */
    com.google.protobuf.ByteString getAnsName();

    /**
     * <code>optional uint32 addRrs = 28;</code>
     * @return Whether the addRrs field is set.
     */
    boolean hasAddRrs();
    /**
     * <code>optional uint32 addRrs = 28;</code>
     * @return The addRrs.
     */
    int getAddRrs();

    /**
     * <code>optional bytes dnsSpf = 29;</code>
     * @return Whether the dnsSpf field is set.
     */
    boolean hasDnsSpf();
    /**
     * <code>optional bytes dnsSpf = 29;</code>
     * @return The dnsSpf.
     */
    com.google.protobuf.ByteString getDnsSpf();

    /**
     * <code>optional bytes dnsTxt = 30;</code>
     * @return Whether the dnsTxt field is set.
     */
    boolean hasDnsTxt();
    /**
     * <code>optional bytes dnsTxt = 30;</code>
     * @return The dnsTxt.
     */
    com.google.protobuf.ByteString getDnsTxt();

    /**
     * <code>optional uint32 queType = 31;</code>
     * @return Whether the queType field is set.
     */
    boolean hasQueType();
    /**
     * <code>optional uint32 queType = 31;</code>
     * @return The queType.
     */
    int getQueType();

    /**
     * <code>optional bytes queName = 32;</code>
     * @return Whether the queName field is set.
     */
    boolean hasQueName();
    /**
     * <code>optional bytes queName = 32;</code>
     * @return The queName.
     */
    com.google.protobuf.ByteString getQueName();

    /**
     * <code>optional uint32 traID = 33;</code>
     * @return Whether the traID field is set.
     */
    boolean hasTraID();
    /**
     * <code>optional uint32 traID = 33;</code>
     * @return The traID.
     */
    int getTraID();

    /**
     * <code>optional uint32 srvFlag = 34;</code>
     * @return Whether the srvFlag field is set.
     */
    boolean hasSrvFlag();
    /**
     * <code>optional uint32 srvFlag = 34;</code>
     * @return The srvFlag.
     */
    int getSrvFlag();

    /**
     * <code>optional bytes ansRes = 35;</code>
     * @return Whether the ansRes field is set.
     */
    boolean hasAnsRes();
    /**
     * <code>optional bytes ansRes = 35;</code>
     * @return The ansRes.
     */
    com.google.protobuf.ByteString getAnsRes();

    /**
     * <code>optional uint32 authAnsType = 36;</code>
     * @return Whether the authAnsType field is set.
     */
    boolean hasAuthAnsType();
    /**
     * <code>optional uint32 authAnsType = 36;</code>
     * @return The authAnsType.
     */
    int getAuthAnsType();

    /**
     * <code>optional bytes authAnsRes = 37;</code>
     * @return Whether the authAnsRes field is set.
     */
    boolean hasAuthAnsRes();
    /**
     * <code>optional bytes authAnsRes = 37;</code>
     * @return The authAnsRes.
     */
    com.google.protobuf.ByteString getAuthAnsRes();

    /**
     * <code>optional uint32 addAnsType = 38;</code>
     * @return Whether the addAnsType field is set.
     */
    boolean hasAddAnsType();
    /**
     * <code>optional uint32 addAnsType = 38;</code>
     * @return The addAnsType.
     */
    int getAddAnsType();

    /**
     * <code>optional bytes addAnsRes = 39;</code>
     * @return Whether the addAnsRes field is set.
     */
    boolean hasAddAnsRes();
    /**
     * <code>optional bytes addAnsRes = 39;</code>
     * @return The addAnsRes.
     */
    com.google.protobuf.ByteString getAddAnsRes();

    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     * @return A list containing the mxIpv6.
     */
    java.util.List<com.google.protobuf.ByteString> getMxIpv6List();
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     * @return The count of mxIpv6.
     */
    int getMxIpv6Count();
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     * @param index The index of the element to return.
     * @return The mxIpv6 at the given index.
     */
    com.google.protobuf.ByteString getMxIpv6(int index);

    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     * @return Whether the mxIpv6Cnt field is set.
     */
    boolean hasMxIpv6Cnt();
    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     * @return The mxIpv6Cnt.
     */
    int getMxIpv6Cnt();

    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     * @return A list containing the nsIpv6.
     */
    java.util.List<com.google.protobuf.ByteString> getNsIpv6List();
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     * @return The count of nsIpv6.
     */
    int getNsIpv6Count();
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     * @param index The index of the element to return.
     * @return The nsIpv6 at the given index.
     */
    com.google.protobuf.ByteString getNsIpv6(int index);

    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     * @return Whether the nsIpv6Cnt field is set.
     */
    boolean hasNsIpv6Cnt();
    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     * @return The nsIpv6Cnt.
     */
    int getNsIpv6Cnt();
  }
  /**
   * Protobuf type {@code DnsInfo}
   */
  public static final class DnsInfo extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:DnsInfo)
      DnsInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        DnsInfo.class.getName());
    }
    // Use DnsInfo.newBuilder() to construct.
    private DnsInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private DnsInfo() {
      aip_ = emptyIntList();
      aipAsn_ = emptyList(com.google.protobuf.ByteString.class);
      aIpv6_ = emptyList(com.google.protobuf.ByteString.class);
      aipCountry_ = emptyList(com.google.protobuf.ByteString.class);
      ansCname_ = emptyList(com.google.protobuf.ByteString.class);
      ansIPv6_ = emptyList(com.google.protobuf.ByteString.class);
      ansQue_ = com.google.protobuf.ByteString.EMPTY;
      ansTypes_ = emptyList(com.google.protobuf.ByteString.class);
      mxIpAsn_ = emptyList(com.google.protobuf.ByteString.class);
      mxIpCountry_ = emptyList(com.google.protobuf.ByteString.class);
      mailSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
      mailSrvIp_ = emptyIntList();
      nameSrvAsn_ = emptyList(com.google.protobuf.ByteString.class);
      nameSrvCountry_ = emptyList(com.google.protobuf.ByteString.class);
      nameSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
      nsIp_ = emptyIntList();
      ansName_ = com.google.protobuf.ByteString.EMPTY;
      dnsSpf_ = com.google.protobuf.ByteString.EMPTY;
      dnsTxt_ = com.google.protobuf.ByteString.EMPTY;
      queName_ = com.google.protobuf.ByteString.EMPTY;
      ansRes_ = com.google.protobuf.ByteString.EMPTY;
      authAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      addAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      mxIpv6_ = emptyList(com.google.protobuf.ByteString.class);
      nsIpv6_ = emptyList(com.google.protobuf.ByteString.class);
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return DnsInfoOuterClass.internal_static_DnsInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return DnsInfoOuterClass.internal_static_DnsInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              DnsInfoOuterClass.DnsInfo.class, DnsInfoOuterClass.DnsInfo.Builder.class);
    }

    private int bitField0_;
    public static final int ADDCNT_FIELD_NUMBER = 1;
    private int addCnt_ = 0;
    /**
     * <code>optional uint32 addCnt = 1;</code>
     * @return Whether the addCnt field is set.
     */
    @java.lang.Override
    public boolean hasAddCnt() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 addCnt = 1;</code>
     * @return The addCnt.
     */
    @java.lang.Override
    public int getAddCnt() {
      return addCnt_;
    }

    public static final int AIP_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList aip_ =
        emptyIntList();
    /**
     * <code>repeated uint32 aip = 2;</code>
     * @return A list containing the aip.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getAipList() {
      return aip_;
    }
    /**
     * <code>repeated uint32 aip = 2;</code>
     * @return The count of aip.
     */
    public int getAipCount() {
      return aip_.size();
    }
    /**
     * <code>repeated uint32 aip = 2;</code>
     * @param index The index of the element to return.
     * @return The aip at the given index.
     */
    public int getAip(int index) {
      return aip_.getInt(index);
    }

    public static final int AIPASN_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> aipAsn_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     * @return A list containing the aipAsn.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAipAsnList() {
      return aipAsn_;
    }
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     * @return The count of aipAsn.
     */
    public int getAipAsnCount() {
      return aipAsn_.size();
    }
    /**
     * <code>repeated bytes aipAsn = 3;</code>
     * @param index The index of the element to return.
     * @return The aipAsn at the given index.
     */
    public com.google.protobuf.ByteString getAipAsn(int index) {
      return aipAsn_.get(index);
    }

    public static final int AIPCNT_FIELD_NUMBER = 4;
    private int aipCnt_ = 0;
    /**
     * <code>optional uint32 aipCnt = 4;</code>
     * @return Whether the aipCnt field is set.
     */
    @java.lang.Override
    public boolean hasAipCnt() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 aipCnt = 4;</code>
     * @return The aipCnt.
     */
    @java.lang.Override
    public int getAipCnt() {
      return aipCnt_;
    }

    public static final int AIPV6_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> aIpv6_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     * @return A list containing the aIpv6.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAIpv6List() {
      return aIpv6_;
    }
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     * @return The count of aIpv6.
     */
    public int getAIpv6Count() {
      return aIpv6_.size();
    }
    /**
     * <code>repeated bytes aIpv6 = 5;</code>
     * @param index The index of the element to return.
     * @return The aIpv6 at the given index.
     */
    public com.google.protobuf.ByteString getAIpv6(int index) {
      return aIpv6_.get(index);
    }

    public static final int AIPV6CNT_FIELD_NUMBER = 6;
    private int aIpv6Cnt_ = 0;
    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     * @return Whether the aIpv6Cnt field is set.
     */
    @java.lang.Override
    public boolean hasAIpv6Cnt() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 aIpv6Cnt = 6;</code>
     * @return The aIpv6Cnt.
     */
    @java.lang.Override
    public int getAIpv6Cnt() {
      return aIpv6Cnt_;
    }

    public static final int AIPCOUNTRY_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> aipCountry_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     * @return A list containing the aipCountry.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAipCountryList() {
      return aipCountry_;
    }
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     * @return The count of aipCountry.
     */
    public int getAipCountryCount() {
      return aipCountry_.size();
    }
    /**
     * <code>repeated bytes aipCountry = 7;</code>
     * @param index The index of the element to return.
     * @return The aipCountry at the given index.
     */
    public com.google.protobuf.ByteString getAipCountry(int index) {
      return aipCountry_.get(index);
    }

    public static final int ANSCNAME_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> ansCname_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes ansCname = 8;</code>
     * @return A list containing the ansCname.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAnsCnameList() {
      return ansCname_;
    }
    /**
     * <code>repeated bytes ansCname = 8;</code>
     * @return The count of ansCname.
     */
    public int getAnsCnameCount() {
      return ansCname_.size();
    }
    /**
     * <code>repeated bytes ansCname = 8;</code>
     * @param index The index of the element to return.
     * @return The ansCname at the given index.
     */
    public com.google.protobuf.ByteString getAnsCname(int index) {
      return ansCname_.get(index);
    }

    public static final int ANSCNAMECNT_FIELD_NUMBER = 9;
    private int ansCnameCnt_ = 0;
    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     * @return Whether the ansCnameCnt field is set.
     */
    @java.lang.Override
    public boolean hasAnsCnameCnt() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 ansCnameCnt = 9;</code>
     * @return The ansCnameCnt.
     */
    @java.lang.Override
    public int getAnsCnameCnt() {
      return ansCnameCnt_;
    }

    public static final int ANSCNT_FIELD_NUMBER = 10;
    private int ansCnt_ = 0;
    /**
     * <code>optional uint32 ansCnt = 10;</code>
     * @return Whether the ansCnt field is set.
     */
    @java.lang.Override
    public boolean hasAnsCnt() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 ansCnt = 10;</code>
     * @return The ansCnt.
     */
    @java.lang.Override
    public int getAnsCnt() {
      return ansCnt_;
    }

    public static final int ANSIPV6_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> ansIPv6_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     * @return A list containing the ansIPv6.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAnsIPv6List() {
      return ansIPv6_;
    }
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     * @return The count of ansIPv6.
     */
    public int getAnsIPv6Count() {
      return ansIPv6_.size();
    }
    /**
     * <code>repeated bytes ansIPv6 = 11;</code>
     * @param index The index of the element to return.
     * @return The ansIPv6 at the given index.
     */
    public com.google.protobuf.ByteString getAnsIPv6(int index) {
      return ansIPv6_.get(index);
    }

    public static final int ANSQUE_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString ansQue_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ansQue = 12;</code>
     * @return Whether the ansQue field is set.
     */
    @java.lang.Override
    public boolean hasAnsQue() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes ansQue = 12;</code>
     * @return The ansQue.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAnsQue() {
      return ansQue_;
    }

    public static final int ANSTYPES_FIELD_NUMBER = 13;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> ansTypes_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     * @return A list containing the ansTypes.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getAnsTypesList() {
      return ansTypes_;
    }
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     * @return The count of ansTypes.
     */
    public int getAnsTypesCount() {
      return ansTypes_.size();
    }
    /**
     * <code>repeated bytes ansTypes = 13;</code>
     * @param index The index of the element to return.
     * @return The ansTypes at the given index.
     */
    public com.google.protobuf.ByteString getAnsTypes(int index) {
      return ansTypes_.get(index);
    }

    public static final int AUTCNT_FIELD_NUMBER = 14;
    private int autCnt_ = 0;
    /**
     * <code>optional uint32 autCnt = 14;</code>
     * @return Whether the autCnt field is set.
     */
    @java.lang.Override
    public boolean hasAutCnt() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 autCnt = 14;</code>
     * @return The autCnt.
     */
    @java.lang.Override
    public int getAutCnt() {
      return autCnt_;
    }

    public static final int MXIPASN_FIELD_NUMBER = 15;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mxIpAsn_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     * @return A list containing the mxIpAsn.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getMxIpAsnList() {
      return mxIpAsn_;
    }
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     * @return The count of mxIpAsn.
     */
    public int getMxIpAsnCount() {
      return mxIpAsn_.size();
    }
    /**
     * <code>repeated bytes mxIpAsn = 15;</code>
     * @param index The index of the element to return.
     * @return The mxIpAsn at the given index.
     */
    public com.google.protobuf.ByteString getMxIpAsn(int index) {
      return mxIpAsn_.get(index);
    }

    public static final int MXIPCOUNTRY_FIELD_NUMBER = 16;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mxIpCountry_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     * @return A list containing the mxIpCountry.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getMxIpCountryList() {
      return mxIpCountry_;
    }
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     * @return The count of mxIpCountry.
     */
    public int getMxIpCountryCount() {
      return mxIpCountry_.size();
    }
    /**
     * <code>repeated bytes mxIpCountry = 16;</code>
     * @param index The index of the element to return.
     * @return The mxIpCountry at the given index.
     */
    public com.google.protobuf.ByteString getMxIpCountry(int index) {
      return mxIpCountry_.get(index);
    }

    public static final int MAILSRVHOST_FIELD_NUMBER = 17;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mailSrvHost_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     * @return A list containing the mailSrvHost.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getMailSrvHostList() {
      return mailSrvHost_;
    }
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     * @return The count of mailSrvHost.
     */
    public int getMailSrvHostCount() {
      return mailSrvHost_.size();
    }
    /**
     * <code>repeated bytes mailSrvHost = 17;</code>
     * @param index The index of the element to return.
     * @return The mailSrvHost at the given index.
     */
    public com.google.protobuf.ByteString getMailSrvHost(int index) {
      return mailSrvHost_.get(index);
    }

    public static final int MAILSRVHOSTCNT_FIELD_NUMBER = 18;
    private int mailSrvHostcnt_ = 0;
    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     * @return Whether the mailSrvHostcnt field is set.
     */
    @java.lang.Override
    public boolean hasMailSrvHostcnt() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional uint32 mailSrvHostcnt = 18;</code>
     * @return The mailSrvHostcnt.
     */
    @java.lang.Override
    public int getMailSrvHostcnt() {
      return mailSrvHostcnt_;
    }

    public static final int MAILSRVIP_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList mailSrvIp_ =
        emptyIntList();
    /**
     * <code>repeated uint32 mailSrvIp = 19;</code>
     * @return A list containing the mailSrvIp.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getMailSrvIpList() {
      return mailSrvIp_;
    }
    /**
     * <code>repeated uint32 mailSrvIp = 19;</code>
     * @return The count of mailSrvIp.
     */
    public int getMailSrvIpCount() {
      return mailSrvIp_.size();
    }
    /**
     * <code>repeated uint32 mailSrvIp = 19;</code>
     * @param index The index of the element to return.
     * @return The mailSrvIp at the given index.
     */
    public int getMailSrvIp(int index) {
      return mailSrvIp_.getInt(index);
    }

    public static final int MAILSRVIPCNT_FIELD_NUMBER = 20;
    private int mailSrvIPCnt_ = 0;
    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     * @return Whether the mailSrvIPCnt field is set.
     */
    @java.lang.Override
    public boolean hasMailSrvIPCnt() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional uint32 mailSrvIPCnt = 20;</code>
     * @return The mailSrvIPCnt.
     */
    @java.lang.Override
    public int getMailSrvIPCnt() {
      return mailSrvIPCnt_;
    }

    public static final int NAMESRVASN_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nameSrvAsn_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     * @return A list containing the nameSrvAsn.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getNameSrvAsnList() {
      return nameSrvAsn_;
    }
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     * @return The count of nameSrvAsn.
     */
    public int getNameSrvAsnCount() {
      return nameSrvAsn_.size();
    }
    /**
     * <code>repeated bytes nameSrvAsn = 21;</code>
     * @param index The index of the element to return.
     * @return The nameSrvAsn at the given index.
     */
    public com.google.protobuf.ByteString getNameSrvAsn(int index) {
      return nameSrvAsn_.get(index);
    }

    public static final int NAMESRVCOUNTRY_FIELD_NUMBER = 22;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nameSrvCountry_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     * @return A list containing the nameSrvCountry.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getNameSrvCountryList() {
      return nameSrvCountry_;
    }
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     * @return The count of nameSrvCountry.
     */
    public int getNameSrvCountryCount() {
      return nameSrvCountry_.size();
    }
    /**
     * <code>repeated bytes nameSrvCountry = 22;</code>
     * @param index The index of the element to return.
     * @return The nameSrvCountry at the given index.
     */
    public com.google.protobuf.ByteString getNameSrvCountry(int index) {
      return nameSrvCountry_.get(index);
    }

    public static final int NAMESRVHOST_FIELD_NUMBER = 23;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nameSrvHost_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     * @return A list containing the nameSrvHost.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getNameSrvHostList() {
      return nameSrvHost_;
    }
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     * @return The count of nameSrvHost.
     */
    public int getNameSrvHostCount() {
      return nameSrvHost_.size();
    }
    /**
     * <code>repeated bytes nameSrvHost = 23;</code>
     * @param index The index of the element to return.
     * @return The nameSrvHost at the given index.
     */
    public com.google.protobuf.ByteString getNameSrvHost(int index) {
      return nameSrvHost_.get(index);
    }

    public static final int NAMESRVHOSTCNT_FIELD_NUMBER = 24;
    private int nameSrvHostCnt_ = 0;
    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     * @return Whether the nameSrvHostCnt field is set.
     */
    @java.lang.Override
    public boolean hasNameSrvHostCnt() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint32 nameSrvHostCnt = 24;</code>
     * @return The nameSrvHostCnt.
     */
    @java.lang.Override
    public int getNameSrvHostCnt() {
      return nameSrvHostCnt_;
    }

    public static final int NSIP_FIELD_NUMBER = 25;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList nsIp_ =
        emptyIntList();
    /**
     * <code>repeated uint32 nsIp = 25;</code>
     * @return A list containing the nsIp.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getNsIpList() {
      return nsIp_;
    }
    /**
     * <code>repeated uint32 nsIp = 25;</code>
     * @return The count of nsIp.
     */
    public int getNsIpCount() {
      return nsIp_.size();
    }
    /**
     * <code>repeated uint32 nsIp = 25;</code>
     * @param index The index of the element to return.
     * @return The nsIp at the given index.
     */
    public int getNsIp(int index) {
      return nsIp_.getInt(index);
    }

    public static final int NSIPCNT_FIELD_NUMBER = 26;
    private int nsIpCnt_ = 0;
    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     * @return Whether the nsIpCnt field is set.
     */
    @java.lang.Override
    public boolean hasNsIpCnt() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint32 nsIpCnt = 26;</code>
     * @return The nsIpCnt.
     */
    @java.lang.Override
    public int getNsIpCnt() {
      return nsIpCnt_;
    }

    public static final int ANSNAME_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString ansName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ansName = 27;</code>
     * @return Whether the ansName field is set.
     */
    @java.lang.Override
    public boolean hasAnsName() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes ansName = 27;</code>
     * @return The ansName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAnsName() {
      return ansName_;
    }

    public static final int ADDRRS_FIELD_NUMBER = 28;
    private int addRrs_ = 0;
    /**
     * <code>optional uint32 addRrs = 28;</code>
     * @return Whether the addRrs field is set.
     */
    @java.lang.Override
    public boolean hasAddRrs() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 addRrs = 28;</code>
     * @return The addRrs.
     */
    @java.lang.Override
    public int getAddRrs() {
      return addRrs_;
    }

    public static final int DNSSPF_FIELD_NUMBER = 29;
    private com.google.protobuf.ByteString dnsSpf_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes dnsSpf = 29;</code>
     * @return Whether the dnsSpf field is set.
     */
    @java.lang.Override
    public boolean hasDnsSpf() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes dnsSpf = 29;</code>
     * @return The dnsSpf.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDnsSpf() {
      return dnsSpf_;
    }

    public static final int DNSTXT_FIELD_NUMBER = 30;
    private com.google.protobuf.ByteString dnsTxt_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes dnsTxt = 30;</code>
     * @return Whether the dnsTxt field is set.
     */
    @java.lang.Override
    public boolean hasDnsTxt() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes dnsTxt = 30;</code>
     * @return The dnsTxt.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDnsTxt() {
      return dnsTxt_;
    }

    public static final int QUETYPE_FIELD_NUMBER = 31;
    private int queType_ = 0;
    /**
     * <code>optional uint32 queType = 31;</code>
     * @return Whether the queType field is set.
     */
    @java.lang.Override
    public boolean hasQueType() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional uint32 queType = 31;</code>
     * @return The queType.
     */
    @java.lang.Override
    public int getQueType() {
      return queType_;
    }

    public static final int QUENAME_FIELD_NUMBER = 32;
    private com.google.protobuf.ByteString queName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes queName = 32;</code>
     * @return Whether the queName field is set.
     */
    @java.lang.Override
    public boolean hasQueName() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes queName = 32;</code>
     * @return The queName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getQueName() {
      return queName_;
    }

    public static final int TRAID_FIELD_NUMBER = 33;
    private int traID_ = 0;
    /**
     * <code>optional uint32 traID = 33;</code>
     * @return Whether the traID field is set.
     */
    @java.lang.Override
    public boolean hasTraID() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 traID = 33;</code>
     * @return The traID.
     */
    @java.lang.Override
    public int getTraID() {
      return traID_;
    }

    public static final int SRVFLAG_FIELD_NUMBER = 34;
    private int srvFlag_ = 0;
    /**
     * <code>optional uint32 srvFlag = 34;</code>
     * @return Whether the srvFlag field is set.
     */
    @java.lang.Override
    public boolean hasSrvFlag() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 srvFlag = 34;</code>
     * @return The srvFlag.
     */
    @java.lang.Override
    public int getSrvFlag() {
      return srvFlag_;
    }

    public static final int ANSRES_FIELD_NUMBER = 35;
    private com.google.protobuf.ByteString ansRes_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ansRes = 35;</code>
     * @return Whether the ansRes field is set.
     */
    @java.lang.Override
    public boolean hasAnsRes() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes ansRes = 35;</code>
     * @return The ansRes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAnsRes() {
      return ansRes_;
    }

    public static final int AUTHANSTYPE_FIELD_NUMBER = 36;
    private int authAnsType_ = 0;
    /**
     * <code>optional uint32 authAnsType = 36;</code>
     * @return Whether the authAnsType field is set.
     */
    @java.lang.Override
    public boolean hasAuthAnsType() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional uint32 authAnsType = 36;</code>
     * @return The authAnsType.
     */
    @java.lang.Override
    public int getAuthAnsType() {
      return authAnsType_;
    }

    public static final int AUTHANSRES_FIELD_NUMBER = 37;
    private com.google.protobuf.ByteString authAnsRes_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes authAnsRes = 37;</code>
     * @return Whether the authAnsRes field is set.
     */
    @java.lang.Override
    public boolean hasAuthAnsRes() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional bytes authAnsRes = 37;</code>
     * @return The authAnsRes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAuthAnsRes() {
      return authAnsRes_;
    }

    public static final int ADDANSTYPE_FIELD_NUMBER = 38;
    private int addAnsType_ = 0;
    /**
     * <code>optional uint32 addAnsType = 38;</code>
     * @return Whether the addAnsType field is set.
     */
    @java.lang.Override
    public boolean hasAddAnsType() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional uint32 addAnsType = 38;</code>
     * @return The addAnsType.
     */
    @java.lang.Override
    public int getAddAnsType() {
      return addAnsType_;
    }

    public static final int ADDANSRES_FIELD_NUMBER = 39;
    private com.google.protobuf.ByteString addAnsRes_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes addAnsRes = 39;</code>
     * @return Whether the addAnsRes field is set.
     */
    @java.lang.Override
    public boolean hasAddAnsRes() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes addAnsRes = 39;</code>
     * @return The addAnsRes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getAddAnsRes() {
      return addAnsRes_;
    }

    public static final int MXIPV6_FIELD_NUMBER = 40;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mxIpv6_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     * @return A list containing the mxIpv6.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getMxIpv6List() {
      return mxIpv6_;
    }
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     * @return The count of mxIpv6.
     */
    public int getMxIpv6Count() {
      return mxIpv6_.size();
    }
    /**
     * <code>repeated bytes mxIpv6 = 40;</code>
     * @param index The index of the element to return.
     * @return The mxIpv6 at the given index.
     */
    public com.google.protobuf.ByteString getMxIpv6(int index) {
      return mxIpv6_.get(index);
    }

    public static final int MXIPV6CNT_FIELD_NUMBER = 41;
    private int mxIpv6Cnt_ = 0;
    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     * @return Whether the mxIpv6Cnt field is set.
     */
    @java.lang.Override
    public boolean hasMxIpv6Cnt() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional uint32 mxIpv6Cnt = 41;</code>
     * @return The mxIpv6Cnt.
     */
    @java.lang.Override
    public int getMxIpv6Cnt() {
      return mxIpv6Cnt_;
    }

    public static final int NSIPV6_FIELD_NUMBER = 42;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nsIpv6_ =
        emptyList(com.google.protobuf.ByteString.class);
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     * @return A list containing the nsIpv6.
     */
    @java.lang.Override
    public java.util.List<com.google.protobuf.ByteString>
        getNsIpv6List() {
      return nsIpv6_;
    }
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     * @return The count of nsIpv6.
     */
    public int getNsIpv6Count() {
      return nsIpv6_.size();
    }
    /**
     * <code>repeated bytes nsIpv6 = 42;</code>
     * @param index The index of the element to return.
     * @return The nsIpv6 at the given index.
     */
    public com.google.protobuf.ByteString getNsIpv6(int index) {
      return nsIpv6_.get(index);
    }

    public static final int NSIPV6CNT_FIELD_NUMBER = 43;
    private int nsIpv6Cnt_ = 0;
    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     * @return Whether the nsIpv6Cnt field is set.
     */
    @java.lang.Override
    public boolean hasNsIpv6Cnt() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional uint32 nsIpv6Cnt = 43;</code>
     * @return The nsIpv6Cnt.
     */
    @java.lang.Override
    public int getNsIpv6Cnt() {
      return nsIpv6Cnt_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, addCnt_);
      }
      for (int i = 0; i < aip_.size(); i++) {
        output.writeUInt32(2, aip_.getInt(i));
      }
      for (int i = 0; i < aipAsn_.size(); i++) {
        output.writeBytes(3, aipAsn_.get(i));
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(4, aipCnt_);
      }
      for (int i = 0; i < aIpv6_.size(); i++) {
        output.writeBytes(5, aIpv6_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(6, aIpv6Cnt_);
      }
      for (int i = 0; i < aipCountry_.size(); i++) {
        output.writeBytes(7, aipCountry_.get(i));
      }
      for (int i = 0; i < ansCname_.size(); i++) {
        output.writeBytes(8, ansCname_.get(i));
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(9, ansCnameCnt_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeUInt32(10, ansCnt_);
      }
      for (int i = 0; i < ansIPv6_.size(); i++) {
        output.writeBytes(11, ansIPv6_.get(i));
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeBytes(12, ansQue_);
      }
      for (int i = 0; i < ansTypes_.size(); i++) {
        output.writeBytes(13, ansTypes_.get(i));
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeUInt32(14, autCnt_);
      }
      for (int i = 0; i < mxIpAsn_.size(); i++) {
        output.writeBytes(15, mxIpAsn_.get(i));
      }
      for (int i = 0; i < mxIpCountry_.size(); i++) {
        output.writeBytes(16, mxIpCountry_.get(i));
      }
      for (int i = 0; i < mailSrvHost_.size(); i++) {
        output.writeBytes(17, mailSrvHost_.get(i));
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeUInt32(18, mailSrvHostcnt_);
      }
      for (int i = 0; i < mailSrvIp_.size(); i++) {
        output.writeUInt32(19, mailSrvIp_.getInt(i));
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeUInt32(20, mailSrvIPCnt_);
      }
      for (int i = 0; i < nameSrvAsn_.size(); i++) {
        output.writeBytes(21, nameSrvAsn_.get(i));
      }
      for (int i = 0; i < nameSrvCountry_.size(); i++) {
        output.writeBytes(22, nameSrvCountry_.get(i));
      }
      for (int i = 0; i < nameSrvHost_.size(); i++) {
        output.writeBytes(23, nameSrvHost_.get(i));
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeUInt32(24, nameSrvHostCnt_);
      }
      for (int i = 0; i < nsIp_.size(); i++) {
        output.writeUInt32(25, nsIp_.getInt(i));
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeUInt32(26, nsIpCnt_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeBytes(27, ansName_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt32(28, addRrs_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeBytes(29, dnsSpf_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeBytes(30, dnsTxt_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeUInt32(31, queType_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(32, queName_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeUInt32(33, traID_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeUInt32(34, srvFlag_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeBytes(35, ansRes_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeUInt32(36, authAnsType_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeBytes(37, authAnsRes_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeUInt32(38, addAnsType_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeBytes(39, addAnsRes_);
      }
      for (int i = 0; i < mxIpv6_.size(); i++) {
        output.writeBytes(40, mxIpv6_.get(i));
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeUInt32(41, mxIpv6Cnt_);
      }
      for (int i = 0; i < nsIpv6_.size(); i++) {
        output.writeBytes(42, nsIpv6_.get(i));
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeUInt32(43, nsIpv6Cnt_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, addCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aip_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(aip_.getInt(i));
        }
        size += dataSize;
        size += 1 * getAipList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aipAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(aipAsn_.get(i));
        }
        size += dataSize;
        size += 1 * getAipAsnList().size();
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, aipCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aIpv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(aIpv6_.get(i));
        }
        size += dataSize;
        size += 1 * getAIpv6List().size();
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, aIpv6Cnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < aipCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(aipCountry_.get(i));
        }
        size += dataSize;
        size += 1 * getAipCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ansCname_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(ansCname_.get(i));
        }
        size += dataSize;
        size += 1 * getAnsCnameList().size();
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, ansCnameCnt_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(10, ansCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ansIPv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(ansIPv6_.get(i));
        }
        size += dataSize;
        size += 1 * getAnsIPv6List().size();
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, ansQue_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ansTypes_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(ansTypes_.get(i));
        }
        size += dataSize;
        size += 1 * getAnsTypesList().size();
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, autCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mxIpAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mxIpAsn_.get(i));
        }
        size += dataSize;
        size += 1 * getMxIpAsnList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mxIpCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mxIpCountry_.get(i));
        }
        size += dataSize;
        size += 2 * getMxIpCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mailSrvHost_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mailSrvHost_.get(i));
        }
        size += dataSize;
        size += 2 * getMailSrvHostList().size();
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, mailSrvHostcnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mailSrvIp_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(mailSrvIp_.getInt(i));
        }
        size += dataSize;
        size += 2 * getMailSrvIpList().size();
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, mailSrvIPCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nameSrvAsn_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nameSrvAsn_.get(i));
        }
        size += dataSize;
        size += 2 * getNameSrvAsnList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nameSrvCountry_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nameSrvCountry_.get(i));
        }
        size += dataSize;
        size += 2 * getNameSrvCountryList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nameSrvHost_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nameSrvHost_.get(i));
        }
        size += dataSize;
        size += 2 * getNameSrvHostList().size();
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(24, nameSrvHostCnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nsIp_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(nsIp_.getInt(i));
        }
        size += dataSize;
        size += 2 * getNsIpList().size();
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(26, nsIpCnt_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, ansName_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, addRrs_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(29, dnsSpf_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(30, dnsTxt_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(31, queType_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(32, queName_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(33, traID_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(34, srvFlag_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(35, ansRes_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(36, authAnsType_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(37, authAnsRes_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, addAnsType_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(39, addAnsRes_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < mxIpv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(mxIpv6_.get(i));
        }
        size += dataSize;
        size += 2 * getMxIpv6List().size();
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(41, mxIpv6Cnt_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < nsIpv6_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeBytesSizeNoTag(nsIpv6_.get(i));
        }
        size += dataSize;
        size += 2 * getNsIpv6List().size();
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(43, nsIpv6Cnt_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof DnsInfoOuterClass.DnsInfo)) {
        return super.equals(obj);
      }
      DnsInfoOuterClass.DnsInfo other = (DnsInfoOuterClass.DnsInfo) obj;

      if (hasAddCnt() != other.hasAddCnt()) return false;
      if (hasAddCnt()) {
        if (getAddCnt()
            != other.getAddCnt()) return false;
      }
      if (!getAipList()
          .equals(other.getAipList())) return false;
      if (!getAipAsnList()
          .equals(other.getAipAsnList())) return false;
      if (hasAipCnt() != other.hasAipCnt()) return false;
      if (hasAipCnt()) {
        if (getAipCnt()
            != other.getAipCnt()) return false;
      }
      if (!getAIpv6List()
          .equals(other.getAIpv6List())) return false;
      if (hasAIpv6Cnt() != other.hasAIpv6Cnt()) return false;
      if (hasAIpv6Cnt()) {
        if (getAIpv6Cnt()
            != other.getAIpv6Cnt()) return false;
      }
      if (!getAipCountryList()
          .equals(other.getAipCountryList())) return false;
      if (!getAnsCnameList()
          .equals(other.getAnsCnameList())) return false;
      if (hasAnsCnameCnt() != other.hasAnsCnameCnt()) return false;
      if (hasAnsCnameCnt()) {
        if (getAnsCnameCnt()
            != other.getAnsCnameCnt()) return false;
      }
      if (hasAnsCnt() != other.hasAnsCnt()) return false;
      if (hasAnsCnt()) {
        if (getAnsCnt()
            != other.getAnsCnt()) return false;
      }
      if (!getAnsIPv6List()
          .equals(other.getAnsIPv6List())) return false;
      if (hasAnsQue() != other.hasAnsQue()) return false;
      if (hasAnsQue()) {
        if (!getAnsQue()
            .equals(other.getAnsQue())) return false;
      }
      if (!getAnsTypesList()
          .equals(other.getAnsTypesList())) return false;
      if (hasAutCnt() != other.hasAutCnt()) return false;
      if (hasAutCnt()) {
        if (getAutCnt()
            != other.getAutCnt()) return false;
      }
      if (!getMxIpAsnList()
          .equals(other.getMxIpAsnList())) return false;
      if (!getMxIpCountryList()
          .equals(other.getMxIpCountryList())) return false;
      if (!getMailSrvHostList()
          .equals(other.getMailSrvHostList())) return false;
      if (hasMailSrvHostcnt() != other.hasMailSrvHostcnt()) return false;
      if (hasMailSrvHostcnt()) {
        if (getMailSrvHostcnt()
            != other.getMailSrvHostcnt()) return false;
      }
      if (!getMailSrvIpList()
          .equals(other.getMailSrvIpList())) return false;
      if (hasMailSrvIPCnt() != other.hasMailSrvIPCnt()) return false;
      if (hasMailSrvIPCnt()) {
        if (getMailSrvIPCnt()
            != other.getMailSrvIPCnt()) return false;
      }
      if (!getNameSrvAsnList()
          .equals(other.getNameSrvAsnList())) return false;
      if (!getNameSrvCountryList()
          .equals(other.getNameSrvCountryList())) return false;
      if (!getNameSrvHostList()
          .equals(other.getNameSrvHostList())) return false;
      if (hasNameSrvHostCnt() != other.hasNameSrvHostCnt()) return false;
      if (hasNameSrvHostCnt()) {
        if (getNameSrvHostCnt()
            != other.getNameSrvHostCnt()) return false;
      }
      if (!getNsIpList()
          .equals(other.getNsIpList())) return false;
      if (hasNsIpCnt() != other.hasNsIpCnt()) return false;
      if (hasNsIpCnt()) {
        if (getNsIpCnt()
            != other.getNsIpCnt()) return false;
      }
      if (hasAnsName() != other.hasAnsName()) return false;
      if (hasAnsName()) {
        if (!getAnsName()
            .equals(other.getAnsName())) return false;
      }
      if (hasAddRrs() != other.hasAddRrs()) return false;
      if (hasAddRrs()) {
        if (getAddRrs()
            != other.getAddRrs()) return false;
      }
      if (hasDnsSpf() != other.hasDnsSpf()) return false;
      if (hasDnsSpf()) {
        if (!getDnsSpf()
            .equals(other.getDnsSpf())) return false;
      }
      if (hasDnsTxt() != other.hasDnsTxt()) return false;
      if (hasDnsTxt()) {
        if (!getDnsTxt()
            .equals(other.getDnsTxt())) return false;
      }
      if (hasQueType() != other.hasQueType()) return false;
      if (hasQueType()) {
        if (getQueType()
            != other.getQueType()) return false;
      }
      if (hasQueName() != other.hasQueName()) return false;
      if (hasQueName()) {
        if (!getQueName()
            .equals(other.getQueName())) return false;
      }
      if (hasTraID() != other.hasTraID()) return false;
      if (hasTraID()) {
        if (getTraID()
            != other.getTraID()) return false;
      }
      if (hasSrvFlag() != other.hasSrvFlag()) return false;
      if (hasSrvFlag()) {
        if (getSrvFlag()
            != other.getSrvFlag()) return false;
      }
      if (hasAnsRes() != other.hasAnsRes()) return false;
      if (hasAnsRes()) {
        if (!getAnsRes()
            .equals(other.getAnsRes())) return false;
      }
      if (hasAuthAnsType() != other.hasAuthAnsType()) return false;
      if (hasAuthAnsType()) {
        if (getAuthAnsType()
            != other.getAuthAnsType()) return false;
      }
      if (hasAuthAnsRes() != other.hasAuthAnsRes()) return false;
      if (hasAuthAnsRes()) {
        if (!getAuthAnsRes()
            .equals(other.getAuthAnsRes())) return false;
      }
      if (hasAddAnsType() != other.hasAddAnsType()) return false;
      if (hasAddAnsType()) {
        if (getAddAnsType()
            != other.getAddAnsType()) return false;
      }
      if (hasAddAnsRes() != other.hasAddAnsRes()) return false;
      if (hasAddAnsRes()) {
        if (!getAddAnsRes()
            .equals(other.getAddAnsRes())) return false;
      }
      if (!getMxIpv6List()
          .equals(other.getMxIpv6List())) return false;
      if (hasMxIpv6Cnt() != other.hasMxIpv6Cnt()) return false;
      if (hasMxIpv6Cnt()) {
        if (getMxIpv6Cnt()
            != other.getMxIpv6Cnt()) return false;
      }
      if (!getNsIpv6List()
          .equals(other.getNsIpv6List())) return false;
      if (hasNsIpv6Cnt() != other.hasNsIpv6Cnt()) return false;
      if (hasNsIpv6Cnt()) {
        if (getNsIpv6Cnt()
            != other.getNsIpv6Cnt()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAddCnt()) {
        hash = (37 * hash) + ADDCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAddCnt();
      }
      if (getAipCount() > 0) {
        hash = (37 * hash) + AIP_FIELD_NUMBER;
        hash = (53 * hash) + getAipList().hashCode();
      }
      if (getAipAsnCount() > 0) {
        hash = (37 * hash) + AIPASN_FIELD_NUMBER;
        hash = (53 * hash) + getAipAsnList().hashCode();
      }
      if (hasAipCnt()) {
        hash = (37 * hash) + AIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAipCnt();
      }
      if (getAIpv6Count() > 0) {
        hash = (37 * hash) + AIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getAIpv6List().hashCode();
      }
      if (hasAIpv6Cnt()) {
        hash = (37 * hash) + AIPV6CNT_FIELD_NUMBER;
        hash = (53 * hash) + getAIpv6Cnt();
      }
      if (getAipCountryCount() > 0) {
        hash = (37 * hash) + AIPCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getAipCountryList().hashCode();
      }
      if (getAnsCnameCount() > 0) {
        hash = (37 * hash) + ANSCNAME_FIELD_NUMBER;
        hash = (53 * hash) + getAnsCnameList().hashCode();
      }
      if (hasAnsCnameCnt()) {
        hash = (37 * hash) + ANSCNAMECNT_FIELD_NUMBER;
        hash = (53 * hash) + getAnsCnameCnt();
      }
      if (hasAnsCnt()) {
        hash = (37 * hash) + ANSCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAnsCnt();
      }
      if (getAnsIPv6Count() > 0) {
        hash = (37 * hash) + ANSIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getAnsIPv6List().hashCode();
      }
      if (hasAnsQue()) {
        hash = (37 * hash) + ANSQUE_FIELD_NUMBER;
        hash = (53 * hash) + getAnsQue().hashCode();
      }
      if (getAnsTypesCount() > 0) {
        hash = (37 * hash) + ANSTYPES_FIELD_NUMBER;
        hash = (53 * hash) + getAnsTypesList().hashCode();
      }
      if (hasAutCnt()) {
        hash = (37 * hash) + AUTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getAutCnt();
      }
      if (getMxIpAsnCount() > 0) {
        hash = (37 * hash) + MXIPASN_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpAsnList().hashCode();
      }
      if (getMxIpCountryCount() > 0) {
        hash = (37 * hash) + MXIPCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpCountryList().hashCode();
      }
      if (getMailSrvHostCount() > 0) {
        hash = (37 * hash) + MAILSRVHOST_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvHostList().hashCode();
      }
      if (hasMailSrvHostcnt()) {
        hash = (37 * hash) + MAILSRVHOSTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvHostcnt();
      }
      if (getMailSrvIpCount() > 0) {
        hash = (37 * hash) + MAILSRVIP_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvIpList().hashCode();
      }
      if (hasMailSrvIPCnt()) {
        hash = (37 * hash) + MAILSRVIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getMailSrvIPCnt();
      }
      if (getNameSrvAsnCount() > 0) {
        hash = (37 * hash) + NAMESRVASN_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvAsnList().hashCode();
      }
      if (getNameSrvCountryCount() > 0) {
        hash = (37 * hash) + NAMESRVCOUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvCountryList().hashCode();
      }
      if (getNameSrvHostCount() > 0) {
        hash = (37 * hash) + NAMESRVHOST_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvHostList().hashCode();
      }
      if (hasNameSrvHostCnt()) {
        hash = (37 * hash) + NAMESRVHOSTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getNameSrvHostCnt();
      }
      if (getNsIpCount() > 0) {
        hash = (37 * hash) + NSIP_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpList().hashCode();
      }
      if (hasNsIpCnt()) {
        hash = (37 * hash) + NSIPCNT_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpCnt();
      }
      if (hasAnsName()) {
        hash = (37 * hash) + ANSNAME_FIELD_NUMBER;
        hash = (53 * hash) + getAnsName().hashCode();
      }
      if (hasAddRrs()) {
        hash = (37 * hash) + ADDRRS_FIELD_NUMBER;
        hash = (53 * hash) + getAddRrs();
      }
      if (hasDnsSpf()) {
        hash = (37 * hash) + DNSSPF_FIELD_NUMBER;
        hash = (53 * hash) + getDnsSpf().hashCode();
      }
      if (hasDnsTxt()) {
        hash = (37 * hash) + DNSTXT_FIELD_NUMBER;
        hash = (53 * hash) + getDnsTxt().hashCode();
      }
      if (hasQueType()) {
        hash = (37 * hash) + QUETYPE_FIELD_NUMBER;
        hash = (53 * hash) + getQueType();
      }
      if (hasQueName()) {
        hash = (37 * hash) + QUENAME_FIELD_NUMBER;
        hash = (53 * hash) + getQueName().hashCode();
      }
      if (hasTraID()) {
        hash = (37 * hash) + TRAID_FIELD_NUMBER;
        hash = (53 * hash) + getTraID();
      }
      if (hasSrvFlag()) {
        hash = (37 * hash) + SRVFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getSrvFlag();
      }
      if (hasAnsRes()) {
        hash = (37 * hash) + ANSRES_FIELD_NUMBER;
        hash = (53 * hash) + getAnsRes().hashCode();
      }
      if (hasAuthAnsType()) {
        hash = (37 * hash) + AUTHANSTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getAuthAnsType();
      }
      if (hasAuthAnsRes()) {
        hash = (37 * hash) + AUTHANSRES_FIELD_NUMBER;
        hash = (53 * hash) + getAuthAnsRes().hashCode();
      }
      if (hasAddAnsType()) {
        hash = (37 * hash) + ADDANSTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getAddAnsType();
      }
      if (hasAddAnsRes()) {
        hash = (37 * hash) + ADDANSRES_FIELD_NUMBER;
        hash = (53 * hash) + getAddAnsRes().hashCode();
      }
      if (getMxIpv6Count() > 0) {
        hash = (37 * hash) + MXIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpv6List().hashCode();
      }
      if (hasMxIpv6Cnt()) {
        hash = (37 * hash) + MXIPV6CNT_FIELD_NUMBER;
        hash = (53 * hash) + getMxIpv6Cnt();
      }
      if (getNsIpv6Count() > 0) {
        hash = (37 * hash) + NSIPV6_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpv6List().hashCode();
      }
      if (hasNsIpv6Cnt()) {
        hash = (37 * hash) + NSIPV6CNT_FIELD_NUMBER;
        hash = (53 * hash) + getNsIpv6Cnt();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static DnsInfoOuterClass.DnsInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static DnsInfoOuterClass.DnsInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static DnsInfoOuterClass.DnsInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static DnsInfoOuterClass.DnsInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(DnsInfoOuterClass.DnsInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code DnsInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:DnsInfo)
        DnsInfoOuterClass.DnsInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return DnsInfoOuterClass.internal_static_DnsInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return DnsInfoOuterClass.internal_static_DnsInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                DnsInfoOuterClass.DnsInfo.class, DnsInfoOuterClass.DnsInfo.Builder.class);
      }

      // Construct using DnsInfoOuterClass.DnsInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        addCnt_ = 0;
        aip_ = emptyIntList();
        aipAsn_ = emptyList(com.google.protobuf.ByteString.class);
        aipCnt_ = 0;
        aIpv6_ = emptyList(com.google.protobuf.ByteString.class);
        aIpv6Cnt_ = 0;
        aipCountry_ = emptyList(com.google.protobuf.ByteString.class);
        ansCname_ = emptyList(com.google.protobuf.ByteString.class);
        ansCnameCnt_ = 0;
        ansCnt_ = 0;
        ansIPv6_ = emptyList(com.google.protobuf.ByteString.class);
        ansQue_ = com.google.protobuf.ByteString.EMPTY;
        ansTypes_ = emptyList(com.google.protobuf.ByteString.class);
        autCnt_ = 0;
        mxIpAsn_ = emptyList(com.google.protobuf.ByteString.class);
        mxIpCountry_ = emptyList(com.google.protobuf.ByteString.class);
        mailSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
        mailSrvHostcnt_ = 0;
        mailSrvIp_ = emptyIntList();
        mailSrvIPCnt_ = 0;
        nameSrvAsn_ = emptyList(com.google.protobuf.ByteString.class);
        nameSrvCountry_ = emptyList(com.google.protobuf.ByteString.class);
        nameSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
        nameSrvHostCnt_ = 0;
        nsIp_ = emptyIntList();
        nsIpCnt_ = 0;
        ansName_ = com.google.protobuf.ByteString.EMPTY;
        addRrs_ = 0;
        dnsSpf_ = com.google.protobuf.ByteString.EMPTY;
        dnsTxt_ = com.google.protobuf.ByteString.EMPTY;
        queType_ = 0;
        queName_ = com.google.protobuf.ByteString.EMPTY;
        traID_ = 0;
        srvFlag_ = 0;
        ansRes_ = com.google.protobuf.ByteString.EMPTY;
        authAnsType_ = 0;
        authAnsRes_ = com.google.protobuf.ByteString.EMPTY;
        addAnsType_ = 0;
        addAnsRes_ = com.google.protobuf.ByteString.EMPTY;
        mxIpv6_ = emptyList(com.google.protobuf.ByteString.class);
        mxIpv6Cnt_ = 0;
        nsIpv6_ = emptyList(com.google.protobuf.ByteString.class);
        nsIpv6Cnt_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return DnsInfoOuterClass.internal_static_DnsInfo_descriptor;
      }

      @java.lang.Override
      public DnsInfoOuterClass.DnsInfo getDefaultInstanceForType() {
        return DnsInfoOuterClass.DnsInfo.getDefaultInstance();
      }

      @java.lang.Override
      public DnsInfoOuterClass.DnsInfo build() {
        DnsInfoOuterClass.DnsInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public DnsInfoOuterClass.DnsInfo buildPartial() {
        DnsInfoOuterClass.DnsInfo result = new DnsInfoOuterClass.DnsInfo(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(DnsInfoOuterClass.DnsInfo result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.addCnt_ = addCnt_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          aip_.makeImmutable();
          result.aip_ = aip_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          aipAsn_.makeImmutable();
          result.aipAsn_ = aipAsn_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.aipCnt_ = aipCnt_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          aIpv6_.makeImmutable();
          result.aIpv6_ = aIpv6_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.aIpv6Cnt_ = aIpv6Cnt_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          aipCountry_.makeImmutable();
          result.aipCountry_ = aipCountry_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          ansCname_.makeImmutable();
          result.ansCname_ = ansCname_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.ansCnameCnt_ = ansCnameCnt_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.ansCnt_ = ansCnt_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          ansIPv6_.makeImmutable();
          result.ansIPv6_ = ansIPv6_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.ansQue_ = ansQue_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          ansTypes_.makeImmutable();
          result.ansTypes_ = ansTypes_;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.autCnt_ = autCnt_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          mxIpAsn_.makeImmutable();
          result.mxIpAsn_ = mxIpAsn_;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          mxIpCountry_.makeImmutable();
          result.mxIpCountry_ = mxIpCountry_;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          mailSrvHost_.makeImmutable();
          result.mailSrvHost_ = mailSrvHost_;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.mailSrvHostcnt_ = mailSrvHostcnt_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          mailSrvIp_.makeImmutable();
          result.mailSrvIp_ = mailSrvIp_;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.mailSrvIPCnt_ = mailSrvIPCnt_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          nameSrvAsn_.makeImmutable();
          result.nameSrvAsn_ = nameSrvAsn_;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          nameSrvCountry_.makeImmutable();
          result.nameSrvCountry_ = nameSrvCountry_;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          nameSrvHost_.makeImmutable();
          result.nameSrvHost_ = nameSrvHost_;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.nameSrvHostCnt_ = nameSrvHostCnt_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          nsIp_.makeImmutable();
          result.nsIp_ = nsIp_;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.nsIpCnt_ = nsIpCnt_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.ansName_ = ansName_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.addRrs_ = addRrs_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.dnsSpf_ = dnsSpf_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.dnsTxt_ = dnsTxt_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.queType_ = queType_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.queName_ = queName_;
          to_bitField0_ |= 0x00010000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(DnsInfoOuterClass.DnsInfo result) {
        int from_bitField1_ = bitField1_;
        int to_bitField0_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.traID_ = traID_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.srvFlag_ = srvFlag_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.ansRes_ = ansRes_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.authAnsType_ = authAnsType_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.authAnsRes_ = authAnsRes_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.addAnsType_ = addAnsType_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.addAnsRes_ = addAnsRes_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          mxIpv6_.makeImmutable();
          result.mxIpv6_ = mxIpv6_;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.mxIpv6Cnt_ = mxIpv6Cnt_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          nsIpv6_.makeImmutable();
          result.nsIpv6_ = nsIpv6_;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          result.nsIpv6Cnt_ = nsIpv6Cnt_;
          to_bitField0_ |= 0x02000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof DnsInfoOuterClass.DnsInfo) {
          return mergeFrom((DnsInfoOuterClass.DnsInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(DnsInfoOuterClass.DnsInfo other) {
        if (other == DnsInfoOuterClass.DnsInfo.getDefaultInstance()) return this;
        if (other.hasAddCnt()) {
          setAddCnt(other.getAddCnt());
        }
        if (!other.aip_.isEmpty()) {
          if (aip_.isEmpty()) {
            aip_ = other.aip_;
            aip_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureAipIsMutable();
            aip_.addAll(other.aip_);
          }
          onChanged();
        }
        if (!other.aipAsn_.isEmpty()) {
          if (aipAsn_.isEmpty()) {
            aipAsn_ = other.aipAsn_;
            aipAsn_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureAipAsnIsMutable();
            aipAsn_.addAll(other.aipAsn_);
          }
          onChanged();
        }
        if (other.hasAipCnt()) {
          setAipCnt(other.getAipCnt());
        }
        if (!other.aIpv6_.isEmpty()) {
          if (aIpv6_.isEmpty()) {
            aIpv6_ = other.aIpv6_;
            aIpv6_.makeImmutable();
            bitField0_ |= 0x00000010;
          } else {
            ensureAIpv6IsMutable();
            aIpv6_.addAll(other.aIpv6_);
          }
          onChanged();
        }
        if (other.hasAIpv6Cnt()) {
          setAIpv6Cnt(other.getAIpv6Cnt());
        }
        if (!other.aipCountry_.isEmpty()) {
          if (aipCountry_.isEmpty()) {
            aipCountry_ = other.aipCountry_;
            aipCountry_.makeImmutable();
            bitField0_ |= 0x00000040;
          } else {
            ensureAipCountryIsMutable();
            aipCountry_.addAll(other.aipCountry_);
          }
          onChanged();
        }
        if (!other.ansCname_.isEmpty()) {
          if (ansCname_.isEmpty()) {
            ansCname_ = other.ansCname_;
            ansCname_.makeImmutable();
            bitField0_ |= 0x00000080;
          } else {
            ensureAnsCnameIsMutable();
            ansCname_.addAll(other.ansCname_);
          }
          onChanged();
        }
        if (other.hasAnsCnameCnt()) {
          setAnsCnameCnt(other.getAnsCnameCnt());
        }
        if (other.hasAnsCnt()) {
          setAnsCnt(other.getAnsCnt());
        }
        if (!other.ansIPv6_.isEmpty()) {
          if (ansIPv6_.isEmpty()) {
            ansIPv6_ = other.ansIPv6_;
            ansIPv6_.makeImmutable();
            bitField0_ |= 0x00000400;
          } else {
            ensureAnsIPv6IsMutable();
            ansIPv6_.addAll(other.ansIPv6_);
          }
          onChanged();
        }
        if (other.hasAnsQue()) {
          setAnsQue(other.getAnsQue());
        }
        if (!other.ansTypes_.isEmpty()) {
          if (ansTypes_.isEmpty()) {
            ansTypes_ = other.ansTypes_;
            ansTypes_.makeImmutable();
            bitField0_ |= 0x00001000;
          } else {
            ensureAnsTypesIsMutable();
            ansTypes_.addAll(other.ansTypes_);
          }
          onChanged();
        }
        if (other.hasAutCnt()) {
          setAutCnt(other.getAutCnt());
        }
        if (!other.mxIpAsn_.isEmpty()) {
          if (mxIpAsn_.isEmpty()) {
            mxIpAsn_ = other.mxIpAsn_;
            mxIpAsn_.makeImmutable();
            bitField0_ |= 0x00004000;
          } else {
            ensureMxIpAsnIsMutable();
            mxIpAsn_.addAll(other.mxIpAsn_);
          }
          onChanged();
        }
        if (!other.mxIpCountry_.isEmpty()) {
          if (mxIpCountry_.isEmpty()) {
            mxIpCountry_ = other.mxIpCountry_;
            mxIpCountry_.makeImmutable();
            bitField0_ |= 0x00008000;
          } else {
            ensureMxIpCountryIsMutable();
            mxIpCountry_.addAll(other.mxIpCountry_);
          }
          onChanged();
        }
        if (!other.mailSrvHost_.isEmpty()) {
          if (mailSrvHost_.isEmpty()) {
            mailSrvHost_ = other.mailSrvHost_;
            mailSrvHost_.makeImmutable();
            bitField0_ |= 0x00010000;
          } else {
            ensureMailSrvHostIsMutable();
            mailSrvHost_.addAll(other.mailSrvHost_);
          }
          onChanged();
        }
        if (other.hasMailSrvHostcnt()) {
          setMailSrvHostcnt(other.getMailSrvHostcnt());
        }
        if (!other.mailSrvIp_.isEmpty()) {
          if (mailSrvIp_.isEmpty()) {
            mailSrvIp_ = other.mailSrvIp_;
            mailSrvIp_.makeImmutable();
            bitField0_ |= 0x00040000;
          } else {
            ensureMailSrvIpIsMutable();
            mailSrvIp_.addAll(other.mailSrvIp_);
          }
          onChanged();
        }
        if (other.hasMailSrvIPCnt()) {
          setMailSrvIPCnt(other.getMailSrvIPCnt());
        }
        if (!other.nameSrvAsn_.isEmpty()) {
          if (nameSrvAsn_.isEmpty()) {
            nameSrvAsn_ = other.nameSrvAsn_;
            nameSrvAsn_.makeImmutable();
            bitField0_ |= 0x00100000;
          } else {
            ensureNameSrvAsnIsMutable();
            nameSrvAsn_.addAll(other.nameSrvAsn_);
          }
          onChanged();
        }
        if (!other.nameSrvCountry_.isEmpty()) {
          if (nameSrvCountry_.isEmpty()) {
            nameSrvCountry_ = other.nameSrvCountry_;
            nameSrvCountry_.makeImmutable();
            bitField0_ |= 0x00200000;
          } else {
            ensureNameSrvCountryIsMutable();
            nameSrvCountry_.addAll(other.nameSrvCountry_);
          }
          onChanged();
        }
        if (!other.nameSrvHost_.isEmpty()) {
          if (nameSrvHost_.isEmpty()) {
            nameSrvHost_ = other.nameSrvHost_;
            nameSrvHost_.makeImmutable();
            bitField0_ |= 0x00400000;
          } else {
            ensureNameSrvHostIsMutable();
            nameSrvHost_.addAll(other.nameSrvHost_);
          }
          onChanged();
        }
        if (other.hasNameSrvHostCnt()) {
          setNameSrvHostCnt(other.getNameSrvHostCnt());
        }
        if (!other.nsIp_.isEmpty()) {
          if (nsIp_.isEmpty()) {
            nsIp_ = other.nsIp_;
            nsIp_.makeImmutable();
            bitField0_ |= 0x01000000;
          } else {
            ensureNsIpIsMutable();
            nsIp_.addAll(other.nsIp_);
          }
          onChanged();
        }
        if (other.hasNsIpCnt()) {
          setNsIpCnt(other.getNsIpCnt());
        }
        if (other.hasAnsName()) {
          setAnsName(other.getAnsName());
        }
        if (other.hasAddRrs()) {
          setAddRrs(other.getAddRrs());
        }
        if (other.hasDnsSpf()) {
          setDnsSpf(other.getDnsSpf());
        }
        if (other.hasDnsTxt()) {
          setDnsTxt(other.getDnsTxt());
        }
        if (other.hasQueType()) {
          setQueType(other.getQueType());
        }
        if (other.hasQueName()) {
          setQueName(other.getQueName());
        }
        if (other.hasTraID()) {
          setTraID(other.getTraID());
        }
        if (other.hasSrvFlag()) {
          setSrvFlag(other.getSrvFlag());
        }
        if (other.hasAnsRes()) {
          setAnsRes(other.getAnsRes());
        }
        if (other.hasAuthAnsType()) {
          setAuthAnsType(other.getAuthAnsType());
        }
        if (other.hasAuthAnsRes()) {
          setAuthAnsRes(other.getAuthAnsRes());
        }
        if (other.hasAddAnsType()) {
          setAddAnsType(other.getAddAnsType());
        }
        if (other.hasAddAnsRes()) {
          setAddAnsRes(other.getAddAnsRes());
        }
        if (!other.mxIpv6_.isEmpty()) {
          if (mxIpv6_.isEmpty()) {
            mxIpv6_ = other.mxIpv6_;
            mxIpv6_.makeImmutable();
            bitField1_ |= 0x00000080;
          } else {
            ensureMxIpv6IsMutable();
            mxIpv6_.addAll(other.mxIpv6_);
          }
          onChanged();
        }
        if (other.hasMxIpv6Cnt()) {
          setMxIpv6Cnt(other.getMxIpv6Cnt());
        }
        if (!other.nsIpv6_.isEmpty()) {
          if (nsIpv6_.isEmpty()) {
            nsIpv6_ = other.nsIpv6_;
            nsIpv6_.makeImmutable();
            bitField1_ |= 0x00000200;
          } else {
            ensureNsIpv6IsMutable();
            nsIpv6_.addAll(other.nsIpv6_);
          }
          onChanged();
        }
        if (other.hasNsIpv6Cnt()) {
          setNsIpv6Cnt(other.getNsIpv6Cnt());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                addCnt_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                int v = input.readUInt32();
                ensureAipIsMutable();
                aip_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureAipIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  aip_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              case 26: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAipAsnIsMutable();
                aipAsn_.add(v);
                break;
              } // case 26
              case 32: {
                aipCnt_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAIpv6IsMutable();
                aIpv6_.add(v);
                break;
              } // case 42
              case 48: {
                aIpv6Cnt_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 58: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAipCountryIsMutable();
                aipCountry_.add(v);
                break;
              } // case 58
              case 66: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAnsCnameIsMutable();
                ansCname_.add(v);
                break;
              } // case 66
              case 72: {
                ansCnameCnt_ = input.readUInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 80: {
                ansCnt_ = input.readUInt32();
                bitField0_ |= 0x00000200;
                break;
              } // case 80
              case 90: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAnsIPv6IsMutable();
                ansIPv6_.add(v);
                break;
              } // case 90
              case 98: {
                ansQue_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 106: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureAnsTypesIsMutable();
                ansTypes_.add(v);
                break;
              } // case 106
              case 112: {
                autCnt_ = input.readUInt32();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 122: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureMxIpAsnIsMutable();
                mxIpAsn_.add(v);
                break;
              } // case 122
              case 130: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureMxIpCountryIsMutable();
                mxIpCountry_.add(v);
                break;
              } // case 130
              case 138: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureMailSrvHostIsMutable();
                mailSrvHost_.add(v);
                break;
              } // case 138
              case 144: {
                mailSrvHostcnt_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 152: {
                int v = input.readUInt32();
                ensureMailSrvIpIsMutable();
                mailSrvIp_.addInt(v);
                break;
              } // case 152
              case 154: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureMailSrvIpIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  mailSrvIp_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 154
              case 160: {
                mailSrvIPCnt_ = input.readUInt32();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              case 170: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureNameSrvAsnIsMutable();
                nameSrvAsn_.add(v);
                break;
              } // case 170
              case 178: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureNameSrvCountryIsMutable();
                nameSrvCountry_.add(v);
                break;
              } // case 178
              case 186: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureNameSrvHostIsMutable();
                nameSrvHost_.add(v);
                break;
              } // case 186
              case 192: {
                nameSrvHostCnt_ = input.readUInt32();
                bitField0_ |= 0x00800000;
                break;
              } // case 192
              case 200: {
                int v = input.readUInt32();
                ensureNsIpIsMutable();
                nsIp_.addInt(v);
                break;
              } // case 200
              case 202: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureNsIpIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  nsIp_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 202
              case 208: {
                nsIpCnt_ = input.readUInt32();
                bitField0_ |= 0x02000000;
                break;
              } // case 208
              case 218: {
                ansName_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 224: {
                addRrs_ = input.readUInt32();
                bitField0_ |= 0x08000000;
                break;
              } // case 224
              case 234: {
                dnsSpf_ = input.readBytes();
                bitField0_ |= 0x10000000;
                break;
              } // case 234
              case 242: {
                dnsTxt_ = input.readBytes();
                bitField0_ |= 0x20000000;
                break;
              } // case 242
              case 248: {
                queType_ = input.readUInt32();
                bitField0_ |= 0x40000000;
                break;
              } // case 248
              case 258: {
                queName_ = input.readBytes();
                bitField0_ |= 0x80000000;
                break;
              } // case 258
              case 264: {
                traID_ = input.readUInt32();
                bitField1_ |= 0x00000001;
                break;
              } // case 264
              case 272: {
                srvFlag_ = input.readUInt32();
                bitField1_ |= 0x00000002;
                break;
              } // case 272
              case 282: {
                ansRes_ = input.readBytes();
                bitField1_ |= 0x00000004;
                break;
              } // case 282
              case 288: {
                authAnsType_ = input.readUInt32();
                bitField1_ |= 0x00000008;
                break;
              } // case 288
              case 298: {
                authAnsRes_ = input.readBytes();
                bitField1_ |= 0x00000010;
                break;
              } // case 298
              case 304: {
                addAnsType_ = input.readUInt32();
                bitField1_ |= 0x00000020;
                break;
              } // case 304
              case 314: {
                addAnsRes_ = input.readBytes();
                bitField1_ |= 0x00000040;
                break;
              } // case 314
              case 322: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureMxIpv6IsMutable();
                mxIpv6_.add(v);
                break;
              } // case 322
              case 328: {
                mxIpv6Cnt_ = input.readUInt32();
                bitField1_ |= 0x00000100;
                break;
              } // case 328
              case 338: {
                com.google.protobuf.ByteString v = input.readBytes();
                ensureNsIpv6IsMutable();
                nsIpv6_.add(v);
                break;
              } // case 338
              case 344: {
                nsIpv6Cnt_ = input.readUInt32();
                bitField1_ |= 0x00000400;
                break;
              } // case 344
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private int addCnt_ ;
      /**
       * <code>optional uint32 addCnt = 1;</code>
       * @return Whether the addCnt field is set.
       */
      @java.lang.Override
      public boolean hasAddCnt() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 addCnt = 1;</code>
       * @return The addCnt.
       */
      @java.lang.Override
      public int getAddCnt() {
        return addCnt_;
      }
      /**
       * <code>optional uint32 addCnt = 1;</code>
       * @param value The addCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAddCnt(int value) {

        addCnt_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 addCnt = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddCnt() {
        bitField0_ = (bitField0_ & ~0x00000001);
        addCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList aip_ = emptyIntList();
      private void ensureAipIsMutable() {
        if (!aip_.isModifiable()) {
          aip_ = makeMutableCopy(aip_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <code>repeated uint32 aip = 2;</code>
       * @return A list containing the aip.
       */
      public java.util.List<java.lang.Integer>
          getAipList() {
        aip_.makeImmutable();
        return aip_;
      }
      /**
       * <code>repeated uint32 aip = 2;</code>
       * @return The count of aip.
       */
      public int getAipCount() {
        return aip_.size();
      }
      /**
       * <code>repeated uint32 aip = 2;</code>
       * @param index The index of the element to return.
       * @return The aip at the given index.
       */
      public int getAip(int index) {
        return aip_.getInt(index);
      }
      /**
       * <code>repeated uint32 aip = 2;</code>
       * @param index The index to set the value at.
       * @param value The aip to set.
       * @return This builder for chaining.
       */
      public Builder setAip(
          int index, int value) {

        ensureAipIsMutable();
        aip_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 aip = 2;</code>
       * @param value The aip to add.
       * @return This builder for chaining.
       */
      public Builder addAip(int value) {

        ensureAipIsMutable();
        aip_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 aip = 2;</code>
       * @param values The aip to add.
       * @return This builder for chaining.
       */
      public Builder addAllAip(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureAipIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aip_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 aip = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAip() {
        aip_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> aipAsn_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAipAsnIsMutable() {
        if (!aipAsn_.isModifiable()) {
          aipAsn_ = makeMutableCopy(aipAsn_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       * @return A list containing the aipAsn.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAipAsnList() {
        aipAsn_.makeImmutable();
        return aipAsn_;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       * @return The count of aipAsn.
       */
      public int getAipAsnCount() {
        return aipAsn_.size();
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       * @param index The index of the element to return.
       * @return The aipAsn at the given index.
       */
      public com.google.protobuf.ByteString getAipAsn(int index) {
        return aipAsn_.get(index);
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       * @param index The index to set the value at.
       * @param value The aipAsn to set.
       * @return This builder for chaining.
       */
      public Builder setAipAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAipAsnIsMutable();
        aipAsn_.set(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       * @param value The aipAsn to add.
       * @return This builder for chaining.
       */
      public Builder addAipAsn(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAipAsnIsMutable();
        aipAsn_.add(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       * @param values The aipAsn to add.
       * @return This builder for chaining.
       */
      public Builder addAllAipAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAipAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aipAsn_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipAsn = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAipAsn() {
        aipAsn_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private int aipCnt_ ;
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       * @return Whether the aipCnt field is set.
       */
      @java.lang.Override
      public boolean hasAipCnt() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       * @return The aipCnt.
       */
      @java.lang.Override
      public int getAipCnt() {
        return aipCnt_;
      }
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       * @param value The aipCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAipCnt(int value) {

        aipCnt_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aipCnt = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAipCnt() {
        bitField0_ = (bitField0_ & ~0x00000008);
        aipCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> aIpv6_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAIpv6IsMutable() {
        if (!aIpv6_.isModifiable()) {
          aIpv6_ = makeMutableCopy(aIpv6_);
        }
        bitField0_ |= 0x00000010;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       * @return A list containing the aIpv6.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAIpv6List() {
        aIpv6_.makeImmutable();
        return aIpv6_;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       * @return The count of aIpv6.
       */
      public int getAIpv6Count() {
        return aIpv6_.size();
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       * @param index The index of the element to return.
       * @return The aIpv6 at the given index.
       */
      public com.google.protobuf.ByteString getAIpv6(int index) {
        return aIpv6_.get(index);
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       * @param index The index to set the value at.
       * @param value The aIpv6 to set.
       * @return This builder for chaining.
       */
      public Builder setAIpv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAIpv6IsMutable();
        aIpv6_.set(index, value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       * @param value The aIpv6 to add.
       * @return This builder for chaining.
       */
      public Builder addAIpv6(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAIpv6IsMutable();
        aIpv6_.add(value);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       * @param values The aIpv6 to add.
       * @return This builder for chaining.
       */
      public Builder addAllAIpv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAIpv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aIpv6_);
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aIpv6 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearAIpv6() {
        aIpv6_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }

      private int aIpv6Cnt_ ;
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       * @return Whether the aIpv6Cnt field is set.
       */
      @java.lang.Override
      public boolean hasAIpv6Cnt() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       * @return The aIpv6Cnt.
       */
      @java.lang.Override
      public int getAIpv6Cnt() {
        return aIpv6Cnt_;
      }
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       * @param value The aIpv6Cnt to set.
       * @return This builder for chaining.
       */
      public Builder setAIpv6Cnt(int value) {

        aIpv6Cnt_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aIpv6Cnt = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearAIpv6Cnt() {
        bitField0_ = (bitField0_ & ~0x00000020);
        aIpv6Cnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> aipCountry_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAipCountryIsMutable() {
        if (!aipCountry_.isModifiable()) {
          aipCountry_ = makeMutableCopy(aipCountry_);
        }
        bitField0_ |= 0x00000040;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       * @return A list containing the aipCountry.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAipCountryList() {
        aipCountry_.makeImmutable();
        return aipCountry_;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       * @return The count of aipCountry.
       */
      public int getAipCountryCount() {
        return aipCountry_.size();
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       * @param index The index of the element to return.
       * @return The aipCountry at the given index.
       */
      public com.google.protobuf.ByteString getAipCountry(int index) {
        return aipCountry_.get(index);
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       * @param index The index to set the value at.
       * @param value The aipCountry to set.
       * @return This builder for chaining.
       */
      public Builder setAipCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAipCountryIsMutable();
        aipCountry_.set(index, value);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       * @param value The aipCountry to add.
       * @return This builder for chaining.
       */
      public Builder addAipCountry(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAipCountryIsMutable();
        aipCountry_.add(value);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       * @param values The aipCountry to add.
       * @return This builder for chaining.
       */
      public Builder addAllAipCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAipCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, aipCountry_);
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes aipCountry = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearAipCountry() {
        aipCountry_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> ansCname_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAnsCnameIsMutable() {
        if (!ansCname_.isModifiable()) {
          ansCname_ = makeMutableCopy(ansCname_);
        }
        bitField0_ |= 0x00000080;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       * @return A list containing the ansCname.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAnsCnameList() {
        ansCname_.makeImmutable();
        return ansCname_;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       * @return The count of ansCname.
       */
      public int getAnsCnameCount() {
        return ansCname_.size();
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       * @param index The index of the element to return.
       * @return The ansCname at the given index.
       */
      public com.google.protobuf.ByteString getAnsCname(int index) {
        return ansCname_.get(index);
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       * @param index The index to set the value at.
       * @param value The ansCname to set.
       * @return This builder for chaining.
       */
      public Builder setAnsCname(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAnsCnameIsMutable();
        ansCname_.set(index, value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       * @param value The ansCname to add.
       * @return This builder for chaining.
       */
      public Builder addAnsCname(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAnsCnameIsMutable();
        ansCname_.add(value);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       * @param values The ansCname to add.
       * @return This builder for chaining.
       */
      public Builder addAllAnsCname(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAnsCnameIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ansCname_);
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansCname = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsCname() {
        ansCname_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }

      private int ansCnameCnt_ ;
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       * @return Whether the ansCnameCnt field is set.
       */
      @java.lang.Override
      public boolean hasAnsCnameCnt() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       * @return The ansCnameCnt.
       */
      @java.lang.Override
      public int getAnsCnameCnt() {
        return ansCnameCnt_;
      }
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       * @param value The ansCnameCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAnsCnameCnt(int value) {

        ansCnameCnt_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ansCnameCnt = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsCnameCnt() {
        bitField0_ = (bitField0_ & ~0x00000100);
        ansCnameCnt_ = 0;
        onChanged();
        return this;
      }

      private int ansCnt_ ;
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       * @return Whether the ansCnt field is set.
       */
      @java.lang.Override
      public boolean hasAnsCnt() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       * @return The ansCnt.
       */
      @java.lang.Override
      public int getAnsCnt() {
        return ansCnt_;
      }
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       * @param value The ansCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAnsCnt(int value) {

        ansCnt_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 ansCnt = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsCnt() {
        bitField0_ = (bitField0_ & ~0x00000200);
        ansCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> ansIPv6_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAnsIPv6IsMutable() {
        if (!ansIPv6_.isModifiable()) {
          ansIPv6_ = makeMutableCopy(ansIPv6_);
        }
        bitField0_ |= 0x00000400;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       * @return A list containing the ansIPv6.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAnsIPv6List() {
        ansIPv6_.makeImmutable();
        return ansIPv6_;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       * @return The count of ansIPv6.
       */
      public int getAnsIPv6Count() {
        return ansIPv6_.size();
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       * @param index The index of the element to return.
       * @return The ansIPv6 at the given index.
       */
      public com.google.protobuf.ByteString getAnsIPv6(int index) {
        return ansIPv6_.get(index);
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       * @param index The index to set the value at.
       * @param value The ansIPv6 to set.
       * @return This builder for chaining.
       */
      public Builder setAnsIPv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAnsIPv6IsMutable();
        ansIPv6_.set(index, value);
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       * @param value The ansIPv6 to add.
       * @return This builder for chaining.
       */
      public Builder addAnsIPv6(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAnsIPv6IsMutable();
        ansIPv6_.add(value);
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       * @param values The ansIPv6 to add.
       * @return This builder for chaining.
       */
      public Builder addAllAnsIPv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAnsIPv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ansIPv6_);
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansIPv6 = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsIPv6() {
        ansIPv6_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ansQue_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ansQue = 12;</code>
       * @return Whether the ansQue field is set.
       */
      @java.lang.Override
      public boolean hasAnsQue() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes ansQue = 12;</code>
       * @return The ansQue.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAnsQue() {
        return ansQue_;
      }
      /**
       * <code>optional bytes ansQue = 12;</code>
       * @param value The ansQue to set.
       * @return This builder for chaining.
       */
      public Builder setAnsQue(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ansQue_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ansQue = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsQue() {
        bitField0_ = (bitField0_ & ~0x00000800);
        ansQue_ = getDefaultInstance().getAnsQue();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> ansTypes_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureAnsTypesIsMutable() {
        if (!ansTypes_.isModifiable()) {
          ansTypes_ = makeMutableCopy(ansTypes_);
        }
        bitField0_ |= 0x00001000;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       * @return A list containing the ansTypes.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getAnsTypesList() {
        ansTypes_.makeImmutable();
        return ansTypes_;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       * @return The count of ansTypes.
       */
      public int getAnsTypesCount() {
        return ansTypes_.size();
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       * @param index The index of the element to return.
       * @return The ansTypes at the given index.
       */
      public com.google.protobuf.ByteString getAnsTypes(int index) {
        return ansTypes_.get(index);
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       * @param index The index to set the value at.
       * @param value The ansTypes to set.
       * @return This builder for chaining.
       */
      public Builder setAnsTypes(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAnsTypesIsMutable();
        ansTypes_.set(index, value);
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       * @param value The ansTypes to add.
       * @return This builder for chaining.
       */
      public Builder addAnsTypes(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureAnsTypesIsMutable();
        ansTypes_.add(value);
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       * @param values The ansTypes to add.
       * @return This builder for chaining.
       */
      public Builder addAllAnsTypes(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureAnsTypesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ansTypes_);
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes ansTypes = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsTypes() {
        ansTypes_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00001000);
        onChanged();
        return this;
      }

      private int autCnt_ ;
      /**
       * <code>optional uint32 autCnt = 14;</code>
       * @return Whether the autCnt field is set.
       */
      @java.lang.Override
      public boolean hasAutCnt() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint32 autCnt = 14;</code>
       * @return The autCnt.
       */
      @java.lang.Override
      public int getAutCnt() {
        return autCnt_;
      }
      /**
       * <code>optional uint32 autCnt = 14;</code>
       * @param value The autCnt to set.
       * @return This builder for chaining.
       */
      public Builder setAutCnt(int value) {

        autCnt_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 autCnt = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearAutCnt() {
        bitField0_ = (bitField0_ & ~0x00002000);
        autCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mxIpAsn_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureMxIpAsnIsMutable() {
        if (!mxIpAsn_.isModifiable()) {
          mxIpAsn_ = makeMutableCopy(mxIpAsn_);
        }
        bitField0_ |= 0x00004000;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       * @return A list containing the mxIpAsn.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMxIpAsnList() {
        mxIpAsn_.makeImmutable();
        return mxIpAsn_;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       * @return The count of mxIpAsn.
       */
      public int getMxIpAsnCount() {
        return mxIpAsn_.size();
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       * @param index The index of the element to return.
       * @return The mxIpAsn at the given index.
       */
      public com.google.protobuf.ByteString getMxIpAsn(int index) {
        return mxIpAsn_.get(index);
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       * @param index The index to set the value at.
       * @param value The mxIpAsn to set.
       * @return This builder for chaining.
       */
      public Builder setMxIpAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMxIpAsnIsMutable();
        mxIpAsn_.set(index, value);
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       * @param value The mxIpAsn to add.
       * @return This builder for chaining.
       */
      public Builder addMxIpAsn(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMxIpAsnIsMutable();
        mxIpAsn_.add(value);
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       * @param values The mxIpAsn to add.
       * @return This builder for chaining.
       */
      public Builder addAllMxIpAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMxIpAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mxIpAsn_);
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpAsn = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearMxIpAsn() {
        mxIpAsn_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00004000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mxIpCountry_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureMxIpCountryIsMutable() {
        if (!mxIpCountry_.isModifiable()) {
          mxIpCountry_ = makeMutableCopy(mxIpCountry_);
        }
        bitField0_ |= 0x00008000;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       * @return A list containing the mxIpCountry.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMxIpCountryList() {
        mxIpCountry_.makeImmutable();
        return mxIpCountry_;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       * @return The count of mxIpCountry.
       */
      public int getMxIpCountryCount() {
        return mxIpCountry_.size();
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       * @param index The index of the element to return.
       * @return The mxIpCountry at the given index.
       */
      public com.google.protobuf.ByteString getMxIpCountry(int index) {
        return mxIpCountry_.get(index);
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       * @param index The index to set the value at.
       * @param value The mxIpCountry to set.
       * @return This builder for chaining.
       */
      public Builder setMxIpCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMxIpCountryIsMutable();
        mxIpCountry_.set(index, value);
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       * @param value The mxIpCountry to add.
       * @return This builder for chaining.
       */
      public Builder addMxIpCountry(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMxIpCountryIsMutable();
        mxIpCountry_.add(value);
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       * @param values The mxIpCountry to add.
       * @return This builder for chaining.
       */
      public Builder addAllMxIpCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMxIpCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mxIpCountry_);
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpCountry = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearMxIpCountry() {
        mxIpCountry_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00008000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mailSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureMailSrvHostIsMutable() {
        if (!mailSrvHost_.isModifiable()) {
          mailSrvHost_ = makeMutableCopy(mailSrvHost_);
        }
        bitField0_ |= 0x00010000;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       * @return A list containing the mailSrvHost.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMailSrvHostList() {
        mailSrvHost_.makeImmutable();
        return mailSrvHost_;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       * @return The count of mailSrvHost.
       */
      public int getMailSrvHostCount() {
        return mailSrvHost_.size();
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       * @param index The index of the element to return.
       * @return The mailSrvHost at the given index.
       */
      public com.google.protobuf.ByteString getMailSrvHost(int index) {
        return mailSrvHost_.get(index);
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       * @param index The index to set the value at.
       * @param value The mailSrvHost to set.
       * @return This builder for chaining.
       */
      public Builder setMailSrvHost(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMailSrvHostIsMutable();
        mailSrvHost_.set(index, value);
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       * @param value The mailSrvHost to add.
       * @return This builder for chaining.
       */
      public Builder addMailSrvHost(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMailSrvHostIsMutable();
        mailSrvHost_.add(value);
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       * @param values The mailSrvHost to add.
       * @return This builder for chaining.
       */
      public Builder addAllMailSrvHost(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMailSrvHostIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mailSrvHost_);
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mailSrvHost = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailSrvHost() {
        mailSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }

      private int mailSrvHostcnt_ ;
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       * @return Whether the mailSrvHostcnt field is set.
       */
      @java.lang.Override
      public boolean hasMailSrvHostcnt() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       * @return The mailSrvHostcnt.
       */
      @java.lang.Override
      public int getMailSrvHostcnt() {
        return mailSrvHostcnt_;
      }
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       * @param value The mailSrvHostcnt to set.
       * @return This builder for chaining.
       */
      public Builder setMailSrvHostcnt(int value) {

        mailSrvHostcnt_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mailSrvHostcnt = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailSrvHostcnt() {
        bitField0_ = (bitField0_ & ~0x00020000);
        mailSrvHostcnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList mailSrvIp_ = emptyIntList();
      private void ensureMailSrvIpIsMutable() {
        if (!mailSrvIp_.isModifiable()) {
          mailSrvIp_ = makeMutableCopy(mailSrvIp_);
        }
        bitField0_ |= 0x00040000;
      }
      /**
       * <code>repeated uint32 mailSrvIp = 19;</code>
       * @return A list containing the mailSrvIp.
       */
      public java.util.List<java.lang.Integer>
          getMailSrvIpList() {
        mailSrvIp_.makeImmutable();
        return mailSrvIp_;
      }
      /**
       * <code>repeated uint32 mailSrvIp = 19;</code>
       * @return The count of mailSrvIp.
       */
      public int getMailSrvIpCount() {
        return mailSrvIp_.size();
      }
      /**
       * <code>repeated uint32 mailSrvIp = 19;</code>
       * @param index The index of the element to return.
       * @return The mailSrvIp at the given index.
       */
      public int getMailSrvIp(int index) {
        return mailSrvIp_.getInt(index);
      }
      /**
       * <code>repeated uint32 mailSrvIp = 19;</code>
       * @param index The index to set the value at.
       * @param value The mailSrvIp to set.
       * @return This builder for chaining.
       */
      public Builder setMailSrvIp(
          int index, int value) {

        ensureMailSrvIpIsMutable();
        mailSrvIp_.setInt(index, value);
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 mailSrvIp = 19;</code>
       * @param value The mailSrvIp to add.
       * @return This builder for chaining.
       */
      public Builder addMailSrvIp(int value) {

        ensureMailSrvIpIsMutable();
        mailSrvIp_.addInt(value);
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 mailSrvIp = 19;</code>
       * @param values The mailSrvIp to add.
       * @return This builder for chaining.
       */
      public Builder addAllMailSrvIp(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureMailSrvIpIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mailSrvIp_);
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 mailSrvIp = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailSrvIp() {
        mailSrvIp_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }

      private int mailSrvIPCnt_ ;
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       * @return Whether the mailSrvIPCnt field is set.
       */
      @java.lang.Override
      public boolean hasMailSrvIPCnt() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       * @return The mailSrvIPCnt.
       */
      @java.lang.Override
      public int getMailSrvIPCnt() {
        return mailSrvIPCnt_;
      }
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       * @param value The mailSrvIPCnt to set.
       * @return This builder for chaining.
       */
      public Builder setMailSrvIPCnt(int value) {

        mailSrvIPCnt_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mailSrvIPCnt = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailSrvIPCnt() {
        bitField0_ = (bitField0_ & ~0x00080000);
        mailSrvIPCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nameSrvAsn_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureNameSrvAsnIsMutable() {
        if (!nameSrvAsn_.isModifiable()) {
          nameSrvAsn_ = makeMutableCopy(nameSrvAsn_);
        }
        bitField0_ |= 0x00100000;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       * @return A list containing the nameSrvAsn.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNameSrvAsnList() {
        nameSrvAsn_.makeImmutable();
        return nameSrvAsn_;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       * @return The count of nameSrvAsn.
       */
      public int getNameSrvAsnCount() {
        return nameSrvAsn_.size();
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       * @param index The index of the element to return.
       * @return The nameSrvAsn at the given index.
       */
      public com.google.protobuf.ByteString getNameSrvAsn(int index) {
        return nameSrvAsn_.get(index);
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       * @param index The index to set the value at.
       * @param value The nameSrvAsn to set.
       * @return This builder for chaining.
       */
      public Builder setNameSrvAsn(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNameSrvAsnIsMutable();
        nameSrvAsn_.set(index, value);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       * @param value The nameSrvAsn to add.
       * @return This builder for chaining.
       */
      public Builder addNameSrvAsn(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNameSrvAsnIsMutable();
        nameSrvAsn_.add(value);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       * @param values The nameSrvAsn to add.
       * @return This builder for chaining.
       */
      public Builder addAllNameSrvAsn(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNameSrvAsnIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nameSrvAsn_);
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvAsn = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameSrvAsn() {
        nameSrvAsn_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nameSrvCountry_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureNameSrvCountryIsMutable() {
        if (!nameSrvCountry_.isModifiable()) {
          nameSrvCountry_ = makeMutableCopy(nameSrvCountry_);
        }
        bitField0_ |= 0x00200000;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       * @return A list containing the nameSrvCountry.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNameSrvCountryList() {
        nameSrvCountry_.makeImmutable();
        return nameSrvCountry_;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       * @return The count of nameSrvCountry.
       */
      public int getNameSrvCountryCount() {
        return nameSrvCountry_.size();
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       * @param index The index of the element to return.
       * @return The nameSrvCountry at the given index.
       */
      public com.google.protobuf.ByteString getNameSrvCountry(int index) {
        return nameSrvCountry_.get(index);
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       * @param index The index to set the value at.
       * @param value The nameSrvCountry to set.
       * @return This builder for chaining.
       */
      public Builder setNameSrvCountry(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNameSrvCountryIsMutable();
        nameSrvCountry_.set(index, value);
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       * @param value The nameSrvCountry to add.
       * @return This builder for chaining.
       */
      public Builder addNameSrvCountry(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNameSrvCountryIsMutable();
        nameSrvCountry_.add(value);
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       * @param values The nameSrvCountry to add.
       * @return This builder for chaining.
       */
      public Builder addAllNameSrvCountry(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNameSrvCountryIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nameSrvCountry_);
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvCountry = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameSrvCountry() {
        nameSrvCountry_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00200000);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nameSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureNameSrvHostIsMutable() {
        if (!nameSrvHost_.isModifiable()) {
          nameSrvHost_ = makeMutableCopy(nameSrvHost_);
        }
        bitField0_ |= 0x00400000;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       * @return A list containing the nameSrvHost.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNameSrvHostList() {
        nameSrvHost_.makeImmutable();
        return nameSrvHost_;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       * @return The count of nameSrvHost.
       */
      public int getNameSrvHostCount() {
        return nameSrvHost_.size();
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       * @param index The index of the element to return.
       * @return The nameSrvHost at the given index.
       */
      public com.google.protobuf.ByteString getNameSrvHost(int index) {
        return nameSrvHost_.get(index);
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       * @param index The index to set the value at.
       * @param value The nameSrvHost to set.
       * @return This builder for chaining.
       */
      public Builder setNameSrvHost(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNameSrvHostIsMutable();
        nameSrvHost_.set(index, value);
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       * @param value The nameSrvHost to add.
       * @return This builder for chaining.
       */
      public Builder addNameSrvHost(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNameSrvHostIsMutable();
        nameSrvHost_.add(value);
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       * @param values The nameSrvHost to add.
       * @return This builder for chaining.
       */
      public Builder addAllNameSrvHost(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNameSrvHostIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nameSrvHost_);
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nameSrvHost = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameSrvHost() {
        nameSrvHost_ = emptyList(com.google.protobuf.ByteString.class);
        bitField0_ = (bitField0_ & ~0x00400000);
        onChanged();
        return this;
      }

      private int nameSrvHostCnt_ ;
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       * @return Whether the nameSrvHostCnt field is set.
       */
      @java.lang.Override
      public boolean hasNameSrvHostCnt() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       * @return The nameSrvHostCnt.
       */
      @java.lang.Override
      public int getNameSrvHostCnt() {
        return nameSrvHostCnt_;
      }
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       * @param value The nameSrvHostCnt to set.
       * @return This builder for chaining.
       */
      public Builder setNameSrvHostCnt(int value) {

        nameSrvHostCnt_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nameSrvHostCnt = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameSrvHostCnt() {
        bitField0_ = (bitField0_ & ~0x00800000);
        nameSrvHostCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList nsIp_ = emptyIntList();
      private void ensureNsIpIsMutable() {
        if (!nsIp_.isModifiable()) {
          nsIp_ = makeMutableCopy(nsIp_);
        }
        bitField0_ |= 0x01000000;
      }
      /**
       * <code>repeated uint32 nsIp = 25;</code>
       * @return A list containing the nsIp.
       */
      public java.util.List<java.lang.Integer>
          getNsIpList() {
        nsIp_.makeImmutable();
        return nsIp_;
      }
      /**
       * <code>repeated uint32 nsIp = 25;</code>
       * @return The count of nsIp.
       */
      public int getNsIpCount() {
        return nsIp_.size();
      }
      /**
       * <code>repeated uint32 nsIp = 25;</code>
       * @param index The index of the element to return.
       * @return The nsIp at the given index.
       */
      public int getNsIp(int index) {
        return nsIp_.getInt(index);
      }
      /**
       * <code>repeated uint32 nsIp = 25;</code>
       * @param index The index to set the value at.
       * @param value The nsIp to set.
       * @return This builder for chaining.
       */
      public Builder setNsIp(
          int index, int value) {

        ensureNsIpIsMutable();
        nsIp_.setInt(index, value);
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 nsIp = 25;</code>
       * @param value The nsIp to add.
       * @return This builder for chaining.
       */
      public Builder addNsIp(int value) {

        ensureNsIpIsMutable();
        nsIp_.addInt(value);
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 nsIp = 25;</code>
       * @param values The nsIp to add.
       * @return This builder for chaining.
       */
      public Builder addAllNsIp(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureNsIpIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nsIp_);
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 nsIp = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearNsIp() {
        nsIp_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x01000000);
        onChanged();
        return this;
      }

      private int nsIpCnt_ ;
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       * @return Whether the nsIpCnt field is set.
       */
      @java.lang.Override
      public boolean hasNsIpCnt() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       * @return The nsIpCnt.
       */
      @java.lang.Override
      public int getNsIpCnt() {
        return nsIpCnt_;
      }
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       * @param value The nsIpCnt to set.
       * @return This builder for chaining.
       */
      public Builder setNsIpCnt(int value) {

        nsIpCnt_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nsIpCnt = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearNsIpCnt() {
        bitField0_ = (bitField0_ & ~0x02000000);
        nsIpCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ansName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ansName = 27;</code>
       * @return Whether the ansName field is set.
       */
      @java.lang.Override
      public boolean hasAnsName() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes ansName = 27;</code>
       * @return The ansName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAnsName() {
        return ansName_;
      }
      /**
       * <code>optional bytes ansName = 27;</code>
       * @param value The ansName to set.
       * @return This builder for chaining.
       */
      public Builder setAnsName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ansName_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ansName = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsName() {
        bitField0_ = (bitField0_ & ~0x04000000);
        ansName_ = getDefaultInstance().getAnsName();
        onChanged();
        return this;
      }

      private int addRrs_ ;
      /**
       * <code>optional uint32 addRrs = 28;</code>
       * @return Whether the addRrs field is set.
       */
      @java.lang.Override
      public boolean hasAddRrs() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint32 addRrs = 28;</code>
       * @return The addRrs.
       */
      @java.lang.Override
      public int getAddRrs() {
        return addRrs_;
      }
      /**
       * <code>optional uint32 addRrs = 28;</code>
       * @param value The addRrs to set.
       * @return This builder for chaining.
       */
      public Builder setAddRrs(int value) {

        addRrs_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 addRrs = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddRrs() {
        bitField0_ = (bitField0_ & ~0x08000000);
        addRrs_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dnsSpf_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       * @return Whether the dnsSpf field is set.
       */
      @java.lang.Override
      public boolean hasDnsSpf() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       * @return The dnsSpf.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDnsSpf() {
        return dnsSpf_;
      }
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       * @param value The dnsSpf to set.
       * @return This builder for chaining.
       */
      public Builder setDnsSpf(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dnsSpf_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes dnsSpf = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearDnsSpf() {
        bitField0_ = (bitField0_ & ~0x10000000);
        dnsSpf_ = getDefaultInstance().getDnsSpf();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dnsTxt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       * @return Whether the dnsTxt field is set.
       */
      @java.lang.Override
      public boolean hasDnsTxt() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       * @return The dnsTxt.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDnsTxt() {
        return dnsTxt_;
      }
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       * @param value The dnsTxt to set.
       * @return This builder for chaining.
       */
      public Builder setDnsTxt(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dnsTxt_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes dnsTxt = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearDnsTxt() {
        bitField0_ = (bitField0_ & ~0x20000000);
        dnsTxt_ = getDefaultInstance().getDnsTxt();
        onChanged();
        return this;
      }

      private int queType_ ;
      /**
       * <code>optional uint32 queType = 31;</code>
       * @return Whether the queType field is set.
       */
      @java.lang.Override
      public boolean hasQueType() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional uint32 queType = 31;</code>
       * @return The queType.
       */
      @java.lang.Override
      public int getQueType() {
        return queType_;
      }
      /**
       * <code>optional uint32 queType = 31;</code>
       * @param value The queType to set.
       * @return This builder for chaining.
       */
      public Builder setQueType(int value) {

        queType_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 queType = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueType() {
        bitField0_ = (bitField0_ & ~0x40000000);
        queType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString queName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes queName = 32;</code>
       * @return Whether the queName field is set.
       */
      @java.lang.Override
      public boolean hasQueName() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes queName = 32;</code>
       * @return The queName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getQueName() {
        return queName_;
      }
      /**
       * <code>optional bytes queName = 32;</code>
       * @param value The queName to set.
       * @return This builder for chaining.
       */
      public Builder setQueName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        queName_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes queName = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueName() {
        bitField0_ = (bitField0_ & ~0x80000000);
        queName_ = getDefaultInstance().getQueName();
        onChanged();
        return this;
      }

      private int traID_ ;
      /**
       * <code>optional uint32 traID = 33;</code>
       * @return Whether the traID field is set.
       */
      @java.lang.Override
      public boolean hasTraID() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 traID = 33;</code>
       * @return The traID.
       */
      @java.lang.Override
      public int getTraID() {
        return traID_;
      }
      /**
       * <code>optional uint32 traID = 33;</code>
       * @param value The traID to set.
       * @return This builder for chaining.
       */
      public Builder setTraID(int value) {

        traID_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 traID = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearTraID() {
        bitField1_ = (bitField1_ & ~0x00000001);
        traID_ = 0;
        onChanged();
        return this;
      }

      private int srvFlag_ ;
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       * @return Whether the srvFlag field is set.
       */
      @java.lang.Override
      public boolean hasSrvFlag() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       * @return The srvFlag.
       */
      @java.lang.Override
      public int getSrvFlag() {
        return srvFlag_;
      }
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       * @param value The srvFlag to set.
       * @return This builder for chaining.
       */
      public Builder setSrvFlag(int value) {

        srvFlag_ = value;
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvFlag = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvFlag() {
        bitField1_ = (bitField1_ & ~0x00000002);
        srvFlag_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ansRes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ansRes = 35;</code>
       * @return Whether the ansRes field is set.
       */
      @java.lang.Override
      public boolean hasAnsRes() {
        return ((bitField1_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes ansRes = 35;</code>
       * @return The ansRes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAnsRes() {
        return ansRes_;
      }
      /**
       * <code>optional bytes ansRes = 35;</code>
       * @param value The ansRes to set.
       * @return This builder for chaining.
       */
      public Builder setAnsRes(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ansRes_ = value;
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ansRes = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearAnsRes() {
        bitField1_ = (bitField1_ & ~0x00000004);
        ansRes_ = getDefaultInstance().getAnsRes();
        onChanged();
        return this;
      }

      private int authAnsType_ ;
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       * @return Whether the authAnsType field is set.
       */
      @java.lang.Override
      public boolean hasAuthAnsType() {
        return ((bitField1_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       * @return The authAnsType.
       */
      @java.lang.Override
      public int getAuthAnsType() {
        return authAnsType_;
      }
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       * @param value The authAnsType to set.
       * @return This builder for chaining.
       */
      public Builder setAuthAnsType(int value) {

        authAnsType_ = value;
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 authAnsType = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthAnsType() {
        bitField1_ = (bitField1_ & ~0x00000008);
        authAnsType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString authAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       * @return Whether the authAnsRes field is set.
       */
      @java.lang.Override
      public boolean hasAuthAnsRes() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       * @return The authAnsRes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAuthAnsRes() {
        return authAnsRes_;
      }
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       * @param value The authAnsRes to set.
       * @return This builder for chaining.
       */
      public Builder setAuthAnsRes(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        authAnsRes_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes authAnsRes = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthAnsRes() {
        bitField1_ = (bitField1_ & ~0x00000010);
        authAnsRes_ = getDefaultInstance().getAuthAnsRes();
        onChanged();
        return this;
      }

      private int addAnsType_ ;
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       * @return Whether the addAnsType field is set.
       */
      @java.lang.Override
      public boolean hasAddAnsType() {
        return ((bitField1_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       * @return The addAnsType.
       */
      @java.lang.Override
      public int getAddAnsType() {
        return addAnsType_;
      }
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       * @param value The addAnsType to set.
       * @return This builder for chaining.
       */
      public Builder setAddAnsType(int value) {

        addAnsType_ = value;
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 addAnsType = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddAnsType() {
        bitField1_ = (bitField1_ & ~0x00000020);
        addAnsType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString addAnsRes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       * @return Whether the addAnsRes field is set.
       */
      @java.lang.Override
      public boolean hasAddAnsRes() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       * @return The addAnsRes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getAddAnsRes() {
        return addAnsRes_;
      }
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       * @param value The addAnsRes to set.
       * @return This builder for chaining.
       */
      public Builder setAddAnsRes(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        addAnsRes_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes addAnsRes = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddAnsRes() {
        bitField1_ = (bitField1_ & ~0x00000040);
        addAnsRes_ = getDefaultInstance().getAddAnsRes();
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> mxIpv6_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureMxIpv6IsMutable() {
        if (!mxIpv6_.isModifiable()) {
          mxIpv6_ = makeMutableCopy(mxIpv6_);
        }
        bitField1_ |= 0x00000080;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       * @return A list containing the mxIpv6.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getMxIpv6List() {
        mxIpv6_.makeImmutable();
        return mxIpv6_;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       * @return The count of mxIpv6.
       */
      public int getMxIpv6Count() {
        return mxIpv6_.size();
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       * @param index The index of the element to return.
       * @return The mxIpv6 at the given index.
       */
      public com.google.protobuf.ByteString getMxIpv6(int index) {
        return mxIpv6_.get(index);
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       * @param index The index to set the value at.
       * @param value The mxIpv6 to set.
       * @return This builder for chaining.
       */
      public Builder setMxIpv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMxIpv6IsMutable();
        mxIpv6_.set(index, value);
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       * @param value The mxIpv6 to add.
       * @return This builder for chaining.
       */
      public Builder addMxIpv6(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureMxIpv6IsMutable();
        mxIpv6_.add(value);
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       * @param values The mxIpv6 to add.
       * @return This builder for chaining.
       */
      public Builder addAllMxIpv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureMxIpv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, mxIpv6_);
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes mxIpv6 = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearMxIpv6() {
        mxIpv6_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00000080);
        onChanged();
        return this;
      }

      private int mxIpv6Cnt_ ;
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       * @return Whether the mxIpv6Cnt field is set.
       */
      @java.lang.Override
      public boolean hasMxIpv6Cnt() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       * @return The mxIpv6Cnt.
       */
      @java.lang.Override
      public int getMxIpv6Cnt() {
        return mxIpv6Cnt_;
      }
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       * @param value The mxIpv6Cnt to set.
       * @return This builder for chaining.
       */
      public Builder setMxIpv6Cnt(int value) {

        mxIpv6Cnt_ = value;
        bitField1_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 mxIpv6Cnt = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearMxIpv6Cnt() {
        bitField1_ = (bitField1_ & ~0x00000100);
        mxIpv6Cnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.ProtobufList<com.google.protobuf.ByteString> nsIpv6_ = emptyList(com.google.protobuf.ByteString.class);
      private void ensureNsIpv6IsMutable() {
        if (!nsIpv6_.isModifiable()) {
          nsIpv6_ = makeMutableCopy(nsIpv6_);
        }
        bitField1_ |= 0x00000200;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       * @return A list containing the nsIpv6.
       */
      public java.util.List<com.google.protobuf.ByteString>
          getNsIpv6List() {
        nsIpv6_.makeImmutable();
        return nsIpv6_;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       * @return The count of nsIpv6.
       */
      public int getNsIpv6Count() {
        return nsIpv6_.size();
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       * @param index The index of the element to return.
       * @return The nsIpv6 at the given index.
       */
      public com.google.protobuf.ByteString getNsIpv6(int index) {
        return nsIpv6_.get(index);
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       * @param index The index to set the value at.
       * @param value The nsIpv6 to set.
       * @return This builder for chaining.
       */
      public Builder setNsIpv6(
          int index, com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNsIpv6IsMutable();
        nsIpv6_.set(index, value);
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       * @param value The nsIpv6 to add.
       * @return This builder for chaining.
       */
      public Builder addNsIpv6(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ensureNsIpv6IsMutable();
        nsIpv6_.add(value);
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       * @param values The nsIpv6 to add.
       * @return This builder for chaining.
       */
      public Builder addAllNsIpv6(
          java.lang.Iterable<? extends com.google.protobuf.ByteString> values) {
        ensureNsIpv6IsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, nsIpv6_);
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>repeated bytes nsIpv6 = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearNsIpv6() {
        nsIpv6_ = emptyList(com.google.protobuf.ByteString.class);
        bitField1_ = (bitField1_ & ~0x00000200);
        onChanged();
        return this;
      }

      private int nsIpv6Cnt_ ;
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       * @return Whether the nsIpv6Cnt field is set.
       */
      @java.lang.Override
      public boolean hasNsIpv6Cnt() {
        return ((bitField1_ & 0x00000400) != 0);
      }
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       * @return The nsIpv6Cnt.
       */
      @java.lang.Override
      public int getNsIpv6Cnt() {
        return nsIpv6Cnt_;
      }
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       * @param value The nsIpv6Cnt to set.
       * @return This builder for chaining.
       */
      public Builder setNsIpv6Cnt(int value) {

        nsIpv6Cnt_ = value;
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 nsIpv6Cnt = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearNsIpv6Cnt() {
        bitField1_ = (bitField1_ & ~0x00000400);
        nsIpv6Cnt_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:DnsInfo)
    }

    // @@protoc_insertion_point(class_scope:DnsInfo)
    private static final DnsInfoOuterClass.DnsInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new DnsInfoOuterClass.DnsInfo();
    }

    public static DnsInfoOuterClass.DnsInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<DnsInfo>
        PARSER = new com.google.protobuf.AbstractParser<DnsInfo>() {
      @java.lang.Override
      public DnsInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<DnsInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DnsInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public DnsInfoOuterClass.DnsInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_DnsInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_DnsInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rDnsInfo.proto\"\222\006\n\007DnsInfo\022\016\n\006addCnt\030\001 " +
      "\001(\r\022\013\n\003aip\030\002 \003(\r\022\016\n\006aipAsn\030\003 \003(\014\022\016\n\006aipC" +
      "nt\030\004 \001(\r\022\r\n\005aIpv6\030\005 \003(\014\022\020\n\010aIpv6Cnt\030\006 \001(" +
      "\r\022\022\n\naipCountry\030\007 \003(\014\022\020\n\010ansCname\030\010 \003(\014\022" +
      "\023\n\013ansCnameCnt\030\t \001(\r\022\016\n\006ansCnt\030\n \001(\r\022\017\n\007" +
      "ansIPv6\030\013 \003(\014\022\016\n\006ansQue\030\014 \001(\014\022\020\n\010ansType" +
      "s\030\r \003(\014\022\016\n\006autCnt\030\016 \001(\r\022\017\n\007mxIpAsn\030\017 \003(\014" +
      "\022\023\n\013mxIpCountry\030\020 \003(\014\022\023\n\013mailSrvHost\030\021 \003" +
      "(\014\022\026\n\016mailSrvHostcnt\030\022 \001(\r\022\021\n\tmailSrvIp\030" +
      "\023 \003(\r\022\024\n\014mailSrvIPCnt\030\024 \001(\r\022\022\n\nnameSrvAs" +
      "n\030\025 \003(\014\022\026\n\016nameSrvCountry\030\026 \003(\014\022\023\n\013nameS" +
      "rvHost\030\027 \003(\014\022\026\n\016nameSrvHostCnt\030\030 \001(\r\022\014\n\004" +
      "nsIp\030\031 \003(\r\022\017\n\007nsIpCnt\030\032 \001(\r\022\017\n\007ansName\030\033" +
      " \001(\014\022\016\n\006addRrs\030\034 \001(\r\022\016\n\006dnsSpf\030\035 \001(\014\022\016\n\006" +
      "dnsTxt\030\036 \001(\014\022\017\n\007queType\030\037 \001(\r\022\017\n\007queName" +
      "\030  \001(\014\022\r\n\005traID\030! \001(\r\022\017\n\007srvFlag\030\" \001(\r\022\016" +
      "\n\006ansRes\030# \001(\014\022\023\n\013authAnsType\030$ \001(\r\022\022\n\na" +
      "uthAnsRes\030% \001(\014\022\022\n\naddAnsType\030& \001(\r\022\021\n\ta" +
      "ddAnsRes\030\' \001(\014\022\016\n\006mxIpv6\030( \003(\014\022\021\n\tmxIpv6" +
      "Cnt\030) \001(\r\022\016\n\006nsIpv6\030* \003(\014\022\021\n\tnsIpv6Cnt\030+" +
      " \001(\r"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_DnsInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_DnsInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_DnsInfo_descriptor,
        new java.lang.String[] { "AddCnt", "Aip", "AipAsn", "AipCnt", "AIpv6", "AIpv6Cnt", "AipCountry", "AnsCname", "AnsCnameCnt", "AnsCnt", "AnsIPv6", "AnsQue", "AnsTypes", "AutCnt", "MxIpAsn", "MxIpCountry", "MailSrvHost", "MailSrvHostcnt", "MailSrvIp", "MailSrvIPCnt", "NameSrvAsn", "NameSrvCountry", "NameSrvHost", "NameSrvHostCnt", "NsIp", "NsIpCnt", "AnsName", "AddRrs", "DnsSpf", "DnsTxt", "QueType", "QueName", "TraID", "SrvFlag", "AnsRes", "AuthAnsType", "AuthAnsRes", "AddAnsType", "AddAnsRes", "MxIpv6", "MxIpv6Cnt", "NsIpv6", "NsIpv6Cnt", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
