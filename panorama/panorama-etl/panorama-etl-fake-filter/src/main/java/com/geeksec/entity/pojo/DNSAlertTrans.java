package com.geeksec.entity.pojo;

import com.geeksec.entity.trans.DNSTrans;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.message.IocAlertInfo;
import lombok.Data;

import java.util.List;

/**
 * @author: jerry<PERSON>
 * @date: 2024/7/22 11:41
 * @Description:
 **/
@Data
public class DNSAlertTrans {
    private String attackId;
    private long attackTime;
    private long attackTypeCode;
    private String killChain;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;
    private String dnsServerAddr;

    private List<DNSTrans> dns;
    private IocAlertInfo.IOC_ALERT_INFO iocAlertInfo;

    private boolean fakeAlarm;

    // 以下信息通过解析后获取
    // aptCampaignName是大的名字
    // aptName是具体APT子行动的名字
    private boolean relateToAPT;
    private String aptCampaignName;
    private String aptName;
    private String aptDesc;
    private String aptId;
    private String aptCountry;
    private String aptMethod;

    private boolean relateToMalicious = false;
    private String maliciousFamily;
    private String maliciousRisk;
    private String maliciousDesc;
    private String maliciousReference;
}
