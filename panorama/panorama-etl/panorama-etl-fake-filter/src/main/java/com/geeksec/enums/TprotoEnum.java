package com.geeksec.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 传输层协议
 *
 */
public enum TprotoEnum {
    TCP(6, "TCP"),
    UDP(17, "UDP");


    /**
     * 协议编号
     */
    private Integer code;

    /**
     * 协议名称
     */
    private String msg;

    TprotoEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, TprotoEnum> codeEnumMap = new HashMap<>();

    static {
        for (TprotoEnum e : TprotoEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        TprotoEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }
}
