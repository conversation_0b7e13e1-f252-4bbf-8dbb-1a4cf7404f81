package com.geeksec.entity.trans;

import lombok.Data;

import java.util.List;

/**
 * @author: jerryzhou
 * @date: 2024/7/22 21:17
 * @Description:
 **/
@Data
public class EmailTrans {
    // 发件人
    private String mailSenderRaw;

    // 收件人
    private String mailReceiverRaw;

    // 邮件主题
    private String mailSubject;

    // 邮件正文
    private String mailContent;

    // 关联附件
    private String fileMd5;

    // 所属行业
    private String industry;

    // 异常标签
    private List<String> anomalyTags;

    // 告警原因
    private String alertReason;
}
