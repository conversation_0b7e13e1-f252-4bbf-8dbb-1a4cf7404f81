package com.geeksec.transfer.function.process;

import com.geeksec.entity.trans.InputCount;
import lombok.extern.slf4j.Slf4j;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import java.util.HashMap;
import java.util.Map;

import static com.geeksec.common.utils.UpdateCountUtils.*;

@Slf4j
public class AlertCountAllWindowFunction extends ProcessWindowFunction<Map<Integer, Integer>, Integer, Integer, TimeWindow> {
    public static final String ALERT_INPUT_COUNT_TODAY = "AlertInputCount:Today";

    @Override
    public void open(Configuration parameters) throws Exception {

    }

    @Override
    public void close() throws Exception {

    }

    @Override
    public void process(Integer integer, ProcessWindowFunction<Map<Integer, Integer>, Inte<PERSON>, Integer, TimeWindow>.Context context, Iterable<Map<Integer, Integer>> elements, Collector<Integer> out) throws Exception {
        // 按类型进行统计
        if (elements.iterator().hasNext()){
            Map<Integer, Integer> countMap = elements.iterator().next();
            // 更新最近一小时每类统计数据
            boolean updatedCountZet = updateCountZet(countMap);
            if (updatedCountZet) {
                log.info("update count zset success");
            } else {
                log.info("update count zset failed");
            }
            // 更新今日统计数据
            Integer counts = countMap.values().stream().reduce(0, Integer::sum);
            updateCountKeys(counts, ALERT_INPUT_COUNT_TODAY);
            out.collect(counts);
        } else {
            log.info("count map is empty");
        }
    }
}
