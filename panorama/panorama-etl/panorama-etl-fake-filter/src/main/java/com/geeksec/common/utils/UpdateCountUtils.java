package com.geeksec.common.utils;

import com.geeksec.common.constant.DetectTypeConstant;
import lombok.extern.slf4j.Slf4j;
import redis.clients.jedis.Jedis;

import java.time.LocalDateTime;
import java.util.Map;

import static com.geeksec.transfer.function.process.ipProcess.IpFilterFunction.jedisPool;


@Slf4j
public class UpdateCountUtils {
    public static final String IOC_INPUT_COUNT_SET = "IOCInputCount:Hourly";
    public static final String IOA_INPUT_COUNT_SET = "IOAInputCount:Hourly";
    public static final String EMAIL_INPUT_COUNT_SET = "EmailInputCount:Hourly";
    public static final String CRYPTO_INPUT_COUNT_SET = "CryptoInputCount:Hourly";
    public static final String SANDBOX_INPUT_COUNT_SET = "SandboxInputCount:Hourly";
    public static final String FILTER_INPUT_COUNT_SET = "FilterInputCount:Hourly";
    public static final String IOT_INPUT_COUNT_SET = "IotInputCount:Hourly";
    public static final String UPDATE_TODAY_COUNT =
            "if redis.call('exists', KEYS[1]) == 0 then " +
                    "   redis.call('set', KEYS[1], ARGV[1]); " +
                    "else " +
                    "   redis.call('incrBy', KEYS[1], ARGV[1]); " +
                    "end; " +
                    "return redis.call('get', KEYS[1])";
    public static final String UPDATE_COUNT_ZET =
            "if redis.call('exists', KEYS[1]) == 0 then " +
                    "   redis.call('zadd', KEYS[1], ARGV[1], ARGV[2]); " +
                    "   redis.call('expire', KEYS[1], ARGV[3]); " +
                    "else " +
                    "   redis.call('zadd', KEYS[1], ARGV[1], ARGV[2]); " +
                    "end; ";
    public static final String HOUR = "3600";
    public static void updateCountKeys(int value, String redisKey) {
        // jedis连接池初始化
        try (Jedis jedis = jedisPool.getResource()) {
            // Lua 脚本：创建 今日统计数据并设置过期时间
            String todayCount = (String) jedis.eval(UPDATE_TODAY_COUNT, 1, redisKey, String.valueOf(value));
            log.info("alert update today {}", todayCount);
        } catch (Exception e) {
            log.info("alert update error {}", e.getMessage());
        }
    }

    public static boolean updateCountZet(Map<Integer, Integer> countMap) {
        LocalDateTime now = LocalDateTime.now();
        try (Jedis jedis = jedisPool.getResource()) {
            long timestamp = System.currentTimeMillis() / 1000;
            // Lua 脚本：创建 Sorted Set 并设置过期时间
            for (Map.Entry<Integer, Integer> count : countMap.entrySet()) {
                String member = now.getHour() + ":" + now.getMinute() + ":" + count.getValue();
                Integer type = count.getKey();
                switch (type) {
                    case DetectTypeConstant.IOC_ALERT_INFO:
                        jedis.eval(UPDATE_COUNT_ZET, 1, IOC_INPUT_COUNT_SET, String.valueOf((int) timestamp), member, HOUR);
                        break;
                    case DetectTypeConstant.IOA_ALERT_INFO:
                        jedis.eval(UPDATE_COUNT_ZET, 1, IOA_INPUT_COUNT_SET, String.valueOf((int) timestamp), member, HOUR);
                        break;
                    case DetectTypeConstant.FILE_ALERT_INFO:
                        jedis.eval(UPDATE_COUNT_ZET, 1, SANDBOX_INPUT_COUNT_SET, String.valueOf((int) timestamp), member, HOUR);
                        break;
                    case DetectTypeConstant.CRYPTO_ALERT_INFO:
                        jedis.eval(UPDATE_COUNT_ZET, 1, CRYPTO_INPUT_COUNT_SET, String.valueOf((int) timestamp), member, HOUR);
                        break;
                    case DetectTypeConstant.MAIL_ALERT_INFO:
                        jedis.eval(UPDATE_COUNT_ZET, 1, EMAIL_INPUT_COUNT_SET, String.valueOf((int) timestamp), member, HOUR);
                        break;
                    case DetectTypeConstant.FILTER_ALERT_INFO:
                        jedis.eval(UPDATE_COUNT_ZET, 1, FILTER_INPUT_COUNT_SET, String.valueOf((int) timestamp), member, HOUR);
                        break;
                    case DetectTypeConstant.IIOT_ALERT_INFO:
                        jedis.eval(UPDATE_COUNT_ZET, 1, IOT_INPUT_COUNT_SET, String.valueOf((int) timestamp), member, HOUR);
                        break;
                    default:
                        break;
                }
            }
            return true;
        } catch (Exception e) {
            log.info("zset update error {}", e.getMessage());
            return false;
        }
    }
}
