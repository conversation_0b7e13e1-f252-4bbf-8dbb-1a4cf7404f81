package com.geeksec.task;

import cn.hutool.core.date.DateUtil;
import com.geeksec.common.MyKeySerializer.MyKeySerializationSchema;
import com.geeksec.common.MyKeySerializer.MyValueSerializationSchema;
import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.common.constant.AlertLogDeserializer;
import com.geeksec.common.utils.IpMatcher;
import com.geeksec.entity.trans.InputCount;
import com.geeksec.proto.AlertLog;
import com.geeksec.proto.message.*;
import com.geeksec.transfer.function.aggr.AlertCountAggr;
import com.geeksec.transfer.function.aggr.AlertInputCountAggr;
import com.geeksec.transfer.function.process.*;
import com.geeksec.transfer.function.process.alertProcess.*;
import com.geeksec.transfer.function.process.ipProcess.IpFilterFunction;
import com.geeksec.transfer.function.process.prorocolProcess.ProtocolFilterFunction;
import com.geeksec.transfer.function.window.AlertAttackTimeWindowFunction;
import com.geeksec.transfer.selector.*;
import com.twitter.chill.protobuf.ProtobufSerializer;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.restartstrategy.RestartStrategies;
import org.apache.flink.api.common.time.Time;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.streaming.api.windowing.assigners.TumblingProcessingTimeWindows;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
public class PanoramaFakeAlertFilterTask {
    private static final Logger logger = LoggerFactory.getLogger(PanoramaFakeAlertFilterTask.class);

    // 子节点kafka地址
    public static String ALERT_KAFKA_BROKER_LIST;

    // kafka 子节点消费topic
    public static String ALERT_CONSUMER_TOPIC;

    // 汇聚节点kafka地址
    public static String FILTERED_KAFKA_BROKER_LIST = "";

    // 汇聚节点告警日志生产topic
    public static String FILTERED_KAFKA_PRODUCER_ALERT_TOPIC;

    // kafka消费组
    public static String GROUP_ID;

    // 过滤算子并行度
    public static Integer ALERT_FILTER_PARALLELISM;

    // 聚合算子并行度
    public static Integer ALERT_AGGR_PARALLELISM;

    // 日志窗口聚合时间(分)
    public static Integer WINDOW_MINUTES;

    // 日志窗口统计时间(分)
    public static Integer WINDOW_COUNT_MINUTES;

    // 日志窗口聚合时间(秒)
    public static Integer WINDOW_SECONDS;

    public static final String IOC_KEY = "IOC";
    public static final String IOA_KEY = "IOA";
    public static final String EMAIL_KEY = "EMAIL";
    public static final String FILE_KEY = "FILE";
    public static final String CRYPTO_KEY = "CRYPTO";
    public static final String IOT_KEY = "IOT";


    public static void main(String[] args) throws Exception {
        // 通过外部配置文件进行配置设置
        ParameterTool parameterTool = ParameterTool.fromArgs(args);
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        String configPath = parameterTool.get("config_path");

        // 从外部配置文件加载配置
        ParameterTool config = ParameterTool.fromPropertiesFile(configPath);
        ALERT_KAFKA_BROKER_LIST = config.get("kafka.bootstrap.server.alert");
        ALERT_CONSUMER_TOPIC = config.get("kafka.topic.alert");
        FILTERED_KAFKA_BROKER_LIST = config.get("kafka.bootstrap.server.filter");
        FILTERED_KAFKA_PRODUCER_ALERT_TOPIC = config.get("kafka.topic.filtered");
        GROUP_ID = config.get("kafka.group.id.alert");
        ALERT_FILTER_PARALLELISM = config.getInt("flink.alert.filter.parallelism");
        ALERT_AGGR_PARALLELISM = config.getInt("flink.alert.aggr.parallelism");
        WINDOW_MINUTES = config.getInt("flink.window.minutes");
        WINDOW_SECONDS = config.getInt("flink.window.seconds");
        WINDOW_COUNT_MINUTES = config.getInt("flink.window.count.minutes");

        // 将外部配置文件导入到全局参数配置
        env.getConfig().setGlobalJobParameters(config);

        // 初始化IP匹配器与Redis连接信息
        IpMatcher.init(config);
        logger.info("Starting init sub node handle fake alert info transfer to kafka task...");

        // 注册对应PB解析器(新版日志) 10种告警日志
        env.getConfig().registerTypeWithKryoSerializer(AlertLog.ALERT_LOG.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IocAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IobAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IoaAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(IiotAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(FileAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(CryptoAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(CertAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(MailAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(MobileAlertInfo.class, ProtobufSerializer.class);
        env.getConfig().registerTypeWithKryoSerializer(ProtoAlertInfo.class,ProtobufSerializer.class);

        env.setRestartStrategy(RestartStrategies.failureRateRestart(
                //最大失败次数
                3,
                // 衡量失败次数的是时间段
                Time.of(3, TimeUnit.SECONDS),
                // 间隔
                Time.of(10, TimeUnit.SECONDS)
        ));

        // 设置链式任务和重置时间点
        env.disableOperatorChaining();

        // 1.创建子节点消费者Kafka配置 & 生产者Kafka配置

        Properties producerKafkaProperties = new Properties();
        producerKafkaProperties.put("bootstrap.servers", FILTERED_KAFKA_BROKER_LIST);

        // 2.开始获取Kafka中的 Alert_log的信息
        KafkaSource<AlertLog.ALERT_LOG> kafkaSource = KafkaSource.<AlertLog.ALERT_LOG>builder()
                .setBootstrapServers(ALERT_KAFKA_BROKER_LIST)
                .setTopics(ALERT_CONSUMER_TOPIC)
                .setGroupId(GROUP_ID)
                .setStartingOffsets(OffsetsInitializer.latest())
                .setDeserializer(new com.geeksec.common.deserializer.AlertLogDeserializer())
                .build();
        DataStream<AlertLog.ALERT_LOG> sourceStream = env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka Source").setParallelism(1).name("读取Kafka中告警日志信息");

        // 统计接收数据
        SingleOutputStreamOperator<Integer> countDataStream = sourceStream
                .keyBy(value -> 1)
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.minutes(WINDOW_COUNT_MINUTES)))
                .aggregate(new AlertInputCountAggr(), new AlertCountAllWindowFunction())
                .name("统计接收数据").setParallelism(1);

        // 3.对日志中的IP字段进行过滤
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> ipFilteredStream = sourceStream
                .filter(Objects::nonNull).name("空告警日志过滤")
                .process(new IpFilterFunction()).name("IP CIDR & 规则研判过滤").setParallelism(ALERT_FILTER_PARALLELISM);

        DataStream<AlertLog.ALERT_LOG> ipFilterSideOutPutStream = ipFilteredStream.getSideOutput(FilterOutPutTagConstant.IP_FILTER_CONTINUE);

        // IP过滤掉的数据
        DataStream<AlertLog.ALERT_LOG> ipFilteredOutStream = ipFilteredStream.getSideOutput(FilterOutPutTagConstant.IP_FILTER_TERMINATE);

        // 根据不同协议进行过滤，所有的告警类型都有这个步骤
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> protocolFilteredStream = ipFilterSideOutPutStream.process(new ProtocolFilterFunction())
                .name("根据不同的协议类型提取信息进行过滤").setParallelism(ALERT_FILTER_PARALLELISM);

        // 根据不同协议保留的数据
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> protocolFilterSideOutPutStream = protocolFilteredStream.getSideOutput(FilterOutPutTagConstant.PROTOCOL_FILTER_CONTINUE)
                .process(new AlertLogSplitProcessFunction()).name("多类型告警日志分流处理").setParallelism(2);;

        // 根据不同协议过滤掉的数据
        DataStream<AlertLog.ALERT_LOG> protocolFilteredOutStream = ipFilteredStream.getSideOutput(FilterOutPutTagConstant.PROTOCOL_FILTER_TERMINATE);

        // TODO 统计过滤的条数和通过的条数，窗口函数+入库
        // 通过的
        protocolFilteredStream.getSideOutput(FilterOutPutTagConstant.PROTOCOL_FILTER_CONTINUE);

        // 3.分流不同类型告警日志
        DataStream<AlertLog.ALERT_LOG> alarmIocStream = protocolFilterSideOutPutStream.getSideOutput(AlertLogDeserializer.IOC_ALERT_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmIoaX509Stream = protocolFilterSideOutPutStream.getSideOutput(AlertLogDeserializer.IOA_ALERT_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmSandboxStream = protocolFilterSideOutPutStream.getSideOutput(AlertLogDeserializer.SANDBOX_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmCryptoStream = protocolFilterSideOutPutStream.getSideOutput(AlertLogDeserializer.CRYPTO_ALERT_INFO_OUTPUT_TAG);
        DataStream<AlertLog.ALERT_LOG> alarmEmailStream = protocolFilterSideOutPutStream.getSideOutput(AlertLogDeserializer.EMAIL_ALERT_INFO_OUTPUT_TAG);
        // TODO IOT告警过滤
        DataStream<AlertLog.ALERT_LOG> alarmIotStream = protocolFilterSideOutPutStream.getSideOutput(AlertLogDeserializer.IOT_ALERT_INFO_OUTPUT_TAG);

        // 4.将每个类型的告警日志分别进行虚警研判与过滤
        // ioc
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> iocFilteredStream = alarmIocStream
                .process(new AlertIocFilterFunction())
                .setParallelism(ALERT_FILTER_PARALLELISM).name("IOC协议告警日志研判过滤");

        DataStream<AlertLog.ALERT_LOG> iocFilteredStreamContinue = iocFilteredStream.getSideOutput(FilterOutPutTagConstant.IOC_FILTER_CONTINUE);
        DataStream<AlertLog.ALERT_LOG> iocFilteredStreamTerminate = iocFilteredStream.getSideOutput(FilterOutPutTagConstant.IOC_FILTER_TERMINATE);

        SingleOutputStreamOperator<AlertLog.ALERT_LOG> iocAggStreamContinue = iocFilteredStreamContinue.keyBy(new IocTransKeySelector())
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.seconds(WINDOW_SECONDS)))
                .process(new AlertAttackTimeWindowFunction()).name("IOC告警日志研判聚合")
                .setParallelism(ALERT_AGGR_PARALLELISM);

        // ioa
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> ioaFilteredStream = alarmIoaX509Stream
                .process(new AlertIoaFilterFunction())
                .setParallelism(ALERT_FILTER_PARALLELISM).name("IOA协议告警日志研判过滤");

        DataStream<AlertLog.ALERT_LOG> ioaFilteredStreamContinue = ioaFilteredStream.getSideOutput(FilterOutPutTagConstant.IOA_FILTER_CONTINUE);
        DataStream<AlertLog.ALERT_LOG> ioaFilteredStreamTerminate = ioaFilteredStream.getSideOutput(FilterOutPutTagConstant.IOA_FILTER_TERMINATE);

        SingleOutputStreamOperator<AlertLog.ALERT_LOG> ioaAggStreamContinue = ioaFilteredStreamContinue.keyBy(new IoaTransKeySelector())
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.seconds(WINDOW_SECONDS)))
                .process(new AlertAttackTimeWindowFunction()).name("IOA协议告警日志研判聚合")
                .setParallelism(ALERT_AGGR_PARALLELISM);

        // 沙箱
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> sandBoxFilteredStream = alarmSandboxStream
                .process(new AlertSandBoxFilterFunction())
                .setParallelism(ALERT_FILTER_PARALLELISM).name("沙盒告警元数据研判过滤");

        DataStream<AlertLog.ALERT_LOG> sandboxFilteredStreamContinue = sandBoxFilteredStream.getSideOutput(FilterOutPutTagConstant.SANDBOX_FILTER_CONTINUE);
        DataStream<AlertLog.ALERT_LOG> sandboxFilteredStreamTerminate = sandBoxFilteredStream.getSideOutput(FilterOutPutTagConstant.SANDBOX_FILTER_TERMINATE);

        SingleOutputStreamOperator<AlertLog.ALERT_LOG> sandboxAggStreamContinue = sandboxFilteredStreamContinue.keyBy(new SandBoxTransKeySelector())
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.seconds(WINDOW_SECONDS)))
                .process(new AlertAttackTimeWindowFunction()).name("沙盒告警日志研判聚合")
                .setParallelism(ALERT_AGGR_PARALLELISM);

        // 邮件
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> emailFilteredStream = alarmEmailStream
                .process(new AlertEmailFilterFunction())
                .setParallelism(ALERT_FILTER_PARALLELISM).name("邮件告警元数据研判过滤");

        DataStream<AlertLog.ALERT_LOG> emailFilteredStreamContinue = emailFilteredStream.getSideOutput(FilterOutPutTagConstant.EMAIL_FILTER_CONTINUE);
        DataStream<AlertLog.ALERT_LOG> emailFilteredStreamTerminate = emailFilteredStream.getSideOutput(FilterOutPutTagConstant.EMAIL_FILTER_TERMINATE);

        SingleOutputStreamOperator<AlertLog.ALERT_LOG> emailAggStreamContinue = emailFilteredStreamContinue.keyBy(new EmailTransKeySelector())
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.seconds(WINDOW_SECONDS)))
                .process(new AlertAttackTimeWindowFunction()).name("邮件告警日志研判聚合")
                .setParallelism(ALERT_AGGR_PARALLELISM);

        // 加密流量
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> cryptoFilteredStream = alarmCryptoStream
                .process(new AlertCryptoFilterFunction())
                .setParallelism(ALERT_FILTER_PARALLELISM).name("密数据告警元数据研判过滤");

        DataStream<AlertLog.ALERT_LOG> cryptoFilteredStreamContinue = cryptoFilteredStream.getSideOutput(FilterOutPutTagConstant.CRYPTO_FILTER_CONTINUE);
        DataStream<AlertLog.ALERT_LOG> cryptoFilteredStreamTerminate = cryptoFilteredStream.getSideOutput(FilterOutPutTagConstant.CRYPTO_FILTER_TERMINATE);

        SingleOutputStreamOperator<AlertLog.ALERT_LOG> cryptoAggStreamContinue = cryptoFilteredStreamContinue.keyBy(new CryptoTransKeySelector())
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.seconds(WINDOW_SECONDS)))
                .process(new AlertAttackTimeWindowFunction()).name("密数据告警日志研判聚合")
                .setParallelism(ALERT_AGGR_PARALLELISM);

        // TODO IOT过滤
        // IOT工控
        SingleOutputStreamOperator<AlertLog.ALERT_LOG> iotFilteredStream = alarmIotStream
                .process(new AlertIotFilterFunction())
                .setParallelism(ALERT_FILTER_PARALLELISM).name("工控告警元数据研判过滤");

        DataStream<AlertLog.ALERT_LOG> iotFilteredStreamContinue = iotFilteredStream.getSideOutput(FilterOutPutTagConstant.IOT_FILTER_CONTINUE);
        DataStream<AlertLog.ALERT_LOG> iotFilteredStreamTerminate = iotFilteredStream.getSideOutput(FilterOutPutTagConstant.IOT_FILTER_TERMINATE);

        SingleOutputStreamOperator<AlertLog.ALERT_LOG> iotAggStreamContinue = iotFilteredStreamContinue.keyBy(new IotTransKeySelector())
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.seconds(WINDOW_SECONDS)))
                .process(new AlertAttackTimeWindowFunction()).name("工控告警日志研判聚合")
                .setParallelism(ALERT_AGGR_PARALLELISM);


        // 所有类型的告警的过滤数量 TODO 合并入库，统计过滤数量
        iocFilteredStreamTerminate.union(ioaFilteredStreamTerminate, cryptoFilteredStreamTerminate,
                sandboxFilteredStreamTerminate, emailFilteredStreamTerminate, iotFilteredStreamTerminate);

//         4.将每个类型的告警日志分别进行虚警研判与过滤
        DataStream<AlertLog.ALERT_LOG> allAggStream = iocAggStreamContinue
                .union(sandboxAggStreamContinue, emailAggStreamContinue,
                        ioaAggStreamContinue, cryptoAggStreamContinue, iotAggStreamContinue);

        // 5.合并所有过滤后的告警日志后推入目的kafka中
        KafkaSink<byte[]> kafkaAlertSink = KafkaSink.<byte[]>builder()
                .setBootstrapServers(producerKafkaProperties.getProperty("bootstrap.servers"))
                .setRecordSerializer(KafkaRecordSerializationSchema.<byte[]>builder()
                        .setTopic(FILTERED_KAFKA_PRODUCER_ALERT_TOPIC)
                        .setKeySerializationSchema(new MyKeySerializationSchema())
                        .setValueSerializationSchema(new MyValueSerializationSchema())
                        .build()
                )
                .build();
        // 合并所有被过滤掉的数据进行统计
        ipFilteredOutStream.union(protocolFilteredOutStream, iocFilteredStreamTerminate,
                        ioaFilteredStreamTerminate, cryptoFilteredStreamTerminate, sandboxFilteredStreamTerminate,
                        emailFilteredStreamTerminate, iotFilteredStreamTerminate,
                        iocAggStreamContinue.getSideOutput(FilterOutPutTagConstant.ALERT_LOG_AGGR),
                        ioaAggStreamContinue.getSideOutput(FilterOutPutTagConstant.ALERT_LOG_AGGR),
                        sandboxAggStreamContinue.getSideOutput(FilterOutPutTagConstant.ALERT_LOG_AGGR),
                        emailAggStreamContinue.getSideOutput(FilterOutPutTagConstant.ALERT_LOG_AGGR),
                        iotAggStreamContinue.getSideOutput(FilterOutPutTagConstant.ALERT_LOG_AGGR),
                        cryptoAggStreamContinue.getSideOutput(FilterOutPutTagConstant.ALERT_LOG_AGGR))
                .keyBy(value -> 1)
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.minutes(WINDOW_COUNT_MINUTES)))
                .aggregate(new AlertCountAggr(), new AlertFilterCountProcess())
                .name("统计过滤数量").setParallelism(1);

        countDataStream.keyBy(value -> 1)
                .window(TumblingProcessingTimeWindows.of(org.apache.flink.streaming.api.windowing.time.Time.days(1)))
                .process(new AlertCountDaylyProces())
                .name("更新每日和昨日统计数据").setParallelism(1);

        DataStream<byte[]> alertLogStream = allAggStream.process(new ProcessFunction<AlertLog.ALERT_LOG, byte[]>() {
            @Override
            public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, byte[]>.Context context, Collector<byte[]> collector) throws Exception {
                if (alertLog != null) {
                    collector.collect(alertLog.toByteArray());
                }
            }
        }).setParallelism(4).name("将过滤后的日志信息推送至汇聚Kafka中");
        alertLogStream.sinkTo(kafkaAlertSink).name("ETL FILTERED TO FILTER KAFAK TOPIC ").setParallelism(4);

        // 打印当前时间
        logger.info("Sub node alert handel fake alert transfer to kafka task start! now -> {} ", DateUtil.now());
        env.execute("ETL-FAKE-ALERT-FILTER-TASK");
    }

}
