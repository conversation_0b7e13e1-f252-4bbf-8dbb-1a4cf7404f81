package com.geeksec.transfer.selector;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple4;

import static com.geeksec.task.PanoramaFakeAlertFilterTask.IOC_KEY;

/**
 * <AUTHOR>
 */
public class IocTransKeySelector implements KeySelector<AlertLog.ALERT_LOG, Tuple4<String, String, String, String>> {

    /**
     * 使用攻击方-受害方-IOC_ID作为Key
     * @param alertLog 告警日志
     * @return Tuple3<String, String, String> 攻击方IP，受害者IP，IOC_ID
     */
    @Override
    public Tuple4<String, String, String, String> getKey(AlertLog.ALERT_LOG alertLog) throws Exception {
        String vipAddr = alertLog.getVip().getIp();
        String aipAddr = alertLog.getAip().getIp();
        String iocId = alertLog.getIocAlertInfo().getIocId();
        return new Tuple4<>(IOC_KEY,aipAddr,vipAddr,iocId);
    }
}
