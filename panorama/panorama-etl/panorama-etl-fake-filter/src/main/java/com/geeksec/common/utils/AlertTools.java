package com.geeksec.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.geeksec.entity.pojo.DNSAlertTrans;
import com.geeksec.entity.pojo.HTTPAlertTrans;
import com.geeksec.entity.pojo.SslAlertTrans;
import com.geeksec.entity.pojo.X509AlertTrans;
import com.geeksec.entity.trans.DNSTrans;
import com.geeksec.entity.trans.HTTPTrans;
import com.geeksec.entity.trans.SslTrans;
import com.geeksec.entity.trans.X509CertTrans;
import com.geeksec.enums.AppIdEnum;
import com.geeksec.proto.AlertLog;
import com.geeksec.proto.ProtocolMetadata;
import com.geeksec.proto.message.IocAlertInfo;
import com.geeksec.proto.protocol.*;
import com.geeksec.transfer.handle.IPTransHandler;
import com.google.protobuf.*;
import io.rebloom.client.Client;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.geeksec.transfer.function.process.prorocolProcess.ProtocolFilterFunction.*;

/**
 * <AUTHOR>
 */
public class AlertTools {

    public static final Logger log = LoggerFactory.getLogger(AlertTools.class);

    public static final String HTTP_TYPE_URL = HttpInfoOuterClass.getDescriptor().getFullName().split("\\.")[0];
    public static final String DNS_TYPE_URL = DnsInfoOuterClass.getDescriptor().getName().split("\\.")[0];
    public static final String SSL_TYPE_URL = SslTlsInfo.getDescriptor().getName().split("\\.")[0];
    public static final String X509_TYPE_URL = X509CerInfoOuterClass.getDescriptor().getName().split("\\.")[0];
    public static final String LINK_TYPE_URL = LinkInfoOuterClass.getDescriptor().getName().split("\\.")[0];
    public static final String COMMON_TYPE_URL = CommonInfoOuterClass.getDescriptor().getName().split("\\.")[0];
    public static final String EMAIL_TYPE_URL = EMAILInfoOuterClass.getDescriptor().getName().split("\\.")[0];

    /**
     * 将告警时间字符串转换为long类型的时间戳。
     * 时间字符串格式为：yyyy-mm-dd hh:mm:ss.ms
     *
     * @param timeStr 告警时间字符串
     * @return 转换后的时间戳，如果解析失败返回-1
     */
    public static long parseTimeToLong(String timeStr) {
        // 定义时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        try {
            // 解析时间字符串
            LocalDateTime localDateTime = LocalDateTime.parse(timeStr, formatter);
            // 转换为毫秒级时间戳
            return localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli();
        } catch (DateTimeParseException e) {
            // 如果解析失败，记录错误并返回-1
            log.error("解析时间字符串失败: {}", timeStr, e);
            return -1;
        }
    }

    public static List<String> convertByteStringListToStringList(List<ByteString> byteStringList) {
        List<String> stringList = new ArrayList<>();
        for (ByteString byteString : byteStringList) {
            // 将ByteString转换为String
            String string = byteString.toStringUtf8();
            stringList.add(string);
        }
        return stringList;
    }


    // 辅助方法
    public static void addIfNotEmpty(List<String> list, String value) {
        if (value != null && !value.trim().isEmpty()) {
            list.add(value);
        }
    }

    public static DNSAlertTrans createDnsAlertTrans(AlertLog.ALERT_LOG alertLog) {
        ByteString metaInfoBytes = alertLog.getMetaData();
        if (metaInfoBytes.isEmpty()) {
            return null;
        }
        try{
            IocAlertInfo.IOC_ALERT_INFO iocAlertInfo = alertLog.getIocAlertInfo();

            DNSAlertTrans dnsAlert = new DNSAlertTrans();
            dnsAlert.setAttackId(alertLog.getGuid());
            dnsAlert.setAttackTime(AlertTools.parseTimeToLong(alertLog.getTime()) / MILLISECONDS_TO_SECONDS);
            dnsAlert.setAttackTypeCode(alertLog.getThreatType());
            dnsAlert.setKillChain(alertLog.getKillChain());
            dnsAlert.setIocAlertInfo(iocAlertInfo);

            setDnsIpInfo(dnsAlert, alertLog);
            setDnsServerAddr(dnsAlert, alertLog);
            processDnsMetaInfo(dnsAlert, alertLog.getMetaData(), alertLog.getDetectType());

            return dnsAlert;
        }catch (Exception e) {
            log.error("Error creating DNSAlertTrans: {}", alertLog.getGuid(), e);
            return null;
        }
    }

    public static void processDnsMetaInfo(DNSAlertTrans dnsAlert, ByteString metaInfoBytes, Integer detectType) {
        if (metaInfoBytes.isEmpty()) {
            return;
        }
        try {
            ProtocolMetadata.MetaInfo metaInfo = ProtocolMetadata.MetaInfo.parseFrom(metaInfoBytes.toByteArray());
            List<ProtocolMetadata.ProtocolInfo> protocolInfoList = metaInfo.getProtocolInfoList();
            List<DNSTrans> dnsAlertTransList = new ArrayList<>();
            for (ProtocolMetadata.ProtocolInfo protocolInfo : protocolInfoList){
                int protocolType = protocolInfo.getType();
                if (protocolType == AppIdEnum.DNS.getCode()) {
                    Any any = protocolInfo.getProtocolMeta();
                    // 先默认使用unPack的方式进行解析，再是parseFrom
                    try{
                        byte[] metaBytes = any.getValue().toByteArray();
                        String metaType = any.getTypeUrl().split("/")[1];
                        DnsInfoOuterClass.DnsInfo dnsInfoUnpack = null;
                        if(metaType.equals(DNS_TYPE_URL)){
                            dnsInfoUnpack = any.unpack(DnsInfoOuterClass.DnsInfo.class);
                        }else if(metaType.contains(DNS_TYPE_URL)){
                            dnsInfoUnpack = DnsInfoOuterClass.DnsInfo.parseFrom(metaBytes);
                        }
                        dnsAlertTransList.add(getDnsTrans(dnsInfoUnpack));
                    }catch (Exception e){
                        log.warn("<{}>类型元数据未使用pack方式打包 DNS 协议元数据,直接使用ParseFrom进行解析",detectType);
                        DnsInfoOuterClass.DnsInfo dnsInfoParseFrom = DnsInfoOuterClass.DnsInfo.parseFrom(protocolInfo.getProtocolMeta().toByteArray());
                        dnsAlertTransList.add(getDnsTrans(dnsInfoParseFrom));
                    }
                }
            }
            dnsAlert.setDns(dnsAlertTransList);
        } catch (InvalidProtocolBufferException e) {
            log.error("Error parsing DNS meta info", e);
        }
    }
    public static void setDnsIpInfo(DNSAlertTrans dnsAlert, AlertLog.ALERT_LOG alertLog) {
        dnsAlert.setSip(IPTransHandler.transIP(alertLog.getSip()));
        dnsAlert.setDip(IPTransHandler.transIP(alertLog.getDip()));
        dnsAlert.setAipAddr(alertLog.getAip().getIp());
        dnsAlert.setVipAddr(alertLog.getVip().getIp());
    }

    public static void setDnsServerAddr(DNSAlertTrans dnsAlert, AlertLog.ALERT_LOG alertLog) {
        String dnsServerAddr = alertLog.getSip().getIp().equals(alertLog.getVip().getIp())
                ? alertLog.getDip().getIp()
                : alertLog.getSip().getIp();
        dnsAlert.setDnsServerAddr(dnsServerAddr);
    }

    public static DNSTrans getDnsTrans(DnsInfoOuterClass.DnsInfo dnsInfo) {
        DNSTrans dnsTrans = new DNSTrans();
        dnsTrans.setQuery(dnsInfo.getQueName().toStringUtf8());
        dnsTrans.setAnswerTypes(AlertTools.convertByteStringListToStringList(dnsInfo.getAnsTypesList()));
        dnsTrans.setCname(AlertTools.convertByteStringListToStringList(dnsInfo.getAnsCnameList()));
        dnsTrans.setAnswerCnt(dnsInfo.getAnsCnt());
        dnsTrans.setAuthCnt(dnsInfo.getAutCnt());
        dnsTrans.setFlags(dnsInfo.getSrvFlag());
        dnsTrans.setIpv6(AlertTools.convertByteStringListToStringList(dnsInfo.getAIpv6List()));
        dnsTrans.setAip(dnsInfo.getAipList());
        dnsTrans.setAipCnt(dnsInfo.getAipCnt());
        dnsTrans.setAipAsn(AlertTools.convertByteStringListToStringList(dnsInfo.getAipAsnList()));
        dnsTrans.setAipCountry(AlertTools.convertByteStringListToStringList(dnsInfo.getAipCountryList()));
        dnsTrans.setNsHost(AlertTools.convertByteStringListToStringList(dnsInfo.getNameSrvHostList()));
        dnsTrans.setNsHostCnt(dnsInfo.getNameSrvHostCnt());
        dnsTrans.setNsIpCnt(dnsInfo.getNsIpCnt());
        dnsTrans.setNsCountry(AlertTools.convertByteStringListToStringList(dnsInfo.getNameSrvCountryList()));
        dnsTrans.setNsIp(dnsInfo.getNsIpList());
        dnsTrans.setNsAsn(AlertTools.convertByteStringListToStringList(dnsInfo.getNameSrvAsnList()));
        dnsTrans.setMxHost(AlertTools.convertByteStringListToStringList(dnsInfo.getMailSrvHostList()));
        dnsTrans.setMxHostCnt(dnsInfo.getMailSrvHostCount());
        dnsTrans.setMxIp(AlertTools.convertByteStringListToStringList(dnsInfo.getMxIpAsnList()));
        dnsTrans.setMxIpCnt(dnsInfo.getMxIpAsnCount());
        dnsTrans.setMxAsn(AlertTools.convertByteStringListToStringList(dnsInfo.getMxIpAsnList()));
        dnsTrans.setMxCountry(AlertTools.convertByteStringListToStringList(dnsInfo.getMxIpCountryList()));
        dnsTrans.setAddCnt(dnsInfo.getAddCnt());
        dnsTrans.setAddAnsType(dnsInfo.getAddAnsType());
        dnsTrans.setAddAnsRes(dnsInfo.getAddAnsRes().toStringUtf8());
        dnsTrans.setAddRrs(dnsInfo.getAddRrs());
        dnsTrans.setQueType(dnsInfo.getQueType());
        return dnsTrans;
    }

    public static boolean dnsFakeAlertCheck(DNSAlertTrans dnsAlert, AlertLog.ALERT_LOG alertLog, Client bloomClient) {
        try {

            List<String> domainList = new ArrayList<>();
            List<DNSTrans> dnsTrans = dnsAlert.getDns();

            for (DNSTrans dns : dnsTrans){
                if (dns != null) {
                    AlertTools.addIfNotEmpty(domainList, dns.getQuery());
                    List<String> dnsCnameList = dns.getCname();
                    List<String> nsHostList = dns.getNsHost();
                    for (String cname : dnsCnameList){
                        AlertTools.addIfNotEmpty(domainList, cname);
                    }
                    for (String nsHost : nsHostList){
                        AlertTools.addIfNotEmpty(domainList, nsHost);
                    }
                }

                if (CollectionUtil.isEmpty(domainList)) {
                    return false;
                }
            }
            // 获取锚域名列表
            List<String> anchorDomainList = domainList.stream()
                    .map(suffixList::getRegistrableDomain)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 合并原始域名和锚域名
            List<String> allDomains = Stream.concat(domainList.stream(), anchorDomainList.stream())
                    .distinct()
                    .collect(Collectors.toList());

            // 使用 RedisBloom 的 exists 方法一次性检查多个域名
            for (String domain : allDomains) {
                if (bloomClient.exists(DOMAIN_BLOOM_FILTER_KEY, domain)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("Error checking for fake DNS alert: {}", alertLog.getGuid(), e);
            return false;
        }
    }

    public static HTTPAlertTrans createHttpAlertTrans(AlertLog.ALERT_LOG alertLog) {
        ByteString metaInfoBytes = alertLog.getMetaData();
        if (metaInfoBytes.isEmpty()) {
            return null;
        }

        try {
            IocAlertInfo.IOC_ALERT_INFO iocAlertInfo = alertLog.getIocAlertInfo();

            HTTPAlertTrans httpAlert = new HTTPAlertTrans();
            httpAlert.setAttackId(alertLog.getGuid());
            httpAlert.setAttackTime(AlertTools.parseTimeToLong(alertLog.getTime()) / MILLISECONDS_TO_SECONDS);
            httpAlert.setAttackTypeCode(alertLog.getThreatType());
            httpAlert.setKillChain(alertLog.getKillChain());
            httpAlert.setIocAlertInfo(iocAlertInfo);
            httpAlert.setSip(IPTransHandler.transIP(alertLog.getSip()));
            httpAlert.setDip(IPTransHandler.transIP(alertLog.getDip()));
            httpAlert.setAipAddr(alertLog.getAip().getIp());
            httpAlert.setVipAddr(alertLog.getVip().getIp());

            processHttpMetaInfo(httpAlert, alertLog.getMetaData(), alertLog.getDetectType());

            return httpAlert;
        } catch (InvalidProtocolBufferException e) {
            log.error("Error creating HTTPAlertTrans: {}", alertLog.getGuid(), e);
            return null;
        }
    }

    public static void processHttpMetaInfo(HTTPAlertTrans httpAlert, ByteString metaInfoBytes, Integer detectType) throws InvalidProtocolBufferException {
        if (metaInfoBytes.isEmpty()) {
            return;
        }
        ProtocolMetadata.MetaInfo metaInfo = ProtocolMetadata.MetaInfo.parseFrom(metaInfoBytes.toByteArray());
        List<ProtocolMetadata.ProtocolInfo> protocolInfoList = metaInfo.getProtocolInfoList();
        List<HTTPTrans> httpAlertTransList = new ArrayList<>();
        for (ProtocolMetadata.ProtocolInfo protocolInfo : protocolInfoList){
            int protocolType = protocolInfo.getType();
            if (protocolType == AppIdEnum.HTTP.getCode()) {
                Any any = protocolInfo.getProtocolMeta();
                // 先默认使用unPack的方式进行解析，再是parseFrom
                try{
                    byte[] metaBytes = any.getValue().toByteArray();
                    String metaType = any.getTypeUrl().split("/")[1];
                    HttpInfoOuterClass.HttpInfo httpInfoUnpack = null;
                    if(metaType.equals(HTTP_TYPE_URL)){
                        httpInfoUnpack = any.unpack(HttpInfoOuterClass.HttpInfo.class);
                    }else if(metaType.contains(HTTP_TYPE_URL)){
                        httpInfoUnpack = HttpInfoOuterClass.HttpInfo.parseFrom(metaBytes);
                    }
                    httpAlertTransList.add(getHttpTrans(httpInfoUnpack));
                }catch (Exception e){
                    log.warn("<{}>类型元数据未使用pack方式打包 HTTP 协议元数据,直接使用ParseFrom进行解析",detectType);
                    HttpInfoOuterClass.HttpInfo httpInfoParseFrom = HttpInfoOuterClass.HttpInfo.parseFrom(protocolInfo.getProtocolMeta().toByteArray());
                    httpAlertTransList.add(getHttpTrans(httpInfoParseFrom));
                }
            }
        }
        httpAlert.setHttp(httpAlertTransList);
    }

    public static boolean httpFakeAlertCheck(HTTPAlertTrans httpAlertTrans, AlertLog.ALERT_LOG alertLog, Client bloomClient) {
        try {

            // 先进行特征规则过滤
            boolean fakeAlarm = false;
            // 攻击类型代码集合
            Set<Long> statusCodeCheck = new HashSet<>();
            statusCodeCheck.add(0x0401L);
            statusCodeCheck.add(0x0409L);
            statusCodeCheck.add(0x040bL);
            statusCodeCheck.add(0x040cL);
            statusCodeCheck.add(0x0414L);

            long attackTypeCode = httpAlertTrans.getAttackTypeCode();
            List<HTTPTrans> httpTransList = httpAlertTrans.getHttp();

            List<String> domainList = new ArrayList<>();
            for (HTTPTrans httpTrans:httpTransList){
                String uri = httpTrans.getUri();
                String method = httpTrans.getMethod().toLowerCase();
                long statusCode = httpTrans.getStatus();
                String agent = httpTrans.getAgent().toLowerCase();

                if (attackTypeCode == 0x0401L) {
                    // SQL注入过滤
                    fakeAlarm =  isSqlInjectionFakeAlarm(method, statusCode, agent, uri);
                } else if (statusCodeCheck.contains(attackTypeCode)) {
                    // 通用过滤策略
                    fakeAlarm = statusCode != 200;
                }

                // 如果特征规则检测出是虚警，则进行过滤
                if (fakeAlarm){
                    return false;
                }
                String domainAddr = httpTrans.getHost();
                AlertTools.addIfNotEmpty(domainList, domainAddr);
            }

            // 获取锚域名列表
            List<String> anchorDomainList = domainList.stream()
                    .map(suffixList::getRegistrableDomain)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 合并原始域名和锚域名
            List<String> allDomains = Stream.concat(domainList.stream(), anchorDomainList.stream())
                    .distinct()
                    .collect(Collectors.toList());

            // 使用 RedisBloom 的 exists 方法一次性检查多个域名
            for (String domain :allDomains){
                if (bloomClient.exists(DOMAIN_BLOOM_FILTER_KEY,domain)){
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("Error checking for fake HTTP alert: {}", alertLog.getGuid(), e);
            return false;
        }
    }

    public static HTTPTrans getHttpTrans(HttpInfoOuterClass.HttpInfo httpInfo) {
        HTTPTrans httpTrans = new HTTPTrans();
        httpTrans.setMethod(httpInfo.getMet().toStringUtf8());
        httpTrans.setStatus(httpInfo.getStatCode());
        httpTrans.setHost(httpInfo.getHost().toStringUtf8().toLowerCase());
        httpTrans.setUri(httpInfo.getUri().toStringUtf8());
        httpTrans.setUriKey(AlertTools.convertByteStringListToStringList(httpInfo.getUriKeyList()));
        httpTrans.setUriPath(httpInfo.getUriPath().toStringUtf8());
        httpTrans.setAgent(httpInfo.getUsrAge().toStringUtf8());
        httpTrans.setAcceptLanguage(httpInfo.getAccLanByCli().toStringUtf8());
        httpTrans.setContentTypeReq(httpInfo.getConType().toStringUtf8());
        httpTrans.setContentTypeResp(httpInfo.getConTypDown().toStringUtf8());
        httpTrans.setCookie(httpInfo.getCookie().toStringUtf8());
        httpTrans.setSetCookieKey(httpInfo.getSetCookieKey().toStringUtf8());
        httpTrans.setSetCookieVal(httpInfo.getSetCookieVal().toStringUtf8());
        httpTrans.setReferer(httpInfo.getRefURL().toStringUtf8());
        httpTrans.setXffIp(httpInfo.getXForFor().toStringUtf8());
        httpTrans.setReqbody(httpInfo.getReqBody().toStringUtf8());
        httpTrans.setRespbody(httpInfo.getRespBody().toStringUtf8());
        httpTrans.setContentType(httpInfo.getConType().toStringUtf8());
        return httpTrans;
    }

    /**
     * SQL注入类型虚警判断
     *
     * @param method
     * @param statusCode
     * @param agent
     * @param uri
     * @return
     */
    public static boolean isSqlInjectionFakeAlarm(String method, long statusCode, String agent, String uri) {
        if ("post".equals(method) || statusCode != 200 || agent.contains("spider")) {
            return true;
        }
        if (uri.contains("?")) {
            String requestPath = uri.split("\\?")[0].toLowerCase();
            return !(requestPath.contains(".php") || requestPath.contains(".asp") || requestPath.contains(".aspx")
                    || requestPath.contains(".jsp") || requestPath.contains("perl") || requestPath.contains("cgi"));
        }
        return true;
    }
    

    public static X509AlertTrans createX509AlertTrans(AlertLog.ALERT_LOG alertLog) {
        ByteString metaInfoBytes = alertLog.getMetaData();
        if (metaInfoBytes.isEmpty()) {
            return null;
        }
        try{
            X509AlertTrans x509Alert = new X509AlertTrans();
            x509Alert.setAttackId(alertLog.getGuid());
            x509Alert.setAttackTime(AlertTools.parseTimeToLong(alertLog.getTime()) / MILLISECONDS_TO_SECONDS);
            x509Alert.setAttackTypeCode(alertLog.getThreatType());
            x509Alert.setKillChain(alertLog.getKillChain());
            x509Alert.setIocAlertInfo(alertLog.getIocAlertInfo());
            x509Alert.setSip(IPTransHandler.transIP(alertLog.getSip()));
            x509Alert.setDip(IPTransHandler.transIP(alertLog.getDip()));
            x509Alert.setAipAddr(alertLog.getAip().getIp());
            x509Alert.setVipAddr(alertLog.getVip().getIp());

            processX509MetaInfo(x509Alert, alertLog.getMetaData(), alertLog.getDetectType());

            return x509Alert;
        } catch (InvalidProtocolBufferException e) {
            log.error("Error creating X509AlertTrans: {}", alertLog.getGuid(), e);
            return null;
        }
    }

    public static void processX509MetaInfo(X509AlertTrans x509Alert, ByteString metaInfoBytes, Integer detectType) throws InvalidProtocolBufferException {
        if (metaInfoBytes.isEmpty()) {
            return;
        }
        ProtocolMetadata.MetaInfo metaInfo = ProtocolMetadata.MetaInfo.parseFrom(metaInfoBytes.toByteArray());
        List<ProtocolMetadata.ProtocolInfo> protocolInfoList = metaInfo.getProtocolInfoList();
        List<X509CertTrans> x509CertTransList = new ArrayList<>();
        for (ProtocolMetadata.ProtocolInfo protocolInfo:protocolInfoList){
            int protocolType = protocolInfo.getType();
            if (protocolType == AppIdEnum.X509_CER.getCode()) {
                Any any = protocolInfo.getProtocolMeta();
                // 先默认使用unPack的方式进行解析，再是parseFrom
                try{
                    byte[] metaBytes = any.getValue().toByteArray();
                    String metaType = any.getTypeUrl().split("/")[1];
                    X509CerInfoOuterClass.X509CerInfo x509InfoUnpack = null;
                    if(metaType.equals(X509_TYPE_URL)){
                        x509InfoUnpack = any.unpack(X509CerInfoOuterClass.X509CerInfo.class);
                    }else if(metaType.contains(X509_TYPE_URL)){
                        x509InfoUnpack = X509CerInfoOuterClass.X509CerInfo.parseFrom(metaBytes);
                    }
                    x509CertTransList.add(getX509CertTrans(x509InfoUnpack));
                }catch (Exception e){
                    log.warn("<{}>类型元数据未使用pack方式打包 X509 协议元数据,直接使用ParseFrom进行解析",detectType);
                    X509CerInfoOuterClass.X509CerInfo x509InfoParseFrom = X509CerInfoOuterClass.X509CerInfo.parseFrom(protocolInfo.getProtocolMeta().toByteArray());
                    x509CertTransList.add(getX509CertTrans(x509InfoParseFrom));
                }
            }
        }
        x509Alert.setX509Cert(x509CertTransList);
    }

    public static boolean x509CheckFakeAlarm(X509AlertTrans x509Alert, AlertLog.ALERT_LOG alertLog, Client bloomClient) {
        try {
            List<X509CertTrans> x509CertTransList = x509Alert.getX509Cert();
            List<String> x509CertFingerPrintList = new ArrayList<>();
            for (X509CertTrans x509CertTrans:x509CertTransList){
                String hash = x509CertTrans.getFingerPrint();
                AlertTools.addIfNotEmpty(x509CertFingerPrintList,hash);
            }

            for (String hash :x509CertFingerPrintList){
                if (bloomClient.exists(CERT_HASH_BLOOM_FILTER_KEY,hash)){
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("Error checking for fake X509 alert: {}", alertLog.getGuid(), e);
            return false;
        }
    }

    public static X509CertTrans getX509CertTrans(X509CerInfoOuterClass.X509CerInfo x509Info){
        X509CertTrans x509CertTrans = new X509CertTrans();
        x509CertTrans.setVersion(x509Info.getVer());
        // TODO 不知道公钥算法是哪个字段
        x509CertTrans.setAlgorithmId(x509Info.getKeyPur().toStringUtf8());
        x509CertTrans.setIssuerCn(x509Info.getIssComName().toStringUtf8());
        x509CertTrans.setIssuerOn(x509Info.getIssOrgName().toStringUtf8());
        x509CertTrans.setIssuerOu(x509Info.getIssOrgUniName().toStringUtf8());
        x509CertTrans.setSubjectCn(x509Info.getSubComName().toStringUtf8());
        x509CertTrans.setSubjectOn(x509Info.getSubOrgName().toStringUtf8());
        x509CertTrans.setSubjectOu(x509Info.getSubOrgUniName().toStringUtf8());
        ByteString fingerByte = x509Info.getHash();
        x509CertTrans.setFingerPrint(byteStringToHex(fingerByte));
        x509CertTrans.setNotBefore(x509Info.getValNotBef());
        x509CertTrans.setNotAfter(x509Info.getValNotAft());
        return x509CertTrans;
    }

    /**
     *  将ByteString转换为十六进制字符串
     *  */
    public static String byteStringToHex(ByteString byteString) {
        // 使用StringBuilder来构建十六进制字符串
        StringBuilder hexString = new StringBuilder(byteString.size() * 2);
        for (byte b : byteString) {
            // 将字节转换为两位数的十六进制字符串，并添加到StringBuilder中
            String hexPart = String.format("%02X", b);
            hexString.append(hexPart);
        }
        return hexString.toString();
    }

    public static SslAlertTrans createSslAlertTrans(AlertLog.ALERT_LOG alertLog) {
        ByteString metaInfoBytes = alertLog.getMetaData();
        if (metaInfoBytes.isEmpty()) {
            return null;
        }
        try{
            SslAlertTrans sslAlertTrans = new SslAlertTrans();
            sslAlertTrans.setAttackId(alertLog.getGuid());
            sslAlertTrans.setAttackTime(AlertTools.parseTimeToLong(alertLog.getTime()) / MILLISECONDS_TO_SECONDS);
            sslAlertTrans.setAttackTypeCode(alertLog.getThreatType());
            sslAlertTrans.setKillChain(alertLog.getKillChain());
            sslAlertTrans.setIocAlertInfo(alertLog.getIocAlertInfo());
            sslAlertTrans.setSip(IPTransHandler.transIP(alertLog.getSip()));
            sslAlertTrans.setDip(IPTransHandler.transIP(alertLog.getDip()));
            sslAlertTrans.setAipAddr(alertLog.getAip().getIp());
            sslAlertTrans.setVipAddr(alertLog.getVip().getIp());

            processSslMetaInfo(sslAlertTrans, alertLog.getMetaData(), alertLog.getDetectType());

            return sslAlertTrans;
        } catch (Exception e) {
            log.error("Error creating SSLAlertTrans: {}", alertLog.getGuid(), e);
            return null;
        }
    }

    public static void processSslMetaInfo(SslAlertTrans sslAlertTrans, ByteString metaInfoBytes, Integer detectType){
        if (metaInfoBytes.isEmpty()) {
            return;
        }
        try{
            ProtocolMetadata.MetaInfo metaInfo = ProtocolMetadata.MetaInfo.parseFrom(metaInfoBytes.toByteArray());
            List<ProtocolMetadata.ProtocolInfo> protocolInfoList = metaInfo.getProtocolInfoList();
            List<SslTrans> sslTransList = new ArrayList<>();
            for (ProtocolMetadata.ProtocolInfo protocolInfo:protocolInfoList){
                int protocolType = protocolInfo.getType();
                if (protocolType == AppIdEnum.SSL_TLS.getCode()) {
                    Any any = protocolInfo.getProtocolMeta();
                    // 先默认使用unPack的方式进行解析，再是parseFrom
                    try{
                        byte[] metaBytes = any.getValue().toByteArray();
                        String metaType = any.getTypeUrl().split("/")[1];
                        SslTlsInfo.Ssl_TlsInfo sslInfoUnpack = null;
                        if(metaType.equals(SSL_TYPE_URL)){
                            sslInfoUnpack = any.unpack(SslTlsInfo.Ssl_TlsInfo.class);
                        }else if(metaType.contains(SSL_TYPE_URL)){
                            sslInfoUnpack = SslTlsInfo.Ssl_TlsInfo.parseFrom(metaBytes);
                        }
                        sslTransList.add(getSslCertTrans(sslInfoUnpack));
                    }catch (Exception e){
                        log.warn("<{}>类型元数据未使用pack方式打包 SSL 协议元数据,直接使用ParseFrom进行解析",detectType);
                        SslTlsInfo.Ssl_TlsInfo sslInfoParseFrom = SslTlsInfo.Ssl_TlsInfo.parseFrom(protocolInfo.getProtocolMeta().toByteArray());
                        sslTransList.add(getSslCertTrans(sslInfoParseFrom));
                    }
                }
            }
            sslAlertTrans.setSsl(sslTransList);
        }catch (InvalidProtocolBufferException e){
            log.error("Error creating SSLAlertTrans: {}", e.toString());
        }

    }

    private static SslTrans getSslCertTrans(SslTlsInfo.Ssl_TlsInfo sslInfo) {
        SslTrans sslTrans = new SslTrans();
        sslTrans.setConType(sslInfo.getConType());
        sslTrans.setAleLev(sslInfo.getAleLev());
        sslTrans.setAleDes(sslInfo.getAleDes());
        sslTrans.setHandShaType(sslInfo.getHandShaType());
        sslTrans.setCliVer(sslInfo.getCliVer());
        sslTrans.setCliGmtUniTime(sslInfo.getCliGMTUniTime());
        sslTrans.setCliRand(sslInfo.getCliRand().toStringUtf8());
        sslTrans.setCliSesId(sslInfo.getCliSesID().toStringUtf8());
        sslTrans.setCliCipSui(sslInfo.getCliCipSui().toStringUtf8());
        sslTrans.setCliComMet(sslInfo.getCliComMet().toStringUtf8());
        sslTrans.setSrvVer(sslInfo.getSrvVer());
        sslTrans.setSrvName(sslInfo.getSrvName().toStringUtf8());
        sslTrans.setSrvNameAttr(sslInfo.getSrvNameAttr());
        sslTrans.setSrvGmtUniTime(sslInfo.getSrvGMTUniTime14());
        sslTrans.setSrvRand(sslInfo.getSrvRand().toStringUtf8());
        sslTrans.setSrvSesId(sslInfo.getSrvSesID().toStringUtf8());
        sslTrans.setSrvComprMet(sslInfo.getSrvComprMet().toStringUtf8());
        sslTrans.setSrvCertLen(sslInfo.getSrvCertLen());
        sslTrans.setCertResType(sslInfo.getCertResType());
        sslTrans.setCliCertLen(sslInfo.getCliCertLen());
        sslTrans.setRsaModOfSrvKeyExc(sslInfo.getRSAModOfSrvKeyExc().toStringUtf8());
        sslTrans.setRsaExpOfSrvKeyExc(sslInfo.getRSAExpOfSrvKeyExc());
        sslTrans.setDhModOfSrvKeyExc(sslInfo.getDHModOfSrvKeyExc().toStringUtf8());
        sslTrans.setDhGenOfSrvKeyExc(sslInfo.getDHGenOfSrvKeyExc().toStringUtf8());
        sslTrans.setSrvDhPubKey(sslInfo.getSrvDHPubKey().toStringUtf8());
        sslTrans.setPreMasKeyEncryByRsa(sslInfo.getPreMasKeyEncryByRSA().toStringUtf8());
        sslTrans.setCliDhPubKey(sslInfo.getCliDHPubKey().toStringUtf8());
        sslTrans.setExtTypeInSsl(sslInfo.getExtTypeInSSL());
        sslTrans.setCliEllCurPoiFor(sslInfo.getCliEllCurPoiFor());
        sslTrans.setCliEllCur(sslInfo.getCliEllCur());
        sslTrans.setSrvEllCurPoiFor(sslInfo.getSrvEllCurPoiFor());
        sslTrans.setSrvEllCur(sslInfo.getSrvEllCur());
        sslTrans.setSrvEllCurDhPubKey(sslInfo.getSrvEllCurDHPubKey().toStringUtf8());
        sslTrans.setCliEllCurDhPubKey(sslInfo.getCliEllCurDHPubKey().toStringUtf8());
        sslTrans.setSrvGmtUniTime2(sslInfo.getSrvGMTUniTime35());
        sslTrans.setCliExtCnt(sslInfo.getCliExtCnt());
        sslTrans.setSrvExtCnt(sslInfo.getSrvExtCnt());
        sslTrans.setCliHandSkLen(sslInfo.getCliHandSkLen());
        sslTrans.setSrvHandSkLen(sslInfo.getSrvHandSkLen());
        sslTrans.setCliExt(sslInfo.getCliExt().toStringUtf8());
        sslTrans.setSrvExt(sslInfo.getSrvExt().toStringUtf8());
        sslTrans.setCliExtGrease(sslInfo.getCliExtGrease());
        sslTrans.setCliJa3(sslInfo.getCliJA3().toStringUtf8());
        sslTrans.setSrvJa3(sslInfo.getSrvJA3().toStringUtf8());
        sslTrans.setCliSessTicket(sslInfo.getCliSessTicket().toStringUtf8());
        sslTrans.setSrvSessTicket(sslInfo.getSrvSessTicket().toStringUtf8());
        sslTrans.setAuthTag(sslInfo.getAuthTag());
        sslTrans.setCliCertCnt(sslInfo.getCliCertCnt());
        sslTrans.setSrvCertCnt(sslInfo.getSrvCertCnt());
        sslTrans.setEcGroupsCli(sslInfo.getEcGroupsCli().toStringUtf8());
        sslTrans.setEcPoiForByServ(sslInfo.getEcPoiForByServ().toStringUtf8());
        sslTrans.setEtags(sslInfo.getEtags().toStringUtf8());
        sslTrans.setTtags(sslInfo.getTtags().toStringUtf8());
        sslTrans.setCliSesIdLen(sslInfo.getCliSesIDLen().toStringUtf8());
        sslTrans.setSrvSesIdLen(sslInfo.getSrvSesIDLen().toStringUtf8());
        sslTrans.setSrvKeyExcLen(sslInfo.getSrvKeyExcLen().toStringUtf8());
        sslTrans.setEcdhCurType(sslInfo.getECDHCurType().toStringUtf8());
        sslTrans.setEcdhSig(sslInfo.getECDHSig().toStringUtf8());
        sslTrans.setDhePLen(sslInfo.getDHEPLen().toStringUtf8());
        sslTrans.setDheGLen(sslInfo.getDHEGLen().toStringUtf8());
        sslTrans.setCliKeyExcLen(sslInfo.getCliKeyExcLen().toStringUtf8());
        sslTrans.setEncPubKey(sslInfo.getEncPubKey().toStringUtf8());
        sslTrans.setEncPubKeyLen(sslInfo.getEncPubKeyLen().toStringUtf8());
        sslTrans.setCliExtLen(sslInfo.getCliExtLen().toStringUtf8());
        sslTrans.setSrvExtLen(sslInfo.getSrvExtLen().toStringUtf8());
        sslTrans.setEcdhPubKeyLen(sslInfo.getECDHPubKeyLen().toStringUtf8());
        sslTrans.setNamType(sslInfo.getNamType().toStringUtf8());
        sslTrans.setNamLen(sslInfo.getNamLen().toStringUtf8());
        sslTrans.setTicDat(sslInfo.getTicDat().toStringUtf8());
        sslTrans.setSrvCipSui(sslInfo.getSrvCipSui().toStringUtf8());
        sslTrans.setCipSuiNum(sslInfo.getCipSuiNum());
        sslTrans.setEcdhSigHash(sslInfo.getECDHSigHash().toStringUtf8());
        sslTrans.setDheSigHash(sslInfo.getDHESigHash().toStringUtf8());
        sslTrans.setRsaSigHash(sslInfo.getRSASigHash().toStringUtf8());
        sslTrans.setGreaseFlag(sslInfo.getGreaseFlag().toStringUtf8());
        sslTrans.setRsaModLen(sslInfo.getRSAModLen().toStringUtf8());
        sslTrans.setRsaExpLen(sslInfo.getRSAExpLen().toStringUtf8());
        sslTrans.setRsaSig(sslInfo.getRSASig().toStringUtf8());
        sslTrans.setDheSig(sslInfo.getDHESig().toStringUtf8());
        sslTrans.setDhePubKeyLen(sslInfo.getDHEPubKeyLen().toStringUtf8());
        sslTrans.setDhePubKey(sslInfo.getDHEPubKey().toStringUtf8());
        sslTrans.setSigAlgType(sslInfo.getSigAlgType().toStringUtf8());
        sslTrans.setSigAlg(sslInfo.getSigAlg().toStringUtf8());
        sslTrans.setSigHashAlg(sslInfo.getSigHashAlg().toStringUtf8());
        sslTrans.setJoy(sslInfo.getJOY().toStringUtf8());
        sslTrans.setJoys(sslInfo.getJOYS().toStringUtf8());
        sslTrans.setStarttls(sslInfo.getSTARTTLS().toStringUtf8());
        sslTrans.setCertNonFlag(sslInfo.getCertNonFlag().toStringUtf8());
        sslTrans.setJoyFp(sslInfo.getJoyFp().toStringUtf8());
        sslTrans.setCertIntactFlag(sslInfo.getCertIntactFlag().toStringUtf8());
        sslTrans.setCertPath(sslInfo.getCertPath().toStringUtf8());
        sslTrans.setSessSecFlag(sslInfo.getSessSecFlag().toStringUtf8());
        sslTrans.setFullText(sslInfo.getFullText().toStringUtf8());
        sslTrans.setCliCertHashes(sslInfo.getCliCertHashes().toStringUtf8());
        sslTrans.setSrvCertHashes(sslInfo.getSrvCertHashes().toStringUtf8());
        sslTrans.setCliCertNum(sslInfo.getCliCertNum());
        sslTrans.setSrvCertNum(sslInfo.getSrvCertNum());
        sslTrans.setCertExist(sslInfo.getCertExist());
        sslTrans.setExtendEcGroupsClient(sslInfo.getExtendEcGroupsClient().toStringUtf8());
        sslTrans.setLeafCertDaysRemaining(sslInfo.getLeafCertDaysRemaining());
        return sslTrans;
    }

    public static boolean sslCheckFakeAlarm(SslAlertTrans sslAlert, AlertLog.ALERT_LOG alertLog, Client bloomClient) {
        try {
            List<SslTrans> x509CertTransList = sslAlert.getSsl();
            List<String> sniList = new ArrayList<>();
            for (SslTrans sslTrans:x509CertTransList){
                String sni = sslTrans.getSrvName();
                AlertTools.addIfNotEmpty(sniList,sni);
            }

            for (String hash :sniList){
                if (bloomClient.exists(DOMAIN_BLOOM_FILTER_KEY,hash)){
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("Error checking for fake SSL alert: {}", alertLog.getGuid(), e);
            return false;
        }
    }
}
