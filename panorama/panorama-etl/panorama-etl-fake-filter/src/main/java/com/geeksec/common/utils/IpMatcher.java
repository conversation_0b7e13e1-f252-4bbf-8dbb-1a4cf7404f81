package com.geeksec.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.api.java.utils.ParameterTool;
import redis.clients.jedis.Jedis;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

@Slf4j
public class IpMatcher {

    // REDIS 主机地址
    private static String REDIS_HOST;

    // REDIS 端口号
    private static Integer REDIS_PORT;

    private static class TrieNode {
        TrieNode[] children;
        boolean isEndOfCIDR;

        TrieNode() {
            children = new TrieNode[2];
            isEndOfCIDR = false;
        }
    }

    private static TrieNode ipv4Root;
    private static TrieNode ipv6Root;

    public static void init(ParameterTool config) throws UnknownHostException {
        REDIS_HOST = config.get("redis.host.addr");
        REDIS_PORT = config.getInt("redis.host.port");
        Set<String> cidrSet = new LinkedHashSet<>();
        try (Jedis jedis = new Jedis(REDIS_HOST, REDIS_PORT)) {
            cidrSet = jedis.smembers("ip_cidr_set");
            log.info("Loaded {} CIDRs from Redis", cidrSet.size());
        } catch (Exception e) {
            log.error("初始化CIDR IP 地址过滤器失败", e);
        }
        ipv4Root = new TrieNode();
        ipv6Root = new TrieNode();
        buildTrie(cidrSet);
    }


    public IpMatcher(String redisHost,Integer redisPort) throws UnknownHostException {
        Set<String> cidrSet = new LinkedHashSet<>();
        try (Jedis jedis = new Jedis(redisHost, redisPort)) {
            cidrSet = jedis.smembers("ip_cidr_set");
            log.info("Loaded {} CIDRs from Redis", cidrSet.size());
        } catch (Exception e) {
            log.error("初始化CIDR IP 地址过滤器失败", e);
        }
        ipv4Root = new TrieNode();
        ipv6Root = new TrieNode();
        buildTrie(cidrSet);
    }

    private static void buildTrie(Set<String> cidrList) throws UnknownHostException {
        for (String cidr : cidrList) {
            String[] parts = cidr.split("/");
            InetAddress subnet = InetAddress.getByName(parts[0]);
            int prefixLength = Integer.parseInt(parts[1]);

            TrieNode root = subnet instanceof java.net.Inet4Address ? ipv4Root : ipv6Root;
            insertCIDR(root, subnet.getAddress(), prefixLength);
        }
    }

    private static void insertCIDR(TrieNode root, byte[] address, int prefixLength) {
        TrieNode current = root;
        for (int i = 0; i < prefixLength; i++) {
            int byteIndex = i / 8;
            int bitIndex = 7 - (i % 8);
            int bit = (address[byteIndex] >> bitIndex) & 1;

            if (current.children[bit] == null) {
                current.children[bit] = new TrieNode();
            }
            current = current.children[bit];
        }
        current.isEndOfCIDR = true;
    }

    public static boolean isIPInCIDRList(String ip) throws UnknownHostException {
        InetAddress address = InetAddress.getByName(ip);
        TrieNode root = address instanceof java.net.Inet4Address ? ipv4Root : ipv6Root;
        return searchIP(root, address.getAddress());
    }

    private static boolean searchIP(TrieNode root, byte[] address) {
        TrieNode current = root;
        for (int i = 0; i < address.length * 8; i++) {
            if (current.isEndOfCIDR) {
                return true;
            }
            int byteIndex = i / 8;
            int bitIndex = 7 - (i % 8);
            int bit = (address[byteIndex] >> bitIndex) & 1;

            if (current.children[bit] == null) {
                return false;
            }
            current = current.children[bit];
        }
        return current.isEndOfCIDR;
    }
}