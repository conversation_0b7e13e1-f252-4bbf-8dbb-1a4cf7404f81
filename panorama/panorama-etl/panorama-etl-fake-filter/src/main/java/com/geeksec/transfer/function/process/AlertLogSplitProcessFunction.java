package com.geeksec.transfer.function.process;

import com.geeksec.common.constant.DetectTypeConstant;
import com.geeksec.common.constant.AlertLogDeserializer;
import com.geeksec.proto.AlertLog;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;

/**
 * <AUTHOR>
 */
public class AlertLogSplitProcessFunction extends ProcessFunction<AlertLog.ALERT_LOG,AlertLog.ALERT_LOG> {


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context ctx, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        // 获取日志类型
        int detectType = alertLog.getDetectType();
        switch (detectType) {
            case DetectTypeConstant.IOC_ALERT_INFO:
                ctx.output(AlertLogDeserializer.IOC_ALERT_OUTPUT_TAG, alertLog);
                break;
            case DetectTypeConstant.IOA_ALERT_INFO:
                ctx.output(AlertLogDeserializer.IOA_ALERT_OUTPUT_TAG, alertLog);
                break;
            case DetectTypeConstant.FILE_ALERT_INFO:
                ctx.output(AlertLogDeserializer.SANDBOX_ALERT_INFO_OUTPUT_TAG, alertLog);
                break;
            case DetectTypeConstant.CRYPTO_ALERT_INFO:
                ctx.output(AlertLogDeserializer.CRYPTO_ALERT_INFO_OUTPUT_TAG, alertLog);
                break;
            case DetectTypeConstant.MAIL_ALERT_INFO:
                ctx.output(AlertLogDeserializer.EMAIL_ALERT_INFO_OUTPUT_TAG, alertLog);
                break;
            default:
                break;
        }
    }
}
