package com.geeksec.proto.protocol;
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: Ssl_TlsInfo.proto
// Protobuf Java Version: 4.29.4

public final class SslTlsInfo {
  private SslTlsInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      SslTlsInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Ssl_TlsInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Ssl_TlsInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional uint32 conType = 1;</code>
     * @return Whether the conType field is set.
     */
    boolean hasConType();
    /**
     * <code>optional uint32 conType = 1;</code>
     * @return The conType.
     */
    int getConType();

    /**
     * <code>optional uint32 aleLev = 2;</code>
     * @return Whether the aleLev field is set.
     */
    boolean hasAleLev();
    /**
     * <code>optional uint32 aleLev = 2;</code>
     * @return The aleLev.
     */
    int getAleLev();

    /**
     * <code>optional uint32 aleDes = 3;</code>
     * @return Whether the aleDes field is set.
     */
    boolean hasAleDes();
    /**
     * <code>optional uint32 aleDes = 3;</code>
     * @return The aleDes.
     */
    int getAleDes();

    /**
     * <code>optional uint32 handShaType = 4;</code>
     * @return Whether the handShaType field is set.
     */
    boolean hasHandShaType();
    /**
     * <code>optional uint32 handShaType = 4;</code>
     * @return The handShaType.
     */
    int getHandShaType();

    /**
     * <code>optional uint32 cliVer = 5;</code>
     * @return Whether the cliVer field is set.
     */
    boolean hasCliVer();
    /**
     * <code>optional uint32 cliVer = 5;</code>
     * @return The cliVer.
     */
    int getCliVer();

    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     * @return Whether the cliGMTUniTime field is set.
     */
    boolean hasCliGMTUniTime();
    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     * @return The cliGMTUniTime.
     */
    long getCliGMTUniTime();

    /**
     * <code>optional bytes cliRand = 7;</code>
     * @return Whether the cliRand field is set.
     */
    boolean hasCliRand();
    /**
     * <code>optional bytes cliRand = 7;</code>
     * @return The cliRand.
     */
    com.google.protobuf.ByteString getCliRand();

    /**
     * <code>optional bytes cliSesID = 8;</code>
     * @return Whether the cliSesID field is set.
     */
    boolean hasCliSesID();
    /**
     * <code>optional bytes cliSesID = 8;</code>
     * @return The cliSesID.
     */
    com.google.protobuf.ByteString getCliSesID();

    /**
     * <code>optional bytes cliCipSui = 9;</code>
     * @return Whether the cliCipSui field is set.
     */
    boolean hasCliCipSui();
    /**
     * <code>optional bytes cliCipSui = 9;</code>
     * @return The cliCipSui.
     */
    com.google.protobuf.ByteString getCliCipSui();

    /**
     * <code>optional bytes cliComMet = 10;</code>
     * @return Whether the cliComMet field is set.
     */
    boolean hasCliComMet();
    /**
     * <code>optional bytes cliComMet = 10;</code>
     * @return The cliComMet.
     */
    com.google.protobuf.ByteString getCliComMet();

    /**
     * <code>optional uint32 srvVer = 11;</code>
     * @return Whether the srvVer field is set.
     */
    boolean hasSrvVer();
    /**
     * <code>optional uint32 srvVer = 11;</code>
     * @return The srvVer.
     */
    int getSrvVer();

    /**
     * <code>optional bytes srvName = 12;</code>
     * @return Whether the srvName field is set.
     */
    boolean hasSrvName();
    /**
     * <code>optional bytes srvName = 12;</code>
     * @return The srvName.
     */
    com.google.protobuf.ByteString getSrvName();

    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     * @return Whether the srvNameAttr field is set.
     */
    boolean hasSrvNameAttr();
    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     * @return The srvNameAttr.
     */
    int getSrvNameAttr();

    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     * @return Whether the srvGMTUniTime field is set.
     */
    boolean hasSrvGMTUniTime14();
    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     * @return The srvGMTUniTime.
     */
    long getSrvGMTUniTime14();

    /**
     * <code>optional bytes srvRand = 15;</code>
     * @return Whether the srvRand field is set.
     */
    boolean hasSrvRand();
    /**
     * <code>optional bytes srvRand = 15;</code>
     * @return The srvRand.
     */
    com.google.protobuf.ByteString getSrvRand();

    /**
     * <code>optional bytes srvSesID = 16;</code>
     * @return Whether the srvSesID field is set.
     */
    boolean hasSrvSesID();
    /**
     * <code>optional bytes srvSesID = 16;</code>
     * @return The srvSesID.
     */
    com.google.protobuf.ByteString getSrvSesID();

    /**
     * <code>optional bytes srvComprMet = 17;</code>
     * @return Whether the srvComprMet field is set.
     */
    boolean hasSrvComprMet();
    /**
     * <code>optional bytes srvComprMet = 17;</code>
     * @return The srvComprMet.
     */
    com.google.protobuf.ByteString getSrvComprMet();

    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     * @return Whether the srvCertLen field is set.
     */
    boolean hasSrvCertLen();
    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     * @return The srvCertLen.
     */
    int getSrvCertLen();

    /**
     * <code>optional uint32 certResType = 19;</code>
     * @return Whether the certResType field is set.
     */
    boolean hasCertResType();
    /**
     * <code>optional uint32 certResType = 19;</code>
     * @return The certResType.
     */
    int getCertResType();

    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     * @return Whether the cliCertLen field is set.
     */
    boolean hasCliCertLen();
    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     * @return The cliCertLen.
     */
    int getCliCertLen();

    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     * @return Whether the rSAModOfSrvKeyExc field is set.
     */
    boolean hasRSAModOfSrvKeyExc();
    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     * @return The rSAModOfSrvKeyExc.
     */
    com.google.protobuf.ByteString getRSAModOfSrvKeyExc();

    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     * @return Whether the rSAExpOfSrvKeyExc field is set.
     */
    boolean hasRSAExpOfSrvKeyExc();
    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     * @return The rSAExpOfSrvKeyExc.
     */
    long getRSAExpOfSrvKeyExc();

    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     * @return Whether the dHModOfSrvKeyExc field is set.
     */
    boolean hasDHModOfSrvKeyExc();
    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     * @return The dHModOfSrvKeyExc.
     */
    com.google.protobuf.ByteString getDHModOfSrvKeyExc();

    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     * @return Whether the dHGenOfSrvKeyExc field is set.
     */
    boolean hasDHGenOfSrvKeyExc();
    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     * @return The dHGenOfSrvKeyExc.
     */
    com.google.protobuf.ByteString getDHGenOfSrvKeyExc();

    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     * @return Whether the srvDHPubKey field is set.
     */
    boolean hasSrvDHPubKey();
    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     * @return The srvDHPubKey.
     */
    com.google.protobuf.ByteString getSrvDHPubKey();

    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     * @return Whether the preMasKeyEncryByRSA field is set.
     */
    boolean hasPreMasKeyEncryByRSA();
    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     * @return The preMasKeyEncryByRSA.
     */
    com.google.protobuf.ByteString getPreMasKeyEncryByRSA();

    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     * @return Whether the cliDHPubKey field is set.
     */
    boolean hasCliDHPubKey();
    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     * @return The cliDHPubKey.
     */
    com.google.protobuf.ByteString getCliDHPubKey();

    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     * @return Whether the extTypeInSSL field is set.
     */
    boolean hasExtTypeInSSL();
    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     * @return The extTypeInSSL.
     */
    int getExtTypeInSSL();

    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     * @return Whether the cliEllCurPoiFor field is set.
     */
    boolean hasCliEllCurPoiFor();
    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     * @return The cliEllCurPoiFor.
     */
    int getCliEllCurPoiFor();

    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     * @return Whether the cliEllCur field is set.
     */
    boolean hasCliEllCur();
    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     * @return The cliEllCur.
     */
    int getCliEllCur();

    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     * @return Whether the srvEllCurPoiFor field is set.
     */
    boolean hasSrvEllCurPoiFor();
    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     * @return The srvEllCurPoiFor.
     */
    int getSrvEllCurPoiFor();

    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     * @return Whether the srvEllCur field is set.
     */
    boolean hasSrvEllCur();
    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     * @return The srvEllCur.
     */
    int getSrvEllCur();

    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     * @return Whether the srvEllCurDHPubKey field is set.
     */
    boolean hasSrvEllCurDHPubKey();
    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     * @return The srvEllCurDHPubKey.
     */
    com.google.protobuf.ByteString getSrvEllCurDHPubKey();

    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     * @return Whether the cliEllCurDHPubKey field is set.
     */
    boolean hasCliEllCurDHPubKey();
    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     * @return The cliEllCurDHPubKey.
     */
    com.google.protobuf.ByteString getCliEllCurDHPubKey();

    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     * @return Whether the srvGMTUniTime field is set.
     */
    boolean hasSrvGMTUniTime35();
    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     * @return The srvGMTUniTime.
     */
    long getSrvGMTUniTime35();

    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     * @return Whether the cliExtCnt field is set.
     */
    boolean hasCliExtCnt();
    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     * @return The cliExtCnt.
     */
    int getCliExtCnt();

    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     * @return Whether the srvExtCnt field is set.
     */
    boolean hasSrvExtCnt();
    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     * @return The srvExtCnt.
     */
    int getSrvExtCnt();

    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     * @return Whether the cliHandSkLen field is set.
     */
    boolean hasCliHandSkLen();
    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     * @return The cliHandSkLen.
     */
    int getCliHandSkLen();

    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     * @return Whether the srvHandSkLen field is set.
     */
    boolean hasSrvHandSkLen();
    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     * @return The srvHandSkLen.
     */
    int getSrvHandSkLen();

    /**
     * <code>optional bytes cliExt = 40;</code>
     * @return Whether the cliExt field is set.
     */
    boolean hasCliExt();
    /**
     * <code>optional bytes cliExt = 40;</code>
     * @return The cliExt.
     */
    com.google.protobuf.ByteString getCliExt();

    /**
     * <code>optional bytes srvExt = 41;</code>
     * @return Whether the srvExt field is set.
     */
    boolean hasSrvExt();
    /**
     * <code>optional bytes srvExt = 41;</code>
     * @return The srvExt.
     */
    com.google.protobuf.ByteString getSrvExt();

    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     * @return Whether the cliExtGrease field is set.
     */
    boolean hasCliExtGrease();
    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     * @return The cliExtGrease.
     */
    int getCliExtGrease();

    /**
     * <code>optional bytes cliJA3 = 43;</code>
     * @return Whether the cliJA3 field is set.
     */
    boolean hasCliJA3();
    /**
     * <code>optional bytes cliJA3 = 43;</code>
     * @return The cliJA3.
     */
    com.google.protobuf.ByteString getCliJA3();

    /**
     * <code>optional bytes srvJA3 = 44;</code>
     * @return Whether the srvJA3 field is set.
     */
    boolean hasSrvJA3();
    /**
     * <code>optional bytes srvJA3 = 44;</code>
     * @return The srvJA3.
     */
    com.google.protobuf.ByteString getSrvJA3();

    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     * @return Whether the cliSessTicket field is set.
     */
    boolean hasCliSessTicket();
    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     * @return The cliSessTicket.
     */
    com.google.protobuf.ByteString getCliSessTicket();

    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     * @return Whether the srvSessTicket field is set.
     */
    boolean hasSrvSessTicket();
    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     * @return The srvSessTicket.
     */
    com.google.protobuf.ByteString getSrvSessTicket();

    /**
     * <code>optional uint32 AuthTag = 47;</code>
     * @return Whether the authTag field is set.
     */
    boolean hasAuthTag();
    /**
     * <code>optional uint32 AuthTag = 47;</code>
     * @return The authTag.
     */
    int getAuthTag();

    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     * @return Whether the cliCertCnt field is set.
     */
    boolean hasCliCertCnt();
    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     * @return The cliCertCnt.
     */
    int getCliCertCnt();

    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     * @return Whether the srvCertCnt field is set.
     */
    boolean hasSrvCertCnt();
    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     * @return The srvCertCnt.
     */
    int getSrvCertCnt();

    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     * @return Whether the ecGroupsCli field is set.
     */
    boolean hasEcGroupsCli();
    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     * @return The ecGroupsCli.
     */
    com.google.protobuf.ByteString getEcGroupsCli();

    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     * @return Whether the ecPoiForByServ field is set.
     */
    boolean hasEcPoiForByServ();
    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     * @return The ecPoiForByServ.
     */
    com.google.protobuf.ByteString getEcPoiForByServ();

    /**
     * <code>optional bytes etags = 52;</code>
     * @return Whether the etags field is set.
     */
    boolean hasEtags();
    /**
     * <code>optional bytes etags = 52;</code>
     * @return The etags.
     */
    com.google.protobuf.ByteString getEtags();

    /**
     * <code>optional bytes ttags = 53;</code>
     * @return Whether the ttags field is set.
     */
    boolean hasTtags();
    /**
     * <code>optional bytes ttags = 53;</code>
     * @return The ttags.
     */
    com.google.protobuf.ByteString getTtags();

    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     * @return Whether the cliSesIDLen field is set.
     */
    boolean hasCliSesIDLen();
    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     * @return The cliSesIDLen.
     */
    com.google.protobuf.ByteString getCliSesIDLen();

    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     * @return Whether the srvSesIDLen field is set.
     */
    boolean hasSrvSesIDLen();
    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     * @return The srvSesIDLen.
     */
    com.google.protobuf.ByteString getSrvSesIDLen();

    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     * @return Whether the srvKeyExcLen field is set.
     */
    boolean hasSrvKeyExcLen();
    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     * @return The srvKeyExcLen.
     */
    com.google.protobuf.ByteString getSrvKeyExcLen();

    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     * @return Whether the eCDHCurType field is set.
     */
    boolean hasECDHCurType();
    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     * @return The eCDHCurType.
     */
    com.google.protobuf.ByteString getECDHCurType();

    /**
     * <code>optional bytes ECDHSig = 58;</code>
     * @return Whether the eCDHSig field is set.
     */
    boolean hasECDHSig();
    /**
     * <code>optional bytes ECDHSig = 58;</code>
     * @return The eCDHSig.
     */
    com.google.protobuf.ByteString getECDHSig();

    /**
     * <code>optional bytes DHEPLen = 59;</code>
     * @return Whether the dHEPLen field is set.
     */
    boolean hasDHEPLen();
    /**
     * <code>optional bytes DHEPLen = 59;</code>
     * @return The dHEPLen.
     */
    com.google.protobuf.ByteString getDHEPLen();

    /**
     * <code>optional bytes DHEGLen = 60;</code>
     * @return Whether the dHEGLen field is set.
     */
    boolean hasDHEGLen();
    /**
     * <code>optional bytes DHEGLen = 60;</code>
     * @return The dHEGLen.
     */
    com.google.protobuf.ByteString getDHEGLen();

    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     * @return Whether the cliKeyExcLen field is set.
     */
    boolean hasCliKeyExcLen();
    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     * @return The cliKeyExcLen.
     */
    com.google.protobuf.ByteString getCliKeyExcLen();

    /**
     * <code>optional bytes encPubKey = 62;</code>
     * @return Whether the encPubKey field is set.
     */
    boolean hasEncPubKey();
    /**
     * <code>optional bytes encPubKey = 62;</code>
     * @return The encPubKey.
     */
    com.google.protobuf.ByteString getEncPubKey();

    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     * @return Whether the encPubKeyLen field is set.
     */
    boolean hasEncPubKeyLen();
    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     * @return The encPubKeyLen.
     */
    com.google.protobuf.ByteString getEncPubKeyLen();

    /**
     * <code>optional bytes cliExtLen = 64;</code>
     * @return Whether the cliExtLen field is set.
     */
    boolean hasCliExtLen();
    /**
     * <code>optional bytes cliExtLen = 64;</code>
     * @return The cliExtLen.
     */
    com.google.protobuf.ByteString getCliExtLen();

    /**
     * <code>optional bytes srvExtLen = 65;</code>
     * @return Whether the srvExtLen field is set.
     */
    boolean hasSrvExtLen();
    /**
     * <code>optional bytes srvExtLen = 65;</code>
     * @return The srvExtLen.
     */
    com.google.protobuf.ByteString getSrvExtLen();

    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     * @return Whether the eCDHPubKeyLen field is set.
     */
    boolean hasECDHPubKeyLen();
    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     * @return The eCDHPubKeyLen.
     */
    com.google.protobuf.ByteString getECDHPubKeyLen();

    /**
     * <code>optional bytes namType = 67;</code>
     * @return Whether the namType field is set.
     */
    boolean hasNamType();
    /**
     * <code>optional bytes namType = 67;</code>
     * @return The namType.
     */
    com.google.protobuf.ByteString getNamType();

    /**
     * <code>optional bytes namLen = 68;</code>
     * @return Whether the namLen field is set.
     */
    boolean hasNamLen();
    /**
     * <code>optional bytes namLen = 68;</code>
     * @return The namLen.
     */
    com.google.protobuf.ByteString getNamLen();

    /**
     * <code>optional bytes ticDat = 69;</code>
     * @return Whether the ticDat field is set.
     */
    boolean hasTicDat();
    /**
     * <code>optional bytes ticDat = 69;</code>
     * @return The ticDat.
     */
    com.google.protobuf.ByteString getTicDat();

    /**
     * <code>optional bytes srvCipSui = 70;</code>
     * @return Whether the srvCipSui field is set.
     */
    boolean hasSrvCipSui();
    /**
     * <code>optional bytes srvCipSui = 70;</code>
     * @return The srvCipSui.
     */
    com.google.protobuf.ByteString getSrvCipSui();

    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     * @return Whether the cipSuiNum field is set.
     */
    boolean hasCipSuiNum();
    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     * @return The cipSuiNum.
     */
    int getCipSuiNum();

    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     * @return Whether the eCDHSigHash field is set.
     */
    boolean hasECDHSigHash();
    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     * @return The eCDHSigHash.
     */
    com.google.protobuf.ByteString getECDHSigHash();

    /**
     * <code>optional bytes DHESigHash = 73;</code>
     * @return Whether the dHESigHash field is set.
     */
    boolean hasDHESigHash();
    /**
     * <code>optional bytes DHESigHash = 73;</code>
     * @return The dHESigHash.
     */
    com.google.protobuf.ByteString getDHESigHash();

    /**
     * <code>optional bytes RSASigHash = 74;</code>
     * @return Whether the rSASigHash field is set.
     */
    boolean hasRSASigHash();
    /**
     * <code>optional bytes RSASigHash = 74;</code>
     * @return The rSASigHash.
     */
    com.google.protobuf.ByteString getRSASigHash();

    /**
     * <code>optional bytes greaseFlag = 75;</code>
     * @return Whether the greaseFlag field is set.
     */
    boolean hasGreaseFlag();
    /**
     * <code>optional bytes greaseFlag = 75;</code>
     * @return The greaseFlag.
     */
    com.google.protobuf.ByteString getGreaseFlag();

    /**
     * <code>optional bytes RSAModLen = 76;</code>
     * @return Whether the rSAModLen field is set.
     */
    boolean hasRSAModLen();
    /**
     * <code>optional bytes RSAModLen = 76;</code>
     * @return The rSAModLen.
     */
    com.google.protobuf.ByteString getRSAModLen();

    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     * @return Whether the rSAExpLen field is set.
     */
    boolean hasRSAExpLen();
    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     * @return The rSAExpLen.
     */
    com.google.protobuf.ByteString getRSAExpLen();

    /**
     * <code>optional bytes RSASig = 78;</code>
     * @return Whether the rSASig field is set.
     */
    boolean hasRSASig();
    /**
     * <code>optional bytes RSASig = 78;</code>
     * @return The rSASig.
     */
    com.google.protobuf.ByteString getRSASig();

    /**
     * <code>optional bytes DHESig = 79;</code>
     * @return Whether the dHESig field is set.
     */
    boolean hasDHESig();
    /**
     * <code>optional bytes DHESig = 79;</code>
     * @return The dHESig.
     */
    com.google.protobuf.ByteString getDHESig();

    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     * @return Whether the dHEPubKeyLen field is set.
     */
    boolean hasDHEPubKeyLen();
    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     * @return The dHEPubKeyLen.
     */
    com.google.protobuf.ByteString getDHEPubKeyLen();

    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     * @return Whether the dHEPubKey field is set.
     */
    boolean hasDHEPubKey();
    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     * @return The dHEPubKey.
     */
    com.google.protobuf.ByteString getDHEPubKey();

    /**
     * <code>optional bytes SigAlgType = 82;</code>
     * @return Whether the sigAlgType field is set.
     */
    boolean hasSigAlgType();
    /**
     * <code>optional bytes SigAlgType = 82;</code>
     * @return The sigAlgType.
     */
    com.google.protobuf.ByteString getSigAlgType();

    /**
     * <code>optional bytes sigAlg = 83;</code>
     * @return Whether the sigAlg field is set.
     */
    boolean hasSigAlg();
    /**
     * <code>optional bytes sigAlg = 83;</code>
     * @return The sigAlg.
     */
    com.google.protobuf.ByteString getSigAlg();

    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     * @return Whether the sigHashAlg field is set.
     */
    boolean hasSigHashAlg();
    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     * @return The sigHashAlg.
     */
    com.google.protobuf.ByteString getSigHashAlg();

    /**
     * <code>optional bytes JOY = 85;</code>
     * @return Whether the jOY field is set.
     */
    boolean hasJOY();
    /**
     * <code>optional bytes JOY = 85;</code>
     * @return The jOY.
     */
    com.google.protobuf.ByteString getJOY();

    /**
     * <code>optional bytes JOYS = 86;</code>
     * @return Whether the jOYS field is set.
     */
    boolean hasJOYS();
    /**
     * <code>optional bytes JOYS = 86;</code>
     * @return The jOYS.
     */
    com.google.protobuf.ByteString getJOYS();

    /**
     * <code>optional bytes STARTTLS = 87;</code>
     * @return Whether the sTARTTLS field is set.
     */
    boolean hasSTARTTLS();
    /**
     * <code>optional bytes STARTTLS = 87;</code>
     * @return The sTARTTLS.
     */
    com.google.protobuf.ByteString getSTARTTLS();

    /**
     * <code>optional bytes certNonFlag = 88;</code>
     * @return Whether the certNonFlag field is set.
     */
    boolean hasCertNonFlag();
    /**
     * <code>optional bytes certNonFlag = 88;</code>
     * @return The certNonFlag.
     */
    com.google.protobuf.ByteString getCertNonFlag();

    /**
     * <code>optional bytes JoyFp = 89;</code>
     * @return Whether the joyFp field is set.
     */
    boolean hasJoyFp();
    /**
     * <code>optional bytes JoyFp = 89;</code>
     * @return The joyFp.
     */
    com.google.protobuf.ByteString getJoyFp();

    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     * @return Whether the certIntactFlag field is set.
     */
    boolean hasCertIntactFlag();
    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     * @return The certIntactFlag.
     */
    com.google.protobuf.ByteString getCertIntactFlag();

    /**
     * <code>optional bytes certPath = 91;</code>
     * @return Whether the certPath field is set.
     */
    boolean hasCertPath();
    /**
     * <code>optional bytes certPath = 91;</code>
     * @return The certPath.
     */
    com.google.protobuf.ByteString getCertPath();

    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     * @return Whether the sessSecFlag field is set.
     */
    boolean hasSessSecFlag();
    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     * @return The sessSecFlag.
     */
    com.google.protobuf.ByteString getSessSecFlag();

    /**
     * <code>optional bytes fullText = 93;</code>
     * @return Whether the fullText field is set.
     */
    boolean hasFullText();
    /**
     * <code>optional bytes fullText = 93;</code>
     * @return The fullText.
     */
    com.google.protobuf.ByteString getFullText();

    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     * @return Whether the cliCertHashes field is set.
     */
    boolean hasCliCertHashes();
    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     * @return The cliCertHashes.
     */
    com.google.protobuf.ByteString getCliCertHashes();

    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     * @return Whether the srvCertHashes field is set.
     */
    boolean hasSrvCertHashes();
    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     * @return The srvCertHashes.
     */
    com.google.protobuf.ByteString getSrvCertHashes();

    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     * @return Whether the cliCertNum field is set.
     */
    boolean hasCliCertNum();
    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     * @return The cliCertNum.
     */
    int getCliCertNum();

    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     * @return Whether the srvCertNum field is set.
     */
    boolean hasSrvCertNum();
    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     * @return The srvCertNum.
     */
    int getSrvCertNum();

    /**
     * <code>optional uint32 certExist = 98;</code>
     * @return Whether the certExist field is set.
     */
    boolean hasCertExist();
    /**
     * <code>optional uint32 certExist = 98;</code>
     * @return The certExist.
     */
    int getCertExist();

    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     * @return Whether the extendEcGroupsClient field is set.
     */
    boolean hasExtendEcGroupsClient();
    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     * @return The extendEcGroupsClient.
     */
    com.google.protobuf.ByteString getExtendEcGroupsClient();

    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     * @return Whether the leafCertDaysRemaining field is set.
     */
    boolean hasLeafCertDaysRemaining();
    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     * @return The leafCertDaysRemaining.
     */
    int getLeafCertDaysRemaining();
  }
  /**
   * Protobuf type {@code Ssl_TlsInfo}
   */
  public static final class Ssl_TlsInfo extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Ssl_TlsInfo)
      Ssl_TlsInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        Ssl_TlsInfo.class.getName());
    }
    // Use Ssl_TlsInfo.newBuilder() to construct.
    private Ssl_TlsInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private Ssl_TlsInfo() {
      cliRand_ = com.google.protobuf.ByteString.EMPTY;
      cliSesID_ = com.google.protobuf.ByteString.EMPTY;
      cliCipSui_ = com.google.protobuf.ByteString.EMPTY;
      cliComMet_ = com.google.protobuf.ByteString.EMPTY;
      srvName_ = com.google.protobuf.ByteString.EMPTY;
      srvRand_ = com.google.protobuf.ByteString.EMPTY;
      srvSesID_ = com.google.protobuf.ByteString.EMPTY;
      srvComprMet_ = com.google.protobuf.ByteString.EMPTY;
      rSAModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      dHModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      dHGenOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      preMasKeyEncryByRSA_ = com.google.protobuf.ByteString.EMPTY;
      cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      srvEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      cliEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      cliExt_ = com.google.protobuf.ByteString.EMPTY;
      srvExt_ = com.google.protobuf.ByteString.EMPTY;
      cliJA3_ = com.google.protobuf.ByteString.EMPTY;
      srvJA3_ = com.google.protobuf.ByteString.EMPTY;
      cliSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      srvSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      ecGroupsCli_ = com.google.protobuf.ByteString.EMPTY;
      ecPoiForByServ_ = com.google.protobuf.ByteString.EMPTY;
      etags_ = com.google.protobuf.ByteString.EMPTY;
      ttags_ = com.google.protobuf.ByteString.EMPTY;
      cliSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      srvSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      srvKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      eCDHCurType_ = com.google.protobuf.ByteString.EMPTY;
      eCDHSig_ = com.google.protobuf.ByteString.EMPTY;
      dHEPLen_ = com.google.protobuf.ByteString.EMPTY;
      dHEGLen_ = com.google.protobuf.ByteString.EMPTY;
      cliKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      encPubKey_ = com.google.protobuf.ByteString.EMPTY;
      encPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      cliExtLen_ = com.google.protobuf.ByteString.EMPTY;
      srvExtLen_ = com.google.protobuf.ByteString.EMPTY;
      eCDHPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      namType_ = com.google.protobuf.ByteString.EMPTY;
      namLen_ = com.google.protobuf.ByteString.EMPTY;
      ticDat_ = com.google.protobuf.ByteString.EMPTY;
      srvCipSui_ = com.google.protobuf.ByteString.EMPTY;
      eCDHSigHash_ = com.google.protobuf.ByteString.EMPTY;
      dHESigHash_ = com.google.protobuf.ByteString.EMPTY;
      rSASigHash_ = com.google.protobuf.ByteString.EMPTY;
      greaseFlag_ = com.google.protobuf.ByteString.EMPTY;
      rSAModLen_ = com.google.protobuf.ByteString.EMPTY;
      rSAExpLen_ = com.google.protobuf.ByteString.EMPTY;
      rSASig_ = com.google.protobuf.ByteString.EMPTY;
      dHESig_ = com.google.protobuf.ByteString.EMPTY;
      dHEPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      dHEPubKey_ = com.google.protobuf.ByteString.EMPTY;
      sigAlgType_ = com.google.protobuf.ByteString.EMPTY;
      sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      sigHashAlg_ = com.google.protobuf.ByteString.EMPTY;
      jOY_ = com.google.protobuf.ByteString.EMPTY;
      jOYS_ = com.google.protobuf.ByteString.EMPTY;
      sTARTTLS_ = com.google.protobuf.ByteString.EMPTY;
      certNonFlag_ = com.google.protobuf.ByteString.EMPTY;
      joyFp_ = com.google.protobuf.ByteString.EMPTY;
      certIntactFlag_ = com.google.protobuf.ByteString.EMPTY;
      certPath_ = com.google.protobuf.ByteString.EMPTY;
      sessSecFlag_ = com.google.protobuf.ByteString.EMPTY;
      fullText_ = com.google.protobuf.ByteString.EMPTY;
      cliCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      srvCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      extendEcGroupsClient_ = com.google.protobuf.ByteString.EMPTY;
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return SslTlsInfo.internal_static_Ssl_TlsInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return SslTlsInfo.internal_static_Ssl_TlsInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              SslTlsInfo.Ssl_TlsInfo.class, SslTlsInfo.Ssl_TlsInfo.Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    private int bitField2_;
    private int bitField3_;
    public static final int CONTYPE_FIELD_NUMBER = 1;
    private int conType_ = 0;
    /**
     * <code>optional uint32 conType = 1;</code>
     * @return Whether the conType field is set.
     */
    @java.lang.Override
    public boolean hasConType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 conType = 1;</code>
     * @return The conType.
     */
    @java.lang.Override
    public int getConType() {
      return conType_;
    }

    public static final int ALELEV_FIELD_NUMBER = 2;
    private int aleLev_ = 0;
    /**
     * <code>optional uint32 aleLev = 2;</code>
     * @return Whether the aleLev field is set.
     */
    @java.lang.Override
    public boolean hasAleLev() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 aleLev = 2;</code>
     * @return The aleLev.
     */
    @java.lang.Override
    public int getAleLev() {
      return aleLev_;
    }

    public static final int ALEDES_FIELD_NUMBER = 3;
    private int aleDes_ = 0;
    /**
     * <code>optional uint32 aleDes = 3;</code>
     * @return Whether the aleDes field is set.
     */
    @java.lang.Override
    public boolean hasAleDes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint32 aleDes = 3;</code>
     * @return The aleDes.
     */
    @java.lang.Override
    public int getAleDes() {
      return aleDes_;
    }

    public static final int HANDSHATYPE_FIELD_NUMBER = 4;
    private int handShaType_ = 0;
    /**
     * <code>optional uint32 handShaType = 4;</code>
     * @return Whether the handShaType field is set.
     */
    @java.lang.Override
    public boolean hasHandShaType() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 handShaType = 4;</code>
     * @return The handShaType.
     */
    @java.lang.Override
    public int getHandShaType() {
      return handShaType_;
    }

    public static final int CLIVER_FIELD_NUMBER = 5;
    private int cliVer_ = 0;
    /**
     * <code>optional uint32 cliVer = 5;</code>
     * @return Whether the cliVer field is set.
     */
    @java.lang.Override
    public boolean hasCliVer() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 cliVer = 5;</code>
     * @return The cliVer.
     */
    @java.lang.Override
    public int getCliVer() {
      return cliVer_;
    }

    public static final int CLIGMTUNITIME_FIELD_NUMBER = 6;
    private long cliGMTUniTime_ = 0L;
    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     * @return Whether the cliGMTUniTime field is set.
     */
    @java.lang.Override
    public boolean hasCliGMTUniTime() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional uint64 cliGMTUniTime = 6;</code>
     * @return The cliGMTUniTime.
     */
    @java.lang.Override
    public long getCliGMTUniTime() {
      return cliGMTUniTime_;
    }

    public static final int CLIRAND_FIELD_NUMBER = 7;
    private com.google.protobuf.ByteString cliRand_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliRand = 7;</code>
     * @return Whether the cliRand field is set.
     */
    @java.lang.Override
    public boolean hasCliRand() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional bytes cliRand = 7;</code>
     * @return The cliRand.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliRand() {
      return cliRand_;
    }

    public static final int CLISESID_FIELD_NUMBER = 8;
    private com.google.protobuf.ByteString cliSesID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliSesID = 8;</code>
     * @return Whether the cliSesID field is set.
     */
    @java.lang.Override
    public boolean hasCliSesID() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes cliSesID = 8;</code>
     * @return The cliSesID.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliSesID() {
      return cliSesID_;
    }

    public static final int CLICIPSUI_FIELD_NUMBER = 9;
    private com.google.protobuf.ByteString cliCipSui_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliCipSui = 9;</code>
     * @return Whether the cliCipSui field is set.
     */
    @java.lang.Override
    public boolean hasCliCipSui() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes cliCipSui = 9;</code>
     * @return The cliCipSui.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliCipSui() {
      return cliCipSui_;
    }

    public static final int CLICOMMET_FIELD_NUMBER = 10;
    private com.google.protobuf.ByteString cliComMet_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliComMet = 10;</code>
     * @return Whether the cliComMet field is set.
     */
    @java.lang.Override
    public boolean hasCliComMet() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes cliComMet = 10;</code>
     * @return The cliComMet.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliComMet() {
      return cliComMet_;
    }

    public static final int SRVVER_FIELD_NUMBER = 11;
    private int srvVer_ = 0;
    /**
     * <code>optional uint32 srvVer = 11;</code>
     * @return Whether the srvVer field is set.
     */
    @java.lang.Override
    public boolean hasSrvVer() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional uint32 srvVer = 11;</code>
     * @return The srvVer.
     */
    @java.lang.Override
    public int getSrvVer() {
      return srvVer_;
    }

    public static final int SRVNAME_FIELD_NUMBER = 12;
    private com.google.protobuf.ByteString srvName_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvName = 12;</code>
     * @return Whether the srvName field is set.
     */
    @java.lang.Override
    public boolean hasSrvName() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes srvName = 12;</code>
     * @return The srvName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvName() {
      return srvName_;
    }

    public static final int SRVNAMEATTR_FIELD_NUMBER = 13;
    private int srvNameAttr_ = 0;
    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     * @return Whether the srvNameAttr field is set.
     */
    @java.lang.Override
    public boolean hasSrvNameAttr() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional uint32 srvNameAttr = 13;</code>
     * @return The srvNameAttr.
     */
    @java.lang.Override
    public int getSrvNameAttr() {
      return srvNameAttr_;
    }

    public static final int SRVGMTUNITIME_FIELD_NUMBER = 14;
    private long srvGMTUniTime14_ = 0L;
    // An alternative name is used for field "srvGMTUniTime" because:
    //     capitalized name of field "srvGMTUniTime" conflicts with field "srvGMTUni_Time"
    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     * @return Whether the srvGMTUniTime field is set.
     */
    @java.lang.Override
    public boolean hasSrvGMTUniTime14() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional uint64 srvGMTUniTime = 14;</code>
     * @return The srvGMTUniTime.
     */
    @java.lang.Override
    public long getSrvGMTUniTime14() {
      return srvGMTUniTime14_;
    }

    public static final int SRVRAND_FIELD_NUMBER = 15;
    private com.google.protobuf.ByteString srvRand_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvRand = 15;</code>
     * @return Whether the srvRand field is set.
     */
    @java.lang.Override
    public boolean hasSrvRand() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes srvRand = 15;</code>
     * @return The srvRand.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvRand() {
      return srvRand_;
    }

    public static final int SRVSESID_FIELD_NUMBER = 16;
    private com.google.protobuf.ByteString srvSesID_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvSesID = 16;</code>
     * @return Whether the srvSesID field is set.
     */
    @java.lang.Override
    public boolean hasSrvSesID() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes srvSesID = 16;</code>
     * @return The srvSesID.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvSesID() {
      return srvSesID_;
    }

    public static final int SRVCOMPRMET_FIELD_NUMBER = 17;
    private com.google.protobuf.ByteString srvComprMet_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvComprMet = 17;</code>
     * @return Whether the srvComprMet field is set.
     */
    @java.lang.Override
    public boolean hasSrvComprMet() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes srvComprMet = 17;</code>
     * @return The srvComprMet.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvComprMet() {
      return srvComprMet_;
    }

    public static final int SRVCERTLEN_FIELD_NUMBER = 18;
    private int srvCertLen_ = 0;
    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     * @return Whether the srvCertLen field is set.
     */
    @java.lang.Override
    public boolean hasSrvCertLen() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <code>optional uint32 srvCertLen = 18;</code>
     * @return The srvCertLen.
     */
    @java.lang.Override
    public int getSrvCertLen() {
      return srvCertLen_;
    }

    public static final int CERTRESTYPE_FIELD_NUMBER = 19;
    private int certResType_ = 0;
    /**
     * <code>optional uint32 certResType = 19;</code>
     * @return Whether the certResType field is set.
     */
    @java.lang.Override
    public boolean hasCertResType() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <code>optional uint32 certResType = 19;</code>
     * @return The certResType.
     */
    @java.lang.Override
    public int getCertResType() {
      return certResType_;
    }

    public static final int CLICERTLEN_FIELD_NUMBER = 20;
    private int cliCertLen_ = 0;
    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     * @return Whether the cliCertLen field is set.
     */
    @java.lang.Override
    public boolean hasCliCertLen() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <code>optional uint32 cliCertLen = 20;</code>
     * @return The cliCertLen.
     */
    @java.lang.Override
    public int getCliCertLen() {
      return cliCertLen_;
    }

    public static final int RSAMODOFSRVKEYEXC_FIELD_NUMBER = 21;
    private com.google.protobuf.ByteString rSAModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     * @return Whether the rSAModOfSrvKeyExc field is set.
     */
    @java.lang.Override
    public boolean hasRSAModOfSrvKeyExc() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
     * @return The rSAModOfSrvKeyExc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRSAModOfSrvKeyExc() {
      return rSAModOfSrvKeyExc_;
    }

    public static final int RSAEXPOFSRVKEYEXC_FIELD_NUMBER = 22;
    private long rSAExpOfSrvKeyExc_ = 0L;
    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     * @return Whether the rSAExpOfSrvKeyExc field is set.
     */
    @java.lang.Override
    public boolean hasRSAExpOfSrvKeyExc() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
     * @return The rSAExpOfSrvKeyExc.
     */
    @java.lang.Override
    public long getRSAExpOfSrvKeyExc() {
      return rSAExpOfSrvKeyExc_;
    }

    public static final int DHMODOFSRVKEYEXC_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString dHModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     * @return Whether the dHModOfSrvKeyExc field is set.
     */
    @java.lang.Override
    public boolean hasDHModOfSrvKeyExc() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
     * @return The dHModOfSrvKeyExc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHModOfSrvKeyExc() {
      return dHModOfSrvKeyExc_;
    }

    public static final int DHGENOFSRVKEYEXC_FIELD_NUMBER = 24;
    private com.google.protobuf.ByteString dHGenOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     * @return Whether the dHGenOfSrvKeyExc field is set.
     */
    @java.lang.Override
    public boolean hasDHGenOfSrvKeyExc() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
     * @return The dHGenOfSrvKeyExc.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHGenOfSrvKeyExc() {
      return dHGenOfSrvKeyExc_;
    }

    public static final int SRVDHPUBKEY_FIELD_NUMBER = 25;
    private com.google.protobuf.ByteString srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     * @return Whether the srvDHPubKey field is set.
     */
    @java.lang.Override
    public boolean hasSrvDHPubKey() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes srvDHPubKey = 25;</code>
     * @return The srvDHPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvDHPubKey() {
      return srvDHPubKey_;
    }

    public static final int PREMASKEYENCRYBYRSA_FIELD_NUMBER = 26;
    private com.google.protobuf.ByteString preMasKeyEncryByRSA_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     * @return Whether the preMasKeyEncryByRSA field is set.
     */
    @java.lang.Override
    public boolean hasPreMasKeyEncryByRSA() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
     * @return The preMasKeyEncryByRSA.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getPreMasKeyEncryByRSA() {
      return preMasKeyEncryByRSA_;
    }

    public static final int CLIDHPUBKEY_FIELD_NUMBER = 27;
    private com.google.protobuf.ByteString cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     * @return Whether the cliDHPubKey field is set.
     */
    @java.lang.Override
    public boolean hasCliDHPubKey() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes cliDHPubKey = 27;</code>
     * @return The cliDHPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliDHPubKey() {
      return cliDHPubKey_;
    }

    public static final int EXTTYPEINSSL_FIELD_NUMBER = 28;
    private int extTypeInSSL_ = 0;
    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     * @return Whether the extTypeInSSL field is set.
     */
    @java.lang.Override
    public boolean hasExtTypeInSSL() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <code>optional uint32 extTypeInSSL = 28;</code>
     * @return The extTypeInSSL.
     */
    @java.lang.Override
    public int getExtTypeInSSL() {
      return extTypeInSSL_;
    }

    public static final int CLIELLCURPOIFOR_FIELD_NUMBER = 29;
    private int cliEllCurPoiFor_ = 0;
    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     * @return Whether the cliEllCurPoiFor field is set.
     */
    @java.lang.Override
    public boolean hasCliEllCurPoiFor() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <code>optional uint32 cliEllCurPoiFor = 29;</code>
     * @return The cliEllCurPoiFor.
     */
    @java.lang.Override
    public int getCliEllCurPoiFor() {
      return cliEllCurPoiFor_;
    }

    public static final int CLIELLCUR_FIELD_NUMBER = 30;
    private int cliEllCur_ = 0;
    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     * @return Whether the cliEllCur field is set.
     */
    @java.lang.Override
    public boolean hasCliEllCur() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <code>optional uint32 cliEllCur = 30;</code>
     * @return The cliEllCur.
     */
    @java.lang.Override
    public int getCliEllCur() {
      return cliEllCur_;
    }

    public static final int SRVELLCURPOIFOR_FIELD_NUMBER = 31;
    private int srvEllCurPoiFor_ = 0;
    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     * @return Whether the srvEllCurPoiFor field is set.
     */
    @java.lang.Override
    public boolean hasSrvEllCurPoiFor() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <code>optional uint32 srvEllCurPoiFor = 31;</code>
     * @return The srvEllCurPoiFor.
     */
    @java.lang.Override
    public int getSrvEllCurPoiFor() {
      return srvEllCurPoiFor_;
    }

    public static final int SRVELLCUR_FIELD_NUMBER = 32;
    private int srvEllCur_ = 0;
    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     * @return Whether the srvEllCur field is set.
     */
    @java.lang.Override
    public boolean hasSrvEllCur() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint32 srvEllCur = 32;</code>
     * @return The srvEllCur.
     */
    @java.lang.Override
    public int getSrvEllCur() {
      return srvEllCur_;
    }

    public static final int SRVELLCURDHPUBKEY_FIELD_NUMBER = 33;
    private com.google.protobuf.ByteString srvEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     * @return Whether the srvEllCurDHPubKey field is set.
     */
    @java.lang.Override
    public boolean hasSrvEllCurDHPubKey() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes srvEllCurDHPubKey = 33;</code>
     * @return The srvEllCurDHPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvEllCurDHPubKey() {
      return srvEllCurDHPubKey_;
    }

    public static final int CLIELLCURDHPUBKEY_FIELD_NUMBER = 34;
    private com.google.protobuf.ByteString cliEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     * @return Whether the cliEllCurDHPubKey field is set.
     */
    @java.lang.Override
    public boolean hasCliEllCurDHPubKey() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes cliEllCurDHPubKey = 34;</code>
     * @return The cliEllCurDHPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliEllCurDHPubKey() {
      return cliEllCurDHPubKey_;
    }

    public static final int SRVGMTUNI_TIME_FIELD_NUMBER = 35;
    private long srvGMTUniTime35_ = 0L;
    // An alternative name is used for field "srvGMTUni_Time" because:
    //     capitalized name of field "srvGMTUniTime" conflicts with field "srvGMTUni_Time"
    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     * @return Whether the srvGMTUniTime field is set.
     */
    @java.lang.Override
    public boolean hasSrvGMTUniTime35() {
      return ((bitField1_ & 0x00000004) != 0);
    }
    /**
     * <code>optional uint64 srvGMTUni_Time = 35;</code>
     * @return The srvGMTUniTime.
     */
    @java.lang.Override
    public long getSrvGMTUniTime35() {
      return srvGMTUniTime35_;
    }

    public static final int CLIEXTCNT_FIELD_NUMBER = 36;
    private int cliExtCnt_ = 0;
    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     * @return Whether the cliExtCnt field is set.
     */
    @java.lang.Override
    public boolean hasCliExtCnt() {
      return ((bitField1_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 cliExtCnt = 36;</code>
     * @return The cliExtCnt.
     */
    @java.lang.Override
    public int getCliExtCnt() {
      return cliExtCnt_;
    }

    public static final int SRVEXTCNT_FIELD_NUMBER = 37;
    private int srvExtCnt_ = 0;
    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     * @return Whether the srvExtCnt field is set.
     */
    @java.lang.Override
    public boolean hasSrvExtCnt() {
      return ((bitField1_ & 0x00000010) != 0);
    }
    /**
     * <code>optional uint32 srvExtCnt = 37;</code>
     * @return The srvExtCnt.
     */
    @java.lang.Override
    public int getSrvExtCnt() {
      return srvExtCnt_;
    }

    public static final int CLIHANDSKLEN_FIELD_NUMBER = 38;
    private int cliHandSkLen_ = 0;
    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     * @return Whether the cliHandSkLen field is set.
     */
    @java.lang.Override
    public boolean hasCliHandSkLen() {
      return ((bitField1_ & 0x00000020) != 0);
    }
    /**
     * <code>optional uint32 cliHandSkLen = 38;</code>
     * @return The cliHandSkLen.
     */
    @java.lang.Override
    public int getCliHandSkLen() {
      return cliHandSkLen_;
    }

    public static final int SRVHANDSKLEN_FIELD_NUMBER = 39;
    private int srvHandSkLen_ = 0;
    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     * @return Whether the srvHandSkLen field is set.
     */
    @java.lang.Override
    public boolean hasSrvHandSkLen() {
      return ((bitField1_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 srvHandSkLen = 39;</code>
     * @return The srvHandSkLen.
     */
    @java.lang.Override
    public int getSrvHandSkLen() {
      return srvHandSkLen_;
    }

    public static final int CLIEXT_FIELD_NUMBER = 40;
    private com.google.protobuf.ByteString cliExt_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliExt = 40;</code>
     * @return Whether the cliExt field is set.
     */
    @java.lang.Override
    public boolean hasCliExt() {
      return ((bitField1_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes cliExt = 40;</code>
     * @return The cliExt.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliExt() {
      return cliExt_;
    }

    public static final int SRVEXT_FIELD_NUMBER = 41;
    private com.google.protobuf.ByteString srvExt_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvExt = 41;</code>
     * @return Whether the srvExt field is set.
     */
    @java.lang.Override
    public boolean hasSrvExt() {
      return ((bitField1_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes srvExt = 41;</code>
     * @return The srvExt.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvExt() {
      return srvExt_;
    }

    public static final int CLIEXTGREASE_FIELD_NUMBER = 42;
    private int cliExtGrease_ = 0;
    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     * @return Whether the cliExtGrease field is set.
     */
    @java.lang.Override
    public boolean hasCliExtGrease() {
      return ((bitField1_ & 0x00000200) != 0);
    }
    /**
     * <code>optional uint32 cliExtGrease = 42;</code>
     * @return The cliExtGrease.
     */
    @java.lang.Override
    public int getCliExtGrease() {
      return cliExtGrease_;
    }

    public static final int CLIJA3_FIELD_NUMBER = 43;
    private com.google.protobuf.ByteString cliJA3_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliJA3 = 43;</code>
     * @return Whether the cliJA3 field is set.
     */
    @java.lang.Override
    public boolean hasCliJA3() {
      return ((bitField1_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes cliJA3 = 43;</code>
     * @return The cliJA3.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliJA3() {
      return cliJA3_;
    }

    public static final int SRVJA3_FIELD_NUMBER = 44;
    private com.google.protobuf.ByteString srvJA3_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvJA3 = 44;</code>
     * @return Whether the srvJA3 field is set.
     */
    @java.lang.Override
    public boolean hasSrvJA3() {
      return ((bitField1_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes srvJA3 = 44;</code>
     * @return The srvJA3.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvJA3() {
      return srvJA3_;
    }

    public static final int CLISESSTICKET_FIELD_NUMBER = 45;
    private com.google.protobuf.ByteString cliSessTicket_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     * @return Whether the cliSessTicket field is set.
     */
    @java.lang.Override
    public boolean hasCliSessTicket() {
      return ((bitField1_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes cliSessTicket = 45;</code>
     * @return The cliSessTicket.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliSessTicket() {
      return cliSessTicket_;
    }

    public static final int SRVSESSTICKET_FIELD_NUMBER = 46;
    private com.google.protobuf.ByteString srvSessTicket_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     * @return Whether the srvSessTicket field is set.
     */
    @java.lang.Override
    public boolean hasSrvSessTicket() {
      return ((bitField1_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes srvSessTicket = 46;</code>
     * @return The srvSessTicket.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvSessTicket() {
      return srvSessTicket_;
    }

    public static final int AUTHTAG_FIELD_NUMBER = 47;
    private int authTag_ = 0;
    /**
     * <code>optional uint32 AuthTag = 47;</code>
     * @return Whether the authTag field is set.
     */
    @java.lang.Override
    public boolean hasAuthTag() {
      return ((bitField1_ & 0x00004000) != 0);
    }
    /**
     * <code>optional uint32 AuthTag = 47;</code>
     * @return The authTag.
     */
    @java.lang.Override
    public int getAuthTag() {
      return authTag_;
    }

    public static final int CLICERTCNT_FIELD_NUMBER = 48;
    private int cliCertCnt_ = 0;
    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     * @return Whether the cliCertCnt field is set.
     */
    @java.lang.Override
    public boolean hasCliCertCnt() {
      return ((bitField1_ & 0x00008000) != 0);
    }
    /**
     * <code>optional uint32 cliCertCnt = 48;</code>
     * @return The cliCertCnt.
     */
    @java.lang.Override
    public int getCliCertCnt() {
      return cliCertCnt_;
    }

    public static final int SRVCERTCNT_FIELD_NUMBER = 49;
    private int srvCertCnt_ = 0;
    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     * @return Whether the srvCertCnt field is set.
     */
    @java.lang.Override
    public boolean hasSrvCertCnt() {
      return ((bitField1_ & 0x00010000) != 0);
    }
    /**
     * <code>optional uint32 srvCertCnt = 49;</code>
     * @return The srvCertCnt.
     */
    @java.lang.Override
    public int getSrvCertCnt() {
      return srvCertCnt_;
    }

    public static final int ECGROUPSCLI_FIELD_NUMBER = 50;
    private com.google.protobuf.ByteString ecGroupsCli_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     * @return Whether the ecGroupsCli field is set.
     */
    @java.lang.Override
    public boolean hasEcGroupsCli() {
      return ((bitField1_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes ecGroupsCli = 50;</code>
     * @return The ecGroupsCli.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEcGroupsCli() {
      return ecGroupsCli_;
    }

    public static final int ECPOIFORBYSERV_FIELD_NUMBER = 51;
    private com.google.protobuf.ByteString ecPoiForByServ_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     * @return Whether the ecPoiForByServ field is set.
     */
    @java.lang.Override
    public boolean hasEcPoiForByServ() {
      return ((bitField1_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes ecPoiForByServ = 51;</code>
     * @return The ecPoiForByServ.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEcPoiForByServ() {
      return ecPoiForByServ_;
    }

    public static final int ETAGS_FIELD_NUMBER = 52;
    private com.google.protobuf.ByteString etags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes etags = 52;</code>
     * @return Whether the etags field is set.
     */
    @java.lang.Override
    public boolean hasEtags() {
      return ((bitField1_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes etags = 52;</code>
     * @return The etags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEtags() {
      return etags_;
    }

    public static final int TTAGS_FIELD_NUMBER = 53;
    private com.google.protobuf.ByteString ttags_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ttags = 53;</code>
     * @return Whether the ttags field is set.
     */
    @java.lang.Override
    public boolean hasTtags() {
      return ((bitField1_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes ttags = 53;</code>
     * @return The ttags.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTtags() {
      return ttags_;
    }

    public static final int CLISESIDLEN_FIELD_NUMBER = 54;
    private com.google.protobuf.ByteString cliSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     * @return Whether the cliSesIDLen field is set.
     */
    @java.lang.Override
    public boolean hasCliSesIDLen() {
      return ((bitField1_ & 0x00200000) != 0);
    }
    /**
     * <code>optional bytes cliSesIDLen = 54;</code>
     * @return The cliSesIDLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliSesIDLen() {
      return cliSesIDLen_;
    }

    public static final int SRVSESIDLEN_FIELD_NUMBER = 55;
    private com.google.protobuf.ByteString srvSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     * @return Whether the srvSesIDLen field is set.
     */
    @java.lang.Override
    public boolean hasSrvSesIDLen() {
      return ((bitField1_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes srvSesIDLen = 55;</code>
     * @return The srvSesIDLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvSesIDLen() {
      return srvSesIDLen_;
    }

    public static final int SRVKEYEXCLEN_FIELD_NUMBER = 56;
    private com.google.protobuf.ByteString srvKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     * @return Whether the srvKeyExcLen field is set.
     */
    @java.lang.Override
    public boolean hasSrvKeyExcLen() {
      return ((bitField1_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes srvKeyExcLen = 56;</code>
     * @return The srvKeyExcLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvKeyExcLen() {
      return srvKeyExcLen_;
    }

    public static final int ECDHCURTYPE_FIELD_NUMBER = 57;
    private com.google.protobuf.ByteString eCDHCurType_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     * @return Whether the eCDHCurType field is set.
     */
    @java.lang.Override
    public boolean hasECDHCurType() {
      return ((bitField1_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes ECDHCurType = 57;</code>
     * @return The eCDHCurType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getECDHCurType() {
      return eCDHCurType_;
    }

    public static final int ECDHSIG_FIELD_NUMBER = 58;
    private com.google.protobuf.ByteString eCDHSig_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ECDHSig = 58;</code>
     * @return Whether the eCDHSig field is set.
     */
    @java.lang.Override
    public boolean hasECDHSig() {
      return ((bitField1_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes ECDHSig = 58;</code>
     * @return The eCDHSig.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getECDHSig() {
      return eCDHSig_;
    }

    public static final int DHEPLEN_FIELD_NUMBER = 59;
    private com.google.protobuf.ByteString dHEPLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHEPLen = 59;</code>
     * @return Whether the dHEPLen field is set.
     */
    @java.lang.Override
    public boolean hasDHEPLen() {
      return ((bitField1_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes DHEPLen = 59;</code>
     * @return The dHEPLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHEPLen() {
      return dHEPLen_;
    }

    public static final int DHEGLEN_FIELD_NUMBER = 60;
    private com.google.protobuf.ByteString dHEGLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHEGLen = 60;</code>
     * @return Whether the dHEGLen field is set.
     */
    @java.lang.Override
    public boolean hasDHEGLen() {
      return ((bitField1_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes DHEGLen = 60;</code>
     * @return The dHEGLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHEGLen() {
      return dHEGLen_;
    }

    public static final int CLIKEYEXCLEN_FIELD_NUMBER = 61;
    private com.google.protobuf.ByteString cliKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     * @return Whether the cliKeyExcLen field is set.
     */
    @java.lang.Override
    public boolean hasCliKeyExcLen() {
      return ((bitField1_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes cliKeyExcLen = 61;</code>
     * @return The cliKeyExcLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliKeyExcLen() {
      return cliKeyExcLen_;
    }

    public static final int ENCPUBKEY_FIELD_NUMBER = 62;
    private com.google.protobuf.ByteString encPubKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes encPubKey = 62;</code>
     * @return Whether the encPubKey field is set.
     */
    @java.lang.Override
    public boolean hasEncPubKey() {
      return ((bitField1_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes encPubKey = 62;</code>
     * @return The encPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEncPubKey() {
      return encPubKey_;
    }

    public static final int ENCPUBKEYLEN_FIELD_NUMBER = 63;
    private com.google.protobuf.ByteString encPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     * @return Whether the encPubKeyLen field is set.
     */
    @java.lang.Override
    public boolean hasEncPubKeyLen() {
      return ((bitField1_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes encPubKeyLen = 63;</code>
     * @return The encPubKeyLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getEncPubKeyLen() {
      return encPubKeyLen_;
    }

    public static final int CLIEXTLEN_FIELD_NUMBER = 64;
    private com.google.protobuf.ByteString cliExtLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliExtLen = 64;</code>
     * @return Whether the cliExtLen field is set.
     */
    @java.lang.Override
    public boolean hasCliExtLen() {
      return ((bitField1_ & 0x80000000) != 0);
    }
    /**
     * <code>optional bytes cliExtLen = 64;</code>
     * @return The cliExtLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliExtLen() {
      return cliExtLen_;
    }

    public static final int SRVEXTLEN_FIELD_NUMBER = 65;
    private com.google.protobuf.ByteString srvExtLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvExtLen = 65;</code>
     * @return Whether the srvExtLen field is set.
     */
    @java.lang.Override
    public boolean hasSrvExtLen() {
      return ((bitField2_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bytes srvExtLen = 65;</code>
     * @return The srvExtLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvExtLen() {
      return srvExtLen_;
    }

    public static final int ECDHPUBKEYLEN_FIELD_NUMBER = 66;
    private com.google.protobuf.ByteString eCDHPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     * @return Whether the eCDHPubKeyLen field is set.
     */
    @java.lang.Override
    public boolean hasECDHPubKeyLen() {
      return ((bitField2_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes ECDHPubKeyLen = 66;</code>
     * @return The eCDHPubKeyLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getECDHPubKeyLen() {
      return eCDHPubKeyLen_;
    }

    public static final int NAMTYPE_FIELD_NUMBER = 67;
    private com.google.protobuf.ByteString namType_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes namType = 67;</code>
     * @return Whether the namType field is set.
     */
    @java.lang.Override
    public boolean hasNamType() {
      return ((bitField2_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes namType = 67;</code>
     * @return The namType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getNamType() {
      return namType_;
    }

    public static final int NAMLEN_FIELD_NUMBER = 68;
    private com.google.protobuf.ByteString namLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes namLen = 68;</code>
     * @return Whether the namLen field is set.
     */
    @java.lang.Override
    public boolean hasNamLen() {
      return ((bitField2_ & 0x00000008) != 0);
    }
    /**
     * <code>optional bytes namLen = 68;</code>
     * @return The namLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getNamLen() {
      return namLen_;
    }

    public static final int TICDAT_FIELD_NUMBER = 69;
    private com.google.protobuf.ByteString ticDat_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ticDat = 69;</code>
     * @return Whether the ticDat field is set.
     */
    @java.lang.Override
    public boolean hasTicDat() {
      return ((bitField2_ & 0x00000010) != 0);
    }
    /**
     * <code>optional bytes ticDat = 69;</code>
     * @return The ticDat.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getTicDat() {
      return ticDat_;
    }

    public static final int SRVCIPSUI_FIELD_NUMBER = 70;
    private com.google.protobuf.ByteString srvCipSui_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvCipSui = 70;</code>
     * @return Whether the srvCipSui field is set.
     */
    @java.lang.Override
    public boolean hasSrvCipSui() {
      return ((bitField2_ & 0x00000020) != 0);
    }
    /**
     * <code>optional bytes srvCipSui = 70;</code>
     * @return The srvCipSui.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvCipSui() {
      return srvCipSui_;
    }

    public static final int CIPSUINUM_FIELD_NUMBER = 71;
    private int cipSuiNum_ = 0;
    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     * @return Whether the cipSuiNum field is set.
     */
    @java.lang.Override
    public boolean hasCipSuiNum() {
      return ((bitField2_ & 0x00000040) != 0);
    }
    /**
     * <code>optional uint32 cipSuiNum = 71;</code>
     * @return The cipSuiNum.
     */
    @java.lang.Override
    public int getCipSuiNum() {
      return cipSuiNum_;
    }

    public static final int ECDHSIGHASH_FIELD_NUMBER = 72;
    private com.google.protobuf.ByteString eCDHSigHash_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     * @return Whether the eCDHSigHash field is set.
     */
    @java.lang.Override
    public boolean hasECDHSigHash() {
      return ((bitField2_ & 0x00000080) != 0);
    }
    /**
     * <code>optional bytes ECDHSigHash = 72;</code>
     * @return The eCDHSigHash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getECDHSigHash() {
      return eCDHSigHash_;
    }

    public static final int DHESIGHASH_FIELD_NUMBER = 73;
    private com.google.protobuf.ByteString dHESigHash_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHESigHash = 73;</code>
     * @return Whether the dHESigHash field is set.
     */
    @java.lang.Override
    public boolean hasDHESigHash() {
      return ((bitField2_ & 0x00000100) != 0);
    }
    /**
     * <code>optional bytes DHESigHash = 73;</code>
     * @return The dHESigHash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHESigHash() {
      return dHESigHash_;
    }

    public static final int RSASIGHASH_FIELD_NUMBER = 74;
    private com.google.protobuf.ByteString rSASigHash_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes RSASigHash = 74;</code>
     * @return Whether the rSASigHash field is set.
     */
    @java.lang.Override
    public boolean hasRSASigHash() {
      return ((bitField2_ & 0x00000200) != 0);
    }
    /**
     * <code>optional bytes RSASigHash = 74;</code>
     * @return The rSASigHash.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRSASigHash() {
      return rSASigHash_;
    }

    public static final int GREASEFLAG_FIELD_NUMBER = 75;
    private com.google.protobuf.ByteString greaseFlag_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes greaseFlag = 75;</code>
     * @return Whether the greaseFlag field is set.
     */
    @java.lang.Override
    public boolean hasGreaseFlag() {
      return ((bitField2_ & 0x00000400) != 0);
    }
    /**
     * <code>optional bytes greaseFlag = 75;</code>
     * @return The greaseFlag.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getGreaseFlag() {
      return greaseFlag_;
    }

    public static final int RSAMODLEN_FIELD_NUMBER = 76;
    private com.google.protobuf.ByteString rSAModLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes RSAModLen = 76;</code>
     * @return Whether the rSAModLen field is set.
     */
    @java.lang.Override
    public boolean hasRSAModLen() {
      return ((bitField2_ & 0x00000800) != 0);
    }
    /**
     * <code>optional bytes RSAModLen = 76;</code>
     * @return The rSAModLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRSAModLen() {
      return rSAModLen_;
    }

    public static final int RSAEXPLEN_FIELD_NUMBER = 77;
    private com.google.protobuf.ByteString rSAExpLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     * @return Whether the rSAExpLen field is set.
     */
    @java.lang.Override
    public boolean hasRSAExpLen() {
      return ((bitField2_ & 0x00001000) != 0);
    }
    /**
     * <code>optional bytes RSAExpLen = 77;</code>
     * @return The rSAExpLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRSAExpLen() {
      return rSAExpLen_;
    }

    public static final int RSASIG_FIELD_NUMBER = 78;
    private com.google.protobuf.ByteString rSASig_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes RSASig = 78;</code>
     * @return Whether the rSASig field is set.
     */
    @java.lang.Override
    public boolean hasRSASig() {
      return ((bitField2_ & 0x00002000) != 0);
    }
    /**
     * <code>optional bytes RSASig = 78;</code>
     * @return The rSASig.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getRSASig() {
      return rSASig_;
    }

    public static final int DHESIG_FIELD_NUMBER = 79;
    private com.google.protobuf.ByteString dHESig_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHESig = 79;</code>
     * @return Whether the dHESig field is set.
     */
    @java.lang.Override
    public boolean hasDHESig() {
      return ((bitField2_ & 0x00004000) != 0);
    }
    /**
     * <code>optional bytes DHESig = 79;</code>
     * @return The dHESig.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHESig() {
      return dHESig_;
    }

    public static final int DHEPUBKEYLEN_FIELD_NUMBER = 80;
    private com.google.protobuf.ByteString dHEPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     * @return Whether the dHEPubKeyLen field is set.
     */
    @java.lang.Override
    public boolean hasDHEPubKeyLen() {
      return ((bitField2_ & 0x00008000) != 0);
    }
    /**
     * <code>optional bytes DHEPubKeyLen = 80;</code>
     * @return The dHEPubKeyLen.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHEPubKeyLen() {
      return dHEPubKeyLen_;
    }

    public static final int DHEPUBKEY_FIELD_NUMBER = 81;
    private com.google.protobuf.ByteString dHEPubKey_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     * @return Whether the dHEPubKey field is set.
     */
    @java.lang.Override
    public boolean hasDHEPubKey() {
      return ((bitField2_ & 0x00010000) != 0);
    }
    /**
     * <code>optional bytes DHEPubKey = 81;</code>
     * @return The dHEPubKey.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getDHEPubKey() {
      return dHEPubKey_;
    }

    public static final int SIGALGTYPE_FIELD_NUMBER = 82;
    private com.google.protobuf.ByteString sigAlgType_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes SigAlgType = 82;</code>
     * @return Whether the sigAlgType field is set.
     */
    @java.lang.Override
    public boolean hasSigAlgType() {
      return ((bitField2_ & 0x00020000) != 0);
    }
    /**
     * <code>optional bytes SigAlgType = 82;</code>
     * @return The sigAlgType.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSigAlgType() {
      return sigAlgType_;
    }

    public static final int SIGALG_FIELD_NUMBER = 83;
    private com.google.protobuf.ByteString sigAlg_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes sigAlg = 83;</code>
     * @return Whether the sigAlg field is set.
     */
    @java.lang.Override
    public boolean hasSigAlg() {
      return ((bitField2_ & 0x00040000) != 0);
    }
    /**
     * <code>optional bytes sigAlg = 83;</code>
     * @return The sigAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSigAlg() {
      return sigAlg_;
    }

    public static final int SIGHASHALG_FIELD_NUMBER = 84;
    private com.google.protobuf.ByteString sigHashAlg_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     * @return Whether the sigHashAlg field is set.
     */
    @java.lang.Override
    public boolean hasSigHashAlg() {
      return ((bitField2_ & 0x00080000) != 0);
    }
    /**
     * <code>optional bytes SigHashAlg = 84;</code>
     * @return The sigHashAlg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSigHashAlg() {
      return sigHashAlg_;
    }

    public static final int JOY_FIELD_NUMBER = 85;
    private com.google.protobuf.ByteString jOY_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes JOY = 85;</code>
     * @return Whether the jOY field is set.
     */
    @java.lang.Override
    public boolean hasJOY() {
      return ((bitField2_ & 0x00100000) != 0);
    }
    /**
     * <code>optional bytes JOY = 85;</code>
     * @return The jOY.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getJOY() {
      return jOY_;
    }

    public static final int JOYS_FIELD_NUMBER = 86;
    private com.google.protobuf.ByteString jOYS_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes JOYS = 86;</code>
     * @return Whether the jOYS field is set.
     */
    @java.lang.Override
    public boolean hasJOYS() {
      return ((bitField2_ & 0x00200000) != 0);
    }
    /**
     * <code>optional bytes JOYS = 86;</code>
     * @return The jOYS.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getJOYS() {
      return jOYS_;
    }

    public static final int STARTTLS_FIELD_NUMBER = 87;
    private com.google.protobuf.ByteString sTARTTLS_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes STARTTLS = 87;</code>
     * @return Whether the sTARTTLS field is set.
     */
    @java.lang.Override
    public boolean hasSTARTTLS() {
      return ((bitField2_ & 0x00400000) != 0);
    }
    /**
     * <code>optional bytes STARTTLS = 87;</code>
     * @return The sTARTTLS.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSTARTTLS() {
      return sTARTTLS_;
    }

    public static final int CERTNONFLAG_FIELD_NUMBER = 88;
    private com.google.protobuf.ByteString certNonFlag_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certNonFlag = 88;</code>
     * @return Whether the certNonFlag field is set.
     */
    @java.lang.Override
    public boolean hasCertNonFlag() {
      return ((bitField2_ & 0x00800000) != 0);
    }
    /**
     * <code>optional bytes certNonFlag = 88;</code>
     * @return The certNonFlag.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCertNonFlag() {
      return certNonFlag_;
    }

    public static final int JOYFP_FIELD_NUMBER = 89;
    private com.google.protobuf.ByteString joyFp_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes JoyFp = 89;</code>
     * @return Whether the joyFp field is set.
     */
    @java.lang.Override
    public boolean hasJoyFp() {
      return ((bitField2_ & 0x01000000) != 0);
    }
    /**
     * <code>optional bytes JoyFp = 89;</code>
     * @return The joyFp.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getJoyFp() {
      return joyFp_;
    }

    public static final int CERTINTACTFLAG_FIELD_NUMBER = 90;
    private com.google.protobuf.ByteString certIntactFlag_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     * @return Whether the certIntactFlag field is set.
     */
    @java.lang.Override
    public boolean hasCertIntactFlag() {
      return ((bitField2_ & 0x02000000) != 0);
    }
    /**
     * <code>optional bytes certIntactFlag = 90;</code>
     * @return The certIntactFlag.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCertIntactFlag() {
      return certIntactFlag_;
    }

    public static final int CERTPATH_FIELD_NUMBER = 91;
    private com.google.protobuf.ByteString certPath_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes certPath = 91;</code>
     * @return Whether the certPath field is set.
     */
    @java.lang.Override
    public boolean hasCertPath() {
      return ((bitField2_ & 0x04000000) != 0);
    }
    /**
     * <code>optional bytes certPath = 91;</code>
     * @return The certPath.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCertPath() {
      return certPath_;
    }

    public static final int SESSSECFLAG_FIELD_NUMBER = 92;
    private com.google.protobuf.ByteString sessSecFlag_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     * @return Whether the sessSecFlag field is set.
     */
    @java.lang.Override
    public boolean hasSessSecFlag() {
      return ((bitField2_ & 0x08000000) != 0);
    }
    /**
     * <code>optional bytes sessSecFlag = 92;</code>
     * @return The sessSecFlag.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSessSecFlag() {
      return sessSecFlag_;
    }

    public static final int FULLTEXT_FIELD_NUMBER = 93;
    private com.google.protobuf.ByteString fullText_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes fullText = 93;</code>
     * @return Whether the fullText field is set.
     */
    @java.lang.Override
    public boolean hasFullText() {
      return ((bitField2_ & 0x10000000) != 0);
    }
    /**
     * <code>optional bytes fullText = 93;</code>
     * @return The fullText.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getFullText() {
      return fullText_;
    }

    public static final int CLICERTHASHES_FIELD_NUMBER = 94;
    private com.google.protobuf.ByteString cliCertHashes_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     * @return Whether the cliCertHashes field is set.
     */
    @java.lang.Override
    public boolean hasCliCertHashes() {
      return ((bitField2_ & 0x20000000) != 0);
    }
    /**
     * <code>optional bytes cliCertHashes = 94;</code>
     * @return The cliCertHashes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getCliCertHashes() {
      return cliCertHashes_;
    }

    public static final int SRVCERTHASHES_FIELD_NUMBER = 95;
    private com.google.protobuf.ByteString srvCertHashes_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     * @return Whether the srvCertHashes field is set.
     */
    @java.lang.Override
    public boolean hasSrvCertHashes() {
      return ((bitField2_ & 0x40000000) != 0);
    }
    /**
     * <code>optional bytes srvCertHashes = 95;</code>
     * @return The srvCertHashes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getSrvCertHashes() {
      return srvCertHashes_;
    }

    public static final int CLICERTNUM_FIELD_NUMBER = 96;
    private int cliCertNum_ = 0;
    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     * @return Whether the cliCertNum field is set.
     */
    @java.lang.Override
    public boolean hasCliCertNum() {
      return ((bitField2_ & 0x80000000) != 0);
    }
    /**
     * <code>optional uint32 cliCertNum = 96;</code>
     * @return The cliCertNum.
     */
    @java.lang.Override
    public int getCliCertNum() {
      return cliCertNum_;
    }

    public static final int SRVCERTNUM_FIELD_NUMBER = 97;
    private int srvCertNum_ = 0;
    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     * @return Whether the srvCertNum field is set.
     */
    @java.lang.Override
    public boolean hasSrvCertNum() {
      return ((bitField3_ & 0x00000001) != 0);
    }
    /**
     * <code>optional uint32 srvCertNum = 97;</code>
     * @return The srvCertNum.
     */
    @java.lang.Override
    public int getSrvCertNum() {
      return srvCertNum_;
    }

    public static final int CERTEXIST_FIELD_NUMBER = 98;
    private int certExist_ = 0;
    /**
     * <code>optional uint32 certExist = 98;</code>
     * @return Whether the certExist field is set.
     */
    @java.lang.Override
    public boolean hasCertExist() {
      return ((bitField3_ & 0x00000002) != 0);
    }
    /**
     * <code>optional uint32 certExist = 98;</code>
     * @return The certExist.
     */
    @java.lang.Override
    public int getCertExist() {
      return certExist_;
    }

    public static final int EXTENDECGROUPSCLIENT_FIELD_NUMBER = 99;
    private com.google.protobuf.ByteString extendEcGroupsClient_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     * @return Whether the extendEcGroupsClient field is set.
     */
    @java.lang.Override
    public boolean hasExtendEcGroupsClient() {
      return ((bitField3_ & 0x00000004) != 0);
    }
    /**
     * <code>optional bytes extendEcGroupsClient = 99;</code>
     * @return The extendEcGroupsClient.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getExtendEcGroupsClient() {
      return extendEcGroupsClient_;
    }

    public static final int LEAFCERTDAYSREMAINING_FIELD_NUMBER = 100;
    private int leafCertDaysRemaining_ = 0;
    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     * @return Whether the leafCertDaysRemaining field is set.
     */
    @java.lang.Override
    public boolean hasLeafCertDaysRemaining() {
      return ((bitField3_ & 0x00000008) != 0);
    }
    /**
     * <code>optional uint32 leafCertDaysRemaining = 100;</code>
     * @return The leafCertDaysRemaining.
     */
    @java.lang.Override
    public int getLeafCertDaysRemaining() {
      return leafCertDaysRemaining_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt32(1, conType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, aleLev_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt32(3, aleDes_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt32(4, handShaType_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeUInt32(5, cliVer_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeUInt64(6, cliGMTUniTime_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeBytes(7, cliRand_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeBytes(8, cliSesID_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeBytes(9, cliCipSui_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeBytes(10, cliComMet_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeUInt32(11, srvVer_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeBytes(12, srvName_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt32(13, srvNameAttr_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeUInt64(14, srvGMTUniTime14_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeBytes(15, srvRand_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeBytes(16, srvSesID_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeBytes(17, srvComprMet_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeUInt32(18, srvCertLen_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeUInt32(19, certResType_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeUInt32(20, cliCertLen_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeBytes(21, rSAModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeUInt64(22, rSAExpOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeBytes(23, dHModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeBytes(24, dHGenOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeBytes(25, srvDHPubKey_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeBytes(26, preMasKeyEncryByRSA_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeBytes(27, cliDHPubKey_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeUInt32(28, extTypeInSSL_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeUInt32(29, cliEllCurPoiFor_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeUInt32(30, cliEllCur_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeUInt32(31, srvEllCurPoiFor_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeUInt32(32, srvEllCur_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeBytes(33, srvEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeBytes(34, cliEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        output.writeUInt64(35, srvGMTUniTime35_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        output.writeUInt32(36, cliExtCnt_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        output.writeUInt32(37, srvExtCnt_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        output.writeUInt32(38, cliHandSkLen_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        output.writeUInt32(39, srvHandSkLen_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        output.writeBytes(40, cliExt_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        output.writeBytes(41, srvExt_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        output.writeUInt32(42, cliExtGrease_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        output.writeBytes(43, cliJA3_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        output.writeBytes(44, srvJA3_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        output.writeBytes(45, cliSessTicket_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        output.writeBytes(46, srvSessTicket_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        output.writeUInt32(47, authTag_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        output.writeUInt32(48, cliCertCnt_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        output.writeUInt32(49, srvCertCnt_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        output.writeBytes(50, ecGroupsCli_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        output.writeBytes(51, ecPoiForByServ_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        output.writeBytes(52, etags_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        output.writeBytes(53, ttags_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        output.writeBytes(54, cliSesIDLen_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        output.writeBytes(55, srvSesIDLen_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        output.writeBytes(56, srvKeyExcLen_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        output.writeBytes(57, eCDHCurType_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        output.writeBytes(58, eCDHSig_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        output.writeBytes(59, dHEPLen_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        output.writeBytes(60, dHEGLen_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        output.writeBytes(61, cliKeyExcLen_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        output.writeBytes(62, encPubKey_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        output.writeBytes(63, encPubKeyLen_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        output.writeBytes(64, cliExtLen_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        output.writeBytes(65, srvExtLen_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        output.writeBytes(66, eCDHPubKeyLen_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        output.writeBytes(67, namType_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        output.writeBytes(68, namLen_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        output.writeBytes(69, ticDat_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        output.writeBytes(70, srvCipSui_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        output.writeUInt32(71, cipSuiNum_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        output.writeBytes(72, eCDHSigHash_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        output.writeBytes(73, dHESigHash_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        output.writeBytes(74, rSASigHash_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        output.writeBytes(75, greaseFlag_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        output.writeBytes(76, rSAModLen_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        output.writeBytes(77, rSAExpLen_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        output.writeBytes(78, rSASig_);
      }
      if (((bitField2_ & 0x00004000) != 0)) {
        output.writeBytes(79, dHESig_);
      }
      if (((bitField2_ & 0x00008000) != 0)) {
        output.writeBytes(80, dHEPubKeyLen_);
      }
      if (((bitField2_ & 0x00010000) != 0)) {
        output.writeBytes(81, dHEPubKey_);
      }
      if (((bitField2_ & 0x00020000) != 0)) {
        output.writeBytes(82, sigAlgType_);
      }
      if (((bitField2_ & 0x00040000) != 0)) {
        output.writeBytes(83, sigAlg_);
      }
      if (((bitField2_ & 0x00080000) != 0)) {
        output.writeBytes(84, sigHashAlg_);
      }
      if (((bitField2_ & 0x00100000) != 0)) {
        output.writeBytes(85, jOY_);
      }
      if (((bitField2_ & 0x00200000) != 0)) {
        output.writeBytes(86, jOYS_);
      }
      if (((bitField2_ & 0x00400000) != 0)) {
        output.writeBytes(87, sTARTTLS_);
      }
      if (((bitField2_ & 0x00800000) != 0)) {
        output.writeBytes(88, certNonFlag_);
      }
      if (((bitField2_ & 0x01000000) != 0)) {
        output.writeBytes(89, joyFp_);
      }
      if (((bitField2_ & 0x02000000) != 0)) {
        output.writeBytes(90, certIntactFlag_);
      }
      if (((bitField2_ & 0x04000000) != 0)) {
        output.writeBytes(91, certPath_);
      }
      if (((bitField2_ & 0x08000000) != 0)) {
        output.writeBytes(92, sessSecFlag_);
      }
      if (((bitField2_ & 0x10000000) != 0)) {
        output.writeBytes(93, fullText_);
      }
      if (((bitField2_ & 0x20000000) != 0)) {
        output.writeBytes(94, cliCertHashes_);
      }
      if (((bitField2_ & 0x40000000) != 0)) {
        output.writeBytes(95, srvCertHashes_);
      }
      if (((bitField2_ & 0x80000000) != 0)) {
        output.writeUInt32(96, cliCertNum_);
      }
      if (((bitField3_ & 0x00000001) != 0)) {
        output.writeUInt32(97, srvCertNum_);
      }
      if (((bitField3_ & 0x00000002) != 0)) {
        output.writeUInt32(98, certExist_);
      }
      if (((bitField3_ & 0x00000004) != 0)) {
        output.writeBytes(99, extendEcGroupsClient_);
      }
      if (((bitField3_ & 0x00000008) != 0)) {
        output.writeUInt32(100, leafCertDaysRemaining_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, conType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, aleLev_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, aleDes_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, handShaType_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, cliVer_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, cliGMTUniTime_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, cliRand_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, cliSesID_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, cliCipSui_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(10, cliComMet_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(11, srvVer_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(12, srvName_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, srvNameAttr_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(14, srvGMTUniTime14_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(15, srvRand_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(16, srvSesID_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(17, srvComprMet_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(18, srvCertLen_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(19, certResType_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(20, cliCertLen_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(21, rSAModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(22, rSAExpOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, dHModOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(24, dHGenOfSrvKeyExc_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(25, srvDHPubKey_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(26, preMasKeyEncryByRSA_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(27, cliDHPubKey_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(28, extTypeInSSL_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(29, cliEllCurPoiFor_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(30, cliEllCur_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(31, srvEllCurPoiFor_);
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(32, srvEllCur_);
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(33, srvEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(34, cliEllCurDHPubKey_);
      }
      if (((bitField1_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(35, srvGMTUniTime35_);
      }
      if (((bitField1_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(36, cliExtCnt_);
      }
      if (((bitField1_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(37, srvExtCnt_);
      }
      if (((bitField1_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(38, cliHandSkLen_);
      }
      if (((bitField1_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(39, srvHandSkLen_);
      }
      if (((bitField1_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(40, cliExt_);
      }
      if (((bitField1_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(41, srvExt_);
      }
      if (((bitField1_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(42, cliExtGrease_);
      }
      if (((bitField1_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(43, cliJA3_);
      }
      if (((bitField1_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(44, srvJA3_);
      }
      if (((bitField1_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(45, cliSessTicket_);
      }
      if (((bitField1_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(46, srvSessTicket_);
      }
      if (((bitField1_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(47, authTag_);
      }
      if (((bitField1_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(48, cliCertCnt_);
      }
      if (((bitField1_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(49, srvCertCnt_);
      }
      if (((bitField1_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(50, ecGroupsCli_);
      }
      if (((bitField1_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(51, ecPoiForByServ_);
      }
      if (((bitField1_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(52, etags_);
      }
      if (((bitField1_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(53, ttags_);
      }
      if (((bitField1_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(54, cliSesIDLen_);
      }
      if (((bitField1_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(55, srvSesIDLen_);
      }
      if (((bitField1_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(56, srvKeyExcLen_);
      }
      if (((bitField1_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(57, eCDHCurType_);
      }
      if (((bitField1_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(58, eCDHSig_);
      }
      if (((bitField1_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(59, dHEPLen_);
      }
      if (((bitField1_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(60, dHEGLen_);
      }
      if (((bitField1_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(61, cliKeyExcLen_);
      }
      if (((bitField1_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(62, encPubKey_);
      }
      if (((bitField1_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(63, encPubKeyLen_);
      }
      if (((bitField1_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(64, cliExtLen_);
      }
      if (((bitField2_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(65, srvExtLen_);
      }
      if (((bitField2_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(66, eCDHPubKeyLen_);
      }
      if (((bitField2_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(67, namType_);
      }
      if (((bitField2_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(68, namLen_);
      }
      if (((bitField2_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(69, ticDat_);
      }
      if (((bitField2_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(70, srvCipSui_);
      }
      if (((bitField2_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(71, cipSuiNum_);
      }
      if (((bitField2_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(72, eCDHSigHash_);
      }
      if (((bitField2_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(73, dHESigHash_);
      }
      if (((bitField2_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(74, rSASigHash_);
      }
      if (((bitField2_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(75, greaseFlag_);
      }
      if (((bitField2_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(76, rSAModLen_);
      }
      if (((bitField2_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(77, rSAExpLen_);
      }
      if (((bitField2_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(78, rSASig_);
      }
      if (((bitField2_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(79, dHESig_);
      }
      if (((bitField2_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(80, dHEPubKeyLen_);
      }
      if (((bitField2_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(81, dHEPubKey_);
      }
      if (((bitField2_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(82, sigAlgType_);
      }
      if (((bitField2_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(83, sigAlg_);
      }
      if (((bitField2_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(84, sigHashAlg_);
      }
      if (((bitField2_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(85, jOY_);
      }
      if (((bitField2_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(86, jOYS_);
      }
      if (((bitField2_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(87, sTARTTLS_);
      }
      if (((bitField2_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(88, certNonFlag_);
      }
      if (((bitField2_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(89, joyFp_);
      }
      if (((bitField2_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(90, certIntactFlag_);
      }
      if (((bitField2_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(91, certPath_);
      }
      if (((bitField2_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(92, sessSecFlag_);
      }
      if (((bitField2_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(93, fullText_);
      }
      if (((bitField2_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(94, cliCertHashes_);
      }
      if (((bitField2_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(95, srvCertHashes_);
      }
      if (((bitField2_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(96, cliCertNum_);
      }
      if (((bitField3_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(97, srvCertNum_);
      }
      if (((bitField3_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(98, certExist_);
      }
      if (((bitField3_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(99, extendEcGroupsClient_);
      }
      if (((bitField3_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(100, leafCertDaysRemaining_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof SslTlsInfo.Ssl_TlsInfo)) {
        return super.equals(obj);
      }
      SslTlsInfo.Ssl_TlsInfo other = (SslTlsInfo.Ssl_TlsInfo) obj;

      if (hasConType() != other.hasConType()) return false;
      if (hasConType()) {
        if (getConType()
            != other.getConType()) return false;
      }
      if (hasAleLev() != other.hasAleLev()) return false;
      if (hasAleLev()) {
        if (getAleLev()
            != other.getAleLev()) return false;
      }
      if (hasAleDes() != other.hasAleDes()) return false;
      if (hasAleDes()) {
        if (getAleDes()
            != other.getAleDes()) return false;
      }
      if (hasHandShaType() != other.hasHandShaType()) return false;
      if (hasHandShaType()) {
        if (getHandShaType()
            != other.getHandShaType()) return false;
      }
      if (hasCliVer() != other.hasCliVer()) return false;
      if (hasCliVer()) {
        if (getCliVer()
            != other.getCliVer()) return false;
      }
      if (hasCliGMTUniTime() != other.hasCliGMTUniTime()) return false;
      if (hasCliGMTUniTime()) {
        if (getCliGMTUniTime()
            != other.getCliGMTUniTime()) return false;
      }
      if (hasCliRand() != other.hasCliRand()) return false;
      if (hasCliRand()) {
        if (!getCliRand()
            .equals(other.getCliRand())) return false;
      }
      if (hasCliSesID() != other.hasCliSesID()) return false;
      if (hasCliSesID()) {
        if (!getCliSesID()
            .equals(other.getCliSesID())) return false;
      }
      if (hasCliCipSui() != other.hasCliCipSui()) return false;
      if (hasCliCipSui()) {
        if (!getCliCipSui()
            .equals(other.getCliCipSui())) return false;
      }
      if (hasCliComMet() != other.hasCliComMet()) return false;
      if (hasCliComMet()) {
        if (!getCliComMet()
            .equals(other.getCliComMet())) return false;
      }
      if (hasSrvVer() != other.hasSrvVer()) return false;
      if (hasSrvVer()) {
        if (getSrvVer()
            != other.getSrvVer()) return false;
      }
      if (hasSrvName() != other.hasSrvName()) return false;
      if (hasSrvName()) {
        if (!getSrvName()
            .equals(other.getSrvName())) return false;
      }
      if (hasSrvNameAttr() != other.hasSrvNameAttr()) return false;
      if (hasSrvNameAttr()) {
        if (getSrvNameAttr()
            != other.getSrvNameAttr()) return false;
      }
      if (hasSrvGMTUniTime14() != other.hasSrvGMTUniTime14()) return false;
      if (hasSrvGMTUniTime14()) {
        if (getSrvGMTUniTime14()
            != other.getSrvGMTUniTime14()) return false;
      }
      if (hasSrvRand() != other.hasSrvRand()) return false;
      if (hasSrvRand()) {
        if (!getSrvRand()
            .equals(other.getSrvRand())) return false;
      }
      if (hasSrvSesID() != other.hasSrvSesID()) return false;
      if (hasSrvSesID()) {
        if (!getSrvSesID()
            .equals(other.getSrvSesID())) return false;
      }
      if (hasSrvComprMet() != other.hasSrvComprMet()) return false;
      if (hasSrvComprMet()) {
        if (!getSrvComprMet()
            .equals(other.getSrvComprMet())) return false;
      }
      if (hasSrvCertLen() != other.hasSrvCertLen()) return false;
      if (hasSrvCertLen()) {
        if (getSrvCertLen()
            != other.getSrvCertLen()) return false;
      }
      if (hasCertResType() != other.hasCertResType()) return false;
      if (hasCertResType()) {
        if (getCertResType()
            != other.getCertResType()) return false;
      }
      if (hasCliCertLen() != other.hasCliCertLen()) return false;
      if (hasCliCertLen()) {
        if (getCliCertLen()
            != other.getCliCertLen()) return false;
      }
      if (hasRSAModOfSrvKeyExc() != other.hasRSAModOfSrvKeyExc()) return false;
      if (hasRSAModOfSrvKeyExc()) {
        if (!getRSAModOfSrvKeyExc()
            .equals(other.getRSAModOfSrvKeyExc())) return false;
      }
      if (hasRSAExpOfSrvKeyExc() != other.hasRSAExpOfSrvKeyExc()) return false;
      if (hasRSAExpOfSrvKeyExc()) {
        if (getRSAExpOfSrvKeyExc()
            != other.getRSAExpOfSrvKeyExc()) return false;
      }
      if (hasDHModOfSrvKeyExc() != other.hasDHModOfSrvKeyExc()) return false;
      if (hasDHModOfSrvKeyExc()) {
        if (!getDHModOfSrvKeyExc()
            .equals(other.getDHModOfSrvKeyExc())) return false;
      }
      if (hasDHGenOfSrvKeyExc() != other.hasDHGenOfSrvKeyExc()) return false;
      if (hasDHGenOfSrvKeyExc()) {
        if (!getDHGenOfSrvKeyExc()
            .equals(other.getDHGenOfSrvKeyExc())) return false;
      }
      if (hasSrvDHPubKey() != other.hasSrvDHPubKey()) return false;
      if (hasSrvDHPubKey()) {
        if (!getSrvDHPubKey()
            .equals(other.getSrvDHPubKey())) return false;
      }
      if (hasPreMasKeyEncryByRSA() != other.hasPreMasKeyEncryByRSA()) return false;
      if (hasPreMasKeyEncryByRSA()) {
        if (!getPreMasKeyEncryByRSA()
            .equals(other.getPreMasKeyEncryByRSA())) return false;
      }
      if (hasCliDHPubKey() != other.hasCliDHPubKey()) return false;
      if (hasCliDHPubKey()) {
        if (!getCliDHPubKey()
            .equals(other.getCliDHPubKey())) return false;
      }
      if (hasExtTypeInSSL() != other.hasExtTypeInSSL()) return false;
      if (hasExtTypeInSSL()) {
        if (getExtTypeInSSL()
            != other.getExtTypeInSSL()) return false;
      }
      if (hasCliEllCurPoiFor() != other.hasCliEllCurPoiFor()) return false;
      if (hasCliEllCurPoiFor()) {
        if (getCliEllCurPoiFor()
            != other.getCliEllCurPoiFor()) return false;
      }
      if (hasCliEllCur() != other.hasCliEllCur()) return false;
      if (hasCliEllCur()) {
        if (getCliEllCur()
            != other.getCliEllCur()) return false;
      }
      if (hasSrvEllCurPoiFor() != other.hasSrvEllCurPoiFor()) return false;
      if (hasSrvEllCurPoiFor()) {
        if (getSrvEllCurPoiFor()
            != other.getSrvEllCurPoiFor()) return false;
      }
      if (hasSrvEllCur() != other.hasSrvEllCur()) return false;
      if (hasSrvEllCur()) {
        if (getSrvEllCur()
            != other.getSrvEllCur()) return false;
      }
      if (hasSrvEllCurDHPubKey() != other.hasSrvEllCurDHPubKey()) return false;
      if (hasSrvEllCurDHPubKey()) {
        if (!getSrvEllCurDHPubKey()
            .equals(other.getSrvEllCurDHPubKey())) return false;
      }
      if (hasCliEllCurDHPubKey() != other.hasCliEllCurDHPubKey()) return false;
      if (hasCliEllCurDHPubKey()) {
        if (!getCliEllCurDHPubKey()
            .equals(other.getCliEllCurDHPubKey())) return false;
      }
      if (hasSrvGMTUniTime35() != other.hasSrvGMTUniTime35()) return false;
      if (hasSrvGMTUniTime35()) {
        if (getSrvGMTUniTime35()
            != other.getSrvGMTUniTime35()) return false;
      }
      if (hasCliExtCnt() != other.hasCliExtCnt()) return false;
      if (hasCliExtCnt()) {
        if (getCliExtCnt()
            != other.getCliExtCnt()) return false;
      }
      if (hasSrvExtCnt() != other.hasSrvExtCnt()) return false;
      if (hasSrvExtCnt()) {
        if (getSrvExtCnt()
            != other.getSrvExtCnt()) return false;
      }
      if (hasCliHandSkLen() != other.hasCliHandSkLen()) return false;
      if (hasCliHandSkLen()) {
        if (getCliHandSkLen()
            != other.getCliHandSkLen()) return false;
      }
      if (hasSrvHandSkLen() != other.hasSrvHandSkLen()) return false;
      if (hasSrvHandSkLen()) {
        if (getSrvHandSkLen()
            != other.getSrvHandSkLen()) return false;
      }
      if (hasCliExt() != other.hasCliExt()) return false;
      if (hasCliExt()) {
        if (!getCliExt()
            .equals(other.getCliExt())) return false;
      }
      if (hasSrvExt() != other.hasSrvExt()) return false;
      if (hasSrvExt()) {
        if (!getSrvExt()
            .equals(other.getSrvExt())) return false;
      }
      if (hasCliExtGrease() != other.hasCliExtGrease()) return false;
      if (hasCliExtGrease()) {
        if (getCliExtGrease()
            != other.getCliExtGrease()) return false;
      }
      if (hasCliJA3() != other.hasCliJA3()) return false;
      if (hasCliJA3()) {
        if (!getCliJA3()
            .equals(other.getCliJA3())) return false;
      }
      if (hasSrvJA3() != other.hasSrvJA3()) return false;
      if (hasSrvJA3()) {
        if (!getSrvJA3()
            .equals(other.getSrvJA3())) return false;
      }
      if (hasCliSessTicket() != other.hasCliSessTicket()) return false;
      if (hasCliSessTicket()) {
        if (!getCliSessTicket()
            .equals(other.getCliSessTicket())) return false;
      }
      if (hasSrvSessTicket() != other.hasSrvSessTicket()) return false;
      if (hasSrvSessTicket()) {
        if (!getSrvSessTicket()
            .equals(other.getSrvSessTicket())) return false;
      }
      if (hasAuthTag() != other.hasAuthTag()) return false;
      if (hasAuthTag()) {
        if (getAuthTag()
            != other.getAuthTag()) return false;
      }
      if (hasCliCertCnt() != other.hasCliCertCnt()) return false;
      if (hasCliCertCnt()) {
        if (getCliCertCnt()
            != other.getCliCertCnt()) return false;
      }
      if (hasSrvCertCnt() != other.hasSrvCertCnt()) return false;
      if (hasSrvCertCnt()) {
        if (getSrvCertCnt()
            != other.getSrvCertCnt()) return false;
      }
      if (hasEcGroupsCli() != other.hasEcGroupsCli()) return false;
      if (hasEcGroupsCli()) {
        if (!getEcGroupsCli()
            .equals(other.getEcGroupsCli())) return false;
      }
      if (hasEcPoiForByServ() != other.hasEcPoiForByServ()) return false;
      if (hasEcPoiForByServ()) {
        if (!getEcPoiForByServ()
            .equals(other.getEcPoiForByServ())) return false;
      }
      if (hasEtags() != other.hasEtags()) return false;
      if (hasEtags()) {
        if (!getEtags()
            .equals(other.getEtags())) return false;
      }
      if (hasTtags() != other.hasTtags()) return false;
      if (hasTtags()) {
        if (!getTtags()
            .equals(other.getTtags())) return false;
      }
      if (hasCliSesIDLen() != other.hasCliSesIDLen()) return false;
      if (hasCliSesIDLen()) {
        if (!getCliSesIDLen()
            .equals(other.getCliSesIDLen())) return false;
      }
      if (hasSrvSesIDLen() != other.hasSrvSesIDLen()) return false;
      if (hasSrvSesIDLen()) {
        if (!getSrvSesIDLen()
            .equals(other.getSrvSesIDLen())) return false;
      }
      if (hasSrvKeyExcLen() != other.hasSrvKeyExcLen()) return false;
      if (hasSrvKeyExcLen()) {
        if (!getSrvKeyExcLen()
            .equals(other.getSrvKeyExcLen())) return false;
      }
      if (hasECDHCurType() != other.hasECDHCurType()) return false;
      if (hasECDHCurType()) {
        if (!getECDHCurType()
            .equals(other.getECDHCurType())) return false;
      }
      if (hasECDHSig() != other.hasECDHSig()) return false;
      if (hasECDHSig()) {
        if (!getECDHSig()
            .equals(other.getECDHSig())) return false;
      }
      if (hasDHEPLen() != other.hasDHEPLen()) return false;
      if (hasDHEPLen()) {
        if (!getDHEPLen()
            .equals(other.getDHEPLen())) return false;
      }
      if (hasDHEGLen() != other.hasDHEGLen()) return false;
      if (hasDHEGLen()) {
        if (!getDHEGLen()
            .equals(other.getDHEGLen())) return false;
      }
      if (hasCliKeyExcLen() != other.hasCliKeyExcLen()) return false;
      if (hasCliKeyExcLen()) {
        if (!getCliKeyExcLen()
            .equals(other.getCliKeyExcLen())) return false;
      }
      if (hasEncPubKey() != other.hasEncPubKey()) return false;
      if (hasEncPubKey()) {
        if (!getEncPubKey()
            .equals(other.getEncPubKey())) return false;
      }
      if (hasEncPubKeyLen() != other.hasEncPubKeyLen()) return false;
      if (hasEncPubKeyLen()) {
        if (!getEncPubKeyLen()
            .equals(other.getEncPubKeyLen())) return false;
      }
      if (hasCliExtLen() != other.hasCliExtLen()) return false;
      if (hasCliExtLen()) {
        if (!getCliExtLen()
            .equals(other.getCliExtLen())) return false;
      }
      if (hasSrvExtLen() != other.hasSrvExtLen()) return false;
      if (hasSrvExtLen()) {
        if (!getSrvExtLen()
            .equals(other.getSrvExtLen())) return false;
      }
      if (hasECDHPubKeyLen() != other.hasECDHPubKeyLen()) return false;
      if (hasECDHPubKeyLen()) {
        if (!getECDHPubKeyLen()
            .equals(other.getECDHPubKeyLen())) return false;
      }
      if (hasNamType() != other.hasNamType()) return false;
      if (hasNamType()) {
        if (!getNamType()
            .equals(other.getNamType())) return false;
      }
      if (hasNamLen() != other.hasNamLen()) return false;
      if (hasNamLen()) {
        if (!getNamLen()
            .equals(other.getNamLen())) return false;
      }
      if (hasTicDat() != other.hasTicDat()) return false;
      if (hasTicDat()) {
        if (!getTicDat()
            .equals(other.getTicDat())) return false;
      }
      if (hasSrvCipSui() != other.hasSrvCipSui()) return false;
      if (hasSrvCipSui()) {
        if (!getSrvCipSui()
            .equals(other.getSrvCipSui())) return false;
      }
      if (hasCipSuiNum() != other.hasCipSuiNum()) return false;
      if (hasCipSuiNum()) {
        if (getCipSuiNum()
            != other.getCipSuiNum()) return false;
      }
      if (hasECDHSigHash() != other.hasECDHSigHash()) return false;
      if (hasECDHSigHash()) {
        if (!getECDHSigHash()
            .equals(other.getECDHSigHash())) return false;
      }
      if (hasDHESigHash() != other.hasDHESigHash()) return false;
      if (hasDHESigHash()) {
        if (!getDHESigHash()
            .equals(other.getDHESigHash())) return false;
      }
      if (hasRSASigHash() != other.hasRSASigHash()) return false;
      if (hasRSASigHash()) {
        if (!getRSASigHash()
            .equals(other.getRSASigHash())) return false;
      }
      if (hasGreaseFlag() != other.hasGreaseFlag()) return false;
      if (hasGreaseFlag()) {
        if (!getGreaseFlag()
            .equals(other.getGreaseFlag())) return false;
      }
      if (hasRSAModLen() != other.hasRSAModLen()) return false;
      if (hasRSAModLen()) {
        if (!getRSAModLen()
            .equals(other.getRSAModLen())) return false;
      }
      if (hasRSAExpLen() != other.hasRSAExpLen()) return false;
      if (hasRSAExpLen()) {
        if (!getRSAExpLen()
            .equals(other.getRSAExpLen())) return false;
      }
      if (hasRSASig() != other.hasRSASig()) return false;
      if (hasRSASig()) {
        if (!getRSASig()
            .equals(other.getRSASig())) return false;
      }
      if (hasDHESig() != other.hasDHESig()) return false;
      if (hasDHESig()) {
        if (!getDHESig()
            .equals(other.getDHESig())) return false;
      }
      if (hasDHEPubKeyLen() != other.hasDHEPubKeyLen()) return false;
      if (hasDHEPubKeyLen()) {
        if (!getDHEPubKeyLen()
            .equals(other.getDHEPubKeyLen())) return false;
      }
      if (hasDHEPubKey() != other.hasDHEPubKey()) return false;
      if (hasDHEPubKey()) {
        if (!getDHEPubKey()
            .equals(other.getDHEPubKey())) return false;
      }
      if (hasSigAlgType() != other.hasSigAlgType()) return false;
      if (hasSigAlgType()) {
        if (!getSigAlgType()
            .equals(other.getSigAlgType())) return false;
      }
      if (hasSigAlg() != other.hasSigAlg()) return false;
      if (hasSigAlg()) {
        if (!getSigAlg()
            .equals(other.getSigAlg())) return false;
      }
      if (hasSigHashAlg() != other.hasSigHashAlg()) return false;
      if (hasSigHashAlg()) {
        if (!getSigHashAlg()
            .equals(other.getSigHashAlg())) return false;
      }
      if (hasJOY() != other.hasJOY()) return false;
      if (hasJOY()) {
        if (!getJOY()
            .equals(other.getJOY())) return false;
      }
      if (hasJOYS() != other.hasJOYS()) return false;
      if (hasJOYS()) {
        if (!getJOYS()
            .equals(other.getJOYS())) return false;
      }
      if (hasSTARTTLS() != other.hasSTARTTLS()) return false;
      if (hasSTARTTLS()) {
        if (!getSTARTTLS()
            .equals(other.getSTARTTLS())) return false;
      }
      if (hasCertNonFlag() != other.hasCertNonFlag()) return false;
      if (hasCertNonFlag()) {
        if (!getCertNonFlag()
            .equals(other.getCertNonFlag())) return false;
      }
      if (hasJoyFp() != other.hasJoyFp()) return false;
      if (hasJoyFp()) {
        if (!getJoyFp()
            .equals(other.getJoyFp())) return false;
      }
      if (hasCertIntactFlag() != other.hasCertIntactFlag()) return false;
      if (hasCertIntactFlag()) {
        if (!getCertIntactFlag()
            .equals(other.getCertIntactFlag())) return false;
      }
      if (hasCertPath() != other.hasCertPath()) return false;
      if (hasCertPath()) {
        if (!getCertPath()
            .equals(other.getCertPath())) return false;
      }
      if (hasSessSecFlag() != other.hasSessSecFlag()) return false;
      if (hasSessSecFlag()) {
        if (!getSessSecFlag()
            .equals(other.getSessSecFlag())) return false;
      }
      if (hasFullText() != other.hasFullText()) return false;
      if (hasFullText()) {
        if (!getFullText()
            .equals(other.getFullText())) return false;
      }
      if (hasCliCertHashes() != other.hasCliCertHashes()) return false;
      if (hasCliCertHashes()) {
        if (!getCliCertHashes()
            .equals(other.getCliCertHashes())) return false;
      }
      if (hasSrvCertHashes() != other.hasSrvCertHashes()) return false;
      if (hasSrvCertHashes()) {
        if (!getSrvCertHashes()
            .equals(other.getSrvCertHashes())) return false;
      }
      if (hasCliCertNum() != other.hasCliCertNum()) return false;
      if (hasCliCertNum()) {
        if (getCliCertNum()
            != other.getCliCertNum()) return false;
      }
      if (hasSrvCertNum() != other.hasSrvCertNum()) return false;
      if (hasSrvCertNum()) {
        if (getSrvCertNum()
            != other.getSrvCertNum()) return false;
      }
      if (hasCertExist() != other.hasCertExist()) return false;
      if (hasCertExist()) {
        if (getCertExist()
            != other.getCertExist()) return false;
      }
      if (hasExtendEcGroupsClient() != other.hasExtendEcGroupsClient()) return false;
      if (hasExtendEcGroupsClient()) {
        if (!getExtendEcGroupsClient()
            .equals(other.getExtendEcGroupsClient())) return false;
      }
      if (hasLeafCertDaysRemaining() != other.hasLeafCertDaysRemaining()) return false;
      if (hasLeafCertDaysRemaining()) {
        if (getLeafCertDaysRemaining()
            != other.getLeafCertDaysRemaining()) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasConType()) {
        hash = (37 * hash) + CONTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getConType();
      }
      if (hasAleLev()) {
        hash = (37 * hash) + ALELEV_FIELD_NUMBER;
        hash = (53 * hash) + getAleLev();
      }
      if (hasAleDes()) {
        hash = (37 * hash) + ALEDES_FIELD_NUMBER;
        hash = (53 * hash) + getAleDes();
      }
      if (hasHandShaType()) {
        hash = (37 * hash) + HANDSHATYPE_FIELD_NUMBER;
        hash = (53 * hash) + getHandShaType();
      }
      if (hasCliVer()) {
        hash = (37 * hash) + CLIVER_FIELD_NUMBER;
        hash = (53 * hash) + getCliVer();
      }
      if (hasCliGMTUniTime()) {
        hash = (37 * hash) + CLIGMTUNITIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCliGMTUniTime());
      }
      if (hasCliRand()) {
        hash = (37 * hash) + CLIRAND_FIELD_NUMBER;
        hash = (53 * hash) + getCliRand().hashCode();
      }
      if (hasCliSesID()) {
        hash = (37 * hash) + CLISESID_FIELD_NUMBER;
        hash = (53 * hash) + getCliSesID().hashCode();
      }
      if (hasCliCipSui()) {
        hash = (37 * hash) + CLICIPSUI_FIELD_NUMBER;
        hash = (53 * hash) + getCliCipSui().hashCode();
      }
      if (hasCliComMet()) {
        hash = (37 * hash) + CLICOMMET_FIELD_NUMBER;
        hash = (53 * hash) + getCliComMet().hashCode();
      }
      if (hasSrvVer()) {
        hash = (37 * hash) + SRVVER_FIELD_NUMBER;
        hash = (53 * hash) + getSrvVer();
      }
      if (hasSrvName()) {
        hash = (37 * hash) + SRVNAME_FIELD_NUMBER;
        hash = (53 * hash) + getSrvName().hashCode();
      }
      if (hasSrvNameAttr()) {
        hash = (37 * hash) + SRVNAMEATTR_FIELD_NUMBER;
        hash = (53 * hash) + getSrvNameAttr();
      }
      if (hasSrvGMTUniTime14()) {
        hash = (37 * hash) + SRVGMTUNITIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSrvGMTUniTime14());
      }
      if (hasSrvRand()) {
        hash = (37 * hash) + SRVRAND_FIELD_NUMBER;
        hash = (53 * hash) + getSrvRand().hashCode();
      }
      if (hasSrvSesID()) {
        hash = (37 * hash) + SRVSESID_FIELD_NUMBER;
        hash = (53 * hash) + getSrvSesID().hashCode();
      }
      if (hasSrvComprMet()) {
        hash = (37 * hash) + SRVCOMPRMET_FIELD_NUMBER;
        hash = (53 * hash) + getSrvComprMet().hashCode();
      }
      if (hasSrvCertLen()) {
        hash = (37 * hash) + SRVCERTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertLen();
      }
      if (hasCertResType()) {
        hash = (37 * hash) + CERTRESTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getCertResType();
      }
      if (hasCliCertLen()) {
        hash = (37 * hash) + CLICERTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertLen();
      }
      if (hasRSAModOfSrvKeyExc()) {
        hash = (37 * hash) + RSAMODOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + getRSAModOfSrvKeyExc().hashCode();
      }
      if (hasRSAExpOfSrvKeyExc()) {
        hash = (37 * hash) + RSAEXPOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRSAExpOfSrvKeyExc());
      }
      if (hasDHModOfSrvKeyExc()) {
        hash = (37 * hash) + DHMODOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + getDHModOfSrvKeyExc().hashCode();
      }
      if (hasDHGenOfSrvKeyExc()) {
        hash = (37 * hash) + DHGENOFSRVKEYEXC_FIELD_NUMBER;
        hash = (53 * hash) + getDHGenOfSrvKeyExc().hashCode();
      }
      if (hasSrvDHPubKey()) {
        hash = (37 * hash) + SRVDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSrvDHPubKey().hashCode();
      }
      if (hasPreMasKeyEncryByRSA()) {
        hash = (37 * hash) + PREMASKEYENCRYBYRSA_FIELD_NUMBER;
        hash = (53 * hash) + getPreMasKeyEncryByRSA().hashCode();
      }
      if (hasCliDHPubKey()) {
        hash = (37 * hash) + CLIDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCliDHPubKey().hashCode();
      }
      if (hasExtTypeInSSL()) {
        hash = (37 * hash) + EXTTYPEINSSL_FIELD_NUMBER;
        hash = (53 * hash) + getExtTypeInSSL();
      }
      if (hasCliEllCurPoiFor()) {
        hash = (37 * hash) + CLIELLCURPOIFOR_FIELD_NUMBER;
        hash = (53 * hash) + getCliEllCurPoiFor();
      }
      if (hasCliEllCur()) {
        hash = (37 * hash) + CLIELLCUR_FIELD_NUMBER;
        hash = (53 * hash) + getCliEllCur();
      }
      if (hasSrvEllCurPoiFor()) {
        hash = (37 * hash) + SRVELLCURPOIFOR_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEllCurPoiFor();
      }
      if (hasSrvEllCur()) {
        hash = (37 * hash) + SRVELLCUR_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEllCur();
      }
      if (hasSrvEllCurDHPubKey()) {
        hash = (37 * hash) + SRVELLCURDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getSrvEllCurDHPubKey().hashCode();
      }
      if (hasCliEllCurDHPubKey()) {
        hash = (37 * hash) + CLIELLCURDHPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getCliEllCurDHPubKey().hashCode();
      }
      if (hasSrvGMTUniTime35()) {
        hash = (37 * hash) + SRVGMTUNI_TIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSrvGMTUniTime35());
      }
      if (hasCliExtCnt()) {
        hash = (37 * hash) + CLIEXTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getCliExtCnt();
      }
      if (hasSrvExtCnt()) {
        hash = (37 * hash) + SRVEXTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvExtCnt();
      }
      if (hasCliHandSkLen()) {
        hash = (37 * hash) + CLIHANDSKLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliHandSkLen();
      }
      if (hasSrvHandSkLen()) {
        hash = (37 * hash) + SRVHANDSKLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvHandSkLen();
      }
      if (hasCliExt()) {
        hash = (37 * hash) + CLIEXT_FIELD_NUMBER;
        hash = (53 * hash) + getCliExt().hashCode();
      }
      if (hasSrvExt()) {
        hash = (37 * hash) + SRVEXT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvExt().hashCode();
      }
      if (hasCliExtGrease()) {
        hash = (37 * hash) + CLIEXTGREASE_FIELD_NUMBER;
        hash = (53 * hash) + getCliExtGrease();
      }
      if (hasCliJA3()) {
        hash = (37 * hash) + CLIJA3_FIELD_NUMBER;
        hash = (53 * hash) + getCliJA3().hashCode();
      }
      if (hasSrvJA3()) {
        hash = (37 * hash) + SRVJA3_FIELD_NUMBER;
        hash = (53 * hash) + getSrvJA3().hashCode();
      }
      if (hasCliSessTicket()) {
        hash = (37 * hash) + CLISESSTICKET_FIELD_NUMBER;
        hash = (53 * hash) + getCliSessTicket().hashCode();
      }
      if (hasSrvSessTicket()) {
        hash = (37 * hash) + SRVSESSTICKET_FIELD_NUMBER;
        hash = (53 * hash) + getSrvSessTicket().hashCode();
      }
      if (hasAuthTag()) {
        hash = (37 * hash) + AUTHTAG_FIELD_NUMBER;
        hash = (53 * hash) + getAuthTag();
      }
      if (hasCliCertCnt()) {
        hash = (37 * hash) + CLICERTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertCnt();
      }
      if (hasSrvCertCnt()) {
        hash = (37 * hash) + SRVCERTCNT_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertCnt();
      }
      if (hasEcGroupsCli()) {
        hash = (37 * hash) + ECGROUPSCLI_FIELD_NUMBER;
        hash = (53 * hash) + getEcGroupsCli().hashCode();
      }
      if (hasEcPoiForByServ()) {
        hash = (37 * hash) + ECPOIFORBYSERV_FIELD_NUMBER;
        hash = (53 * hash) + getEcPoiForByServ().hashCode();
      }
      if (hasEtags()) {
        hash = (37 * hash) + ETAGS_FIELD_NUMBER;
        hash = (53 * hash) + getEtags().hashCode();
      }
      if (hasTtags()) {
        hash = (37 * hash) + TTAGS_FIELD_NUMBER;
        hash = (53 * hash) + getTtags().hashCode();
      }
      if (hasCliSesIDLen()) {
        hash = (37 * hash) + CLISESIDLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliSesIDLen().hashCode();
      }
      if (hasSrvSesIDLen()) {
        hash = (37 * hash) + SRVSESIDLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvSesIDLen().hashCode();
      }
      if (hasSrvKeyExcLen()) {
        hash = (37 * hash) + SRVKEYEXCLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvKeyExcLen().hashCode();
      }
      if (hasECDHCurType()) {
        hash = (37 * hash) + ECDHCURTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getECDHCurType().hashCode();
      }
      if (hasECDHSig()) {
        hash = (37 * hash) + ECDHSIG_FIELD_NUMBER;
        hash = (53 * hash) + getECDHSig().hashCode();
      }
      if (hasDHEPLen()) {
        hash = (37 * hash) + DHEPLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHEPLen().hashCode();
      }
      if (hasDHEGLen()) {
        hash = (37 * hash) + DHEGLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHEGLen().hashCode();
      }
      if (hasCliKeyExcLen()) {
        hash = (37 * hash) + CLIKEYEXCLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliKeyExcLen().hashCode();
      }
      if (hasEncPubKey()) {
        hash = (37 * hash) + ENCPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getEncPubKey().hashCode();
      }
      if (hasEncPubKeyLen()) {
        hash = (37 * hash) + ENCPUBKEYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getEncPubKeyLen().hashCode();
      }
      if (hasCliExtLen()) {
        hash = (37 * hash) + CLIEXTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getCliExtLen().hashCode();
      }
      if (hasSrvExtLen()) {
        hash = (37 * hash) + SRVEXTLEN_FIELD_NUMBER;
        hash = (53 * hash) + getSrvExtLen().hashCode();
      }
      if (hasECDHPubKeyLen()) {
        hash = (37 * hash) + ECDHPUBKEYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getECDHPubKeyLen().hashCode();
      }
      if (hasNamType()) {
        hash = (37 * hash) + NAMTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getNamType().hashCode();
      }
      if (hasNamLen()) {
        hash = (37 * hash) + NAMLEN_FIELD_NUMBER;
        hash = (53 * hash) + getNamLen().hashCode();
      }
      if (hasTicDat()) {
        hash = (37 * hash) + TICDAT_FIELD_NUMBER;
        hash = (53 * hash) + getTicDat().hashCode();
      }
      if (hasSrvCipSui()) {
        hash = (37 * hash) + SRVCIPSUI_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCipSui().hashCode();
      }
      if (hasCipSuiNum()) {
        hash = (37 * hash) + CIPSUINUM_FIELD_NUMBER;
        hash = (53 * hash) + getCipSuiNum();
      }
      if (hasECDHSigHash()) {
        hash = (37 * hash) + ECDHSIGHASH_FIELD_NUMBER;
        hash = (53 * hash) + getECDHSigHash().hashCode();
      }
      if (hasDHESigHash()) {
        hash = (37 * hash) + DHESIGHASH_FIELD_NUMBER;
        hash = (53 * hash) + getDHESigHash().hashCode();
      }
      if (hasRSASigHash()) {
        hash = (37 * hash) + RSASIGHASH_FIELD_NUMBER;
        hash = (53 * hash) + getRSASigHash().hashCode();
      }
      if (hasGreaseFlag()) {
        hash = (37 * hash) + GREASEFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getGreaseFlag().hashCode();
      }
      if (hasRSAModLen()) {
        hash = (37 * hash) + RSAMODLEN_FIELD_NUMBER;
        hash = (53 * hash) + getRSAModLen().hashCode();
      }
      if (hasRSAExpLen()) {
        hash = (37 * hash) + RSAEXPLEN_FIELD_NUMBER;
        hash = (53 * hash) + getRSAExpLen().hashCode();
      }
      if (hasRSASig()) {
        hash = (37 * hash) + RSASIG_FIELD_NUMBER;
        hash = (53 * hash) + getRSASig().hashCode();
      }
      if (hasDHESig()) {
        hash = (37 * hash) + DHESIG_FIELD_NUMBER;
        hash = (53 * hash) + getDHESig().hashCode();
      }
      if (hasDHEPubKeyLen()) {
        hash = (37 * hash) + DHEPUBKEYLEN_FIELD_NUMBER;
        hash = (53 * hash) + getDHEPubKeyLen().hashCode();
      }
      if (hasDHEPubKey()) {
        hash = (37 * hash) + DHEPUBKEY_FIELD_NUMBER;
        hash = (53 * hash) + getDHEPubKey().hashCode();
      }
      if (hasSigAlgType()) {
        hash = (37 * hash) + SIGALGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getSigAlgType().hashCode();
      }
      if (hasSigAlg()) {
        hash = (37 * hash) + SIGALG_FIELD_NUMBER;
        hash = (53 * hash) + getSigAlg().hashCode();
      }
      if (hasSigHashAlg()) {
        hash = (37 * hash) + SIGHASHALG_FIELD_NUMBER;
        hash = (53 * hash) + getSigHashAlg().hashCode();
      }
      if (hasJOY()) {
        hash = (37 * hash) + JOY_FIELD_NUMBER;
        hash = (53 * hash) + getJOY().hashCode();
      }
      if (hasJOYS()) {
        hash = (37 * hash) + JOYS_FIELD_NUMBER;
        hash = (53 * hash) + getJOYS().hashCode();
      }
      if (hasSTARTTLS()) {
        hash = (37 * hash) + STARTTLS_FIELD_NUMBER;
        hash = (53 * hash) + getSTARTTLS().hashCode();
      }
      if (hasCertNonFlag()) {
        hash = (37 * hash) + CERTNONFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getCertNonFlag().hashCode();
      }
      if (hasJoyFp()) {
        hash = (37 * hash) + JOYFP_FIELD_NUMBER;
        hash = (53 * hash) + getJoyFp().hashCode();
      }
      if (hasCertIntactFlag()) {
        hash = (37 * hash) + CERTINTACTFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getCertIntactFlag().hashCode();
      }
      if (hasCertPath()) {
        hash = (37 * hash) + CERTPATH_FIELD_NUMBER;
        hash = (53 * hash) + getCertPath().hashCode();
      }
      if (hasSessSecFlag()) {
        hash = (37 * hash) + SESSSECFLAG_FIELD_NUMBER;
        hash = (53 * hash) + getSessSecFlag().hashCode();
      }
      if (hasFullText()) {
        hash = (37 * hash) + FULLTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getFullText().hashCode();
      }
      if (hasCliCertHashes()) {
        hash = (37 * hash) + CLICERTHASHES_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertHashes().hashCode();
      }
      if (hasSrvCertHashes()) {
        hash = (37 * hash) + SRVCERTHASHES_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertHashes().hashCode();
      }
      if (hasCliCertNum()) {
        hash = (37 * hash) + CLICERTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getCliCertNum();
      }
      if (hasSrvCertNum()) {
        hash = (37 * hash) + SRVCERTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getSrvCertNum();
      }
      if (hasCertExist()) {
        hash = (37 * hash) + CERTEXIST_FIELD_NUMBER;
        hash = (53 * hash) + getCertExist();
      }
      if (hasExtendEcGroupsClient()) {
        hash = (37 * hash) + EXTENDECGROUPSCLIENT_FIELD_NUMBER;
        hash = (53 * hash) + getExtendEcGroupsClient().hashCode();
      }
      if (hasLeafCertDaysRemaining()) {
        hash = (37 * hash) + LEAFCERTDAYSREMAINING_FIELD_NUMBER;
        hash = (53 * hash) + getLeafCertDaysRemaining();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static SslTlsInfo.Ssl_TlsInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static SslTlsInfo.Ssl_TlsInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static SslTlsInfo.Ssl_TlsInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(SslTlsInfo.Ssl_TlsInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Ssl_TlsInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Ssl_TlsInfo)
        SslTlsInfo.Ssl_TlsInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return SslTlsInfo.internal_static_Ssl_TlsInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return SslTlsInfo.internal_static_Ssl_TlsInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                SslTlsInfo.Ssl_TlsInfo.class, SslTlsInfo.Ssl_TlsInfo.Builder.class);
      }

      // Construct using SslTlsInfo.Ssl_TlsInfo.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        bitField2_ = 0;
        bitField3_ = 0;
        conType_ = 0;
        aleLev_ = 0;
        aleDes_ = 0;
        handShaType_ = 0;
        cliVer_ = 0;
        cliGMTUniTime_ = 0L;
        cliRand_ = com.google.protobuf.ByteString.EMPTY;
        cliSesID_ = com.google.protobuf.ByteString.EMPTY;
        cliCipSui_ = com.google.protobuf.ByteString.EMPTY;
        cliComMet_ = com.google.protobuf.ByteString.EMPTY;
        srvVer_ = 0;
        srvName_ = com.google.protobuf.ByteString.EMPTY;
        srvNameAttr_ = 0;
        srvGMTUniTime14_ = 0L;
        srvRand_ = com.google.protobuf.ByteString.EMPTY;
        srvSesID_ = com.google.protobuf.ByteString.EMPTY;
        srvComprMet_ = com.google.protobuf.ByteString.EMPTY;
        srvCertLen_ = 0;
        certResType_ = 0;
        cliCertLen_ = 0;
        rSAModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
        rSAExpOfSrvKeyExc_ = 0L;
        dHModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
        dHGenOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
        srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        preMasKeyEncryByRSA_ = com.google.protobuf.ByteString.EMPTY;
        cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        extTypeInSSL_ = 0;
        cliEllCurPoiFor_ = 0;
        cliEllCur_ = 0;
        srvEllCurPoiFor_ = 0;
        srvEllCur_ = 0;
        srvEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        cliEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
        srvGMTUniTime35_ = 0L;
        cliExtCnt_ = 0;
        srvExtCnt_ = 0;
        cliHandSkLen_ = 0;
        srvHandSkLen_ = 0;
        cliExt_ = com.google.protobuf.ByteString.EMPTY;
        srvExt_ = com.google.protobuf.ByteString.EMPTY;
        cliExtGrease_ = 0;
        cliJA3_ = com.google.protobuf.ByteString.EMPTY;
        srvJA3_ = com.google.protobuf.ByteString.EMPTY;
        cliSessTicket_ = com.google.protobuf.ByteString.EMPTY;
        srvSessTicket_ = com.google.protobuf.ByteString.EMPTY;
        authTag_ = 0;
        cliCertCnt_ = 0;
        srvCertCnt_ = 0;
        ecGroupsCli_ = com.google.protobuf.ByteString.EMPTY;
        ecPoiForByServ_ = com.google.protobuf.ByteString.EMPTY;
        etags_ = com.google.protobuf.ByteString.EMPTY;
        ttags_ = com.google.protobuf.ByteString.EMPTY;
        cliSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
        srvSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
        srvKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
        eCDHCurType_ = com.google.protobuf.ByteString.EMPTY;
        eCDHSig_ = com.google.protobuf.ByteString.EMPTY;
        dHEPLen_ = com.google.protobuf.ByteString.EMPTY;
        dHEGLen_ = com.google.protobuf.ByteString.EMPTY;
        cliKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
        encPubKey_ = com.google.protobuf.ByteString.EMPTY;
        encPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
        cliExtLen_ = com.google.protobuf.ByteString.EMPTY;
        srvExtLen_ = com.google.protobuf.ByteString.EMPTY;
        eCDHPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
        namType_ = com.google.protobuf.ByteString.EMPTY;
        namLen_ = com.google.protobuf.ByteString.EMPTY;
        ticDat_ = com.google.protobuf.ByteString.EMPTY;
        srvCipSui_ = com.google.protobuf.ByteString.EMPTY;
        cipSuiNum_ = 0;
        eCDHSigHash_ = com.google.protobuf.ByteString.EMPTY;
        dHESigHash_ = com.google.protobuf.ByteString.EMPTY;
        rSASigHash_ = com.google.protobuf.ByteString.EMPTY;
        greaseFlag_ = com.google.protobuf.ByteString.EMPTY;
        rSAModLen_ = com.google.protobuf.ByteString.EMPTY;
        rSAExpLen_ = com.google.protobuf.ByteString.EMPTY;
        rSASig_ = com.google.protobuf.ByteString.EMPTY;
        dHESig_ = com.google.protobuf.ByteString.EMPTY;
        dHEPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
        dHEPubKey_ = com.google.protobuf.ByteString.EMPTY;
        sigAlgType_ = com.google.protobuf.ByteString.EMPTY;
        sigAlg_ = com.google.protobuf.ByteString.EMPTY;
        sigHashAlg_ = com.google.protobuf.ByteString.EMPTY;
        jOY_ = com.google.protobuf.ByteString.EMPTY;
        jOYS_ = com.google.protobuf.ByteString.EMPTY;
        sTARTTLS_ = com.google.protobuf.ByteString.EMPTY;
        certNonFlag_ = com.google.protobuf.ByteString.EMPTY;
        joyFp_ = com.google.protobuf.ByteString.EMPTY;
        certIntactFlag_ = com.google.protobuf.ByteString.EMPTY;
        certPath_ = com.google.protobuf.ByteString.EMPTY;
        sessSecFlag_ = com.google.protobuf.ByteString.EMPTY;
        fullText_ = com.google.protobuf.ByteString.EMPTY;
        cliCertHashes_ = com.google.protobuf.ByteString.EMPTY;
        srvCertHashes_ = com.google.protobuf.ByteString.EMPTY;
        cliCertNum_ = 0;
        srvCertNum_ = 0;
        certExist_ = 0;
        extendEcGroupsClient_ = com.google.protobuf.ByteString.EMPTY;
        leafCertDaysRemaining_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return SslTlsInfo.internal_static_Ssl_TlsInfo_descriptor;
      }

      @java.lang.Override
      public SslTlsInfo.Ssl_TlsInfo getDefaultInstanceForType() {
        return SslTlsInfo.Ssl_TlsInfo.getDefaultInstance();
      }

      @java.lang.Override
      public SslTlsInfo.Ssl_TlsInfo build() {
        SslTlsInfo.Ssl_TlsInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public SslTlsInfo.Ssl_TlsInfo buildPartial() {
        SslTlsInfo.Ssl_TlsInfo result = new SslTlsInfo.Ssl_TlsInfo(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        if (bitField2_ != 0) { buildPartial2(result); }
        if (bitField3_ != 0) { buildPartial3(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(SslTlsInfo.Ssl_TlsInfo result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.conType_ = conType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.aleLev_ = aleLev_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.aleDes_ = aleDes_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.handShaType_ = handShaType_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.cliVer_ = cliVer_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.cliGMTUniTime_ = cliGMTUniTime_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.cliRand_ = cliRand_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.cliSesID_ = cliSesID_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.cliCipSui_ = cliCipSui_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.cliComMet_ = cliComMet_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.srvVer_ = srvVer_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.srvName_ = srvName_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.srvNameAttr_ = srvNameAttr_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.srvGMTUniTime14_ = srvGMTUniTime14_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.srvRand_ = srvRand_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.srvSesID_ = srvSesID_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.srvComprMet_ = srvComprMet_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.srvCertLen_ = srvCertLen_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.certResType_ = certResType_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.cliCertLen_ = cliCertLen_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.rSAModOfSrvKeyExc_ = rSAModOfSrvKeyExc_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.rSAExpOfSrvKeyExc_ = rSAExpOfSrvKeyExc_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.dHModOfSrvKeyExc_ = dHModOfSrvKeyExc_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.dHGenOfSrvKeyExc_ = dHGenOfSrvKeyExc_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.srvDHPubKey_ = srvDHPubKey_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.preMasKeyEncryByRSA_ = preMasKeyEncryByRSA_;
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.cliDHPubKey_ = cliDHPubKey_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.extTypeInSSL_ = extTypeInSSL_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.cliEllCurPoiFor_ = cliEllCurPoiFor_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.cliEllCur_ = cliEllCur_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.srvEllCurPoiFor_ = srvEllCurPoiFor_;
          to_bitField0_ |= 0x40000000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.srvEllCur_ = srvEllCur_;
          to_bitField0_ |= 0x80000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(SslTlsInfo.Ssl_TlsInfo result) {
        int from_bitField1_ = bitField1_;
        int to_bitField1_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.srvEllCurDHPubKey_ = srvEllCurDHPubKey_;
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.cliEllCurDHPubKey_ = cliEllCurDHPubKey_;
          to_bitField1_ |= 0x00000002;
        }
        if (((from_bitField1_ & 0x00000004) != 0)) {
          result.srvGMTUniTime35_ = srvGMTUniTime35_;
          to_bitField1_ |= 0x00000004;
        }
        if (((from_bitField1_ & 0x00000008) != 0)) {
          result.cliExtCnt_ = cliExtCnt_;
          to_bitField1_ |= 0x00000008;
        }
        if (((from_bitField1_ & 0x00000010) != 0)) {
          result.srvExtCnt_ = srvExtCnt_;
          to_bitField1_ |= 0x00000010;
        }
        if (((from_bitField1_ & 0x00000020) != 0)) {
          result.cliHandSkLen_ = cliHandSkLen_;
          to_bitField1_ |= 0x00000020;
        }
        if (((from_bitField1_ & 0x00000040) != 0)) {
          result.srvHandSkLen_ = srvHandSkLen_;
          to_bitField1_ |= 0x00000040;
        }
        if (((from_bitField1_ & 0x00000080) != 0)) {
          result.cliExt_ = cliExt_;
          to_bitField1_ |= 0x00000080;
        }
        if (((from_bitField1_ & 0x00000100) != 0)) {
          result.srvExt_ = srvExt_;
          to_bitField1_ |= 0x00000100;
        }
        if (((from_bitField1_ & 0x00000200) != 0)) {
          result.cliExtGrease_ = cliExtGrease_;
          to_bitField1_ |= 0x00000200;
        }
        if (((from_bitField1_ & 0x00000400) != 0)) {
          result.cliJA3_ = cliJA3_;
          to_bitField1_ |= 0x00000400;
        }
        if (((from_bitField1_ & 0x00000800) != 0)) {
          result.srvJA3_ = srvJA3_;
          to_bitField1_ |= 0x00000800;
        }
        if (((from_bitField1_ & 0x00001000) != 0)) {
          result.cliSessTicket_ = cliSessTicket_;
          to_bitField1_ |= 0x00001000;
        }
        if (((from_bitField1_ & 0x00002000) != 0)) {
          result.srvSessTicket_ = srvSessTicket_;
          to_bitField1_ |= 0x00002000;
        }
        if (((from_bitField1_ & 0x00004000) != 0)) {
          result.authTag_ = authTag_;
          to_bitField1_ |= 0x00004000;
        }
        if (((from_bitField1_ & 0x00008000) != 0)) {
          result.cliCertCnt_ = cliCertCnt_;
          to_bitField1_ |= 0x00008000;
        }
        if (((from_bitField1_ & 0x00010000) != 0)) {
          result.srvCertCnt_ = srvCertCnt_;
          to_bitField1_ |= 0x00010000;
        }
        if (((from_bitField1_ & 0x00020000) != 0)) {
          result.ecGroupsCli_ = ecGroupsCli_;
          to_bitField1_ |= 0x00020000;
        }
        if (((from_bitField1_ & 0x00040000) != 0)) {
          result.ecPoiForByServ_ = ecPoiForByServ_;
          to_bitField1_ |= 0x00040000;
        }
        if (((from_bitField1_ & 0x00080000) != 0)) {
          result.etags_ = etags_;
          to_bitField1_ |= 0x00080000;
        }
        if (((from_bitField1_ & 0x00100000) != 0)) {
          result.ttags_ = ttags_;
          to_bitField1_ |= 0x00100000;
        }
        if (((from_bitField1_ & 0x00200000) != 0)) {
          result.cliSesIDLen_ = cliSesIDLen_;
          to_bitField1_ |= 0x00200000;
        }
        if (((from_bitField1_ & 0x00400000) != 0)) {
          result.srvSesIDLen_ = srvSesIDLen_;
          to_bitField1_ |= 0x00400000;
        }
        if (((from_bitField1_ & 0x00800000) != 0)) {
          result.srvKeyExcLen_ = srvKeyExcLen_;
          to_bitField1_ |= 0x00800000;
        }
        if (((from_bitField1_ & 0x01000000) != 0)) {
          result.eCDHCurType_ = eCDHCurType_;
          to_bitField1_ |= 0x01000000;
        }
        if (((from_bitField1_ & 0x02000000) != 0)) {
          result.eCDHSig_ = eCDHSig_;
          to_bitField1_ |= 0x02000000;
        }
        if (((from_bitField1_ & 0x04000000) != 0)) {
          result.dHEPLen_ = dHEPLen_;
          to_bitField1_ |= 0x04000000;
        }
        if (((from_bitField1_ & 0x08000000) != 0)) {
          result.dHEGLen_ = dHEGLen_;
          to_bitField1_ |= 0x08000000;
        }
        if (((from_bitField1_ & 0x10000000) != 0)) {
          result.cliKeyExcLen_ = cliKeyExcLen_;
          to_bitField1_ |= 0x10000000;
        }
        if (((from_bitField1_ & 0x20000000) != 0)) {
          result.encPubKey_ = encPubKey_;
          to_bitField1_ |= 0x20000000;
        }
        if (((from_bitField1_ & 0x40000000) != 0)) {
          result.encPubKeyLen_ = encPubKeyLen_;
          to_bitField1_ |= 0x40000000;
        }
        if (((from_bitField1_ & 0x80000000) != 0)) {
          result.cliExtLen_ = cliExtLen_;
          to_bitField1_ |= 0x80000000;
        }
        result.bitField1_ |= to_bitField1_;
      }

      private void buildPartial2(SslTlsInfo.Ssl_TlsInfo result) {
        int from_bitField2_ = bitField2_;
        int to_bitField2_ = 0;
        if (((from_bitField2_ & 0x00000001) != 0)) {
          result.srvExtLen_ = srvExtLen_;
          to_bitField2_ |= 0x00000001;
        }
        if (((from_bitField2_ & 0x00000002) != 0)) {
          result.eCDHPubKeyLen_ = eCDHPubKeyLen_;
          to_bitField2_ |= 0x00000002;
        }
        if (((from_bitField2_ & 0x00000004) != 0)) {
          result.namType_ = namType_;
          to_bitField2_ |= 0x00000004;
        }
        if (((from_bitField2_ & 0x00000008) != 0)) {
          result.namLen_ = namLen_;
          to_bitField2_ |= 0x00000008;
        }
        if (((from_bitField2_ & 0x00000010) != 0)) {
          result.ticDat_ = ticDat_;
          to_bitField2_ |= 0x00000010;
        }
        if (((from_bitField2_ & 0x00000020) != 0)) {
          result.srvCipSui_ = srvCipSui_;
          to_bitField2_ |= 0x00000020;
        }
        if (((from_bitField2_ & 0x00000040) != 0)) {
          result.cipSuiNum_ = cipSuiNum_;
          to_bitField2_ |= 0x00000040;
        }
        if (((from_bitField2_ & 0x00000080) != 0)) {
          result.eCDHSigHash_ = eCDHSigHash_;
          to_bitField2_ |= 0x00000080;
        }
        if (((from_bitField2_ & 0x00000100) != 0)) {
          result.dHESigHash_ = dHESigHash_;
          to_bitField2_ |= 0x00000100;
        }
        if (((from_bitField2_ & 0x00000200) != 0)) {
          result.rSASigHash_ = rSASigHash_;
          to_bitField2_ |= 0x00000200;
        }
        if (((from_bitField2_ & 0x00000400) != 0)) {
          result.greaseFlag_ = greaseFlag_;
          to_bitField2_ |= 0x00000400;
        }
        if (((from_bitField2_ & 0x00000800) != 0)) {
          result.rSAModLen_ = rSAModLen_;
          to_bitField2_ |= 0x00000800;
        }
        if (((from_bitField2_ & 0x00001000) != 0)) {
          result.rSAExpLen_ = rSAExpLen_;
          to_bitField2_ |= 0x00001000;
        }
        if (((from_bitField2_ & 0x00002000) != 0)) {
          result.rSASig_ = rSASig_;
          to_bitField2_ |= 0x00002000;
        }
        if (((from_bitField2_ & 0x00004000) != 0)) {
          result.dHESig_ = dHESig_;
          to_bitField2_ |= 0x00004000;
        }
        if (((from_bitField2_ & 0x00008000) != 0)) {
          result.dHEPubKeyLen_ = dHEPubKeyLen_;
          to_bitField2_ |= 0x00008000;
        }
        if (((from_bitField2_ & 0x00010000) != 0)) {
          result.dHEPubKey_ = dHEPubKey_;
          to_bitField2_ |= 0x00010000;
        }
        if (((from_bitField2_ & 0x00020000) != 0)) {
          result.sigAlgType_ = sigAlgType_;
          to_bitField2_ |= 0x00020000;
        }
        if (((from_bitField2_ & 0x00040000) != 0)) {
          result.sigAlg_ = sigAlg_;
          to_bitField2_ |= 0x00040000;
        }
        if (((from_bitField2_ & 0x00080000) != 0)) {
          result.sigHashAlg_ = sigHashAlg_;
          to_bitField2_ |= 0x00080000;
        }
        if (((from_bitField2_ & 0x00100000) != 0)) {
          result.jOY_ = jOY_;
          to_bitField2_ |= 0x00100000;
        }
        if (((from_bitField2_ & 0x00200000) != 0)) {
          result.jOYS_ = jOYS_;
          to_bitField2_ |= 0x00200000;
        }
        if (((from_bitField2_ & 0x00400000) != 0)) {
          result.sTARTTLS_ = sTARTTLS_;
          to_bitField2_ |= 0x00400000;
        }
        if (((from_bitField2_ & 0x00800000) != 0)) {
          result.certNonFlag_ = certNonFlag_;
          to_bitField2_ |= 0x00800000;
        }
        if (((from_bitField2_ & 0x01000000) != 0)) {
          result.joyFp_ = joyFp_;
          to_bitField2_ |= 0x01000000;
        }
        if (((from_bitField2_ & 0x02000000) != 0)) {
          result.certIntactFlag_ = certIntactFlag_;
          to_bitField2_ |= 0x02000000;
        }
        if (((from_bitField2_ & 0x04000000) != 0)) {
          result.certPath_ = certPath_;
          to_bitField2_ |= 0x04000000;
        }
        if (((from_bitField2_ & 0x08000000) != 0)) {
          result.sessSecFlag_ = sessSecFlag_;
          to_bitField2_ |= 0x08000000;
        }
        if (((from_bitField2_ & 0x10000000) != 0)) {
          result.fullText_ = fullText_;
          to_bitField2_ |= 0x10000000;
        }
        if (((from_bitField2_ & 0x20000000) != 0)) {
          result.cliCertHashes_ = cliCertHashes_;
          to_bitField2_ |= 0x20000000;
        }
        if (((from_bitField2_ & 0x40000000) != 0)) {
          result.srvCertHashes_ = srvCertHashes_;
          to_bitField2_ |= 0x40000000;
        }
        if (((from_bitField2_ & 0x80000000) != 0)) {
          result.cliCertNum_ = cliCertNum_;
          to_bitField2_ |= 0x80000000;
        }
        result.bitField2_ |= to_bitField2_;
      }

      private void buildPartial3(SslTlsInfo.Ssl_TlsInfo result) {
        int from_bitField3_ = bitField3_;
        int to_bitField3_ = 0;
        if (((from_bitField3_ & 0x00000001) != 0)) {
          result.srvCertNum_ = srvCertNum_;
          to_bitField3_ |= 0x00000001;
        }
        if (((from_bitField3_ & 0x00000002) != 0)) {
          result.certExist_ = certExist_;
          to_bitField3_ |= 0x00000002;
        }
        if (((from_bitField3_ & 0x00000004) != 0)) {
          result.extendEcGroupsClient_ = extendEcGroupsClient_;
          to_bitField3_ |= 0x00000004;
        }
        if (((from_bitField3_ & 0x00000008) != 0)) {
          result.leafCertDaysRemaining_ = leafCertDaysRemaining_;
          to_bitField3_ |= 0x00000008;
        }
        result.bitField3_ |= to_bitField3_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof SslTlsInfo.Ssl_TlsInfo) {
          return mergeFrom((SslTlsInfo.Ssl_TlsInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(SslTlsInfo.Ssl_TlsInfo other) {
        if (other == SslTlsInfo.Ssl_TlsInfo.getDefaultInstance()) return this;
        if (other.hasConType()) {
          setConType(other.getConType());
        }
        if (other.hasAleLev()) {
          setAleLev(other.getAleLev());
        }
        if (other.hasAleDes()) {
          setAleDes(other.getAleDes());
        }
        if (other.hasHandShaType()) {
          setHandShaType(other.getHandShaType());
        }
        if (other.hasCliVer()) {
          setCliVer(other.getCliVer());
        }
        if (other.hasCliGMTUniTime()) {
          setCliGMTUniTime(other.getCliGMTUniTime());
        }
        if (other.hasCliRand()) {
          setCliRand(other.getCliRand());
        }
        if (other.hasCliSesID()) {
          setCliSesID(other.getCliSesID());
        }
        if (other.hasCliCipSui()) {
          setCliCipSui(other.getCliCipSui());
        }
        if (other.hasCliComMet()) {
          setCliComMet(other.getCliComMet());
        }
        if (other.hasSrvVer()) {
          setSrvVer(other.getSrvVer());
        }
        if (other.hasSrvName()) {
          setSrvName(other.getSrvName());
        }
        if (other.hasSrvNameAttr()) {
          setSrvNameAttr(other.getSrvNameAttr());
        }
        if (other.hasSrvGMTUniTime14()) {
          setSrvGMTUniTime14(other.getSrvGMTUniTime14());
        }
        if (other.hasSrvRand()) {
          setSrvRand(other.getSrvRand());
        }
        if (other.hasSrvSesID()) {
          setSrvSesID(other.getSrvSesID());
        }
        if (other.hasSrvComprMet()) {
          setSrvComprMet(other.getSrvComprMet());
        }
        if (other.hasSrvCertLen()) {
          setSrvCertLen(other.getSrvCertLen());
        }
        if (other.hasCertResType()) {
          setCertResType(other.getCertResType());
        }
        if (other.hasCliCertLen()) {
          setCliCertLen(other.getCliCertLen());
        }
        if (other.hasRSAModOfSrvKeyExc()) {
          setRSAModOfSrvKeyExc(other.getRSAModOfSrvKeyExc());
        }
        if (other.hasRSAExpOfSrvKeyExc()) {
          setRSAExpOfSrvKeyExc(other.getRSAExpOfSrvKeyExc());
        }
        if (other.hasDHModOfSrvKeyExc()) {
          setDHModOfSrvKeyExc(other.getDHModOfSrvKeyExc());
        }
        if (other.hasDHGenOfSrvKeyExc()) {
          setDHGenOfSrvKeyExc(other.getDHGenOfSrvKeyExc());
        }
        if (other.hasSrvDHPubKey()) {
          setSrvDHPubKey(other.getSrvDHPubKey());
        }
        if (other.hasPreMasKeyEncryByRSA()) {
          setPreMasKeyEncryByRSA(other.getPreMasKeyEncryByRSA());
        }
        if (other.hasCliDHPubKey()) {
          setCliDHPubKey(other.getCliDHPubKey());
        }
        if (other.hasExtTypeInSSL()) {
          setExtTypeInSSL(other.getExtTypeInSSL());
        }
        if (other.hasCliEllCurPoiFor()) {
          setCliEllCurPoiFor(other.getCliEllCurPoiFor());
        }
        if (other.hasCliEllCur()) {
          setCliEllCur(other.getCliEllCur());
        }
        if (other.hasSrvEllCurPoiFor()) {
          setSrvEllCurPoiFor(other.getSrvEllCurPoiFor());
        }
        if (other.hasSrvEllCur()) {
          setSrvEllCur(other.getSrvEllCur());
        }
        if (other.hasSrvEllCurDHPubKey()) {
          setSrvEllCurDHPubKey(other.getSrvEllCurDHPubKey());
        }
        if (other.hasCliEllCurDHPubKey()) {
          setCliEllCurDHPubKey(other.getCliEllCurDHPubKey());
        }
        if (other.hasSrvGMTUniTime35()) {
          setSrvGMTUniTime35(other.getSrvGMTUniTime35());
        }
        if (other.hasCliExtCnt()) {
          setCliExtCnt(other.getCliExtCnt());
        }
        if (other.hasSrvExtCnt()) {
          setSrvExtCnt(other.getSrvExtCnt());
        }
        if (other.hasCliHandSkLen()) {
          setCliHandSkLen(other.getCliHandSkLen());
        }
        if (other.hasSrvHandSkLen()) {
          setSrvHandSkLen(other.getSrvHandSkLen());
        }
        if (other.hasCliExt()) {
          setCliExt(other.getCliExt());
        }
        if (other.hasSrvExt()) {
          setSrvExt(other.getSrvExt());
        }
        if (other.hasCliExtGrease()) {
          setCliExtGrease(other.getCliExtGrease());
        }
        if (other.hasCliJA3()) {
          setCliJA3(other.getCliJA3());
        }
        if (other.hasSrvJA3()) {
          setSrvJA3(other.getSrvJA3());
        }
        if (other.hasCliSessTicket()) {
          setCliSessTicket(other.getCliSessTicket());
        }
        if (other.hasSrvSessTicket()) {
          setSrvSessTicket(other.getSrvSessTicket());
        }
        if (other.hasAuthTag()) {
          setAuthTag(other.getAuthTag());
        }
        if (other.hasCliCertCnt()) {
          setCliCertCnt(other.getCliCertCnt());
        }
        if (other.hasSrvCertCnt()) {
          setSrvCertCnt(other.getSrvCertCnt());
        }
        if (other.hasEcGroupsCli()) {
          setEcGroupsCli(other.getEcGroupsCli());
        }
        if (other.hasEcPoiForByServ()) {
          setEcPoiForByServ(other.getEcPoiForByServ());
        }
        if (other.hasEtags()) {
          setEtags(other.getEtags());
        }
        if (other.hasTtags()) {
          setTtags(other.getTtags());
        }
        if (other.hasCliSesIDLen()) {
          setCliSesIDLen(other.getCliSesIDLen());
        }
        if (other.hasSrvSesIDLen()) {
          setSrvSesIDLen(other.getSrvSesIDLen());
        }
        if (other.hasSrvKeyExcLen()) {
          setSrvKeyExcLen(other.getSrvKeyExcLen());
        }
        if (other.hasECDHCurType()) {
          setECDHCurType(other.getECDHCurType());
        }
        if (other.hasECDHSig()) {
          setECDHSig(other.getECDHSig());
        }
        if (other.hasDHEPLen()) {
          setDHEPLen(other.getDHEPLen());
        }
        if (other.hasDHEGLen()) {
          setDHEGLen(other.getDHEGLen());
        }
        if (other.hasCliKeyExcLen()) {
          setCliKeyExcLen(other.getCliKeyExcLen());
        }
        if (other.hasEncPubKey()) {
          setEncPubKey(other.getEncPubKey());
        }
        if (other.hasEncPubKeyLen()) {
          setEncPubKeyLen(other.getEncPubKeyLen());
        }
        if (other.hasCliExtLen()) {
          setCliExtLen(other.getCliExtLen());
        }
        if (other.hasSrvExtLen()) {
          setSrvExtLen(other.getSrvExtLen());
        }
        if (other.hasECDHPubKeyLen()) {
          setECDHPubKeyLen(other.getECDHPubKeyLen());
        }
        if (other.hasNamType()) {
          setNamType(other.getNamType());
        }
        if (other.hasNamLen()) {
          setNamLen(other.getNamLen());
        }
        if (other.hasTicDat()) {
          setTicDat(other.getTicDat());
        }
        if (other.hasSrvCipSui()) {
          setSrvCipSui(other.getSrvCipSui());
        }
        if (other.hasCipSuiNum()) {
          setCipSuiNum(other.getCipSuiNum());
        }
        if (other.hasECDHSigHash()) {
          setECDHSigHash(other.getECDHSigHash());
        }
        if (other.hasDHESigHash()) {
          setDHESigHash(other.getDHESigHash());
        }
        if (other.hasRSASigHash()) {
          setRSASigHash(other.getRSASigHash());
        }
        if (other.hasGreaseFlag()) {
          setGreaseFlag(other.getGreaseFlag());
        }
        if (other.hasRSAModLen()) {
          setRSAModLen(other.getRSAModLen());
        }
        if (other.hasRSAExpLen()) {
          setRSAExpLen(other.getRSAExpLen());
        }
        if (other.hasRSASig()) {
          setRSASig(other.getRSASig());
        }
        if (other.hasDHESig()) {
          setDHESig(other.getDHESig());
        }
        if (other.hasDHEPubKeyLen()) {
          setDHEPubKeyLen(other.getDHEPubKeyLen());
        }
        if (other.hasDHEPubKey()) {
          setDHEPubKey(other.getDHEPubKey());
        }
        if (other.hasSigAlgType()) {
          setSigAlgType(other.getSigAlgType());
        }
        if (other.hasSigAlg()) {
          setSigAlg(other.getSigAlg());
        }
        if (other.hasSigHashAlg()) {
          setSigHashAlg(other.getSigHashAlg());
        }
        if (other.hasJOY()) {
          setJOY(other.getJOY());
        }
        if (other.hasJOYS()) {
          setJOYS(other.getJOYS());
        }
        if (other.hasSTARTTLS()) {
          setSTARTTLS(other.getSTARTTLS());
        }
        if (other.hasCertNonFlag()) {
          setCertNonFlag(other.getCertNonFlag());
        }
        if (other.hasJoyFp()) {
          setJoyFp(other.getJoyFp());
        }
        if (other.hasCertIntactFlag()) {
          setCertIntactFlag(other.getCertIntactFlag());
        }
        if (other.hasCertPath()) {
          setCertPath(other.getCertPath());
        }
        if (other.hasSessSecFlag()) {
          setSessSecFlag(other.getSessSecFlag());
        }
        if (other.hasFullText()) {
          setFullText(other.getFullText());
        }
        if (other.hasCliCertHashes()) {
          setCliCertHashes(other.getCliCertHashes());
        }
        if (other.hasSrvCertHashes()) {
          setSrvCertHashes(other.getSrvCertHashes());
        }
        if (other.hasCliCertNum()) {
          setCliCertNum(other.getCliCertNum());
        }
        if (other.hasSrvCertNum()) {
          setSrvCertNum(other.getSrvCertNum());
        }
        if (other.hasCertExist()) {
          setCertExist(other.getCertExist());
        }
        if (other.hasExtendEcGroupsClient()) {
          setExtendEcGroupsClient(other.getExtendEcGroupsClient());
        }
        if (other.hasLeafCertDaysRemaining()) {
          setLeafCertDaysRemaining(other.getLeafCertDaysRemaining());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                conType_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                aleLev_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                aleDes_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                handShaType_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                cliVer_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                cliGMTUniTime_ = input.readUInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 58: {
                cliRand_ = input.readBytes();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                cliSesID_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                cliCipSui_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                cliComMet_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 88: {
                srvVer_ = input.readUInt32();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              case 98: {
                srvName_ = input.readBytes();
                bitField0_ |= 0x00000800;
                break;
              } // case 98
              case 104: {
                srvNameAttr_ = input.readUInt32();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                srvGMTUniTime14_ = input.readUInt64();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 122: {
                srvRand_ = input.readBytes();
                bitField0_ |= 0x00004000;
                break;
              } // case 122
              case 130: {
                srvSesID_ = input.readBytes();
                bitField0_ |= 0x00008000;
                break;
              } // case 130
              case 138: {
                srvComprMet_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 144: {
                srvCertLen_ = input.readUInt32();
                bitField0_ |= 0x00020000;
                break;
              } // case 144
              case 152: {
                certResType_ = input.readUInt32();
                bitField0_ |= 0x00040000;
                break;
              } // case 152
              case 160: {
                cliCertLen_ = input.readUInt32();
                bitField0_ |= 0x00080000;
                break;
              } // case 160
              case 170: {
                rSAModOfSrvKeyExc_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 176: {
                rSAExpOfSrvKeyExc_ = input.readUInt64();
                bitField0_ |= 0x00200000;
                break;
              } // case 176
              case 186: {
                dHModOfSrvKeyExc_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                dHGenOfSrvKeyExc_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 202: {
                srvDHPubKey_ = input.readBytes();
                bitField0_ |= 0x01000000;
                break;
              } // case 202
              case 210: {
                preMasKeyEncryByRSA_ = input.readBytes();
                bitField0_ |= 0x02000000;
                break;
              } // case 210
              case 218: {
                cliDHPubKey_ = input.readBytes();
                bitField0_ |= 0x04000000;
                break;
              } // case 218
              case 224: {
                extTypeInSSL_ = input.readUInt32();
                bitField0_ |= 0x08000000;
                break;
              } // case 224
              case 232: {
                cliEllCurPoiFor_ = input.readUInt32();
                bitField0_ |= 0x10000000;
                break;
              } // case 232
              case 240: {
                cliEllCur_ = input.readUInt32();
                bitField0_ |= 0x20000000;
                break;
              } // case 240
              case 248: {
                srvEllCurPoiFor_ = input.readUInt32();
                bitField0_ |= 0x40000000;
                break;
              } // case 248
              case 256: {
                srvEllCur_ = input.readUInt32();
                bitField0_ |= 0x80000000;
                break;
              } // case 256
              case 266: {
                srvEllCurDHPubKey_ = input.readBytes();
                bitField1_ |= 0x00000001;
                break;
              } // case 266
              case 274: {
                cliEllCurDHPubKey_ = input.readBytes();
                bitField1_ |= 0x00000002;
                break;
              } // case 274
              case 280: {
                srvGMTUniTime35_ = input.readUInt64();
                bitField1_ |= 0x00000004;
                break;
              } // case 280
              case 288: {
                cliExtCnt_ = input.readUInt32();
                bitField1_ |= 0x00000008;
                break;
              } // case 288
              case 296: {
                srvExtCnt_ = input.readUInt32();
                bitField1_ |= 0x00000010;
                break;
              } // case 296
              case 304: {
                cliHandSkLen_ = input.readUInt32();
                bitField1_ |= 0x00000020;
                break;
              } // case 304
              case 312: {
                srvHandSkLen_ = input.readUInt32();
                bitField1_ |= 0x00000040;
                break;
              } // case 312
              case 322: {
                cliExt_ = input.readBytes();
                bitField1_ |= 0x00000080;
                break;
              } // case 322
              case 330: {
                srvExt_ = input.readBytes();
                bitField1_ |= 0x00000100;
                break;
              } // case 330
              case 336: {
                cliExtGrease_ = input.readUInt32();
                bitField1_ |= 0x00000200;
                break;
              } // case 336
              case 346: {
                cliJA3_ = input.readBytes();
                bitField1_ |= 0x00000400;
                break;
              } // case 346
              case 354: {
                srvJA3_ = input.readBytes();
                bitField1_ |= 0x00000800;
                break;
              } // case 354
              case 362: {
                cliSessTicket_ = input.readBytes();
                bitField1_ |= 0x00001000;
                break;
              } // case 362
              case 370: {
                srvSessTicket_ = input.readBytes();
                bitField1_ |= 0x00002000;
                break;
              } // case 370
              case 376: {
                authTag_ = input.readUInt32();
                bitField1_ |= 0x00004000;
                break;
              } // case 376
              case 384: {
                cliCertCnt_ = input.readUInt32();
                bitField1_ |= 0x00008000;
                break;
              } // case 384
              case 392: {
                srvCertCnt_ = input.readUInt32();
                bitField1_ |= 0x00010000;
                break;
              } // case 392
              case 402: {
                ecGroupsCli_ = input.readBytes();
                bitField1_ |= 0x00020000;
                break;
              } // case 402
              case 410: {
                ecPoiForByServ_ = input.readBytes();
                bitField1_ |= 0x00040000;
                break;
              } // case 410
              case 418: {
                etags_ = input.readBytes();
                bitField1_ |= 0x00080000;
                break;
              } // case 418
              case 426: {
                ttags_ = input.readBytes();
                bitField1_ |= 0x00100000;
                break;
              } // case 426
              case 434: {
                cliSesIDLen_ = input.readBytes();
                bitField1_ |= 0x00200000;
                break;
              } // case 434
              case 442: {
                srvSesIDLen_ = input.readBytes();
                bitField1_ |= 0x00400000;
                break;
              } // case 442
              case 450: {
                srvKeyExcLen_ = input.readBytes();
                bitField1_ |= 0x00800000;
                break;
              } // case 450
              case 458: {
                eCDHCurType_ = input.readBytes();
                bitField1_ |= 0x01000000;
                break;
              } // case 458
              case 466: {
                eCDHSig_ = input.readBytes();
                bitField1_ |= 0x02000000;
                break;
              } // case 466
              case 474: {
                dHEPLen_ = input.readBytes();
                bitField1_ |= 0x04000000;
                break;
              } // case 474
              case 482: {
                dHEGLen_ = input.readBytes();
                bitField1_ |= 0x08000000;
                break;
              } // case 482
              case 490: {
                cliKeyExcLen_ = input.readBytes();
                bitField1_ |= 0x10000000;
                break;
              } // case 490
              case 498: {
                encPubKey_ = input.readBytes();
                bitField1_ |= 0x20000000;
                break;
              } // case 498
              case 506: {
                encPubKeyLen_ = input.readBytes();
                bitField1_ |= 0x40000000;
                break;
              } // case 506
              case 514: {
                cliExtLen_ = input.readBytes();
                bitField1_ |= 0x80000000;
                break;
              } // case 514
              case 522: {
                srvExtLen_ = input.readBytes();
                bitField2_ |= 0x00000001;
                break;
              } // case 522
              case 530: {
                eCDHPubKeyLen_ = input.readBytes();
                bitField2_ |= 0x00000002;
                break;
              } // case 530
              case 538: {
                namType_ = input.readBytes();
                bitField2_ |= 0x00000004;
                break;
              } // case 538
              case 546: {
                namLen_ = input.readBytes();
                bitField2_ |= 0x00000008;
                break;
              } // case 546
              case 554: {
                ticDat_ = input.readBytes();
                bitField2_ |= 0x00000010;
                break;
              } // case 554
              case 562: {
                srvCipSui_ = input.readBytes();
                bitField2_ |= 0x00000020;
                break;
              } // case 562
              case 568: {
                cipSuiNum_ = input.readUInt32();
                bitField2_ |= 0x00000040;
                break;
              } // case 568
              case 578: {
                eCDHSigHash_ = input.readBytes();
                bitField2_ |= 0x00000080;
                break;
              } // case 578
              case 586: {
                dHESigHash_ = input.readBytes();
                bitField2_ |= 0x00000100;
                break;
              } // case 586
              case 594: {
                rSASigHash_ = input.readBytes();
                bitField2_ |= 0x00000200;
                break;
              } // case 594
              case 602: {
                greaseFlag_ = input.readBytes();
                bitField2_ |= 0x00000400;
                break;
              } // case 602
              case 610: {
                rSAModLen_ = input.readBytes();
                bitField2_ |= 0x00000800;
                break;
              } // case 610
              case 618: {
                rSAExpLen_ = input.readBytes();
                bitField2_ |= 0x00001000;
                break;
              } // case 618
              case 626: {
                rSASig_ = input.readBytes();
                bitField2_ |= 0x00002000;
                break;
              } // case 626
              case 634: {
                dHESig_ = input.readBytes();
                bitField2_ |= 0x00004000;
                break;
              } // case 634
              case 642: {
                dHEPubKeyLen_ = input.readBytes();
                bitField2_ |= 0x00008000;
                break;
              } // case 642
              case 650: {
                dHEPubKey_ = input.readBytes();
                bitField2_ |= 0x00010000;
                break;
              } // case 650
              case 658: {
                sigAlgType_ = input.readBytes();
                bitField2_ |= 0x00020000;
                break;
              } // case 658
              case 666: {
                sigAlg_ = input.readBytes();
                bitField2_ |= 0x00040000;
                break;
              } // case 666
              case 674: {
                sigHashAlg_ = input.readBytes();
                bitField2_ |= 0x00080000;
                break;
              } // case 674
              case 682: {
                jOY_ = input.readBytes();
                bitField2_ |= 0x00100000;
                break;
              } // case 682
              case 690: {
                jOYS_ = input.readBytes();
                bitField2_ |= 0x00200000;
                break;
              } // case 690
              case 698: {
                sTARTTLS_ = input.readBytes();
                bitField2_ |= 0x00400000;
                break;
              } // case 698
              case 706: {
                certNonFlag_ = input.readBytes();
                bitField2_ |= 0x00800000;
                break;
              } // case 706
              case 714: {
                joyFp_ = input.readBytes();
                bitField2_ |= 0x01000000;
                break;
              } // case 714
              case 722: {
                certIntactFlag_ = input.readBytes();
                bitField2_ |= 0x02000000;
                break;
              } // case 722
              case 730: {
                certPath_ = input.readBytes();
                bitField2_ |= 0x04000000;
                break;
              } // case 730
              case 738: {
                sessSecFlag_ = input.readBytes();
                bitField2_ |= 0x08000000;
                break;
              } // case 738
              case 746: {
                fullText_ = input.readBytes();
                bitField2_ |= 0x10000000;
                break;
              } // case 746
              case 754: {
                cliCertHashes_ = input.readBytes();
                bitField2_ |= 0x20000000;
                break;
              } // case 754
              case 762: {
                srvCertHashes_ = input.readBytes();
                bitField2_ |= 0x40000000;
                break;
              } // case 762
              case 768: {
                cliCertNum_ = input.readUInt32();
                bitField2_ |= 0x80000000;
                break;
              } // case 768
              case 776: {
                srvCertNum_ = input.readUInt32();
                bitField3_ |= 0x00000001;
                break;
              } // case 776
              case 784: {
                certExist_ = input.readUInt32();
                bitField3_ |= 0x00000002;
                break;
              } // case 784
              case 794: {
                extendEcGroupsClient_ = input.readBytes();
                bitField3_ |= 0x00000004;
                break;
              } // case 794
              case 800: {
                leafCertDaysRemaining_ = input.readUInt32();
                bitField3_ |= 0x00000008;
                break;
              } // case 800
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;
      private int bitField2_;
      private int bitField3_;

      private int conType_ ;
      /**
       * <code>optional uint32 conType = 1;</code>
       * @return Whether the conType field is set.
       */
      @java.lang.Override
      public boolean hasConType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 conType = 1;</code>
       * @return The conType.
       */
      @java.lang.Override
      public int getConType() {
        return conType_;
      }
      /**
       * <code>optional uint32 conType = 1;</code>
       * @param value The conType to set.
       * @return This builder for chaining.
       */
      public Builder setConType(int value) {

        conType_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 conType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        conType_ = 0;
        onChanged();
        return this;
      }

      private int aleLev_ ;
      /**
       * <code>optional uint32 aleLev = 2;</code>
       * @return Whether the aleLev field is set.
       */
      @java.lang.Override
      public boolean hasAleLev() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 aleLev = 2;</code>
       * @return The aleLev.
       */
      @java.lang.Override
      public int getAleLev() {
        return aleLev_;
      }
      /**
       * <code>optional uint32 aleLev = 2;</code>
       * @param value The aleLev to set.
       * @return This builder for chaining.
       */
      public Builder setAleLev(int value) {

        aleLev_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aleLev = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAleLev() {
        bitField0_ = (bitField0_ & ~0x00000002);
        aleLev_ = 0;
        onChanged();
        return this;
      }

      private int aleDes_ ;
      /**
       * <code>optional uint32 aleDes = 3;</code>
       * @return Whether the aleDes field is set.
       */
      @java.lang.Override
      public boolean hasAleDes() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint32 aleDes = 3;</code>
       * @return The aleDes.
       */
      @java.lang.Override
      public int getAleDes() {
        return aleDes_;
      }
      /**
       * <code>optional uint32 aleDes = 3;</code>
       * @param value The aleDes to set.
       * @return This builder for chaining.
       */
      public Builder setAleDes(int value) {

        aleDes_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 aleDes = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAleDes() {
        bitField0_ = (bitField0_ & ~0x00000004);
        aleDes_ = 0;
        onChanged();
        return this;
      }

      private int handShaType_ ;
      /**
       * <code>optional uint32 handShaType = 4;</code>
       * @return Whether the handShaType field is set.
       */
      @java.lang.Override
      public boolean hasHandShaType() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 handShaType = 4;</code>
       * @return The handShaType.
       */
      @java.lang.Override
      public int getHandShaType() {
        return handShaType_;
      }
      /**
       * <code>optional uint32 handShaType = 4;</code>
       * @param value The handShaType to set.
       * @return This builder for chaining.
       */
      public Builder setHandShaType(int value) {

        handShaType_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 handShaType = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHandShaType() {
        bitField0_ = (bitField0_ & ~0x00000008);
        handShaType_ = 0;
        onChanged();
        return this;
      }

      private int cliVer_ ;
      /**
       * <code>optional uint32 cliVer = 5;</code>
       * @return Whether the cliVer field is set.
       */
      @java.lang.Override
      public boolean hasCliVer() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 cliVer = 5;</code>
       * @return The cliVer.
       */
      @java.lang.Override
      public int getCliVer() {
        return cliVer_;
      }
      /**
       * <code>optional uint32 cliVer = 5;</code>
       * @param value The cliVer to set.
       * @return This builder for chaining.
       */
      public Builder setCliVer(int value) {

        cliVer_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliVer = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliVer() {
        bitField0_ = (bitField0_ & ~0x00000010);
        cliVer_ = 0;
        onChanged();
        return this;
      }

      private long cliGMTUniTime_ ;
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       * @return Whether the cliGMTUniTime field is set.
       */
      @java.lang.Override
      public boolean hasCliGMTUniTime() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       * @return The cliGMTUniTime.
       */
      @java.lang.Override
      public long getCliGMTUniTime() {
        return cliGMTUniTime_;
      }
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       * @param value The cliGMTUniTime to set.
       * @return This builder for chaining.
       */
      public Builder setCliGMTUniTime(long value) {

        cliGMTUniTime_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 cliGMTUniTime = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliGMTUniTime() {
        bitField0_ = (bitField0_ & ~0x00000020);
        cliGMTUniTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliRand_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliRand = 7;</code>
       * @return Whether the cliRand field is set.
       */
      @java.lang.Override
      public boolean hasCliRand() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional bytes cliRand = 7;</code>
       * @return The cliRand.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliRand() {
        return cliRand_;
      }
      /**
       * <code>optional bytes cliRand = 7;</code>
       * @param value The cliRand to set.
       * @return This builder for chaining.
       */
      public Builder setCliRand(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliRand_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliRand = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliRand() {
        bitField0_ = (bitField0_ & ~0x00000040);
        cliRand_ = getDefaultInstance().getCliRand();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliSesID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliSesID = 8;</code>
       * @return Whether the cliSesID field is set.
       */
      @java.lang.Override
      public boolean hasCliSesID() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes cliSesID = 8;</code>
       * @return The cliSesID.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliSesID() {
        return cliSesID_;
      }
      /**
       * <code>optional bytes cliSesID = 8;</code>
       * @param value The cliSesID to set.
       * @return This builder for chaining.
       */
      public Builder setCliSesID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliSesID_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliSesID = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliSesID() {
        bitField0_ = (bitField0_ & ~0x00000080);
        cliSesID_ = getDefaultInstance().getCliSesID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliCipSui_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       * @return Whether the cliCipSui field is set.
       */
      @java.lang.Override
      public boolean hasCliCipSui() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       * @return The cliCipSui.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliCipSui() {
        return cliCipSui_;
      }
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       * @param value The cliCipSui to set.
       * @return This builder for chaining.
       */
      public Builder setCliCipSui(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliCipSui_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliCipSui = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliCipSui() {
        bitField0_ = (bitField0_ & ~0x00000100);
        cliCipSui_ = getDefaultInstance().getCliCipSui();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliComMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliComMet = 10;</code>
       * @return Whether the cliComMet field is set.
       */
      @java.lang.Override
      public boolean hasCliComMet() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes cliComMet = 10;</code>
       * @return The cliComMet.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliComMet() {
        return cliComMet_;
      }
      /**
       * <code>optional bytes cliComMet = 10;</code>
       * @param value The cliComMet to set.
       * @return This builder for chaining.
       */
      public Builder setCliComMet(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliComMet_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliComMet = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliComMet() {
        bitField0_ = (bitField0_ & ~0x00000200);
        cliComMet_ = getDefaultInstance().getCliComMet();
        onChanged();
        return this;
      }

      private int srvVer_ ;
      /**
       * <code>optional uint32 srvVer = 11;</code>
       * @return Whether the srvVer field is set.
       */
      @java.lang.Override
      public boolean hasSrvVer() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional uint32 srvVer = 11;</code>
       * @return The srvVer.
       */
      @java.lang.Override
      public int getSrvVer() {
        return srvVer_;
      }
      /**
       * <code>optional uint32 srvVer = 11;</code>
       * @param value The srvVer to set.
       * @return This builder for chaining.
       */
      public Builder setSrvVer(int value) {

        srvVer_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvVer = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvVer() {
        bitField0_ = (bitField0_ & ~0x00000400);
        srvVer_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvName_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvName = 12;</code>
       * @return Whether the srvName field is set.
       */
      @java.lang.Override
      public boolean hasSrvName() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes srvName = 12;</code>
       * @return The srvName.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvName() {
        return srvName_;
      }
      /**
       * <code>optional bytes srvName = 12;</code>
       * @param value The srvName to set.
       * @return This builder for chaining.
       */
      public Builder setSrvName(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvName_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvName = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvName() {
        bitField0_ = (bitField0_ & ~0x00000800);
        srvName_ = getDefaultInstance().getSrvName();
        onChanged();
        return this;
      }

      private int srvNameAttr_ ;
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       * @return Whether the srvNameAttr field is set.
       */
      @java.lang.Override
      public boolean hasSrvNameAttr() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       * @return The srvNameAttr.
       */
      @java.lang.Override
      public int getSrvNameAttr() {
        return srvNameAttr_;
      }
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       * @param value The srvNameAttr to set.
       * @return This builder for chaining.
       */
      public Builder setSrvNameAttr(int value) {

        srvNameAttr_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvNameAttr = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvNameAttr() {
        bitField0_ = (bitField0_ & ~0x00001000);
        srvNameAttr_ = 0;
        onChanged();
        return this;
      }

      private long srvGMTUniTime14_ ;
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       * @return Whether the srvGMTUniTime field is set.
       */
      @java.lang.Override
      public boolean hasSrvGMTUniTime14() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       * @return The srvGMTUniTime.
       */
      @java.lang.Override
      public long getSrvGMTUniTime14() {
        return srvGMTUniTime14_;
      }
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       * @param value The srvGMTUniTime to set.
       * @return This builder for chaining.
       */
      public Builder setSrvGMTUniTime14(long value) {

        srvGMTUniTime14_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 srvGMTUniTime = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvGMTUniTime14() {
        bitField0_ = (bitField0_ & ~0x00002000);
        srvGMTUniTime14_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvRand_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvRand = 15;</code>
       * @return Whether the srvRand field is set.
       */
      @java.lang.Override
      public boolean hasSrvRand() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes srvRand = 15;</code>
       * @return The srvRand.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvRand() {
        return srvRand_;
      }
      /**
       * <code>optional bytes srvRand = 15;</code>
       * @param value The srvRand to set.
       * @return This builder for chaining.
       */
      public Builder setSrvRand(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvRand_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvRand = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvRand() {
        bitField0_ = (bitField0_ & ~0x00004000);
        srvRand_ = getDefaultInstance().getSrvRand();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvSesID_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvSesID = 16;</code>
       * @return Whether the srvSesID field is set.
       */
      @java.lang.Override
      public boolean hasSrvSesID() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes srvSesID = 16;</code>
       * @return The srvSesID.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvSesID() {
        return srvSesID_;
      }
      /**
       * <code>optional bytes srvSesID = 16;</code>
       * @param value The srvSesID to set.
       * @return This builder for chaining.
       */
      public Builder setSrvSesID(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvSesID_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvSesID = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvSesID() {
        bitField0_ = (bitField0_ & ~0x00008000);
        srvSesID_ = getDefaultInstance().getSrvSesID();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvComprMet_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       * @return Whether the srvComprMet field is set.
       */
      @java.lang.Override
      public boolean hasSrvComprMet() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       * @return The srvComprMet.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvComprMet() {
        return srvComprMet_;
      }
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       * @param value The srvComprMet to set.
       * @return This builder for chaining.
       */
      public Builder setSrvComprMet(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvComprMet_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvComprMet = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvComprMet() {
        bitField0_ = (bitField0_ & ~0x00010000);
        srvComprMet_ = getDefaultInstance().getSrvComprMet();
        onChanged();
        return this;
      }

      private int srvCertLen_ ;
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       * @return Whether the srvCertLen field is set.
       */
      @java.lang.Override
      public boolean hasSrvCertLen() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       * @return The srvCertLen.
       */
      @java.lang.Override
      public int getSrvCertLen() {
        return srvCertLen_;
      }
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       * @param value The srvCertLen to set.
       * @return This builder for chaining.
       */
      public Builder setSrvCertLen(int value) {

        srvCertLen_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCertLen = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvCertLen() {
        bitField0_ = (bitField0_ & ~0x00020000);
        srvCertLen_ = 0;
        onChanged();
        return this;
      }

      private int certResType_ ;
      /**
       * <code>optional uint32 certResType = 19;</code>
       * @return Whether the certResType field is set.
       */
      @java.lang.Override
      public boolean hasCertResType() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <code>optional uint32 certResType = 19;</code>
       * @return The certResType.
       */
      @java.lang.Override
      public int getCertResType() {
        return certResType_;
      }
      /**
       * <code>optional uint32 certResType = 19;</code>
       * @param value The certResType to set.
       * @return This builder for chaining.
       */
      public Builder setCertResType(int value) {

        certResType_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 certResType = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertResType() {
        bitField0_ = (bitField0_ & ~0x00040000);
        certResType_ = 0;
        onChanged();
        return this;
      }

      private int cliCertLen_ ;
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       * @return Whether the cliCertLen field is set.
       */
      @java.lang.Override
      public boolean hasCliCertLen() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       * @return The cliCertLen.
       */
      @java.lang.Override
      public int getCliCertLen() {
        return cliCertLen_;
      }
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       * @param value The cliCertLen to set.
       * @return This builder for chaining.
       */
      public Builder setCliCertLen(int value) {

        cliCertLen_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliCertLen = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliCertLen() {
        bitField0_ = (bitField0_ & ~0x00080000);
        cliCertLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       * @return Whether the rSAModOfSrvKeyExc field is set.
       */
      @java.lang.Override
      public boolean hasRSAModOfSrvKeyExc() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       * @return The rSAModOfSrvKeyExc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRSAModOfSrvKeyExc() {
        return rSAModOfSrvKeyExc_;
      }
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       * @param value The rSAModOfSrvKeyExc to set.
       * @return This builder for chaining.
       */
      public Builder setRSAModOfSrvKeyExc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rSAModOfSrvKeyExc_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAModOfSrvKeyExc = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSAModOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00100000);
        rSAModOfSrvKeyExc_ = getDefaultInstance().getRSAModOfSrvKeyExc();
        onChanged();
        return this;
      }

      private long rSAExpOfSrvKeyExc_ ;
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       * @return Whether the rSAExpOfSrvKeyExc field is set.
       */
      @java.lang.Override
      public boolean hasRSAExpOfSrvKeyExc() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       * @return The rSAExpOfSrvKeyExc.
       */
      @java.lang.Override
      public long getRSAExpOfSrvKeyExc() {
        return rSAExpOfSrvKeyExc_;
      }
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       * @param value The rSAExpOfSrvKeyExc to set.
       * @return This builder for chaining.
       */
      public Builder setRSAExpOfSrvKeyExc(long value) {

        rSAExpOfSrvKeyExc_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 RSAExpOfSrvKeyExc = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSAExpOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00200000);
        rSAExpOfSrvKeyExc_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHModOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       * @return Whether the dHModOfSrvKeyExc field is set.
       */
      @java.lang.Override
      public boolean hasDHModOfSrvKeyExc() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       * @return The dHModOfSrvKeyExc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHModOfSrvKeyExc() {
        return dHModOfSrvKeyExc_;
      }
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       * @param value The dHModOfSrvKeyExc to set.
       * @return This builder for chaining.
       */
      public Builder setDHModOfSrvKeyExc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHModOfSrvKeyExc_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHModOfSrvKeyExc = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHModOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00400000);
        dHModOfSrvKeyExc_ = getDefaultInstance().getDHModOfSrvKeyExc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHGenOfSrvKeyExc_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       * @return Whether the dHGenOfSrvKeyExc field is set.
       */
      @java.lang.Override
      public boolean hasDHGenOfSrvKeyExc() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       * @return The dHGenOfSrvKeyExc.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHGenOfSrvKeyExc() {
        return dHGenOfSrvKeyExc_;
      }
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       * @param value The dHGenOfSrvKeyExc to set.
       * @return This builder for chaining.
       */
      public Builder setDHGenOfSrvKeyExc(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHGenOfSrvKeyExc_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHGenOfSrvKeyExc = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHGenOfSrvKeyExc() {
        bitField0_ = (bitField0_ & ~0x00800000);
        dHGenOfSrvKeyExc_ = getDefaultInstance().getDHGenOfSrvKeyExc();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       * @return Whether the srvDHPubKey field is set.
       */
      @java.lang.Override
      public boolean hasSrvDHPubKey() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       * @return The srvDHPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvDHPubKey() {
        return srvDHPubKey_;
      }
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       * @param value The srvDHPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setSrvDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvDHPubKey_ = value;
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvDHPubKey = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvDHPubKey() {
        bitField0_ = (bitField0_ & ~0x01000000);
        srvDHPubKey_ = getDefaultInstance().getSrvDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString preMasKeyEncryByRSA_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       * @return Whether the preMasKeyEncryByRSA field is set.
       */
      @java.lang.Override
      public boolean hasPreMasKeyEncryByRSA() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       * @return The preMasKeyEncryByRSA.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getPreMasKeyEncryByRSA() {
        return preMasKeyEncryByRSA_;
      }
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       * @param value The preMasKeyEncryByRSA to set.
       * @return This builder for chaining.
       */
      public Builder setPreMasKeyEncryByRSA(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        preMasKeyEncryByRSA_ = value;
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes preMasKeyEncryByRSA = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearPreMasKeyEncryByRSA() {
        bitField0_ = (bitField0_ & ~0x02000000);
        preMasKeyEncryByRSA_ = getDefaultInstance().getPreMasKeyEncryByRSA();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       * @return Whether the cliDHPubKey field is set.
       */
      @java.lang.Override
      public boolean hasCliDHPubKey() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       * @return The cliDHPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliDHPubKey() {
        return cliDHPubKey_;
      }
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       * @param value The cliDHPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setCliDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliDHPubKey_ = value;
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliDHPubKey = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliDHPubKey() {
        bitField0_ = (bitField0_ & ~0x04000000);
        cliDHPubKey_ = getDefaultInstance().getCliDHPubKey();
        onChanged();
        return this;
      }

      private int extTypeInSSL_ ;
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       * @return Whether the extTypeInSSL field is set.
       */
      @java.lang.Override
      public boolean hasExtTypeInSSL() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       * @return The extTypeInSSL.
       */
      @java.lang.Override
      public int getExtTypeInSSL() {
        return extTypeInSSL_;
      }
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       * @param value The extTypeInSSL to set.
       * @return This builder for chaining.
       */
      public Builder setExtTypeInSSL(int value) {

        extTypeInSSL_ = value;
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 extTypeInSSL = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtTypeInSSL() {
        bitField0_ = (bitField0_ & ~0x08000000);
        extTypeInSSL_ = 0;
        onChanged();
        return this;
      }

      private int cliEllCurPoiFor_ ;
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       * @return Whether the cliEllCurPoiFor field is set.
       */
      @java.lang.Override
      public boolean hasCliEllCurPoiFor() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       * @return The cliEllCurPoiFor.
       */
      @java.lang.Override
      public int getCliEllCurPoiFor() {
        return cliEllCurPoiFor_;
      }
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       * @param value The cliEllCurPoiFor to set.
       * @return This builder for chaining.
       */
      public Builder setCliEllCurPoiFor(int value) {

        cliEllCurPoiFor_ = value;
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliEllCurPoiFor = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliEllCurPoiFor() {
        bitField0_ = (bitField0_ & ~0x10000000);
        cliEllCurPoiFor_ = 0;
        onChanged();
        return this;
      }

      private int cliEllCur_ ;
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       * @return Whether the cliEllCur field is set.
       */
      @java.lang.Override
      public boolean hasCliEllCur() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       * @return The cliEllCur.
       */
      @java.lang.Override
      public int getCliEllCur() {
        return cliEllCur_;
      }
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       * @param value The cliEllCur to set.
       * @return This builder for chaining.
       */
      public Builder setCliEllCur(int value) {

        cliEllCur_ = value;
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliEllCur = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliEllCur() {
        bitField0_ = (bitField0_ & ~0x20000000);
        cliEllCur_ = 0;
        onChanged();
        return this;
      }

      private int srvEllCurPoiFor_ ;
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       * @return Whether the srvEllCurPoiFor field is set.
       */
      @java.lang.Override
      public boolean hasSrvEllCurPoiFor() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       * @return The srvEllCurPoiFor.
       */
      @java.lang.Override
      public int getSrvEllCurPoiFor() {
        return srvEllCurPoiFor_;
      }
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       * @param value The srvEllCurPoiFor to set.
       * @return This builder for chaining.
       */
      public Builder setSrvEllCurPoiFor(int value) {

        srvEllCurPoiFor_ = value;
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvEllCurPoiFor = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvEllCurPoiFor() {
        bitField0_ = (bitField0_ & ~0x40000000);
        srvEllCurPoiFor_ = 0;
        onChanged();
        return this;
      }

      private int srvEllCur_ ;
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       * @return Whether the srvEllCur field is set.
       */
      @java.lang.Override
      public boolean hasSrvEllCur() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       * @return The srvEllCur.
       */
      @java.lang.Override
      public int getSrvEllCur() {
        return srvEllCur_;
      }
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       * @param value The srvEllCur to set.
       * @return This builder for chaining.
       */
      public Builder setSrvEllCur(int value) {

        srvEllCur_ = value;
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvEllCur = 32;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvEllCur() {
        bitField0_ = (bitField0_ & ~0x80000000);
        srvEllCur_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       * @return Whether the srvEllCurDHPubKey field is set.
       */
      @java.lang.Override
      public boolean hasSrvEllCurDHPubKey() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       * @return The srvEllCurDHPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvEllCurDHPubKey() {
        return srvEllCurDHPubKey_;
      }
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       * @param value The srvEllCurDHPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setSrvEllCurDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvEllCurDHPubKey_ = value;
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvEllCurDHPubKey = 33;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvEllCurDHPubKey() {
        bitField1_ = (bitField1_ & ~0x00000001);
        srvEllCurDHPubKey_ = getDefaultInstance().getSrvEllCurDHPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliEllCurDHPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       * @return Whether the cliEllCurDHPubKey field is set.
       */
      @java.lang.Override
      public boolean hasCliEllCurDHPubKey() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       * @return The cliEllCurDHPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliEllCurDHPubKey() {
        return cliEllCurDHPubKey_;
      }
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       * @param value The cliEllCurDHPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setCliEllCurDHPubKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliEllCurDHPubKey_ = value;
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliEllCurDHPubKey = 34;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliEllCurDHPubKey() {
        bitField1_ = (bitField1_ & ~0x00000002);
        cliEllCurDHPubKey_ = getDefaultInstance().getCliEllCurDHPubKey();
        onChanged();
        return this;
      }

      private long srvGMTUniTime35_ ;
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       * @return Whether the srvGMTUniTime field is set.
       */
      @java.lang.Override
      public boolean hasSrvGMTUniTime35() {
        return ((bitField1_ & 0x00000004) != 0);
      }
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       * @return The srvGMTUniTime.
       */
      @java.lang.Override
      public long getSrvGMTUniTime35() {
        return srvGMTUniTime35_;
      }
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       * @param value The srvGMTUniTime to set.
       * @return This builder for chaining.
       */
      public Builder setSrvGMTUniTime35(long value) {

        srvGMTUniTime35_ = value;
        bitField1_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint64 srvGMTUni_Time = 35;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvGMTUniTime35() {
        bitField1_ = (bitField1_ & ~0x00000004);
        srvGMTUniTime35_ = 0L;
        onChanged();
        return this;
      }

      private int cliExtCnt_ ;
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       * @return Whether the cliExtCnt field is set.
       */
      @java.lang.Override
      public boolean hasCliExtCnt() {
        return ((bitField1_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       * @return The cliExtCnt.
       */
      @java.lang.Override
      public int getCliExtCnt() {
        return cliExtCnt_;
      }
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       * @param value The cliExtCnt to set.
       * @return This builder for chaining.
       */
      public Builder setCliExtCnt(int value) {

        cliExtCnt_ = value;
        bitField1_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliExtCnt = 36;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliExtCnt() {
        bitField1_ = (bitField1_ & ~0x00000008);
        cliExtCnt_ = 0;
        onChanged();
        return this;
      }

      private int srvExtCnt_ ;
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       * @return Whether the srvExtCnt field is set.
       */
      @java.lang.Override
      public boolean hasSrvExtCnt() {
        return ((bitField1_ & 0x00000010) != 0);
      }
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       * @return The srvExtCnt.
       */
      @java.lang.Override
      public int getSrvExtCnt() {
        return srvExtCnt_;
      }
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       * @param value The srvExtCnt to set.
       * @return This builder for chaining.
       */
      public Builder setSrvExtCnt(int value) {

        srvExtCnt_ = value;
        bitField1_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvExtCnt = 37;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvExtCnt() {
        bitField1_ = (bitField1_ & ~0x00000010);
        srvExtCnt_ = 0;
        onChanged();
        return this;
      }

      private int cliHandSkLen_ ;
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       * @return Whether the cliHandSkLen field is set.
       */
      @java.lang.Override
      public boolean hasCliHandSkLen() {
        return ((bitField1_ & 0x00000020) != 0);
      }
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       * @return The cliHandSkLen.
       */
      @java.lang.Override
      public int getCliHandSkLen() {
        return cliHandSkLen_;
      }
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       * @param value The cliHandSkLen to set.
       * @return This builder for chaining.
       */
      public Builder setCliHandSkLen(int value) {

        cliHandSkLen_ = value;
        bitField1_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliHandSkLen = 38;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliHandSkLen() {
        bitField1_ = (bitField1_ & ~0x00000020);
        cliHandSkLen_ = 0;
        onChanged();
        return this;
      }

      private int srvHandSkLen_ ;
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       * @return Whether the srvHandSkLen field is set.
       */
      @java.lang.Override
      public boolean hasSrvHandSkLen() {
        return ((bitField1_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       * @return The srvHandSkLen.
       */
      @java.lang.Override
      public int getSrvHandSkLen() {
        return srvHandSkLen_;
      }
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       * @param value The srvHandSkLen to set.
       * @return This builder for chaining.
       */
      public Builder setSrvHandSkLen(int value) {

        srvHandSkLen_ = value;
        bitField1_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvHandSkLen = 39;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvHandSkLen() {
        bitField1_ = (bitField1_ & ~0x00000040);
        srvHandSkLen_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliExt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliExt = 40;</code>
       * @return Whether the cliExt field is set.
       */
      @java.lang.Override
      public boolean hasCliExt() {
        return ((bitField1_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes cliExt = 40;</code>
       * @return The cliExt.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliExt() {
        return cliExt_;
      }
      /**
       * <code>optional bytes cliExt = 40;</code>
       * @param value The cliExt to set.
       * @return This builder for chaining.
       */
      public Builder setCliExt(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliExt_ = value;
        bitField1_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliExt = 40;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliExt() {
        bitField1_ = (bitField1_ & ~0x00000080);
        cliExt_ = getDefaultInstance().getCliExt();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvExt_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvExt = 41;</code>
       * @return Whether the srvExt field is set.
       */
      @java.lang.Override
      public boolean hasSrvExt() {
        return ((bitField1_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes srvExt = 41;</code>
       * @return The srvExt.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvExt() {
        return srvExt_;
      }
      /**
       * <code>optional bytes srvExt = 41;</code>
       * @param value The srvExt to set.
       * @return This builder for chaining.
       */
      public Builder setSrvExt(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvExt_ = value;
        bitField1_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvExt = 41;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvExt() {
        bitField1_ = (bitField1_ & ~0x00000100);
        srvExt_ = getDefaultInstance().getSrvExt();
        onChanged();
        return this;
      }

      private int cliExtGrease_ ;
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       * @return Whether the cliExtGrease field is set.
       */
      @java.lang.Override
      public boolean hasCliExtGrease() {
        return ((bitField1_ & 0x00000200) != 0);
      }
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       * @return The cliExtGrease.
       */
      @java.lang.Override
      public int getCliExtGrease() {
        return cliExtGrease_;
      }
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       * @param value The cliExtGrease to set.
       * @return This builder for chaining.
       */
      public Builder setCliExtGrease(int value) {

        cliExtGrease_ = value;
        bitField1_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliExtGrease = 42;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliExtGrease() {
        bitField1_ = (bitField1_ & ~0x00000200);
        cliExtGrease_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliJA3_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       * @return Whether the cliJA3 field is set.
       */
      @java.lang.Override
      public boolean hasCliJA3() {
        return ((bitField1_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       * @return The cliJA3.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliJA3() {
        return cliJA3_;
      }
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       * @param value The cliJA3 to set.
       * @return This builder for chaining.
       */
      public Builder setCliJA3(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliJA3_ = value;
        bitField1_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliJA3 = 43;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliJA3() {
        bitField1_ = (bitField1_ & ~0x00000400);
        cliJA3_ = getDefaultInstance().getCliJA3();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvJA3_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       * @return Whether the srvJA3 field is set.
       */
      @java.lang.Override
      public boolean hasSrvJA3() {
        return ((bitField1_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       * @return The srvJA3.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvJA3() {
        return srvJA3_;
      }
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       * @param value The srvJA3 to set.
       * @return This builder for chaining.
       */
      public Builder setSrvJA3(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvJA3_ = value;
        bitField1_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvJA3 = 44;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvJA3() {
        bitField1_ = (bitField1_ & ~0x00000800);
        srvJA3_ = getDefaultInstance().getSrvJA3();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       * @return Whether the cliSessTicket field is set.
       */
      @java.lang.Override
      public boolean hasCliSessTicket() {
        return ((bitField1_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       * @return The cliSessTicket.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliSessTicket() {
        return cliSessTicket_;
      }
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       * @param value The cliSessTicket to set.
       * @return This builder for chaining.
       */
      public Builder setCliSessTicket(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliSessTicket_ = value;
        bitField1_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliSessTicket = 45;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliSessTicket() {
        bitField1_ = (bitField1_ & ~0x00001000);
        cliSessTicket_ = getDefaultInstance().getCliSessTicket();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvSessTicket_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       * @return Whether the srvSessTicket field is set.
       */
      @java.lang.Override
      public boolean hasSrvSessTicket() {
        return ((bitField1_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       * @return The srvSessTicket.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvSessTicket() {
        return srvSessTicket_;
      }
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       * @param value The srvSessTicket to set.
       * @return This builder for chaining.
       */
      public Builder setSrvSessTicket(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvSessTicket_ = value;
        bitField1_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvSessTicket = 46;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvSessTicket() {
        bitField1_ = (bitField1_ & ~0x00002000);
        srvSessTicket_ = getDefaultInstance().getSrvSessTicket();
        onChanged();
        return this;
      }

      private int authTag_ ;
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       * @return Whether the authTag field is set.
       */
      @java.lang.Override
      public boolean hasAuthTag() {
        return ((bitField1_ & 0x00004000) != 0);
      }
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       * @return The authTag.
       */
      @java.lang.Override
      public int getAuthTag() {
        return authTag_;
      }
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       * @param value The authTag to set.
       * @return This builder for chaining.
       */
      public Builder setAuthTag(int value) {

        authTag_ = value;
        bitField1_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 AuthTag = 47;</code>
       * @return This builder for chaining.
       */
      public Builder clearAuthTag() {
        bitField1_ = (bitField1_ & ~0x00004000);
        authTag_ = 0;
        onChanged();
        return this;
      }

      private int cliCertCnt_ ;
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       * @return Whether the cliCertCnt field is set.
       */
      @java.lang.Override
      public boolean hasCliCertCnt() {
        return ((bitField1_ & 0x00008000) != 0);
      }
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       * @return The cliCertCnt.
       */
      @java.lang.Override
      public int getCliCertCnt() {
        return cliCertCnt_;
      }
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       * @param value The cliCertCnt to set.
       * @return This builder for chaining.
       */
      public Builder setCliCertCnt(int value) {

        cliCertCnt_ = value;
        bitField1_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliCertCnt = 48;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliCertCnt() {
        bitField1_ = (bitField1_ & ~0x00008000);
        cliCertCnt_ = 0;
        onChanged();
        return this;
      }

      private int srvCertCnt_ ;
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       * @return Whether the srvCertCnt field is set.
       */
      @java.lang.Override
      public boolean hasSrvCertCnt() {
        return ((bitField1_ & 0x00010000) != 0);
      }
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       * @return The srvCertCnt.
       */
      @java.lang.Override
      public int getSrvCertCnt() {
        return srvCertCnt_;
      }
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       * @param value The srvCertCnt to set.
       * @return This builder for chaining.
       */
      public Builder setSrvCertCnt(int value) {

        srvCertCnt_ = value;
        bitField1_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCertCnt = 49;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvCertCnt() {
        bitField1_ = (bitField1_ & ~0x00010000);
        srvCertCnt_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ecGroupsCli_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       * @return Whether the ecGroupsCli field is set.
       */
      @java.lang.Override
      public boolean hasEcGroupsCli() {
        return ((bitField1_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       * @return The ecGroupsCli.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEcGroupsCli() {
        return ecGroupsCli_;
      }
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       * @param value The ecGroupsCli to set.
       * @return This builder for chaining.
       */
      public Builder setEcGroupsCli(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ecGroupsCli_ = value;
        bitField1_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ecGroupsCli = 50;</code>
       * @return This builder for chaining.
       */
      public Builder clearEcGroupsCli() {
        bitField1_ = (bitField1_ & ~0x00020000);
        ecGroupsCli_ = getDefaultInstance().getEcGroupsCli();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ecPoiForByServ_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       * @return Whether the ecPoiForByServ field is set.
       */
      @java.lang.Override
      public boolean hasEcPoiForByServ() {
        return ((bitField1_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       * @return The ecPoiForByServ.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEcPoiForByServ() {
        return ecPoiForByServ_;
      }
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       * @param value The ecPoiForByServ to set.
       * @return This builder for chaining.
       */
      public Builder setEcPoiForByServ(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ecPoiForByServ_ = value;
        bitField1_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ecPoiForByServ = 51;</code>
       * @return This builder for chaining.
       */
      public Builder clearEcPoiForByServ() {
        bitField1_ = (bitField1_ & ~0x00040000);
        ecPoiForByServ_ = getDefaultInstance().getEcPoiForByServ();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString etags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes etags = 52;</code>
       * @return Whether the etags field is set.
       */
      @java.lang.Override
      public boolean hasEtags() {
        return ((bitField1_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes etags = 52;</code>
       * @return The etags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEtags() {
        return etags_;
      }
      /**
       * <code>optional bytes etags = 52;</code>
       * @param value The etags to set.
       * @return This builder for chaining.
       */
      public Builder setEtags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        etags_ = value;
        bitField1_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes etags = 52;</code>
       * @return This builder for chaining.
       */
      public Builder clearEtags() {
        bitField1_ = (bitField1_ & ~0x00080000);
        etags_ = getDefaultInstance().getEtags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ttags_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ttags = 53;</code>
       * @return Whether the ttags field is set.
       */
      @java.lang.Override
      public boolean hasTtags() {
        return ((bitField1_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes ttags = 53;</code>
       * @return The ttags.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTtags() {
        return ttags_;
      }
      /**
       * <code>optional bytes ttags = 53;</code>
       * @param value The ttags to set.
       * @return This builder for chaining.
       */
      public Builder setTtags(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ttags_ = value;
        bitField1_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ttags = 53;</code>
       * @return This builder for chaining.
       */
      public Builder clearTtags() {
        bitField1_ = (bitField1_ & ~0x00100000);
        ttags_ = getDefaultInstance().getTtags();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       * @return Whether the cliSesIDLen field is set.
       */
      @java.lang.Override
      public boolean hasCliSesIDLen() {
        return ((bitField1_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       * @return The cliSesIDLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliSesIDLen() {
        return cliSesIDLen_;
      }
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       * @param value The cliSesIDLen to set.
       * @return This builder for chaining.
       */
      public Builder setCliSesIDLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliSesIDLen_ = value;
        bitField1_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliSesIDLen = 54;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliSesIDLen() {
        bitField1_ = (bitField1_ & ~0x00200000);
        cliSesIDLen_ = getDefaultInstance().getCliSesIDLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvSesIDLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       * @return Whether the srvSesIDLen field is set.
       */
      @java.lang.Override
      public boolean hasSrvSesIDLen() {
        return ((bitField1_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       * @return The srvSesIDLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvSesIDLen() {
        return srvSesIDLen_;
      }
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       * @param value The srvSesIDLen to set.
       * @return This builder for chaining.
       */
      public Builder setSrvSesIDLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvSesIDLen_ = value;
        bitField1_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvSesIDLen = 55;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvSesIDLen() {
        bitField1_ = (bitField1_ & ~0x00400000);
        srvSesIDLen_ = getDefaultInstance().getSrvSesIDLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       * @return Whether the srvKeyExcLen field is set.
       */
      @java.lang.Override
      public boolean hasSrvKeyExcLen() {
        return ((bitField1_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       * @return The srvKeyExcLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvKeyExcLen() {
        return srvKeyExcLen_;
      }
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       * @param value The srvKeyExcLen to set.
       * @return This builder for chaining.
       */
      public Builder setSrvKeyExcLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvKeyExcLen_ = value;
        bitField1_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvKeyExcLen = 56;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvKeyExcLen() {
        bitField1_ = (bitField1_ & ~0x00800000);
        srvKeyExcLen_ = getDefaultInstance().getSrvKeyExcLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHCurType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       * @return Whether the eCDHCurType field is set.
       */
      @java.lang.Override
      public boolean hasECDHCurType() {
        return ((bitField1_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       * @return The eCDHCurType.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getECDHCurType() {
        return eCDHCurType_;
      }
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       * @param value The eCDHCurType to set.
       * @return This builder for chaining.
       */
      public Builder setECDHCurType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        eCDHCurType_ = value;
        bitField1_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHCurType = 57;</code>
       * @return This builder for chaining.
       */
      public Builder clearECDHCurType() {
        bitField1_ = (bitField1_ & ~0x01000000);
        eCDHCurType_ = getDefaultInstance().getECDHCurType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHSig_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       * @return Whether the eCDHSig field is set.
       */
      @java.lang.Override
      public boolean hasECDHSig() {
        return ((bitField1_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       * @return The eCDHSig.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getECDHSig() {
        return eCDHSig_;
      }
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       * @param value The eCDHSig to set.
       * @return This builder for chaining.
       */
      public Builder setECDHSig(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        eCDHSig_ = value;
        bitField1_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHSig = 58;</code>
       * @return This builder for chaining.
       */
      public Builder clearECDHSig() {
        bitField1_ = (bitField1_ & ~0x02000000);
        eCDHSig_ = getDefaultInstance().getECDHSig();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEPLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       * @return Whether the dHEPLen field is set.
       */
      @java.lang.Override
      public boolean hasDHEPLen() {
        return ((bitField1_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       * @return The dHEPLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHEPLen() {
        return dHEPLen_;
      }
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       * @param value The dHEPLen to set.
       * @return This builder for chaining.
       */
      public Builder setDHEPLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHEPLen_ = value;
        bitField1_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEPLen = 59;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHEPLen() {
        bitField1_ = (bitField1_ & ~0x04000000);
        dHEPLen_ = getDefaultInstance().getDHEPLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEGLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       * @return Whether the dHEGLen field is set.
       */
      @java.lang.Override
      public boolean hasDHEGLen() {
        return ((bitField1_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       * @return The dHEGLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHEGLen() {
        return dHEGLen_;
      }
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       * @param value The dHEGLen to set.
       * @return This builder for chaining.
       */
      public Builder setDHEGLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHEGLen_ = value;
        bitField1_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEGLen = 60;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHEGLen() {
        bitField1_ = (bitField1_ & ~0x08000000);
        dHEGLen_ = getDefaultInstance().getDHEGLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliKeyExcLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       * @return Whether the cliKeyExcLen field is set.
       */
      @java.lang.Override
      public boolean hasCliKeyExcLen() {
        return ((bitField1_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       * @return The cliKeyExcLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliKeyExcLen() {
        return cliKeyExcLen_;
      }
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       * @param value The cliKeyExcLen to set.
       * @return This builder for chaining.
       */
      public Builder setCliKeyExcLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliKeyExcLen_ = value;
        bitField1_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliKeyExcLen = 61;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliKeyExcLen() {
        bitField1_ = (bitField1_ & ~0x10000000);
        cliKeyExcLen_ = getDefaultInstance().getCliKeyExcLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString encPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes encPubKey = 62;</code>
       * @return Whether the encPubKey field is set.
       */
      @java.lang.Override
      public boolean hasEncPubKey() {
        return ((bitField1_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes encPubKey = 62;</code>
       * @return The encPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEncPubKey() {
        return encPubKey_;
      }
      /**
       * <code>optional bytes encPubKey = 62;</code>
       * @param value The encPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setEncPubKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        encPubKey_ = value;
        bitField1_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes encPubKey = 62;</code>
       * @return This builder for chaining.
       */
      public Builder clearEncPubKey() {
        bitField1_ = (bitField1_ & ~0x20000000);
        encPubKey_ = getDefaultInstance().getEncPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString encPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       * @return Whether the encPubKeyLen field is set.
       */
      @java.lang.Override
      public boolean hasEncPubKeyLen() {
        return ((bitField1_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       * @return The encPubKeyLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getEncPubKeyLen() {
        return encPubKeyLen_;
      }
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       * @param value The encPubKeyLen to set.
       * @return This builder for chaining.
       */
      public Builder setEncPubKeyLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        encPubKeyLen_ = value;
        bitField1_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes encPubKeyLen = 63;</code>
       * @return This builder for chaining.
       */
      public Builder clearEncPubKeyLen() {
        bitField1_ = (bitField1_ & ~0x40000000);
        encPubKeyLen_ = getDefaultInstance().getEncPubKeyLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliExtLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       * @return Whether the cliExtLen field is set.
       */
      @java.lang.Override
      public boolean hasCliExtLen() {
        return ((bitField1_ & 0x80000000) != 0);
      }
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       * @return The cliExtLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliExtLen() {
        return cliExtLen_;
      }
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       * @param value The cliExtLen to set.
       * @return This builder for chaining.
       */
      public Builder setCliExtLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliExtLen_ = value;
        bitField1_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliExtLen = 64;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliExtLen() {
        bitField1_ = (bitField1_ & ~0x80000000);
        cliExtLen_ = getDefaultInstance().getCliExtLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvExtLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       * @return Whether the srvExtLen field is set.
       */
      @java.lang.Override
      public boolean hasSrvExtLen() {
        return ((bitField2_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       * @return The srvExtLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvExtLen() {
        return srvExtLen_;
      }
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       * @param value The srvExtLen to set.
       * @return This builder for chaining.
       */
      public Builder setSrvExtLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvExtLen_ = value;
        bitField2_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvExtLen = 65;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvExtLen() {
        bitField2_ = (bitField2_ & ~0x00000001);
        srvExtLen_ = getDefaultInstance().getSrvExtLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       * @return Whether the eCDHPubKeyLen field is set.
       */
      @java.lang.Override
      public boolean hasECDHPubKeyLen() {
        return ((bitField2_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       * @return The eCDHPubKeyLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getECDHPubKeyLen() {
        return eCDHPubKeyLen_;
      }
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       * @param value The eCDHPubKeyLen to set.
       * @return This builder for chaining.
       */
      public Builder setECDHPubKeyLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        eCDHPubKeyLen_ = value;
        bitField2_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHPubKeyLen = 66;</code>
       * @return This builder for chaining.
       */
      public Builder clearECDHPubKeyLen() {
        bitField2_ = (bitField2_ & ~0x00000002);
        eCDHPubKeyLen_ = getDefaultInstance().getECDHPubKeyLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString namType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes namType = 67;</code>
       * @return Whether the namType field is set.
       */
      @java.lang.Override
      public boolean hasNamType() {
        return ((bitField2_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes namType = 67;</code>
       * @return The namType.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getNamType() {
        return namType_;
      }
      /**
       * <code>optional bytes namType = 67;</code>
       * @param value The namType to set.
       * @return This builder for chaining.
       */
      public Builder setNamType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        namType_ = value;
        bitField2_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes namType = 67;</code>
       * @return This builder for chaining.
       */
      public Builder clearNamType() {
        bitField2_ = (bitField2_ & ~0x00000004);
        namType_ = getDefaultInstance().getNamType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString namLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes namLen = 68;</code>
       * @return Whether the namLen field is set.
       */
      @java.lang.Override
      public boolean hasNamLen() {
        return ((bitField2_ & 0x00000008) != 0);
      }
      /**
       * <code>optional bytes namLen = 68;</code>
       * @return The namLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getNamLen() {
        return namLen_;
      }
      /**
       * <code>optional bytes namLen = 68;</code>
       * @param value The namLen to set.
       * @return This builder for chaining.
       */
      public Builder setNamLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        namLen_ = value;
        bitField2_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes namLen = 68;</code>
       * @return This builder for chaining.
       */
      public Builder clearNamLen() {
        bitField2_ = (bitField2_ & ~0x00000008);
        namLen_ = getDefaultInstance().getNamLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString ticDat_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ticDat = 69;</code>
       * @return Whether the ticDat field is set.
       */
      @java.lang.Override
      public boolean hasTicDat() {
        return ((bitField2_ & 0x00000010) != 0);
      }
      /**
       * <code>optional bytes ticDat = 69;</code>
       * @return The ticDat.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getTicDat() {
        return ticDat_;
      }
      /**
       * <code>optional bytes ticDat = 69;</code>
       * @param value The ticDat to set.
       * @return This builder for chaining.
       */
      public Builder setTicDat(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ticDat_ = value;
        bitField2_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ticDat = 69;</code>
       * @return This builder for chaining.
       */
      public Builder clearTicDat() {
        bitField2_ = (bitField2_ & ~0x00000010);
        ticDat_ = getDefaultInstance().getTicDat();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvCipSui_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       * @return Whether the srvCipSui field is set.
       */
      @java.lang.Override
      public boolean hasSrvCipSui() {
        return ((bitField2_ & 0x00000020) != 0);
      }
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       * @return The srvCipSui.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvCipSui() {
        return srvCipSui_;
      }
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       * @param value The srvCipSui to set.
       * @return This builder for chaining.
       */
      public Builder setSrvCipSui(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvCipSui_ = value;
        bitField2_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvCipSui = 70;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvCipSui() {
        bitField2_ = (bitField2_ & ~0x00000020);
        srvCipSui_ = getDefaultInstance().getSrvCipSui();
        onChanged();
        return this;
      }

      private int cipSuiNum_ ;
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       * @return Whether the cipSuiNum field is set.
       */
      @java.lang.Override
      public boolean hasCipSuiNum() {
        return ((bitField2_ & 0x00000040) != 0);
      }
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       * @return The cipSuiNum.
       */
      @java.lang.Override
      public int getCipSuiNum() {
        return cipSuiNum_;
      }
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       * @param value The cipSuiNum to set.
       * @return This builder for chaining.
       */
      public Builder setCipSuiNum(int value) {

        cipSuiNum_ = value;
        bitField2_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cipSuiNum = 71;</code>
       * @return This builder for chaining.
       */
      public Builder clearCipSuiNum() {
        bitField2_ = (bitField2_ & ~0x00000040);
        cipSuiNum_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString eCDHSigHash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       * @return Whether the eCDHSigHash field is set.
       */
      @java.lang.Override
      public boolean hasECDHSigHash() {
        return ((bitField2_ & 0x00000080) != 0);
      }
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       * @return The eCDHSigHash.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getECDHSigHash() {
        return eCDHSigHash_;
      }
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       * @param value The eCDHSigHash to set.
       * @return This builder for chaining.
       */
      public Builder setECDHSigHash(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        eCDHSigHash_ = value;
        bitField2_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes ECDHSigHash = 72;</code>
       * @return This builder for chaining.
       */
      public Builder clearECDHSigHash() {
        bitField2_ = (bitField2_ & ~0x00000080);
        eCDHSigHash_ = getDefaultInstance().getECDHSigHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHESigHash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       * @return Whether the dHESigHash field is set.
       */
      @java.lang.Override
      public boolean hasDHESigHash() {
        return ((bitField2_ & 0x00000100) != 0);
      }
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       * @return The dHESigHash.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHESigHash() {
        return dHESigHash_;
      }
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       * @param value The dHESigHash to set.
       * @return This builder for chaining.
       */
      public Builder setDHESigHash(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHESigHash_ = value;
        bitField2_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHESigHash = 73;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHESigHash() {
        bitField2_ = (bitField2_ & ~0x00000100);
        dHESigHash_ = getDefaultInstance().getDHESigHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSASigHash_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       * @return Whether the rSASigHash field is set.
       */
      @java.lang.Override
      public boolean hasRSASigHash() {
        return ((bitField2_ & 0x00000200) != 0);
      }
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       * @return The rSASigHash.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRSASigHash() {
        return rSASigHash_;
      }
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       * @param value The rSASigHash to set.
       * @return This builder for chaining.
       */
      public Builder setRSASigHash(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rSASigHash_ = value;
        bitField2_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSASigHash = 74;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSASigHash() {
        bitField2_ = (bitField2_ & ~0x00000200);
        rSASigHash_ = getDefaultInstance().getRSASigHash();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString greaseFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       * @return Whether the greaseFlag field is set.
       */
      @java.lang.Override
      public boolean hasGreaseFlag() {
        return ((bitField2_ & 0x00000400) != 0);
      }
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       * @return The greaseFlag.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getGreaseFlag() {
        return greaseFlag_;
      }
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       * @param value The greaseFlag to set.
       * @return This builder for chaining.
       */
      public Builder setGreaseFlag(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        greaseFlag_ = value;
        bitField2_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes greaseFlag = 75;</code>
       * @return This builder for chaining.
       */
      public Builder clearGreaseFlag() {
        bitField2_ = (bitField2_ & ~0x00000400);
        greaseFlag_ = getDefaultInstance().getGreaseFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAModLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       * @return Whether the rSAModLen field is set.
       */
      @java.lang.Override
      public boolean hasRSAModLen() {
        return ((bitField2_ & 0x00000800) != 0);
      }
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       * @return The rSAModLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRSAModLen() {
        return rSAModLen_;
      }
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       * @param value The rSAModLen to set.
       * @return This builder for chaining.
       */
      public Builder setRSAModLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rSAModLen_ = value;
        bitField2_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAModLen = 76;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSAModLen() {
        bitField2_ = (bitField2_ & ~0x00000800);
        rSAModLen_ = getDefaultInstance().getRSAModLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSAExpLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       * @return Whether the rSAExpLen field is set.
       */
      @java.lang.Override
      public boolean hasRSAExpLen() {
        return ((bitField2_ & 0x00001000) != 0);
      }
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       * @return The rSAExpLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRSAExpLen() {
        return rSAExpLen_;
      }
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       * @param value The rSAExpLen to set.
       * @return This builder for chaining.
       */
      public Builder setRSAExpLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rSAExpLen_ = value;
        bitField2_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSAExpLen = 77;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSAExpLen() {
        bitField2_ = (bitField2_ & ~0x00001000);
        rSAExpLen_ = getDefaultInstance().getRSAExpLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString rSASig_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes RSASig = 78;</code>
       * @return Whether the rSASig field is set.
       */
      @java.lang.Override
      public boolean hasRSASig() {
        return ((bitField2_ & 0x00002000) != 0);
      }
      /**
       * <code>optional bytes RSASig = 78;</code>
       * @return The rSASig.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getRSASig() {
        return rSASig_;
      }
      /**
       * <code>optional bytes RSASig = 78;</code>
       * @param value The rSASig to set.
       * @return This builder for chaining.
       */
      public Builder setRSASig(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rSASig_ = value;
        bitField2_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes RSASig = 78;</code>
       * @return This builder for chaining.
       */
      public Builder clearRSASig() {
        bitField2_ = (bitField2_ & ~0x00002000);
        rSASig_ = getDefaultInstance().getRSASig();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHESig_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHESig = 79;</code>
       * @return Whether the dHESig field is set.
       */
      @java.lang.Override
      public boolean hasDHESig() {
        return ((bitField2_ & 0x00004000) != 0);
      }
      /**
       * <code>optional bytes DHESig = 79;</code>
       * @return The dHESig.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHESig() {
        return dHESig_;
      }
      /**
       * <code>optional bytes DHESig = 79;</code>
       * @param value The dHESig to set.
       * @return This builder for chaining.
       */
      public Builder setDHESig(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHESig_ = value;
        bitField2_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHESig = 79;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHESig() {
        bitField2_ = (bitField2_ & ~0x00004000);
        dHESig_ = getDefaultInstance().getDHESig();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEPubKeyLen_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       * @return Whether the dHEPubKeyLen field is set.
       */
      @java.lang.Override
      public boolean hasDHEPubKeyLen() {
        return ((bitField2_ & 0x00008000) != 0);
      }
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       * @return The dHEPubKeyLen.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHEPubKeyLen() {
        return dHEPubKeyLen_;
      }
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       * @param value The dHEPubKeyLen to set.
       * @return This builder for chaining.
       */
      public Builder setDHEPubKeyLen(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHEPubKeyLen_ = value;
        bitField2_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEPubKeyLen = 80;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHEPubKeyLen() {
        bitField2_ = (bitField2_ & ~0x00008000);
        dHEPubKeyLen_ = getDefaultInstance().getDHEPubKeyLen();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString dHEPubKey_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       * @return Whether the dHEPubKey field is set.
       */
      @java.lang.Override
      public boolean hasDHEPubKey() {
        return ((bitField2_ & 0x00010000) != 0);
      }
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       * @return The dHEPubKey.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getDHEPubKey() {
        return dHEPubKey_;
      }
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       * @param value The dHEPubKey to set.
       * @return This builder for chaining.
       */
      public Builder setDHEPubKey(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        dHEPubKey_ = value;
        bitField2_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes DHEPubKey = 81;</code>
       * @return This builder for chaining.
       */
      public Builder clearDHEPubKey() {
        bitField2_ = (bitField2_ & ~0x00010000);
        dHEPubKey_ = getDefaultInstance().getDHEPubKey();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigAlgType_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       * @return Whether the sigAlgType field is set.
       */
      @java.lang.Override
      public boolean hasSigAlgType() {
        return ((bitField2_ & 0x00020000) != 0);
      }
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       * @return The sigAlgType.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSigAlgType() {
        return sigAlgType_;
      }
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       * @param value The sigAlgType to set.
       * @return This builder for chaining.
       */
      public Builder setSigAlgType(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sigAlgType_ = value;
        bitField2_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SigAlgType = 82;</code>
       * @return This builder for chaining.
       */
      public Builder clearSigAlgType() {
        bitField2_ = (bitField2_ & ~0x00020000);
        sigAlgType_ = getDefaultInstance().getSigAlgType();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sigAlg = 83;</code>
       * @return Whether the sigAlg field is set.
       */
      @java.lang.Override
      public boolean hasSigAlg() {
        return ((bitField2_ & 0x00040000) != 0);
      }
      /**
       * <code>optional bytes sigAlg = 83;</code>
       * @return The sigAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSigAlg() {
        return sigAlg_;
      }
      /**
       * <code>optional bytes sigAlg = 83;</code>
       * @param value The sigAlg to set.
       * @return This builder for chaining.
       */
      public Builder setSigAlg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sigAlg_ = value;
        bitField2_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sigAlg = 83;</code>
       * @return This builder for chaining.
       */
      public Builder clearSigAlg() {
        bitField2_ = (bitField2_ & ~0x00040000);
        sigAlg_ = getDefaultInstance().getSigAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sigHashAlg_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       * @return Whether the sigHashAlg field is set.
       */
      @java.lang.Override
      public boolean hasSigHashAlg() {
        return ((bitField2_ & 0x00080000) != 0);
      }
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       * @return The sigHashAlg.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSigHashAlg() {
        return sigHashAlg_;
      }
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       * @param value The sigHashAlg to set.
       * @return This builder for chaining.
       */
      public Builder setSigHashAlg(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sigHashAlg_ = value;
        bitField2_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes SigHashAlg = 84;</code>
       * @return This builder for chaining.
       */
      public Builder clearSigHashAlg() {
        bitField2_ = (bitField2_ & ~0x00080000);
        sigHashAlg_ = getDefaultInstance().getSigHashAlg();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString jOY_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes JOY = 85;</code>
       * @return Whether the jOY field is set.
       */
      @java.lang.Override
      public boolean hasJOY() {
        return ((bitField2_ & 0x00100000) != 0);
      }
      /**
       * <code>optional bytes JOY = 85;</code>
       * @return The jOY.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getJOY() {
        return jOY_;
      }
      /**
       * <code>optional bytes JOY = 85;</code>
       * @param value The jOY to set.
       * @return This builder for chaining.
       */
      public Builder setJOY(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        jOY_ = value;
        bitField2_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes JOY = 85;</code>
       * @return This builder for chaining.
       */
      public Builder clearJOY() {
        bitField2_ = (bitField2_ & ~0x00100000);
        jOY_ = getDefaultInstance().getJOY();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString jOYS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes JOYS = 86;</code>
       * @return Whether the jOYS field is set.
       */
      @java.lang.Override
      public boolean hasJOYS() {
        return ((bitField2_ & 0x00200000) != 0);
      }
      /**
       * <code>optional bytes JOYS = 86;</code>
       * @return The jOYS.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getJOYS() {
        return jOYS_;
      }
      /**
       * <code>optional bytes JOYS = 86;</code>
       * @param value The jOYS to set.
       * @return This builder for chaining.
       */
      public Builder setJOYS(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        jOYS_ = value;
        bitField2_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes JOYS = 86;</code>
       * @return This builder for chaining.
       */
      public Builder clearJOYS() {
        bitField2_ = (bitField2_ & ~0x00200000);
        jOYS_ = getDefaultInstance().getJOYS();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sTARTTLS_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       * @return Whether the sTARTTLS field is set.
       */
      @java.lang.Override
      public boolean hasSTARTTLS() {
        return ((bitField2_ & 0x00400000) != 0);
      }
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       * @return The sTARTTLS.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSTARTTLS() {
        return sTARTTLS_;
      }
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       * @param value The sTARTTLS to set.
       * @return This builder for chaining.
       */
      public Builder setSTARTTLS(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sTARTTLS_ = value;
        bitField2_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes STARTTLS = 87;</code>
       * @return This builder for chaining.
       */
      public Builder clearSTARTTLS() {
        bitField2_ = (bitField2_ & ~0x00400000);
        sTARTTLS_ = getDefaultInstance().getSTARTTLS();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certNonFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       * @return Whether the certNonFlag field is set.
       */
      @java.lang.Override
      public boolean hasCertNonFlag() {
        return ((bitField2_ & 0x00800000) != 0);
      }
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       * @return The certNonFlag.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCertNonFlag() {
        return certNonFlag_;
      }
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       * @param value The certNonFlag to set.
       * @return This builder for chaining.
       */
      public Builder setCertNonFlag(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certNonFlag_ = value;
        bitField2_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certNonFlag = 88;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertNonFlag() {
        bitField2_ = (bitField2_ & ~0x00800000);
        certNonFlag_ = getDefaultInstance().getCertNonFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString joyFp_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes JoyFp = 89;</code>
       * @return Whether the joyFp field is set.
       */
      @java.lang.Override
      public boolean hasJoyFp() {
        return ((bitField2_ & 0x01000000) != 0);
      }
      /**
       * <code>optional bytes JoyFp = 89;</code>
       * @return The joyFp.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getJoyFp() {
        return joyFp_;
      }
      /**
       * <code>optional bytes JoyFp = 89;</code>
       * @param value The joyFp to set.
       * @return This builder for chaining.
       */
      public Builder setJoyFp(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        joyFp_ = value;
        bitField2_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes JoyFp = 89;</code>
       * @return This builder for chaining.
       */
      public Builder clearJoyFp() {
        bitField2_ = (bitField2_ & ~0x01000000);
        joyFp_ = getDefaultInstance().getJoyFp();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certIntactFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       * @return Whether the certIntactFlag field is set.
       */
      @java.lang.Override
      public boolean hasCertIntactFlag() {
        return ((bitField2_ & 0x02000000) != 0);
      }
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       * @return The certIntactFlag.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCertIntactFlag() {
        return certIntactFlag_;
      }
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       * @param value The certIntactFlag to set.
       * @return This builder for chaining.
       */
      public Builder setCertIntactFlag(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certIntactFlag_ = value;
        bitField2_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certIntactFlag = 90;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertIntactFlag() {
        bitField2_ = (bitField2_ & ~0x02000000);
        certIntactFlag_ = getDefaultInstance().getCertIntactFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString certPath_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes certPath = 91;</code>
       * @return Whether the certPath field is set.
       */
      @java.lang.Override
      public boolean hasCertPath() {
        return ((bitField2_ & 0x04000000) != 0);
      }
      /**
       * <code>optional bytes certPath = 91;</code>
       * @return The certPath.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCertPath() {
        return certPath_;
      }
      /**
       * <code>optional bytes certPath = 91;</code>
       * @param value The certPath to set.
       * @return This builder for chaining.
       */
      public Builder setCertPath(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        certPath_ = value;
        bitField2_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes certPath = 91;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertPath() {
        bitField2_ = (bitField2_ & ~0x04000000);
        certPath_ = getDefaultInstance().getCertPath();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString sessSecFlag_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       * @return Whether the sessSecFlag field is set.
       */
      @java.lang.Override
      public boolean hasSessSecFlag() {
        return ((bitField2_ & 0x08000000) != 0);
      }
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       * @return The sessSecFlag.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSessSecFlag() {
        return sessSecFlag_;
      }
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       * @param value The sessSecFlag to set.
       * @return This builder for chaining.
       */
      public Builder setSessSecFlag(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sessSecFlag_ = value;
        bitField2_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes sessSecFlag = 92;</code>
       * @return This builder for chaining.
       */
      public Builder clearSessSecFlag() {
        bitField2_ = (bitField2_ & ~0x08000000);
        sessSecFlag_ = getDefaultInstance().getSessSecFlag();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString fullText_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes fullText = 93;</code>
       * @return Whether the fullText field is set.
       */
      @java.lang.Override
      public boolean hasFullText() {
        return ((bitField2_ & 0x10000000) != 0);
      }
      /**
       * <code>optional bytes fullText = 93;</code>
       * @return The fullText.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getFullText() {
        return fullText_;
      }
      /**
       * <code>optional bytes fullText = 93;</code>
       * @param value The fullText to set.
       * @return This builder for chaining.
       */
      public Builder setFullText(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        fullText_ = value;
        bitField2_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes fullText = 93;</code>
       * @return This builder for chaining.
       */
      public Builder clearFullText() {
        bitField2_ = (bitField2_ & ~0x10000000);
        fullText_ = getDefaultInstance().getFullText();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString cliCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       * @return Whether the cliCertHashes field is set.
       */
      @java.lang.Override
      public boolean hasCliCertHashes() {
        return ((bitField2_ & 0x20000000) != 0);
      }
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       * @return The cliCertHashes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getCliCertHashes() {
        return cliCertHashes_;
      }
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       * @param value The cliCertHashes to set.
       * @return This builder for chaining.
       */
      public Builder setCliCertHashes(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        cliCertHashes_ = value;
        bitField2_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes cliCertHashes = 94;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliCertHashes() {
        bitField2_ = (bitField2_ & ~0x20000000);
        cliCertHashes_ = getDefaultInstance().getCliCertHashes();
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString srvCertHashes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       * @return Whether the srvCertHashes field is set.
       */
      @java.lang.Override
      public boolean hasSrvCertHashes() {
        return ((bitField2_ & 0x40000000) != 0);
      }
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       * @return The srvCertHashes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getSrvCertHashes() {
        return srvCertHashes_;
      }
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       * @param value The srvCertHashes to set.
       * @return This builder for chaining.
       */
      public Builder setSrvCertHashes(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        srvCertHashes_ = value;
        bitField2_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes srvCertHashes = 95;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvCertHashes() {
        bitField2_ = (bitField2_ & ~0x40000000);
        srvCertHashes_ = getDefaultInstance().getSrvCertHashes();
        onChanged();
        return this;
      }

      private int cliCertNum_ ;
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       * @return Whether the cliCertNum field is set.
       */
      @java.lang.Override
      public boolean hasCliCertNum() {
        return ((bitField2_ & 0x80000000) != 0);
      }
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       * @return The cliCertNum.
       */
      @java.lang.Override
      public int getCliCertNum() {
        return cliCertNum_;
      }
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       * @param value The cliCertNum to set.
       * @return This builder for chaining.
       */
      public Builder setCliCertNum(int value) {

        cliCertNum_ = value;
        bitField2_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 cliCertNum = 96;</code>
       * @return This builder for chaining.
       */
      public Builder clearCliCertNum() {
        bitField2_ = (bitField2_ & ~0x80000000);
        cliCertNum_ = 0;
        onChanged();
        return this;
      }

      private int srvCertNum_ ;
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       * @return Whether the srvCertNum field is set.
       */
      @java.lang.Override
      public boolean hasSrvCertNum() {
        return ((bitField3_ & 0x00000001) != 0);
      }
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       * @return The srvCertNum.
       */
      @java.lang.Override
      public int getSrvCertNum() {
        return srvCertNum_;
      }
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       * @param value The srvCertNum to set.
       * @return This builder for chaining.
       */
      public Builder setSrvCertNum(int value) {

        srvCertNum_ = value;
        bitField3_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 srvCertNum = 97;</code>
       * @return This builder for chaining.
       */
      public Builder clearSrvCertNum() {
        bitField3_ = (bitField3_ & ~0x00000001);
        srvCertNum_ = 0;
        onChanged();
        return this;
      }

      private int certExist_ ;
      /**
       * <code>optional uint32 certExist = 98;</code>
       * @return Whether the certExist field is set.
       */
      @java.lang.Override
      public boolean hasCertExist() {
        return ((bitField3_ & 0x00000002) != 0);
      }
      /**
       * <code>optional uint32 certExist = 98;</code>
       * @return The certExist.
       */
      @java.lang.Override
      public int getCertExist() {
        return certExist_;
      }
      /**
       * <code>optional uint32 certExist = 98;</code>
       * @param value The certExist to set.
       * @return This builder for chaining.
       */
      public Builder setCertExist(int value) {

        certExist_ = value;
        bitField3_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 certExist = 98;</code>
       * @return This builder for chaining.
       */
      public Builder clearCertExist() {
        bitField3_ = (bitField3_ & ~0x00000002);
        certExist_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString extendEcGroupsClient_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       * @return Whether the extendEcGroupsClient field is set.
       */
      @java.lang.Override
      public boolean hasExtendEcGroupsClient() {
        return ((bitField3_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       * @return The extendEcGroupsClient.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getExtendEcGroupsClient() {
        return extendEcGroupsClient_;
      }
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       * @param value The extendEcGroupsClient to set.
       * @return This builder for chaining.
       */
      public Builder setExtendEcGroupsClient(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        extendEcGroupsClient_ = value;
        bitField3_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes extendEcGroupsClient = 99;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtendEcGroupsClient() {
        bitField3_ = (bitField3_ & ~0x00000004);
        extendEcGroupsClient_ = getDefaultInstance().getExtendEcGroupsClient();
        onChanged();
        return this;
      }

      private int leafCertDaysRemaining_ ;
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       * @return Whether the leafCertDaysRemaining field is set.
       */
      @java.lang.Override
      public boolean hasLeafCertDaysRemaining() {
        return ((bitField3_ & 0x00000008) != 0);
      }
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       * @return The leafCertDaysRemaining.
       */
      @java.lang.Override
      public int getLeafCertDaysRemaining() {
        return leafCertDaysRemaining_;
      }
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       * @param value The leafCertDaysRemaining to set.
       * @return This builder for chaining.
       */
      public Builder setLeafCertDaysRemaining(int value) {

        leafCertDaysRemaining_ = value;
        bitField3_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>optional uint32 leafCertDaysRemaining = 100;</code>
       * @return This builder for chaining.
       */
      public Builder clearLeafCertDaysRemaining() {
        bitField3_ = (bitField3_ & ~0x00000008);
        leafCertDaysRemaining_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Ssl_TlsInfo)
    }

    // @@protoc_insertion_point(class_scope:Ssl_TlsInfo)
    private static final SslTlsInfo.Ssl_TlsInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new SslTlsInfo.Ssl_TlsInfo();
    }

    public static SslTlsInfo.Ssl_TlsInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Ssl_TlsInfo>
        PARSER = new com.google.protobuf.AbstractParser<Ssl_TlsInfo>() {
      @java.lang.Override
      public Ssl_TlsInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<Ssl_TlsInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Ssl_TlsInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public SslTlsInfo.Ssl_TlsInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Ssl_TlsInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Ssl_TlsInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021Ssl_TlsInfo.proto\"\347\017\n\013Ssl_TlsInfo\022\017\n\007c" +
      "onType\030\001 \001(\r\022\016\n\006aleLev\030\002 \001(\r\022\016\n\006aleDes\030\003" +
      " \001(\r\022\023\n\013handShaType\030\004 \001(\r\022\016\n\006cliVer\030\005 \001(" +
      "\r\022\025\n\rcliGMTUniTime\030\006 \001(\004\022\017\n\007cliRand\030\007 \001(" +
      "\014\022\020\n\010cliSesID\030\010 \001(\014\022\021\n\tcliCipSui\030\t \001(\014\022\021" +
      "\n\tcliComMet\030\n \001(\014\022\016\n\006srvVer\030\013 \001(\r\022\017\n\007srv" +
      "Name\030\014 \001(\014\022\023\n\013srvNameAttr\030\r \001(\r\022\025\n\rsrvGM" +
      "TUniTime\030\016 \001(\004\022\017\n\007srvRand\030\017 \001(\014\022\020\n\010srvSe" +
      "sID\030\020 \001(\014\022\023\n\013srvComprMet\030\021 \001(\014\022\022\n\nsrvCer" +
      "tLen\030\022 \001(\r\022\023\n\013certResType\030\023 \001(\r\022\022\n\ncliCe" +
      "rtLen\030\024 \001(\r\022\031\n\021RSAModOfSrvKeyExc\030\025 \001(\014\022\031" +
      "\n\021RSAExpOfSrvKeyExc\030\026 \001(\004\022\030\n\020DHModOfSrvK" +
      "eyExc\030\027 \001(\014\022\030\n\020DHGenOfSrvKeyExc\030\030 \001(\014\022\023\n" +
      "\013srvDHPubKey\030\031 \001(\014\022\033\n\023preMasKeyEncryByRS" +
      "A\030\032 \001(\014\022\023\n\013cliDHPubKey\030\033 \001(\014\022\024\n\014extTypeI" +
      "nSSL\030\034 \001(\r\022\027\n\017cliEllCurPoiFor\030\035 \001(\r\022\021\n\tc" +
      "liEllCur\030\036 \001(\r\022\027\n\017srvEllCurPoiFor\030\037 \001(\r\022" +
      "\021\n\tsrvEllCur\030  \001(\r\022\031\n\021srvEllCurDHPubKey\030" +
      "! \001(\014\022\031\n\021cliEllCurDHPubKey\030\" \001(\014\022\026\n\016srvG" +
      "MTUni_Time\030# \001(\004\022\021\n\tcliExtCnt\030$ \001(\r\022\021\n\ts" +
      "rvExtCnt\030% \001(\r\022\024\n\014cliHandSkLen\030& \001(\r\022\024\n\014" +
      "srvHandSkLen\030\' \001(\r\022\016\n\006cliExt\030( \001(\014\022\016\n\006sr" +
      "vExt\030) \001(\014\022\024\n\014cliExtGrease\030* \001(\r\022\016\n\006cliJ" +
      "A3\030+ \001(\014\022\016\n\006srvJA3\030, \001(\014\022\025\n\rcliSessTicke" +
      "t\030- \001(\014\022\025\n\rsrvSessTicket\030. \001(\014\022\017\n\007AuthTa" +
      "g\030/ \001(\r\022\022\n\ncliCertCnt\0300 \001(\r\022\022\n\nsrvCertCn" +
      "t\0301 \001(\r\022\023\n\013ecGroupsCli\0302 \001(\014\022\026\n\016ecPoiFor" +
      "ByServ\0303 \001(\014\022\r\n\005etags\0304 \001(\014\022\r\n\005ttags\0305 \001" +
      "(\014\022\023\n\013cliSesIDLen\0306 \001(\014\022\023\n\013srvSesIDLen\0307" +
      " \001(\014\022\024\n\014srvKeyExcLen\0308 \001(\014\022\023\n\013ECDHCurTyp" +
      "e\0309 \001(\014\022\017\n\007ECDHSig\030: \001(\014\022\017\n\007DHEPLen\030; \001(" +
      "\014\022\017\n\007DHEGLen\030< \001(\014\022\024\n\014cliKeyExcLen\030= \001(\014" +
      "\022\021\n\tencPubKey\030> \001(\014\022\024\n\014encPubKeyLen\030? \001(" +
      "\014\022\021\n\tcliExtLen\030@ \001(\014\022\021\n\tsrvExtLen\030A \001(\014\022" +
      "\025\n\rECDHPubKeyLen\030B \001(\014\022\017\n\007namType\030C \001(\014\022" +
      "\016\n\006namLen\030D \001(\014\022\016\n\006ticDat\030E \001(\014\022\021\n\tsrvCi" +
      "pSui\030F \001(\014\022\021\n\tcipSuiNum\030G \001(\r\022\023\n\013ECDHSig" +
      "Hash\030H \001(\014\022\022\n\nDHESigHash\030I \001(\014\022\022\n\nRSASig" +
      "Hash\030J \001(\014\022\022\n\ngreaseFlag\030K \001(\014\022\021\n\tRSAMod" +
      "Len\030L \001(\014\022\021\n\tRSAExpLen\030M \001(\014\022\016\n\006RSASig\030N" +
      " \001(\014\022\016\n\006DHESig\030O \001(\014\022\024\n\014DHEPubKeyLen\030P \001" +
      "(\014\022\021\n\tDHEPubKey\030Q \001(\014\022\022\n\nSigAlgType\030R \001(" +
      "\014\022\016\n\006sigAlg\030S \001(\014\022\022\n\nSigHashAlg\030T \001(\014\022\013\n" +
      "\003JOY\030U \001(\014\022\014\n\004JOYS\030V \001(\014\022\020\n\010STARTTLS\030W \001" +
      "(\014\022\023\n\013certNonFlag\030X \001(\014\022\r\n\005JoyFp\030Y \001(\014\022\026" +
      "\n\016certIntactFlag\030Z \001(\014\022\020\n\010certPath\030[ \001(\014" +
      "\022\023\n\013sessSecFlag\030\\ \001(\014\022\020\n\010fullText\030] \001(\014\022" +
      "\025\n\rcliCertHashes\030^ \001(\014\022\025\n\rsrvCertHashes\030" +
      "_ \001(\014\022\022\n\ncliCertNum\030` \001(\r\022\022\n\nsrvCertNum\030" +
      "a \001(\r\022\021\n\tcertExist\030b \001(\r\022\034\n\024extendEcGrou" +
      "psClient\030c \001(\014\022\035\n\025leafCertDaysRemaining\030" +
      "d \001(\r"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Ssl_TlsInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Ssl_TlsInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Ssl_TlsInfo_descriptor,
        new java.lang.String[] { "ConType", "AleLev", "AleDes", "HandShaType", "CliVer", "CliGMTUniTime", "CliRand", "CliSesID", "CliCipSui", "CliComMet", "SrvVer", "SrvName", "SrvNameAttr", "SrvGMTUniTime14", "SrvRand", "SrvSesID", "SrvComprMet", "SrvCertLen", "CertResType", "CliCertLen", "RSAModOfSrvKeyExc", "RSAExpOfSrvKeyExc", "DHModOfSrvKeyExc", "DHGenOfSrvKeyExc", "SrvDHPubKey", "PreMasKeyEncryByRSA", "CliDHPubKey", "ExtTypeInSSL", "CliEllCurPoiFor", "CliEllCur", "SrvEllCurPoiFor", "SrvEllCur", "SrvEllCurDHPubKey", "CliEllCurDHPubKey", "SrvGMTUniTime35", "CliExtCnt", "SrvExtCnt", "CliHandSkLen", "SrvHandSkLen", "CliExt", "SrvExt", "CliExtGrease", "CliJA3", "SrvJA3", "CliSessTicket", "SrvSessTicket", "AuthTag", "CliCertCnt", "SrvCertCnt", "EcGroupsCli", "EcPoiForByServ", "Etags", "Ttags", "CliSesIDLen", "SrvSesIDLen", "SrvKeyExcLen", "ECDHCurType", "ECDHSig", "DHEPLen", "DHEGLen", "CliKeyExcLen", "EncPubKey", "EncPubKeyLen", "CliExtLen", "SrvExtLen", "ECDHPubKeyLen", "NamType", "NamLen", "TicDat", "SrvCipSui", "CipSuiNum", "ECDHSigHash", "DHESigHash", "RSASigHash", "GreaseFlag", "RSAModLen", "RSAExpLen", "RSASig", "DHESig", "DHEPubKeyLen", "DHEPubKey", "SigAlgType", "SigAlg", "SigHashAlg", "JOY", "JOYS", "STARTTLS", "CertNonFlag", "JoyFp", "CertIntactFlag", "CertPath", "SessSecFlag", "FullText", "CliCertHashes", "SrvCertHashes", "CliCertNum", "SrvCertNum", "CertExist", "ExtendEcGroupsClient", "LeafCertDaysRemaining", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
