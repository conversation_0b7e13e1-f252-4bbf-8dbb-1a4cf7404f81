package com.geeksec.transfer.function.process.ipProcess;

import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.common.utils.IpMatcher;
import com.geeksec.common.utils.RedisUtils;
import com.geeksec.proto.AlertLog;
import io.rebloom.client.Client;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;

/**
 * @author: jerryzhou
 * @date: 2024/10/16 15:52
 * @Description:
 **/
public class IpFilterFunction extends ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG> {

    private static final Logger logger = LoggerFactory.getLogger(IpFilterFunction.class);

    // 布隆过滤器链接实例
    private Client bloomClient;

    // JedisPool连接池实例
    public static JedisPool jedisPool;

    // IP地址过滤布隆过滤器KEY
    private static final String IP_ADDR_BLOOM_FILTER_KEY = "ip_addr_filter";

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        ParameterTool globalJobParameters = (ParameterTool)
                getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        String redisHost = globalJobParameters.get("redis.host.addr");
        Integer redisPort = globalJobParameters.getInt("redis.host.port");
        jedisPool = RedisUtils.getJedisPool(redisHost, redisPort);
        bloomClient = new Client(jedisPool);
    }

    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context context, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        // 获取当前alertLog里的IP信息，直接进行判断
        String sIp = alertLog.getSip().getIp();
        String dIp = alertLog.getDip().getIp();
        try {
            // 批量检查IP，减少循环
            boolean existsSip = bloomClient.exists(IP_ADDR_BLOOM_FILTER_KEY,
                    alertLog.getSip().getIp()
            );

            boolean existsDip = bloomClient.exists(IP_ADDR_BLOOM_FILTER_KEY,
                    alertLog.getDip().getIp()
            );

            if (existsSip || existsDip) {
                context.output(FilterOutPutTagConstant.IP_FILTER_TERMINATE,alertLog);
            }
            // CIDR检查
            if(!IpMatcher.isIPInCIDRList(sIp) && !IpMatcher.isIPInCIDRList(dIp)){
                context.output(FilterOutPutTagConstant.IP_FILTER_CONTINUE,alertLog);
            }else{
                context.output(FilterOutPutTagConstant.IP_FILTER_TERMINATE,alertLog);
            }
        } catch (Exception e) {
            // 处理异常
            System.out.println(e.getMessage());
            // TODO 放到服务器上会报错
//            logger.error("当前IP规则匹配异常, sip->{},  dip->{}, 报错：{}", sIp, dIp, e.getStackTrace());
            context.output(FilterOutPutTagConstant.IP_FILTER_CONTINUE,alertLog);
        }
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (bloomClient != null) {
            bloomClient.close();
        }
    }
}
