package com.geeksec.enums;

import org.apache.commons.lang.math.NumberUtils;

import java.util.*;

/**
 * 威胁类型（attack_type）
 */
public enum ThreatTypeEnum {


    // 恶意代码
    APT(0x010001, "APT攻击"),
    REMOTE_CONTROL_TROJAN(0x010002, "远控木马"),
    STEALING_TROJAN(0x010003, "窃密木马"),
    MINING_TROJAN(0x010004, "挖矿木马"),
    DOWNLOADER(0x010005, "下载器"),
    BOTNET(0x010006, "僵尸网络"),
    NETWORK_WORM(0x010007, "网络蠕虫"),
    COMPUTER_VIRUS(0x010008, "计算机病毒"),
    BLACK_MARKET_TOOL(0x010009, "黑市工具"),
    RANSOMWARE(0x010010, "勒索软件"),
    ROGUE_PROMOTION(0x01000A, "流氓推广"),
    ADWARE(0x01000B, "广告软件"),
    BACKDOOR_PROGRAM(0x01000C, "后门程序"),
    DDOS_SOFTWARE(0x01000D, "DDoS软件"),
    PHISHING_SOFTWARE(0x01000E, "钓鱼软件"),
    MACRO_VIRUS_DOCUMENT(0x01000F, "宏病毒文档"),
    MALICIOUS_OFFICE_DOCUMENT(0x010020, "恶意办公文档"),
    MALICIOUS_WEB_FILE(0x010021, "恶意网页文件"),
    MALICIOUS_SCRIPT_FILE(0x010022, "恶意脚本文件"),
    MALICIOUS_MOBILE_APP(0x010023, "恶意移动应用"),
    EXPLOIT(0x010024, "漏洞利用程序"),
    FILE_BOMB(0x010025, "文件炸弹"),
    KEYLOGGER(0x010026, "键盘记录器"),
    JOKE_SOFTWARE(0x010027, "游戏/玩笑软件"),
    ENCRYPTED_SOFTWARE(0x010028, "加密/加壳软件"),
    HACKING_TOOL(0x010029, "黑客工具"),
    SUSPICIOUS_PROGRAM(0x01002A, "可疑程序"),
    WEBSHELL(0x01002B, "webshell"),
    TUNNEL_TOOL(0x01002C, "隧道工具"),
    PROXY_TOOL(0x01002D, "代理工具"),
    LATERAL_MOVEMENT_TOOL(0x01002E, "横向工具"),

    // 入侵渗透
    SCANNING_DETECTION(0x020001, "扫描探测"),
    FINGERPRINT_IDENTIFICATION(0x020002, "指纹识别"),
    NETWORK_SNIFFING(0x020003, "网络嗅探"),
    INFORMATION_CRAWLING(0x020004, "信息爬取"),
    DNS_PROBE(0x020005, "DNS探测"),
    NETWORK_PHISHING(0x020006, "网络钓鱼"),
    AD_IMPLANTATION(0x020007, "广告植入"),
    FILE_READING(0x020008, "文件读取"),
    FILE_DOWNLOAD(0x020009, "文件下载"),
    FILE_WRITING(0x020010, "文件写入"),
    FILE_UPLOAD(0x02000A, "文件上传"),
    FILE_INCLUSION(0x02000B, "文件包含"),
    XXE_INJECTION(0x02000C, "XXE注入"),
    INFORMATION_LEAKAGE(0x02000D, "信息泄漏"),
    URL_JUMP(0x02000E, "URL跳转"),
    PATH_TRAVERSAL(0x02000F, "路径穿越"),
    CODE_EXECUTION(0x020020, "代码执行"),
    COMMAND_EXECUTION(0x020021, "命令执行"),
    IMPROPER_DEFAULT_CONFIGURATION(0x020022, "默认配置不当"),
    LOGIC_ERROR(0x020023, "逻辑错误"),
    BRUTE_FORCE(0x020024, "暴力猜解"),
    AUTHENTICATION_BYPASS(0x020025, "身份认证绕过"),
    SQL_INJECTION(0x020026, "SQL注入"),
    CSRF(0x020027, "跨站请求伪造（CSRF）"),
    SSRF(0x020028, "服务端请求伪造(SSRF)"),
    DESERIALIZATION_ATTACK(0x020029, "反序列化攻击"),
    BUFFER_OVERFLOW(0x02002A, "缓冲区溢出"),
    WEAK_PASSWORD_ACCESS(0x02002B, "弱口令访问"),
    XSS(0x02002C, "跨站脚本攻击（XSS）"),
    UNAUTHORIZED_ACCESS(0x02002D, "未授权访问/权限绕过"),
    DOS_ATTACK(0x02002E, "拒绝服务攻击"),
    SOCIAL_ENGINEERING(0x020031, "社交工程投放"),
    SPYWARE_IMPLANTATION(0x020032, "间谍软件植入"),
    BACKDOOR_ACCESS(0x020033, "后门访问"),
    CONTROLLED_RECONNECTION(0x020034, "受控回连"),
    BACKDOOR_ACCOUNT_OPERATION(0x020035, "后门账号操作"),
    DATA_STEALING(0x020036, "数据窃取"),
    DATA_DESTRUCTION(0x020037, "数据破坏"),

    // 邮件威胁
    MALICIOUS_CODE_DELIVERY(0x030001, "恶意代码投递"),
    PHISHING_EMAIL(0x030002, "钓鱼邮件"),
    SCRIPT_INJECTION(0x030003, "脚本注入"),
    FILE_NAME_SPOOFING(0x030004, "文件名伪装"),
    ATTACHMENT_PASSWORD_HIDDEN(0x030005, "附件密码隐藏"),
    POINT_TO_POINT_TARGETED_ATTACK(0x030006, "点对点定向攻击"),
    MASS_TARGETED_ATTACK(0x030007, "群发定向攻击"),

    // 工控物联网威胁
    HMI_VULNERABILITY_EXPLOITATION(0x040001, "上位机漏洞利用"),
    INDUSTRIAL_SWITCH_VULNERABILITY_EXPLOITATION(0x040002, "工业交换机漏洞利用"),
    PLC_VULNERABILITY_EXPLOITATION(0x040003, "PLC漏洞利用"),
    SCADA_VULNERABILITY_EXPLOITATION(0x040004, "SCADA漏洞利用"),
    DCS_VULNERABILITY_EXPLOITATION(0x040005, "DCS漏洞利用"),
    HMI_VULNERABILITY(0x040006, "HMI漏洞利用"),
    BUILDING_AUTOMATION_VULNERABILITY_EXPLOITATION(0x040007, "楼宇自动化漏洞利用"),
    OPERATION_INSTRUCTION_FORGING(0x040008, "操作指令伪造"),
    IOT_DEVICE_ATTACK(0x040009, "物联网设备攻击"),
    RTOS_ATTACK(0x040010, "实时操作系统攻击"),
    REAL_TIME_DATABASE_ATTACK(0x04000A, "实时数据库攻击");

    /**
     * 类型编号
     */
    private Integer code;

    /**
     * 名称
     */
    private String msg;

    ThreatTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private static Map<Integer, ThreatTypeEnum> codeEnumMap = new HashMap<>();

    static {
        for (ThreatTypeEnum e : ThreatTypeEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }


    public static String getMsgByCode(Integer code) {
        ThreatTypeEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }

    public static String getMsgByCode(Set<String> codeSet, String delimiter) {
        Set<String> set = new HashSet<>();
        for (String str : codeSet) {
            Integer code = NumberUtils.createInteger(str);
            set.add(getMsgByCode(code));
        }
        return String.join(delimiter, set);
    }

    public static ThreatTypeEnum get(Integer code) {
        return codeEnumMap.get(code);
    }

    /**
     * 枚举查询 根据 code 查询message信息
     *
     * @param code code值
     * @return message
     */
    public static String getEnumMessage(Integer code) {
        ThreatTypeEnum attackTypeEnum = ThreatTypeEnum.get(code);
        return Objects.isNull(attackTypeEnum) ? null : attackTypeEnum.getMsg();
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
