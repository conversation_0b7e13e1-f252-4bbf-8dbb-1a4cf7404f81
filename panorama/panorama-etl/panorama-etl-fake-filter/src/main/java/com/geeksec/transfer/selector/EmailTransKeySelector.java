package com.geeksec.transfer.selector;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple4;

import static com.geeksec.task.PanoramaFakeAlertFilterTask.EMAIL_KEY;


/**
 * <AUTHOR>
 */
public class EmailTransKeySelector implements KeySelector<AlertLog.ALERT_LOG, Tuple4<String, String, String, String>> {


    /**
     * 使用攻击方-受害方-Email通信双方作为Key
     * @param alertLog 告警日志
     * @return Tuple3<String, String, String> 攻击方IP-受害方IP-Email通信双方
     */
    @Override
    public Tuple4<String, String, String, String> getKey(AlertLog.ALERT_LOG alertLog) throws Exception {
        String vipAddr = alertLog.getVip().getIp();
        String aipAddr = alertLog.getAip().getIp();
        String emailSender = alertLog.getMailAlertInfo().getEmailSender();
        String emailReceiver = alertLog.getMailAlertInfo().getEmailReceiver();
        String emailConnect = String.format("%s-%s",emailSender,emailReceiver);
        return new Tuple4<>(EMAIL_KEY,aipAddr,vipAddr,emailConnect);
    }
}