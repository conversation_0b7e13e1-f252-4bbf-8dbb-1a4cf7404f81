package com.geeksec.entity.trans;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class IPTrans {
    /**
     * IP地址
     */
    private String IPAddr;
    /**
     * 端口
     */
    private int port;
    /**
     * IP所属城市
     */
    private String city;
    /**
     * IP所属国家
     */
    private String country;
    /**
     * 所属企业
     */
    private String org;
    /**
     * 纬度
     */
    private double latitude;
    /**
     * 经度
     */
    private double longitude;
    /**
     * ISP
     */
    private String ISP;
    /**
     * AS
     */
    private String AS;
    /**
     * STAT 省份
     */
    private String STAT;
}
