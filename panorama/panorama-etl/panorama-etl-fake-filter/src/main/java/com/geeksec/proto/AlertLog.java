package com.geeksec.proto;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: alert_log.proto
// Protobuf Java Version: 4.29.4

import com.geeksec.proto.base.IpInfo;
import com.geeksec.proto.message.*;

public final class AlertLog {
  private AlertLog() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      AlertLog.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ALERT_LOGOrBuilder extends
      // @@protoc_insertion_point(interface_extends:ALERT_LOG)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     * @return Whether the guid field is set.
     */
    boolean hasGuid();
    /**
     * <pre>
     * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     * @return The guid.
     */
    String getGuid();
    /**
     * <pre>
     * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     * @return The bytes for guid.
     */
    com.google.protobuf.ByteString
        getGuidBytes();

    /**
     * <pre>
     * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     * @return Whether the time field is set.
     */
    boolean hasTime();
    /**
     * <pre>
     * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     * @return The time.
     */
    String getTime();
    /**
     * <pre>
     * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     * @return The bytes for time.
     */
    com.google.protobuf.ByteString
        getTimeBytes();

    /**
     * <pre>
     * 线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     * @return Whether the lineInfo field is set.
     */
    boolean hasLineInfo();
    /**
     * <pre>
     * 线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     * @return The lineInfo.
     */
    String getLineInfo();
    /**
     * <pre>
     * 线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     * @return The bytes for lineInfo.
     */
    com.google.protobuf.ByteString
        getLineInfoBytes();

    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     * @return Whether the sip field is set.
     */
    boolean hasSip();
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     * @return The sip.
     */
    IpInfo.IP_INFO getSip();
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    IpInfo.IP_INFOOrBuilder getSipOrBuilder();

    /**
     * <pre>
     * 目的IP信息;
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     * @return Whether the dip field is set.
     */
    boolean hasDip();
    /**
     * <pre>
     * 目的IP信息;
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     * @return The dip.
     */
    IpInfo.IP_INFO getDip();
    /**
     * <pre>
     * 目的IP信息;
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    IpInfo.IP_INFOOrBuilder getDipOrBuilder();

    /**
     * <pre>
     * 受害者IP信息;
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     * @return Whether the aip field is set.
     */
    boolean hasAip();
    /**
     * <pre>
     * 受害者IP信息;
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     * @return The aip.
     */
    IpInfo.IP_INFO getAip();
    /**
     * <pre>
     * 受害者IP信息;
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    IpInfo.IP_INFOOrBuilder getAipOrBuilder();

    /**
     * <pre>
     * 攻击IP信息;
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     * @return Whether the vip field is set.
     */
    boolean hasVip();
    /**
     * <pre>
     * 攻击IP信息;
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     * @return The vip.
     */
    IpInfo.IP_INFO getVip();
    /**
     * <pre>
     * 攻击IP信息;
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    IpInfo.IP_INFOOrBuilder getVipOrBuilder();

    /**
     * <pre>
     * 传感器IP
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     * @return Whether the sensorIp field is set.
     */
    boolean hasSensorIp();
    /**
     * <pre>
     * 传感器IP
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     * @return The sensorIp.
     */
    String getSensorIp();
    /**
     * <pre>
     * 传感器IP
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     * @return The bytes for sensorIp.
     */
    com.google.protobuf.ByteString
        getSensorIpBytes();

    /**
     * <pre>
     * 供应商ID
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     * @return Whether the vendorId field is set.
     */
    boolean hasVendorId();
    /**
     * <pre>
     * 供应商ID
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     * @return The vendorId.
     */
    String getVendorId();
    /**
     * <pre>
     * 供应商ID
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     * @return The bytes for vendorId.
     */
    com.google.protobuf.ByteString
        getVendorIdBytes();

    /**
     * <pre>
     * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     * @return Whether the lRAggregateValue field is set.
     */
    boolean hasLRAggregateValue();
    /**
     * <pre>
     * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     * @return The lRAggregateValue.
     */
    String getLRAggregateValue();
    /**
     * <pre>
     * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     * @return The bytes for lRAggregateValue.
     */
    com.google.protobuf.ByteString
        getLRAggregateValueBytes();

    /**
     * <pre>
     * 最近短时首次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     * @return Whether the lRFirstAlertDate field is set.
     */
    boolean hasLRFirstAlertDate();
    /**
     * <pre>
     * 最近短时首次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     * @return The lRFirstAlertDate.
     */
    long getLRFirstAlertDate();

    /**
     * <pre>
     * 最近短时末次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     * @return Whether the lRLastAlertDate field is set.
     */
    boolean hasLRLastAlertDate();
    /**
     * <pre>
     * 最近短时末次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     * @return The lRLastAlertDate.
     */
    long getLRLastAlertDate();

    /**
     * <pre>
     * 最近短时告警次数
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     * @return Whether the lRAlertTimes field is set.
     */
    boolean hasLRAlertTimes();
    /**
     * <pre>
     * 最近短时告警次数
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     * @return The lRAlertTimes.
     */
    int getLRAlertTimes();

    /**
     * <pre>
     * 检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     * @return Whether the detectType field is set.
     */
    boolean hasDetectType();
    /**
     * <pre>
     * 检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     * @return The detectType.
     */
    int getDetectType();

    /**
     * <pre>
     * 威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     * @return Whether the threatType field is set.
     */
    boolean hasThreatType();
    /**
     * <pre>
     * 威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     * @return The threatType.
     */
    int getThreatType();

    /**
     * <pre>
     * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     * @return Whether the severity field is set.
     */
    boolean hasSeverity();
    /**
     * <pre>
     * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     * @return The severity.
     */
    int getSeverity();

    /**
     * <pre>
     * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     * @return Whether the killChain field is set.
     */
    boolean hasKillChain();
    /**
     * <pre>
     * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     * @return The killChain.
     */
    String getKillChain();
    /**
     * <pre>
     * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     * @return The bytes for killChain.
     */
    com.google.protobuf.ByteString
        getKillChainBytes();

    /**
     * <pre>
     * ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     * @return Whether the tactic field is set.
     */
    boolean hasTactic();
    /**
     * <pre>
     * ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     * @return The tactic.
     */
    String getTactic();
    /**
     * <pre>
     * ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     * @return The bytes for tactic.
     */
    com.google.protobuf.ByteString
        getTacticBytes();

    /**
     * <pre>
     * ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     * @return Whether the technique field is set.
     */
    boolean hasTechnique();
    /**
     * <pre>
     * ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     * @return The technique.
     */
    String getTechnique();
    /**
     * <pre>
     * ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     * @return The bytes for technique.
     */
    com.google.protobuf.ByteString
        getTechniqueBytes();

    /**
     * <pre>
     * 置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     * @return Whether the confidence field is set.
     */
    boolean hasConfidence();
    /**
     * <pre>
     * 置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     * @return The confidence.
     */
    String getConfidence();
    /**
     * <pre>
     * 置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     * @return The bytes for confidence.
     */
    com.google.protobuf.ByteString
        getConfidenceBytes();

    /**
     * <pre>
     * 传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     * @return Whether the tranProto field is set.
     */
    boolean hasTranProto();
    /**
     * <pre>
     * 传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     * @return The tranProto.
     */
    String getTranProto();
    /**
     * <pre>
     * 传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     * @return The bytes for tranProto.
     */
    com.google.protobuf.ByteString
        getTranProtoBytes();

    /**
     * <pre>
     * 应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     * @return Whether the appProto field is set.
     */
    boolean hasAppProto();
    /**
     * <pre>
     * 应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     * @return The appProto.
     */
    String getAppProto();
    /**
     * <pre>
     * 应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     * @return The bytes for appProto.
     */
    com.google.protobuf.ByteString
        getAppProtoBytes();

    /**
     * <pre>
     * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     * @return Whether the metaData field is set.
     */
    boolean hasMetaData();
    /**
     * <pre>
     * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     * @return The metaData.
     */
    com.google.protobuf.ByteString getMetaData();

    /**
     * <pre>
     * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     * @return Whether the rawData field is set.
     */
    boolean hasRawData();
    /**
     * <pre>
     * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     * @return The rawData.
     */
    String getRawData();
    /**
     * <pre>
     * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     * @return The bytes for rawData.
     */
    com.google.protobuf.ByteString
        getRawDataBytes();

    /**
     * <pre>
     * 失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     * @return Whether the iocAlertInfo field is set.
     */
    boolean hasIocAlertInfo();
    /**
     * <pre>
     * 失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     * @return The iocAlertInfo.
     */
    IocAlertInfo.IOC_ALERT_INFO getIocAlertInfo();
    /**
     * <pre>
     * 失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    IocAlertInfo.IOC_ALERT_INFOOrBuilder getIocAlertInfoOrBuilder();

    /**
     * <pre>
     * 异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     * @return Whether the iobAlertInfo field is set.
     */
    boolean hasIobAlertInfo();
    /**
     * <pre>
     * 异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     * @return The iobAlertInfo.
     */
    IobAlertInfo.IOB_ALERT_INFO getIobAlertInfo();
    /**
     * <pre>
     * 异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    IobAlertInfo.IOB_ALERT_INFOOrBuilder getIobAlertInfoOrBuilder();

    /**
     * <pre>
     * 攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     * @return Whether the ioaAlertInfo field is set.
     */
    boolean hasIoaAlertInfo();
    /**
     * <pre>
     * 攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     * @return The ioaAlertInfo.
     */
    IoaAlertInfo.IOA_ALERT_INFO getIoaAlertInfo();
    /**
     * <pre>
     * 攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    IoaAlertInfo.IOA_ALERT_INFOOrBuilder getIoaAlertInfoOrBuilder();

    /**
     * <pre>
     * 工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     * @return Whether the iiotAlertInfo field is set.
     */
    boolean hasIiotAlertInfo();
    /**
     * <pre>
     * 工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     * @return The iiotAlertInfo.
     */
    IiotAlertInfo.IIOT_ALERT_INFO getIiotAlertInfo();
    /**
     * <pre>
     * 工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    IiotAlertInfo.IIOT_ALERT_INFOOrBuilder getIiotAlertInfoOrBuilder();

    /**
     * <pre>
     * 文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     * @return Whether the fileAlertInfo field is set.
     */
    boolean hasFileAlertInfo();
    /**
     * <pre>
     * 文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     * @return The fileAlertInfo.
     */
    FileAlertInfo.FILE_ALERT_INFO getFileAlertInfo();
    /**
     * <pre>
     * 文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    FileAlertInfo.FILE_ALERT_INFOOrBuilder getFileAlertInfoOrBuilder();

    /**
     * <pre>
     * 密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     * @return Whether the cryptoAlertInfo field is set.
     */
    boolean hasCryptoAlertInfo();
    /**
     * <pre>
     * 密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     * @return The cryptoAlertInfo.
     */
    CryptoAlertInfo.CRYPTO_ALERT_INFO getCryptoAlertInfo();
    /**
     * <pre>
     * 密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder getCryptoAlertInfoOrBuilder();

    /**
     * <pre>
     * 证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     * @return Whether the certAlertInfo field is set.
     */
    boolean hasCertAlertInfo();
    /**
     * <pre>
     * 证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     * @return The certAlertInfo.
     */
    CertAlertInfo.CERT_ALERT_INFO getCertAlertInfo();
    /**
     * <pre>
     * 证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    CertAlertInfo.CERT_ALERT_INFOOrBuilder getCertAlertInfoOrBuilder();

    /**
     * <pre>
     * 邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     * @return Whether the mailAlertInfo field is set.
     */
    boolean hasMailAlertInfo();
    /**
     * <pre>
     * 邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     * @return The mailAlertInfo.
     */
    MailAlertInfo.MAIL_ALERT_INFO getMailAlertInfo();
    /**
     * <pre>
     * 邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    MailAlertInfo.MAIL_ALERT_INFOOrBuilder getMailAlertInfoOrBuilder();

    /**
     * <pre>
     * 移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     * @return Whether the mobileAlertInfo field is set.
     */
    boolean hasMobileAlertInfo();
    /**
     * <pre>
     * 移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     * @return The mobileAlertInfo.
     */
    MobileAlertInfo.MOBILE_ALERT_INFO getMobileAlertInfo();
    /**
     * <pre>
     * 移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder getMobileAlertInfoOrBuilder();

    /**
     * <pre>
     * 特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     * @return Whether the protoAlertInfo field is set.
     */
    boolean hasProtoAlertInfo();
    /**
     * <pre>
     * 特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     * @return The protoAlertInfo.
     */
    ProtoAlertInfo.PROTO_ALERT_INFO getProtoAlertInfo();
    /**
     * <pre>
     * 特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder getProtoAlertInfoOrBuilder();
  }
  /**
   * Protobuf type {@code ALERT_LOG}
   */
  public static final class ALERT_LOG extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:ALERT_LOG)
      ALERT_LOGOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        ALERT_LOG.class.getName());
    }
    // Use ALERT_LOG.newBuilder() to construct.
    private ALERT_LOG(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ALERT_LOG() {
      guid_ = "";
      time_ = "";
      lineInfo_ = "";
      sensorIp_ = "";
      vendorId_ = "";
      lRAggregateValue_ = "";
      killChain_ = "";
      tactic_ = "";
      technique_ = "";
      confidence_ = "";
      tranProto_ = "";
      appProto_ = "";
      metaData_ = com.google.protobuf.ByteString.EMPTY;
      rawData_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return AlertLog.internal_static_ALERT_LOG_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return AlertLog.internal_static_ALERT_LOG_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ALERT_LOG.class, Builder.class);
    }

    private int bitField0_;
    private int bitField1_;
    public static final int GUID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object guid_ = "";
    /**
     * <pre>
     * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     * @return Whether the guid field is set.
     */
    @Override
    public boolean hasGuid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     * @return The guid.
     */
    @Override
    public String getGuid() {
      Object ref = guid_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          guid_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
     * </pre>
     *
     * <code>required string guid = 1;</code>
     * @return The bytes for guid.
     */
    @Override
    public com.google.protobuf.ByteString
        getGuidBytes() {
      Object ref = guid_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        guid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TIME_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile Object time_ = "";
    /**
     * <pre>
     * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     * @return Whether the time field is set.
     */
    @Override
    public boolean hasTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     * @return The time.
     */
    @Override
    public String getTime() {
      Object ref = time_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          time_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
     * </pre>
     *
     * <code>required string time = 2;</code>
     * @return The bytes for time.
     */
    @Override
    public com.google.protobuf.ByteString
        getTimeBytes() {
      Object ref = time_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        time_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LINE_INFO_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile Object lineInfo_ = "";
    /**
     * <pre>
     * 线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     * @return Whether the lineInfo field is set.
     */
    @Override
    public boolean hasLineInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     * @return The lineInfo.
     */
    @Override
    public String getLineInfo() {
      Object ref = lineInfo_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lineInfo_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 线路号	51字节头
     * </pre>
     *
     * <code>required string line_info = 3;</code>
     * @return The bytes for lineInfo.
     */
    @Override
    public com.google.protobuf.ByteString
        getLineInfoBytes() {
      Object ref = lineInfo_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        lineInfo_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SIP_FIELD_NUMBER = 4;
    private IpInfo.IP_INFO sip_;
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     * @return Whether the sip field is set.
     */
    @Override
    public boolean hasSip() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     * @return The sip.
     */
    @Override
    public IpInfo.IP_INFO getSip() {
      return sip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : sip_;
    }
    /**
     * <pre>
     * 源IP信息
     * </pre>
     *
     * <code>required .IP_INFO sip = 4;</code>
     */
    @Override
    public IpInfo.IP_INFOOrBuilder getSipOrBuilder() {
      return sip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : sip_;
    }

    public static final int DIP_FIELD_NUMBER = 5;
    private IpInfo.IP_INFO dip_;
    /**
     * <pre>
     * 目的IP信息;
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     * @return Whether the dip field is set.
     */
    @Override
    public boolean hasDip() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 目的IP信息;
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     * @return The dip.
     */
    @Override
    public IpInfo.IP_INFO getDip() {
      return dip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : dip_;
    }
    /**
     * <pre>
     * 目的IP信息;
     * </pre>
     *
     * <code>required .IP_INFO dip = 5;</code>
     */
    @Override
    public IpInfo.IP_INFOOrBuilder getDipOrBuilder() {
      return dip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : dip_;
    }

    public static final int AIP_FIELD_NUMBER = 6;
    private IpInfo.IP_INFO aip_;
    /**
     * <pre>
     * 受害者IP信息;
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     * @return Whether the aip field is set.
     */
    @Override
    public boolean hasAip() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 受害者IP信息;
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     * @return The aip.
     */
    @Override
    public IpInfo.IP_INFO getAip() {
      return aip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : aip_;
    }
    /**
     * <pre>
     * 受害者IP信息;
     * </pre>
     *
     * <code>required .IP_INFO aip = 6;</code>
     */
    @Override
    public IpInfo.IP_INFOOrBuilder getAipOrBuilder() {
      return aip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : aip_;
    }

    public static final int VIP_FIELD_NUMBER = 7;
    private IpInfo.IP_INFO vip_;
    /**
     * <pre>
     * 攻击IP信息;
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     * @return Whether the vip field is set.
     */
    @Override
    public boolean hasVip() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 攻击IP信息;
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     * @return The vip.
     */
    @Override
    public IpInfo.IP_INFO getVip() {
      return vip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : vip_;
    }
    /**
     * <pre>
     * 攻击IP信息;
     * </pre>
     *
     * <code>required .IP_INFO vip = 7;</code>
     */
    @Override
    public IpInfo.IP_INFOOrBuilder getVipOrBuilder() {
      return vip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : vip_;
    }

    public static final int SENSOR_IP_FIELD_NUMBER = 8;
    @SuppressWarnings("serial")
    private volatile Object sensorIp_ = "";
    /**
     * <pre>
     * 传感器IP
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     * @return Whether the sensorIp field is set.
     */
    @Override
    public boolean hasSensorIp() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 传感器IP
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     * @return The sensorIp.
     */
    @Override
    public String getSensorIp() {
      Object ref = sensorIp_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sensorIp_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 传感器IP
     * </pre>
     *
     * <code>required string sensor_ip = 8;</code>
     * @return The bytes for sensorIp.
     */
    @Override
    public com.google.protobuf.ByteString
        getSensorIpBytes() {
      Object ref = sensorIp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        sensorIp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VENDOR_ID_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile Object vendorId_ = "";
    /**
     * <pre>
     * 供应商ID
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     * @return Whether the vendorId field is set.
     */
    @Override
    public boolean hasVendorId() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 供应商ID
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     * @return The vendorId.
     */
    @Override
    public String getVendorId() {
      Object ref = vendorId_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          vendorId_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 供应商ID
     * </pre>
     *
     * <code>required string vendor_id = 9;</code>
     * @return The bytes for vendorId.
     */
    @Override
    public com.google.protobuf.ByteString
        getVendorIdBytes() {
      Object ref = vendorId_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        vendorId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LR_AGGREGATE_VALUE_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile Object lRAggregateValue_ = "";
    /**
     * <pre>
     * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     * @return Whether the lRAggregateValue field is set.
     */
    @Override
    public boolean hasLRAggregateValue() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     * @return The lRAggregateValue.
     */
    @Override
    public String getLRAggregateValue() {
      Object ref = lRAggregateValue_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lRAggregateValue_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
     * </pre>
     *
     * <code>required string LR_aggregate_value = 10;</code>
     * @return The bytes for lRAggregateValue.
     */
    @Override
    public com.google.protobuf.ByteString
        getLRAggregateValueBytes() {
      Object ref = lRAggregateValue_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        lRAggregateValue_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LR_FIRST_ALERT_DATE_FIELD_NUMBER = 11;
    private long lRFirstAlertDate_ = 0L;
    /**
     * <pre>
     * 最近短时首次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     * @return Whether the lRFirstAlertDate field is set.
     */
    @Override
    public boolean hasLRFirstAlertDate() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 最近短时首次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_first_alert_date = 11;</code>
     * @return The lRFirstAlertDate.
     */
    @Override
    public long getLRFirstAlertDate() {
      return lRFirstAlertDate_;
    }

    public static final int LR_LAST_ALERT_DATE_FIELD_NUMBER = 12;
    private long lRLastAlertDate_ = 0L;
    /**
     * <pre>
     * 最近短时末次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     * @return Whether the lRLastAlertDate field is set.
     */
    @Override
    public boolean hasLRLastAlertDate() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 最近短时末次告警时刻
     * </pre>
     *
     * <code>required uint64 LR_last_alert_date = 12;</code>
     * @return The lRLastAlertDate.
     */
    @Override
    public long getLRLastAlertDate() {
      return lRLastAlertDate_;
    }

    public static final int LR_ALERT_TIMES_FIELD_NUMBER = 13;
    private int lRAlertTimes_ = 0;
    /**
     * <pre>
     * 最近短时告警次数
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     * @return Whether the lRAlertTimes field is set.
     */
    @Override
    public boolean hasLRAlertTimes() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 最近短时告警次数
     * </pre>
     *
     * <code>required uint32 LR_alert_times = 13;</code>
     * @return The lRAlertTimes.
     */
    @Override
    public int getLRAlertTimes() {
      return lRAlertTimes_;
    }

    public static final int DETECT_TYPE_FIELD_NUMBER = 14;
    private int detectType_ = 0;
    /**
     * <pre>
     * 检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     * @return Whether the detectType field is set.
     */
    @Override
    public boolean hasDetectType() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 检测类型	取值范围为后文中*_alert_info的序列编号
     * </pre>
     *
     * <code>required uint32 detect_type = 14;</code>
     * @return The detectType.
     */
    @Override
    public int getDetectType() {
      return detectType_;
    }

    public static final int THREAT_TYPE_FIELD_NUMBER = 15;
    private int threatType_ = 0;
    /**
     * <pre>
     * 威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     * @return Whether the threatType field is set.
     */
    @Override
    public boolean hasThreatType() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 威胁类型	见威胁类型列表
     * </pre>
     *
     * <code>required uint32 threat_type = 15;</code>
     * @return The threatType.
     */
    @Override
    public int getThreatType() {
      return threatType_;
    }

    public static final int SEVERITY_FIELD_NUMBER = 16;
    private int severity_ = 0;
    /**
     * <pre>
     * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     * @return Whether the severity field is set.
     */
    @Override
    public boolean hasSeverity() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
     * </pre>
     *
     * <code>required uint32 severity = 16;</code>
     * @return The severity.
     */
    @Override
    public int getSeverity() {
      return severity_;
    }

    public static final int KILL_CHAIN_FIELD_NUMBER = 17;
    @SuppressWarnings("serial")
    private volatile Object killChain_ = "";
    /**
     * <pre>
     * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     * @return Whether the killChain field is set.
     */
    @Override
    public boolean hasKillChain() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     * @return The killChain.
     */
    @Override
    public String getKillChain() {
      Object ref = killChain_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          killChain_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
     * </pre>
     *
     * <code>required string kill_chain = 17;</code>
     * @return The bytes for killChain.
     */
    @Override
    public com.google.protobuf.ByteString
        getKillChainBytes() {
      Object ref = killChain_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        killChain_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TACTIC_FIELD_NUMBER = 18;
    @SuppressWarnings("serial")
    private volatile Object tactic_ = "";
    /**
     * <pre>
     * ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     * @return Whether the tactic field is set.
     */
    @Override
    public boolean hasTactic() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     * @return The tactic.
     */
    @Override
    public String getTactic() {
      Object ref = tactic_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          tactic_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * ATT&amp;CK策略标签	TA0001（初始访问）
     * </pre>
     *
     * <code>optional string tactic = 18;</code>
     * @return The bytes for tactic.
     */
    @Override
    public com.google.protobuf.ByteString
        getTacticBytes() {
      Object ref = tactic_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        tactic_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TECHNIQUE_FIELD_NUMBER = 19;
    @SuppressWarnings("serial")
    private volatile Object technique_ = "";
    /**
     * <pre>
     * ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     * @return Whether the technique field is set.
     */
    @Override
    public boolean hasTechnique() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     * @return The technique.
     */
    @Override
    public String getTechnique() {
      Object ref = technique_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          technique_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * ATT&amp;CK技术标签	T1566（网络钓鱼）
     * </pre>
     *
     * <code>optional string technique = 19;</code>
     * @return The bytes for technique.
     */
    @Override
    public com.google.protobuf.ByteString
        getTechniqueBytes() {
      Object ref = technique_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        technique_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONFIDENCE_FIELD_NUMBER = 20;
    @SuppressWarnings("serial")
    private volatile Object confidence_ = "";
    /**
     * <pre>
     * 置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     * @return Whether the confidence field is set.
     */
    @Override
    public boolean hasConfidence() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     * @return The confidence.
     */
    @Override
    public String getConfidence() {
      Object ref = confidence_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          confidence_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 置信度	低、中、高
     * </pre>
     *
     * <code>required string confidence = 20;</code>
     * @return The bytes for confidence.
     */
    @Override
    public com.google.protobuf.ByteString
        getConfidenceBytes() {
      Object ref = confidence_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        confidence_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TRAN_PROTO_FIELD_NUMBER = 21;
    @SuppressWarnings("serial")
    private volatile Object tranProto_ = "";
    /**
     * <pre>
     * 传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     * @return Whether the tranProto field is set.
     */
    @Override
    public boolean hasTranProto() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     * @return The tranProto.
     */
    @Override
    public String getTranProto() {
      Object ref = tranProto_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          tranProto_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 传输层协议	TCP、UDP、SCTP
     * </pre>
     *
     * <code>required string tran_proto = 21;</code>
     * @return The bytes for tranProto.
     */
    @Override
    public com.google.protobuf.ByteString
        getTranProtoBytes() {
      Object ref = tranProto_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        tranProto_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APP_PROTO_FIELD_NUMBER = 22;
    @SuppressWarnings("serial")
    private volatile Object appProto_ = "";
    /**
     * <pre>
     * 应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     * @return Whether the appProto field is set.
     */
    @Override
    public boolean hasAppProto() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     * @return The appProto.
     */
    @Override
    public String getAppProto() {
      Object ref = appProto_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appProto_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 应用层协议	HTTP、TLS、SSH
     * </pre>
     *
     * <code>optional string app_proto = 22;</code>
     * @return The bytes for appProto.
     */
    @Override
    public com.google.protobuf.ByteString
        getAppProtoBytes() {
      Object ref = appProto_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        appProto_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int META_DATA_FIELD_NUMBER = 23;
    private com.google.protobuf.ByteString metaData_ = com.google.protobuf.ByteString.EMPTY;
    /**
     * <pre>
     * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     * @return Whether the metaData field is set.
     */
    @Override
    public boolean hasMetaData() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
     * </pre>
     *
     * <code>optional bytes meta_data = 23;</code>
     * @return The metaData.
     */
    @Override
    public com.google.protobuf.ByteString getMetaData() {
      return metaData_;
    }

    public static final int RAW_DATA_FIELD_NUMBER = 24;
    @SuppressWarnings("serial")
    private volatile Object rawData_ = "";
    /**
     * <pre>
     * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     * @return Whether the rawData field is set.
     */
    @Override
    public boolean hasRawData() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     * @return The rawData.
     */
    @Override
    public String getRawData() {
      Object ref = rawData_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          rawData_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
     * </pre>
     *
     * <code>optional string raw_data = 24;</code>
     * @return The bytes for rawData.
     */
    @Override
    public com.google.protobuf.ByteString
        getRawDataBytes() {
      Object ref = rawData_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        rawData_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IOC_ALERT_INFO_FIELD_NUMBER = 100;
    private IocAlertInfo.IOC_ALERT_INFO iocAlertInfo_;
    /**
     * <pre>
     * 失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     * @return Whether the iocAlertInfo field is set.
     */
    @Override
    public boolean hasIocAlertInfo() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <pre>
     * 失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     * @return The iocAlertInfo.
     */
    @Override
    public IocAlertInfo.IOC_ALERT_INFO getIocAlertInfo() {
      return iocAlertInfo_ == null ? IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
    }
    /**
     * <pre>
     * 失陷情报告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
     */
    @Override
    public IocAlertInfo.IOC_ALERT_INFOOrBuilder getIocAlertInfoOrBuilder() {
      return iocAlertInfo_ == null ? IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
    }

    public static final int IOB_ALERT_INFO_FIELD_NUMBER = 101;
    private IobAlertInfo.IOB_ALERT_INFO iobAlertInfo_;
    /**
     * <pre>
     * 异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     * @return Whether the iobAlertInfo field is set.
     */
    @Override
    public boolean hasIobAlertInfo() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <pre>
     * 异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     * @return The iobAlertInfo.
     */
    @Override
    public IobAlertInfo.IOB_ALERT_INFO getIobAlertInfo() {
      return iobAlertInfo_ == null ? IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
    }
    /**
     * <pre>
     * 异常行为告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
     */
    @Override
    public IobAlertInfo.IOB_ALERT_INFOOrBuilder getIobAlertInfoOrBuilder() {
      return iobAlertInfo_ == null ? IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
    }

    public static final int IOA_ALERT_INFO_FIELD_NUMBER = 102;
    private IoaAlertInfo.IOA_ALERT_INFO ioaAlertInfo_;
    /**
     * <pre>
     * 攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     * @return Whether the ioaAlertInfo field is set.
     */
    @Override
    public boolean hasIoaAlertInfo() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <pre>
     * 攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     * @return The ioaAlertInfo.
     */
    @Override
    public IoaAlertInfo.IOA_ALERT_INFO getIoaAlertInfo() {
      return ioaAlertInfo_ == null ? IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
    }
    /**
     * <pre>
     * 攻击利用告警信息	封装格式
     * </pre>
     *
     * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
     */
    @Override
    public IoaAlertInfo.IOA_ALERT_INFOOrBuilder getIoaAlertInfoOrBuilder() {
      return ioaAlertInfo_ == null ? IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
    }

    public static final int IIOT_ALERT_INFO_FIELD_NUMBER = 103;
    private IiotAlertInfo.IIOT_ALERT_INFO iiotAlertInfo_;
    /**
     * <pre>
     * 工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     * @return Whether the iiotAlertInfo field is set.
     */
    @Override
    public boolean hasIiotAlertInfo() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <pre>
     * 工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     * @return The iiotAlertInfo.
     */
    @Override
    public IiotAlertInfo.IIOT_ALERT_INFO getIiotAlertInfo() {
      return iiotAlertInfo_ == null ? IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
    }
    /**
     * <pre>
     * 工业物联网告警信息	封装格式
     * </pre>
     *
     * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
     */
    @Override
    public IiotAlertInfo.IIOT_ALERT_INFOOrBuilder getIiotAlertInfoOrBuilder() {
      return iiotAlertInfo_ == null ? IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
    }

    public static final int FILE_ALERT_INFO_FIELD_NUMBER = 104;
    private FileAlertInfo.FILE_ALERT_INFO fileAlertInfo_;
    /**
     * <pre>
     * 文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     * @return Whether the fileAlertInfo field is set.
     */
    @Override
    public boolean hasFileAlertInfo() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <pre>
     * 文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     * @return The fileAlertInfo.
     */
    @Override
    public FileAlertInfo.FILE_ALERT_INFO getFileAlertInfo() {
      return fileAlertInfo_ == null ? FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
    }
    /**
     * <pre>
     * 文件检测告警信息	封装格式
     * </pre>
     *
     * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
     */
    @Override
    public FileAlertInfo.FILE_ALERT_INFOOrBuilder getFileAlertInfoOrBuilder() {
      return fileAlertInfo_ == null ? FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
    }

    public static final int CRYPTO_ALERT_INFO_FIELD_NUMBER = 105;
    private CryptoAlertInfo.CRYPTO_ALERT_INFO cryptoAlertInfo_;
    /**
     * <pre>
     * 密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     * @return Whether the cryptoAlertInfo field is set.
     */
    @Override
    public boolean hasCryptoAlertInfo() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <pre>
     * 密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     * @return The cryptoAlertInfo.
     */
    @Override
    public CryptoAlertInfo.CRYPTO_ALERT_INFO getCryptoAlertInfo() {
      return cryptoAlertInfo_ == null ? CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
    }
    /**
     * <pre>
     * 密数据异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
     */
    @Override
    public CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder getCryptoAlertInfoOrBuilder() {
      return cryptoAlertInfo_ == null ? CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
    }

    public static final int CERT_ALERT_INFO_FIELD_NUMBER = 106;
    private CertAlertInfo.CERT_ALERT_INFO certAlertInfo_;
    /**
     * <pre>
     * 证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     * @return Whether the certAlertInfo field is set.
     */
    @Override
    public boolean hasCertAlertInfo() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <pre>
     * 证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     * @return The certAlertInfo.
     */
    @Override
    public CertAlertInfo.CERT_ALERT_INFO getCertAlertInfo() {
      return certAlertInfo_ == null ? CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
    }
    /**
     * <pre>
     * 证书异常告警信息	封装格式
     * </pre>
     *
     * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
     */
    @Override
    public CertAlertInfo.CERT_ALERT_INFOOrBuilder getCertAlertInfoOrBuilder() {
      return certAlertInfo_ == null ? CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
    }

    public static final int MAIL_ALERT_INFO_FIELD_NUMBER = 107;
    private MailAlertInfo.MAIL_ALERT_INFO mailAlertInfo_;
    /**
     * <pre>
     * 邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     * @return Whether the mailAlertInfo field is set.
     */
    @Override
    public boolean hasMailAlertInfo() {
      return ((bitField0_ & 0x80000000) != 0);
    }
    /**
     * <pre>
     * 邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     * @return The mailAlertInfo.
     */
    @Override
    public MailAlertInfo.MAIL_ALERT_INFO getMailAlertInfo() {
      return mailAlertInfo_ == null ? MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
    }
    /**
     * <pre>
     * 邮件威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
     */
    @Override
    public MailAlertInfo.MAIL_ALERT_INFOOrBuilder getMailAlertInfoOrBuilder() {
      return mailAlertInfo_ == null ? MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
    }

    public static final int MOBILE_ALERT_INFO_FIELD_NUMBER = 108;
    private MobileAlertInfo.MOBILE_ALERT_INFO mobileAlertInfo_;
    /**
     * <pre>
     * 移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     * @return Whether the mobileAlertInfo field is set.
     */
    @Override
    public boolean hasMobileAlertInfo() {
      return ((bitField1_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     * @return The mobileAlertInfo.
     */
    @Override
    public MobileAlertInfo.MOBILE_ALERT_INFO getMobileAlertInfo() {
      return mobileAlertInfo_ == null ? MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
    }
    /**
     * <pre>
     * 移动网威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
     */
    @Override
    public MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder getMobileAlertInfoOrBuilder() {
      return mobileAlertInfo_ == null ? MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
    }

    public static final int PROTO_ALERT_INFO_FIELD_NUMBER = 109;
    private ProtoAlertInfo.PROTO_ALERT_INFO protoAlertInfo_;
    /**
     * <pre>
     * 特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     * @return Whether the protoAlertInfo field is set.
     */
    @Override
    public boolean hasProtoAlertInfo() {
      return ((bitField1_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     * @return The protoAlertInfo.
     */
    @Override
    public ProtoAlertInfo.PROTO_ALERT_INFO getProtoAlertInfo() {
      return protoAlertInfo_ == null ? ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
    }
    /**
     * <pre>
     * 特色协议威胁告警信息	封装格式
     * </pre>
     *
     * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
     */
    @Override
    public ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder getProtoAlertInfoOrBuilder() {
      return protoAlertInfo_ == null ? ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasGuid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLineInfo()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasVip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSensorIp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasVendorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRAggregateValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRFirstAlertDate()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRLastAlertDate()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLRAlertTimes()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDetectType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasThreatType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSeverity()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasKillChain()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasConfidence()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTranProto()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getSip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getDip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getAip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getVip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasIocAlertInfo()) {
        if (!getIocAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasIoaAlertInfo()) {
        if (!getIoaAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasIiotAlertInfo()) {
        if (!getIiotAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasFileAlertInfo()) {
        if (!getFileAlertInfo().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, guid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, time_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, lineInfo_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getSip());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getDip());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(6, getAip());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getVip());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 8, sensorIp_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, vendorId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, lRAggregateValue_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeUInt64(11, lRFirstAlertDate_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeUInt64(12, lRLastAlertDate_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeUInt32(13, lRAlertTimes_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeUInt32(14, detectType_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeUInt32(15, threatType_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeUInt32(16, severity_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 17, killChain_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 18, tactic_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 19, technique_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 20, confidence_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 21, tranProto_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 22, appProto_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeBytes(23, metaData_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 24, rawData_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeMessage(100, getIocAlertInfo());
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        output.writeMessage(101, getIobAlertInfo());
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeMessage(102, getIoaAlertInfo());
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeMessage(103, getIiotAlertInfo());
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeMessage(104, getFileAlertInfo());
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeMessage(105, getCryptoAlertInfo());
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeMessage(106, getCertAlertInfo());
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        output.writeMessage(107, getMailAlertInfo());
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        output.writeMessage(108, getMobileAlertInfo());
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        output.writeMessage(109, getProtoAlertInfo());
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, guid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, time_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, lineInfo_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getSip());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getDip());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getAip());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getVip());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(8, sensorIp_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(9, vendorId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, lRAggregateValue_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, lRFirstAlertDate_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(12, lRLastAlertDate_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(13, lRAlertTimes_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(14, detectType_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(15, threatType_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(16, severity_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(17, killChain_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(18, tactic_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(19, technique_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(20, confidence_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(21, tranProto_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(22, appProto_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(23, metaData_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(24, rawData_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(100, getIocAlertInfo());
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(101, getIobAlertInfo());
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(102, getIoaAlertInfo());
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(103, getIiotAlertInfo());
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(104, getFileAlertInfo());
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(105, getCryptoAlertInfo());
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(106, getCertAlertInfo());
      }
      if (((bitField0_ & 0x80000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(107, getMailAlertInfo());
      }
      if (((bitField1_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(108, getMobileAlertInfo());
      }
      if (((bitField1_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(109, getProtoAlertInfo());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ALERT_LOG)) {
        return super.equals(obj);
      }
      ALERT_LOG other = (ALERT_LOG) obj;

      if (hasGuid() != other.hasGuid()) return false;
      if (hasGuid()) {
        if (!getGuid()
            .equals(other.getGuid())) return false;
      }
      if (hasTime() != other.hasTime()) return false;
      if (hasTime()) {
        if (!getTime()
            .equals(other.getTime())) return false;
      }
      if (hasLineInfo() != other.hasLineInfo()) return false;
      if (hasLineInfo()) {
        if (!getLineInfo()
            .equals(other.getLineInfo())) return false;
      }
      if (hasSip() != other.hasSip()) return false;
      if (hasSip()) {
        if (!getSip()
            .equals(other.getSip())) return false;
      }
      if (hasDip() != other.hasDip()) return false;
      if (hasDip()) {
        if (!getDip()
            .equals(other.getDip())) return false;
      }
      if (hasAip() != other.hasAip()) return false;
      if (hasAip()) {
        if (!getAip()
            .equals(other.getAip())) return false;
      }
      if (hasVip() != other.hasVip()) return false;
      if (hasVip()) {
        if (!getVip()
            .equals(other.getVip())) return false;
      }
      if (hasSensorIp() != other.hasSensorIp()) return false;
      if (hasSensorIp()) {
        if (!getSensorIp()
            .equals(other.getSensorIp())) return false;
      }
      if (hasVendorId() != other.hasVendorId()) return false;
      if (hasVendorId()) {
        if (!getVendorId()
            .equals(other.getVendorId())) return false;
      }
      if (hasLRAggregateValue() != other.hasLRAggregateValue()) return false;
      if (hasLRAggregateValue()) {
        if (!getLRAggregateValue()
            .equals(other.getLRAggregateValue())) return false;
      }
      if (hasLRFirstAlertDate() != other.hasLRFirstAlertDate()) return false;
      if (hasLRFirstAlertDate()) {
        if (getLRFirstAlertDate()
            != other.getLRFirstAlertDate()) return false;
      }
      if (hasLRLastAlertDate() != other.hasLRLastAlertDate()) return false;
      if (hasLRLastAlertDate()) {
        if (getLRLastAlertDate()
            != other.getLRLastAlertDate()) return false;
      }
      if (hasLRAlertTimes() != other.hasLRAlertTimes()) return false;
      if (hasLRAlertTimes()) {
        if (getLRAlertTimes()
            != other.getLRAlertTimes()) return false;
      }
      if (hasDetectType() != other.hasDetectType()) return false;
      if (hasDetectType()) {
        if (getDetectType()
            != other.getDetectType()) return false;
      }
      if (hasThreatType() != other.hasThreatType()) return false;
      if (hasThreatType()) {
        if (getThreatType()
            != other.getThreatType()) return false;
      }
      if (hasSeverity() != other.hasSeverity()) return false;
      if (hasSeverity()) {
        if (getSeverity()
            != other.getSeverity()) return false;
      }
      if (hasKillChain() != other.hasKillChain()) return false;
      if (hasKillChain()) {
        if (!getKillChain()
            .equals(other.getKillChain())) return false;
      }
      if (hasTactic() != other.hasTactic()) return false;
      if (hasTactic()) {
        if (!getTactic()
            .equals(other.getTactic())) return false;
      }
      if (hasTechnique() != other.hasTechnique()) return false;
      if (hasTechnique()) {
        if (!getTechnique()
            .equals(other.getTechnique())) return false;
      }
      if (hasConfidence() != other.hasConfidence()) return false;
      if (hasConfidence()) {
        if (!getConfidence()
            .equals(other.getConfidence())) return false;
      }
      if (hasTranProto() != other.hasTranProto()) return false;
      if (hasTranProto()) {
        if (!getTranProto()
            .equals(other.getTranProto())) return false;
      }
      if (hasAppProto() != other.hasAppProto()) return false;
      if (hasAppProto()) {
        if (!getAppProto()
            .equals(other.getAppProto())) return false;
      }
      if (hasMetaData() != other.hasMetaData()) return false;
      if (hasMetaData()) {
        if (!getMetaData()
            .equals(other.getMetaData())) return false;
      }
      if (hasRawData() != other.hasRawData()) return false;
      if (hasRawData()) {
        if (!getRawData()
            .equals(other.getRawData())) return false;
      }
      if (hasIocAlertInfo() != other.hasIocAlertInfo()) return false;
      if (hasIocAlertInfo()) {
        if (!getIocAlertInfo()
            .equals(other.getIocAlertInfo())) return false;
      }
      if (hasIobAlertInfo() != other.hasIobAlertInfo()) return false;
      if (hasIobAlertInfo()) {
        if (!getIobAlertInfo()
            .equals(other.getIobAlertInfo())) return false;
      }
      if (hasIoaAlertInfo() != other.hasIoaAlertInfo()) return false;
      if (hasIoaAlertInfo()) {
        if (!getIoaAlertInfo()
            .equals(other.getIoaAlertInfo())) return false;
      }
      if (hasIiotAlertInfo() != other.hasIiotAlertInfo()) return false;
      if (hasIiotAlertInfo()) {
        if (!getIiotAlertInfo()
            .equals(other.getIiotAlertInfo())) return false;
      }
      if (hasFileAlertInfo() != other.hasFileAlertInfo()) return false;
      if (hasFileAlertInfo()) {
        if (!getFileAlertInfo()
            .equals(other.getFileAlertInfo())) return false;
      }
      if (hasCryptoAlertInfo() != other.hasCryptoAlertInfo()) return false;
      if (hasCryptoAlertInfo()) {
        if (!getCryptoAlertInfo()
            .equals(other.getCryptoAlertInfo())) return false;
      }
      if (hasCertAlertInfo() != other.hasCertAlertInfo()) return false;
      if (hasCertAlertInfo()) {
        if (!getCertAlertInfo()
            .equals(other.getCertAlertInfo())) return false;
      }
      if (hasMailAlertInfo() != other.hasMailAlertInfo()) return false;
      if (hasMailAlertInfo()) {
        if (!getMailAlertInfo()
            .equals(other.getMailAlertInfo())) return false;
      }
      if (hasMobileAlertInfo() != other.hasMobileAlertInfo()) return false;
      if (hasMobileAlertInfo()) {
        if (!getMobileAlertInfo()
            .equals(other.getMobileAlertInfo())) return false;
      }
      if (hasProtoAlertInfo() != other.hasProtoAlertInfo()) return false;
      if (hasProtoAlertInfo()) {
        if (!getProtoAlertInfo()
            .equals(other.getProtoAlertInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasGuid()) {
        hash = (37 * hash) + GUID_FIELD_NUMBER;
        hash = (53 * hash) + getGuid().hashCode();
      }
      if (hasTime()) {
        hash = (37 * hash) + TIME_FIELD_NUMBER;
        hash = (53 * hash) + getTime().hashCode();
      }
      if (hasLineInfo()) {
        hash = (37 * hash) + LINE_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getLineInfo().hashCode();
      }
      if (hasSip()) {
        hash = (37 * hash) + SIP_FIELD_NUMBER;
        hash = (53 * hash) + getSip().hashCode();
      }
      if (hasDip()) {
        hash = (37 * hash) + DIP_FIELD_NUMBER;
        hash = (53 * hash) + getDip().hashCode();
      }
      if (hasAip()) {
        hash = (37 * hash) + AIP_FIELD_NUMBER;
        hash = (53 * hash) + getAip().hashCode();
      }
      if (hasVip()) {
        hash = (37 * hash) + VIP_FIELD_NUMBER;
        hash = (53 * hash) + getVip().hashCode();
      }
      if (hasSensorIp()) {
        hash = (37 * hash) + SENSOR_IP_FIELD_NUMBER;
        hash = (53 * hash) + getSensorIp().hashCode();
      }
      if (hasVendorId()) {
        hash = (37 * hash) + VENDOR_ID_FIELD_NUMBER;
        hash = (53 * hash) + getVendorId().hashCode();
      }
      if (hasLRAggregateValue()) {
        hash = (37 * hash) + LR_AGGREGATE_VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getLRAggregateValue().hashCode();
      }
      if (hasLRFirstAlertDate()) {
        hash = (37 * hash) + LR_FIRST_ALERT_DATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLRFirstAlertDate());
      }
      if (hasLRLastAlertDate()) {
        hash = (37 * hash) + LR_LAST_ALERT_DATE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getLRLastAlertDate());
      }
      if (hasLRAlertTimes()) {
        hash = (37 * hash) + LR_ALERT_TIMES_FIELD_NUMBER;
        hash = (53 * hash) + getLRAlertTimes();
      }
      if (hasDetectType()) {
        hash = (37 * hash) + DETECT_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getDetectType();
      }
      if (hasThreatType()) {
        hash = (37 * hash) + THREAT_TYPE_FIELD_NUMBER;
        hash = (53 * hash) + getThreatType();
      }
      if (hasSeverity()) {
        hash = (37 * hash) + SEVERITY_FIELD_NUMBER;
        hash = (53 * hash) + getSeverity();
      }
      if (hasKillChain()) {
        hash = (37 * hash) + KILL_CHAIN_FIELD_NUMBER;
        hash = (53 * hash) + getKillChain().hashCode();
      }
      if (hasTactic()) {
        hash = (37 * hash) + TACTIC_FIELD_NUMBER;
        hash = (53 * hash) + getTactic().hashCode();
      }
      if (hasTechnique()) {
        hash = (37 * hash) + TECHNIQUE_FIELD_NUMBER;
        hash = (53 * hash) + getTechnique().hashCode();
      }
      if (hasConfidence()) {
        hash = (37 * hash) + CONFIDENCE_FIELD_NUMBER;
        hash = (53 * hash) + getConfidence().hashCode();
      }
      if (hasTranProto()) {
        hash = (37 * hash) + TRAN_PROTO_FIELD_NUMBER;
        hash = (53 * hash) + getTranProto().hashCode();
      }
      if (hasAppProto()) {
        hash = (37 * hash) + APP_PROTO_FIELD_NUMBER;
        hash = (53 * hash) + getAppProto().hashCode();
      }
      if (hasMetaData()) {
        hash = (37 * hash) + META_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getMetaData().hashCode();
      }
      if (hasRawData()) {
        hash = (37 * hash) + RAW_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getRawData().hashCode();
      }
      if (hasIocAlertInfo()) {
        hash = (37 * hash) + IOC_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIocAlertInfo().hashCode();
      }
      if (hasIobAlertInfo()) {
        hash = (37 * hash) + IOB_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIobAlertInfo().hashCode();
      }
      if (hasIoaAlertInfo()) {
        hash = (37 * hash) + IOA_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIoaAlertInfo().hashCode();
      }
      if (hasIiotAlertInfo()) {
        hash = (37 * hash) + IIOT_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getIiotAlertInfo().hashCode();
      }
      if (hasFileAlertInfo()) {
        hash = (37 * hash) + FILE_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getFileAlertInfo().hashCode();
      }
      if (hasCryptoAlertInfo()) {
        hash = (37 * hash) + CRYPTO_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getCryptoAlertInfo().hashCode();
      }
      if (hasCertAlertInfo()) {
        hash = (37 * hash) + CERT_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getCertAlertInfo().hashCode();
      }
      if (hasMailAlertInfo()) {
        hash = (37 * hash) + MAIL_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getMailAlertInfo().hashCode();
      }
      if (hasMobileAlertInfo()) {
        hash = (37 * hash) + MOBILE_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getMobileAlertInfo().hashCode();
      }
      if (hasProtoAlertInfo()) {
        hash = (37 * hash) + PROTO_ALERT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getProtoAlertInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ALERT_LOG parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ALERT_LOG parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ALERT_LOG parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ALERT_LOG parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ALERT_LOG parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ALERT_LOG parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ALERT_LOG parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static ALERT_LOG parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static ALERT_LOG parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static ALERT_LOG parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ALERT_LOG parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static ALERT_LOG parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ALERT_LOG prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code ALERT_LOG}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:ALERT_LOG)
        ALERT_LOGOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return AlertLog.internal_static_ALERT_LOG_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return AlertLog.internal_static_ALERT_LOG_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ALERT_LOG.class, Builder.class);
      }

      // Construct using AlertLog.ALERT_LOG.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getSipFieldBuilder();
          getDipFieldBuilder();
          getAipFieldBuilder();
          getVipFieldBuilder();
          getIocAlertInfoFieldBuilder();
          getIobAlertInfoFieldBuilder();
          getIoaAlertInfoFieldBuilder();
          getIiotAlertInfoFieldBuilder();
          getFileAlertInfoFieldBuilder();
          getCryptoAlertInfoFieldBuilder();
          getCertAlertInfoFieldBuilder();
          getMailAlertInfoFieldBuilder();
          getMobileAlertInfoFieldBuilder();
          getProtoAlertInfoFieldBuilder();
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        bitField1_ = 0;
        guid_ = "";
        time_ = "";
        lineInfo_ = "";
        sip_ = null;
        if (sipBuilder_ != null) {
          sipBuilder_.dispose();
          sipBuilder_ = null;
        }
        dip_ = null;
        if (dipBuilder_ != null) {
          dipBuilder_.dispose();
          dipBuilder_ = null;
        }
        aip_ = null;
        if (aipBuilder_ != null) {
          aipBuilder_.dispose();
          aipBuilder_ = null;
        }
        vip_ = null;
        if (vipBuilder_ != null) {
          vipBuilder_.dispose();
          vipBuilder_ = null;
        }
        sensorIp_ = "";
        vendorId_ = "";
        lRAggregateValue_ = "";
        lRFirstAlertDate_ = 0L;
        lRLastAlertDate_ = 0L;
        lRAlertTimes_ = 0;
        detectType_ = 0;
        threatType_ = 0;
        severity_ = 0;
        killChain_ = "";
        tactic_ = "";
        technique_ = "";
        confidence_ = "";
        tranProto_ = "";
        appProto_ = "";
        metaData_ = com.google.protobuf.ByteString.EMPTY;
        rawData_ = "";
        iocAlertInfo_ = null;
        if (iocAlertInfoBuilder_ != null) {
          iocAlertInfoBuilder_.dispose();
          iocAlertInfoBuilder_ = null;
        }
        iobAlertInfo_ = null;
        if (iobAlertInfoBuilder_ != null) {
          iobAlertInfoBuilder_.dispose();
          iobAlertInfoBuilder_ = null;
        }
        ioaAlertInfo_ = null;
        if (ioaAlertInfoBuilder_ != null) {
          ioaAlertInfoBuilder_.dispose();
          ioaAlertInfoBuilder_ = null;
        }
        iiotAlertInfo_ = null;
        if (iiotAlertInfoBuilder_ != null) {
          iiotAlertInfoBuilder_.dispose();
          iiotAlertInfoBuilder_ = null;
        }
        fileAlertInfo_ = null;
        if (fileAlertInfoBuilder_ != null) {
          fileAlertInfoBuilder_.dispose();
          fileAlertInfoBuilder_ = null;
        }
        cryptoAlertInfo_ = null;
        if (cryptoAlertInfoBuilder_ != null) {
          cryptoAlertInfoBuilder_.dispose();
          cryptoAlertInfoBuilder_ = null;
        }
        certAlertInfo_ = null;
        if (certAlertInfoBuilder_ != null) {
          certAlertInfoBuilder_.dispose();
          certAlertInfoBuilder_ = null;
        }
        mailAlertInfo_ = null;
        if (mailAlertInfoBuilder_ != null) {
          mailAlertInfoBuilder_.dispose();
          mailAlertInfoBuilder_ = null;
        }
        mobileAlertInfo_ = null;
        if (mobileAlertInfoBuilder_ != null) {
          mobileAlertInfoBuilder_.dispose();
          mobileAlertInfoBuilder_ = null;
        }
        protoAlertInfo_ = null;
        if (protoAlertInfoBuilder_ != null) {
          protoAlertInfoBuilder_.dispose();
          protoAlertInfoBuilder_ = null;
        }
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return AlertLog.internal_static_ALERT_LOG_descriptor;
      }

      @Override
      public ALERT_LOG getDefaultInstanceForType() {
        return ALERT_LOG.getDefaultInstance();
      }

      @Override
      public ALERT_LOG build() {
        ALERT_LOG result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ALERT_LOG buildPartial() {
        ALERT_LOG result = new ALERT_LOG(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        if (bitField1_ != 0) { buildPartial1(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(ALERT_LOG result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.guid_ = guid_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.time_ = time_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.lineInfo_ = lineInfo_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.sip_ = sipBuilder_ == null
              ? sip_
              : sipBuilder_.build();
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.dip_ = dipBuilder_ == null
              ? dip_
              : dipBuilder_.build();
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.aip_ = aipBuilder_ == null
              ? aip_
              : aipBuilder_.build();
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.vip_ = vipBuilder_ == null
              ? vip_
              : vipBuilder_.build();
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.sensorIp_ = sensorIp_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.vendorId_ = vendorId_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.lRAggregateValue_ = lRAggregateValue_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.lRFirstAlertDate_ = lRFirstAlertDate_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.lRLastAlertDate_ = lRLastAlertDate_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.lRAlertTimes_ = lRAlertTimes_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.detectType_ = detectType_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.threatType_ = threatType_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.severity_ = severity_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.killChain_ = killChain_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.tactic_ = tactic_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.technique_ = technique_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.confidence_ = confidence_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.tranProto_ = tranProto_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.appProto_ = appProto_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.metaData_ = metaData_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.rawData_ = rawData_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.iocAlertInfo_ = iocAlertInfoBuilder_ == null
              ? iocAlertInfo_
              : iocAlertInfoBuilder_.build();
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          result.iobAlertInfo_ = iobAlertInfoBuilder_ == null
              ? iobAlertInfo_
              : iobAlertInfoBuilder_.build();
          to_bitField0_ |= 0x02000000;
        }
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.ioaAlertInfo_ = ioaAlertInfoBuilder_ == null
              ? ioaAlertInfo_
              : ioaAlertInfoBuilder_.build();
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.iiotAlertInfo_ = iiotAlertInfoBuilder_ == null
              ? iiotAlertInfo_
              : iiotAlertInfoBuilder_.build();
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.fileAlertInfo_ = fileAlertInfoBuilder_ == null
              ? fileAlertInfo_
              : fileAlertInfoBuilder_.build();
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.cryptoAlertInfo_ = cryptoAlertInfoBuilder_ == null
              ? cryptoAlertInfo_
              : cryptoAlertInfoBuilder_.build();
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.certAlertInfo_ = certAlertInfoBuilder_ == null
              ? certAlertInfo_
              : certAlertInfoBuilder_.build();
          to_bitField0_ |= 0x40000000;
        }
        if (((from_bitField0_ & 0x80000000) != 0)) {
          result.mailAlertInfo_ = mailAlertInfoBuilder_ == null
              ? mailAlertInfo_
              : mailAlertInfoBuilder_.build();
          to_bitField0_ |= 0x80000000;
        }
        result.bitField0_ |= to_bitField0_;
      }

      private void buildPartial1(ALERT_LOG result) {
        int from_bitField1_ = bitField1_;
        int to_bitField1_ = 0;
        if (((from_bitField1_ & 0x00000001) != 0)) {
          result.mobileAlertInfo_ = mobileAlertInfoBuilder_ == null
              ? mobileAlertInfo_
              : mobileAlertInfoBuilder_.build();
          to_bitField1_ |= 0x00000001;
        }
        if (((from_bitField1_ & 0x00000002) != 0)) {
          result.protoAlertInfo_ = protoAlertInfoBuilder_ == null
              ? protoAlertInfo_
              : protoAlertInfoBuilder_.build();
          to_bitField1_ |= 0x00000002;
        }
        result.bitField1_ |= to_bitField1_;
      }

      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ALERT_LOG) {
          return mergeFrom((ALERT_LOG)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ALERT_LOG other) {
        if (other == ALERT_LOG.getDefaultInstance()) return this;
        if (other.hasGuid()) {
          guid_ = other.guid_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasTime()) {
          time_ = other.time_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (other.hasLineInfo()) {
          lineInfo_ = other.lineInfo_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasSip()) {
          mergeSip(other.getSip());
        }
        if (other.hasDip()) {
          mergeDip(other.getDip());
        }
        if (other.hasAip()) {
          mergeAip(other.getAip());
        }
        if (other.hasVip()) {
          mergeVip(other.getVip());
        }
        if (other.hasSensorIp()) {
          sensorIp_ = other.sensorIp_;
          bitField0_ |= 0x00000080;
          onChanged();
        }
        if (other.hasVendorId()) {
          vendorId_ = other.vendorId_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (other.hasLRAggregateValue()) {
          lRAggregateValue_ = other.lRAggregateValue_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (other.hasLRFirstAlertDate()) {
          setLRFirstAlertDate(other.getLRFirstAlertDate());
        }
        if (other.hasLRLastAlertDate()) {
          setLRLastAlertDate(other.getLRLastAlertDate());
        }
        if (other.hasLRAlertTimes()) {
          setLRAlertTimes(other.getLRAlertTimes());
        }
        if (other.hasDetectType()) {
          setDetectType(other.getDetectType());
        }
        if (other.hasThreatType()) {
          setThreatType(other.getThreatType());
        }
        if (other.hasSeverity()) {
          setSeverity(other.getSeverity());
        }
        if (other.hasKillChain()) {
          killChain_ = other.killChain_;
          bitField0_ |= 0x00010000;
          onChanged();
        }
        if (other.hasTactic()) {
          tactic_ = other.tactic_;
          bitField0_ |= 0x00020000;
          onChanged();
        }
        if (other.hasTechnique()) {
          technique_ = other.technique_;
          bitField0_ |= 0x00040000;
          onChanged();
        }
        if (other.hasConfidence()) {
          confidence_ = other.confidence_;
          bitField0_ |= 0x00080000;
          onChanged();
        }
        if (other.hasTranProto()) {
          tranProto_ = other.tranProto_;
          bitField0_ |= 0x00100000;
          onChanged();
        }
        if (other.hasAppProto()) {
          appProto_ = other.appProto_;
          bitField0_ |= 0x00200000;
          onChanged();
        }
        if (other.hasMetaData()) {
          setMetaData(other.getMetaData());
        }
        if (other.hasRawData()) {
          rawData_ = other.rawData_;
          bitField0_ |= 0x00800000;
          onChanged();
        }
        if (other.hasIocAlertInfo()) {
          mergeIocAlertInfo(other.getIocAlertInfo());
        }
        if (other.hasIobAlertInfo()) {
          mergeIobAlertInfo(other.getIobAlertInfo());
        }
        if (other.hasIoaAlertInfo()) {
          mergeIoaAlertInfo(other.getIoaAlertInfo());
        }
        if (other.hasIiotAlertInfo()) {
          mergeIiotAlertInfo(other.getIiotAlertInfo());
        }
        if (other.hasFileAlertInfo()) {
          mergeFileAlertInfo(other.getFileAlertInfo());
        }
        if (other.hasCryptoAlertInfo()) {
          mergeCryptoAlertInfo(other.getCryptoAlertInfo());
        }
        if (other.hasCertAlertInfo()) {
          mergeCertAlertInfo(other.getCertAlertInfo());
        }
        if (other.hasMailAlertInfo()) {
          mergeMailAlertInfo(other.getMailAlertInfo());
        }
        if (other.hasMobileAlertInfo()) {
          mergeMobileAlertInfo(other.getMobileAlertInfo());
        }
        if (other.hasProtoAlertInfo()) {
          mergeProtoAlertInfo(other.getProtoAlertInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        if (!hasGuid()) {
          return false;
        }
        if (!hasTime()) {
          return false;
        }
        if (!hasLineInfo()) {
          return false;
        }
        if (!hasSip()) {
          return false;
        }
        if (!hasDip()) {
          return false;
        }
        if (!hasAip()) {
          return false;
        }
        if (!hasVip()) {
          return false;
        }
        if (!hasSensorIp()) {
          return false;
        }
        if (!hasVendorId()) {
          return false;
        }
        if (!hasLRAggregateValue()) {
          return false;
        }
        if (!hasLRFirstAlertDate()) {
          return false;
        }
        if (!hasLRLastAlertDate()) {
          return false;
        }
        if (!hasLRAlertTimes()) {
          return false;
        }
        if (!hasDetectType()) {
          return false;
        }
        if (!hasThreatType()) {
          return false;
        }
        if (!hasSeverity()) {
          return false;
        }
        if (!hasKillChain()) {
          return false;
        }
        if (!hasConfidence()) {
          return false;
        }
        if (!hasTranProto()) {
          return false;
        }
        if (!getSip().isInitialized()) {
          return false;
        }
        if (!getDip().isInitialized()) {
          return false;
        }
        if (!getAip().isInitialized()) {
          return false;
        }
        if (!getVip().isInitialized()) {
          return false;
        }
        if (hasIocAlertInfo()) {
          if (!getIocAlertInfo().isInitialized()) {
            return false;
          }
        }
        if (hasIoaAlertInfo()) {
          if (!getIoaAlertInfo().isInitialized()) {
            return false;
          }
        }
        if (hasIiotAlertInfo()) {
          if (!getIiotAlertInfo().isInitialized()) {
            return false;
          }
        }
        if (hasFileAlertInfo()) {
          if (!getFileAlertInfo().isInitialized()) {
            return false;
          }
        }
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                guid_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                time_ = input.readBytes();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                lineInfo_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                input.readMessage(
                    getSipFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getDipFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                input.readMessage(
                    getAipFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 58: {
                input.readMessage(
                    getVipFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 66: {
                sensorIp_ = input.readBytes();
                bitField0_ |= 0x00000080;
                break;
              } // case 66
              case 74: {
                vendorId_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                lRAggregateValue_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 88: {
                lRFirstAlertDate_ = input.readUInt64();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              case 96: {
                lRLastAlertDate_ = input.readUInt64();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              case 104: {
                lRAlertTimes_ = input.readUInt32();
                bitField0_ |= 0x00001000;
                break;
              } // case 104
              case 112: {
                detectType_ = input.readUInt32();
                bitField0_ |= 0x00002000;
                break;
              } // case 112
              case 120: {
                threatType_ = input.readUInt32();
                bitField0_ |= 0x00004000;
                break;
              } // case 120
              case 128: {
                severity_ = input.readUInt32();
                bitField0_ |= 0x00008000;
                break;
              } // case 128
              case 138: {
                killChain_ = input.readBytes();
                bitField0_ |= 0x00010000;
                break;
              } // case 138
              case 146: {
                tactic_ = input.readBytes();
                bitField0_ |= 0x00020000;
                break;
              } // case 146
              case 154: {
                technique_ = input.readBytes();
                bitField0_ |= 0x00040000;
                break;
              } // case 154
              case 162: {
                confidence_ = input.readBytes();
                bitField0_ |= 0x00080000;
                break;
              } // case 162
              case 170: {
                tranProto_ = input.readBytes();
                bitField0_ |= 0x00100000;
                break;
              } // case 170
              case 178: {
                appProto_ = input.readBytes();
                bitField0_ |= 0x00200000;
                break;
              } // case 178
              case 186: {
                metaData_ = input.readBytes();
                bitField0_ |= 0x00400000;
                break;
              } // case 186
              case 194: {
                rawData_ = input.readBytes();
                bitField0_ |= 0x00800000;
                break;
              } // case 194
              case 802: {
                input.readMessage(
                    getIocAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x01000000;
                break;
              } // case 802
              case 810: {
                input.readMessage(
                    getIobAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x02000000;
                break;
              } // case 810
              case 818: {
                input.readMessage(
                    getIoaAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x04000000;
                break;
              } // case 818
              case 826: {
                input.readMessage(
                    getIiotAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x08000000;
                break;
              } // case 826
              case 834: {
                input.readMessage(
                    getFileAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x10000000;
                break;
              } // case 834
              case 842: {
                input.readMessage(
                    getCryptoAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x20000000;
                break;
              } // case 842
              case 850: {
                input.readMessage(
                    getCertAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x40000000;
                break;
              } // case 850
              case 858: {
                input.readMessage(
                    getMailAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x80000000;
                break;
              } // case 858
              case 866: {
                input.readMessage(
                    getMobileAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField1_ |= 0x00000001;
                break;
              } // case 866
              case 874: {
                input.readMessage(
                    getProtoAlertInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField1_ |= 0x00000002;
                break;
              } // case 874
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;
      private int bitField1_;

      private Object guid_ = "";
      /**
       * <pre>
       * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       * @return Whether the guid field is set.
       */
      public boolean hasGuid() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       * @return The guid.
       */
      public String getGuid() {
        Object ref = guid_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            guid_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       * @return The bytes for guid.
       */
      public com.google.protobuf.ByteString
          getGuidBytes() {
        Object ref = guid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          guid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       * @param value The guid to set.
       * @return This builder for chaining.
       */
      public Builder setGuid(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        guid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGuid() {
        guid_ = getDefaultInstance().getGuid();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日志全局ID	唯一 (设备IP+时间戳 SHA-256)
       * </pre>
       *
       * <code>required string guid = 1;</code>
       * @param value The bytes for guid to set.
       * @return This builder for chaining.
       */
      public Builder setGuidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        guid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private Object time_ = "";
      /**
       * <pre>
       * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       * @return Whether the time field is set.
       */
      public boolean hasTime() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       * @return The time.
       */
      public String getTime() {
        Object ref = time_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            time_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       * @return The bytes for time.
       */
      public com.google.protobuf.ByteString
          getTimeBytes() {
        Object ref = time_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          time_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        time_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        time_ = getDefaultInstance().getTime();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 告警时间	毫秒级,yyyy-mm-dd hh:mm:ss.ms
       * </pre>
       *
       * <code>required string time = 2;</code>
       * @param value The bytes for time to set.
       * @return This builder for chaining.
       */
      public Builder setTimeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        time_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private Object lineInfo_ = "";
      /**
       * <pre>
       * 线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       * @return Whether the lineInfo field is set.
       */
      public boolean hasLineInfo() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       * @return The lineInfo.
       */
      public String getLineInfo() {
        Object ref = lineInfo_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            lineInfo_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       * @return The bytes for lineInfo.
       */
      public com.google.protobuf.ByteString
          getLineInfoBytes() {
        Object ref = lineInfo_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          lineInfo_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       * @param value The lineInfo to set.
       * @return This builder for chaining.
       */
      public Builder setLineInfo(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        lineInfo_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineInfo() {
        lineInfo_ = getDefaultInstance().getLineInfo();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 线路号	51字节头
       * </pre>
       *
       * <code>required string line_info = 3;</code>
       * @param value The bytes for lineInfo to set.
       * @return This builder for chaining.
       */
      public Builder setLineInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        lineInfo_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private IpInfo.IP_INFO sip_;
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> sipBuilder_;
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       * @return Whether the sip field is set.
       */
      public boolean hasSip() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       * @return The sip.
       */
      public IpInfo.IP_INFO getSip() {
        if (sipBuilder_ == null) {
          return sip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : sip_;
        } else {
          return sipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder setSip(IpInfo.IP_INFO value) {
        if (sipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sip_ = value;
        } else {
          sipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder setSip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (sipBuilder_ == null) {
          sip_ = builderForValue.build();
        } else {
          sipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder mergeSip(IpInfo.IP_INFO value) {
        if (sipBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            sip_ != null &&
            sip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            getSipBuilder().mergeFrom(value);
          } else {
            sip_ = value;
          }
        } else {
          sipBuilder_.mergeFrom(value);
        }
        if (sip_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public Builder clearSip() {
        bitField0_ = (bitField0_ & ~0x00000008);
        sip_ = null;
        if (sipBuilder_ != null) {
          sipBuilder_.dispose();
          sipBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public IpInfo.IP_INFO.Builder getSipBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getSipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      public IpInfo.IP_INFOOrBuilder getSipOrBuilder() {
        if (sipBuilder_ != null) {
          return sipBuilder_.getMessageOrBuilder();
        } else {
          return sip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : sip_;
        }
      }
      /**
       * <pre>
       * 源IP信息
       * </pre>
       *
       * <code>required .IP_INFO sip = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getSipFieldBuilder() {
        if (sipBuilder_ == null) {
          sipBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getSip(),
                  getParentForChildren(),
                  isClean());
          sip_ = null;
        }
        return sipBuilder_;
      }

      private IpInfo.IP_INFO dip_;
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> dipBuilder_;
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       * @return Whether the dip field is set.
       */
      public boolean hasDip() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       * @return The dip.
       */
      public IpInfo.IP_INFO getDip() {
        if (dipBuilder_ == null) {
          return dip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : dip_;
        } else {
          return dipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder setDip(IpInfo.IP_INFO value) {
        if (dipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dip_ = value;
        } else {
          dipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder setDip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (dipBuilder_ == null) {
          dip_ = builderForValue.build();
        } else {
          dipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder mergeDip(IpInfo.IP_INFO value) {
        if (dipBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            dip_ != null &&
            dip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            getDipBuilder().mergeFrom(value);
          } else {
            dip_ = value;
          }
        } else {
          dipBuilder_.mergeFrom(value);
        }
        if (dip_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public Builder clearDip() {
        bitField0_ = (bitField0_ & ~0x00000010);
        dip_ = null;
        if (dipBuilder_ != null) {
          dipBuilder_.dispose();
          dipBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public IpInfo.IP_INFO.Builder getDipBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getDipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      public IpInfo.IP_INFOOrBuilder getDipOrBuilder() {
        if (dipBuilder_ != null) {
          return dipBuilder_.getMessageOrBuilder();
        } else {
          return dip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : dip_;
        }
      }
      /**
       * <pre>
       * 目的IP信息;
       * </pre>
       *
       * <code>required .IP_INFO dip = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getDipFieldBuilder() {
        if (dipBuilder_ == null) {
          dipBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getDip(),
                  getParentForChildren(),
                  isClean());
          dip_ = null;
        }
        return dipBuilder_;
      }

      private IpInfo.IP_INFO aip_;
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> aipBuilder_;
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       * @return Whether the aip field is set.
       */
      public boolean hasAip() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       * @return The aip.
       */
      public IpInfo.IP_INFO getAip() {
        if (aipBuilder_ == null) {
          return aip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : aip_;
        } else {
          return aipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder setAip(IpInfo.IP_INFO value) {
        if (aipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          aip_ = value;
        } else {
          aipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder setAip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (aipBuilder_ == null) {
          aip_ = builderForValue.build();
        } else {
          aipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder mergeAip(IpInfo.IP_INFO value) {
        if (aipBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
            aip_ != null &&
            aip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            getAipBuilder().mergeFrom(value);
          } else {
            aip_ = value;
          }
        } else {
          aipBuilder_.mergeFrom(value);
        }
        if (aip_ != null) {
          bitField0_ |= 0x00000020;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public Builder clearAip() {
        bitField0_ = (bitField0_ & ~0x00000020);
        aip_ = null;
        if (aipBuilder_ != null) {
          aipBuilder_.dispose();
          aipBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public IpInfo.IP_INFO.Builder getAipBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getAipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      public IpInfo.IP_INFOOrBuilder getAipOrBuilder() {
        if (aipBuilder_ != null) {
          return aipBuilder_.getMessageOrBuilder();
        } else {
          return aip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : aip_;
        }
      }
      /**
       * <pre>
       * 受害者IP信息;
       * </pre>
       *
       * <code>required .IP_INFO aip = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getAipFieldBuilder() {
        if (aipBuilder_ == null) {
          aipBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getAip(),
                  getParentForChildren(),
                  isClean());
          aip_ = null;
        }
        return aipBuilder_;
      }

      private IpInfo.IP_INFO vip_;
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> vipBuilder_;
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       * @return Whether the vip field is set.
       */
      public boolean hasVip() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       * @return The vip.
       */
      public IpInfo.IP_INFO getVip() {
        if (vipBuilder_ == null) {
          return vip_ == null ? IpInfo.IP_INFO.getDefaultInstance() : vip_;
        } else {
          return vipBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder setVip(IpInfo.IP_INFO value) {
        if (vipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          vip_ = value;
        } else {
          vipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder setVip(
          IpInfo.IP_INFO.Builder builderForValue) {
        if (vipBuilder_ == null) {
          vip_ = builderForValue.build();
        } else {
          vipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder mergeVip(IpInfo.IP_INFO value) {
        if (vipBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
            vip_ != null &&
            vip_ != IpInfo.IP_INFO.getDefaultInstance()) {
            getVipBuilder().mergeFrom(value);
          } else {
            vip_ = value;
          }
        } else {
          vipBuilder_.mergeFrom(value);
        }
        if (vip_ != null) {
          bitField0_ |= 0x00000040;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public Builder clearVip() {
        bitField0_ = (bitField0_ & ~0x00000040);
        vip_ = null;
        if (vipBuilder_ != null) {
          vipBuilder_.dispose();
          vipBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public IpInfo.IP_INFO.Builder getVipBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getVipFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      public IpInfo.IP_INFOOrBuilder getVipOrBuilder() {
        if (vipBuilder_ != null) {
          return vipBuilder_.getMessageOrBuilder();
        } else {
          return vip_ == null ?
              IpInfo.IP_INFO.getDefaultInstance() : vip_;
        }
      }
      /**
       * <pre>
       * 攻击IP信息;
       * </pre>
       *
       * <code>required .IP_INFO vip = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder> 
          getVipFieldBuilder() {
        if (vipBuilder_ == null) {
          vipBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IpInfo.IP_INFO, IpInfo.IP_INFO.Builder, IpInfo.IP_INFOOrBuilder>(
                  getVip(),
                  getParentForChildren(),
                  isClean());
          vip_ = null;
        }
        return vipBuilder_;
      }

      private Object sensorIp_ = "";
      /**
       * <pre>
       * 传感器IP
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       * @return Whether the sensorIp field is set.
       */
      public boolean hasSensorIp() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 传感器IP
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       * @return The sensorIp.
       */
      public String getSensorIp() {
        Object ref = sensorIp_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sensorIp_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 传感器IP
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       * @return The bytes for sensorIp.
       */
      public com.google.protobuf.ByteString
          getSensorIpBytes() {
        Object ref = sensorIp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          sensorIp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 传感器IP
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       * @param value The sensorIp to set.
       * @return This builder for chaining.
       */
      public Builder setSensorIp(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        sensorIp_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 传感器IP
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearSensorIp() {
        sensorIp_ = getDefaultInstance().getSensorIp();
        bitField0_ = (bitField0_ & ~0x00000080);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 传感器IP
       * </pre>
       *
       * <code>required string sensor_ip = 8;</code>
       * @param value The bytes for sensorIp to set.
       * @return This builder for chaining.
       */
      public Builder setSensorIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        sensorIp_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }

      private Object vendorId_ = "";
      /**
       * <pre>
       * 供应商ID
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       * @return Whether the vendorId field is set.
       */
      public boolean hasVendorId() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 供应商ID
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       * @return The vendorId.
       */
      public String getVendorId() {
        Object ref = vendorId_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            vendorId_ = s;
          }
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <pre>
       * 供应商ID
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       * @return The bytes for vendorId.
       */
      public com.google.protobuf.ByteString
          getVendorIdBytes() {
        Object ref = vendorId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          vendorId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 供应商ID
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       * @param value The vendorId to set.
       * @return This builder for chaining.
       */
      public Builder setVendorId(
          String value) {
        if (value == null) { throw new NullPointerException(); }
        vendorId_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 供应商ID
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearVendorId() {
        vendorId_ = getDefaultInstance().getVendorId();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 供应商ID
       * </pre>
       *
       * <code>required string vendor_id = 9;</code>
       * @param value The bytes for vendorId to set.
       * @return This builder for chaining.
       */
      public Builder setVendorIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        vendorId_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private Object lRAggregateValue_ = "";
      /**
       * <pre>
       * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       * @return Whether the lRAggregateValue field is set.
       */
      public boolean hasLRAggregateValue() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       * @return The lRAggregateValue.
       */
      public String getLRAggregateValue() {
        Object ref = lRAggregateValue_;
        if (!(ref instanceof String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            lRAggregateValue_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       * @return The bytes for lRAggregateValue.
       */
      public com.google.protobuf.ByteString
          getLRAggregateValueBytes() {
        java.lang.Object ref = lRAggregateValue_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lRAggregateValue_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       * @param value The lRAggregateValue to set.
       * @return This builder for chaining.
       */
      public Builder setLRAggregateValue(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        lRAggregateValue_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearLRAggregateValue() {
        lRAggregateValue_ = getDefaultInstance().getLRAggregateValue();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最近短时聚合值	根据不同的威胁类型采用不同的聚合策略
       * </pre>
       *
       * <code>required string LR_aggregate_value = 10;</code>
       * @param value The bytes for lRAggregateValue to set.
       * @return This builder for chaining.
       */
      public Builder setLRAggregateValueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        lRAggregateValue_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private long lRFirstAlertDate_ ;
      /**
       * <pre>
       * 最近短时首次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       * @return Whether the lRFirstAlertDate field is set.
       */
      @java.lang.Override
      public boolean hasLRFirstAlertDate() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 最近短时首次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       * @return The lRFirstAlertDate.
       */
      @java.lang.Override
      public long getLRFirstAlertDate() {
        return lRFirstAlertDate_;
      }
      /**
       * <pre>
       * 最近短时首次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       * @param value The lRFirstAlertDate to set.
       * @return This builder for chaining.
       */
      public Builder setLRFirstAlertDate(long value) {

        lRFirstAlertDate_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最近短时首次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_first_alert_date = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearLRFirstAlertDate() {
        bitField0_ = (bitField0_ & ~0x00000400);
        lRFirstAlertDate_ = 0L;
        onChanged();
        return this;
      }

      private long lRLastAlertDate_ ;
      /**
       * <pre>
       * 最近短时末次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       * @return Whether the lRLastAlertDate field is set.
       */
      @java.lang.Override
      public boolean hasLRLastAlertDate() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 最近短时末次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       * @return The lRLastAlertDate.
       */
      @java.lang.Override
      public long getLRLastAlertDate() {
        return lRLastAlertDate_;
      }
      /**
       * <pre>
       * 最近短时末次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       * @param value The lRLastAlertDate to set.
       * @return This builder for chaining.
       */
      public Builder setLRLastAlertDate(long value) {

        lRLastAlertDate_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最近短时末次告警时刻
       * </pre>
       *
       * <code>required uint64 LR_last_alert_date = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearLRLastAlertDate() {
        bitField0_ = (bitField0_ & ~0x00000800);
        lRLastAlertDate_ = 0L;
        onChanged();
        return this;
      }

      private int lRAlertTimes_ ;
      /**
       * <pre>
       * 最近短时告警次数
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       * @return Whether the lRAlertTimes field is set.
       */
      @java.lang.Override
      public boolean hasLRAlertTimes() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 最近短时告警次数
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       * @return The lRAlertTimes.
       */
      @java.lang.Override
      public int getLRAlertTimes() {
        return lRAlertTimes_;
      }
      /**
       * <pre>
       * 最近短时告警次数
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       * @param value The lRAlertTimes to set.
       * @return This builder for chaining.
       */
      public Builder setLRAlertTimes(int value) {

        lRAlertTimes_ = value;
        bitField0_ |= 0x00001000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最近短时告警次数
       * </pre>
       *
       * <code>required uint32 LR_alert_times = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearLRAlertTimes() {
        bitField0_ = (bitField0_ & ~0x00001000);
        lRAlertTimes_ = 0;
        onChanged();
        return this;
      }

      private int detectType_ ;
      /**
       * <pre>
       * 检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       * @return Whether the detectType field is set.
       */
      @java.lang.Override
      public boolean hasDetectType() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       * @return The detectType.
       */
      @java.lang.Override
      public int getDetectType() {
        return detectType_;
      }
      /**
       * <pre>
       * 检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       * @param value The detectType to set.
       * @return This builder for chaining.
       */
      public Builder setDetectType(int value) {

        detectType_ = value;
        bitField0_ |= 0x00002000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 检测类型	取值范围为后文中*_alert_info的序列编号
       * </pre>
       *
       * <code>required uint32 detect_type = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearDetectType() {
        bitField0_ = (bitField0_ & ~0x00002000);
        detectType_ = 0;
        onChanged();
        return this;
      }

      private int threatType_ ;
      /**
       * <pre>
       * 威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       * @return Whether the threatType field is set.
       */
      @java.lang.Override
      public boolean hasThreatType() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       * @return The threatType.
       */
      @java.lang.Override
      public int getThreatType() {
        return threatType_;
      }
      /**
       * <pre>
       * 威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       * @param value The threatType to set.
       * @return This builder for chaining.
       */
      public Builder setThreatType(int value) {

        threatType_ = value;
        bitField0_ |= 0x00004000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁类型	见威胁类型列表
       * </pre>
       *
       * <code>required uint32 threat_type = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearThreatType() {
        bitField0_ = (bitField0_ & ~0x00004000);
        threatType_ = 0;
        onChanged();
        return this;
      }

      private int severity_ ;
      /**
       * <pre>
       * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       * @return Whether the severity field is set.
       */
      @java.lang.Override
      public boolean hasSeverity() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       * @return The severity.
       */
      @java.lang.Override
      public int getSeverity() {
        return severity_;
      }
      /**
       * <pre>
       * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       * @param value The severity to set.
       * @return This builder for chaining.
       */
      public Builder setSeverity(int value) {

        severity_ = value;
        bitField0_ |= 0x00008000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 威胁等级	0（安全）、1（低危）、2（中危）、3（高危）、4（危急），危急是未公开披露APT或独家高价值情报
       * </pre>
       *
       * <code>required uint32 severity = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeverity() {
        bitField0_ = (bitField0_ & ~0x00008000);
        severity_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object killChain_ = "";
      /**
       * <pre>
       * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       * @return Whether the killChain field is set.
       */
      public boolean hasKillChain() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       * @return The killChain.
       */
      public java.lang.String getKillChain() {
        java.lang.Object ref = killChain_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            killChain_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       * @return The bytes for killChain.
       */
      public com.google.protobuf.ByteString
          getKillChainBytes() {
        java.lang.Object ref = killChain_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          killChain_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       * @param value The killChain to set.
       * @return This builder for chaining.
       */
      public Builder setKillChain(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        killChain_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearKillChain() {
        killChain_ = getDefaultInstance().getKillChain();
        bitField0_ = (bitField0_ & ~0x00010000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 杀伤链标签	侦察跟踪、武器构建、载荷投递、漏洞利用、安装植入、命令控制、目标达成
       * </pre>
       *
       * <code>required string kill_chain = 17;</code>
       * @param value The bytes for killChain to set.
       * @return This builder for chaining.
       */
      public Builder setKillChainBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        killChain_ = value;
        bitField0_ |= 0x00010000;
        onChanged();
        return this;
      }

      private java.lang.Object tactic_ = "";
      /**
       * <pre>
       * ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       * @return Whether the tactic field is set.
       */
      public boolean hasTactic() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       * @return The tactic.
       */
      public java.lang.String getTactic() {
        java.lang.Object ref = tactic_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            tactic_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       * @return The bytes for tactic.
       */
      public com.google.protobuf.ByteString
          getTacticBytes() {
        java.lang.Object ref = tactic_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tactic_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       * @param value The tactic to set.
       * @return This builder for chaining.
       */
      public Builder setTactic(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        tactic_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearTactic() {
        tactic_ = getDefaultInstance().getTactic();
        bitField0_ = (bitField0_ & ~0x00020000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ATT&amp;CK策略标签	TA0001（初始访问）
       * </pre>
       *
       * <code>optional string tactic = 18;</code>
       * @param value The bytes for tactic to set.
       * @return This builder for chaining.
       */
      public Builder setTacticBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        tactic_ = value;
        bitField0_ |= 0x00020000;
        onChanged();
        return this;
      }

      private java.lang.Object technique_ = "";
      /**
       * <pre>
       * ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       * @return Whether the technique field is set.
       */
      public boolean hasTechnique() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       * @return The technique.
       */
      public java.lang.String getTechnique() {
        java.lang.Object ref = technique_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            technique_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       * @return The bytes for technique.
       */
      public com.google.protobuf.ByteString
          getTechniqueBytes() {
        java.lang.Object ref = technique_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          technique_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       * @param value The technique to set.
       * @return This builder for chaining.
       */
      public Builder setTechnique(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        technique_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearTechnique() {
        technique_ = getDefaultInstance().getTechnique();
        bitField0_ = (bitField0_ & ~0x00040000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * ATT&amp;CK技术标签	T1566（网络钓鱼）
       * </pre>
       *
       * <code>optional string technique = 19;</code>
       * @param value The bytes for technique to set.
       * @return This builder for chaining.
       */
      public Builder setTechniqueBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        technique_ = value;
        bitField0_ |= 0x00040000;
        onChanged();
        return this;
      }

      private java.lang.Object confidence_ = "";
      /**
       * <pre>
       * 置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       * @return Whether the confidence field is set.
       */
      public boolean hasConfidence() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       * @return The confidence.
       */
      public java.lang.String getConfidence() {
        java.lang.Object ref = confidence_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            confidence_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       * @return The bytes for confidence.
       */
      public com.google.protobuf.ByteString
          getConfidenceBytes() {
        java.lang.Object ref = confidence_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          confidence_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       * @param value The confidence to set.
       * @return This builder for chaining.
       */
      public Builder setConfidence(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        confidence_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfidence() {
        confidence_ = getDefaultInstance().getConfidence();
        bitField0_ = (bitField0_ & ~0x00080000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 置信度	低、中、高
       * </pre>
       *
       * <code>required string confidence = 20;</code>
       * @param value The bytes for confidence to set.
       * @return This builder for chaining.
       */
      public Builder setConfidenceBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        confidence_ = value;
        bitField0_ |= 0x00080000;
        onChanged();
        return this;
      }

      private java.lang.Object tranProto_ = "";
      /**
       * <pre>
       * 传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       * @return Whether the tranProto field is set.
       */
      public boolean hasTranProto() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       * @return The tranProto.
       */
      public java.lang.String getTranProto() {
        java.lang.Object ref = tranProto_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            tranProto_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       * @return The bytes for tranProto.
       */
      public com.google.protobuf.ByteString
          getTranProtoBytes() {
        java.lang.Object ref = tranProto_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          tranProto_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       * @param value The tranProto to set.
       * @return This builder for chaining.
       */
      public Builder setTranProto(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        tranProto_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearTranProto() {
        tranProto_ = getDefaultInstance().getTranProto();
        bitField0_ = (bitField0_ & ~0x00100000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 传输层协议	TCP、UDP、SCTP
       * </pre>
       *
       * <code>required string tran_proto = 21;</code>
       * @param value The bytes for tranProto to set.
       * @return This builder for chaining.
       */
      public Builder setTranProtoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        tranProto_ = value;
        bitField0_ |= 0x00100000;
        onChanged();
        return this;
      }

      private java.lang.Object appProto_ = "";
      /**
       * <pre>
       * 应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       * @return Whether the appProto field is set.
       */
      public boolean hasAppProto() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       * @return The appProto.
       */
      public java.lang.String getAppProto() {
        java.lang.Object ref = appProto_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            appProto_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       * @return The bytes for appProto.
       */
      public com.google.protobuf.ByteString
          getAppProtoBytes() {
        java.lang.Object ref = appProto_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appProto_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       * @param value The appProto to set.
       * @return This builder for chaining.
       */
      public Builder setAppProto(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appProto_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppProto() {
        appProto_ = getDefaultInstance().getAppProto();
        bitField0_ = (bitField0_ & ~0x00200000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 应用层协议	HTTP、TLS、SSH
       * </pre>
       *
       * <code>optional string app_proto = 22;</code>
       * @param value The bytes for appProto to set.
       * @return This builder for chaining.
       */
      public Builder setAppProtoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        appProto_ = value;
        bitField0_ |= 0x00200000;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString metaData_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <pre>
       * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       * @return Whether the metaData field is set.
       */
      @java.lang.Override
      public boolean hasMetaData() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       * @return The metaData.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMetaData() {
        return metaData_;
      }
      /**
       * <pre>
       * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       * @param value The metaData to set.
       * @return This builder for chaining.
       */
      public Builder setMetaData(com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        metaData_ = value;
        bitField0_ |= 0x00400000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 原始元数据	产生告警的链接原始元数据，封装为一个字段，参考SDX网防目标元数据存储规范
       * </pre>
       *
       * <code>optional bytes meta_data = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearMetaData() {
        bitField0_ = (bitField0_ & ~0x00400000);
        metaData_ = getDefaultInstance().getMetaData();
        onChanged();
        return this;
      }

      private java.lang.Object rawData_ = "";
      /**
       * <pre>
       * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       * @return Whether the rawData field is set.
       */
      public boolean hasRawData() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       * @return The rawData.
       */
      public java.lang.String getRawData() {
        java.lang.Object ref = rawData_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            rawData_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       * @return The bytes for rawData.
       */
      public com.google.protobuf.ByteString
          getRawDataBytes() {
        java.lang.Object ref = rawData_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          rawData_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       * @param value The rawData to set.
       * @return This builder for chaining.
       */
      public Builder setRawData(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        rawData_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearRawData() {
        rawData_ = getDefaultInstance().getRawData();
        bitField0_ = (bitField0_ & ~0x00800000);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 原始数据（存储路径）	产生告警的原始数据样本在网络文件系统中的存储路径
       * </pre>
       *
       * <code>optional string raw_data = 24;</code>
       * @param value The bytes for rawData to set.
       * @return This builder for chaining.
       */
      public Builder setRawDataBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        rawData_ = value;
        bitField0_ |= 0x00800000;
        onChanged();
        return this;
      }

      private IocAlertInfo.IOC_ALERT_INFO iocAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          IocAlertInfo.IOC_ALERT_INFO, IocAlertInfo.IOC_ALERT_INFO.Builder, IocAlertInfo.IOC_ALERT_INFOOrBuilder> iocAlertInfoBuilder_;
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       * @return Whether the iocAlertInfo field is set.
       */
      public boolean hasIocAlertInfo() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       * @return The iocAlertInfo.
       */
      public IocAlertInfo.IOC_ALERT_INFO getIocAlertInfo() {
        if (iocAlertInfoBuilder_ == null) {
          return iocAlertInfo_ == null ? IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
        } else {
          return iocAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder setIocAlertInfo(IocAlertInfo.IOC_ALERT_INFO value) {
        if (iocAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          iocAlertInfo_ = value;
        } else {
          iocAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder setIocAlertInfo(
          IocAlertInfo.IOC_ALERT_INFO.Builder builderForValue) {
        if (iocAlertInfoBuilder_ == null) {
          iocAlertInfo_ = builderForValue.build();
        } else {
          iocAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x01000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder mergeIocAlertInfo(IocAlertInfo.IOC_ALERT_INFO value) {
        if (iocAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x01000000) != 0) &&
            iocAlertInfo_ != null &&
            iocAlertInfo_ != IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance()) {
            getIocAlertInfoBuilder().mergeFrom(value);
          } else {
            iocAlertInfo_ = value;
          }
        } else {
          iocAlertInfoBuilder_.mergeFrom(value);
        }
        if (iocAlertInfo_ != null) {
          bitField0_ |= 0x01000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public Builder clearIocAlertInfo() {
        bitField0_ = (bitField0_ & ~0x01000000);
        iocAlertInfo_ = null;
        if (iocAlertInfoBuilder_ != null) {
          iocAlertInfoBuilder_.dispose();
          iocAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public IocAlertInfo.IOC_ALERT_INFO.Builder getIocAlertInfoBuilder() {
        bitField0_ |= 0x01000000;
        onChanged();
        return getIocAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      public IocAlertInfo.IOC_ALERT_INFOOrBuilder getIocAlertInfoOrBuilder() {
        if (iocAlertInfoBuilder_ != null) {
          return iocAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return iocAlertInfo_ == null ?
              IocAlertInfo.IOC_ALERT_INFO.getDefaultInstance() : iocAlertInfo_;
        }
      }
      /**
       * <pre>
       * 失陷情报告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOC_ALERT_INFO ioc_alert_info = 100;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IocAlertInfo.IOC_ALERT_INFO, IocAlertInfo.IOC_ALERT_INFO.Builder, IocAlertInfo.IOC_ALERT_INFOOrBuilder> 
          getIocAlertInfoFieldBuilder() {
        if (iocAlertInfoBuilder_ == null) {
          iocAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IocAlertInfo.IOC_ALERT_INFO, IocAlertInfo.IOC_ALERT_INFO.Builder, IocAlertInfo.IOC_ALERT_INFOOrBuilder>(
                  getIocAlertInfo(),
                  getParentForChildren(),
                  isClean());
          iocAlertInfo_ = null;
        }
        return iocAlertInfoBuilder_;
      }

      private IobAlertInfo.IOB_ALERT_INFO iobAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          IobAlertInfo.IOB_ALERT_INFO, IobAlertInfo.IOB_ALERT_INFO.Builder, IobAlertInfo.IOB_ALERT_INFOOrBuilder> iobAlertInfoBuilder_;
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       * @return Whether the iobAlertInfo field is set.
       */
      public boolean hasIobAlertInfo() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       * @return The iobAlertInfo.
       */
      public IobAlertInfo.IOB_ALERT_INFO getIobAlertInfo() {
        if (iobAlertInfoBuilder_ == null) {
          return iobAlertInfo_ == null ? IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
        } else {
          return iobAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder setIobAlertInfo(IobAlertInfo.IOB_ALERT_INFO value) {
        if (iobAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          iobAlertInfo_ = value;
        } else {
          iobAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder setIobAlertInfo(
          IobAlertInfo.IOB_ALERT_INFO.Builder builderForValue) {
        if (iobAlertInfoBuilder_ == null) {
          iobAlertInfo_ = builderForValue.build();
        } else {
          iobAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x02000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder mergeIobAlertInfo(IobAlertInfo.IOB_ALERT_INFO value) {
        if (iobAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x02000000) != 0) &&
            iobAlertInfo_ != null &&
            iobAlertInfo_ != IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance()) {
            getIobAlertInfoBuilder().mergeFrom(value);
          } else {
            iobAlertInfo_ = value;
          }
        } else {
          iobAlertInfoBuilder_.mergeFrom(value);
        }
        if (iobAlertInfo_ != null) {
          bitField0_ |= 0x02000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public Builder clearIobAlertInfo() {
        bitField0_ = (bitField0_ & ~0x02000000);
        iobAlertInfo_ = null;
        if (iobAlertInfoBuilder_ != null) {
          iobAlertInfoBuilder_.dispose();
          iobAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public IobAlertInfo.IOB_ALERT_INFO.Builder getIobAlertInfoBuilder() {
        bitField0_ |= 0x02000000;
        onChanged();
        return getIobAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      public IobAlertInfo.IOB_ALERT_INFOOrBuilder getIobAlertInfoOrBuilder() {
        if (iobAlertInfoBuilder_ != null) {
          return iobAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return iobAlertInfo_ == null ?
              IobAlertInfo.IOB_ALERT_INFO.getDefaultInstance() : iobAlertInfo_;
        }
      }
      /**
       * <pre>
       * 异常行为告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOB_ALERT_INFO iob_alert_info = 101;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IobAlertInfo.IOB_ALERT_INFO, IobAlertInfo.IOB_ALERT_INFO.Builder, IobAlertInfo.IOB_ALERT_INFOOrBuilder> 
          getIobAlertInfoFieldBuilder() {
        if (iobAlertInfoBuilder_ == null) {
          iobAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IobAlertInfo.IOB_ALERT_INFO, IobAlertInfo.IOB_ALERT_INFO.Builder, IobAlertInfo.IOB_ALERT_INFOOrBuilder>(
                  getIobAlertInfo(),
                  getParentForChildren(),
                  isClean());
          iobAlertInfo_ = null;
        }
        return iobAlertInfoBuilder_;
      }

      private IoaAlertInfo.IOA_ALERT_INFO ioaAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          IoaAlertInfo.IOA_ALERT_INFO, IoaAlertInfo.IOA_ALERT_INFO.Builder, IoaAlertInfo.IOA_ALERT_INFOOrBuilder> ioaAlertInfoBuilder_;
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       * @return Whether the ioaAlertInfo field is set.
       */
      public boolean hasIoaAlertInfo() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       * @return The ioaAlertInfo.
       */
      public IoaAlertInfo.IOA_ALERT_INFO getIoaAlertInfo() {
        if (ioaAlertInfoBuilder_ == null) {
          return ioaAlertInfo_ == null ? IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
        } else {
          return ioaAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder setIoaAlertInfo(IoaAlertInfo.IOA_ALERT_INFO value) {
        if (ioaAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ioaAlertInfo_ = value;
        } else {
          ioaAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder setIoaAlertInfo(
          IoaAlertInfo.IOA_ALERT_INFO.Builder builderForValue) {
        if (ioaAlertInfoBuilder_ == null) {
          ioaAlertInfo_ = builderForValue.build();
        } else {
          ioaAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x04000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder mergeIoaAlertInfo(IoaAlertInfo.IOA_ALERT_INFO value) {
        if (ioaAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x04000000) != 0) &&
            ioaAlertInfo_ != null &&
            ioaAlertInfo_ != IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance()) {
            getIoaAlertInfoBuilder().mergeFrom(value);
          } else {
            ioaAlertInfo_ = value;
          }
        } else {
          ioaAlertInfoBuilder_.mergeFrom(value);
        }
        if (ioaAlertInfo_ != null) {
          bitField0_ |= 0x04000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public Builder clearIoaAlertInfo() {
        bitField0_ = (bitField0_ & ~0x04000000);
        ioaAlertInfo_ = null;
        if (ioaAlertInfoBuilder_ != null) {
          ioaAlertInfoBuilder_.dispose();
          ioaAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public IoaAlertInfo.IOA_ALERT_INFO.Builder getIoaAlertInfoBuilder() {
        bitField0_ |= 0x04000000;
        onChanged();
        return getIoaAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      public IoaAlertInfo.IOA_ALERT_INFOOrBuilder getIoaAlertInfoOrBuilder() {
        if (ioaAlertInfoBuilder_ != null) {
          return ioaAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return ioaAlertInfo_ == null ?
              IoaAlertInfo.IOA_ALERT_INFO.getDefaultInstance() : ioaAlertInfo_;
        }
      }
      /**
       * <pre>
       * 攻击利用告警信息	封装格式
       * </pre>
       *
       * <code>optional .IOA_ALERT_INFO ioa_alert_info = 102;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IoaAlertInfo.IOA_ALERT_INFO, IoaAlertInfo.IOA_ALERT_INFO.Builder, IoaAlertInfo.IOA_ALERT_INFOOrBuilder> 
          getIoaAlertInfoFieldBuilder() {
        if (ioaAlertInfoBuilder_ == null) {
          ioaAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IoaAlertInfo.IOA_ALERT_INFO, IoaAlertInfo.IOA_ALERT_INFO.Builder, IoaAlertInfo.IOA_ALERT_INFOOrBuilder>(
                  getIoaAlertInfo(),
                  getParentForChildren(),
                  isClean());
          ioaAlertInfo_ = null;
        }
        return ioaAlertInfoBuilder_;
      }

      private IiotAlertInfo.IIOT_ALERT_INFO iiotAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          IiotAlertInfo.IIOT_ALERT_INFO, IiotAlertInfo.IIOT_ALERT_INFO.Builder, IiotAlertInfo.IIOT_ALERT_INFOOrBuilder> iiotAlertInfoBuilder_;
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       * @return Whether the iiotAlertInfo field is set.
       */
      public boolean hasIiotAlertInfo() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       * @return The iiotAlertInfo.
       */
      public IiotAlertInfo.IIOT_ALERT_INFO getIiotAlertInfo() {
        if (iiotAlertInfoBuilder_ == null) {
          return iiotAlertInfo_ == null ? IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
        } else {
          return iiotAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder setIiotAlertInfo(IiotAlertInfo.IIOT_ALERT_INFO value) {
        if (iiotAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          iiotAlertInfo_ = value;
        } else {
          iiotAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder setIiotAlertInfo(
          IiotAlertInfo.IIOT_ALERT_INFO.Builder builderForValue) {
        if (iiotAlertInfoBuilder_ == null) {
          iiotAlertInfo_ = builderForValue.build();
        } else {
          iiotAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x08000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder mergeIiotAlertInfo(IiotAlertInfo.IIOT_ALERT_INFO value) {
        if (iiotAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x08000000) != 0) &&
            iiotAlertInfo_ != null &&
            iiotAlertInfo_ != IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance()) {
            getIiotAlertInfoBuilder().mergeFrom(value);
          } else {
            iiotAlertInfo_ = value;
          }
        } else {
          iiotAlertInfoBuilder_.mergeFrom(value);
        }
        if (iiotAlertInfo_ != null) {
          bitField0_ |= 0x08000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public Builder clearIiotAlertInfo() {
        bitField0_ = (bitField0_ & ~0x08000000);
        iiotAlertInfo_ = null;
        if (iiotAlertInfoBuilder_ != null) {
          iiotAlertInfoBuilder_.dispose();
          iiotAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public IiotAlertInfo.IIOT_ALERT_INFO.Builder getIiotAlertInfoBuilder() {
        bitField0_ |= 0x08000000;
        onChanged();
        return getIiotAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      public IiotAlertInfo.IIOT_ALERT_INFOOrBuilder getIiotAlertInfoOrBuilder() {
        if (iiotAlertInfoBuilder_ != null) {
          return iiotAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return iiotAlertInfo_ == null ?
              IiotAlertInfo.IIOT_ALERT_INFO.getDefaultInstance() : iiotAlertInfo_;
        }
      }
      /**
       * <pre>
       * 工业物联网告警信息	封装格式
       * </pre>
       *
       * <code>optional .IIOT_ALERT_INFO iiot_alert_info = 103;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          IiotAlertInfo.IIOT_ALERT_INFO, IiotAlertInfo.IIOT_ALERT_INFO.Builder, IiotAlertInfo.IIOT_ALERT_INFOOrBuilder> 
          getIiotAlertInfoFieldBuilder() {
        if (iiotAlertInfoBuilder_ == null) {
          iiotAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              IiotAlertInfo.IIOT_ALERT_INFO, IiotAlertInfo.IIOT_ALERT_INFO.Builder, IiotAlertInfo.IIOT_ALERT_INFOOrBuilder>(
                  getIiotAlertInfo(),
                  getParentForChildren(),
                  isClean());
          iiotAlertInfo_ = null;
        }
        return iiotAlertInfoBuilder_;
      }

      private FileAlertInfo.FILE_ALERT_INFO fileAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          FileAlertInfo.FILE_ALERT_INFO, FileAlertInfo.FILE_ALERT_INFO.Builder, FileAlertInfo.FILE_ALERT_INFOOrBuilder> fileAlertInfoBuilder_;
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       * @return Whether the fileAlertInfo field is set.
       */
      public boolean hasFileAlertInfo() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       * @return The fileAlertInfo.
       */
      public FileAlertInfo.FILE_ALERT_INFO getFileAlertInfo() {
        if (fileAlertInfoBuilder_ == null) {
          return fileAlertInfo_ == null ? FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
        } else {
          return fileAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder setFileAlertInfo(FileAlertInfo.FILE_ALERT_INFO value) {
        if (fileAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          fileAlertInfo_ = value;
        } else {
          fileAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder setFileAlertInfo(
          FileAlertInfo.FILE_ALERT_INFO.Builder builderForValue) {
        if (fileAlertInfoBuilder_ == null) {
          fileAlertInfo_ = builderForValue.build();
        } else {
          fileAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x10000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder mergeFileAlertInfo(FileAlertInfo.FILE_ALERT_INFO value) {
        if (fileAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x10000000) != 0) &&
            fileAlertInfo_ != null &&
            fileAlertInfo_ != FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance()) {
            getFileAlertInfoBuilder().mergeFrom(value);
          } else {
            fileAlertInfo_ = value;
          }
        } else {
          fileAlertInfoBuilder_.mergeFrom(value);
        }
        if (fileAlertInfo_ != null) {
          bitField0_ |= 0x10000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public Builder clearFileAlertInfo() {
        bitField0_ = (bitField0_ & ~0x10000000);
        fileAlertInfo_ = null;
        if (fileAlertInfoBuilder_ != null) {
          fileAlertInfoBuilder_.dispose();
          fileAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public FileAlertInfo.FILE_ALERT_INFO.Builder getFileAlertInfoBuilder() {
        bitField0_ |= 0x10000000;
        onChanged();
        return getFileAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      public FileAlertInfo.FILE_ALERT_INFOOrBuilder getFileAlertInfoOrBuilder() {
        if (fileAlertInfoBuilder_ != null) {
          return fileAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return fileAlertInfo_ == null ?
              FileAlertInfo.FILE_ALERT_INFO.getDefaultInstance() : fileAlertInfo_;
        }
      }
      /**
       * <pre>
       * 文件检测告警信息	封装格式
       * </pre>
       *
       * <code>optional .FILE_ALERT_INFO file_alert_info = 104;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          FileAlertInfo.FILE_ALERT_INFO, FileAlertInfo.FILE_ALERT_INFO.Builder, FileAlertInfo.FILE_ALERT_INFOOrBuilder> 
          getFileAlertInfoFieldBuilder() {
        if (fileAlertInfoBuilder_ == null) {
          fileAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              FileAlertInfo.FILE_ALERT_INFO, FileAlertInfo.FILE_ALERT_INFO.Builder, FileAlertInfo.FILE_ALERT_INFOOrBuilder>(
                  getFileAlertInfo(),
                  getParentForChildren(),
                  isClean());
          fileAlertInfo_ = null;
        }
        return fileAlertInfoBuilder_;
      }

      private CryptoAlertInfo.CRYPTO_ALERT_INFO cryptoAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          CryptoAlertInfo.CRYPTO_ALERT_INFO, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder, CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder> cryptoAlertInfoBuilder_;
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       * @return Whether the cryptoAlertInfo field is set.
       */
      public boolean hasCryptoAlertInfo() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       * @return The cryptoAlertInfo.
       */
      public CryptoAlertInfo.CRYPTO_ALERT_INFO getCryptoAlertInfo() {
        if (cryptoAlertInfoBuilder_ == null) {
          return cryptoAlertInfo_ == null ? CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
        } else {
          return cryptoAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder setCryptoAlertInfo(CryptoAlertInfo.CRYPTO_ALERT_INFO value) {
        if (cryptoAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cryptoAlertInfo_ = value;
        } else {
          cryptoAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder setCryptoAlertInfo(
          CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder builderForValue) {
        if (cryptoAlertInfoBuilder_ == null) {
          cryptoAlertInfo_ = builderForValue.build();
        } else {
          cryptoAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x20000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder mergeCryptoAlertInfo(CryptoAlertInfo.CRYPTO_ALERT_INFO value) {
        if (cryptoAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x20000000) != 0) &&
            cryptoAlertInfo_ != null &&
            cryptoAlertInfo_ != CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance()) {
            getCryptoAlertInfoBuilder().mergeFrom(value);
          } else {
            cryptoAlertInfo_ = value;
          }
        } else {
          cryptoAlertInfoBuilder_.mergeFrom(value);
        }
        if (cryptoAlertInfo_ != null) {
          bitField0_ |= 0x20000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public Builder clearCryptoAlertInfo() {
        bitField0_ = (bitField0_ & ~0x20000000);
        cryptoAlertInfo_ = null;
        if (cryptoAlertInfoBuilder_ != null) {
          cryptoAlertInfoBuilder_.dispose();
          cryptoAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder getCryptoAlertInfoBuilder() {
        bitField0_ |= 0x20000000;
        onChanged();
        return getCryptoAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      public CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder getCryptoAlertInfoOrBuilder() {
        if (cryptoAlertInfoBuilder_ != null) {
          return cryptoAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return cryptoAlertInfo_ == null ?
              CryptoAlertInfo.CRYPTO_ALERT_INFO.getDefaultInstance() : cryptoAlertInfo_;
        }
      }
      /**
       * <pre>
       * 密数据异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CRYPTO_ALERT_INFO crypto_alert_info = 105;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          CryptoAlertInfo.CRYPTO_ALERT_INFO, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder, CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder> 
          getCryptoAlertInfoFieldBuilder() {
        if (cryptoAlertInfoBuilder_ == null) {
          cryptoAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              CryptoAlertInfo.CRYPTO_ALERT_INFO, CryptoAlertInfo.CRYPTO_ALERT_INFO.Builder, CryptoAlertInfo.CRYPTO_ALERT_INFOOrBuilder>(
                  getCryptoAlertInfo(),
                  getParentForChildren(),
                  isClean());
          cryptoAlertInfo_ = null;
        }
        return cryptoAlertInfoBuilder_;
      }

      private CertAlertInfo.CERT_ALERT_INFO certAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          CertAlertInfo.CERT_ALERT_INFO, CertAlertInfo.CERT_ALERT_INFO.Builder, CertAlertInfo.CERT_ALERT_INFOOrBuilder> certAlertInfoBuilder_;
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       * @return Whether the certAlertInfo field is set.
       */
      public boolean hasCertAlertInfo() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       * @return The certAlertInfo.
       */
      public CertAlertInfo.CERT_ALERT_INFO getCertAlertInfo() {
        if (certAlertInfoBuilder_ == null) {
          return certAlertInfo_ == null ? CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
        } else {
          return certAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder setCertAlertInfo(CertAlertInfo.CERT_ALERT_INFO value) {
        if (certAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          certAlertInfo_ = value;
        } else {
          certAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder setCertAlertInfo(
          CertAlertInfo.CERT_ALERT_INFO.Builder builderForValue) {
        if (certAlertInfoBuilder_ == null) {
          certAlertInfo_ = builderForValue.build();
        } else {
          certAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x40000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder mergeCertAlertInfo(CertAlertInfo.CERT_ALERT_INFO value) {
        if (certAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x40000000) != 0) &&
            certAlertInfo_ != null &&
            certAlertInfo_ != CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance()) {
            getCertAlertInfoBuilder().mergeFrom(value);
          } else {
            certAlertInfo_ = value;
          }
        } else {
          certAlertInfoBuilder_.mergeFrom(value);
        }
        if (certAlertInfo_ != null) {
          bitField0_ |= 0x40000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public Builder clearCertAlertInfo() {
        bitField0_ = (bitField0_ & ~0x40000000);
        certAlertInfo_ = null;
        if (certAlertInfoBuilder_ != null) {
          certAlertInfoBuilder_.dispose();
          certAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public CertAlertInfo.CERT_ALERT_INFO.Builder getCertAlertInfoBuilder() {
        bitField0_ |= 0x40000000;
        onChanged();
        return getCertAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      public CertAlertInfo.CERT_ALERT_INFOOrBuilder getCertAlertInfoOrBuilder() {
        if (certAlertInfoBuilder_ != null) {
          return certAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return certAlertInfo_ == null ?
              CertAlertInfo.CERT_ALERT_INFO.getDefaultInstance() : certAlertInfo_;
        }
      }
      /**
       * <pre>
       * 证书异常告警信息	封装格式
       * </pre>
       *
       * <code>optional .CERT_ALERT_INFO cert_alert_info = 106;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          CertAlertInfo.CERT_ALERT_INFO, CertAlertInfo.CERT_ALERT_INFO.Builder, CertAlertInfo.CERT_ALERT_INFOOrBuilder> 
          getCertAlertInfoFieldBuilder() {
        if (certAlertInfoBuilder_ == null) {
          certAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              CertAlertInfo.CERT_ALERT_INFO, CertAlertInfo.CERT_ALERT_INFO.Builder, CertAlertInfo.CERT_ALERT_INFOOrBuilder>(
                  getCertAlertInfo(),
                  getParentForChildren(),
                  isClean());
          certAlertInfo_ = null;
        }
        return certAlertInfoBuilder_;
      }

      private MailAlertInfo.MAIL_ALERT_INFO mailAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          MailAlertInfo.MAIL_ALERT_INFO, MailAlertInfo.MAIL_ALERT_INFO.Builder, MailAlertInfo.MAIL_ALERT_INFOOrBuilder> mailAlertInfoBuilder_;
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       * @return Whether the mailAlertInfo field is set.
       */
      public boolean hasMailAlertInfo() {
        return ((bitField0_ & 0x80000000) != 0);
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       * @return The mailAlertInfo.
       */
      public MailAlertInfo.MAIL_ALERT_INFO getMailAlertInfo() {
        if (mailAlertInfoBuilder_ == null) {
          return mailAlertInfo_ == null ? MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
        } else {
          return mailAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder setMailAlertInfo(MailAlertInfo.MAIL_ALERT_INFO value) {
        if (mailAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mailAlertInfo_ = value;
        } else {
          mailAlertInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder setMailAlertInfo(
          MailAlertInfo.MAIL_ALERT_INFO.Builder builderForValue) {
        if (mailAlertInfoBuilder_ == null) {
          mailAlertInfo_ = builderForValue.build();
        } else {
          mailAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x80000000;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder mergeMailAlertInfo(MailAlertInfo.MAIL_ALERT_INFO value) {
        if (mailAlertInfoBuilder_ == null) {
          if (((bitField0_ & 0x80000000) != 0) &&
            mailAlertInfo_ != null &&
            mailAlertInfo_ != MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance()) {
            getMailAlertInfoBuilder().mergeFrom(value);
          } else {
            mailAlertInfo_ = value;
          }
        } else {
          mailAlertInfoBuilder_.mergeFrom(value);
        }
        if (mailAlertInfo_ != null) {
          bitField0_ |= 0x80000000;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public Builder clearMailAlertInfo() {
        bitField0_ = (bitField0_ & ~0x80000000);
        mailAlertInfo_ = null;
        if (mailAlertInfoBuilder_ != null) {
          mailAlertInfoBuilder_.dispose();
          mailAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public MailAlertInfo.MAIL_ALERT_INFO.Builder getMailAlertInfoBuilder() {
        bitField0_ |= 0x80000000;
        onChanged();
        return getMailAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      public MailAlertInfo.MAIL_ALERT_INFOOrBuilder getMailAlertInfoOrBuilder() {
        if (mailAlertInfoBuilder_ != null) {
          return mailAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return mailAlertInfo_ == null ?
              MailAlertInfo.MAIL_ALERT_INFO.getDefaultInstance() : mailAlertInfo_;
        }
      }
      /**
       * <pre>
       * 邮件威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MAIL_ALERT_INFO mail_alert_info = 107;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          MailAlertInfo.MAIL_ALERT_INFO, MailAlertInfo.MAIL_ALERT_INFO.Builder, MailAlertInfo.MAIL_ALERT_INFOOrBuilder> 
          getMailAlertInfoFieldBuilder() {
        if (mailAlertInfoBuilder_ == null) {
          mailAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              MailAlertInfo.MAIL_ALERT_INFO, MailAlertInfo.MAIL_ALERT_INFO.Builder, MailAlertInfo.MAIL_ALERT_INFOOrBuilder>(
                  getMailAlertInfo(),
                  getParentForChildren(),
                  isClean());
          mailAlertInfo_ = null;
        }
        return mailAlertInfoBuilder_;
      }

      private MobileAlertInfo.MOBILE_ALERT_INFO mobileAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          MobileAlertInfo.MOBILE_ALERT_INFO, MobileAlertInfo.MOBILE_ALERT_INFO.Builder, MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder> mobileAlertInfoBuilder_;
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       * @return Whether the mobileAlertInfo field is set.
       */
      public boolean hasMobileAlertInfo() {
        return ((bitField1_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       * @return The mobileAlertInfo.
       */
      public MobileAlertInfo.MOBILE_ALERT_INFO getMobileAlertInfo() {
        if (mobileAlertInfoBuilder_ == null) {
          return mobileAlertInfo_ == null ? MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
        } else {
          return mobileAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder setMobileAlertInfo(MobileAlertInfo.MOBILE_ALERT_INFO value) {
        if (mobileAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mobileAlertInfo_ = value;
        } else {
          mobileAlertInfoBuilder_.setMessage(value);
        }
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder setMobileAlertInfo(
          MobileAlertInfo.MOBILE_ALERT_INFO.Builder builderForValue) {
        if (mobileAlertInfoBuilder_ == null) {
          mobileAlertInfo_ = builderForValue.build();
        } else {
          mobileAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField1_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder mergeMobileAlertInfo(MobileAlertInfo.MOBILE_ALERT_INFO value) {
        if (mobileAlertInfoBuilder_ == null) {
          if (((bitField1_ & 0x00000001) != 0) &&
            mobileAlertInfo_ != null &&
            mobileAlertInfo_ != MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance()) {
            getMobileAlertInfoBuilder().mergeFrom(value);
          } else {
            mobileAlertInfo_ = value;
          }
        } else {
          mobileAlertInfoBuilder_.mergeFrom(value);
        }
        if (mobileAlertInfo_ != null) {
          bitField1_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public Builder clearMobileAlertInfo() {
        bitField1_ = (bitField1_ & ~0x00000001);
        mobileAlertInfo_ = null;
        if (mobileAlertInfoBuilder_ != null) {
          mobileAlertInfoBuilder_.dispose();
          mobileAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public MobileAlertInfo.MOBILE_ALERT_INFO.Builder getMobileAlertInfoBuilder() {
        bitField1_ |= 0x00000001;
        onChanged();
        return getMobileAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      public MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder getMobileAlertInfoOrBuilder() {
        if (mobileAlertInfoBuilder_ != null) {
          return mobileAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return mobileAlertInfo_ == null ?
              MobileAlertInfo.MOBILE_ALERT_INFO.getDefaultInstance() : mobileAlertInfo_;
        }
      }
      /**
       * <pre>
       * 移动网威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .MOBILE_ALERT_INFO mobile_alert_info = 108;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          MobileAlertInfo.MOBILE_ALERT_INFO, MobileAlertInfo.MOBILE_ALERT_INFO.Builder, MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder> 
          getMobileAlertInfoFieldBuilder() {
        if (mobileAlertInfoBuilder_ == null) {
          mobileAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              MobileAlertInfo.MOBILE_ALERT_INFO, MobileAlertInfo.MOBILE_ALERT_INFO.Builder, MobileAlertInfo.MOBILE_ALERT_INFOOrBuilder>(
                  getMobileAlertInfo(),
                  getParentForChildren(),
                  isClean());
          mobileAlertInfo_ = null;
        }
        return mobileAlertInfoBuilder_;
      }

      private ProtoAlertInfo.PROTO_ALERT_INFO protoAlertInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          ProtoAlertInfo.PROTO_ALERT_INFO, ProtoAlertInfo.PROTO_ALERT_INFO.Builder, ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder> protoAlertInfoBuilder_;
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       * @return Whether the protoAlertInfo field is set.
       */
      public boolean hasProtoAlertInfo() {
        return ((bitField1_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       * @return The protoAlertInfo.
       */
      public ProtoAlertInfo.PROTO_ALERT_INFO getProtoAlertInfo() {
        if (protoAlertInfoBuilder_ == null) {
          return protoAlertInfo_ == null ? ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
        } else {
          return protoAlertInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder setProtoAlertInfo(ProtoAlertInfo.PROTO_ALERT_INFO value) {
        if (protoAlertInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          protoAlertInfo_ = value;
        } else {
          protoAlertInfoBuilder_.setMessage(value);
        }
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder setProtoAlertInfo(
          ProtoAlertInfo.PROTO_ALERT_INFO.Builder builderForValue) {
        if (protoAlertInfoBuilder_ == null) {
          protoAlertInfo_ = builderForValue.build();
        } else {
          protoAlertInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField1_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder mergeProtoAlertInfo(ProtoAlertInfo.PROTO_ALERT_INFO value) {
        if (protoAlertInfoBuilder_ == null) {
          if (((bitField1_ & 0x00000002) != 0) &&
            protoAlertInfo_ != null &&
            protoAlertInfo_ != ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance()) {
            getProtoAlertInfoBuilder().mergeFrom(value);
          } else {
            protoAlertInfo_ = value;
          }
        } else {
          protoAlertInfoBuilder_.mergeFrom(value);
        }
        if (protoAlertInfo_ != null) {
          bitField1_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public Builder clearProtoAlertInfo() {
        bitField1_ = (bitField1_ & ~0x00000002);
        protoAlertInfo_ = null;
        if (protoAlertInfoBuilder_ != null) {
          protoAlertInfoBuilder_.dispose();
          protoAlertInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public ProtoAlertInfo.PROTO_ALERT_INFO.Builder getProtoAlertInfoBuilder() {
        bitField1_ |= 0x00000002;
        onChanged();
        return getProtoAlertInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      public ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder getProtoAlertInfoOrBuilder() {
        if (protoAlertInfoBuilder_ != null) {
          return protoAlertInfoBuilder_.getMessageOrBuilder();
        } else {
          return protoAlertInfo_ == null ?
              ProtoAlertInfo.PROTO_ALERT_INFO.getDefaultInstance() : protoAlertInfo_;
        }
      }
      /**
       * <pre>
       * 特色协议威胁告警信息	封装格式
       * </pre>
       *
       * <code>optional .PROTO_ALERT_INFO proto_alert_info = 109;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          ProtoAlertInfo.PROTO_ALERT_INFO, ProtoAlertInfo.PROTO_ALERT_INFO.Builder, ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder> 
          getProtoAlertInfoFieldBuilder() {
        if (protoAlertInfoBuilder_ == null) {
          protoAlertInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              ProtoAlertInfo.PROTO_ALERT_INFO, ProtoAlertInfo.PROTO_ALERT_INFO.Builder, ProtoAlertInfo.PROTO_ALERT_INFOOrBuilder>(
                  getProtoAlertInfo(),
                  getParentForChildren(),
                  isClean());
          protoAlertInfo_ = null;
        }
        return protoAlertInfoBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:ALERT_LOG)
    }

    // @@protoc_insertion_point(class_scope:ALERT_LOG)
    private static final AlertLog.ALERT_LOG DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new AlertLog.ALERT_LOG();
    }

    public static AlertLog.ALERT_LOG getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ALERT_LOG>
        PARSER = new com.google.protobuf.AbstractParser<ALERT_LOG>() {
      @java.lang.Override
      public ALERT_LOG parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ALERT_LOG> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ALERT_LOG> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public AlertLog.ALERT_LOG getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_ALERT_LOG_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_ALERT_LOG_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017alert_log.proto\032\022base/IP_INFO.proto\032\035m" +
      "essage/CERT_ALERT_INFO.proto\032\037message/CR" +
      "YPTO_ALERT_INFO.proto\032\035message/FILE_ALER" +
      "T_INFO.proto\032\035message/IIOT_ALERT_INFO.pr" +
      "oto\032\034message/IOA_ALERT_INFO.proto\032\034messa" +
      "ge/IOB_ALERT_INFO.proto\032\034message/IOC_ALE" +
      "RT_INFO.proto\032\035message/MAIL_ALERT_INFO.p" +
      "roto\032\037message/MOBILE_ALERT_INFO.proto\032\036m" +
      "essage/PROTO_ALERT_INFO.proto\"\256\007\n\tALERT_" +
      "LOG\022\014\n\004guid\030\001 \002(\t\022\014\n\004time\030\002 \002(\t\022\021\n\tline_" +
      "info\030\003 \002(\t\022\025\n\003sip\030\004 \002(\0132\010.IP_INFO\022\025\n\003dip" +
      "\030\005 \002(\0132\010.IP_INFO\022\025\n\003aip\030\006 \002(\0132\010.IP_INFO\022" +
      "\025\n\003vip\030\007 \002(\0132\010.IP_INFO\022\021\n\tsensor_ip\030\010 \002(" +
      "\t\022\021\n\tvendor_id\030\t \002(\t\022\032\n\022LR_aggregate_val" +
      "ue\030\n \002(\t\022\033\n\023LR_first_alert_date\030\013 \002(\004\022\032\n" +
      "\022LR_last_alert_date\030\014 \002(\004\022\026\n\016LR_alert_ti" +
      "mes\030\r \002(\r\022\023\n\013detect_type\030\016 \002(\r\022\023\n\013threat" +
      "_type\030\017 \002(\r\022\020\n\010severity\030\020 \002(\r\022\022\n\nkill_ch" +
      "ain\030\021 \002(\t\022\016\n\006tactic\030\022 \001(\t\022\021\n\ttechnique\030\023" +
      " \001(\t\022\022\n\nconfidence\030\024 \002(\t\022\022\n\ntran_proto\030\025" +
      " \002(\t\022\021\n\tapp_proto\030\026 \001(\t\022\021\n\tmeta_data\030\027 \001" +
      "(\014\022\020\n\010raw_data\030\030 \001(\t\022\'\n\016ioc_alert_info\030d" +
      " \001(\0132\017.IOC_ALERT_INFO\022\'\n\016iob_alert_info\030" +
      "e \001(\0132\017.IOB_ALERT_INFO\022\'\n\016ioa_alert_info" +
      "\030f \001(\0132\017.IOA_ALERT_INFO\022)\n\017iiot_alert_in" +
      "fo\030g \001(\0132\020.IIOT_ALERT_INFO\022)\n\017file_alert" +
      "_info\030h \001(\0132\020.FILE_ALERT_INFO\022-\n\021crypto_" +
      "alert_info\030i \001(\0132\022.CRYPTO_ALERT_INFO\022)\n\017" +
      "cert_alert_info\030j \001(\0132\020.CERT_ALERT_INFO\022" +
      ")\n\017mail_alert_info\030k \001(\0132\020.MAIL_ALERT_IN" +
      "FO\022-\n\021mobile_alert_info\030l \001(\0132\022.MOBILE_A" +
      "LERT_INFO\022+\n\020proto_alert_info\030m \001(\0132\021.PR" +
      "OTO_ALERT_INFOB\nB\010AlertLog"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          IpInfo.getDescriptor(),
          CertAlertInfo.getDescriptor(),
          CryptoAlertInfo.getDescriptor(),
          FileAlertInfo.getDescriptor(),
          IiotAlertInfo.getDescriptor(),
          IoaAlertInfo.getDescriptor(),
          IobAlertInfo.getDescriptor(),
          IocAlertInfo.getDescriptor(),
          MailAlertInfo.getDescriptor(),
          MobileAlertInfo.getDescriptor(),
          ProtoAlertInfo.getDescriptor(),
        });
    internal_static_ALERT_LOG_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_ALERT_LOG_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_ALERT_LOG_descriptor,
        new java.lang.String[] { "Guid", "Time", "LineInfo", "Sip", "Dip", "Aip", "Vip", "SensorIp", "VendorId", "LRAggregateValue", "LRFirstAlertDate", "LRLastAlertDate", "LRAlertTimes", "DetectType", "ThreatType", "Severity", "KillChain", "Tactic", "Technique", "Confidence", "TranProto", "AppProto", "MetaData", "RawData", "IocAlertInfo", "IobAlertInfo", "IoaAlertInfo", "IiotAlertInfo", "FileAlertInfo", "CryptoAlertInfo", "CertAlertInfo", "MailAlertInfo", "MobileAlertInfo", "ProtoAlertInfo", });
    descriptor.resolveAllFeaturesImmutable();
    IpInfo.getDescriptor();
    CertAlertInfo.getDescriptor();
    CryptoAlertInfo.getDescriptor();
    FileAlertInfo.getDescriptor();
    IiotAlertInfo.getDescriptor();
    IoaAlertInfo.getDescriptor();
    IobAlertInfo.getDescriptor();
    IocAlertInfo.getDescriptor();
    MailAlertInfo.getDescriptor();
    MobileAlertInfo.getDescriptor();
    ProtoAlertInfo.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
