package com.geeksec.entity.pojo;

import com.geeksec.entity.po.Email;
import com.geeksec.entity.trans.EmailTrans;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.message.MailAlertInfo;
import lombok.Data;

import java.util.List;

/**
 * @author: jerryzhou
 * @date: 2024/8/5 14:16
 * @Description:
 **/
@Data
public class EmailAlertTrans {
    private String attackId;
    private long attackTime;
    private long attackTypeCode;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private EmailTrans emailTrans;

    private MailAlertInfo.MAIL_ALERT_INFO emailAlertInfo;

    // 对于文件来说 可能会存在邮件的收发者
    // 发起者统一为单个个体
    private Email emailSender;
    // 可能会有多个收件人
    private List<Email> receiverList;
}
