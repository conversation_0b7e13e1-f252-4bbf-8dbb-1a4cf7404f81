package com.geeksec.transfer.handle;

import com.geeksec.common.utils.IpNetUtils;
import com.geeksec.common.utils.MD5;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.base.IpInfo;
import org.apache.flink.types.Row;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: jerryzhou
 * @date: 2024/7/22 19:30
 * @Description: IP实体转义器&处理器
 **/
public class IPTransHandler {

    /**
     * IP实体转义器方法，所有的IP实体通过这个方法进行获取
     *
     * @param ipInfo
     * @return
     */
    public static IPTrans transIP(IpInfo.IP_INFO ipInfo) {
        IPTrans ipTrans = new IPTrans();
        ipTrans.setIPAddr(ipInfo.getIp());
        ipTrans.setCity(ipInfo.getIpCity());
        ipTrans.setCountry(ipInfo.getIpCountry());
        ipTrans.setOrg(ipInfo.getIpOrg());
        ipTrans.setLatitude(ipInfo.getIpLatitude());
        ipTrans.setLongitude(ipInfo.getIpLongitude());
        ipTrans.setISP(ipInfo.getIpIsp());
        ipTrans.setAS(ipInfo.getIpAsn());
        return ipTrans;
    }

    public static List<Row> getAllIpRows(IPTrans sIp, IPTrans dIp) {
        List<Row> rows = new ArrayList<>();

        Row sIpRow = new Row(10);
        sIpRow.setField(0, "TAG_IP");
        sIpRow.setField(1, sIp.getIPAddr());  // vid
        sIpRow.setField(2, sIp.getIPAddr());
        if (Boolean.TRUE.equals(IpNetUtils.isValidIPV4(sIp.getIPAddr()))) {
            sIpRow.setField(3, "V4");
        } else {
            sIpRow.setField(3, "V6");
        }
        sIpRow.setField(4, sIp.getCity());
        sIpRow.setField(5, sIp.getCountry());
        sIpRow.setField(6, sIp.getLatitude());
        sIpRow.setField(7, sIp.getLongitude());
        sIpRow.setField(8, sIp.getISP());
        sIpRow.setField(9, sIp.getAS());
        rows.add(sIpRow);

        Row dipRow = new Row(10);
        dipRow.setField(0, "TAG_IP");
        dipRow.setField(1, dIp.getIPAddr());  // vid
        dipRow.setField(2, dIp.getIPAddr());
        if (Boolean.TRUE.equals(IpNetUtils.isValidIPV4(dIp.getIPAddr()))) {
            dipRow.setField(3, "V4");
        } else {
            dipRow.setField(3, "V6");
        }
        dipRow.setField(4, dIp.getCity());
        dipRow.setField(5, dIp.getCountry());
        dipRow.setField(6, dIp.getLatitude());
        dipRow.setField(7, dIp.getLongitude());
        dipRow.setField(8, dIp.getISP());
        dipRow.setField(9, dIp.getAS());
        rows.add(dipRow);

        Row sIpOrgRow = new Row(3);
        String sIpOrg = sIp.getOrg();
        sIpOrgRow.setField(0, "TAG_ORG");
        sIpOrgRow.setField(1, MD5.getMd5(sIpOrg));
        sIpOrgRow.setField(2, sIpOrg);
        rows.add(sIpOrgRow);

        Row sIpBelongToRow = new Row(4);
        sIpBelongToRow.setField(0, "EDGE_ip_belong_to_org");
        sIpBelongToRow.setField(1, sIp.getIPAddr());
        sIpBelongToRow.setField(2, MD5.getMd5(sIp.getOrg()));
        sIpBelongToRow.setField(3, 0);  // rank
        rows.add(sIpBelongToRow);

        Row dIpOrgRow = new Row(3);
        String dIpOrg = dIp.getOrg();
        dIpOrgRow.setField(0, "TAG_ORG");
        dIpOrgRow.setField(1, MD5.getMd5(dIpOrg));
        dIpOrgRow.setField(2, dIpOrg);
        rows.add(dIpOrgRow);

        Row dIpBelongToRow = new Row(4);
        dIpBelongToRow.setField(0, "EDGE_ip_belong_to_org");
        dIpBelongToRow.setField(1, dIp.getIPAddr());
        dIpBelongToRow.setField(2, MD5.getMd5(dIp.getOrg()));
        dIpBelongToRow.setField(3, 0);  // rank
        rows.add(dIpBelongToRow);

        return rows;
    }
}