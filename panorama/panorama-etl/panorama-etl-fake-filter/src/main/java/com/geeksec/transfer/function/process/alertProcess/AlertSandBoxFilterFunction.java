package com.geeksec.transfer.function.process.alertProcess;

import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.common.utils.RedisUtils;
import com.geeksec.proto.AlertLog;
import com.geeksec.proto.message.FileAlertInfo;
import io.rebloom.client.Client;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;

/**
 * <AUTHOR>
 */
public class AlertSandBoxFilterFunction extends ProcessFunction<AlertLog.ALERT_LOG,AlertLog.ALERT_LOG> {

    private static final Logger logger = LoggerFactory.getLogger(AlertSandBoxFilterFunction.class);

    // 布隆过滤器链接实例
    private Client bloomClient;

    // JedisPool连接池实例
    private JedisPool jedisPool;

    // 文件MD5过滤布隆过滤器KEY
    private static final String FILE_MD5_BLOOM_FILTER_KEY = "file_md5_filter";

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        ParameterTool globalJobParameters = (ParameterTool)
                getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        String redisHost = globalJobParameters.get("redis.host.addr");
        Integer redisPort = globalJobParameters.getInt("redis.host.port");
        jedisPool = RedisUtils.getJedisPool(redisHost,redisPort);
        bloomClient = new Client(jedisPool);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (bloomClient != null) {
            bloomClient.close();
        }
    }


    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context context, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        try {
            if(checkFakeAlert(alertLog.getFileAlertInfo(),alertLog)){
                context.output(FilterOutPutTagConstant.SANDBOX_FILTER_TERMINATE,alertLog);
            }
            context.output(FilterOutPutTagConstant.SANDBOX_FILTER_CONTINUE,alertLog);
        } catch (Exception e) {
            logger.error("Error processing sandbox alert log: {}", alertLog.getGuid(), e);
            context.output(FilterOutPutTagConstant.SANDBOX_FILTER_CONTINUE,alertLog);
        }
    }

    private boolean checkFakeAlert(FileAlertInfo.FILE_ALERT_INFO sandboxAlertLog, AlertLog.ALERT_LOG alertLog) {
        String fileMd5 = sandboxAlertLog.getFileMd5();
        try {
            // 若文件MD5值存在于过滤器重，则当前为虚警
            if (bloomClient.exists(FILE_MD5_BLOOM_FILTER_KEY, fileMd5)) {
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("Error checking for fake alert: {}", alertLog.getGuid(), e);
            return false;
        }
    }
}