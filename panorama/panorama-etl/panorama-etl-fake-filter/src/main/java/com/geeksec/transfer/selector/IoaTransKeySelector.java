package com.geeksec.transfer.selector;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple4;

import static com.geeksec.task.PanoramaFakeAlertFilterTask.IOA_KEY;

/**
 * <AUTHOR>
 */
public class IoaTransKeySelector implements KeySelector<AlertLog.ALERT_LOG, Tuple4<String, String, String, String>> {

    /**
     * 使用攻击方-受害方-IOA规则号作为Key
     * @param alertLog 告警日志
     * @return Tuple3<String, String, String> 攻击方IP，受害者IP，IOA规则号
     */
    @Override
    public Tuple4<String, String, String, String> getKey(AlertLog.ALERT_LOG alertLog) throws Exception {
        String vipAddr = alertLog.getVip().getIp();
        String aipAddr = alertLog.getAip().getIp();
        String ioaRuleId = String.valueOf(alertLog.getIoaAlertInfo().getIoaRule());
        return new Tuple4<>(IOA_KEY,aipAddr,vipAddr,ioaRuleId);
    }
}
