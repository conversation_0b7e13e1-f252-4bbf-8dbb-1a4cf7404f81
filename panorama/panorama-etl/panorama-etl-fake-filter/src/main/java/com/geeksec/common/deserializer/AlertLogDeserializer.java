package com.geeksec.common.deserializer;

import com.geeksec.proto.AlertLog;
import org.apache.commons.lang.SerializationException;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.util.Collector;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.io.IOException;

/**
 * @author: jerry<PERSON>
 * @date: 2024/12/3 15:19
 * @Description:
 **/
public class AlertLogDeserializer implements KafkaRecordDeserializationSchema<AlertLog.ALERT_LOG> {
    @Override
    public TypeInformation<AlertLog.ALERT_LOG> getProducedType() {
        return TypeInformation.of(AlertLog.ALERT_LOG.class);
    }

    @Override
    public void deserialize(ConsumerRecord<byte[], byte[]> record, Collector<AlertLog.ALERT_LOG> collector) throws IOException {
        byte[] values = record.value();
        if (values != null && values.length > 0){
            try{
                collector.collect(AlertLog.ALERT_LOG.parseFrom(values));
            }catch (Exception e){
                throw new SerializationException("Error when serializing Alert Log Info byte[]" +e );
            }
        }
    }
}
