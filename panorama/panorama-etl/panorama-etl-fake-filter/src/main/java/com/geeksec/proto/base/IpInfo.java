package com.geeksec.proto.base;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: IP_INFO.proto
// Protobuf Java Version: 4.29.4

public final class IpInfo {
  private IpInfo() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 4,
      /* suffix= */ "",
      IpInfo.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface IP_INFOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:IP_INFO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    boolean hasIp();
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     * @return The ip.
     */
    String getIp();
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     * @return The bytes for ip.
     */
    com.google.protobuf.ByteString
        getIpBytes();

    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     * @return Whether the port field is set.
     */
    boolean hasPort();
    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     * @return The port.
     */
    int getPort();

    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     * @return Whether the ipCountry field is set.
     */
    boolean hasIpCountry();
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     * @return The ipCountry.
     */
    String getIpCountry();
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     * @return The bytes for ipCountry.
     */
    com.google.protobuf.ByteString
        getIpCountryBytes();

    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     * @return Whether the ipStat field is set.
     */
    boolean hasIpStat();
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     * @return The ipStat.
     */
    String getIpStat();
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     * @return The bytes for ipStat.
     */
    com.google.protobuf.ByteString
        getIpStatBytes();

    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     * @return Whether the ipCity field is set.
     */
    boolean hasIpCity();
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     * @return The ipCity.
     */
    String getIpCity();
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     * @return The bytes for ipCity.
     */
    com.google.protobuf.ByteString
        getIpCityBytes();

    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     * @return Whether the ipOrg field is set.
     */
    boolean hasIpOrg();
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     * @return The ipOrg.
     */
    String getIpOrg();
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     * @return The bytes for ipOrg.
     */
    com.google.protobuf.ByteString
        getIpOrgBytes();

    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     * @return Whether the ipLongitude field is set.
     */
    boolean hasIpLongitude();
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     * @return The ipLongitude.
     */
    double getIpLongitude();

    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     * @return Whether the ipLatitude field is set.
     */
    boolean hasIpLatitude();
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     * @return The ipLatitude.
     */
    double getIpLatitude();

    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     * @return Whether the ipIsp field is set.
     */
    boolean hasIpIsp();
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     * @return The ipIsp.
     */
    String getIpIsp();
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     * @return The bytes for ipIsp.
     */
    com.google.protobuf.ByteString
        getIpIspBytes();

    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     * @return Whether the ipAsn field is set.
     */
    boolean hasIpAsn();
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     * @return The ipAsn.
     */
    String getIpAsn();
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     * @return The bytes for ipAsn.
     */
    com.google.protobuf.ByteString
        getIpAsnBytes();

    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     * @return Whether the ipTag field is set.
     */
    boolean hasIpTag();
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     * @return The ipTag.
     */
    String getIpTag();
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     * @return The bytes for ipTag.
     */
    com.google.protobuf.ByteString
        getIpTagBytes();
  }
  /**
   * Protobuf type {@code IP_INFO}
   */
  public static final class IP_INFO extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:IP_INFO)
      IP_INFOOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 4,
        /* suffix= */ "",
        IP_INFO.class.getName());
    }
    // Use IP_INFO.newBuilder() to construct.
    private IP_INFO(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private IP_INFO() {
      ip_ = "";
      ipCountry_ = "";
      ipStat_ = "";
      ipCity_ = "";
      ipOrg_ = "";
      ipIsp_ = "";
      ipAsn_ = "";
      ipTag_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return IpInfo.internal_static_IP_INFO_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return IpInfo.internal_static_IP_INFO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              IP_INFO.class, Builder.class);
    }

    private int bitField0_;
    public static final int IP_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile Object ip_ = "";
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     * @return Whether the ip field is set.
     */
    @Override
    public boolean hasIp() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     * @return The ip.
     */
    @Override
    public String getIp() {
      Object ref = ip_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ip_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * IP地址
     * </pre>
     *
     * <code>required string ip = 1;</code>
     * @return The bytes for ip.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpBytes() {
      Object ref = ip_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PORT_FIELD_NUMBER = 2;
    private int port_ = 0;
    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     * @return Whether the port field is set.
     */
    @Override
    public boolean hasPort() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 端口
     * </pre>
     *
     * <code>required uint32 port = 2;</code>
     * @return The port.
     */
    @Override
    public int getPort() {
      return port_;
    }

    public static final int IP_COUNTRY_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile Object ipCountry_ = "";
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     * @return Whether the ipCountry field is set.
     */
    @Override
    public boolean hasIpCountry() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     * @return The ipCountry.
     */
    @Override
    public String getIpCountry() {
      Object ref = ipCountry_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipCountry_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 国家名
     * </pre>
     *
     * <code>required string ip_country = 3;</code>
     * @return The bytes for ipCountry.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpCountryBytes() {
      Object ref = ipCountry_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipCountry_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_STAT_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile Object ipStat_ = "";
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     * @return Whether the ipStat field is set.
     */
    @Override
    public boolean hasIpStat() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     * @return The ipStat.
     */
    @Override
    public String getIpStat() {
      Object ref = ipStat_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipStat_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 省份/洲名
     * </pre>
     *
     * <code>required string ip_stat = 4;</code>
     * @return The bytes for ipStat.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpStatBytes() {
      Object ref = ipStat_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipStat_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_CITY_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile Object ipCity_ = "";
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     * @return Whether the ipCity field is set.
     */
    @Override
    public boolean hasIpCity() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     * @return The ipCity.
     */
    @Override
    public String getIpCity() {
      Object ref = ipCity_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipCity_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 城市名
     * </pre>
     *
     * <code>required string ip_city = 5;</code>
     * @return The bytes for ipCity.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpCityBytes() {
      Object ref = ipCity_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipCity_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_ORG_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private volatile Object ipOrg_ = "";
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     * @return Whether the ipOrg field is set.
     */
    @Override
    public boolean hasIpOrg() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     * @return The ipOrg.
     */
    @Override
    public String getIpOrg() {
      Object ref = ipOrg_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipOrg_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 机构名
     * </pre>
     *
     * <code>required string ip_org = 6;</code>
     * @return The bytes for ipOrg.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpOrgBytes() {
      Object ref = ipOrg_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipOrg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_LONGITUDE_FIELD_NUMBER = 7;
    private double ipLongitude_ = 0D;
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     * @return Whether the ipLongitude field is set.
     */
    @Override
    public boolean hasIpLongitude() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 纬度
     * </pre>
     *
     * <code>required double ip_longitude = 7;</code>
     * @return The ipLongitude.
     */
    @Override
    public double getIpLongitude() {
      return ipLongitude_;
    }

    public static final int IP_LATITUDE_FIELD_NUMBER = 8;
    private double ipLatitude_ = 0D;
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     * @return Whether the ipLatitude field is set.
     */
    @Override
    public boolean hasIpLatitude() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 经度
     * </pre>
     *
     * <code>required double ip_latitude = 8;</code>
     * @return The ipLatitude.
     */
    @Override
    public double getIpLatitude() {
      return ipLatitude_;
    }

    public static final int IP_ISP_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private volatile Object ipIsp_ = "";
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     * @return Whether the ipIsp field is set.
     */
    @Override
    public boolean hasIpIsp() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     * @return The ipIsp.
     */
    @Override
    public String getIpIsp() {
      Object ref = ipIsp_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipIsp_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 运营商
     * </pre>
     *
     * <code>required string ip_isp = 9;</code>
     * @return The bytes for ipIsp.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpIspBytes() {
      Object ref = ipIsp_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipIsp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_ASN_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile Object ipAsn_ = "";
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     * @return Whether the ipAsn field is set.
     */
    @Override
    public boolean hasIpAsn() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     * @return The ipAsn.
     */
    @Override
    public String getIpAsn() {
      Object ref = ipAsn_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipAsn_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * AS信息
     * </pre>
     *
     * <code>required string ip_asn = 10;</code>
     * @return The bytes for ipAsn.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpAsnBytes() {
      Object ref = ipAsn_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipAsn_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int IP_TAG_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private volatile Object ipTag_ = "";
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     * @return Whether the ipTag field is set.
     */
    @Override
    public boolean hasIpTag() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     * @return The ipTag.
     */
    @Override
    public String getIpTag() {
      Object ref = ipTag_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ipTag_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 标签
     * </pre>
     *
     * <code>optional string ip_tag = 11;</code>
     * @return The bytes for ipTag.
     */
    @Override
    public com.google.protobuf.ByteString
        getIpTagBytes() {
      Object ref = ipTag_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        ipTag_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      if (!hasIp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPort()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpCountry()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpStat()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpCity()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpOrg()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpLongitude()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpLatitude()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpIsp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIpAsn()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt32(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, ipCountry_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, ipStat_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 5, ipCity_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 6, ipOrg_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeDouble(7, ipLongitude_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeDouble(8, ipLatitude_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 9, ipIsp_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, ipAsn_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 11, ipTag_);
      }
      getUnknownFields().writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(1, ip_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, port_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, ipCountry_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, ipStat_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(5, ipCity_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(6, ipOrg_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(7, ipLongitude_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeDoubleSize(8, ipLatitude_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(9, ipIsp_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, ipAsn_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(11, ipTag_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof IP_INFO)) {
        return super.equals(obj);
      }
      IP_INFO other = (IP_INFO) obj;

      if (hasIp() != other.hasIp()) return false;
      if (hasIp()) {
        if (!getIp()
            .equals(other.getIp())) return false;
      }
      if (hasPort() != other.hasPort()) return false;
      if (hasPort()) {
        if (getPort()
            != other.getPort()) return false;
      }
      if (hasIpCountry() != other.hasIpCountry()) return false;
      if (hasIpCountry()) {
        if (!getIpCountry()
            .equals(other.getIpCountry())) return false;
      }
      if (hasIpStat() != other.hasIpStat()) return false;
      if (hasIpStat()) {
        if (!getIpStat()
            .equals(other.getIpStat())) return false;
      }
      if (hasIpCity() != other.hasIpCity()) return false;
      if (hasIpCity()) {
        if (!getIpCity()
            .equals(other.getIpCity())) return false;
      }
      if (hasIpOrg() != other.hasIpOrg()) return false;
      if (hasIpOrg()) {
        if (!getIpOrg()
            .equals(other.getIpOrg())) return false;
      }
      if (hasIpLongitude() != other.hasIpLongitude()) return false;
      if (hasIpLongitude()) {
        if (Double.doubleToLongBits(getIpLongitude())
            != Double.doubleToLongBits(
                other.getIpLongitude())) return false;
      }
      if (hasIpLatitude() != other.hasIpLatitude()) return false;
      if (hasIpLatitude()) {
        if (Double.doubleToLongBits(getIpLatitude())
            != Double.doubleToLongBits(
                other.getIpLatitude())) return false;
      }
      if (hasIpIsp() != other.hasIpIsp()) return false;
      if (hasIpIsp()) {
        if (!getIpIsp()
            .equals(other.getIpIsp())) return false;
      }
      if (hasIpAsn() != other.hasIpAsn()) return false;
      if (hasIpAsn()) {
        if (!getIpAsn()
            .equals(other.getIpAsn())) return false;
      }
      if (hasIpTag() != other.hasIpTag()) return false;
      if (hasIpTag()) {
        if (!getIpTag()
            .equals(other.getIpTag())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIp()) {
        hash = (37 * hash) + IP_FIELD_NUMBER;
        hash = (53 * hash) + getIp().hashCode();
      }
      if (hasPort()) {
        hash = (37 * hash) + PORT_FIELD_NUMBER;
        hash = (53 * hash) + getPort();
      }
      if (hasIpCountry()) {
        hash = (37 * hash) + IP_COUNTRY_FIELD_NUMBER;
        hash = (53 * hash) + getIpCountry().hashCode();
      }
      if (hasIpStat()) {
        hash = (37 * hash) + IP_STAT_FIELD_NUMBER;
        hash = (53 * hash) + getIpStat().hashCode();
      }
      if (hasIpCity()) {
        hash = (37 * hash) + IP_CITY_FIELD_NUMBER;
        hash = (53 * hash) + getIpCity().hashCode();
      }
      if (hasIpOrg()) {
        hash = (37 * hash) + IP_ORG_FIELD_NUMBER;
        hash = (53 * hash) + getIpOrg().hashCode();
      }
      if (hasIpLongitude()) {
        hash = (37 * hash) + IP_LONGITUDE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            Double.doubleToLongBits(getIpLongitude()));
      }
      if (hasIpLatitude()) {
        hash = (37 * hash) + IP_LATITUDE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            Double.doubleToLongBits(getIpLatitude()));
      }
      if (hasIpIsp()) {
        hash = (37 * hash) + IP_ISP_FIELD_NUMBER;
        hash = (53 * hash) + getIpIsp().hashCode();
      }
      if (hasIpAsn()) {
        hash = (37 * hash) + IP_ASN_FIELD_NUMBER;
        hash = (53 * hash) + getIpAsn().hashCode();
      }
      if (hasIpTag()) {
        hash = (37 * hash) + IP_TAG_FIELD_NUMBER;
        hash = (53 * hash) + getIpTag().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static IP_INFO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IP_INFO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IP_INFO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IP_INFO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IP_INFO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static IP_INFO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static IP_INFO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IP_INFO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static IpInfo.IP_INFO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static IpInfo.IP_INFO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static IpInfo.IP_INFO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static IpInfo.IP_INFO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(IpInfo.IP_INFO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code IP_INFO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:IP_INFO)
        IpInfo.IP_INFOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return IpInfo.internal_static_IP_INFO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return IpInfo.internal_static_IP_INFO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                IpInfo.IP_INFO.class, IpInfo.IP_INFO.Builder.class);
      }

      // Construct using IpInfo.IP_INFO.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        ip_ = "";
        port_ = 0;
        ipCountry_ = "";
        ipStat_ = "";
        ipCity_ = "";
        ipOrg_ = "";
        ipLongitude_ = 0D;
        ipLatitude_ = 0D;
        ipIsp_ = "";
        ipAsn_ = "";
        ipTag_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return IpInfo.internal_static_IP_INFO_descriptor;
      }

      @java.lang.Override
      public IpInfo.IP_INFO getDefaultInstanceForType() {
        return IpInfo.IP_INFO.getDefaultInstance();
      }

      @java.lang.Override
      public IpInfo.IP_INFO build() {
        IpInfo.IP_INFO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public IpInfo.IP_INFO buildPartial() {
        IpInfo.IP_INFO result = new IpInfo.IP_INFO(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(IpInfo.IP_INFO result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ip_ = ip_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.port_ = port_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.ipCountry_ = ipCountry_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.ipStat_ = ipStat_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.ipCity_ = ipCity_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.ipOrg_ = ipOrg_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.ipLongitude_ = ipLongitude_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.ipLatitude_ = ipLatitude_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.ipIsp_ = ipIsp_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.ipAsn_ = ipAsn_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.ipTag_ = ipTag_;
          to_bitField0_ |= 0x00000400;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof IpInfo.IP_INFO) {
          return mergeFrom((IpInfo.IP_INFO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(IpInfo.IP_INFO other) {
        if (other == IpInfo.IP_INFO.getDefaultInstance()) return this;
        if (other.hasIp()) {
          ip_ = other.ip_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (other.hasPort()) {
          setPort(other.getPort());
        }
        if (other.hasIpCountry()) {
          ipCountry_ = other.ipCountry_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (other.hasIpStat()) {
          ipStat_ = other.ipStat_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (other.hasIpCity()) {
          ipCity_ = other.ipCity_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.hasIpOrg()) {
          ipOrg_ = other.ipOrg_;
          bitField0_ |= 0x00000020;
          onChanged();
        }
        if (other.hasIpLongitude()) {
          setIpLongitude(other.getIpLongitude());
        }
        if (other.hasIpLatitude()) {
          setIpLatitude(other.getIpLatitude());
        }
        if (other.hasIpIsp()) {
          ipIsp_ = other.ipIsp_;
          bitField0_ |= 0x00000100;
          onChanged();
        }
        if (other.hasIpAsn()) {
          ipAsn_ = other.ipAsn_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (other.hasIpTag()) {
          ipTag_ = other.ipTag_;
          bitField0_ |= 0x00000400;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        if (!hasIp()) {
          return false;
        }
        if (!hasPort()) {
          return false;
        }
        if (!hasIpCountry()) {
          return false;
        }
        if (!hasIpStat()) {
          return false;
        }
        if (!hasIpCity()) {
          return false;
        }
        if (!hasIpOrg()) {
          return false;
        }
        if (!hasIpLongitude()) {
          return false;
        }
        if (!hasIpLatitude()) {
          return false;
        }
        if (!hasIpIsp()) {
          return false;
        }
        if (!hasIpAsn()) {
          return false;
        }
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                ip_ = input.readBytes();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                port_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                ipCountry_ = input.readBytes();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                ipStat_ = input.readBytes();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                ipCity_ = input.readBytes();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                ipOrg_ = input.readBytes();
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              case 57: {
                ipLongitude_ = input.readDouble();
                bitField0_ |= 0x00000040;
                break;
              } // case 57
              case 65: {
                ipLatitude_ = input.readDouble();
                bitField0_ |= 0x00000080;
                break;
              } // case 65
              case 74: {
                ipIsp_ = input.readBytes();
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              case 82: {
                ipAsn_ = input.readBytes();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 90: {
                ipTag_ = input.readBytes();
                bitField0_ |= 0x00000400;
                break;
              } // case 90
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object ip_ = "";
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       * @return Whether the ip field is set.
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       * @return The ip.
       */
      public java.lang.String getIp() {
        java.lang.Object ref = ip_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ip_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       * @return The bytes for ip.
       */
      public com.google.protobuf.ByteString
          getIpBytes() {
        java.lang.Object ref = ip_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ip_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       * @param value The ip to set.
       * @return This builder for chaining.
       */
      public Builder setIp(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ip_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIp() {
        ip_ = getDefaultInstance().getIp();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * IP地址
       * </pre>
       *
       * <code>required string ip = 1;</code>
       * @param value The bytes for ip to set.
       * @return This builder for chaining.
       */
      public Builder setIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ip_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private int port_ ;
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       * @return Whether the port field is set.
       */
      @java.lang.Override
      public boolean hasPort() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       * @return The port.
       */
      @java.lang.Override
      public int getPort() {
        return port_;
      }
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       * @param value The port to set.
       * @return This builder for chaining.
       */
      public Builder setPort(int value) {

        port_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 端口
       * </pre>
       *
       * <code>required uint32 port = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPort() {
        bitField0_ = (bitField0_ & ~0x00000002);
        port_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object ipCountry_ = "";
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       * @return Whether the ipCountry field is set.
       */
      public boolean hasIpCountry() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       * @return The ipCountry.
       */
      public java.lang.String getIpCountry() {
        java.lang.Object ref = ipCountry_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipCountry_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       * @return The bytes for ipCountry.
       */
      public com.google.protobuf.ByteString
          getIpCountryBytes() {
        java.lang.Object ref = ipCountry_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipCountry_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       * @param value The ipCountry to set.
       * @return This builder for chaining.
       */
      public Builder setIpCountry(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipCountry_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpCountry() {
        ipCountry_ = getDefaultInstance().getIpCountry();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国家名
       * </pre>
       *
       * <code>required string ip_country = 3;</code>
       * @param value The bytes for ipCountry to set.
       * @return This builder for chaining.
       */
      public Builder setIpCountryBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipCountry_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object ipStat_ = "";
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       * @return Whether the ipStat field is set.
       */
      public boolean hasIpStat() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       * @return The ipStat.
       */
      public java.lang.String getIpStat() {
        java.lang.Object ref = ipStat_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipStat_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       * @return The bytes for ipStat.
       */
      public com.google.protobuf.ByteString
          getIpStatBytes() {
        java.lang.Object ref = ipStat_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipStat_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       * @param value The ipStat to set.
       * @return This builder for chaining.
       */
      public Builder setIpStat(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipStat_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpStat() {
        ipStat_ = getDefaultInstance().getIpStat();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 省份/洲名
       * </pre>
       *
       * <code>required string ip_stat = 4;</code>
       * @param value The bytes for ipStat to set.
       * @return This builder for chaining.
       */
      public Builder setIpStatBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipStat_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.lang.Object ipCity_ = "";
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       * @return Whether the ipCity field is set.
       */
      public boolean hasIpCity() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       * @return The ipCity.
       */
      public java.lang.String getIpCity() {
        java.lang.Object ref = ipCity_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipCity_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       * @return The bytes for ipCity.
       */
      public com.google.protobuf.ByteString
          getIpCityBytes() {
        java.lang.Object ref = ipCity_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipCity_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       * @param value The ipCity to set.
       * @return This builder for chaining.
       */
      public Builder setIpCity(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipCity_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpCity() {
        ipCity_ = getDefaultInstance().getIpCity();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 城市名
       * </pre>
       *
       * <code>required string ip_city = 5;</code>
       * @param value The bytes for ipCity to set.
       * @return This builder for chaining.
       */
      public Builder setIpCityBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipCity_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private java.lang.Object ipOrg_ = "";
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       * @return Whether the ipOrg field is set.
       */
      public boolean hasIpOrg() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       * @return The ipOrg.
       */
      public java.lang.String getIpOrg() {
        java.lang.Object ref = ipOrg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipOrg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       * @return The bytes for ipOrg.
       */
      public com.google.protobuf.ByteString
          getIpOrgBytes() {
        java.lang.Object ref = ipOrg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipOrg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       * @param value The ipOrg to set.
       * @return This builder for chaining.
       */
      public Builder setIpOrg(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipOrg_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpOrg() {
        ipOrg_ = getDefaultInstance().getIpOrg();
        bitField0_ = (bitField0_ & ~0x00000020);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 机构名
       * </pre>
       *
       * <code>required string ip_org = 6;</code>
       * @param value The bytes for ipOrg to set.
       * @return This builder for chaining.
       */
      public Builder setIpOrgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipOrg_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }

      private double ipLongitude_ ;
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       * @return Whether the ipLongitude field is set.
       */
      @java.lang.Override
      public boolean hasIpLongitude() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       * @return The ipLongitude.
       */
      @java.lang.Override
      public double getIpLongitude() {
        return ipLongitude_;
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       * @param value The ipLongitude to set.
       * @return This builder for chaining.
       */
      public Builder setIpLongitude(double value) {

        ipLongitude_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 纬度
       * </pre>
       *
       * <code>required double ip_longitude = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpLongitude() {
        bitField0_ = (bitField0_ & ~0x00000040);
        ipLongitude_ = 0D;
        onChanged();
        return this;
      }

      private double ipLatitude_ ;
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       * @return Whether the ipLatitude field is set.
       */
      @java.lang.Override
      public boolean hasIpLatitude() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       * @return The ipLatitude.
       */
      @java.lang.Override
      public double getIpLatitude() {
        return ipLatitude_;
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       * @param value The ipLatitude to set.
       * @return This builder for chaining.
       */
      public Builder setIpLatitude(double value) {

        ipLatitude_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 经度
       * </pre>
       *
       * <code>required double ip_latitude = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpLatitude() {
        bitField0_ = (bitField0_ & ~0x00000080);
        ipLatitude_ = 0D;
        onChanged();
        return this;
      }

      private java.lang.Object ipIsp_ = "";
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       * @return Whether the ipIsp field is set.
       */
      public boolean hasIpIsp() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       * @return The ipIsp.
       */
      public java.lang.String getIpIsp() {
        java.lang.Object ref = ipIsp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipIsp_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       * @return The bytes for ipIsp.
       */
      public com.google.protobuf.ByteString
          getIpIspBytes() {
        java.lang.Object ref = ipIsp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipIsp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       * @param value The ipIsp to set.
       * @return This builder for chaining.
       */
      public Builder setIpIsp(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipIsp_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpIsp() {
        ipIsp_ = getDefaultInstance().getIpIsp();
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 运营商
       * </pre>
       *
       * <code>required string ip_isp = 9;</code>
       * @param value The bytes for ipIsp to set.
       * @return This builder for chaining.
       */
      public Builder setIpIspBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipIsp_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }

      private java.lang.Object ipAsn_ = "";
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       * @return Whether the ipAsn field is set.
       */
      public boolean hasIpAsn() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       * @return The ipAsn.
       */
      public java.lang.String getIpAsn() {
        java.lang.Object ref = ipAsn_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipAsn_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       * @return The bytes for ipAsn.
       */
      public com.google.protobuf.ByteString
          getIpAsnBytes() {
        java.lang.Object ref = ipAsn_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipAsn_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       * @param value The ipAsn to set.
       * @return This builder for chaining.
       */
      public Builder setIpAsn(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipAsn_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpAsn() {
        ipAsn_ = getDefaultInstance().getIpAsn();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * AS信息
       * </pre>
       *
       * <code>required string ip_asn = 10;</code>
       * @param value The bytes for ipAsn to set.
       * @return This builder for chaining.
       */
      public Builder setIpAsnBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipAsn_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private java.lang.Object ipTag_ = "";
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       * @return Whether the ipTag field is set.
       */
      public boolean hasIpTag() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       * @return The ipTag.
       */
      public java.lang.String getIpTag() {
        java.lang.Object ref = ipTag_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            ipTag_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       * @return The bytes for ipTag.
       */
      public com.google.protobuf.ByteString
          getIpTagBytes() {
        java.lang.Object ref = ipTag_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ipTag_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       * @param value The ipTag to set.
       * @return This builder for chaining.
       */
      public Builder setIpTag(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        ipTag_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearIpTag() {
        ipTag_ = getDefaultInstance().getIpTag();
        bitField0_ = (bitField0_ & ~0x00000400);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标签
       * </pre>
       *
       * <code>optional string ip_tag = 11;</code>
       * @param value The bytes for ipTag to set.
       * @return This builder for chaining.
       */
      public Builder setIpTagBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        ipTag_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:IP_INFO)
    }

    // @@protoc_insertion_point(class_scope:IP_INFO)
    private static final IpInfo.IP_INFO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new IpInfo.IP_INFO();
    }

    public static IpInfo.IP_INFO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<IP_INFO>
        PARSER = new com.google.protobuf.AbstractParser<IP_INFO>() {
      @java.lang.Override
      public IP_INFO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<IP_INFO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<IP_INFO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public IpInfo.IP_INFO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_IP_INFO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_IP_INFO_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rIP_INFO.proto\"\304\001\n\007IP_INFO\022\n\n\002ip\030\001 \002(\t\022" +
      "\014\n\004port\030\002 \002(\r\022\022\n\nip_country\030\003 \002(\t\022\017\n\007ip_" +
      "stat\030\004 \002(\t\022\017\n\007ip_city\030\005 \002(\t\022\016\n\006ip_org\030\006 " +
      "\002(\t\022\024\n\014ip_longitude\030\007 \002(\001\022\023\n\013ip_latitude" +
      "\030\010 \002(\001\022\016\n\006ip_isp\030\t \002(\t\022\016\n\006ip_asn\030\n \002(\t\022\016" +
      "\n\006ip_tag\030\013 \001(\tB\010B\006IpInfo"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_IP_INFO_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_IP_INFO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_IP_INFO_descriptor,
        new java.lang.String[] { "Ip", "Port", "IpCountry", "IpStat", "IpCity", "IpOrg", "IpLongitude", "IpLatitude", "IpIsp", "IpAsn", "IpTag", });
    descriptor.resolveAllFeaturesImmutable();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
