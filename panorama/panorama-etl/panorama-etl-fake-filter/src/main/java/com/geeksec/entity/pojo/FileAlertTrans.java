package com.geeksec.entity.pojo;

import com.geeksec.common.utils.MailUtils;
import com.geeksec.entity.po.Email;
import com.geeksec.entity.trans.FileTrans;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.message.FileAlertInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class FileAlertTrans {
    private String attackId;
    private long attackTime;
    private long attackTypeCode;
    private String killChain;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;
    private FileTrans fileTrans;

    private FileAlertInfo.FILE_ALERT_INFO fileAlertInfo;

    private boolean fakeAlarm;
}
