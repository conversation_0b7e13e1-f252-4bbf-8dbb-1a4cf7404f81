package com.geeksec.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * 危害级别(severity)
 *
 */
public enum SeverityEnum {

    HIGH_RISK(3, "高危"),
    INTERMEDIATE_RISK(2, "中危"),
    LOW_RISK(1, "低危"),
    CUSTOM(4,"自定义");

    /**
     * 取值
     */
    private Integer code;

    /**
     * 危害级别
     */
    private String msg;

    SeverityEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, SeverityEnum> codeEnumMap = new HashMap<>();

    static {
        for (SeverityEnum e : SeverityEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        SeverityEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }

}
