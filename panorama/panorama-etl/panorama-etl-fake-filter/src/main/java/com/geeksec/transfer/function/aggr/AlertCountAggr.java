package com.geeksec.transfer.function.aggr;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.common.functions.AggregateFunction;

public class AlertCountAggr implements AggregateFunction<AlertLog.ALERT_LOG, Integer, Integer> {
    @Override
    public Integer createAccumulator() {
        return 0;
    }

    @Override
    public Integer add(AlertLog.ALERT_LOG alertLog, Integer accumulator) {
        return accumulator+1;
    }

    @Override
    public Integer getResult(Integer integer) {
        return integer;
    }

    @Override
    public Integer merge(Integer a, Integer b) {
        return a + b;
    }
}
