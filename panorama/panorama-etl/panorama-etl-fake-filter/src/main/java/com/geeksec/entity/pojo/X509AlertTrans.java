package com.geeksec.entity.pojo;

import com.geeksec.entity.trans.IPTrans;
import com.geeksec.entity.trans.X509CertTrans;
import com.geeksec.proto.message.IocAlertInfo;
import lombok.Data;

import java.util.List;

/**
 * @author: jerryzhou
 * @date: 2024/9/20 10:07
 * @Description:
 **/
@Data
public class X509AlertTrans {
    private String attackId;
    private long attackTime;
    private long attackTypeCode;
    private String killChain;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private List<X509CertTrans> x509Cert;

    private IocAlertInfo.IOC_ALERT_INFO iocAlertInfo;

    private boolean fakeAlarm;
}
