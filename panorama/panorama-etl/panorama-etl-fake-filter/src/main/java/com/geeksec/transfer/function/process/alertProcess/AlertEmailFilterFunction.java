package com.geeksec.transfer.function.process.alertProcess;

import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.common.utils.AlertTools;
import com.geeksec.common.utils.MailUtils;
import com.geeksec.common.utils.RedisUtils;
import com.geeksec.entity.po.Email;
import com.geeksec.entity.pojo.EmailAlertTrans;
import com.geeksec.entity.trans.EmailTrans;
import com.geeksec.proto.AlertLog;
import com.geeksec.proto.ProtocolMetadata;
import com.geeksec.proto.message.MailAlertInfo;
import com.geeksec.proto.protocol.EMAILInfoOuterClass;
import com.geeksec.transfer.handle.IPTransHandler;
import com.google.protobuf.InvalidProtocolBufferException;
import io.rebloom.client.Client;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.geeksec.transfer.function.process.prorocolProcess.ProtocolFilterFunction.FILE_MD5_BLOOM_FILTER_KEY;

/**
 * <AUTHOR>
 */
public class AlertEmailFilterFunction extends ProcessFunction<AlertLog.ALERT_LOG,AlertLog.ALERT_LOG> {

    private static final Logger logger = LoggerFactory.getLogger(AlertEmailFilterFunction.class);
    private static final int MILLISECONDS_TO_SECONDS = 1000;

    // 布隆过滤器链接实例
    private Client bloomClient;

    // JedisPool连接池实例
    private JedisPool jedisPool;

    // 邮件地址过滤布隆过滤器KEY
    private static final String EMAIL_ADDR_BLOOM_FILTER_KEY = "email_filter";
    private static final String EMAIL_FILTER_REGEX = "^(security|noc|soc|abuse)@.*\\\\..*$";
    private static final Pattern emailPattern = Pattern.compile(EMAIL_FILTER_REGEX, Pattern.CASE_INSENSITIVE);


    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        ParameterTool globalJobParameters = (ParameterTool)
                getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        String redisHost = globalJobParameters.get("redis.host.addr");
        Integer redisPort = globalJobParameters.getInt("redis.host.port");
        jedisPool = RedisUtils.getJedisPool(redisHost,redisPort);
        bloomClient = new Client(jedisPool);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (bloomClient != null) {
            bloomClient.close();
        }
    }

    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context context, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        try {
            EmailAlertTrans emailAlert = createEmailAlertTrans(alertLog);
            if(checkEmailFakeAlert(emailAlert.getEmailTrans())){
                context.output(FilterOutPutTagConstant.EMAIL_FILTER_TERMINATE,alertLog);
            }
            if(checkEmailAttFile(alertLog)){
                context.output(FilterOutPutTagConstant.EMAIL_FILTER_TERMINATE,alertLog);
            }
            context.output(FilterOutPutTagConstant.EMAIL_FILTER_CONTINUE,alertLog);
        } catch (Exception e) {
            logger.error("Error processing Email alert log: {}", alertLog.getGuid(), e);
            context.output(FilterOutPutTagConstant.EMAIL_FILTER_CONTINUE,alertLog);
        }
    }

    private boolean checkEmailAttFile(AlertLog.ALERT_LOG alertLog){
        try{
            ProtocolMetadata.MetaInfo  metaInfo = ProtocolMetadata.MetaInfo.parseFrom(alertLog.getMetaData());
            List<ProtocolMetadata.ProtocolInfo> protocolInfoList = metaInfo.getProtocolInfoList();
            List<String> attFileList = new  ArrayList<>();
            for (ProtocolMetadata.ProtocolInfo protocolInfo:protocolInfoList){
                EMAILInfoOuterClass.EMAILInfo emailInfo = EMAILInfoOuterClass.EMAILInfo.parseFrom(protocolInfo.toByteArray());
                List<String> attFile = AlertTools.convertByteStringListToStringList(emailInfo.getAttFileNameList());
                for (String md5:attFile){
                    AlertTools.addIfNotEmpty(attFileList,md5);
                }
            }
            for(String md5:attFileList){
                if(bloomClient.exists(FILE_MD5_BLOOM_FILTER_KEY,md5)){
                    return true;
                }
            }
            return false;
        }catch (InvalidProtocolBufferException e){
            logger.error("Error checking for fake SSL alert: {}", alertLog.getGuid(), e);
            return false;
        }
    }

    private EmailAlertTrans createEmailAlertTrans(AlertLog.ALERT_LOG alertLog) {
        EmailAlertTrans emailAlert = new EmailAlertTrans();
        MailAlertInfo.MAIL_ALERT_INFO emailAlertLog = alertLog.getMailAlertInfo();

        setBasicAlertInfo(emailAlert, alertLog);
        setIpInfo(emailAlert, alertLog);
        emailAlert.setEmailAlertInfo(emailAlertLog);
        emailAlert.setEmailTrans(createEmailTrans(emailAlertLog));

        return emailAlert;
    }

    private void setBasicAlertInfo(EmailAlertTrans emailAlert, AlertLog.ALERT_LOG alertLog) {
        emailAlert.setAttackId(alertLog.getGuid());
        emailAlert.setAttackTime(AlertTools.parseTimeToLong(alertLog.getTime()) / MILLISECONDS_TO_SECONDS);
        emailAlert.setAttackTypeCode(alertLog.getThreatType());
    }

    private void setIpInfo(EmailAlertTrans emailAlert, AlertLog.ALERT_LOG alertLog) {
        emailAlert.setSip(IPTransHandler.transIP(alertLog.getSip()));
        emailAlert.setDip(IPTransHandler.transIP(alertLog.getDip()));
        emailAlert.setAipAddr(alertLog.getAip().getIp());
        emailAlert.setVipAddr(alertLog.getVip().getIp());
    }

    private EmailTrans createEmailTrans(MailAlertInfo.MAIL_ALERT_INFO emailAlertLog) {
        EmailTrans emailTrans = new EmailTrans();
        emailTrans.setMailSenderRaw(emailAlertLog.getEmailSender());
        emailTrans.setMailReceiverRaw(emailAlertLog.getEmailReceiver());
        emailTrans.setMailSubject(emailAlertLog.getEmailSubject());
        emailTrans.setMailContent(emailAlertLog.getEmailContent());
        emailTrans.setFileMd5(emailAlertLog.getEmailAttachmentMd5());
        emailTrans.setIndustry(emailAlertLog.getEmailIndustry());
        emailTrans.setAnomalyTags(emailAlertLog.getEmailAnomalyTagsList());
        emailTrans.setAlertReason(emailAlertLog.getEmailAlertReason());
        return emailTrans;
    }

    private boolean checkEmailFakeAlert(EmailTrans emailTrans) {

        Email sender = MailUtils.parseEmailRaw(emailTrans.getMailSenderRaw()).get(0);
        List<Email> receiverList = MailUtils.parseEmailRaw(emailTrans.getMailReceiverRaw());

        List<String> emailAddrList = new ArrayList<>();
        emailAddrList.add(sender.getEmailAddr().split("@")[1]);

        // 只用获取当前email addr字符串中 @后面的部分
        for (Email receiver : receiverList) {
            emailAddrList.add(receiver.getEmailAddr().split("@")[1]);
        }

        try {
            // 1.匹配邮件地址是否存在于RedisBloom过滤器中
            for (String emailAddr : emailAddrList){
                if(bloomClient.exists(EMAIL_ADDR_BLOOM_FILTER_KEY,emailAddr)){
                    return false;
                }
            }

            // 通过正则表达式进行匹配
            for (String emailAddr : emailAddrList) {
                Matcher matcher = emailPattern.matcher(emailAddr);
                if (matcher.matches()) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            logger.error("当前邮件虚警检测异常,emailTrans->{}", emailTrans);
            return false;
        }
    }
}