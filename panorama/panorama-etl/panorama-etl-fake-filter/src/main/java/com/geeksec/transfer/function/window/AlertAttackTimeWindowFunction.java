package com.geeksec.transfer.function.window;

import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.common.utils.AlertTools;
import com.geeksec.proto.AlertLog;
import org.apache.flink.api.java.tuple.Tuple4;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;

import java.util.List;

import static com.geeksec.task.PanoramaFakeAlertFilterTask.IOC_KEY;
import static com.geeksec.task.PanoramaFakeAlertFilterTask.IOA_KEY;
import static com.geeksec.task.PanoramaFakeAlertFilterTask.CRYPTO_KEY;
import static com.geeksec.task.PanoramaFakeAlertFilterTask.EMAIL_KEY;
import static com.geeksec.task.PanoramaFakeAlertFilterTask.FILE_KEY;

/**
 * <AUTHOR>
 */
public class AlertAttackTimeWindowFunction extends ProcessWindowFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG, Tuple4<String, String, String, String>, TimeWindow> {

    /**
     * 解决的字段聚合问题：
     * LR_aggregate_value String 最近短时聚合值,根据不同的威胁类型采用不同的聚合策略
     * LR_first_alert_date int 最近短时首次告警时刻
     * LR_last_alert_date int 最近短时末次告警时刻
     * LR_alert_times int 最近短时告警次数
     */

    @Override
    public void process(Tuple4<String, String, String, String> stringTuple4, ProcessWindowFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG, Tuple4<String, String, String, String>, TimeWindow>.Context context, Iterable<AlertLog.ALERT_LOG> iterable, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        AlertLog.ALERT_LOG alertLogResult = null;
        long firstAttackTime = 0L;
        long lastAttackTime = 0L;
        int attackCount = 0;
        String aggregateValueAll = null;

        String aggregationType  = stringTuple4.getField(0);

        // 对于同一个Key键对的数据，进行统计并计算事件进行研判聚合，取最早的那一条告警作为到时候的攻击起始时间
        for (AlertLog.ALERT_LOG alertLog : iterable) {
            String aggregateValue = alertLog.getLRAggregateValue();

            if(alertLogResult==null){
                alertLogResult = alertLog;
            }
            long attackTime = AlertTools.parseTimeToLong(alertLog.getTime())/1000;

            if (firstAttackTime == 0L || attackTime < firstAttackTime) {
                firstAttackTime = attackTime;
            }
            if (lastAttackTime == 0L || attackTime > lastAttackTime) {
                lastAttackTime = attackTime;
            }
            attackCount += 1;

            if(aggregateValue==null || aggregateValue.isEmpty()){
                aggregateValueAll = updateAggregateValue(aggregateValueAll,aggregateValue);
            }else{
                switch (aggregationType){
                    case IOC_KEY:
                        aggregateValueAll = updateIocAggregateValue(aggregateValueAll,alertLog);
                        if (iterable.iterator().hasNext()){
                            context.output(FilterOutPutTagConstant.ALERT_LOG_AGGR,alertLog);
                        }
                        break;
                    case IOA_KEY:
                        aggregateValueAll = updateIoaAggregateValue(aggregateValueAll,alertLog);
                        if (iterable.iterator().hasNext()){
                            context.output(FilterOutPutTagConstant.ALERT_LOG_AGGR,alertLog);
                        }
                        break;
                    case CRYPTO_KEY:
                        aggregateValueAll = updateCryptoAggregateValue(aggregateValueAll,alertLog);
                        if (iterable.iterator().hasNext()){
                            context.output(FilterOutPutTagConstant.ALERT_LOG_AGGR,alertLog);
                        }
                        break;
                    case EMAIL_KEY:
                        aggregateValueAll = updateEmailAggregateValue(aggregateValueAll,alertLog);
                        if (iterable.iterator().hasNext()){
                            context.output(FilterOutPutTagConstant.ALERT_LOG_AGGR,alertLog);
                        }
                        break;
                    case FILE_KEY:
                        aggregateValueAll = updateFileAggregateValue(aggregateValueAll,alertLog);
                        if (iterable.iterator().hasNext()){
                            context.output(FilterOutPutTagConstant.ALERT_LOG_AGGR,alertLog);
                        }
                        break;
                    default:
                        break;
                }
            }
        }


        if (alertLogResult != null) {
            AlertLog.ALERT_LOG.Builder alertLogBuilder = alertLogResult.toBuilder();
            alertLogBuilder.setLRAlertTimes(attackCount);
            alertLogBuilder.setLRLastAlertDate(lastAttackTime);
            alertLogBuilder.setLRFirstAlertDate(firstAttackTime);
            if (aggregateValueAll!=null && !aggregateValueAll.isEmpty()){
                alertLogBuilder.setLRAggregateValue(aggregateValueAll);
            }

            collector.collect(alertLogBuilder.build());
        }

    }

    private String updateAggregateValue(String aggregateValueAll, String aggregateValue) {
        if(aggregateValueAll==null){
            aggregateValueAll = aggregateValue;
        }else {
            aggregateValueAll = aggregateValueAll + "," + aggregateValue;
        }
        return aggregateValueAll;
    }

    // file_yara_rule_name
    private String updateFileAggregateValue(String aggregateValue, AlertLog.ALERT_LOG alertLog) {
        String fileYaraRuleName =  alertLog.getFileAlertInfo().getFileYaraRuleName();
        if(aggregateValue==null){
            aggregateValue = fileYaraRuleName;
        }else {
            aggregateValue = aggregateValue + "," + fileYaraRuleName;
        }
        return aggregateValue;
    }

    // email_anomaly_tags
    private String updateEmailAggregateValue(String aggregateValue, AlertLog.ALERT_LOG alertLog) {
        List<String> emailAnomalyTags =  alertLog.getMailAlertInfo().getEmailAnomalyTagsList();
        if(aggregateValue==null){
            aggregateValue = String.join(",", emailAnomalyTags);
        }else {
            aggregateValue = aggregateValue + "," + String.join(",", emailAnomalyTags);
        }
        return aggregateValue;
    }

    // crypto_risk_name
    private String updateCryptoAggregateValue(String aggregateValue, AlertLog.ALERT_LOG alertLog) {
        String cryptoRiskName =  alertLog.getCryptoAlertInfo().getCryptoRiskName();
        if(aggregateValue==null){
            aggregateValue = cryptoRiskName;
        }else {
            aggregateValue = aggregateValue + "," + cryptoRiskName;
        }
        return aggregateValue;
    }

    // ioa_value
    private String updateIoaAggregateValue(String aggregateValue, AlertLog.ALERT_LOG alertLog) {
        String ioaValue =  alertLog.getIoaAlertInfo().getIoaValue();
        if(aggregateValue==null){
            aggregateValue = ioaValue;
        }else {
            aggregateValue = aggregateValue + "," + ioaValue;
        }
        return aggregateValue;
    }

    // ioc_attack_method
    private String updateIocAggregateValue(String aggregateValue, AlertLog.ALERT_LOG alertLog) {
        String iocAttackMethod =  alertLog.getIocAlertInfo().getIocAttackMethod();
        if(aggregateValue==null){
            aggregateValue = iocAttackMethod;
        }else {
            aggregateValue = aggregateValue + "," + iocAttackMethod;
        }
        return aggregateValue;
    }
}
