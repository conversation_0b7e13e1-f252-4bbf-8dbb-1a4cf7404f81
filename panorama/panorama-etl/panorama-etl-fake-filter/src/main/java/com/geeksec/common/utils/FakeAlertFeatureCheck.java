package com.geeksec.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.geeksec.common.loader.PropertiesLoader;
import com.geeksec.entity.po.Email;
import com.geeksec.entity.pojo.*;
import com.geeksec.entity.trans.EmailTrans;
import com.geeksec.entity.trans.HTTPTrans;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import io.rebloom.client.Client;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.net.UnknownHostException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: jerryzhou
 * @date: 2024/7/20 16:11
 * @Description: 虚警过滤检测器(元数据特征规则)
 **/
public class FakeAlertFeatureCheck {

    private static final Logger logger = LoggerFactory.getLogger(FakeAlertFeatureCheck.class);

    // 获取当前环境配置用于初始化Jedis连接池
    private static final PropertiesLoader PROPERTIES_LOADER = new PropertiesLoader("classpath:fake-filter.properties");

    public static JedisPool jedisPool;

    private static Client bloomClient;

    // IP地址过滤布隆过滤器KEY
    private static final String IP_ADDR_BLOOMFILTER_KEY = "ip_addr_filter";

    // 域名过滤布隆过滤器KEY
    private static final String DOMAIN_BLOOMFILTER_KEY = "domain_filter";

    // 邮件地址过滤布隆过滤器KEY
    private static final String EMAIL_ADDR_BLOOMFILTER_KEY = "email_filter";
    private static final String EMAIL_FILTER_REGEX = "^(security|noc|soc|abuse)@.*\\\\..*$";
    private static Pattern emailPattern = Pattern.compile(EMAIL_FILTER_REGEX, Pattern.CASE_INSENSITIVE);

    // 文件MD5过滤布隆过滤器KEY
    private static final String FILE_MD5_BLOOMFILTER_KEY = "file_md5_filter";

    // IP地址CIDR过滤匹配器
    private static IpMatcher ipMatcher;

    private static PublicSuffixListFactory factory = null;
    private static PublicSuffixList suffixList = null;

    public static void initChecker(String redisHost,Integer redisPort){

        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(256);
        poolConfig.setMaxIdle(64);
        poolConfig.setMinIdle(32);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestOnReturn(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setMinEvictableIdleTimeMillis(60000);
        poolConfig.setTimeBetweenEvictionRunsMillis(30000);
        poolConfig.setNumTestsPerEvictionRun(-1);

        // jedis连接池初始化
        jedisPool = new JedisPool(poolConfig, redisHost, redisPort);
        // 布隆过滤器连接初始化
        bloomClient = new Client(jedisPool);
        // IP匹配过滤器初始化
        try {
            ipMatcher = new IpMatcher(redisHost, redisPort);
        } catch (UnknownHostException e) {
            throw new RuntimeException(e);
        }
        // 锚域名转化器初始化
        factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
        logger.info("FakeAlertFeatureCheck 虚警过滤器初始化完成");
    }

    /**
     * 检查域名特征
     *
     * @param domainList 待检查的域名列表
     * @return 如果存在匹配的域名特征，返回true；否则返回false
     */
    public static boolean domainFeatureCheck(List<String> domainList) {
        if (CollectionUtil.isEmpty(domainList)) {
            return false;
        }

        try {
            // 获取锚域名列表
            List<String> anchorDomainList = domainList.stream()
                    .map(suffixList::getRegistrableDomain)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 合并原始域名和锚域名
            List<String> allDomains = Stream.concat(domainList.stream(), anchorDomainList.stream())
                    .distinct()
                    .collect(Collectors.toList());

            // 使用 RedisBloom 的 exists 方法一次性检查多个域名
            for (String domain :allDomains){
                if (bloomClient.exists(DOMAIN_BLOOMFILTER_KEY,domain)){
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            logger.error("域名规则匹配异常，domainList: {}", domainList, e);
            return false;
        }
    }

    /**
     * IP特征 & CIDR格式检测匹配机制
     *
     * @param ipList
     * @return
     */
    public static boolean ipFeatureCheck(List<String> ipList) {
        String sIpAddr = ipList.get(0);
        String dIpAddr = ipList.get(1);
        // 先进行明确IP地址的匹配
        try {
            // 1.使用 RedisBloom 的 exists 方法一次性检查两个IP地址
            for (String ipAddr : ipList){
                if (bloomClient.exists(IP_ADDR_BLOOMFILTER_KEY,ipAddr)){
                    return true;
                }
            }
            // 2.使用IpMatcher对CIDR格式的域名进行匹配 未实现最终效果
            if (ipMatcher.isIPInCIDRList(sIpAddr) || ipMatcher.isIPInCIDRList(dIpAddr)) {
                return true;
            }
            return false;
        } catch (Exception e) {
            // 处理异常
            logger.error("当前IP规则匹配异常,ipList->{}", ipList);
            return false; // 或者根据您的错误处理策略返回适当的值
        }
    }

    /**
     * 邮件地址过滤
     * @param emailTrans
     * @return
     */
    public static boolean emailFakeAlertCheck(EmailTrans emailTrans) {
        Email sender = MailUtils.parseEmailRaw(emailTrans.getMailSenderRaw()).get(0);
        List<Email> receiverList = MailUtils.parseEmailRaw(emailTrans.getMailReceiverRaw());

        List<String> emailAddrList = new ArrayList<>();
        emailAddrList.add(sender.getEmailAddr().split("@")[1]);

        // 只用获取当前email addr字符串中 @后面的部分
        for (Email receiver : receiverList) {
            emailAddrList.add(receiver.getEmailAddr().split("@")[1]);
        }

        try {
            // 1.匹配邮件地址是否存在于RedisBloom过滤器中
            for (String emailAddr : emailAddrList){
                if(bloomClient.exists(EMAIL_ADDR_BLOOMFILTER_KEY,emailAddr)){
                    return true;
                }
            }

            // 通过正则表达式进行匹配
            for (String emailAddr : emailAddrList) {
                Matcher matcher = emailPattern.matcher(emailAddr);
                if (matcher.matches()) {
                    return true;
                }
            }
        } catch (Exception e) {
            logger.error("当前邮件虚警检测异常,emailTrans->{}", emailTrans);
        }
        return false;
    }

    /**
     * 邮件MD5过滤
     * @param md5
     * @return
     */
    public static boolean fileMD5FakeAlertCheck(String md5) {
        try {
            // 使用 RedisBloom 的 existsMulti 方法一次性检查多个MD5值
            boolean result = bloomClient.exists(FILE_MD5_BLOOMFILTER_KEY, md5);

            // 检查结果数组，如果有任何一个为 true，就返回 true，说明存在对应的文件MD5
            if (result) {
                return true;
            }
            return false;
        } catch (Exception e) {
            // 处理异常
            logger.error("当前文件MD5规则匹配异常,md5->{}", md5);
            return false; // 或者根据您的错误处理策略返回适当的值
        }

    }
}
