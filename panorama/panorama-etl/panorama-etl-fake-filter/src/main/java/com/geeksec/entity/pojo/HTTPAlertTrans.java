package com.geeksec.entity.pojo;

import com.geeksec.entity.trans.HTTPTrans;
import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.message.IocAlertInfo;
import lombok.Data;

import java.util.List;

@Data
public class HTTPAlertTrans {

    private String attackId;
    private long attackTime;
    private long attackTypeCode;
    private String killChain;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private List<HTTPTrans> http;

    private IocAlertInfo.IOC_ALERT_INFO iocAlertInfo;

    private boolean fakeAlarm;
}
