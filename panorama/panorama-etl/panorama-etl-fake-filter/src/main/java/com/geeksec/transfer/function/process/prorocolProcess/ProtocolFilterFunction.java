package com.geeksec.transfer.function.process.prorocolProcess;

import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.common.utils.AlertTools;
import com.geeksec.common.utils.RedisUtils;
import com.geeksec.entity.pojo.DNSAlertTrans;
import com.geeksec.entity.pojo.HTTPAlertTrans;
import com.geeksec.entity.pojo.SslAlertTrans;
import com.geeksec.entity.pojo.X509AlertTrans;
import com.geeksec.enums.AppIdEnum;
import com.geeksec.proto.AlertLog;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixList;
import de.malkusch.whoisServerList.publicSuffixList.PublicSuffixListFactory;
import io.rebloom.client.Client;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import redis.clients.jedis.JedisPool;

import java.util.Properties;

/**
 * <AUTHOR>
 */
public class ProtocolFilterFunction extends ProcessFunction<AlertLog.ALERT_LOG,AlertLog.ALERT_LOG> {

    public static final int MILLISECONDS_TO_SECONDS = 1000;

    // 布隆过滤器链接实例
    private Client bloomClient;

    public static PublicSuffixList suffixList = null;

    public static final String DOMAIN_BLOOM_FILTER_KEY = "domain_filter";
    public static final String FILE_MD5_BLOOM_FILTER_KEY = "file_md5_filter";
    public static final String CERT_HASH_BLOOM_FILTER_KEY = "cert_hash_filter";

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        ParameterTool globalJobParameters = (ParameterTool)
                getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        String redisHost = globalJobParameters.get("redis.host.addr");
        Integer redisPort = globalJobParameters.getInt("redis.host.port");
        // JedisPool连接池实例
        JedisPool jedisPool = RedisUtils.getJedisPool(redisHost, redisPort);
        bloomClient = new Client(jedisPool);

        // 锚域名转化器初始化
        PublicSuffixListFactory factory = new PublicSuffixListFactory();
        Properties properties = factory.getDefaults();
        properties.setProperty(PublicSuffixListFactory.PROPERTY_LIST_FILE, "/effective_tld_names.dat");
        suffixList = factory.build();
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (bloomClient != null) {
            bloomClient.close();
        }
    }


    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context context, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        String appProto = alertLog.getAppProto().toLowerCase();
        if (appProto.equals(AppIdEnum.DNS.getMsg())) {
            DNSAlertTrans dnsAlert = AlertTools.createDnsAlertTrans(alertLog);
            if(dnsAlert!=null){
                // 若是虚警直接进行过滤，不是则推送至下一步
                if(AlertTools.dnsFakeAlertCheck(dnsAlert, alertLog, bloomClient)){
                    context.output(FilterOutPutTagConstant.PROTOCOL_FILTER_TERMINATE,alertLog);
                }
            }
            context.output(FilterOutPutTagConstant.PROTOCOL_FILTER_CONTINUE,alertLog);
        } else if (appProto.equals(AppIdEnum.HTTP.getMsg())) {
            HTTPAlertTrans httpAlert = AlertTools.createHttpAlertTrans(alertLog);
            if (httpAlert!=null){
                if(AlertTools.httpFakeAlertCheck(httpAlert, alertLog, bloomClient)){
                    context.output(FilterOutPutTagConstant.PROTOCOL_FILTER_TERMINATE,alertLog);
                }
            }
            context.output(FilterOutPutTagConstant.PROTOCOL_FILTER_CONTINUE,alertLog);
        } else if(appProto.equals(AppIdEnum.SSL_TLS.getMsg()) || "tls".equals(appProto)
                || "ssl".equals(appProto) || appProto.equals(AppIdEnum.X509_CER.getMsg())){
            // 提取IOC SSL证书告警日志元数据
            SslAlertTrans sslAlert = AlertTools.createSslAlertTrans(alertLog);
            // 提取IOC X509证书告警日志元数据
            X509AlertTrans x509Alert = AlertTools.createX509AlertTrans(alertLog);

            boolean sslCheck = false;
            boolean x509Check = false;

            if(sslAlert!=null){
                sslCheck = AlertTools.sslCheckFakeAlarm(sslAlert, alertLog, bloomClient);
            }
            if(x509Alert!=null){
                x509Check = AlertTools.x509CheckFakeAlarm(x509Alert, alertLog, bloomClient);
            }

            if(sslCheck || x509Check){
                context.output(FilterOutPutTagConstant.PROTOCOL_FILTER_TERMINATE,alertLog);
            }else {
                context.output(FilterOutPutTagConstant.PROTOCOL_FILTER_CONTINUE,alertLog);
            }
        }else {
            // TODO 支持更多协议类型
            context.output(FilterOutPutTagConstant.PROTOCOL_FILTER_CONTINUE,alertLog);
        }
    }
}
