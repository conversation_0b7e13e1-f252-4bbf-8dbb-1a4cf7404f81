package com.geeksec.transfer.selector;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.java.functions.KeySelector;
import org.apache.flink.api.java.tuple.Tuple4;

import static com.geeksec.task.PanoramaFakeAlertFilterTask.CRYPTO_KEY;

/**
 * <AUTHOR>
 */
public class CryptoTransKeySelector implements KeySelector<AlertLog.ALERT_LOG, Tuple4<String, String, String, String>> {

    /**
     * 使用攻击方-受害方-加密应用名称作为Key
     * @param alertLog 告警日志
     * @return Tuple3<String, String, String> 攻击方IP-受害方IP-加密应用名称
     */
    @Override
    public Tuple4<String, String, String, String> getKey(AlertLog.ALERT_LOG alertLog) throws Exception {
        String vipAddr = alertLog.getVip().getIp();
        String aipAddr = alertLog.getAip().getIp();
        String cryptoAppName = alertLog.getCryptoAlertInfo().getCryptoAppName();
        return new Tuple4<>(CRYPTO_KEY,aipAddr,vipAddr,cryptoAppName);
    }
}
