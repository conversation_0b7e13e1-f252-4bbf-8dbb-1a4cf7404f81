package com.geeksec.transfer.function.process;

import lombok.extern.slf4j.Slf4j;
import org.apache.flink.streaming.api.functions.windowing.ProcessWindowFunction;
import org.apache.flink.streaming.api.windowing.windows.TimeWindow;
import org.apache.flink.util.Collector;
import redis.clients.jedis.Jedis;

import static com.geeksec.transfer.function.process.ipProcess.IpFilterFunction.jedisPool;


@Slf4j
public class AlertCountDaylyProces extends ProcessWindowFunction<Integer, Integer, Integer, TimeWindow> {

    public static final String YESTERDAY = ":Yesterday";
    public static final String TODAY = ":Today";

    @Override
    public void process(Integer integer, ProcessWindowFunction<Integer, Integer, Integer, TimeWindow>.Context context, Iterable<Integer> elements, Collector<Integer> out) throws Exception {
        // 每日定时更新redis中的今日和昨日统计数据
        try (Jedis jedis = jedisPool.getResource()) {
            updateDaylyCount(jedis, "AlertInputCount");
            updateDaylyCount(jedis, "FilterAlertCount");
        } catch (Exception e) {
            log.info("alert update error {}", e.getMessage());
        }
    }

    private static void updateDaylyCount(Jedis jedis, String keyType) {
        if (jedis.exists(keyType+TODAY)) {
            String todayCount = jedis.get(keyType+TODAY);
            if (todayCount != null) {
                jedis.set(keyType+TODAY, "0");
                jedis.set(keyType+YESTERDAY, todayCount);
            }
        }
    }
}
