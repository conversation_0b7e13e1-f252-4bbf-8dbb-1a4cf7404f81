package com.geeksec.enums;


import java.util.HashMap;
import java.util.Map;

/**
 * 应用层协议
 *
 */
public enum AppIdEnum {

    COMMON_FIELD(0x00000400, "共性字段"),
    S_LINK(0x10100800, "SLink"),
    LINK(0x10200C00, "Link"),
    UDP_SINGLE_FRAME(0x10301000, "UDP单帧"),
    TCP_SINGLE_FRAME(0x10301400, "TCP单帧"),
    SCTP_SINGLE_FRAME(0x10301800, "SCTP单帧"),
    IP_SINGLE_FRAME(0x10301C00, "IP单帧"),
    GRE(0x10402000, "gre"),
    L2TP(0x10402400, "l2tp"),
    PPPOE(0x10402800, "pppoe"),
    PPTP(0x10402C00, "pptp"),
    ACCELERATOR(0x10503000, "Accelerator"),
    CCP(0x10503400, "ccp"),
    HDR_COMPR(0x10503800, "HdrCompr"),
    PAY_COMPR(0x10503C00, "PayCompr"),
    NTP(0x10604000, "ntp"),
    SCTP(0x10604400, "sctp"),
    STUN(0x10604800, "stun"),
    CDP(0x10704C00, "cdp"),
    CFLOW(0x10705000, "cflow"),
    CLDAP(0x10705400, "CLDAP"),
    DHCP(0x10705800, "dhcp"),
    DNS(0x10705C00, "DNS"),
    ICMP(0x10706000, "icmp"),
    IGMP(0x10706400, "igmp"),
    LACP(0x10706800, "LACP"),
    LCP(0x10706C00, "lcp"),
    LDAP(0x10707000, "LDAP"),
    LDP(0x10707400, "LDP"),
    LLDP(0x10707800, "LLDP"),
    NET_LOGIN(0x10707C00, "NET_LOGIN"),
    NETBIOS(0x10708000, "NETBIOS"),
    NET_BIOS_DATA_SVC(0x10708400, "NetBiosDataSvc"),
    NET_BIOS_NAME_SVC(0x10708800, "NetBiosNameSvc"),
    NET_BIOS_SES_SVC(0x10708C00, "NetBiosSesSvc"),
    NETFLOW(0x10709000, "netflow"),
    RSVP(0x10709400, "rsvp"),
    SNMP(0x10709800, "snmp"),
    SOCKS(0x10709C00, "SOCKS"),
    SYSLOG(0x1070A000, "SYSLOG"),
    WIN_LOGIN(0x1070A400, "WinLogin"),
    WINS(0x1070A800, "WINS"),
    ARP(0x1080AC00, "ARP"),
    BGP(0x1080B000, "BGP"),
    EIGRP(0x1080B400, "EIGRP"),
    ISIS(0x1080B800, "ISIS"),
    OSPF(0x1080BC00, "OSPF"),
    PIM(0x1080C000, "PIM"),
    RIP(0x1080C400, "RIP"),
    STP(0x1080C800, "STP"),
    VRRP(0x1080CC00, "VRRP"),
    DCERPC(0x1090D000, "DCERPC"),
    DCERPC_BIND(0x1090D400, "DCERPC_BIND"),
    RDP(0x1090D800, "RDP"),
    TELNET(0x1090DC00, "TELNET"),
    VNC(0x1090E000, "VNC"),
    X_WIN(0x1090E400, "XWin"),
    NTLMSSP(0x10921800, "NTLMSSP"),
    AH(0x10A0E800, "AH"),
    DTLS(0x10A0EC00, "DTLS"),
    ESP(0x10A0F000, "ESP"),
    ISAKMP(0x10A0F400, "ISAKMP"),
    OCSP(0x10A0F800, "OCSP"),
    SSH(0x10A0FC00, "SSH"),
    SSL_TLS(0x10A10000, "SSL_TLS"),
    X509_CER(0x10A10400, "X509Cer"),
    CHAP(0x10B10800, "CHAP"),
    DIAMETER(0x10B10C00, "Diameter"),
    KERBEROS(0x10B11000, "KERBEROS"),
    NTLM(0x10B11400, "NTLM"),
    PAP(0x10B11800, "PAP"),
    RADIUS(0x10B11C00, "RADIUS"),
    SPNEGO(0x10B12000, "SPNEGO"),
    HTTP(0x10C12400, "HTTP"),
    FTP(0x10D12800, "FTP"),
    NFS(0x10D12C00, "NFS"),
    P2P(0x10D13000, "P2P"),
    RSYNC(0x10D13400, "RSYNC"),
    SMB(0x10D13800, "SMB"),
    SMB_LOGON(0x10D13C00, "SMB_LOGON"),
    SMB2(0x10D14000, "SMB2"),
    TFTP(0x10D14400, "TFTP"),
    IPCALRECONMET(0x10E14800, "IPCALRECONMET"),
    MEGACO_MET(0x10E14C00, "MegacoMet"),
    MGCP(0x10E15000, "MGCP"),
    ESMTP(0x10F15400, "ESMTP"),
    LOTUS_MAIL(0x10F15800, "LotusMail"),
    LOTUS_NSF(0x10F15C00, "LotusNSF"),
    LSA(0x10F16000, "LSA"),
    MAPI(0x10F16400, "MAPI"),
    NGWBCEF_BUF(0x10F16800, "NGWBCEFBuf"),
    NG_W_MAIL_TRAN(0x10F16C00, "NGWMailTran"),
    NGW_MSG(0x10F17000, "NGWMsg"),
    NGWWPC(0x10F17400, "NGWWPC"),
    NSPI(0x10F17800, "NSPI"),
    NSPIRFR(0x10F17C00, "NSPIRFR"),
    CIT_XEN_ACC_SITE(0x11018000, "CitXenAccSite"),
    CLO_STA_API(0x11018400, "CloStaAPI"),
    ICA(0x11018800, "ICA"),
    ICA_FILE(0x11018C00, "ICAFile"),
    SAN_DATA_TRAN(0x11019000, "SANDataTran"),
    DB_BASIC(0x11119400, "DBBasic"),
    MYSQL(0x11119800, "MySQL"),
    TDS(0x11119C00, "TDS"),
    UNKNOWN_FLOW_INFO(0x1121A000, "UnKnoFlowInfo"),
    EMAIL(0x1131A400, "EMAIL"),
    AIS_POS_INFO_1(0x1141A800, "AisPosInfo1"),
    AIS_POS_INFO_5(0x1141AC00, "AisPosInfo5"),
    SHIP_GPS_INFO(0x1141B000, "ShipGpsInfo"),
    ADS_B_SVC_INFO(0x1151B400, "AdsBSvcInfo"),
    ADS_B_TARGET_INFO(0x1151B800, "AdsBTargetInfo"),
    DNP3(0x1161BC00, "DNP3"),
    ICS(0x1161C000, "ICS"),
    MODBUS(0x1161C400, "modbus"),
    S7_COMM(0x1161C800, "S7Comm"),
    ENIP(0x11621C00, "ENIP"),
    BACNET(0x11622000, "BACNET"),
    OPC_UA(0x11622400, "OPC-UA"),
    PROFINET(0x11622800, "Profinet"),
    ETHERNET_IP_CIP(0x11622C00, "Ethernet/IP（CIP）"),
    OMRON_FINS(0x11623000, "OMRON FINS"),
    IEC104(0x11623400, "IEC104"),
    IEC61850_MMS(0x11623800, "IEC61850 MMS"),
    SINEC_H1(0x11623C00, "SINEC_H1"),
    CWMP_TR069(0x1171CC00, "CWMP（TR069）"),
    BLACKBERRY_MSG(0x1181D000, "BlackBerryMsg"),
    BICC_SIGNAL_DETECT(0x3191D400, "BICC信令侦察要素"),
    DIAMETER_SIGNAL(0x3191D800, "Diameter"),
    GTPV0(0x3191DC00, "gtpv0"),
    GTPV1(0x3191E000, "gtpv1"),
    GTPV2(0x3191E400, "gtpv2"),
    IUA(0x3191E800, "IUA"),
    M2PA(0x3191EC00, "M2PA"),
    M2UA_SIGNAL_DETECT(0x3191F000, "M2UA信令侦察要素"),
    M3UA(0x3191F400, "M3UA"),
    SUA(0x3191F800, "SUA"),
    V5UA(0x3191FC00, "V5UA"),
    VOLTE_EXT_SIP_SIGNAL(0x31920000, "VoLTE扩展SIP信令侦察要素"),
    MOBILE_PACKET_DOMAIN(0x31920400, "移动分组域"),
    MOBILE_PACKET_BASIC_INFO(0x31920800, "移动分组域基础信息"),
    H248(0x21A20C00, "H248"),
    H323(0x21A21000, "H323"),
    SIP(0x21A21400, "SIP"),
    THREAT(0x78000400, "Threat");

    /**
     * 应用层协议编号
     */
    private Integer code;

    /**
     * 应用层协议名称
     */
    private String msg;

    AppIdEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    private static Map<Integer, AppIdEnum> codeEnumMap = new HashMap<>();

    static {
        for (AppIdEnum e : AppIdEnum.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        AppIdEnum getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }

    // 可选：通过 code 或 msg 反向查找枚举值的方法
    public static AppIdEnum getByCode(int code) {
        for (AppIdEnum e : AppIdEnum.values()) {
            if (e.code == code) {
                return e;
            }
        }
        return null;
    }
}
