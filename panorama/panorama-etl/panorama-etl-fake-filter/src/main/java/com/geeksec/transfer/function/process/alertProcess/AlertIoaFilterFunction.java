package com.geeksec.transfer.function.process.alertProcess;

import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.proto.AlertLog;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class AlertIoaFilterFunction extends ProcessFunction<AlertLog.ALERT_LOG,AlertLog.ALERT_LOG> {
    private static final Logger logger = LoggerFactory.getLogger(AlertIoaFilterFunction.class);

    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
    }

    @Override
    public void close() throws Exception {
        super.close();
    }

    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context context, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        context.output(FilterOutPutTagConstant.IOA_FILTER_CONTINUE,alertLog);
    }
}
