package com.geeksec.entity.pojo;

import com.geeksec.entity.trans.IPTrans;
import com.geeksec.proto.message.CryptoAlertInfo;
import com.geeksec.proto.message.IocAlertInfo;
import lombok.Data;

import java.util.List;

@Data
public class CryptoAlertTrans {
    private String attackId;
    private long attackTime;
    private long attackTypeCode;
    private String killChain;

    private IPTrans sip;
    private IPTrans dip;
    private String aipAddr;
    private String vipAddr;

    private Long cryptoStreamId;
    private Boolean cryptoEncrypted;
    private String cryptoAppName;
    private Integer cryptoAppTypeId;
    private String cryptoAppType;
    private Integer cryptoAppClassId;
    private String cryptoAppClass;
    private String cryptoActionType;
    private Integer assetIdClient;
    private Integer assetIdServer;
    private String cryptoRiskName;
    private String cryptoRiskLevel;
    private String cryptoCertFingerprint;
    private Long cryptoRuleId;
    private String cryptoRuleType;
    private String cryptoThreatSubtype;
    private String cryptoThreatLevel;
    private String cryptoThreatFamily;
    private String cryptoThreatGroup;
    private String cryptoThreatDirection;
    private String cryptoThreatDescription;
    private String cryptoDirection;
    private String cryptoDetectionState;
    private String cryptoDetectionDescribe;
    private String cryptoHandResult;
    private String cryptoFlowResult;
    private String cryptoCertResult;
    private String cryptoDomainResult;
    private String cryptoResult;

    private boolean fakeAlarm;
}
