package com.geeksec.transfer.function.process.alertProcess;

import com.geeksec.common.constant.FilterOutPutTagConstant;
import com.geeksec.common.utils.AlertTools;
import com.geeksec.common.utils.RedisUtils;
import com.geeksec.entity.pojo.CryptoAlertTrans;
import com.geeksec.proto.AlertLog;
import com.geeksec.proto.message.CryptoAlertInfo;
import com.geeksec.transfer.handle.IPTransHandler;
import io.rebloom.client.Client;
import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisPool;

import static com.geeksec.transfer.function.process.prorocolProcess.ProtocolFilterFunction.CERT_HASH_BLOOM_FILTER_KEY;

/**
 * <AUTHOR>
 */
public class AlertCryptoFilterFunction extends ProcessFunction<AlertLog.ALERT_LOG,AlertLog.ALERT_LOG> {

    private static final Logger logger = LoggerFactory.getLogger(AlertCryptoFilterFunction.class);

    // 布隆过滤器链接实例
    private Client bloomClient;

    // JedisPool连接池实例
    private JedisPool jedisPool;

    @Override
    public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
        super.open(parameters);
        ParameterTool globalJobParameters = (ParameterTool)
                getRuntimeContext().getExecutionConfig().getGlobalJobParameters();
        String redisHost = globalJobParameters.get("redis.host.addr");
        Integer redisPort = globalJobParameters.getInt("redis.host.port");
        jedisPool = RedisUtils.getJedisPool(redisHost,redisPort);
        bloomClient = new Client(jedisPool);
    }

    @Override
    public void close() throws Exception {
        super.close();
        if (jedisPool != null) {
            jedisPool.close();
        }
        if (bloomClient != null) {
            bloomClient.close();
        }
    }

    @Override
    public void processElement(AlertLog.ALERT_LOG alertLog, ProcessFunction<AlertLog.ALERT_LOG, AlertLog.ALERT_LOG>.Context context, Collector<AlertLog.ALERT_LOG> collector) throws Exception {
        try {
            CryptoAlertTrans cryptoAlert = createCryptoAlertTrans(alertLog);
            if(checkCryptoFakeAlert(cryptoAlert)){
                context.output(FilterOutPutTagConstant.CRYPTO_FILTER_TERMINATE,alertLog);
            }
            context.output(FilterOutPutTagConstant.CRYPTO_FILTER_CONTINUE,alertLog);
        } catch (Exception e) {
            logger.error("Error processing Email alert log: {}", alertLog.getGuid(), e);
            context.output(FilterOutPutTagConstant.CRYPTO_FILTER_CONTINUE,alertLog);
        }
    }

    private boolean checkCryptoFakeAlert(CryptoAlertTrans cryptoAlert) {
        // crypto_detection_state，研判状态是误报
        if("误报".equals(cryptoAlert.getCryptoDetectionState())){
            return true;
        }
        // crypto_cert_fingerprint，证书指纹是白名单，crypto_risk_name，威胁名称包含证书
        if(cryptoAlert.getCryptoRiskName().contains("证书")){
            if(cryptoAlert.getCryptoCertFingerprint() != null){
                return bloomClient.exists(CERT_HASH_BLOOM_FILTER_KEY, cryptoAlert.getCryptoCertFingerprint());
            }
        }
        return false;
    }

    private CryptoAlertTrans createCryptoAlertTrans(AlertLog.ALERT_LOG alertLog) {
        CryptoAlertTrans cryptoAlertTrans =  new CryptoAlertTrans();
        cryptoAlertTrans.setAttackId(alertLog.getGuid());
        cryptoAlertTrans.setAttackTime(AlertTools.parseTimeToLong(alertLog.getTime()));
        cryptoAlertTrans.setAttackTypeCode(alertLog.getThreatType());
        cryptoAlertTrans.setKillChain(alertLog.getKillChain());
        cryptoAlertTrans.setSip(IPTransHandler.transIP(alertLog.getSip()));
        cryptoAlertTrans.setDip(IPTransHandler.transIP(alertLog.getDip()));
        cryptoAlertTrans.setAipAddr(alertLog.getAip().getIp());
        cryptoAlertTrans.setVipAddr(alertLog.getVip().getIp());

        CryptoAlertInfo.CRYPTO_ALERT_INFO cryptoAlertInfo = alertLog.getCryptoAlertInfo();
        cryptoAlertTrans.setCryptoStreamId(cryptoAlertInfo.getCryptoStreamId());
        cryptoAlertTrans.setCryptoEncrypted(cryptoAlertInfo.getCryptoEncrypted());
        cryptoAlertTrans.setCryptoAppName(cryptoAlertInfo.getCryptoAppName());
        cryptoAlertTrans.setCryptoAppTypeId(cryptoAlertInfo.getCryptoAppTypeId());
        cryptoAlertTrans.setCryptoAppType(cryptoAlertInfo.getCryptoAppType());
        cryptoAlertTrans.setCryptoAppClassId(cryptoAlertInfo.getCryptoAppClassId());
        cryptoAlertTrans.setCryptoAppClass(cryptoAlertInfo.getCryptoAppClass());
        cryptoAlertTrans.setCryptoActionType(cryptoAlertInfo.getCryptoActionType());
        cryptoAlertTrans.setAssetIdClient(cryptoAlertInfo.getAssetIdClient());
        cryptoAlertTrans.setAssetIdServer(cryptoAlertInfo.getAssetIdServer());
        cryptoAlertTrans.setCryptoRiskName(cryptoAlertInfo.getCryptoRiskName());
        cryptoAlertTrans.setCryptoRiskLevel(cryptoAlertInfo.getCryptoRiskLevel());
        cryptoAlertTrans.setCryptoCertFingerprint(cryptoAlertInfo.getCryptoCertFingerprint());
        cryptoAlertTrans.setCryptoRuleId(cryptoAlertInfo.getCryptoRuleId());
        cryptoAlertTrans.setCryptoRuleType(cryptoAlertInfo.getCryptoRuleType());
        cryptoAlertTrans.setCryptoThreatSubtype(cryptoAlertInfo.getCryptoThreatSubtype());
        cryptoAlertTrans.setCryptoThreatLevel(cryptoAlertInfo.getCryptoThreatLevel());
        cryptoAlertTrans.setCryptoThreatFamily(cryptoAlertInfo.getCryptoThreatFamily());
        cryptoAlertTrans.setCryptoThreatGroup(cryptoAlertInfo.getCryptoThreatGroup());
        cryptoAlertTrans.setCryptoThreatDirection(cryptoAlertInfo.getCryptoThreatDirection());
        cryptoAlertTrans.setCryptoThreatDescription(cryptoAlertInfo.getCryptoThreatDescription());
        cryptoAlertTrans.setCryptoDirection(cryptoAlertInfo.getCryptoDirection());
        cryptoAlertTrans.setCryptoDetectionState(cryptoAlertInfo.getCryptoDetectionState());
        cryptoAlertTrans.setCryptoDetectionDescribe(cryptoAlertInfo.getCryptoDetectionDescribe());
        cryptoAlertTrans.setCryptoHandResult(cryptoAlertInfo.getCryptoHandResult());
        cryptoAlertTrans.setCryptoFlowResult(cryptoAlertInfo.getCryptoFlowResult());
        cryptoAlertTrans.setCryptoCertResult(cryptoAlertInfo.getCryptoCertResult());
        cryptoAlertTrans.setCryptoDomainResult(cryptoAlertInfo.getCryptoDomainResult());
        cryptoAlertTrans.setCryptoResult(cryptoAlertInfo.getCryptoResult());

        return cryptoAlertTrans;
    }
}
