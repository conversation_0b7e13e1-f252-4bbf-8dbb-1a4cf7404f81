package com.geeksec.common.constant;

import com.geeksec.proto.AlertLog;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.util.OutputTag;

/**
 * @author: jerry<PERSON>
 * @date: 2024/7/17 21:13
 * @Description:
 **/
public class AlertLogOutPutTagConstant {

    /**
     * 标准待处理多规则类型告警日志输出标签
     */
    public static final OutputTag<AlertLog.ALERT_LOG> IOC_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("IOC_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOB_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("IOB_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IOA_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("IOA_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> IIOT_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("IIOT_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> FILE_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("FILE_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> CRYPTO_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("CRYPTO_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> CERT_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("CERT_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> MAIL_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("MAIL_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> MOBILE_ALERT_INFO_OUTPUT_TAG = new OutputTag<>("MOBILE_ALERT_INFO_OUTPUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));
    public static final OutputTag<AlertLog.ALERT_LOG> PROTO_ALERT_INFO_OUT_TAG = new OutputTag<>("PROTO_ALERT_INFO_OUT_TAG", TypeInformation.of(AlertLog.ALERT_LOG.class));

}


