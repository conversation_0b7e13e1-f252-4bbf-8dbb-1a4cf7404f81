package com.geeksec.common.utils;

import org.apache.flink.api.java.utils.ParameterTool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

/**
 * <AUTHOR>
 * @Description：
 */
public class RedisUtils {

    private static final Logger logger = LoggerFactory.getLogger(RedisUtils.class);

    public static JedisPool getJedisPool(String redisHost,Integer redisPort) {
        try{
            JedisPool jedisPool = new JedisPool(getRedisConfig(), redisHost, redisPort, 10000);
            return jedisPool;
        }catch (Exception e){
            logger.error("初始化jedis pool失败！REDIS_HOST ->{},REDIS_PORT  ->{}", "127.0.0.1", 6379, e);
            throw new RuntimeException("Failed to initialize JedisPool", e);
        }
    }

    private static JedisPoolConfig getRedisConfig() {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(16); //最大可用连接数
        jedisPoolConfig.setBlockWhenExhausted(true); //连接耗尽是否等待
        jedisPoolConfig.setMaxWaitMillis(60000); //等待时间
        jedisPoolConfig.setMaxIdle(8); //最大闲置连接数
        jedisPoolConfig.setMinIdle(2); //最小闲置连接数
        jedisPoolConfig.setTestOnBorrow(true); //取连接的时候进行一下测试 ping pong
        jedisPoolConfig.setTestOnReturn(true);
        return jedisPoolConfig;
    }
}
