package com.geeksec.entity.trans;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class X509CertTrans {
    private int version;

    private String issuer;

    private String subject;

    private String algorithmId;

    private String issuerCn;

    // issOrgName
    private String issuerOn;

    // issuerOrgUniName
    private String issuerOu;

    private String subjectCn;

    // subOrgName
    private String subjectOn;

    // subOrgUniName
    private String subjectOu;

    private String fingerPrint;

    private String fpAlg;

    private Long notBefore;

    private Long notAfter;
}