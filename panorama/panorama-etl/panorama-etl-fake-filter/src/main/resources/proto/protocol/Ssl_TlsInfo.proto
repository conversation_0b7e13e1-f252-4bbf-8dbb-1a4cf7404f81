syntax = "proto2";

message Ssl_TlsInfo
{
	optional	uint32		conType					=	1;
	optional	uint32		aleLev					=	2;
	optional	uint32		aleDes					=	3;
	optional	uint32		handShaType				=	4;
	optional	uint32		cliVer					=	5;
	optional	uint64		cliGMTUniTime			=	6;
	optional	bytes		cliRand					=	7;
	optional	bytes		cliSesID				=	8;
	optional	bytes		cliCipSui				=	9;
	optional	bytes		cliComMet				=	10;
	optional	uint32		srvVer					=	11;
	optional	bytes		srvName					=	12;
	optional	uint32		srvNameAttr				=	13;
	optional	uint64		srvGMTUniTime			=	14;
	optional	bytes		srvRand					=	15;
	optional	bytes		srvSesID				=	16;
	optional	bytes		srvComprMet				=	17;
	optional	uint32		srvCertLen				=	18;
	optional	uint32		certResType				=	19;
	optional	uint32		cliCertLen				=	20;
	optional	bytes		RSAModOfSrvKeyExc		=	21;
	optional	uint64		RSAExpOfSrvKeyExc		=	22;
	optional	bytes		DHModOfSrvKeyExc		=	23;
	optional	bytes		DHGenOfSrvKeyExc		=	24;
	optional	bytes		srvDHPubKey				=	25;
	optional	bytes		preMasKeyEncryByRSA		=	26;
	optional	bytes		cliDHPubKey				=	27;
	optional	uint32		extTypeInSSL			=	28;
	optional	uint32		cliEllCurPoiFor			=	29;
	optional	uint32		cliEllCur				=	30;
	optional	uint32		srvEllCurPoiFor			=	31;
	optional	uint32		srvEllCur				=	32;
	optional	bytes		srvEllCurDHPubKey		=	33;
	optional	bytes		cliEllCurDHPubKey		=	34;
	optional	uint64		srvGMTUni_Time			=	35;
	optional	uint32		cliExtCnt				=	36;
	optional	uint32		srvExtCnt				=	37;
	optional	uint32		cliHandSkLen			=	38;
	optional	uint32		srvHandSkLen			=	39;
	optional	bytes		cliExt					=	40;
	optional	bytes		srvExt					=	41;
	optional	uint32		cliExtGrease			=	42;
	optional	bytes		cliJA3					=	43;
	optional	bytes		srvJA3					=	44;
	optional	bytes		cliSessTicket			=	45;
	optional	bytes		srvSessTicket			=	46;
	optional	uint32		AuthTag					=	47;
	optional	uint32		cliCertCnt				=	48;
	optional	uint32		srvCertCnt				=	49;
	optional	bytes		ecGroupsCli				=	50;
	optional	bytes		ecPoiForByServ 			=	51;
	optional	bytes		etags					=	52;
	optional	bytes		ttags					=	53;
	optional	bytes		cliSesIDLen				=	54;
	optional	bytes		srvSesIDLen				=	55;
	optional	bytes		srvKeyExcLen			=	56;
	optional	bytes		ECDHCurType				=	57;
	optional	bytes		ECDHSig					=	58;
	optional	bytes		DHEPLen					=	59;
	optional	bytes		DHEGLen					=	60;
	optional	bytes		cliKeyExcLen			=	61;
	optional	bytes		encPubKey				=	62;
	optional	bytes		encPubKeyLen			=	63;
	optional	bytes		cliExtLen				=	64;
	optional	bytes		srvExtLen				=	65;
	optional	bytes		ECDHPubKeyLen			=	66;
	optional	bytes		namType					=	67;
	optional	bytes		namLen					=	68;
	optional	bytes		ticDat					=	69;
	optional	bytes		srvCipSui				=	70;
	optional	uint32		cipSuiNum				=	71;
	optional	bytes		ECDHSigHash				=	72;
	optional	bytes		DHESigHash				=	73;
	optional	bytes		RSASigHash				=	74;
	optional	bytes		greaseFlag				=	75;
	optional	bytes		RSAModLen				=	76;
	optional	bytes		RSAExpLen				=	77;
	optional	bytes		RSASig					=	78;
	optional	bytes		DHESig					=	79;
	optional	bytes		DHEPubKeyLen			=	80;
	optional	bytes		DHEPubKey				=	81;
	optional	bytes		SigAlgType				=	82;
	optional	bytes		sigAlg					=	83;
	optional	bytes		SigHashAlg				=	84;
	optional	bytes		JOY						=	85;
	optional	bytes		JOYS					=	86;
	optional	bytes		STARTTLS				=	87;
	optional	bytes		certNonFlag				=	88;
	optional	bytes		JoyFp					=	89;
	optional	bytes		certIntactFlag			=	90;
	optional	bytes		certPath				=	91;
	optional	bytes		sessSecFlag				=	92;
	optional	bytes		fullText				=	93;
	optional	bytes		cliCertHashes			=	94;
	optional	bytes		srvCertHashes			=	95;
	optional	uint32		cliCertNum				=	96;
	optional	uint32		srvCertNum				=	97;
	optional	uint32		certExist				=	98;
	optional	bytes		extendEcGroupsClient	=	99;
	optional	uint32		leafCertDaysRemaining	=	100;
}
