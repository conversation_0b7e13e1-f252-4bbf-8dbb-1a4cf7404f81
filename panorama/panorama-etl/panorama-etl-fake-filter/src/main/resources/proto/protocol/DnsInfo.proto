syntax = "proto2";

message DnsInfo
{
  optional  uint32    addCnt = 1;             // 附加记录计数
  repeated  uint32    aip = 2;                // A记录IP地址列表(IPv4)
  repeated  bytes     aipAsn = 3;             // A记录IP对应的ASN信息
  optional  uint32    aipCnt = 4;             // A记录IP地址数量
  repeated  bytes     aIpv6 = 5;              // AAAA记录IPv6地址列表
  optional  uint32    aIpv6Cnt = 6;           // AAAA记录IPv6地址数量
  repeated  bytes     aipCountry = 7;         // A记录IP所属国家/地区
  repeated  bytes     ansCname = 8;           // CNAME记录列表
  optional  uint32    ansCnameCnt = 9;        // CNAME记录数量
  optional  uint32    ansCnt = 10;            // 回答记录总数
  repeated  bytes     ansIPv6 = 11;           // 回答中的IPv6地址
  optional  bytes     ansQue = 12;            // 回答对应的查询
  repeated  bytes     ansTypes = 13;          // 回答记录的类型列表
  optional  uint32    autCnt = 14;            // 权威记录计数
  repeated  bytes     mxIpAsn = 15;           // MX记录IP的ASN信息
  repeated  bytes     mxIpCountry = 16;       // MX记录IP所属国家/地区
  repeated  bytes     mailSrvHost = 17;       // 邮件服务器主机名列表
  optional  uint32    mailSrvHostcnt = 18;    // 邮件服务器主机名数量
  repeated  uint32    mailSrvIp = 19;         // 邮件服务器IP地址列表
  optional  uint32    mailSrvIPCnt = 20;      // 邮件服务器IP地址数量
  repeated  bytes     nameSrvAsn = 21;        // 名称服务器的ASN信息
  repeated  bytes     nameSrvCountry = 22;    // 名称服务器所属国家/地区
  repeated  bytes     nameSrvHost = 23;       // 名称服务器主机名列表
  optional  uint32    nameSrvHostCnt = 24;    // 名称服务器主机名数量
  repeated  uint32    nsIp = 25;              // NS记录的IP地址列表(IPv4)
  optional  uint32    nsIpCnt = 26;           // NS记录的IP地址数量
  optional  bytes     ansName = 27;           // 回答的域名
  optional  uint32    addRrs = 28;            // 附加资源记录
  optional  bytes     dnsSpf = 29;            // DNS SPF记录(发件人策略框架)
  optional  bytes     dnsTxt = 30;            // DNS TXT记录
  optional  uint32    queType = 31;           // 查询类型(A/AAAA/MX/NS等)
  optional  bytes     queName = 32;           // 查询的域名
  optional  uint32    traID = 33;             // 事务ID
  optional  uint32    srvFlag = 34;           // 服务器标志
  optional  bytes     ansRes = 35;            // 回答资源记录
  optional  uint32    authAnsType = 36;       // 权威回答类型
  optional  bytes     authAnsRes = 37;        // 权威回答资源记录
  optional  uint32    addAnsType = 38;        // 附加回答类型
  optional  bytes     addAnsRes = 39;         // 附加回答资源记录
  repeated  bytes     mxIpv6 = 40;            // MX记录的IPv6地址列表
  optional  uint32    mxIpv6Cnt = 41;         // MX记录的IPv6地址数量
  repeated  bytes     nsIpv6 = 42;            // NS记录的IPv6地址列表
  optional  uint32    nsIpv6Cnt = 43;         // NS记录的IPv6地址数量
}