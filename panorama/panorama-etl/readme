panorama-etl-fake-filter 虚警研判过滤ETL应用
启动命令：flink run -c com.geeksec.task.PanoramaFakeAlertFilterTask panorama-etl-fake-filter-1.0.0-SNAPSHOT.jar --config_path ./config.properties


panorama-etl-sink-nebula 告警日志图数据库实体&关系抽取ETL应用
启动命令: flink run -c com.geeksec.task.PanoramaAlertKafka2Nebula panorama-etl-sink-nebula-1.0.0-SNAPSHOT.jar --config_path ./config.properties

flink UI 启动参数：
--config_path /opt/GeekSec/panorama/flink-web-upload/etl-conf/fake-filter.properties
--config_path /opt/GeekSec/panorama/flink-web-upload/etl-conf/sink-nebula.properties