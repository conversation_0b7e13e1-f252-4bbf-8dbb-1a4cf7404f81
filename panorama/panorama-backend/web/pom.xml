<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>panorama-backend</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>web</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- 通用模块 -->
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>ngbatis</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>core</artifactId>
            <version>1.0.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.flink</groupId>
            <artifactId>flink-shaded-zookeeper-3</artifactId>
            <version>3.7.1-17.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>