package com.geeksec.panorama.controller;

import com.geeksec.ngbatis.vo.VertexAssociationEdgeVo;
import com.geeksec.panorama.entity.Result;
import com.geeksec.panorama.enums.EdgeType;
import com.geeksec.panorama.exception.EventNotFoundException;
import com.geeksec.panorama.model.ModelRunVo;
import com.geeksec.panorama.model.StatusUpdateRequest;
import com.geeksec.panorama.service.ModelRunService;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

/**
 * <AUTHOR>
 * @since 2024-08-29
 *     <p>Description: 事件模型管理接口
 */
@RestController
@RequestMapping("/runs")
@Slf4j
public class ModelRunController {
  private final ModelRunService modelRunService;
  private KafkaTemplate<String, Object> kafkaTemplate;
  private static final String KAFKA_TOPIC = "threat-event";
  private static final DateTimeFormatter DATE_FORMATTER =
      DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

  @Autowired
  public ModelRunController(
      ModelRunService modelRunService, KafkaTemplate<String, Object> kafkaTemplate) {
    this.modelRunService = modelRunService;
    this.kafkaTemplate = kafkaTemplate;
  }

  @GetMapping("/{id}")
  public Result<ModelRunVo> getRun(@PathVariable String id) {
    return Result.success(modelRunService.getRunById(id));
  }

  @PostMapping("/{id}/report")
  public ResponseEntity<String> reportRun(@PathVariable String id) {
    try {
      //      EventDto event = eventService.getEventsByIds(Collections.singletonList(id)).get(0);
      //      kafkaTemplate.send(KAFKA_TOPIC, event);
      log.info("Run reported successfully. RunID: {}", id);
      return ResponseEntity.ok("Event reported successfully");
    } catch (Exception e) {
      log.error("Error reporting run. RunID: {}", id, e);
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error reporting run");
    }
  }

  @GetMapping("/{id}/edges")
  public Result<List<VertexAssociationEdgeVo>> getEdgesBetweenVertices(
      @PathVariable @NotBlank(message = "runId不能为空") String id,
      @RequestParam @NotBlank(message = "结束事件ID不能为空") String endEventId,
      @RequestParam @NotBlank(message = "起始顶点ID不能为空") String fromVertexId,
      @RequestParam @NotBlank(message = "目标顶点ID不能为空") String toVertexId,
      @RequestParam @NotBlank(message = "边类型不能为空") String edgeType) {
    log.info(
        "Fetching edges between vertices for run ID: {}, up to event ID: {}, from: {}, to: {}, type: {}",
        id,
        endEventId,
        fromVertexId,
        toVertexId,
        edgeType);

    List<VertexAssociationEdgeVo> edges =
        modelRunService.getEdgesBetweenVertices(
            id,
            endEventId,
            fromVertexId,
            toVertexId,
            EdgeType.fromCode(Integer.parseInt(edgeType)));

    if (edges.isEmpty()) {
      log.info("No edges found between the specified vertices");
      return Result.success(Collections.emptyList());
    }

    return Result.success(edges);
  }

  @PutMapping("/{id}/status")
  public ResponseEntity<String> updateRunStatus(
      @PathVariable String id, @RequestBody StatusUpdateRequest request) {
    log.info("Updating run status. RunID: {}, NewStatus: {}", id, request.getStatus());
    try {
      boolean updated = modelRunService.updateRunStatus(id, request.getStatus());
      if (updated) {
        return ResponseEntity.ok().body("Run updated successfully");
      } else {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Failed to update run status");
      }
    } catch (EventNotFoundException e) {
      return ResponseEntity.status(HttpStatus.NOT_FOUND).body(e.getMessage());
    }
  }

  private ResponseEntity<StreamingResponseBody> getStreamingResponseBody(
      List<ModelRunVo> modelRuns, String fileNamePrefix) {
    StreamingResponseBody responseBody =
        outputStream -> {
          try (Writer writer = new OutputStreamWriter(outputStream, StandardCharsets.UTF_8);
              CSVPrinter csvPrinter =
                  new CSVPrinter(
                      writer,
                      CSVFormat.DEFAULT
                          .builder()
                          .setHeader(
                              "事件名称", "事件类别", "事件类型", "事件等级", "事件状态", "模型种类", "事件描述", "开始时间",
                              "结束时间", "告警总数", "受害IP数量", "攻击者IP数量", "受害者IP分布", "攻击者IP数量", "攻击者IP分布",
                              "数据来源分布", "威胁等级分布", "攻击链数量", "攻击链分布", "技战术分布", "漏洞总数", "漏洞分布", "文件数量",
                              "文件分布", "可疑域名数量", "可疑域名信息", "事件发现者", "事件更新时间", "事件发现时间")
                          .build())) {
            for (ModelRunVo run : modelRuns) {
              csvPrinter.printRecord(
                  //                  run.getEventName(),
                  //                  run.getEventClass(),
                  //                  run.getEventType(),
                  //                  run.getSeverity().getDesc(),
                  //                  run.getStatus().getDesc(),
                  //                  run.getModelType().getDesc(),
                  //                  run.getDescription(),
                  //                  formatDateTime(run.getStartTime()),
                  //                  formatDateTime(run.getEndTime()),
                  //                  run.getAlertCount(),
                  //                  run.getVictimIpCount(),
                  //                  run.getVictimIpCountInfo(),
                  //                  run.getAttackerIpCount(),
                  //                  run.getAttackerIpCountInfo(),
                  //                  run.getVendorIdCountInfo(),
                  //                  run.getThreatLevelCountInfo(),
                  //                  run.getKillChainCount(),
                  //                  run.getKillChainCountInfo(),
                  //                  run.getIoaTagCountInfo(),
                  //                  run.getVulnerabilityCount(),
                  //                  run.getVulnerabilityCountInfo(),
                  //                  run.getFileCount(),
                  //                  run.getFileCountInfo(),
                  //                  run.getQueryCount(),
                  //                  run.getQueryCountInfo(),
                  //                  run.getCreatedBy(),
                  //                  formatDateTime(run.getUpdateTime()),
                  formatDateTime(run.getRunTime()));
            }
            csvPrinter.flush();
          } catch (IOException e) {
            log.error("Error while writing CSV", e);
          }
        };

    String filename =
        fileNamePrefix
            + "_"
            + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"))
            + ".csv";

    return ResponseEntity.ok()
        .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
        .contentType(MediaType.parseMediaType("text/csv"))
        .body(responseBody);
  }

  private String formatDateTime(LocalDateTime dateTime) {
    return dateTime != null ? dateTime.format(DATE_FORMATTER) : "";
  }
}
