package com.geeksec.panorama.controller;


import com.geeksec.panorama.dao.CustomAttackModelDao;
import com.geeksec.panorama.entity.Result;
import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.enums.GkError;
import com.geeksec.panorama.model.*;
import com.geeksec.panorama.service.ScheduleTaskService;
import org.quartz.SchedulerException;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/task")
public class TaskManageController {

    final ScheduleTaskService scheduleTaskService;
    final CustomAttackModelDao customAttackModelDao;

    @Autowired
    public TaskManageController(ScheduleTaskService scheduleTaskService, CustomAttackModelDao customAttackModelDao) {
        this.scheduleTaskService = scheduleTaskService;
        this.customAttackModelDao = customAttackModelDao;
    }

    @PostMapping("/add")
    public Result<?> addTask(@RequestBody ScheduleTask entity) {
        String insertedTask = scheduleTaskService.insertTask(entity);
        return Result.successMsg("创建任务成功, taskId: " + insertedTask);
    }

    @DeleteMapping("/del/{taskId}")
    public Result<?> delTask(@PathVariable String taskId) {
        scheduleTaskService.del(taskId);
        return Result.successMsg("删除任务成功");
    }

    @PutMapping("/switch")
    public Result<?> updateTaskStatus(@RequestBody ScheduleTaskUpdate entity) {
        scheduleTaskService.update(entity);
        return Result.successMsg("更新任务状态成功");
    }

    @PutMapping("/setting")
    public Result<?> updateTaskSettingStatus(@RequestBody ScheduleTaskUpdate entity) {
        scheduleTaskService.updateSetting(entity);
        return Result.successMsg("更新任务参数成功");
    }


    @PostMapping("/list")
    public Result<PageResult<ScheduleTaskVo>> listTask(@RequestBody TaskPageCondition condition) {
        return Result.success(scheduleTaskService.list(condition));
    }

    @GetMapping("/jobs")
    public Result<List<Map<String, Object>>> getAllJobs() throws SchedulerException {
        return Result.success(scheduleTaskService.getAllJobs());
    }

    @GetMapping("/models")
    public Result<List<TaskModelDto>> getAllModels() {
        String userId = MDC.get("user_id");
        List<TaskModelDto> customAttackModels = customAttackModelDao.selectModels(userId);
        if (customAttackModels.isEmpty()) {
            return Result.fail(GkError.MODEL_NOT_FOUND.getCode(), "暂无模型");
        }
        return Result.success(customAttackModels);
    }
}

