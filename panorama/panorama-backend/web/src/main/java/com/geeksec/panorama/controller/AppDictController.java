package com.geeksec.panorama.controller;


import com.geeksec.panorama.entity.Result;
import com.geeksec.panorama.service.DictionaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description：
 */
@RestController
@CrossOrigin(origins = "*")
public class AppDictController {

    @Autowired
    private DictionaryService dictService;

    @GetMapping("/dict")
    public Result<Map<String, Object>> getAppDict() throws IOException {
        Map<String,Object> result = dictService.getDict();
        return Result.success(result);
    }
}
