package com.geeksec.panorama.controller;

import com.geeksec.panorama.entity.AttackEvent;
import com.geeksec.panorama.entity.Result;
import com.geeksec.panorama.exception.EventNotFoundException;
import com.geeksec.panorama.service.AttackEventService;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @author: jerryzhou
 * @date: 2024/8/26 20:56 @Description:
 */
@RestController
@RequestMapping("/events")
@Slf4j
@Validated
public class AttackEventController {

  private final AttackEventService attackEventService;

  @Autowired
  public AttackEventController(AttackEventService attackEventService) {
    this.attackEventService = attackEventService;
  }

  /**
   * 通过事件ID获取图事件结果
   *
   * @param eventId
   * @return
   */
  @GetMapping("/{id}")
  public Result<AttackEvent> getAttackEvent(
      @PathVariable("id") @NotBlank(message = "事件ID不能为空") String eventId) {
    log.info("Fetching attack event with ID: {}", eventId);
    AttackEvent result = attackEventService.getEventById(eventId);
    if (result == null) {
      throw new EventNotFoundException("ID为" + eventId + "的事件不存在");
    }
    return Result.success(result);
  }

  /**
   * 获取特定runId的第一个事件
   *
   * @param runId
   * @return
   */
  @GetMapping("/first")
  public Result<AttackEvent> getFirstEvent(
      @RequestParam @NotBlank(message = "runId不能为空") String runId) {
    log.info("Fetching first event for runId: {}", runId);
    return attackEventService
        .getFirstEvent(runId)
        .map(Result::success)
        .orElseGet(
            () -> {
              log.info("No events found for runId: {}", runId);
              return Result.success(null);
            });
  }

  /**
   * 获取特定runId的下一个事件
   *
   * @param runId
   * @param after
   * @return
   */
  @GetMapping("/next")
  public Result<AttackEvent> getNextEvent(
      @RequestParam @NotBlank(message = "runId不能为空") String runId,
      @RequestParam @NotBlank(message = "当前事件ID不能为空") String after) {
    log.info("Fetching next event for runId: {} after event ID: {}", runId, after);
    return attackEventService
        .getNextEvent(runId, after)
        .map(Result::success)
        .orElseGet(
            () -> {
              log.info("No next event found for runId: {} after event ID: {}", runId, after);
              return Result.success(null);
            });
  }
}
