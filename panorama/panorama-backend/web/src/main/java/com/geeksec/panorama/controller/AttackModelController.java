package com.geeksec.panorama.controller;

import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.entity.PresetAttackModel;
import com.geeksec.panorama.entity.Result;
import com.geeksec.panorama.exception.NgqlExecutionException;
import com.geeksec.panorama.model.CustomAttackModelDto;
import com.geeksec.panorama.model.GraphPattern;
import com.geeksec.panorama.model.PageResult;
import com.geeksec.panorama.model.PresetModelUpdateDto;
import com.geeksec.panorama.service.CustomAttackModelService;
import com.geeksec.panorama.service.ModelExecutionService;
import com.geeksec.panorama.service.PresetAttackModelService;
import com.vesoft.nebula.client.graph.data.ResultSet;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @since 2024-07-17 攻击模型控制器，处理预置攻击模型和自定义攻击模型的 CRUD 操作和状态管理
 */
@RestController
@RequestMapping("/models")
@Slf4j
@Validated
public class AttackModelController {

  private final PresetAttackModelService presetAttackModelService;

  private final CustomAttackModelService customAttackModelService;
  private final ModelExecutionService modelExecutionService;

  @Autowired
  public AttackModelController(
      PresetAttackModelService presetAttackModelService,
      CustomAttackModelService customAttackModelService,
      ModelExecutionService modelExecutionService) {
    this.presetAttackModelService = presetAttackModelService;
    this.customAttackModelService = customAttackModelService;
    this.modelExecutionService = modelExecutionService;
  }

  /**
   * 获取预置攻击模型列表（分页）
   *
   * @param page 页码（从1开始）
   * @param pageSize 每页大小（最大50）
   * @return 包含预置攻击模型列表的分页结果
   */
  @GetMapping("/preset")
  public Result<PageResult<PresetAttackModel>> getPresetModelPage(
      @RequestParam(defaultValue = "1") @Min(1) int page,
      @RequestParam(defaultValue = "10") @Min(1) @Max(50) int pageSize) {
    log.info("Fetching preset attack model list. Page: {}, Size: {}", page, pageSize);
    PageResult<PresetAttackModel> pageResult =
        presetAttackModelService.getPresetModelPage(page, pageSize);
    return Result.success(pageResult);
  }

  /**
   * 修改预置攻击模型
   *
   * @param id 预置攻击模型ID
   * @param updateDto 更新数据传输对象
   * @return 操作结果
   */
  @PatchMapping("/preset/{id}")
  public Result<Void> modifyPresetModel(
      @PathVariable @NotBlank String id, @RequestBody PresetModelUpdateDto updateDto) {
    log.info("Updating preset model. ID: {}", id);
    presetAttackModelService.updatePresetModel(id, updateDto);
    return Result.successMsg("更新预置模型成功");
  }

  /**
   * 激活预置攻击模型
   *
   * @param id 预置攻击模型ID
   * @return 操作结果
   */
  @PostMapping("/preset/{id}/resume")
  public Result<Void> activatePresetModel(@PathVariable String id) {
    return togglePresetModel(id, true);
  }

  /**
   * 停用预置攻击模型
   *
   * @param id 预置攻击模型ID
   * @return 操作结果
   */
  @PostMapping("/preset/{id}/pause")
  public Result<Void> deactivatePresetModel(@PathVariable String id) {
    return togglePresetModel(id, false);
  }

  /**
   * 切换预置攻击模型状态
   *
   * @param id 预置攻击模型ID
   * @param active 是否激活
   * @return 操作结果
   */
  private Result<Void> togglePresetModel(String id, boolean active) {
    log.info("Toggling preset attack model status. ID: {}, Active: {}", id, active);
    presetAttackModelService.toggleModelStatus(id, active);
    String action = active ? "恢复" : "暂停";
    return Result.successMsg(action + "预置模型成功");
  }

  /**
   * 获取自定义攻击模型列表（分页）
   *
   * @param page 页码（从1开始）
   * @param pageSize 每页大小（最大50）
   * @return 包含自定义攻击模型列表的分页结果
   */
  @GetMapping("/custom")
  public Result<PageResult<CustomAttackModel>> getCustomModelPage(
      @RequestParam(defaultValue = "1") @Min(1) int page,
      @RequestParam(defaultValue = "10") @Min(1) @Max(50) int pageSize) {
    log.info("Fetching custom attack model list. Page: {}, Size: {}", page, pageSize);
    PageResult<CustomAttackModel> pageResult =
        customAttackModelService.getCustomModelPage(page, pageSize);
    return Result.success(pageResult);
  }

  /**
   * 创建自定义攻击模型
   *
   * @param model 自定义攻击模型数据传输对象
   * @return 操作结果
   */
  @PostMapping("/custom")
  public Result<Void> createCustomModel(@RequestBody @Valid CustomAttackModelDto model) {
    log.info("Creating custom attack model");
    customAttackModelService.createCustomModel(model);
    return Result.successMsg("创建自定义攻击模型成功");
  }

  /**
   * 修改自定义攻击模型
   *
   * @param id 自定义攻击模型ID
   * @param model 更新后的自定义攻击模型数据传输对象
   * @return 操作结果
   */
  @PutMapping("/custom/{id}")
  public Result<Void> modifyCustomEventModel(
      @PathVariable String id, @RequestBody @Valid CustomAttackModelDto model) {
    log.info("Updating custom attack model. ID: {}", id);
    customAttackModelService.updateCustomModel(id, model);
    return Result.successMsg("修改自定义攻击模型成功");
  }

  /**
   * 删除自定义攻击模型
   *
   * @param id 自定义攻击模型ID
   * @return 操作结果
   */
  @DeleteMapping("/custom/{id}")
  public Result<Void> deleteCustomModel(@PathVariable @Valid String id) {
    log.info("Deleting custJom attack model. ID: {}", id);
    customAttackModelService.deleteCustomModel(id);
    return Result.successMsg("删除自定义攻击模型成功");
  }

  /**
   * 运行自定义攻击模型
   *
   * @param id 自定义攻击模型ID
   * @return 操作结果
   */
  @PostMapping("/custom/{id}/run")
  public Result<Void> runCustomModel(@PathVariable @Valid String id) {
    log.info("Running custom attack model. ID: {}", id);
    customAttackModelService.run(id,"");
    return Result.successMsg("自定义攻击模型开始运行");
  }

  /**
   * 尝试执行查询
   *
   * @param pattern 图模式
   * @return 包含是否有查询结果的结果
   */
  @PostMapping("/custom/try")
  public Result<String> tryCustomModel(@RequestBody @Valid GraphPattern pattern) {
    log.info("Trying custom attack model query with provided graph pattern");

    ResultSet results = modelExecutionService.executeQuery(pattern);
    if (!results.isSucceeded()) {
      throw new NgqlExecutionException("查询失败");
    }
    return Result.success(results.isEmpty() ? "无命中结果" : "有命中结果");
  }
}
