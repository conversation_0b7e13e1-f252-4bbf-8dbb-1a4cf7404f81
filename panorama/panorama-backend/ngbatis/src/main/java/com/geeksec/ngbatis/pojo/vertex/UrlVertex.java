package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
*@description: URL点
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "URL")
@Data
public class UrlVertex {

  @Id
  @Column(name = "url_key")
  private String urlKey;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

}
