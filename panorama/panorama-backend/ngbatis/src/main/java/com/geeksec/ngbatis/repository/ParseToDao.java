package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.pojo.edge.ParseToEdge;
import com.geeksec.ngbatis.vo.DomainIpVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ParseToDao extends NebulaDaoBasic<ParseToEdge, String> {

    /**
     * 根据域名查询边类型为parse_to和cname_result的实体
     */
    List<DomainIpVo> listParseToCnameResultByDomains(@Param("domains") List<String> domains);

}
