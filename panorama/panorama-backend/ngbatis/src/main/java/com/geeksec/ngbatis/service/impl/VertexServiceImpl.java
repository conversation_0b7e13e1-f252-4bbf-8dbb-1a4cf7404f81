package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.service.CertService;
import com.geeksec.ngbatis.service.DomainService;
import com.geeksec.ngbatis.service.IpService;
import com.geeksec.ngbatis.service.VertexService;
import com.geeksec.ngbatis.vo.*;
import com.geeksec.ngbatis.vo.VertexAssociationEdgeVo;
import com.geeksec.ngbatis.vo.VertexAssociationVertexVo;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;
import com.geeksec.panorama.enums.VertexType;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @description: 点服务实现类
 * @author: shiwenxu
 * @createtime: 2023/8/31 15:40
 */
@Service
public class VertexServiceImpl implements VertexService {

  @Autowired private IpService ipService;
  @Autowired private DomainService domainService;
  @Autowired private CertService certService;

  /** 点关联查询 */
  @Override
  public VertexAssociationVo getAssociation(String param, String type) {
    VertexAssociationVo vertexAssociationVo = new VertexAssociationVo();
    List<VertexEdgeVo> vertexEdgeVoList = new ArrayList<>();
    switch (type) {
      case "IP":
        vertexEdgeVoList = ipService.getIpNebulaAssociation(param);
        break;
      case "DOMAIN":
        vertexEdgeVoList = domainService.getDomainNebulaAssociation(param);
        break;
      case "CERT":
        vertexEdgeVoList = certService.getCertNebulaAssociation(param);
        break;
    }
    // 根据关联数据组装所有关联点和边
    this.setVertexEdgeList(vertexAssociationVo, vertexEdgeVoList);
    return vertexAssociationVo;
  }

  @Override
  public VertexAssociationNextVo getAssociationNext(GraphNextInfoCondition condition) {
    VertexAssociationNextVo vertexAssociationNextVo = new VertexAssociationNextVo();
    List<VertexEdgeNextVo> vertexEdgeNextVoList = new ArrayList<>();
    switch (condition.getType()) {
      case "IP":
        vertexEdgeNextVoList = ipService.getIpNebulaAssociationNext(condition);
        break;
    }
    // 根据关联数据组装所有关联点和边
    this.setVertexEdgeNextList(vertexAssociationNextVo, vertexEdgeNextVoList);
    return vertexAssociationNextVo;
  }

  /** 根据关联数据组装所有关联点和边 */
  public void setVertexEdgeList(
      VertexAssociationVo vertexAssociationVo, List<VertexEdgeVo> vertexEdgeVoList) {
    Set<VertexAssociationVertexVo> vertexList = new LinkedHashSet<>();
    Set<VertexAssociationEdgeVo> edgeList = new LinkedHashSet<>();
    // 根据关联数据组装所有关联点和边
    for (VertexEdgeVo vertexEdgeVo : vertexEdgeVoList) {
      if (vertexEdgeVo.getSourceStatus()) {
        // 组装源数据点
        vertexList.add(
            new VertexAssociationVertexVo() {
              {
                setType(VertexType.fromName(vertexEdgeVo.getFromType()));
                setLv(vertexEdgeVo.getFromBlackList());
                setLabel(vertexEdgeVo.getFromAddr());
                setId(vertexEdgeVo.getFromAddr());
                setMain("YES");
              }
            });
      } else {
        // 组装起始点
        vertexList.add(
            new VertexAssociationVertexVo() {
              {
                setType(VertexType.fromName(vertexEdgeVo.getFromType()));
                setLv(vertexEdgeVo.getFromBlackList());
                setLabel(vertexEdgeVo.getFromAddr());
                setId(vertexEdgeVo.getFromAddr());
              }
            });
        // 组装终点
        vertexList.add(
            new VertexAssociationVertexVo() {
              {
                setType(VertexType.fromName(vertexEdgeVo.getToType()));
                setLv(vertexEdgeVo.getToBlackList());
                setLabel(vertexEdgeVo.getToAddr());
                setId(vertexEdgeVo.getToAddr());
              }
            });
        // 组装中间点
        vertexList.add(
            new VertexAssociationVertexVo() {
              {
                setType(VertexType.fromName(vertexEdgeVo.getMiddleType()));
                setLabel(vertexEdgeVo.getMiddleLabel());
                setId(vertexEdgeVo.getMiddleId());
              }
            });
        // 组装起点到中间点的边
        edgeList.add(
            new VertexAssociationEdgeVo() {
              {
                setFrom(vertexEdgeVo.getFromAddr());
                setTo(vertexEdgeVo.getMiddleId());
                if (vertexEdgeVo.getDirectionStatus()) {
                  // 如果是正向设置起点到中间点的边的label
                  setLabel(vertexEdgeVo.getMiddleId());
                }
              }
            });
        // 组装中间点到终点的边
        edgeList.add(
            new VertexAssociationEdgeVo() {
              {
                setFrom(vertexEdgeVo.getMiddleId());
                setTo(vertexEdgeVo.getToAddr());
                if (!vertexEdgeVo.getDirectionStatus()) {
                  // 如果是反向设置中间点到终点的边的label
                  setLabel(vertexEdgeVo.getMiddleId());
                }
              }
            });
      }
    }
    vertexAssociationVo.setVertex(vertexList);
    vertexAssociationVo.setEdge(edgeList);
  }

  /** 根据关联数据组装所有关联点和边 */
  public void setVertexEdgeNextList(
      VertexAssociationNextVo vertexAssociationNextVo,
      List<VertexEdgeNextVo> vertexEdgeNextVoList) {
    Set<VertexAssociationVertexNextVo> vertexList = new LinkedHashSet<>();
    Set<VertexAssociationEdgeVo> edgeList = new LinkedHashSet<>();
    // 根据关联数据组装所有关联点和边
    if (vertexEdgeNextVoList != null && vertexEdgeNextVoList.size() > 1) {
      for (VertexEdgeNextVo vertexEdgeNextVo : vertexEdgeNextVoList) {
        if (vertexEdgeNextVo.getSourceStatus()) {
          // 组装源数据点
          vertexList.add(
              new VertexAssociationVertexNextVo() {
                {
                  setType(vertexEdgeNextVo.getFromType());
                  setLv(vertexEdgeNextVo.getFromBlackList());
                  setLabel(vertexEdgeNextVo.getFromAddr());
                  setId(vertexEdgeNextVo.getFromAddr());
                }
              });
        } else {
          // 组装终点
          vertexList.add(
              new VertexAssociationVertexNextVo() {
                {
                  setType(vertexEdgeNextVo.getToType());
                  setLv(vertexEdgeNextVo.getToBlackList());
                  setLabel(vertexEdgeNextVo.getToAddr());
                  setId(vertexEdgeNextVo.getToAddr());
                  if (vertexEdgeNextVo.getVInfo() != null) {
                    setVInfo(
                        vertexEdgeNextVo.getVInfo().entrySet().stream()
                            .collect(
                                Collectors.toMap(
                                    Map.Entry::getKey,
                                    e -> e.getValue() != null ? e.getValue().toString() : "")));
                  }
                }
              });
          // 组装起点到终点的边
          edgeList.add(
              new VertexAssociationEdgeVo() {
                {
                  setFrom(vertexEdgeNextVo.getFromAddr());
                  setTo(vertexEdgeNextVo.getToAddr());
                  setLabel(vertexEdgeNextVo.getTeage());
                }
              });
        }
      }
    }
    vertexAssociationNextVo.setVertex(vertexList);
    vertexAssociationNextVo.setEdge(edgeList);
  }
}
