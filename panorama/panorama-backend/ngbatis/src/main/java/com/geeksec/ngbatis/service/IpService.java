package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.vo.*;
import com.geeksec.ngbatis.vo.IpHasLabelVo;
import com.geeksec.ngbatis.vo.IpRelatedDomainsVo;
import com.geeksec.ngbatis.vo.VertexEdgeNextVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
*@description: Ip服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface IpService {

    /**
    * IP关联查询
    */
    List<VertexEdgeVo> getIpNebulaAssociation(String ip);

    /**
     * IP关联查询Next
     */
    List<VertexEdgeNextVo> getIpNebulaAssociationNext(GraphNextInfoCondition condition);

    /**
     * 根据app_server边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByAppServerEdge(List<String> ips);

    /**
     * 根据client_app边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByClientAppEdge(List<String> ips);

    /**
     * 根据ids查询ip详情
     */
    List<IpVo> listByIps(List<String> ips);

    /**
     * 根据ids查询标签
     */
    List<IpHasLabelVo> listHasLabelByIps(List<String> ips);

    /**
     * 根据域名的边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByDomainEdge(List<String> ips);

    /**
     * 根据证书的边类型进行IP聚合总数查询
     */
    List<IpCountVo> countIpByCertEdge(List<String> ips);

    /**
     * 根据ids查询存在IP属性
     */
    List<ExistIpPropertiesVo> listExistIpProperties(List<String> ips);

    /**
     * 查询ip详情
     */
    Map<String, Object> getIpInfo(String ip);

    /**
     * 统计开放端口数
     */
    IpCountVo countAppServerByIp(String ip);

    /**
     * 统计访问端口数
     */
    IpCountVo countClientAppByIp(String ip);

    /**
     * 关联锚域名（parse_to）
     */
    IpRelatedDomainsVo listParseToRelatedDomainsByIp(String ip);

    /**
     * 关联锚域名（server_ssl_connect_domain SSL应用 server_http_connect_domain HTTP应用）
     */
    IpRelatedDomainsVo listServerRelatedDomainsByIp(String ip);

    /**
     * 根据domains统计所属锚域名数
     */
    Integer countFDomainNumByDomains(Set<String> domains);

    /**
     * 根据id查询标签
     */
    List<Long> listHasLabelByIp(String ip);

    /**
     * 修改备注
     */
    void updateRemark(String type, String id, String remark);

}
