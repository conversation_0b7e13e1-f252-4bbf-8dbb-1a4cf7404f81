package com.geeksec.ngbatis.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.panorama.enums.VertexType;
import java.util.Map;
import java.util.Objects;
import lombok.Data;

/**
 * @description: 点关联关系点Vo
 * @author: shiwenxu
 * @createtime: 2023/8/30 11:28
 */
@Data
public class VertexAssociationVertexVo {

  private String num = "";

  private Long lv = 0L;

  private String main = "";

  private String label = "";

  private String id = "";

  private VertexType type;

  private String status = "";

  @JsonProperty(value = "v_info")
  private Map<String, Object> vInfo;

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }
    if (obj instanceof VertexAssociationVertexVo) {
      VertexAssociationVertexVo p = (VertexAssociationVertexVo) obj;
      if ((this.getNum().equals(p.getNum()))
          && (this.getLv().equals(p.getLv()))
          && (this.getId().equals(p.getId()))
          && (this.getType().equals(p.getType()))
          && (this.getStatus().equals(p.getStatus()))) {
        return true;
      }
    }
    return false;
  }

  @Override
  public int hashCode() {
    return Objects.hash(num, lv, id, type, status);
  }
}
