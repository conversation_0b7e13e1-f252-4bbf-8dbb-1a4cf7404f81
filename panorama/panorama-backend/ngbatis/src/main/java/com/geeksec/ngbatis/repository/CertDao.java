package com.geeksec.ngbatis.repository;

import com.geeksec.ngbatis.pojo.vertex.DomainVertex;
import com.geeksec.ngbatis.vo.*;
import com.geeksec.ngbatis.vo.CertCountVo;
import com.geeksec.ngbatis.vo.CertVo;
import com.geeksec.ngbatis.vo.ExistCertPropertiesVo;
import org.nebula.contrib.ngbatis.proxy.NebulaDaoBasic;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Map;

public interface CertDao extends NebulaDaoBasic<DomainVertex, String> {

    /**
     * 查询证书所有边类型关联数据
     */
    List<VertexEdgeVo> listCertAllEdgeTypeAssociation(@Param("cert") String cert);

    /**
     * 根据ids查询存在证书属性
     */
    List<ExistCertPropertiesVo> listExistCertProperties(@Param("certs") List<String> certs);

    /**
     * 根据ids查询证书详情
     */
    List<CertVo> listByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询客户端热度
     */
    List<CertRelatedIpsVo> listRelatedIpsByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询关联域名个数
     */
    List<CertCountVo> countDomainNumByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询关联服务器ip个数
     */
    List<CertCountVo> countServerIpNumByCerts(@Param("certs") List<String> certs);

    /**
     * 根据ids查询关联SSL个数
     */
    List<CertCountVo> countSslIpNumByCerts(@Param("certs") List<String> certs);

    /**
     * 查询证书详情
     */
    Map<String, Object> getCertInfo(@Param("cert") String cert);

    /**
     * 根据id查询标签
     */
    List<Long> listHasLabelByCert(@Param("cert") String cert);

    /**
     * 根据id查询服务端热度
     */
    List<String> listRelatedServerIpsByCert(@Param("cert") String cert);

    /**
     * 根据id查询客户端热度
     */
    List<String> listRelatedClientIpsByCert(@Param("cert") String cert);

    /**
     * 根据id查询任务id列表
     */
    List<String> getTaskIds(@Param("cert") String cert);

}
