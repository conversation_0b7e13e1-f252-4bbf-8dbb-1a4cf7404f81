package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
*@description: 资产测绘数据
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "SURVEY_DATA")
@Data
public class SurveyDataVertex {

  /**
  * 测绘ID（不展示）
  */
  @Id
  @Column(name = "survey_key")
  private String surveyKey;

  /**
  * IP地址
  */
  @Column(name = "ip_addr")
  private String ipAddr;

  /**
  * 端口
  */
  private Long port;

  /**
  * 协议名
  */
  private String protocol;

  /**
  * 域名
  */
  private String domain;

  /**
  * 国家名
  */
  @Column(name = "country_name")
  private String countryName;

  /**
  * 城市
  */
  private String city;

  /**
  * ICP备案号
  */
  private String icp;

  /**
  * 网站服务器
  */
  private String server;

  /**
  * jarm指纹
  */
  private String jarm;

  /**
  * 网站标题
  */
  private String title;

  /**
  * asn组织
  */
  @Column(name = "as_organization")
  private String asOrganization;

  /**
  * 数据来源
  */
  private String source;

  /**
  * 网站header
  */
  private String header;

  /**
  * 协议banner
  */
  private String banner;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

  /**
  * 产品目录
  */
  @Column(name = "product_catalog")
  private String productCatalog;

  /**
  * 产品类型
  */
  @Column(name = "product_type")
  private String productType;

  /**
  * 产品等级
  */
  @Column(name = "product_level")
  private String productLevel;

  /**
  * 产品供应商
  */
  @Column(name = "product_vendor")
  private String productVendor;

}
