package com.geeksec.ngbatis.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.panorama.enums.EdgeType;
import java.util.Map;
import lombok.Data;

/**
 * @description: 点关联关系边Vo
 * @author: shiwenxu
 * @createtime: 2023/8/30 11:28
 */
@Data
public class VertexAssociationEdgeVo {

  private String num = "";

  private String from = "";

  private Long lv = 0L;

  private String to = "";

  private String label = "";

  private String id = "";

  private EdgeType type;

  private String status = "";

  /** 边所包含的属性 */
  @JsonProperty(value = "edge_info")
  private Map<String, Object> edgeInfo;
}
