package com.geeksec.ngbatis.service;

import com.geeksec.ngbatis.condition.GraphNextInfoCondition;
import com.geeksec.ngbatis.vo.VertexAssociationNextVo;
import com.geeksec.ngbatis.vo.VertexAssociationVo;

/**
*@description: 点服务
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
public interface VertexService {

    /**
    * 点关联查询
    */
    VertexAssociationVo getAssociation(String str, String type);

    /**
     * 点关联查询Next
     */
    VertexAssociationNextVo getAssociationNext(GraphNextInfoCondition condition);

}
