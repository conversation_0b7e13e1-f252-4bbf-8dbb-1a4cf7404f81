package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
*@description: 应用类型
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "APPTYPE")
@Data
public class ApptypeVertex {

  /**
  * 应用名
  */
  @Id
  private String Application;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

}
