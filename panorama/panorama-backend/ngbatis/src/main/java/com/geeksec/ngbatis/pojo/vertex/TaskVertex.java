package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 任务
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "TASK")
@Data
public class TaskVertex {

  /**
   * 任务ID
   */
  @Id
  @Column(name = "task_id")
  private Long taskId;

  /**
  * 批次ID
  */
  @Column(name = "batch_id")
  private Long batchId;

  /**
  * 任务名称
  */
  @Column(name = "task_name")
  private String taskName;

  /**
  * 任务解释
  */
  @Column(name = "task_remark")
  private String taskRemark;

  /**
  * 创建时间
  */
  @Column(name = "created_time")
  private Timestamp createdTime;

  /**
  * 任务状态
  */
  private Integer state;

}
