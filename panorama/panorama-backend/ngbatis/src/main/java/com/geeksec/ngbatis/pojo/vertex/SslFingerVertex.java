package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
*@description: SSL指纹
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "SSLFINGER")
@Data
public class SslFingerVertex {

  /**
  * 指纹ID
  */
  @Id
  @Column(name = "finger_id")
  private String fingerId;

  /**
  * Ja3指纹Hash
  */
  @Column(name = "ja3_hash")
  private String ja3Hash;

  /**
  * 指纹说明
  */
  @Column(name = "finger_desc")
  private String fingerDesc;

  /**
  * 客户端还是服务端
  */
  private String type;

}
