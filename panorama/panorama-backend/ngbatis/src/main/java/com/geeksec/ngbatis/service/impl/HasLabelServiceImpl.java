package com.geeksec.ngbatis.service.impl;

import com.geeksec.ngbatis.repository.HasLabelDao;
import com.geeksec.ngbatis.service.HasLabelService;
import com.geeksec.ngbatis.vo.HasLabelVertexVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
*@description: 标签服务实现类
*@author: shiwenxu
*@createtime: 2023/8/31 15:40
**/
@Service
public class HasLabelServiceImpl implements HasLabelService {

    @Autowired
    private HasLabelDao hasLabelDao;

    /**
    * 查询当前符合标签条件的实体
    */
    @Override
    public List<HasLabelVertexVo> listTagVertexByTagIds(List<String> tagIds) {
        return hasLabelDao.listTagVertexByTagIds(tagIds);
    }
}
