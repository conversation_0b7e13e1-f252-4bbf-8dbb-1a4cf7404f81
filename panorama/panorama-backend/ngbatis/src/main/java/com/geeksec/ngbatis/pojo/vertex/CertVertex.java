package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

/**
*@description: 证书
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "CERT")
@Data
public class CertVertex {

  /**
  * 证书id
  */
  @Id
  @Column(name = "cert_id")
  private String certId;

  /**
  * 首次出现时间
  */
  @Column(name = "first_time")
  private Timestamp firstTime;

  /**
  * 末次出现时间
  */
  @Column(name = "last_time")
  private Timestamp lastTime;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

  /**
  * 备注
  */
  private String remark;

}
