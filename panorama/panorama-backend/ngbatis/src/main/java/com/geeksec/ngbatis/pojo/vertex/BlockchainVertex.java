package com.geeksec.ngbatis.pojo.vertex;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

/**
*@description: 区块链地址
*@author: shiwenxu
*@createtime: 2023/8/30 11:43
**/
@Table(name = "BLOCKCHAIN")
@Data
public class BlockchainVertex {

  /**
  * 区块链地址
  */
  @Id
  private String addr;

  /**
  * 来源
  */
  @Column(name = "chain_source")
  private String chainSource;

  /**
  * 余额
  */
  @Column(name = "balance_account")
  private Long balanceAccount;

  /**
  * 黑名单权值
  */
  @Column(name = "black_list")
  private Integer blackList;

  /**
  * 白名单权值
  */
  @Column(name = "white_list")
  private Integer whiteList;

}
