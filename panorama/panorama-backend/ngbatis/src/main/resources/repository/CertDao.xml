<mapper namespace="com.geeksec.ngbatis.repository.CertDao">

    <select id="listCertAllEdgeTypeAssociation" resultType="com.geeksec.ngbatis.vo.VertexEdgeVo">
        MATCH (CERT:CERT)
        WHERE id(CERT) == $cert
        RETURN "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "YES" AS middleType,
        "" AS middleId,
        "" AS middleLabel,
        true as sourceStatus,
        true as directionStatus
        LIMIT 1

        UNION ALL
        MATCH (IP:IP)-[server_use_cert:server_use_cert]->(CERT:CERT)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        "Folder" AS middleType,
        "server_use_cert" AS middleId,
        "d_CERT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[cert_belong_to_org:belong_to_org]->(ORG:ORG)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "CERT" AS fromType,
        CERT.CERT.black_list AS fromBlackList,
        CERT.CERT.cert_id AS fromAddr,
        "ORG" AS toType,
        ORG.ORG.black_list AS toBlackList,
        ORG.ORG.org_name AS toAddr,
        "Folder" AS middleType,
        "cert_belong_to_org" AS middleId,
        "d_ORG" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[client_use_cert:client_use_cert]-(IP:IP)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        "Folder" AS middleType,
        "client_use_cert" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[client_connect_cert:client_connect_cert]-(IP:IP)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        "Folder" AS middleType,
        "client_connect_cert" AS middleId,
        "s_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[sni_bind:sni_bind]-(DOMAIN:DOMAIN)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        "Folder" AS middleType,
        "sni_bind" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (CERT:CERT)-[sslfinger_connect_cert:sslfinger_connect_cert]-(SSLFINGER:SSLFINGER)
        WHERE id(CERT) == $cert
        RETURN DISTINCT "SSLFINGER" AS fromType,
        0 AS fromBlackList,
        SSLFINGER.SSLFINGER.finger_id AS fromAddr,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        "Folder" AS middleType,
        "sslfinger_connect_cert" AS middleId,
        "d_SSLFINGER" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5
    </select>

    <select id="listExistCertProperties" resultType="com.geeksec.ngbatis.vo.ExistCertPropertiesVo">
        FETCH PROP ON CERT ${ ng.join(certs,",","ng.valueFmt") }
        YIELD properties(vertex).cert_id AS cert_id
        | YIELD $-.cert_id AS certId where $-.cert_id is not null
    </select>

    <select id="listByCerts" resultType="com.geeksec.ngbatis.vo.CertVo">
        MATCH (A:CERT)
        WHERE id(A) IN ${certs}
        RETURN A.CERT.cert_id AS certId,
        A.CERT.first_time AS firstTime,
        A.CERT.last_time AS lastTime
    </select>

    <select id="listRelatedIpsByCerts" resultType="com.geeksec.ngbatis.vo.CertRelatedIpsVo">
        GO FROM ${ ng.join(certs,",","ng.valueFmt") }
        OVER client_connect_cert REVERSELY
        YIELD properties($^).cert_id AS cert_id, properties($$).ip_addr AS ip_addr
        | GROUP BY $-.cert_id
        YIELD $-.cert_id AS certId, collect($-.ip_addr) AS ipList
    </select>

    <select id="countDomainNumByCerts" resultType="com.geeksec.ngbatis.vo.CertCountVo">
        MATCH (A:DOMAIN)-[e:sni_bind]->(B:CERT)
        WHERE id(B) IN ${certs}
        RETURN B.CERT.cert_id AS certId, count(A) AS count
    </select>

    <select id="countServerIpNumByCerts" resultType="com.geeksec.ngbatis.vo.CertCountVo">
        MATCH (A:IP)-[e:server_use_cert]->(B:CERT)
        WHERE id(B) IN ${certs}
        RETURN B.CERT.cert_id AS certId, count(A) AS count
    </select>

    <select id="countSslIpNumByCerts" resultType="com.geeksec.ngbatis.vo.CertCountVo">
        MATCH (A:SSLFINGER)-[e:sslfinger_connect_cert]->(B:CERT)
        WHERE id(B) IN ${certs}
        RETURN B.CERT.cert_id AS certId, count(A) AS count
    </select>

    <select id="getCertInfo" resultType="java.util.Map">
        MATCH (cert:CERT)
        WHERE id(cert) == $cert
        RETURN cert.CERT.cert_id AS cert,
        cert.CERT.remark AS remark,
        cert.CERT.first_time AS firstTime,
        cert.CERT.last_time AS lastTime,
        cert.CERT.black_list AS blackList,
        cert.CERT.white_list AS whiteList
    </select>

    <select id="listHasLabelByCert" resultType="java.lang.String">
        MATCH (A:CERT)-[e:has_label]->(B:LABEL)
        WHERE id(A) == $cert
        RETURN DISTINCT B.LABEL.label_id AS labelId
    </select>

    <select id="listRelatedServerIpsByCert" resultType="java.lang.String">
        MATCH (A:IP)-[e:server_use_cert]->(B:CERT)
        WHERE id(B) == $cert
        RETURN A.IP.ip_addr AS ipKey
    </select>

    <select id="listRelatedClientIpsByCert" resultType="java.lang.String">
        MATCH (A:IP)-[e:client_connect_cert]->(B:CERT)
        WHERE id(B) == $cert
        RETURN A.IP.ip_addr AS ipKey
    </select>

    <select id="getTaskIds" resultType="java.lang.String">
        MATCH (A:CERT)-[e:task_belong_to]->(B:TASK)
        WHERE id(A) == $cert
        RETURN B.TASK.task_name AS task_name
    </select>

</mapper>