<mapper namespace="com.geeksec.ngbatis.repository.DomainDao">

    <select id="listDomainAllEdgeTypeAssociation" resultType="com.geeksec.ngbatis.vo.VertexEdgeVo">
        MATCH (DOMAIN:DOMAIN)
        WHERE id(DOMAIN) == $domain
        RETURN "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "" AS toType,
        0 AS toBlackList,
        "" AS toAddr,
        "" AS middleType,
        "" AS middleId,
        "" AS middleLabel,
        true as sourceStatus,
        true as directionStatus
        LIMIT 1

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[parse_to:parse_to]->(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        "Folder" AS middleType,
        "parse_to" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[cname:cname]->(DOMAIN1:DOMAIN)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN1.DOMAIN.black_list AS toBlackList,
        DOMAIN1.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "cname" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[cname_result:cname_result]->(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "IP" AS toType,
        IP.IP.black_list AS toBlackList,
        IP.IP.ip_addr AS toAddr,
        "Folder" AS middleType,
        "cname_result" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[domain_belong_to:domain_belong_to]->(FDOMAIN:FDOMAIN)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "FDOMAIN" AS toType,
        0 AS toBlackList,
        FDOMAIN.FDOMAIN.fdomain_addr AS toAddr,
        "Folder" AS middleType,
        "domain_belong_to" AS middleId,
        "d_FDOMAIN" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[sni_bind:sni_bind]->(CERT:CERT)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "CERT" AS toType,
        CERT.CERT.black_list AS toBlackList,
        CERT.CERT.cert_id AS toAddr,
        "Folder" AS middleType,
        "sni_bind" AS middleId,
        "d_CERT" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[domain_belong_to_org:belong_to_org]->(ORG:ORG)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN.DOMAIN.black_list AS fromBlackList,
        DOMAIN.DOMAIN.domain_addr AS fromAddr,
        "ORG" AS toType,
        ORG.ORG.black_list AS toBlackList,
        ORG.ORG.org_name AS toAddr,
        "Folder" AS middleType,
        "domain_belong_to_org" AS middleId,
        "d_ORG" AS middleLabel,
        false as sourceStatus,
        true as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[client_query_domain:client_query_domain]-(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "client_query_domain" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[dns_server_domain:dns_server_domain]-(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "dns_server_domain" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN1:DOMAIN)-[cname:cname]-(DOMAIN:DOMAIN)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "DOMAIN" AS fromType,
        DOMAIN1.DOMAIN.black_list AS fromBlackList,
        DOMAIN1.DOMAIN.domain_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "cname" AS middleId,
        "d_DOMAIN" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[client_ssl_connect_domain:client_ssl_connect_domain]-(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "client_ssl_connect_domain" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[server_ssl_connect_domain:server_ssl_connect_domain]-(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "server_ssl_connect_domain" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[sslfinger_connect_domain:sslfinger_connect_domain]-(SSLFINGER:SSLFINGER)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "SSLFINGER" AS fromType,
        0 AS fromBlackList,
        SSLFINGER.SSLFINGER.finger_id AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "sslfinger_connect_domain" AS middleId,
        "d_SSLFINGER" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[ua_connect_domain:ua_connect_domain]-(UA:UA)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "UA" AS fromType,
        0 AS fromBlackList,
        UA.UA.ua_id AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "ua_connect_domain" AS middleId,
        "d_UA" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[client_http_connect_domain:client_http_connect_domain]-(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "client_http_connect_domain" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5

        UNION ALL
        MATCH (DOMAIN:DOMAIN)-[server_http_connect_domain:server_http_connect_domain]-(IP:IP)
        WHERE id(DOMAIN) == $domain
        RETURN DISTINCT "IP" AS fromType,
        IP.IP.black_list AS fromBlackList,
        IP.IP.ip_addr AS fromAddr,
        "DOMAIN" AS toType,
        DOMAIN.DOMAIN.black_list AS toBlackList,
        DOMAIN.DOMAIN.domain_addr AS toAddr,
        "Folder" AS middleType,
        "server_http_connect_domain" AS middleId,
        "d_IP" AS middleLabel,
        false as sourceStatus,
        false as directionStatus
        LIMIT 5
    </select>

    <select id="listExistDomainProperties" resultType="com.geeksec.ngbatis.vo.ExistDomainPropertiesVo">
        FETCH PROP ON DOMAIN ${ ng.join(domains,",","ng.valueFmt") }
        YIELD properties(vertex).domain_addr AS domain_addr
        | YIELD $-.domain_addr AS domainAddr where $-.domain_addr is not null
    </select>

    <select id="listByDomains" resultType="com.geeksec.ngbatis.vo.DomainVo">
        MATCH (A:DOMAIN)
        WHERE id(A) IN ${domains}
        RETURN A.DOMAIN.domain_addr AS domainAddr,
        A.DOMAIN.first_time AS firstTime,
        A.DOMAIN.last_time AS lastTime,
        A.DOMAIN.black_list AS blackList
    </select>

    <select id="listHasLabelByDomains" resultType="com.geeksec.ngbatis.vo.DomainHasLabelVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER has_label
        YIELD properties($^).domain_addr AS domain_addr,
        dst(edge) AS dst,
        properties($$).label_id AS label_id
        | GROUP BY $-.domain_addr
        YIELD $-.domain_addr AS domainAddr,
        collect($-.label_id) AS labels
    </select>

    <select id="listFDomainByDomains" resultType="com.geeksec.ngbatis.vo.FDomainVo">
        MATCH (A:DOMAIN)-[e:domain_belong_to]->(B:FDOMAIN)
        WHERE id(A) IN ${domains}
        RETURN A.DOMAIN.domain_addr AS domainAddr, B.FDOMAIN.fdomain_addr AS fdomainAddr;
    </select>

    <select id="getHasLabelByDomain" resultType="com.geeksec.ngbatis.vo.DomainHasLabelVo">
        GO FROM ${ ng.valueFmt(domain) }
        OVER has_label
        YIELD properties($^).domain_addr AS domain_addr,
        dst(edge) AS dst,
        properties($$).label_id AS label_id
        | GROUP BY $-.domain_addr
        YIELD $-.domain_addr AS domainAddr,
        collect($-.label_id) AS labels
    </select>

    <select id="countBrotherNumByDomain" resultType="com.geeksec.ngbatis.vo.DomainCountVo">
        GO FROM ${ ng.valueFmt(domain) }
        OVER domain_belong_to REVERSELY
        YIELD properties($^).fdomain_addr AS fdomain_addr, properties($$).domain_addr AS domain_addr
        | GROUP BY $-.fdomain_addr
        YIELD $-.fdomain_addr AS domainAddr, count(*) AS count
    </select>

    <select id="listRelatedIpsByDomains" resultType="com.geeksec.ngbatis.vo.DomainRelatedIpsVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER parse_to, cname_result
        YIELD properties($^).domain_addr AS domain_addr, properties($$).ip_addr AS ip_addr
        | GROUP BY $-.domain_addr
        YIELD $-.domain_addr AS domainAddr, collect($-.ip_addr) AS ipList
    </select>

    <select id="countCnameDomainsByDomains" resultType="com.geeksec.ngbatis.vo.DomainCountVo">
        MATCH (A:DOMAIN)-[e:cname]->(B:DOMAIN)
        WHERE id(B) IN ${domains}
        RETURN B.DOMAIN.domain_addr AS domainAddr, COUNT(e) AS count
    </select>

    <select id="countCnamePointDomainsByDomains" resultType="com.geeksec.ngbatis.vo.DomainCountVo">
        MATCH (A:DOMAIN)-[e:cname]->(B:DOMAIN)
        WHERE id(A) IN ${domains}
        RETURN A.DOMAIN.domain_addr AS domainAddr, COUNT(e) AS count
    </select>

    <select id="listRequestDomainIpsByDomains" resultType="com.geeksec.ngbatis.vo.DomainRelatedIpsVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER client_ssl_connect_domain, client_http_connect_domain REVERSELY
        YIELD properties($$).ip_addr AS ip, properties($^).domain_addr AS domain
        | GROUP BY $-.domain
        YIELD DISTINCT $-.domain AS domainAddr, collect($-.ip) AS ipList
    </select>

    <select id="listResponseTypeIpsByDomains" resultType="com.geeksec.ngbatis.vo.DomainRelatedIpsVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER parse_to
        WHERE properties(edge).final_parse == true
        YIELD DISTINCT properties($^).domain_addr AS domain_addr, properties($$).ip_addr AS ip_addr, dst(edge) AS dst_id
        | GROUP BY $-.domain_addr
        YIELD $-.domain_addr AS domainAddr, collect($-.dst_id) AS ipList
    </select>

    <select id="countDnsServerDomainByDomains" resultType="com.geeksec.ngbatis.vo.DomainCountVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER dns_server_domain REVERSELY
        YIELD src(edge) AS src, dst(edge) AS dst
        | GROUP BY $-.dst
        YIELD $-.dst AS domainAddr, count(*) AS count
    </select>

    <select id="countServerSslConnectDomainByDomains" resultType="com.geeksec.ngbatis.vo.DomainCountVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER server_ssl_connect_domain REVERSELY
        YIELD properties($^).domain_addr AS domain_addr, src(edge) AS src
        | GROUP BY $-.domain_addr
        YIELD $-.domain_addr AS domainAddr, count(*) AS count
    </select>

    <select id="countServerHttpConnectDomainByDomains" resultType="com.geeksec.ngbatis.vo.DomainCountVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER server_http_connect_domain REVERSELY
        YIELD properties($^).domain_addr AS domain_addr, src(edge) AS src
        | GROUP BY $-.domain_addr
        YIELD $-.domain_addr AS domainAddr, count(*) AS count
    </select>

    <select id="countSniBindByDomains" resultType="com.geeksec.ngbatis.vo.DomainCountVo">
        GO FROM ${ ng.join(domains,",","ng.valueFmt") }
        OVER sni_bind REVERSELY
        YIELD properties($^).domain_addr AS domain_addr, src(edge) AS src
        | GROUP BY $-.domain_addr
        YIELD $-.domain_addr AS domainAddr, count(*) AS count
    </select>

    <select id="getTaskIds" resultType="java.lang.String">
        MATCH (A:DOMAIN)-[e:task_belong_to]->(B:TASK)
        WHERE id(A) == $domain
        RETURN B.TASK.task_name AS task_name
    </select>

    <select id="getDomainInfo" resultType="java.util.Map">
        MATCH (A:DOMAIN)
        WHERE id(A) == $domain
        RETURN A.DOMAIN.alexa_rank AS alexaRank,
        A.DOMAIN.remark AS remark,
        A.DOMAIN.first_time AS firstTime,
        A.DOMAIN.last_time AS lastTime,
        A.DOMAIN.black_list AS blackList,
        A.DOMAIN.white_list AS whiteList,
        A.DOMAIN.whois AS whoIs
    </select>

    <select id="listHasLabelByDomain" resultType="java.lang.String">
        MATCH (A:DOMAIN)-[e:has_label]->(B:LABEL)
        WHERE id(A) == $domain
        RETURN DISTINCT B.LABEL.label_id AS labelId
    </select>

    <select id="getFDomainByDomain" resultType="java.lang.String">
        MATCH (A:DOMAIN)-[e:domain_belong_to]->(B:FDOMAIN)
        WHERE id(A) == $domain
        RETURN DISTINCT B.FDOMAIN.fdomain_addr AS domainAddr
        LIMIT 1
    </select>

    <select id="listHasLabelByFDomain" resultType="java.lang.String">
        MATCH (A:FDOMAIN)-[e:has_label]->(B:LABEL)
        WHERE id(A) == $domain
        RETURN DISTINCT B.LABEL.label_id AS labelId
    </select>

    <select id="countCnameResultByDomain" resultType="java.lang.Integer">
        MATCH (A:DOMAIN)-[e:cname_result]->(B:IP)
        WHERE id(A) == $domain
        RETURN DISTINCT count(e) AS NUM
    </select>

    <select id="countParseToByDomain" resultType="java.lang.Integer">
        MATCH (A:DOMAIN)-[e:parse_to]->(B:IP)
        WHERE id(A) == $domain
        RETURN DISTINCT count(e) AS NUM
    </select>

    <select id="listRelatedClientIpsByDomain" resultType="com.geeksec.ngbatis.vo.DomainRelatedIpsVo">
        GO FROM ${ ng.valueFmt(domain) }
        OVER client_ssl_connect_domain, client_http_connect_domain, client_query_domain REVERSELY
        YIELD properties($$).ip_addr AS ip, properties($^).domain_addr AS domain
        | GROUP BY $-.domain
        YIELD DISTINCT $-.domain AS domainAddr, collect($-.ip) AS ipList
    </select>

    <select id="countCertNumByDomain" resultType="java.lang.Integer">
        MATCH (A:DOMAIN)-[e:sni_bind]->(B:CERT)
        WHERE id(A) == $domain
        RETURN DISTINCT count(e) AS NUM
    </select>

</mapper>