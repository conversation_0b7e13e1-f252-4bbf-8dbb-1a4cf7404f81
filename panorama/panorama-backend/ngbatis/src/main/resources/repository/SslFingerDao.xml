<mapper namespace="com.geeksec.ngbatis.repository.SslFingerDao">

    <select id="listFingerDescribeByFingers" resultType="com.geeksec.ngbatis.vo.SslFingerDescVo">
        MATCH (S:SSLFINGER)
        WHERE id(S) IN ${fingers}
        RETURN S.SSLFINGER.finger_id AS fingerId,
        S.SSLFINGER.type AS fingerType,
        S.SSLFINGER.finger_desc AS fingerDesc
    </select>

    <select id="listFingerLabelByFingers" resultType="com.geeksec.ngbatis.vo.SslFingerLabelVo">
        MATCH (F:SSLFINGER)-[h:has_label]->(L:LABEL)
        WHERE id(F) IN ${fingers}
        RETURN F.SSLFINGER.finger_id AS fingerId,
        F.SSLFINGER.type AS fingerType,
        L.LABEL.label_id AS labelId,
        L.LABEL.label_name AS labelName
    </select>

    <select id="listServerRelatedIpsByFingers" resultType="com.geeksec.ngbatis.vo.SslFingerRelatedIpsVo">
        GO FROM ${ ng.join(fingers,",","ng.valueFmt") }
        OVER server_use_sslfinger REVERSELY
        YIELD properties($^).finger_id AS finger, properties($$).ip_addr AS ip
        | GROUP BY $-.finger
        YIELD $-.finger AS fingerId, collect($-.ip) AS ipList
    </select>

    <select id="listClientRelatedIpsByFingers" resultType="com.geeksec.ngbatis.vo.SslFingerRelatedIpsVo">
        GO FROM ${ ng.join(fingers,",","ng.valueFmt") }
        OVER client_use_sslfinger REVERSELY
        YIELD properties($^).finger_id AS finger, properties($$).ip_addr AS ip
        | GROUP BY $-.finger
        YIELD $-.finger AS fingerId, collect($-.ip) AS ipList
    </select>

    <select id="countCertByFingers" resultType="com.geeksec.ngbatis.vo.SslFingerCountVo">
        MATCH (L:SSLFINGER)-[E:sslfinger_connect_cert]->(C:CERT)
        WHERE id(L) IN ${fingers}
        RETURN L.SSLFINGER.finger_id AS fingerId, count(E) AS count
    </select>

</mapper>