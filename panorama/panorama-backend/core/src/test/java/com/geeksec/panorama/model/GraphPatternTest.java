package com.geeksec.panorama.model;

import static org.junit.jupiter.api.Assertions.*;

import com.geeksec.panorama.enums.EdgeType;
import com.geeksec.panorama.enums.Operator;
import com.geeksec.panorama.enums.VertexType;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;

class GraphPatternTest {

  private GraphPattern graphPattern;

  @BeforeEach
  void setUp() {
    graphPattern = new GraphPattern();
  }

  @Nested
  @DisplayName("Basic tests")
  class BasicTests {
    @Test
    @DisplayName("Test toNGQL with filters")
    void testToNGQLWithFilters() {
      Vertex ipVertex = new Vertex();
      ipVertex.setId("v1");
      ipVertex.setType(VertexType.IP);
      ipVertex.setFilters(
          Collections.singletonList(new Filter("ip_addr", Operator.EQUALS, "***********")));

      Vertex domainVertex = new Vertex();
      domainVertex.setId("v2");
      domainVertex.setType(VertexType.DOMAIN);
      domainVertex.setFilters(
          Collections.singletonList(new Filter("domain_addr", Operator.EQUALS, "example.com")));

      Edge resolves = new Edge();
      resolves.setId("e");
      resolves.setType(EdgeType.PARSE_TO);
      resolves.setSrcId("v2");
      resolves.setDstId("v1");
      resolves.setFilters(
          Collections.singletonList(new Filter("is_active", Operator.EQUALS, true)));

      graphPattern.setVertices(Arrays.asList(ipVertex, domainVertex));
      graphPattern.setEdges(Collections.singletonList(resolves));

      String result = graphPattern.toNGQL();
      // 使用正则表达式来匹配预期的模式，保持MATCH子句顺序，但允许WHERE和RETURN子句顺序灵活
      String expectedPattern =
          "MATCH \\(v2:DOMAIN\\)-\\[e:parse_to]->\\(v1:IP\\) "
              + "WHERE (v2\\.DOMAIN\\.domain_addr == 'example\\.com'|v1\\.IP\\.ip_addr == '192\\.168\\.1\\.1'|e\\.is_active == true)"
              + "( AND (v2\\.DOMAIN\\.domain_addr == 'example\\.com'|v1\\.IP\\.ip_addr == '192\\.168\\.1\\.1'|e\\.is_active == true)){2}"
              + " RETURN (v1|v2), id\\((v1|v2)\\), (v1|v2), id\\((v1|v2)\\), e LIMIT 10";

      assertTrue(
          result.matches(expectedPattern), "Generated nGQL does not match the expected pattern");
    }

    @Test
    @DisplayName("Test toNGQL without filters")
    void testToNGQLWithoutFilters() {
      Vertex ipVertex = new Vertex();
      ipVertex.setId("v1");
      ipVertex.setType(VertexType.IP);

      Vertex domainVertex = new Vertex();
      domainVertex.setId("v2");
      domainVertex.setType(VertexType.DOMAIN);

      Edge resolves = new Edge();
      resolves.setId("e");
      resolves.setType(EdgeType.PARSE_TO);
      resolves.setSrcId("v2");
      resolves.setDstId("v1");

      graphPattern.setVertices(Arrays.asList(ipVertex, domainVertex));
      graphPattern.setEdges(Collections.singletonList(resolves));

      String result = graphPattern.toNGQL();

      String expectedPattern =
          "MATCH \\(v2:DOMAIN\\)-\\[e:parse_to]->\\(v1:IP\\) "
              + "RETURN (v1|v2), id\\((v1|v2)\\), (v1|v2), id\\((v1|v2)\\), e LIMIT 10";

      assertTrue(
          result.matches(expectedPattern), "Generated nGQL does not match the expected pattern");
    }
  }

  @Nested
  @DisplayName("Advanced tests")
  class AdvancedTests {
    @Test
    @DisplayName("Test toNGQL with multiple edges")
    void testToNGQLWithMultipleEdges() {
      Vertex ipVertex = new Vertex();
      ipVertex.setId("v1");
      ipVertex.setType(VertexType.IP);

      Vertex domainVertex = new Vertex();
      domainVertex.setId("v2");
      domainVertex.setType(VertexType.DOMAIN);

      Vertex uaVertex = new Vertex();
      uaVertex.setId("v3");
      uaVertex.setType(VertexType.UA);

      Edge resolves = new Edge();
      resolves.setId("e1");
//      resolves.setType(EdgeType.DOMAIN_RESOLVES_TO_IP);
      resolves.setSrcId("v2");
      resolves.setDstId("v1");

      Edge accesses = new Edge();
      accesses.setId("e2");
//      accesses.setType(EdgeType.USER_AGENT_ACCESSES_DOMAIN);
      accesses.setSrcId("v3");
      accesses.setDstId("v2");

      graphPattern.setVertices(Arrays.asList(ipVertex, domainVertex, uaVertex));
      graphPattern.setEdges(Arrays.asList(resolves, accesses));

      String result = graphPattern.toNGQL();

      String expectedPattern =
          "MATCH \\(v2:DOMAIN\\)-\\[e1:parse_to]->\\(v1:IP\\) "
              + "MATCH \\(v3:UA\\)-\\[e2:ua_connect_domain]->\\(v2:DOMAIN\\) "
              + "RETURN (v1|v2|v3), id\\((v1|v2|v3)\\), (v1|v2|v3), id\\((v1|v2|v3)\\), (v1|v2|v3), id\\((v1|v2|v3)\\), (e1|e2), (e1|e2) LIMIT 10";

      assertTrue(
          result.matches(expectedPattern), "Generated nGQL does not match the expected pattern");
    }

    @Test
    @DisplayName("Test toNGQL with different operators and data types")
    void testToNGQLWithDifferentOperatorsAndDataTypes() {
      Vertex ipVertex = new Vertex();
      ipVertex.setId("v1");
      ipVertex.setType(VertexType.IP);
      ipVertex.setFilters(
          Arrays.asList(
              new Filter("ip_addr", Operator.EQUALS, "***********"),
              new Filter("ttl", Operator.GREATER_THAN, 60)));

      Vertex domainVertex = new Vertex();
      domainVertex.setId("v2");
      domainVertex.setType(VertexType.DOMAIN);
      domainVertex.setFilters(
          Arrays.asList(
              new Filter("domain_addr", Operator.CONTAINS, "example"),
              new Filter("is_active", Operator.EQUALS, true)));

      Edge resolves = new Edge();
      resolves.setId("e");
      resolves.setType(EdgeType.PARSE_TO);
      resolves.setSrcId("v2");
      resolves.setDstId("v1");
      resolves.setFilters(
          Arrays.asList(
              new Filter("weight", Operator.GREATER_THAN_OR_EQUAL, 0.5),
              new Filter("count", Operator.LESS_THAN, 1000)));

      graphPattern.setVertices(Arrays.asList(ipVertex, domainVertex));
      graphPattern.setEdges(Collections.singletonList(resolves));

      String result = graphPattern.toNGQL();
      String expectedPattern =
          "MATCH \\(v2:DOMAIN\\)-\\[e:parse_to]->\\(v1:IP\\) "
              + "WHERE (v2\\.DOMAIN\\.domain_addr CONTAINS 'example'|v2\\.DOMAIN\\.is_active == true|"
              + "v1\\.IP\\.ip_addr == '192\\.168\\.1\\.1'|v1\\.IP\\.ttl > 60|e\\.weight >= 0\\.5|e\\.count < 1000)"
              + "( AND (v2\\.DOMAIN\\.domain_addr CONTAINS 'example'|v2\\.DOMAIN\\.is_active == true|"
              + "v1\\.IP\\.ip_addr == '192\\.168\\.1\\.1'|v1\\.IP\\.ttl > 60|e\\.weight >= 0\\.5|e\\.count < 1000)){5}"
              + " RETURN (v1|v2), id\\((v1|v2)\\), (v1|v2), id\\((v1|v2)\\), e LIMIT 10";

      assertTrue(
          result.matches(expectedPattern), "Generated nGQL does not match the expected pattern");
    }
  }
}
