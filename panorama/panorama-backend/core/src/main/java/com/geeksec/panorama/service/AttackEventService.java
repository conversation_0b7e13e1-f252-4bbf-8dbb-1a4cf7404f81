package com.geeksec.panorama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geeksec.panorama.entity.AttackEvent;
import java.util.List;
import java.util.Optional;

/**
 * @author: jerryzhou
 * @date: 2024/8/22 15:41 @Description:
 */
public interface AttackEventService extends IService<AttackEvent> {

  /** 创建攻击事件 */
  void createEvent(AttackEvent attackEvent);

  /** 获取攻击事件 */
  Optional<AttackEvent> getFirstEvent(String runId);

  Optional<AttackEvent> getNextEvent(String runId, String currentId);

  AttackEvent getEventById(String id);

  long countEventsByRun(String runId);

  List<AttackEvent> getEventsByRunIdUpToEventId(String runId, String eventId);
}
