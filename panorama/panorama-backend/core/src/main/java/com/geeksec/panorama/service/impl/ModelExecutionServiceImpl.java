package com.geeksec.panorama.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.geeksec.ngbatis.vo.VertexAssociationEdgeVo;
import com.geeksec.ngbatis.vo.VertexAssociationVertexVo;
import com.geeksec.ngbatis.vo.VertexAssociationVo;
import com.geeksec.panorama.consts.ModelType;
import com.geeksec.panorama.entity.AttackEvent;
import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.entity.ModelRun;
import com.geeksec.panorama.entity.ModelRunAttackTimeRange;
import com.geeksec.panorama.enums.EdgeType;
import com.geeksec.panorama.enums.EventStatus;
import com.geeksec.panorama.enums.VertexType;
import com.geeksec.panorama.exception.ModelExecutionException;
import com.geeksec.panorama.exception.ModelRunCreationException;
import com.geeksec.panorama.exception.NgqlExecutionException;
import com.geeksec.panorama.model.GraphPattern;
import com.geeksec.panorama.nebula.convert.NebulaResultConverter;
import com.geeksec.panorama.service.AttackEventService;
import com.geeksec.panorama.service.ModelExecutionService;
import com.geeksec.panorama.service.ModelRunService;
import com.vesoft.nebula.client.graph.SessionPool;
import com.vesoft.nebula.client.graph.data.Node;
import com.vesoft.nebula.client.graph.data.Relationship;
import com.vesoft.nebula.client.graph.data.ResultSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;

@Slf4j
@Service
public class ModelExecutionServiceImpl implements ModelExecutionService {

    private final SessionPool sessionPool;
    private final AttackEventService attackEventService;
    private final ModelRunService modelRunService;
    private static final ZoneId SHANGHAI_ZONE = ZoneId.of("Asia/Shanghai");

    @Autowired
    public ModelExecutionServiceImpl(
            SessionPool sessionPool,
            AttackEventService attackEventService,
            ModelRunService modelRunService) {
        this.sessionPool = sessionPool;
        this.attackEventService = attackEventService;
        this.modelRunService = modelRunService;
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Boolean> executeCustomModel(CustomAttackModel model, String taskId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    try {
                        String ngql = model.getNgql();
                        log.info("Executing NGQL to create graph event. NGQL: {}", ngql);
                        ResultSet resultSet = sessionPool.execute(ngql);

                        if (!resultSet.isSucceeded()) {
                            throw new NgqlExecutionException(
                                    "执行nGQL语句失败，模型名称：" + model.getName() + ", nGQL语句：" + ngql);
                        }

                        ModelRun modelRun = createModelRun(model, taskId);
                        String modelRunId = modelRun.getId();
                        // 将一次性查询到的进行事件维度的处理
                        processResultSet(resultSet, modelRunId);
                        log.info("Successfully executed custom attack model. Model ID: {},Model Run ID :{}", model.getId(), modelRunId);
                        return true;
                    } catch (Exception e) {
                        log.error("Failed to execute custom attack model. Model ID: {}", model.getId(), e);
                        throw new ModelExecutionException("模型\"" + model.getName() + "\"运行失败。");
                    }
                });
    }

    @Override
    public ModelRun createModelRun(CustomAttackModel model,String taskId) {
        ModelRun modelRun = new ModelRun();
        modelRun.setModelId(model.getId());
        modelRun.setModelVersion(model.getVersion());
        modelRun.setModelType(ModelType.CUSTOM_MODE);
        modelRun.setCreateTime(LocalDateTime.now());
        if (!taskId.isEmpty()){
            modelRun.setTaskId(taskId);
        }
        boolean saved = modelRunService.save(modelRun);
        if (!saved) {
            throw new ModelRunCreationException("记录模型\"" + model.getName() + "\"执行失败。");
        }
        return modelRun;
    }

    @Override
    public ResultSet executeQuery(GraphPattern pattern) throws NgqlExecutionException {
        String ngql = pattern.toNGQL();
        log.info("Executing NGQL query: {}", ngql);
        try {
            return sessionPool.execute(ngql);
        } catch (Exception e) {
            log.error("Failed to execute NGQL query", e);
            throw new NgqlExecutionException("查询失败");
        }
    }

    /**
     * 返回待更新的模型执行结果
     *
     * @param resultSet
     * @param runId
     * @throws UnsupportedEncodingException
     */
    private void processResultSet(ResultSet resultSet, String runId)
            throws UnsupportedEncodingException {
        if (!resultSet.isEmpty()) {
            List<NebulaResultConverter.NebulaResult> convertResultList =
                    NebulaResultConverter.convertResultSet(resultSet);

            // 记录事件中的受害者IP信息
            Set<String> vipSet = new LinkedHashSet<>();

            // 记录事件中所有的告警数量
            Set<String> alertSet = new LinkedHashSet<>();

            // 记录事件中所有的传感器信息
            Map<String, Long> vendorMap = new HashMap<>();

            // 当前模型执行的时间最早发生时间与最晚时间
            ModelRunAttackTimeRange timeRange = new ModelRunAttackTimeRange();

            for (NebulaResultConverter.NebulaResult result : convertResultList) {
                // 每一条记录都是单独的结果集
                Set<VertexAssociationVertexVo> vertexList = new LinkedHashSet<>();
                Set<VertexAssociationEdgeVo> edgeList = new LinkedHashSet<>();

                // 处理边和关联关系
                processNodes(result, vertexList);
                // 处理边时，获取受害者数量、关联告警数、关联传感器、事件最早时间&最晚发生时间
                processRelationships(result, edgeList, vipSet, alertSet, vendorMap, timeRange);

                // 处理完单条记录边开始入事件表
                VertexAssociationVo vertexAssociationVo = new VertexAssociationVo();
                vertexAssociationVo.setVertex(vertexList);
                vertexAssociationVo.setEdge(edgeList);
                AttackEvent attackEvent = createAttackEvent(vertexAssociationVo, runId);
                attackEventService.createEvent(attackEvent);
            }

            // 通过RunId 更新对应的记录数据
            ModelRun modelRun = modelRunService.getById(runId);
            modelRun.setAlarmCount(alertSet.size());
            modelRun.setUniqueVictimCount(vipSet.size());
            List<Map<String, Object>> vendorList = new ArrayList<>();
            for (Map.Entry<String, Long> entry : vendorMap.entrySet()) {
                Map<String, Object> item = new HashMap<>();
                item.put("key", entry.getKey());
                item.put("cnt", entry.getValue());
                vendorList.add(item);
            }
            modelRun.setStatus(EventStatus.PENDING);
            modelRun.setSourceDistribution(vendorList);
            modelRun.setEarliestEventTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(timeRange.getStartTime()), ZoneId.systemDefault()));
            modelRun.setLatestEventTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(timeRange.getEndTime()), ZoneId.systemDefault()));
            modelRunService.updateById(modelRun);
        }else{
            // 若查询出来的事件记录为空，则更新空的事件执行结果
            ModelRun modelRun = modelRunService.getById(runId);
            modelRun.setAlarmCount(0);
            modelRun.setUniqueVictimCount(0);
            modelRun.setStatus(EventStatus.PENDING);
            modelRun.setSourceDistribution(new ArrayList<>());
            modelRun.setEarliestEventTime(null);
            modelRun.setLatestEventTime(null);
            modelRunService.updateById(modelRun);
        }
    }

    private AttackEvent createAttackEvent(VertexAssociationVo associationVo, String runId) {
        AttackEvent attackEvent = new AttackEvent();
        attackEvent.setRunId(runId);
        attackEvent.setEventGraph(associationVo);
        // 处理本次事件的开始时间和结束事件（目前只存在于包含attack_time的情况）
        long minAttackTime = Long.MAX_VALUE;
        long maxAttackTime = Long.MIN_VALUE;

        for (VertexAssociationEdgeVo edge : associationVo.getEdge()) {
            Object attackTimeObj = edge.getEdgeInfo().get("attack_time");
            // 只抽取攻击事件
            if (attackTimeObj instanceof Long) {
                long attackTime = (Long) attackTimeObj;
                minAttackTime = Math.min(minAttackTime, attackTime);
                maxAttackTime = Math.max(maxAttackTime, attackTime);
            }
        }
        LocalDateTime startTime =
                (minAttackTime != Long.MAX_VALUE)
                        ? LocalDateTime.ofInstant(Instant.ofEpochSecond(minAttackTime), SHANGHAI_ZONE)
                        : null;
        LocalDateTime endTime =
                (maxAttackTime != Long.MIN_VALUE)
                        ? LocalDateTime.ofInstant(Instant.ofEpochSecond(maxAttackTime), SHANGHAI_ZONE)
                        : null;
        attackEvent.setStartTime(startTime);
        attackEvent.setEndTime(endTime);
        return attackEvent;
    }

    private void processNodes(
            NebulaResultConverter.NebulaResult result, Set<VertexAssociationVertexVo> vertexList)
            throws UnsupportedEncodingException {
        for (Node node : result.getNodes()) {
            VertexAssociationVertexVo vertexVo = new VertexAssociationVertexVo();
            vertexVo.setId(node.getId().asString());
            String vertexType = node.tagNames().get(0);
            vertexVo.setType(VertexType.fromName(vertexType));
            Map<String, Object> vertexMap =
                    NebulaResultConverter.convertNodePropertiesToMap(node, vertexType);
            Function<Map<String, Object>, String> extractor =
                    NebulaResultConverter.EXTRACTORS.getOrDefault(vertexType, map -> StrUtil.EMPTY);
            String label = extractor.apply(vertexMap);

            vertexVo.setLabel(label);
            vertexVo.setVInfo(vertexMap);
            vertexList.add(vertexVo);
        }
    }

    /**
     * 处理单个事件里的关系边信息
     *
     * @param result
     * @param edgeList
     * @param vipSet    关联受害者
     * @param alertSet  关联告警
     * @param timeRange
     * @throws UnsupportedEncodingException
     */
    private void processRelationships(
            NebulaResultConverter.NebulaResult result,
            Set<VertexAssociationEdgeVo> edgeList,
            Set<String> vipSet, Set<String> alertSet, Map<String, Long> vendorMap, ModelRunAttackTimeRange timeRange)
            throws UnsupportedEncodingException {

        for (Relationship relationship : result.getRelationships()) {
            VertexAssociationEdgeVo edgeVo = createEdgeVo(relationship);

            if ("make_attack_to".equals(edgeVo.getType().getName())) {
                // 从攻击边中获取受害者IP信息
                processMakeAttackToEdge(edgeVo, vipSet, alertSet, vendorMap, timeRange);
            }
            edgeList.add(edgeVo);
        }
    }

    private VertexAssociationEdgeVo createEdgeVo(Relationship relationship) throws UnsupportedEncodingException {
        VertexAssociationEdgeVo edgeVo = new VertexAssociationEdgeVo();
        edgeVo.setFrom(relationship.srcId().asString());
        edgeVo.setTo(relationship.dstId().asString());

        EdgeType edgeType = EdgeType.fromName(relationship.edgeName());
        edgeVo.setLabel(edgeType.getDesc());
        edgeVo.setType(edgeType);
        edgeVo.setId(String.valueOf(relationship.ranking()));
        edgeVo.setEdgeInfo(NebulaResultConverter.convertRelationshipPropertiesToMap(relationship));

        return edgeVo;
    }

    /**
     * 获取关联告警相关信息
     *
     * @param edgeVo
     * @param vipSet
     * @param alertSet
     * @param vendorMap
     * @param timeRange 当前已经存储的模型执行最早时间与最晚时间
     * @return
     */
    private void processMakeAttackToEdge(VertexAssociationEdgeVo edgeVo, Set<String> vipSet, Set<String> alertSet, Map<String, Long> vendorMap, ModelRunAttackTimeRange timeRange) {
        String vip = edgeVo.getTo();
        Map<String, Object> edgeInfo = edgeVo.getEdgeInfo();
        String attackId = (String) edgeInfo.get("attack_id");
        Long attackTime = (Long) edgeInfo.get("attack_time");

        // 获取传感器信息 & 告警数量 & 受害者数量
        if (StrUtil.isNotEmpty(vip)) {
            vipSet.add(vip);
            alertSet.add(attackId);
            String vendorId = (String) edgeInfo.get("vendor_id");
            vendorMap.merge(vendorId, 1L, Long::sum);
        }

        // 获取timeRange的内容并进行比较
        if (attackTime != null) {
            Long startTime = timeRange.getStartTime();
            Long endTime = timeRange.getEndTime();

            if (startTime == 0L && endTime == 0L) {
                timeRange.setStartTime(attackTime);
                timeRange.setEndTime(attackTime);
            }
            if (attackTime < startTime) {
                timeRange.setStartTime(attackTime);
            }
            if (attackTime > endTime) {
                timeRange.setEndTime(attackTime);
            }
        }
    }
}
