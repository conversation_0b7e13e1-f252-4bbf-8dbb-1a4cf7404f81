package com.geeksec.panorama.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.geeksec.ngbatis.vo.VertexAssociationVo;
import com.geeksec.panorama.entity.handler.JsonbTypeHandler;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/** 攻击事件实体类 */
@Data
@TableName(value = "attack_event", autoResultMap = true)
public class AttackEvent implements Serializable {

  @TableField(exist = false)
  private static final long serialVersionUID = 1L;

  /** 事件ID */
  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  /** 关联的模型运行记录ID */
  private String runId;

  /** 攻击事件图 */
  @TableField(typeHandler = JsonbTypeHandler.class)
  private VertexAssociationVo eventGraph;

  /** 攻击起始时间 */
  private LocalDateTime startTime;

  /** 攻击结束时间 */
  private LocalDateTime endTime;

  /** 事件入库时间 */
  private Date createTime;
}
