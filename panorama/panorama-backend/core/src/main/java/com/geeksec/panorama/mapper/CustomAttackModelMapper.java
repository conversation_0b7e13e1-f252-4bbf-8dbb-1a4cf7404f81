package com.geeksec.panorama.mapper;

import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.model.CustomAttackModelDto;
import com.geeksec.panorama.model.GraphPattern;
import org.mapstruct.*;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomAttackModelMapper {

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "ngql", source = "pattern", qualifiedByName = "patternToNgql")
  @Mapping(target = "running", ignore = true)
  @Mapping(target = "createTime", ignore = true)
  @Mapping(target = "updateTime", ignore = true)
  @Mapping(target = "lastRunTime", ignore = true)
  CustomAttackModel toEntity(CustomAttackModelDto dto);

  CustomAttackModelDto toDto(CustomAttackModel entity);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "ngql", source = "pattern", qualifiedByName = "patternToNgql")
  @Mapping(target = "running", ignore = true)
  @Mapping(target = "createTime", ignore = true)
  @Mapping(target = "updateTime", ignore = true)
  @Mapping(target = "lastRunTime", ignore = true)
  void updateEntity(CustomAttackModelDto dto, @MappingTarget CustomAttackModel entity);

  @Named("patternToNgql")
  default String patternToNgql(GraphPattern pattern) {
    return pattern == null ? null : pattern.toNGQL();
  }
}
