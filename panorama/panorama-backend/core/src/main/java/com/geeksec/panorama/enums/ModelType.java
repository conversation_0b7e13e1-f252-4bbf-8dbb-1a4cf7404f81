package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ModelType {
  EVENT_SEQUENCE_CORRELATION_MODEL(1, "事件序列关联模型"),
  GRAPH_PATTERN_MODEL(2, "图模式匹配模型");

  @EnumValue private final int code;
  private final String desc;

  ModelType(int code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  @JsonValue
  public int getCode() {
    return code;
  }

  @JsonCreator
  public static ModelType fromCode(int code) {
    for (ModelType type : values()) {
      if (type.getCode() == code) {
        return type;
      }
    }
    throw new IllegalArgumentException("Invalid ModelType.java code: " + code);
  }
}
