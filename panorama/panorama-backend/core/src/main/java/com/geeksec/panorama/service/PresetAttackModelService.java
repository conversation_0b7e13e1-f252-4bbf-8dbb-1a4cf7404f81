package com.geeksec.panorama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geeksec.panorama.model.PageResult;
import com.geeksec.panorama.model.PresetModelUpdateDto;
import com.geeksec.panorama.entity.PresetAttackModel;
import com.geeksec.panorama.exception.GkException;

/**
 * <AUTHOR> @Description：预置事件模型服务
 */
public interface PresetAttackModelService extends IService<PresetAttackModel> {
  PageResult<PresetAttackModel> getPresetModelPage(int page, int pageSize);

  void updatePresetModel(String id, PresetModelUpdateDto updateDto) throws GkException;

  void toggleModelStatus(String id, boolean active) throws GkException;
}
