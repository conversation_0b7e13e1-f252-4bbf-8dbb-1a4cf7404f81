package com.geeksec.panorama.mapper;

import com.geeksec.panorama.entity.ModelRun;
import com.geeksec.panorama.model.ModelRunVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
    componentModel = "spring",
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ModelRunMapper {
  ModelRunVo toDto(ModelRun entity);
}
