package com.geeksec.panorama.filer;

import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.enums.GkError;
import com.geeksec.panorama.enums.JobType;
import com.geeksec.panorama.exception.GkException;
import com.geeksec.panorama.mapper.ScheduleTaskUpdateMapper;
import com.geeksec.panorama.model.ScheduleTaskUpdate;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.quartz.CronExpression;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

@Component
@Aspect
@Slf4j
public class ParamValidAspect {

    @Before("(@annotation(org.springframework.web.bind.annotation.PostMapping) || " +
            "@annotation(org.springframework.web.bind.annotation.PutMapping))")
    public void validateRequestBody(JoinPoint joinPoint) {
        // 拦截 /task 下的所有请求
        // 获取当前请求
        HttpServletRequest request = ((ServletRequestAttributes)
                RequestContextHolder.getRequestAttributes()).getRequest();
        Object[] args = joinPoint.getArgs();
        if (args[0] instanceof ScheduleTask) {
            ScheduleTask entity = (ScheduleTask) args[0];  // 获取第一个参数（请求体）
            // 只校验特定URL
            validScheduleParam(request, entity);
        } else if (args[0] instanceof ScheduleTaskUpdate) {
            ScheduleTaskUpdate entity = (ScheduleTaskUpdate) args[0];  // 获取第一个参数（请求体）
            ScheduleTask scheduleTask = ScheduleTaskUpdateMapper.INSTANCE.convertScheduleTask(entity);
            // 只校验特定URL
            validScheduleParam(request, scheduleTask);
        }

    }

    private static void validScheduleParam(HttpServletRequest request, ScheduleTask entity) {
        if (request.getRequestURI().startsWith("/task") && ("POST".equals(request.getMethod()) || "PUT".equals(request.getMethod()))) {
            // 校验必填字段
            if (entity.getExecutionType() != null) {
                JobType jobType = JobType.valueOf(entity.getExecutionType());
                switch (jobType) {
                    case SINGLE:
                        if (entity.getCronExpression() != null || entity.getIntervalUnit() != null || entity.getIntervalValue() != null) {
                            log.error("SINGLE task should not contain setting like cronExpression, intervalUnit or intervalValue");
                            throw new GkException(GkError.EXTRA_PARAMETER_ERROR);
                        }
                        break;
                    case LOOP:
                        if (entity.getIntervalUnit() == null || entity.getIntervalValue() == null || entity.getIntervalValue() <= 0) {
                            log.error("LOOP task should contain intervalUnit or intervalValue setting");
                            throw new GkException(GkError.LOOP_PARAMETER_ERROR);
                        }
                        break;
                    case SCHEDULED:
                        if (entity.getCronExpression() == null || !CronExpression.isValidExpression(entity.getCronExpression())) {
                            log.error("SCHEDULED task should contain cronExpression setting and valid cronExpression String");
                            throw new GkException(GkError.SCHEDULED_PARAMETER_ERROR);
                        }
                        break;
                    default:
                        throw new GkException(GkError.TASK_TYPE_NOT_EXIST);
                }
            }
        }
    }
}