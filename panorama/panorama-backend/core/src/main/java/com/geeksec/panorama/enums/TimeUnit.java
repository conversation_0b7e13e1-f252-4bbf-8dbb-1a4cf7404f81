package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum TimeUnit {
  SECOND(1, "秒"),
  MINUTE(2, "分钟"),
  HOUR(3, "小时"),
  DAY(4, "天");

  @EnumValue private final int code;
  private final String desc;

  TimeUnit(int code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  @JsonValue
  public int getCode() {
    return code;
  }

  @JsonCreator
  public static TimeUnit fromCode(int code) {
    for (TimeUnit severity : values()) {
      if (severity.code == code) {
        return severity;
      }
    }
    throw new IllegalArgumentException("Invalid TimeUnit code: " + code);
  }
}
