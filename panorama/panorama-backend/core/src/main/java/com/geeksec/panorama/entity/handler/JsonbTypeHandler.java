package com.geeksec.panorama.entity.handler;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

@Slf4j
@MappedTypes({Object.class})
public class JsonbTypeHandler extends JacksonTypeHandler {
  private static final PGobject jsonObject = new PGobject();

  public JsonbTypeHandler(Class<?> type) {
    super(type);
  }

  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType)
      throws SQLException {
    if (ps != null) {
      jsonObject.setType("jsonb");
      jsonObject.setValue(toJson(parameter));
      ps.setObject(i, jsonObject);
    }
  }
}
