package com.geeksec.panorama.service;

import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.entity.ModelRun;
import com.geeksec.panorama.model.GraphPattern;
import com.vesoft.nebula.client.graph.data.ResultSet;
import java.util.concurrent.CompletableFuture;

public interface ModelExecutionService {

  /**
   * 执行自定义模型
   *
   * @param model
   * @return
   */
  CompletableFuture<Boolean> executeCustomModel(CustomAttackModel model, String taskId);

  /**
   * 创建模型执行结果
   *
   * @param model
   * @return
   */
  ModelRun createModelRun(CustomAttackModel model, String taskId);

  /**
   * 执行图模式匹配
   *
   * @param pattern 图模式
   * @return 查询结果列表
   */
  ResultSet executeQuery(GraphPattern pattern);
}
