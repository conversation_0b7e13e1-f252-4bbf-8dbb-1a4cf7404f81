package com.geeksec.panorama.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.geeksec.panorama.consts.ModelType;
import com.geeksec.panorama.dao.CustomAttackModelDao;
import com.geeksec.panorama.dao.ScheduleTaskDao;
import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.entity.ModelRun;
import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.enums.TaskStatus;
import com.geeksec.panorama.service.CustomAttackModelService;
import com.geeksec.panorama.service.ModelRunService;
import lombok.NoArgsConstructor;
import org.quartz.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

@Component
public class TaskJob extends QuartzJobBean {

    private static final Logger logger = LoggerFactory.getLogger(TaskJob.class);

    Scheduler scheduler;

    CustomAttackModelService customAttackModelService;

    CustomAttackModelDao customAttackModelDao;

    ModelRunService modelRunService;

    ScheduleTaskDao scheduleTaskDao;

    @Autowired
    public void setScheduler(Scheduler scheduler) {
        this.scheduler = scheduler;
    }
    @Autowired
    public void setCustomAttackModelService(CustomAttackModelService customAttackModelService) {
        this.customAttackModelService = customAttackModelService;
    }
    @Autowired
    public void setCustomAttackModelDao(CustomAttackModelDao customAttackModelDao) {
        this.customAttackModelDao = customAttackModelDao;
    }
    @Autowired
    public void setModelRunService(ModelRunService modelRunService) {
        this.modelRunService = modelRunService;
    }
    @Autowired
    public void setScheduleTaskDao(ScheduleTaskDao scheduleTaskDao) {
        this.scheduleTaskDao = scheduleTaskDao;
    }

    @Override
    protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
        String modelId = context.getJobDetail().getJobDataMap().getString("modelId");
        String taskId = context.getJobDetail().getJobDataMap().getString("taskId");

        try {
            // exec model -> ngql
            CompletableFuture<Boolean> run = customAttackModelService.run(modelId, taskId);
            // model run result -> update task status
            if (!run.get()){
                throw new JobExecutionException("任务执行失败");
            }
        } catch (Exception e) {
            throw new JobExecutionException("任务执行失败", e);
        }
    }
}
