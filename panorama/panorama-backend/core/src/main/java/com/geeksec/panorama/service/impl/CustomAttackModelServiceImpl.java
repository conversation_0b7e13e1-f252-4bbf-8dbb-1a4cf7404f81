package com.geeksec.panorama.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geeksec.panorama.dao.CustomAttackModelDao;
import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.exception.*;
import com.geeksec.panorama.mapper.CustomAttackModelMapper;
import com.geeksec.panorama.model.CustomAttackModelDto;
import com.geeksec.panorama.model.PageResult;
import com.geeksec.panorama.service.CustomAttackModelService;
import com.geeksec.panorama.service.ModelExecutionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

@Slf4j
@Service
@Validated
@Transactional
public class CustomAttackModelServiceImpl
        extends ServiceImpl<CustomAttackModelDao, CustomAttackModel>
        implements CustomAttackModelService {
    private final CustomAttackModelMapper customAttackModelMapper;
    private final ModelExecutionService modelExecutionService;

    @Autowired
    public CustomAttackModelServiceImpl(
            CustomAttackModelMapper customAttackModelMapper,
            @Lazy ModelExecutionService modelExecutionService) {
        this.customAttackModelMapper = customAttackModelMapper;
        this.modelExecutionService = modelExecutionService;
    }

    /**
     * 获取自定义模型分页列表
     *
     * @param page     页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    @Override
    @Cacheable(value = "customModels", key = "#page + '-' + #pageSize")
    public PageResult<CustomAttackModel> getCustomModelPage(int page, int pageSize) {
        log.info("Fetching custom attack model list. Page: {}, Size: {}", page, pageSize);
        String userId = getCurrentUserId();
        Page<CustomAttackModel> pageRequest = new Page<>(page, pageSize);
        LambdaQueryWrapper<CustomAttackModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(CustomAttackModel::isCurrent, true)
                .orderByDesc(CustomAttackModel::getCreateTime);
        applyUserFilter(queryWrapper, userId);
        return new PageResult<>(page(pageRequest, queryWrapper));
    }

    @Override
    public CustomAttackModel getByIdAndVersion(String id, Integer version) {
        log.debug("Fetching CustomAttackModel with id: {} and version: {}", id, version);
        return lambdaQuery()
                .eq(CustomAttackModel::getId, id)
                .eq(CustomAttackModel::getVersion, version)
                .one();
    }

    /**
     * 创建自定义模型
     *
     * @param modelDto 模型数据传输对象
     */
    @Override
    @CacheEvict(value = "customModels", allEntries = true)
    public void createCustomModel(@Valid CustomAttackModelDto modelDto) {
        log.info("Creating custom attack model: {}", modelDto);

        modelDto.setCreator(getCurrentUserId());

        if (isNameExists(modelDto.getName())) {
            throw new ModelNameDuplicateException("模型名称\"" + modelDto.getName() + "\"已存在。");
        }

        CustomAttackModel modelEntity = customAttackModelMapper.toEntity(modelDto);
        modelEntity.setCurrent(true);
        modelEntity.setVersion(1);

        if (!save(modelEntity)) {
            throw new ModelCreationException("创建自定义攻击模型失败。");
        }

        log.info("Successfully created custom attack model, model_id: {}", modelEntity.getId());
    }

    /**
     * 更新自定义模型
     *
     * @param id       模型ID
     * @param modelDto 模型数据传输对象
     */
    @Override
    @Transactional
    @CacheEvict(value = "customModels", allEntries = true)
    public void updateCustomModel(String id, CustomAttackModelDto modelDto) {
        log.info("Updating custom attack model. ID: {}", id);
        CustomAttackModel currentModel = getCurrentVersionModel(id);

        if (currentModel == null) {
            throw new ModelNotFoundException("ID为" + id + "的模型不存在。");
        }

        if (currentModel.isRunning()) {
            throw new ModelBusyException("该模型正在运行中，无法修改。");
        }

        if (!currentModel.getName().equals(modelDto.getName())
                && isNameDuplicateWithOtherModels(modelDto.getName(), id)) {
            throw new ModelNameDuplicateException("模型名称\"" + modelDto.getName() + "\"已存在。");
        }

        updateModelVersion(currentModel, modelDto);

        log.info("Successfully updated custom attack model. ID: {}", id);
    }

    /**
     * 删除自定义模型
     *
     * @param id 模型ID
     */
    @Override
    @Transactional
    @CacheEvict(value = "customModels", allEntries = true)
    public void deleteCustomModel(String id) {
        log.info("Deleting custom attack model. ID: {}", id);

        CustomAttackModel currentModel = getCurrentVersionModel(id);
        if (currentModel == null) {
            throw new ModelNotFoundException("ID为" + id + "的模型不存在。");
        }

        if (currentModel.isRunning()) {
            throw new ModelBusyException("该模型正在运行中，无法删除。");
        }

        boolean removed =
                remove(new LambdaQueryWrapper<CustomAttackModel>().eq(CustomAttackModel::getId, id));

        if (!removed) {
            throw new ModelDeletionException("删除自定义攻击模型失败。");
        }

        log.info("Successfully deleted all versions of custom attack model. ID: {}", id);
    }

    /**
     * 运行自定义模型
     *
     * @param modelId 模型ID
     * @return 运行结果的CompletableFuture
     */
    @Override
    @Transactional
    public CompletableFuture<Boolean> run(String modelId, String taskId) {
        log.info("Starting to run custom attack model. Model ID: {}", modelId);
        CustomAttackModel model = getCurrentVersionModel(modelId);
        if (model == null) {
            throw new ModelNotFoundException("ID为" + modelId + "的模型不存在。");
        }
//        if (model.isRunning()) {
//            throw new ModelBusyException("该模型正在运行中，无法再次运行。");
//        }

        boolean updated =
                updateCurrentVersion(
                        modelId,
                        wrapper ->
                                wrapper
                                        .set(CustomAttackModel::isRunning, true)
                                        .set(CustomAttackModel::getLastRunTime, LocalDateTime.now()));

        if (!updated) {
            throw new ModelUpdateException("更新模型状态失败，模型ID: " + modelId);
        }

        return modelExecutionService
                .executeCustomModel(model, taskId)
                .whenComplete((result, throwable) -> updateModelStatus(modelId, false));
    }

    private void updateModelStatus(String modelId, boolean running) {
        updateCurrentVersion(modelId, wrapper -> wrapper.set(CustomAttackModel::isRunning, running));
    }

    private void updateModelVersion(CustomAttackModel currentModel, CustomAttackModelDto modelDto) {
        boolean updated =
                updateCurrentVersion(
                        currentModel.getId(), wrapper -> wrapper.set(CustomAttackModel::isCurrent, false));
        if (!updated) {
            throw new ModelUpdateException("更新模型版本失败。");
        }

        CustomAttackModel newVersion = new CustomAttackModel();
        customAttackModelMapper.updateEntity(modelDto, newVersion);
        newVersion.setId(currentModel.getId());
        newVersion.setVersion(currentModel.getVersion() + 1);
        newVersion.setCreator(currentModel.getCreator());
        newVersion.setCurrent(true);

        if (!save(newVersion)) {
            throw new ModelUpdateException("创建模型新版本失败。");
        }
    }

    private boolean updateCurrentVersion(
            String modelId, Consumer<LambdaUpdateWrapper<CustomAttackModel>> updateFunction) {
        LambdaUpdateWrapper<CustomAttackModel> updateWrapper =
                new LambdaUpdateWrapper<CustomAttackModel>()
                        .eq(CustomAttackModel::getId, modelId)
                        .eq(CustomAttackModel::isCurrent, true);

        updateFunction.accept(updateWrapper);

        return update(updateWrapper);
    }

    private boolean isNameExists(String name) {
        return lambdaQuery()
                .eq(CustomAttackModel::getName, name)
                .eq(CustomAttackModel::isCurrent, true)
                .count()
                > 0;
    }

    private boolean isNameDuplicateWithOtherModels(String name, String currentModelId) {
        return lambdaQuery()
                .eq(CustomAttackModel::getName, name)
                .ne(CustomAttackModel::getId, currentModelId)
                .eq(CustomAttackModel::isCurrent, true)
                .count()
                > 0;
    }

    private CustomAttackModel getCurrentVersionModel(String id) {
        return lambdaQuery()
                .eq(CustomAttackModel::getId, id)
                .eq(CustomAttackModel::isCurrent, true)
                .one();
    }

    /**
     * 获取当前用户名
     *
     * @return 当前用户名
     */
    public static String getCurrentUserId() {
        String userId = MDC.get("user_id");
        log.info("当前操作用户user_id->{}", userId);
        return userId;
    }

    /**
     * 按用户过滤
     *
     * @param queryWrapper 查询包装器
     * @param userId       用户ID
     */
    private void applyUserFilter(
            LambdaQueryWrapper<CustomAttackModel> queryWrapper, String userId) {
        log.info("查询用户条件 user_id -> {}", userId);
        if (!"admin".equals(userId)) {
            queryWrapper.and(
                    i ->
                            i.eq(CustomAttackModel::getCreator, userId)
                                    .or()
                                    .eq(CustomAttackModel::getCreator, ""));
        }
    }
}
