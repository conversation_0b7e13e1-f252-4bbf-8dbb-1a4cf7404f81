package com.geeksec.panorama.model;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ScheduleTaskVo {
    private String taskId;
    private String taskName;
    private String executionType;
    private String intervalUnit;
    private Integer intervalValue;
    private String cronExpression;
    private String taskStatus = "PENDING";
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private LocalDateTime lastRunTime;
    private String createdBy;
    private String modelName;
    private String modelId;
}
