package com.geeksec.panorama.config;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.geeksec.panorama.dao.ScheduleTaskDao;
import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.enums.JobType;
import com.geeksec.panorama.enums.TaskStatus;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;

@Component
@Slf4j
public class TaskJobListener implements JobListener {
    ScheduleTaskDao scheduleTaskDao;

    @Autowired
    void setScheduleTaskDao(ScheduleTaskDao scheduleTaskDao) {
        this.scheduleTaskDao = scheduleTaskDao;
    }
    @Override
    public String getName() {
        return "TaskJobListener";
    }

    @Override
    public void jobToBeExecuted(JobExecutionContext context) {
        String taskId = context.getJobDetail().getJobDataMap().getString("taskId");
        scheduleTaskDao.update(new LambdaUpdateWrapper<ScheduleTask>().eq(ScheduleTask::getTaskId, taskId).set(ScheduleTask::getTaskStatus, TaskStatus.RUNNING));
        log.info("Job 即将执行: {}", context.getJobDetail().getKey());
    }

    @Override
    public void jobExecutionVetoed(JobExecutionContext context) {
    }

    @Override
    public void jobWasExecuted(JobExecutionContext context, JobExecutionException jobException) {
        if (jobException != null) {
            log.error("Job 执行失败: {}", context.getJobDetail().getKey(), jobException);
            String taskId = context.getJobDetail().getJobDataMap().getString("taskId");
            scheduleTaskDao.update(new LambdaUpdateWrapper<ScheduleTask>().eq(ScheduleTask::getTaskId, taskId).set(ScheduleTask::getTaskStatus, TaskStatus.FAILED));
        } else  {
            String taskId = context.getJobDetail().getJobDataMap().getString("taskId");
            String taskType = context.getJobDetail().getJobDataMap().getString("taskType");
            try {
                if (JobType.valueOf(taskType).equals(JobType.SINGLE)){
                    scheduleTaskDao.update(new LambdaUpdateWrapper<ScheduleTask>().eq(ScheduleTask::getTaskId, taskId)
                            .set(ScheduleTask::getTaskStatus, TaskStatus.COMPLETED).set(ScheduleTask::getLastRunTime, LocalDateTime.now()));
                }
            } catch (RuntimeException e) {
                log.error("非任务调度 Job 执行完成: {}", context.getJobDetail().getKey());
            }

            log.info("Job 执行完成: {}", context.getJobDetail().getKey());
        }
    }
}
