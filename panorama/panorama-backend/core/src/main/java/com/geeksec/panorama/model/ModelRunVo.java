package com.geeksec.panorama.model;

import com.geeksec.panorama.enums.AttackType;
import com.geeksec.panorama.enums.ClassType;
import com.geeksec.panorama.enums.Severity;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class ModelRunVo {
  private String modelName;
  private Severity severity;
  private ClassType type;
  private GraphPattern pattern;
  private LocalDateTime runTime;
  private long eventCount;
}
