package com.geeksec.panorama.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geeksec.panorama.dao.AttackEventDao;
import com.geeksec.panorama.dao.CustomAttackModelDao;
import com.geeksec.panorama.dao.ModelRunDao;
import com.geeksec.panorama.dao.ScheduleTaskDao;
import com.geeksec.panorama.entity.AttackEvent;
import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.entity.ModelRun;
import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.enums.GkError;
import com.geeksec.panorama.enums.JobType;
import com.geeksec.panorama.enums.TaskStatus;
import com.geeksec.panorama.exception.TaskFailedException;
import com.geeksec.panorama.job.JobCreationStrategyFactory;
import com.geeksec.panorama.job.TaskJob;
import com.geeksec.panorama.mapper.ScheduleTaskUpdateMapper;
import com.geeksec.panorama.model.PageResult;
import com.geeksec.panorama.model.ScheduleTaskUpdate;
import com.geeksec.panorama.model.ScheduleTaskVo;
import com.geeksec.panorama.model.TaskPageCondition;
import com.geeksec.panorama.service.JobCreationStrategy;
import com.geeksec.panorama.service.ScheduleTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;

import static org.quartz.JobBuilder.newJob;

@Service
@Slf4j
public class ScheduleTaskServiceImpl extends ServiceImpl<ScheduleTaskDao, ScheduleTask>
        implements ScheduleTaskService {
    final ModelRunDao modelRunDao;
    final ScheduleTaskDao scheduleTaskDao;
    final CustomAttackModelDao customAttackModelDao;
    final JobCreationStrategyFactory jobCreationStrategyFactory;
    final Scheduler scheduler;
    final AttackEventDao attackEventDao;

    public ScheduleTaskServiceImpl(ModelRunDao modelRunDao, ScheduleTaskDao scheduleTaskDao, CustomAttackModelDao customAttackModelDao, JobCreationStrategyFactory jobCreationStrategyFactory, Scheduler scheduler, AttackEventDao attackEventDao) {
        this.modelRunDao = modelRunDao;
        this.scheduleTaskDao = scheduleTaskDao;
        this.customAttackModelDao = customAttackModelDao;
        this.jobCreationStrategyFactory = jobCreationStrategyFactory;
        this.scheduler = scheduler;
        this.attackEventDao = attackEventDao;
    }


    @Override
    public String insertTask(ScheduleTask entity) {
        // check task name
        if (StringUtils.isBlank(entity.getTaskName()) || validTaskNameExists(entity.getTaskName())) {
            throw new TaskFailedException(GkError.TASK_NAME_INVALID);
        }
        // check model
        validModel(entity.getModelId());
        ScheduleTask insertEntity = new ScheduleTask();
        String currentUserId = CustomAttackModelServiceImpl.getCurrentUserId();
        String taskId = UUID.randomUUID().toString();

        insertEntity.setTaskName(entity.getTaskName());
        insertEntity.setCreatedBy(currentUserId);
        insertEntity.setTaskId(taskId);
        insertEntity.setModelId(entity.getModelId());
        insertEntity.setExecutionType(entity.getExecutionType());

        switch (JobType.valueOf(entity.getExecutionType())) {
            case SINGLE:
                break;
            case LOOP:
                insertEntity.setIntervalUnit(entity.getIntervalUnit());
                insertEntity.setIntervalValue(entity.getIntervalValue());
                break;
            case SCHEDULED:
                insertEntity.setCronExpression(entity.getCronExpression());
                break;
        }
        // create schedule job
        createScheduleTask(insertEntity);
        save(insertEntity);
        return taskId;
    }

    private boolean validTaskNameExists(String taskName) {
        ScheduleTask scheduleTask = scheduleTaskDao.selectOne(new QueryWrapper<ScheduleTask>().eq("task_name", taskName));
        return scheduleTask != null;
    }

    private void createScheduleTask(ScheduleTask entity) {
        try {
            JobType jobType = JobType.valueOf(entity.getExecutionType());
            JobCreationStrategy strategy = jobCreationStrategyFactory.getStrategy(jobType);
            scheduler.scheduleJob(strategy.createJobDetail(entity), strategy.createTrigger(entity));
        } catch (IllegalArgumentException e) {
            log.error("Task type: {} not exist", entity.getExecutionType());
            throw new TaskFailedException(GkError.TASK_TYPE_NOT_EXIST);
        } catch (SchedulerException e) {
            log.error("Create task failed: {}", e.getMessage());
            throw new TaskFailedException(GkError.JOB_CREATE_FAILED);
        }
    }

    @Override
    @Transactional
    public boolean del(String taskId) {
        ScheduleTask scheduleTask = getById(taskId);
        if (scheduleTask == null) {
            log.info("taskId: {} not found", taskId);
            throw new TaskFailedException(GkError.TASK_NOT_FOUND_FAILED);
        }
        String currentUserId = CustomAttackModelServiceImpl.getCurrentUserId();
        if (!currentUserId.equals(scheduleTask.getCreatedBy())){
            log.error("taskId: {} owner Uer {} current User: {} ", scheduleTask.getTaskId(), scheduleTask.getCreatedBy(), currentUserId);
            throw new TaskFailedException(GkError.TASK_OWNER_NOT_MATCH);
        }
        // delete task -> model run
        List<ModelRun> modelRuns = modelRunDao.selectList(new LambdaQueryWrapper<ModelRun>().eq(ModelRun::getTaskId, taskId));
        for (ModelRun modelRun : modelRuns) {
            // delete model run -> attack event
            attackEventDao.delete(new LambdaQueryWrapper<AttackEvent>().eq(AttackEvent::getRunId, modelRun.getId()));
            modelRunDao.deleteById(modelRun.getId());
        }
        modelRunDao.delete(new LambdaQueryWrapper<ModelRun>().eq(ModelRun::getTaskId, taskId));

        // delete task job
        JobKey jobKey = JobKey.jobKey(taskId + "_" + scheduleTask.getExecutionType() + "_" + scheduleTask.getModelId());
        try {
            if (scheduler.checkExists(jobKey)) {
                scheduler.deleteJob(jobKey);
            }
        } catch (SchedulerException e) {
            log.info("task{} -> model job {} not found", taskId, taskId + "_" + scheduleTask.getExecutionType() + "_" + scheduleTask.getModelId());
        }
        // delete task
        return removeById(taskId);
    }

    @Override
    public boolean update(ScheduleTaskUpdate entity) {
        String taskId = entity.getTaskId();
        ScheduleTask scheduleTask = getById(taskId);
        String currentUserId = CustomAttackModelServiceImpl.getCurrentUserId();
        if (scheduleTask == null) {
            log.error("taskId: {} not found", taskId);
            throw new TaskFailedException(GkError.TASK_NOT_FOUND_FAILED);
        }
        if (!currentUserId.equals(scheduleTask.getCreatedBy())){
            log.error("taskId: {} owner Uer {} current User: {} ", taskId, scheduleTask.getCreatedBy(), currentUserId);
            throw new TaskFailedException(GkError.TASK_OWNER_NOT_MATCH);
        }


        try {
            TaskStatus currentStatus = TaskStatus.valueOf(scheduleTask.getTaskStatus());
            TaskStatus updateStatus = TaskStatus.valueOf(entity.getTaskStatus());
            JobKey jobKey = JobKey.jobKey(taskId + "_" + scheduleTask.getExecutionType() + "_" + scheduleTask.getModelId());
            if (!scheduler.checkExists(jobKey)) {
                throw new TaskFailedException(GkError.JOB_NOT_FOUND_FAILED);
            }
            switch (updateStatus) {
                case STOP:
                    // check status
                    if (currentStatus.equals(TaskStatus.PAUSE)) {
                        throw new TaskFailedException(GkError.TASK_STATUS_NOT_CHANGED);
                    }
                    update(new LambdaUpdateWrapper<ScheduleTask>().eq(ScheduleTask::getTaskId, taskId).set(ScheduleTask::getTaskStatus, TaskStatus.PAUSE));
                    scheduler.pauseJob(jobKey);
                    break;
                case RESUME:
                    // check status
                    if (!currentStatus.equals(TaskStatus.PAUSE)) {
                        throw new TaskFailedException(GkError.TASK_STATUS_NOT_CHANGED);
                    }
                    update(new LambdaUpdateWrapper<ScheduleTask>().eq(ScheduleTask::getTaskId, taskId).set(ScheduleTask::getTaskStatus, TaskStatus.RUNNING));
                    scheduler.resumeJob(jobKey);
                    break;
                default:
                    break;
            }
        } catch (IllegalArgumentException e) {
            throw new TaskFailedException(GkError.TASK_STATUS_NOT_EXIST);
        } catch (SchedulerException e) {
            throw new RuntimeException(e);
        }
        return true;
    }

    /**
     * update task setting
     * if current executionType is single -> create new job
     * else -> renew job trigger
     *         if modelId exist and not equal current modelId -> change model for task, delete old job and create new job with new setting
     *         else ->
     *             executionType exist -> change execution type for task, delete old job and create new job
     *                     -> if executionType is single -> create new job
     *                     -> else -> renew job trigger
     *                -> if type is single, intervalUnit, intervalValue, cronExpression is useless
     *                -> if type is loop, intervalUnit, intervalValue is necessary
     *                -> if type is scheduled, cronExpression is necessary
     * @param updateVo
     * @return
     */
    @Override
    public boolean updateSetting(ScheduleTaskUpdate updateVo) {
        String updateTaskId = updateVo.getTaskId();
        ScheduleTask currentTask = getById(updateTaskId);
        if (currentTask == null) {
            log.error("updateTaskId: {} not found", updateTaskId);
            throw new TaskFailedException(GkError.TASK_NOT_FOUND_FAILED);
        }
        if (StringUtils.isEmpty(updateVo.getExecutionType())){
            log.error("updateTaskId: {} executionType is empty", updateTaskId);
            throw new TaskFailedException(GkError.TASK_EXECUTION_TYPE_EMPTY);
        }
        String currentUserId = CustomAttackModelServiceImpl.getCurrentUserId();
        if (!currentUserId.equals(currentTask.getCreatedBy())){
            log.error("taskId: {} owner Uer {} current User: {} ", currentTask.getTaskId(), currentTask.getCreatedBy(), currentUserId);
            throw new TaskFailedException(GkError.TASK_OWNER_NOT_MATCH);
        }
        String currentExecType = currentTask.getExecutionType();
        JobType currentJobType = JobType.valueOf(currentExecType);
        ScheduleTask updateScheduleTask = ScheduleTaskUpdateMapper.INSTANCE.convertScheduleTask(updateVo);
        String currentModelId = currentTask.getModelId();

        // update database task settings
        LambdaUpdateWrapper<ScheduleTask> scheduleTaskLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        scheduleTaskLambdaUpdateWrapper.eq(ScheduleTask::getTaskId, updateTaskId);

        try {
            String jobKeyStr = updateTaskId + "_" + currentExecType + "_" + currentModelId;
            if (StringUtils.isNotBlank(updateScheduleTask.getModelId()) && !currentModelId.equals(updateScheduleTask.getModelId())){
                // check model is current
                validModel(updateScheduleTask.getModelId());
                // change model -> delete old job and create new job
                JobKey currentJobKey = JobKey.jobKey(jobKeyStr);
                try {
                    if (scheduler.checkExists(currentJobKey)) {
                        TriggerKey triggerKey = TriggerKey.triggerKey(jobKeyStr);
                        scheduler.pauseTrigger(triggerKey);// 停止触发器
                        scheduler.unscheduleJob(triggerKey);// 移除触发器
                        scheduler.deleteJob(currentJobKey);
                    }
                    createScheduleTask(updateScheduleTask);
                } catch (SchedulerException e) {
                    log.info("Updating setting failed for task{} -> model job {} not found", updateTaskId, jobKeyStr);
                    throw new TaskFailedException(GkError.JOB_UPDATE_FAILED);
                }
            } else {
                updateScheduleTask.setModelId(currentModelId);
                // if original type is single -> create new job
                if (currentJobType.equals(JobType.SINGLE)) {
                    createScheduleTask(updateScheduleTask);
                } else {
                    // renew job trigger
                    TriggerKey currentTriggerKey = TriggerKey.triggerKey(jobKeyStr);
                    Trigger currentTrigger = scheduler.getTrigger(currentTriggerKey);

                    if (currentTrigger == null) {
                        log.info("Updating setting failed -> No existing trigger found for task {}", updateTaskId);
                        createScheduleTask(updateScheduleTask);
                    } else {
                        log.info("Updating setting for task {}", updateTaskId);
                        JobCreationStrategy strategy = jobCreationStrategyFactory.getStrategy(JobType.valueOf(updateScheduleTask.getExecutionType()));
                        Trigger newTrigger = strategy.createTrigger(updateScheduleTask);
                        scheduler.rescheduleJob(currentTriggerKey, newTrigger);
                    }
                }
            }
            updateScheduleSetting(updateScheduleTask, scheduleTaskLambdaUpdateWrapper);
            if (!currentExecType.equals(updateScheduleTask.getExecutionType())) {
                scheduleTaskLambdaUpdateWrapper.set(ScheduleTask::getExecutionType, updateScheduleTask.getExecutionType());
            }
            if (StringUtils.isNotEmpty(updateVo.getModelId())) {
                scheduleTaskLambdaUpdateWrapper.set(ScheduleTask::getModelId, updateVo.getModelId());
            }
            // 任务名不为空的话，进行修改
            if (StringUtils.isNotEmpty(updateVo.getTaskName())) {
                scheduleTaskLambdaUpdateWrapper.set(ScheduleTask::getTaskName, updateVo.getTaskName());
            }
            update(null, scheduleTaskLambdaUpdateWrapper);
        } catch (SchedulerException e) {
            throw new TaskFailedException(GkError.JOB_UPDATE_FAILED);
        }

        return true;
    }

    /**
     * single not update other setting
     * loop only update intervalUnit, intervalValue
     * scheduled only update cronExpression
     */
    private static void updateScheduleSetting(ScheduleTask updateVo, LambdaUpdateWrapper<ScheduleTask> scheduleTaskLambdaUpdateWrapper) {
        switch (JobType.valueOf(updateVo.getExecutionType())) {
            case SINGLE:
                scheduleTaskLambdaUpdateWrapper.set(ScheduleTask::getIntervalValue, 0)
                        .set(ScheduleTask::getIntervalUnit, "")
                        .set(ScheduleTask::getCronExpression, "");
                break;
            case LOOP:
                scheduleTaskLambdaUpdateWrapper.set(ScheduleTask::getIntervalUnit, updateVo.getIntervalUnit())
                        .set(ScheduleTask::getIntervalValue, updateVo.getIntervalValue())
                        .set(ScheduleTask::getCronExpression, "");
                break;
            case SCHEDULED:
                scheduleTaskLambdaUpdateWrapper.set(ScheduleTask::getCronExpression, updateVo.getCronExpression())
                        .set(ScheduleTask::getIntervalUnit, "")
                        .set(ScheduleTask::getIntervalValue, 0);
                break;
        }
    }

    private void validModel(String updateVoModelId) {
        QueryWrapper<CustomAttackModel> queryWrapper = new QueryWrapper();
        queryWrapper.eq("id",updateVoModelId).eq("current",true);
        CustomAttackModel updateModel = customAttackModelDao.selectOne(queryWrapper);
        if (updateModel == null) {
            log.error("update modelId: {} not found", updateVoModelId);
            throw new TaskFailedException(GkError.MODEL_NOT_FOUND);
        }
        if (!updateModel.isCurrent()){
            log.error("update modelId: {} is not current", updateVoModelId);
            throw new TaskFailedException(GkError.MODEL_NOT_CURRENT);
        }
    }

    @Override
    public PageResult<ScheduleTaskVo> list(TaskPageCondition condition) {
        Integer page = condition.getPage();
        Integer pageSize = condition.getSize();
        if (page == null || page < 1) {
            page = 1;
        }
        if (pageSize == null || pageSize < 1) {
            pageSize = 10;
        }
        if (!condition.getSortOrder().equals("asc") && !condition.getSortOrder().equals("desc")) {
            condition.setSortOrder("desc");
        }
        String currentUserId = CustomAttackModelServiceImpl.getCurrentUserId();
        Page<ScheduleTaskVo> voPage = new Page<>(page, pageSize);
        Page<ScheduleTaskVo> allScheduleTasks = scheduleTaskDao.getAllScheduleTasks(voPage,
                currentUserId,condition.getTaskName(),condition.getModelName(),condition.getSortOrder(), condition.getStartTime(), condition.getEndTime());
        return new PageResult<>(allScheduleTasks);
    }

    @Override
    public List<Map<String, Object>> getAllJobs() throws SchedulerException {
        // 查询所有 Job
        Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.anyJobGroup());
        List<Map<String, Object>> jobs = new ArrayList<>();

        for (JobKey jobKey : jobKeys) {
            JobDetail jobDetail = scheduler.getJobDetail(jobKey);
            List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);

            Map<String, Object> jobInfo = new HashMap<>();
            jobInfo.put("jobName", jobKey.getName());
            jobInfo.put("jobGroup", jobKey.getGroup());
            jobInfo.put("jobClass", jobDetail.getJobClass().getName());
            jobInfo.put("description", jobDetail.getDescription());

            // 处理JobDataMap
            if (jobDetail.getJobDataMap() != null && !jobDetail.getJobDataMap().isEmpty()) {
                jobInfo.put("jobData", jobDetail.getJobDataMap().getWrappedMap());
            }

            // 处理触发器信息
            List<Map<String, Object>> triggerInfos = new ArrayList<>();
            for (Trigger trigger : triggers) {
                Map<String, Object> triggerInfo = new HashMap<>();
                triggerInfo.put("triggerName", trigger.getKey().getName());
                triggerInfo.put("triggerGroup", trigger.getKey().getGroup());
                triggerInfo.put("triggerClass", trigger.getClass().getSimpleName());
                triggerInfo.put("nextFireTime", trigger.getNextFireTime());
                triggerInfo.put("previousFireTime", trigger.getPreviousFireTime());
                triggerInfo.put("priority", trigger.getPriority());
                triggerInfo.put("startTime", trigger.getStartTime());
                triggerInfo.put("endTime", trigger.getEndTime());
                triggerInfo.put("state", scheduler.getTriggerState(trigger.getKey()).toString());

                // 针对不同类型的触发器添加特定信息
                if (trigger instanceof SimpleTrigger) {
                    SimpleTrigger simpleTrigger = (SimpleTrigger) trigger;
                    triggerInfo.put("repeatCount", simpleTrigger.getRepeatCount());
                    triggerInfo.put("repeatInterval", simpleTrigger.getRepeatInterval());
                    triggerInfo.put("timesTriggered", simpleTrigger.getTimesTriggered());
                } else if (trigger instanceof CronTrigger) {
                    CronTrigger cronTrigger = (CronTrigger) trigger;
                    triggerInfo.put("cronExpression", cronTrigger.getCronExpression());
                    triggerInfo.put("timeZone", cronTrigger.getTimeZone() != null ?
                            cronTrigger.getTimeZone().getID() : null);
                }

                triggerInfos.add(triggerInfo);
            }

            jobInfo.put("triggers", triggerInfos);
            jobs.add(jobInfo);
        }

        return jobs;
    }

    public static JobDetail getJobDetail(ScheduleTask scheduleTask) {
        JobDataMap jobDataMap = new JobDataMap();
        jobDataMap.put("modelId", scheduleTask.getModelId());
        jobDataMap.put("taskId", scheduleTask.getTaskId());
        jobDataMap.put("taskType", scheduleTask.getExecutionType());

        return newJob(TaskJob.class) // IntervalJob 是具体的 Job 实现
                .withIdentity(scheduleTask.getTaskId() + "_" + scheduleTask.getExecutionType() + "_" + scheduleTask.getModelId())
                .usingJobData(jobDataMap)
                .build();
    }
}
