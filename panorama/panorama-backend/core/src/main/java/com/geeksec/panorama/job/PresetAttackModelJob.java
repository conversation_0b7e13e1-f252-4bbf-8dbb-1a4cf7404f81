package com.geeksec.panorama.job;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.geeksec.panorama.consts.ModelType;
import com.geeksec.panorama.entity.ModelRun;
import com.geeksec.panorama.entity.PresetAttackModel;
import com.geeksec.panorama.service.ModelRunService;
import com.geeksec.panorama.service.PresetAttackModelService;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PresetAttackModelJob extends QuartzJobBean {
  private static final String KAFKA_TOPIC = "preset-attack-model-topic";

  private KafkaTemplate<String, Object> kafkaTemplate;
  private PresetAttackModelService presetAttackModelService;
  private ModelRunService modelRunService;

  @Autowired
  public void setKafkaTemplate(KafkaTemplate<String, Object> kafkaTemplate) {
    this.kafkaTemplate = kafkaTemplate;
  }

  @Autowired
  public void setPresetEventModelService(PresetAttackModelService presetAttackModelService) {
    this.presetAttackModelService = presetAttackModelService;
  }

  @Autowired
  public void setModelRunService(ModelRunService modelRunService) {
    this.modelRunService = modelRunService;
  }

  @Override
  protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
    String modelId = context.getJobDetail().getJobDataMap().getString("modelId");
    ModelRun modelRun = new ModelRun();
    modelRun.setModelId(modelId);
    modelRun.setModelType(ModelType.PRESET_MODE);
    modelRun.setCreateTime(LocalDateTime.now());

    try {
      modelRunService.save(modelRun);

      PresetAttackModel model = presetAttackModelService.getById(modelId);
      if (model == null) {
        throw new JobExecutionException("Model not found for id: " + modelId);
      }

      Map<String, Object> message = new HashMap<>();
      message.put("modelId", modelId);
      message.put("runId", modelRun.getId());
      message.put("params", model.getParams());

      kafkaTemplate.send(KAFKA_TOPIC, message);
      log.info("Model: {} is triggered.", modelId);

      LambdaUpdateWrapper<PresetAttackModel> updateWrapper = new LambdaUpdateWrapper<>();
      updateWrapper
          .eq(PresetAttackModel::getId, modelId)
          .set(PresetAttackModel::getLastRunTime, Instant.now());
      presetAttackModelService.update(updateWrapper);
    } catch (Exception e) {
      throw new JobExecutionException("Failed to execute job for model: " + modelId, e);
    }
  }
}
