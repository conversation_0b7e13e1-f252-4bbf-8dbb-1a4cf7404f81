package com.geeksec.panorama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geeksec.ngbatis.vo.VertexAssociationEdgeVo;
import com.geeksec.panorama.consts.ModelType;
import com.geeksec.panorama.dao.ModelRunDao;
import com.geeksec.panorama.entity.*;
import com.geeksec.panorama.enums.EdgeType;
import com.geeksec.panorama.enums.EventStatus;
import com.geeksec.panorama.exception.ModelNotFoundException;
import com.geeksec.panorama.exception.ModelRunNotFoundException;
import com.geeksec.panorama.model.ModelRunVo;
import com.geeksec.panorama.service.AttackEventService;
import com.geeksec.panorama.service.CustomAttackModelService;
import com.geeksec.panorama.service.ModelRunService;
import com.geeksec.panorama.service.PresetAttackModelService;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class ModelRunServiceImpl extends ServiceImpl<ModelRunDao, ModelRun>
    implements ModelRunService {

  private final CustomAttackModelService customAttackModelService;
  private final PresetAttackModelService presetAttackModelService;
  private final AttackEventService attackEventService;

  @Autowired
  public ModelRunServiceImpl(
      @Lazy CustomAttackModelService customAttackModelService,
      PresetAttackModelService presetAttackModelService,
      AttackEventService attackEventService) {
    this.customAttackModelService = customAttackModelService;
    this.presetAttackModelService = presetAttackModelService;
    this.attackEventService = attackEventService;
  }

  @Override
  public ModelRunVo getRunById(String id) {
    log.info("Fetching model run by ID: {}", id);
    ModelRun modelRun = getById(id);

    if (modelRun == null) {
      log.warn("Model run not found with ID: {}", id);
      throw new ModelRunNotFoundException("ID为" + id + "的模型不存在。");
    }

    ModelRunVo view = new ModelRunVo();
    view.setRunTime(modelRun.getCreateTime());
    if (modelRun.getModelType() == ModelType.PRESET_MODE) {
      PresetAttackModel model = presetAttackModelService.getById(modelRun.getModelId());
      if (model != null) {
        populateVoFromEntity(view, model);
      } else {
        throw new ModelNotFoundException("运行记录：" + modelRun.getId() + "对应的预置模型不存在。");
      }
    } else {
      CustomAttackModel model =
          customAttackModelService.getByIdAndVersion(
              modelRun.getModelId(), modelRun.getModelVersion());
      if (model != null) {
        populateVoFromEntity(view, model);
      } else {
        throw new ModelNotFoundException("运行记录：" + modelRun.getId() + "对应的自定义模型不存在。");
      }
    }

    view.setEventCount(calculateEventCount(id));
    return view;
  }

  private void populateVoFromEntity(ModelRunVo view, BaseAttackModel model) {
    view.setModelName(model.getName());
    view.setSeverity(model.getSeverity());
    view.setType(model.getType());
    view.setPattern(model.getPattern());
  }

  private long calculateEventCount(String runId) {
    return attackEventService.countEventsByRun(runId);
  }

  @Override
  public List<VertexAssociationEdgeVo> getEdgesBetweenVertices(
      String runId, String endEventId, String fromVertexId, String toVertexId, EdgeType edgeType) {
    log.info(
        "Querying edges between vertices. Run ID: {}, Event ID: {}, From: {}, To: {}, Edge Type: {}",
        runId,
        endEventId,
        fromVertexId,
        toVertexId,
        edgeType);
    List<AttackEvent> events = attackEventService.getEventsByRunIdUpToEventId(runId, endEventId);
    return events.stream()
        .filter(event -> event.getEventGraph() != null)
        .flatMap(event -> event.getEventGraph().getEdge().stream())
        .filter(
            edge ->
                edge.getFrom().equals(fromVertexId)
                    && edge.getTo().equals(toVertexId)
                    && edge.getType().equals(edgeType))
        .map(this::formatAttackTime)
        .distinct()
        .collect(Collectors.toList());
  }

  private VertexAssociationEdgeVo formatAttackTime(VertexAssociationEdgeVo edge) {
    if (edge.getType() == EdgeType.MAKE_ATTACK && edge.getEdgeInfo() != null) {
      Object attackTimeObj = edge.getEdgeInfo().get("attack_time");
      if (attackTimeObj instanceof Integer) {
        int attackTime = (Integer) attackTimeObj;
        Instant instant = Instant.ofEpochSecond(attackTime);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedTime = dateTime.format(formatter);
        edge.getEdgeInfo().put("attack_time", formattedTime);
      }
    }
    return edge;
  }

  //  @Override
  //  public List<ModelRunView> getRunByIds(List<String> ids) {
  //    log.info("Fetching runs by IDs: {}", ids);
  //    List<ModelRun> modelRuns = listByIds(ids);
  //    return modelRuns.stream().map(modelRunMapper::toDto).collect(Collectors.toList());
  //  }
  //
  //  @Override
  //  public List<ModelRunView> getRecentRuns(int count) {
  //    log.info("Fetching {} most recent runs", count);
  //    LambdaQueryWrapper<ModelRun> queryWrapper = new LambdaQueryWrapper<>();
  //    queryWrapper.orderByDesc(ModelRun::getCreateTime).last("LIMIT " + count);
  //
  //    List<ModelRun> modelRuns = list(queryWrapper);
  //    return modelRuns.stream().map(modelRunMapper::toDto).collect(Collectors.toList());
  //  }

  @Override
  @Transactional
  public boolean updateRunStatus(String id, EventStatus status) throws ModelRunNotFoundException {
    ModelRun modelRun = getById(id);
    if (modelRun == null) {
      log.warn("Model run not found with ID: {}", id);
      throw new ModelRunNotFoundException("ID为" + id + "的事件不存在");
    }

    modelRun.setStatus(status);
    boolean updated = updateById(modelRun);
    if (updated) {
      log.info("Successfully updated status of event {} to {}", id, status);
    } else {
      log.warn("Failed to update status of event {} to {}", id, status);
    }
    return updated;
  }
}
