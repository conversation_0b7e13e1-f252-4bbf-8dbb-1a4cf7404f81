package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.Getter;

@Getter
public enum Operator {
  EQUALS(1, "==", "等于", Arrays.asList("string", "int", "long", "double", "boolean", "timestamp")),
  NOT_EQUALS(
      2, "!=", "不等于", Arrays.asList("string", "int", "long", "double", "boolean", "timestamp")),
  GREATER_THAN(3, ">", "大于", Arrays.asList("int", "long", "double", "timestamp")),
  GREATER_THAN_OR_EQUAL(4, "大于等于", "≥", Arrays.asList("int", "long", "double", "timestamp")),
  LESS_THAN(5, "<", "小于", Arrays.asList("int", "long", "double", "timestamp")),
  LESS_THAN_OR_EQUAL(6, "小于等于", "≤", Arrays.asList("int", "long", "double", "timestamp")),
  CONTAINS(7, "CONTAINS", "包含", Collections.singletonList("string"));

  @EnumValue private final int code;
  private final String name;
  private final String desc;
  private final List<String> supportedTypes;

  @JsonValue
  public int getCode() {
    return code;
  }

  @JsonCreator
  public static Operator fromCode(int code) {
    for (Operator operator : values()) {
      if (operator.code == code) {
        return operator;
      }
    }
    throw new IllegalArgumentException("Invalid operator code: " + code);
  }

  Operator(int code, String name, String desc, List<String> supportedTypes) {
    this.code = code;
    this.name = name;
    this.desc = desc;
    this.supportedTypes = supportedTypes;
  }

  public boolean supportsType(String type) {
    return supportedTypes.contains(type);
  }
}
