package com.geeksec.panorama.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@TableName("schedule_task")
@Data
public class ScheduleTask {
    @TableId(type= IdType.ASSIGN_UUID)
    private String taskId;
    private String taskName;
    private String modelId;
    private String executionType;
    private String intervalUnit;
    private Integer intervalValue;
    private String cronExpression;
    private String taskStatus = "RUNNING";
    private LocalDateTime createTime = LocalDateTime.now();
    private LocalDateTime updateTime = LocalDateTime.now();
    private LocalDateTime lastRunTime = LocalDateTime.now();
    private String createdBy;
}
