package com.geeksec.panorama.config;

import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.SessionPool;
import com.vesoft.nebula.client.graph.SessionPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import java.util.Collections;
import java.util.List;
import org.nebula.contrib.ngbatis.config.NgbatisConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class NebulaGraphConfig {

  @Value("${nebula.hosts}")
  private String nebulaHosts;

  @Value("${nebula.username}")
  private String nebulaUsername;

  @Value("${nebula.password}")
  private String nebulaPassword;

  @Value("${nebula.space}")
  private String nebulaSpaceName;

  @Bean
  public NgbatisConfig ngbatisConfig() {
    NgbatisConfig config = new NgbatisConfig();
    config.setSessionLifeLength(30 * 60 * 1000L);
    config.setCheckFixedRate(5 * 60 * 1000L);
    config.setUseSessionPool(true);
    return config;
  }

  @Bean
  public NebulaPoolConfig nebulaPoolConfig() {
    NebulaPoolConfig poolConfig = new NebulaPoolConfig();
    poolConfig.setMaxConnSize(10);
    poolConfig.setMinConnSize(5);
    poolConfig.setIdleTime(30 * 60 * 1000);
    poolConfig.setIntervalIdle(5 * 60 * 1000);
    poolConfig.setWaitTime(30 * 1000);
    return poolConfig;
  }

  @Bean
  public List<HostAddress> nebulaHosts() {
    return parseHostAddresses(nebulaHosts);
  }

  @Bean
  public SessionPool sessionPool(List<HostAddress> nebulaHosts, NebulaPoolConfig nebulaPoolConfig) {
    SessionPoolConfig sessionPoolConfig =
        new SessionPoolConfig(nebulaHosts, nebulaSpaceName, nebulaUsername, nebulaPassword);

    sessionPoolConfig
        .setMaxSessionSize(nebulaPoolConfig.getMaxConnSize())
        .setMinSessionSize(nebulaPoolConfig.getMinConnSize())
        .setRetryConnectTimes(3)
        .setWaitTime(nebulaPoolConfig.getWaitTime())
        .setRetryTimes(1)
        .setIntervalTime(0);

    return new SessionPool(sessionPoolConfig);
  }

  private List<HostAddress> parseHostAddresses(String hosts) {
    String[] hostParts = hosts.split(":");
    if (hostParts.length != 2) {
      throw new IllegalArgumentException(
          "Invalid Nebula Graph host format. Expected format: host:port");
    }
    String host = hostParts[0];
    int port = Integer.parseInt(hostParts[1]);
    return Collections.singletonList(new HostAddress(host, port));
  }
}
