package com.geeksec.panorama.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geeksec.panorama.dao.AttackEventDao;
import com.geeksec.panorama.entity.AttackEvent;
import com.geeksec.panorama.enums.*;
import com.geeksec.panorama.exception.GkException;
import com.geeksec.panorama.service.AttackEventService;
import java.util.List;
import java.util.Optional;
import javax.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author: jerryzhou
 * @date: 2024/8/22 16:27 @Description:
 */
@Slf4j
@Service
public class AttackEventServiceImpl extends ServiceImpl<AttackEventDao, AttackEvent>
    implements AttackEventService {

  @Override
  @Transactional
  public void createEvent(AttackEvent attackEvent) {
    log.info("Creating graph model event result :{}", attackEvent);

    if (!save(attackEvent)) {
      throw new GkException(GkError.LOGOUT_ERROR);
    }
  }

  @Override
  public Optional<AttackEvent> getFirstEvent(String runId) {
    return lambdaQuery()
        .eq(AttackEvent::getRunId, runId)
        .orderByAsc(AttackEvent::getId)
        .last("LIMIT 1")
        .oneOpt();
  }

  @Override
  public Optional<AttackEvent> getNextEvent(String runId, String currentId) {
    log.info("Querying next event after ID: {}", currentId);

    return lambdaQuery()
        .eq(AttackEvent::getRunId, runId)
        .gt(AttackEvent::getId, currentId)
        .orderByAsc(AttackEvent::getId)
        .last("LIMIT 1")
        .oneOpt();
  }

  @Override
  public AttackEvent getEventById(String eventId) {
    log.info("Querying graph model event result by eventId :{}", eventId);
    return getById(eventId);
  }

  @Override
  public long countEventsByRun(String runId) {
    return lambdaQuery().eq(AttackEvent::getRunId, runId).count();
  }

  @Override
  public List<AttackEvent> getEventsByRunIdUpToEventId(String runId, String eventId) {
    return lambdaQuery()
        .eq(AttackEvent::getRunId, runId)
        .le(AttackEvent::getId, eventId)
        .orderByAsc(AttackEvent::getId)
        .list();
  }
}
