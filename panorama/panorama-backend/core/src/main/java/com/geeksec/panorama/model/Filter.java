package com.geeksec.panorama.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.panorama.enums.Operator;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Filter {
  @JsonProperty("property")
  private String property;

  @JsonProperty("operator")
  private Operator operator;

  @JsonProperty("value")
  private Object value;
}
