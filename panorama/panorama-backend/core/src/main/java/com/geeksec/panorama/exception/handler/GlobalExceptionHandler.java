package com.geeksec.panorama.exception.handler;

import cn.hutool.core.util.StrUtil;
import com.geeksec.panorama.entity.Result;
import com.geeksec.panorama.exception.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

  @ExceptionHandler(BaseException.class)
  public ResponseEntity<Result<Void>> handleBaseException(BaseException ex) {
    log.error("A business error occurred: ", ex);
    return ResponseEntity.status(HttpStatus.OK)
        .body(Result.fail(ex.getErrorCode(), ex.getMessage()));
  }

  @ExceptionHandler(GkException.class)
  public ResponseEntity handleGkException(GkException ex) {
    log.error("A GK error occurred: ", ex);
    if (StrUtil.isNotEmpty(ex.getUrl())){
      return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
              .body(Result.authFailReturnUrl(ex.getUrl()));
    }
    return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
            .body(Result.fail(ex.getCode(), ex.getMessage()));
  }

  @ExceptionHandler({
    ModelNotFoundException.class,
    ModelRunNotFoundException.class,
    EventNotFoundException.class
  })
  public ResponseEntity<Result<Void>> handleModelNotFoundException(BaseException ex) {
    log.error("Resource not found: ", ex);
    return ResponseEntity.status(HttpStatus.NOT_FOUND)
        .body(Result.fail(ex.getErrorCode(), ex.getMessage()));
  }

  @ExceptionHandler(MethodArgumentNotValidException.class)
  public ResponseEntity<Result<Void>> handleValidationExceptions(
      MethodArgumentNotValidException ex) {
    log.error("Validation error: ", ex);
    String errorMessage = ex.getBindingResult().getAllErrors().get(0).getDefaultMessage();
    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
        .body(Result.fail(HttpStatus.BAD_REQUEST.value(), errorMessage));
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<Result<Void>> handleGenericException(Exception ex) {
    log.error("An unexpected error occurred: ", ex);
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
        .body(
            Result.fail(HttpStatus.INTERNAL_SERVER_ERROR.value(), "An unexpected error occurred"));
  }
}
