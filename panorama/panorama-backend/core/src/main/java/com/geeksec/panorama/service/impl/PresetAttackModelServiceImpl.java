package com.geeksec.panorama.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.geeksec.panorama.dao.PresetAttackModelDao;
import com.geeksec.panorama.entity.PresetAttackModel;
import com.geeksec.panorama.exception.ModelNotFoundException;
import com.geeksec.panorama.exception.ModelUpdateException;
import com.geeksec.panorama.exception.SchedulerUpdateException;
import com.geeksec.panorama.mapper.PresetAttackModelMapper;
import com.geeksec.panorama.model.PageResult;
import com.geeksec.panorama.model.PresetAttackModelDto;
import com.geeksec.panorama.model.PresetModelUpdateDto;
import com.geeksec.panorama.service.PresetAttackModelService;
import com.geeksec.panorama.service.SchedulerService;
import java.util.List;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.quartz.SchedulerException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> @Description：
 */
@Slf4j
@Service
public class PresetAttackModelServiceImpl
    extends ServiceImpl<PresetAttackModelDao, PresetAttackModel>
    implements PresetAttackModelService {
  private final SchedulerService schedulerService;
  private final PresetAttackModelMapper modelMapper;

  @Autowired
  public PresetAttackModelServiceImpl(
      SchedulerService schedulerService, PresetAttackModelMapper modelMapper) {
    this.schedulerService = schedulerService;
    this.modelMapper = modelMapper;
  }

  @PostConstruct
  public void initializeScheduler() {
    log.info("Initializing scheduler with active preset event models");
    List<PresetAttackModel> activeModels = getActiveModels();
    scheduleActiveModels(activeModels);
    log.info("Finished initializing scheduler with {} active preset models", activeModels.size());
  }

  private List<PresetAttackModel> getActiveModels() {
    return list(new LambdaQueryWrapper<PresetAttackModel>().eq(PresetAttackModel::isActive, true));
  }

  private void scheduleActiveModels(List<PresetAttackModel> activeModels) {
    for (PresetAttackModel model : activeModels) {
      try {
        scheduleModel(model);
      } catch (SchedulerException e) {
        log.error("Failed to schedule job for preset model: {}", model.getId(), e);
      }
    }
  }

  private void scheduleModel(PresetAttackModel model) throws SchedulerException {
    PresetAttackModelDto modelDto = modelMapper.toDto(model);
    schedulerService.scheduleJob(modelDto);
    log.info("Scheduled job for active preset model: {}", model.getId());
  }

  @Override
  public PageResult<PresetAttackModel> getPresetModelPage(int page, int pageSize) {
    log.info("Fetching preset attack model list. Page: {}, Size: {}", page, pageSize);
    Page<PresetAttackModel> pageRequest = new Page<>(page, pageSize);
    IPage<PresetAttackModel> modelPage =
        page(
            pageRequest,
            new LambdaQueryWrapper<PresetAttackModel>().orderByAsc(PresetAttackModel::getId));
    return new PageResult<>(modelPage);
  }

  @Transactional
  @Override
  public void updatePresetModel(String id, PresetModelUpdateDto updateDto) {
    log.info("Updating preset model. ID: {}", id);
    PresetAttackModel modelEntity = getPresetModelById(id);
    updateModelEntity(modelEntity, updateDto);
    if (modelEntity.isActive()) {
      reschedule(modelEntity);
    }
  }

  private PresetAttackModel getPresetModelById(String id) {
    PresetAttackModel modelEntity = getById(id);
    if (modelEntity == null) {
      throw new ModelNotFoundException("ID为" + id + "的预置模型不存在。");
    }
    return modelEntity;
  }

  private void updateModelEntity(PresetAttackModel modelEntity, PresetModelUpdateDto updateDto) {
    modelEntity.setParams(updateDto.getParams());
    modelEntity.setInterval(updateDto.getInterval());
    modelEntity.setUnit(updateDto.getUnit());
    modelEntity.setType(updateDto.getType());

    if (!updateById(modelEntity)) {
      throw new ModelUpdateException("更新预置模型\"" + modelEntity.getName() + "\"失败。");
    }
  }

  private void reschedule(PresetAttackModel modelEntity) {
    try {
      PresetAttackModelDto modelDto = modelMapper.toDto(modelEntity);
      schedulerService.updateJobSchedule(modelDto);
    } catch (SchedulerException e) {
      throw new SchedulerUpdateException("修改预置模型\"" + modelEntity.getName() + "\"执行间隔失败。");
    }
  }

  @Transactional
  @Override
  public void toggleModelStatus(String id, boolean active) {
    log.info("Toggling preset model status. ID: {}, Active: {}", id, active);
    PresetAttackModel model = getPresetModelById(id);
    updateModelStatus(model, active);
    updateSchedulerStatus(model, active);
    log.info("Successfully toggled preset model status and updated scheduler. ID: {}", id);
  }

  private void updateModelStatus(PresetAttackModel model, boolean active) {
    model.setActive(active);
    boolean updated = updateById(model);
    if (!updated) {
      throw new ModelUpdateException("修改预置模型\"" + model.getName() + "\"状态失败。");
    }
  }

  private void updateSchedulerStatus(PresetAttackModel model, boolean active) {
    try {
      if (active) {
        activateModelInScheduler(model);
      } else {
        schedulerService.pauseJob(model.getId());
      }
    } catch (SchedulerException e) {
      throw new SchedulerUpdateException("切换预置模型\"" + model.getName() + "\"状态失败。");
    }
  }

  private void activateModelInScheduler(PresetAttackModel model) throws SchedulerException {
    if (!schedulerService.checkJobExists(model.getId())) {
      PresetAttackModelDto modelDto = modelMapper.toDto(model);
      schedulerService.scheduleJob(modelDto);
    } else {
      schedulerService.resumeJob(model.getId());
    }
  }
}
