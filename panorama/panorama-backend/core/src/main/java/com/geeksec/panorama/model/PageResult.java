package com.geeksec.panorama.model;

import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import lombok.Getter;

@Getter
public class PageResult<T> {
  private final List<T> data;
  private final long total;
  private final long current;
  private final long size;
  private final long pages;

  public PageResult(IPage<T> page) {
    this.data = page.getRecords();
    this.total = page.getTotal();
    this.current = page.getCurrent();
    this.size = page.getSize();
    this.pages = page.getPages();
  }
}
