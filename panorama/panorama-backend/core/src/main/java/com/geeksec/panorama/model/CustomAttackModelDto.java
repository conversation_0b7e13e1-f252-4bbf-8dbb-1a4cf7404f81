package com.geeksec.panorama.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.panorama.enums.AttackType;
import com.geeksec.panorama.enums.ClassType;
import com.geeksec.panorama.enums.Severity;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * @author: hufeng<PERSON>
 * @date: 2024/7/31 09:33 @Description:
 */
@Data
public class CustomAttackModelDto {

  /** 模型ID */
  @JsonProperty("id")
  private String id;

  /** 模型名称 */
  @JsonProperty("name")
  private String name;

  /** 事件严重等级 */
  @JsonProperty("severity")
  private Severity severity;

  /** 事件分类 */
  @JsonProperty("type")
  private ClassType type;

  /** 模型描述 */
  @JsonProperty("description")
  private String description;

  /** 图模式 */
  @JsonProperty("pattern")
  private GraphPattern pattern;

  @JsonProperty("ngql")
  private String nGQL;

  /** 模型运行状态 */
  @JsonProperty("is_running")
  private boolean isRunning;

  /** 创建者 */
  @JsonProperty("creator")
  private String creator;

  /** 最后运行时间 */
  @JsonProperty("last_run_time")
  private LocalDateTime lastRunTime;
}
