package com.geeksec.panorama.service.impl;

import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.service.JobCreationStrategy;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.quartz.CronScheduleBuilder;
import org.springframework.stereotype.Component;

import static com.geeksec.panorama.service.impl.ScheduleTaskServiceImpl.getJobDetail;
import static org.quartz.TriggerBuilder.newTrigger;

@Component
public class CronJobStrategy implements JobCreationStrategy {
    @Override
    public JobDetail createJobDetail(ScheduleTask scheduleTask) {
        return getJobDetail(scheduleTask);
    }

    @Override
    public Trigger createTrigger(ScheduleTask scheduleTask) {
        return newTrigger()
                .withIdentity(scheduleTask.getTaskId()+"_"+scheduleTask.getExecutionType()+"_"+scheduleTask.getModelId())
                .withSchedule(CronScheduleBuilder.cronSchedule(scheduleTask.getCronExpression())) // 动态设置 Cron 表达式
                .build();
    }
}