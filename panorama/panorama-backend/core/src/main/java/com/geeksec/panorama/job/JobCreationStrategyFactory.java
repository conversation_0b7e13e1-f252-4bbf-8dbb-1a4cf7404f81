package com.geeksec.panorama.job;

import com.geeksec.panorama.enums.JobType;
import com.geeksec.panorama.service.JobCreationStrategy;
import com.geeksec.panorama.service.impl.CronJobStrategy;
import com.geeksec.panorama.service.impl.IntervalJobStrategy;
import com.geeksec.panorama.service.impl.SimpleJobStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
public class JobCreationStrategyFactory {
    private static final Map<JobType, JobCreationStrategy> strategies = new HashMap<>();

    @Autowired
    public JobCreationStrategyFactory(SimpleJobStrategy simpleJobStrategy,
                                      CronJobStrategy cronJobStrategy,
                                      IntervalJobStrategy intervalJobStrategy) {
        strategies.put(JobType.SINGLE, simpleJobStrategy);
        strategies.put(JobType.SCHEDULED, cronJobStrategy);
        strategies.put(JobType.LOOP, intervalJobStrategy);
    }

    public JobCreationStrategy getStrategy(JobType jobType) {
        return strategies.get(jobType);
    }
}
