package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Data;
import lombok.Getter;

/**
 * @author: jerryzhou
 * @date: 2024/9/6 14:42
 * @Description:
 **/
@Getter
public enum ClassType {

    BACKDOOR_EXPLOITATION(1,"后门利用"),
    REMOTE_CONTROL(2,"远程控制"),
    RANSOMWARE(3,"勒索软件"),
    INTRUSION_INFILTRATION(4,"其他渗透入侵事件"),
    MINING_VIRUS(5,"挖矿病毒");

    @EnumValue final int code;

    private final String className;

    ClassType(int code, String className) {
        this.code = code;
        this.className = className;
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    @JsonCreator
    public static ClassType fromCode(int code) {
        for (ClassType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid ClassType.java code: " + code);
    }

}
