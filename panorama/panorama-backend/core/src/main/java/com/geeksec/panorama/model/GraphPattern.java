package com.geeksec.panorama.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.panorama.entity.PropertyHolder;
import com.geeksec.panorama.enums.VertexType;
import java.util.*;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Data
public class GraphPattern {

  // MATCH查询模板
  private static final String MATCH_CLAUSE_TEMPLATE = "MATCH (%s:%s)-[%s:%s]->(%s:%s)";
  // 条件模板
  private static final String FILTER_TEMPLATE = "%s.%s %s %s";

  @JsonProperty("vertices")
  private List<Vertex> vertices;

  @JsonProperty("edges")
  private List<Edge> edges;

  @JsonProperty("limit")
  private Integer limit;

  /**
   * 将GraphPattern转化为nGQL语句
   *
   * @return
   */
  public String toNGQL() {
    StringBuilder queryBuilder = new StringBuilder();

    Map<String, Vertex> vertexMap =
        vertices.stream().collect(Collectors.toMap(Vertex::getId, v -> v));

    for (Edge edge : edges) {
      Vertex srcVertex =
          Optional.ofNullable(vertexMap.get(edge.getSrcId()))
              .orElseThrow(() -> new IllegalStateException("Source vertex not found"));
      Vertex dstVertex =
          Optional.ofNullable(vertexMap.get(edge.getDstId()))
              .orElseThrow(() -> new IllegalStateException("Destination vertex not found"));

      queryBuilder.append(
          String.format(
              MATCH_CLAUSE_TEMPLATE,
              srcVertex.getId(),
              srcVertex.getType().getName(),
              edge.getId(),
              edge.getType().getName(),
              dstVertex.getId(),
              dstVertex.getType().getName()));

      List<String> filterClauses = new ArrayList<>();
      addFilters(filterClauses, srcVertex);
      addFilters(filterClauses, dstVertex);
      addFilters(filterClauses, edge);

      if (!filterClauses.isEmpty()) {
        queryBuilder.append(" WHERE ").append(String.join(" AND ", filterClauses));
      }

      queryBuilder.append(" ");
    }

    queryBuilder.append("RETURN ");
    StringJoiner returnJoiner = new StringJoiner(", ");
    vertexMap
        .keySet()
        .forEach(vertexKey -> returnJoiner.add(vertexKey + ", id(" + vertexKey + ")"));
    edges.forEach(edge -> returnJoiner.add(edge.getId() + ", properties(" + edge.getId() + ")"));
    queryBuilder.append(returnJoiner).append(" LIMIT ").append(limit);

    return queryBuilder.toString().trim();
  }

  private void addFilters(List<String> filterClauses, Filterable element) {
    if (element.getFilters() != null) {
      element.getFilters().stream()
          .map(filter -> formatFilter(element.getId(), element.getType(), filter))
          .forEach(filterClauses::add);
    }
  }

  private String formatFilter(String elementId, PropertyHolder elementType, Filter filter) {
    String propertyName =
        elementType instanceof VertexType
            ? elementType.getName() + "." + filter.getProperty()
            : filter.getProperty();

    return String.format(
        FILTER_TEMPLATE,
        elementId,
        propertyName,
        filter.getOperator().getName(),
        formatFilterValue(elementType, filter));
  }

  private String formatFilterValue(PropertyHolder propertyHolder, Filter filter) {
    return isPropertyString(propertyHolder, filter.getProperty())
        ? "'" + filter.getValue() + "'"
        : String.valueOf(filter.getValue());
  }

  private boolean isPropertyString(PropertyHolder propertyHolder, String property) {
    return propertyHolder.getProperties().stream()
        .filter(p -> p.getKey().equals(property))
        .findFirst()
        .map(p -> p.getType().equals("string"))
        .orElse(false);
  }
}
