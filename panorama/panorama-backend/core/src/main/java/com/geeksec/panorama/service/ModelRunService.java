package com.geeksec.panorama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geeksec.ngbatis.vo.VertexAssociationEdgeVo;
import com.geeksec.panorama.entity.ModelRun;
import com.geeksec.panorama.enums.EdgeType;
import com.geeksec.panorama.enums.EventStatus;
import com.geeksec.panorama.exception.EventNotFoundException;
import com.geeksec.panorama.model.ModelRunVo;
import java.util.List;

public interface ModelRunService extends IService<ModelRun> {

  /**
   * 获取模型运行记录
   *
   * @param id
   * @return
   */
  ModelRunVo getRunById(String id);

  List<VertexAssociationEdgeVo> getEdgesBetweenVertices(
      String runId, String endEventId, String fromVertexId, String toVertexId, EdgeType edgeType);

  /**
   * 更新事件状态
   *
   * @param eventId
   * @param status
   * @return
   * @throws EventNotFoundException
   */
  boolean updateRunStatus(String eventId, EventStatus status) throws EventNotFoundException;
}
