package com.geeksec.panorama.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.geeksec.panorama.entity.handler.JsonbTypeHandler;
import com.geeksec.panorama.enums.TimeUnit;
import com.geeksec.panorama.model.ModelParameter;
import java.util.List;
import lombok.Data;

/** 预置事件模型实体类 */
@TableName(value = "preset_attack_model", autoResultMap = true)
@Data
public class PresetAttackModel extends BaseAttackModel {
  @TableField(exist = false)
  private static final long serialVersionUID = 1L;

  /** 激活状态 */
  private boolean active;

  /** 执行间隔 */
  private int interval;

  /** 间隔单位 */
  private TimeUnit unit;

  /** 模型参数 */
  @TableField(typeHandler = JsonbTypeHandler.class)
  private List<ModelParameter> params;
}
