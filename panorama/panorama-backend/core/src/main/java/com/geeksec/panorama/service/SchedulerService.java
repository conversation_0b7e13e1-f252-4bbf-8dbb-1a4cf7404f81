package com.geeksec.panorama.service;

import com.geeksec.panorama.enums.TimeUnit;
import com.geeksec.panorama.job.PresetAttackModelJob;
import com.geeksec.panorama.model.PresetAttackModelDto;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SchedulerService {
  private final Scheduler scheduler;

  @Autowired
  public SchedulerService(Scheduler scheduler) {
    this.scheduler = scheduler;
  }

  public void scheduleJob(PresetAttackModelDto model) throws SchedulerException {
    JobDetail jobDetail = buildJobDetail(model);
    Trigger trigger = buildTrigger(model);
    scheduler.scheduleJob(jobDetail, trigger);
  }

  private JobDetail buildJobDetail(PresetAttackModelDto model) {
    return JobBuilder.newJob(PresetAttackModelJob.class)
        .withIdentity(model.getId())
        .usingJobData("modelId", model.getId())
        .build();
  }

  private Trigger buildTrigger(PresetAttackModelDto model) {
    return TriggerBuilder.newTrigger()
        .withIdentity(model.getId())
        .withSchedule(
            SimpleScheduleBuilder.simpleSchedule()
                .withIntervalInSeconds(convertToSeconds(model.getInterval(), model.getUnit()))
                .repeatForever())
        .build();
  }

  public boolean checkJobExists(String modelId) throws SchedulerException {
    JobKey jobKey = JobKey.jobKey(modelId);
    return scheduler.checkExists(jobKey);
  }

  public void resumeJob(String modelId) throws SchedulerException {
    JobKey jobKey = JobKey.jobKey(modelId);
    scheduler.resumeJob(jobKey);
  }

  public void pauseJob(String modelId) throws SchedulerException {
    JobKey jobKey = JobKey.jobKey(modelId);
    scheduler.pauseJob(jobKey);
  }

  public void updateJobSchedule(PresetAttackModelDto model) throws SchedulerException {
    if (!model.isActive()) {
      log.info("Model {} is not active. Skipping schedule update.", model.getId());
      return;
    }

    TriggerKey triggerKey = TriggerKey.triggerKey(model.getId());
    Trigger oldTrigger = scheduler.getTrigger(triggerKey);

    if (oldTrigger == null) {
      log.warn("No existing trigger found for model {}. Scheduling new job.", model.getId());
      scheduleJob(model);
    } else {
      log.info(
          "Updating schedule for model {} with new interval: {} {}",
          model.getId(),
          model.getInterval(),
          model.getUnit());
      Trigger newTrigger = buildTrigger(model);
      scheduler.rescheduleJob(triggerKey, newTrigger);
    }
  }

  private int convertToSeconds(int interval, TimeUnit unit) {
    switch (unit) {
      case SECOND:
        return interval;
      case MINUTE:
        return interval * 60;
      case HOUR:
        return interval * 3600;
      case DAY:
        return interval * 86400;
      default:
        throw new IllegalArgumentException("Unsupported time unit: " + unit);
    }
  }
}
