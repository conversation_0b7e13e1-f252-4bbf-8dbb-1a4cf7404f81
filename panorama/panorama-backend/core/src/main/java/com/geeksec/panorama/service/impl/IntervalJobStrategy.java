package com.geeksec.panorama.service.impl;

import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.enums.GkError;
import com.geeksec.panorama.exception.TaskFailedException;
import com.geeksec.panorama.service.JobCreationStrategy;
import org.quartz.JobDetail;
import org.quartz.Trigger;
import org.quartz.SimpleScheduleBuilder;
import org.springframework.stereotype.Component;

import static com.geeksec.panorama.service.impl.ScheduleTaskServiceImpl.getJobDetail;
import static org.quartz.TriggerBuilder.newTrigger;

@Component
public class IntervalJobStrategy implements JobCreationStrategy {
    @Override
    public JobDetail createJobDetail(ScheduleTask scheduleTask) {
        return getJobDetail(scheduleTask);
    }

    @Override
    public Trigger createTrigger(ScheduleTask scheduleTask) {
        String timeUnit = scheduleTask.getIntervalUnit();
        Integer intervalValue = scheduleTask.getIntervalValue();
        // 根据时间单位创建 Trigger
        SimpleScheduleBuilder scheduleBuilder = SimpleScheduleBuilder.simpleSchedule()
                .withIntervalInSeconds(1) // 默认值，后续会根据时间单位覆盖
                .repeatForever();

        switch (timeUnit) {
            case "DAY":
                scheduleBuilder.withIntervalInHours(intervalValue*24);
                break;
            case "MINUTE":
                scheduleBuilder.withIntervalInMinutes(intervalValue);
                break;
            case "HOUR":
                scheduleBuilder.withIntervalInHours(intervalValue);
                break;
            default:
                throw new TaskFailedException(GkError.UNKNOWN_TIME_UNIT);
        }

        return newTrigger()
                .withIdentity(scheduleTask.getTaskId()+"_"+scheduleTask.getExecutionType()+"_"+scheduleTask.getModelId())
                .withSchedule(scheduleBuilder)
                .build();
    }
}
