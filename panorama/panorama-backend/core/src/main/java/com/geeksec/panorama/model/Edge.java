package com.geeksec.panorama.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.panorama.enums.EdgeType;
import com.geeksec.panorama.enums.VertexType;
import java.util.List;
import lombok.Data;

@Data
public class Edge implements Filterable {
  @JsonProperty("id")
  private String id;

  @JsonProperty("srcId")
  private String srcId;

  @JsonProperty("srcType")
  private VertexType srcType;

  @JsonProperty("dstId")
  private String dstId;

  @JsonProperty("dstType")
  private VertexType dstType;

  @JsonProperty("type")
  private EdgeType type;

  @JsonProperty("filters")
  private List<Filter> filters;
}
