package com.geeksec.panorama.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.geeksec.panorama.entity.handler.JsonbTypeHandler;
import com.geeksec.panorama.enums.EventStatus;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import lombok.Data;

/** 模型运行记录实体类 */
@Data
@TableName("model_run")
public class ModelRun {

  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  /** 关联的模型ID */
  private String modelId;

  /** 模型版本号 */
  private Integer modelVersion;

  /** 模型类型（预置/自定义） */
  private int modelType;

  /** 状态：0-已删除，1-未处置，2-已忽略，3-已处置，4-重点关注 */
  private EventStatus status;

  /** 涉及的告警日志总数 */
  private Integer alarmCount;

  /** 独立受害者IP数量 */
  private Integer uniqueVictimCount;

  /** 各数据源的告警日志数量分布 格式: [{"sourceId1": count1, "sourceId2": count2, ...}] */
  @TableField(typeHandler = JsonbTypeHandler.class)
  private List<Map<String, Object>> sourceDistribution;

  /** 发现的最早攻击事件时间 */
  private LocalDateTime earliestEventTime;

  /** 发现的最晚攻击事件时间 */
  private LocalDateTime latestEventTime;

  /** 运行时间 */
  private LocalDateTime createTime;

  /** 运行任务id */
  private String taskId;
}
