package com.geeksec.panorama.mapper;

import com.geeksec.panorama.entity.AttackEvent;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

/**
 * @author: jerryzhou
 * @date: 2024/8/22 16:33
 * @Description:
 **/
@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AttackEventMapper {

    AttackEvent toEntity(AttackEvent result);

}
