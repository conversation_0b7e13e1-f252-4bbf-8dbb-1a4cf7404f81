package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.*;
import lombok.Getter;
import org.apache.commons.lang.math.NumberUtils;

/** 威胁类型（attack_type）/ 事件类型枚举 */
public enum AttackType {

  /** 1 APT事件 */
  APT(256, -1, "APT事件"),
  APT_EVENT(257, 256, "APT攻击"),
  APT_CUSTOM(510, 256, "APT事件用户自定义"),
  APT_OTHER(511, 256, "其他APT类型"),

  /** 2 侦察 */
  RECONNAISSANCE(512, -1, "侦察"),
  PORT_SCAN(513, 512, "端口扫描"),
  NETWORK_SCAN(514, 512, "网络扫描"),
  CUSTOM(766, 512, "侦察用户自定义"),
  RECONNAISSANCE_OTHER(767, 512, "其他侦察类型"),

  /** 3 拒绝服务 */
  DENIAL_SERVICE(768, -1, "拒绝服务"),
  SYN_FLOOD(769, 768, "SYN Flood"),
  ACK_FLOOD(770, 768, "ACK Flood"),
  HTTP_FLOOD(771, 768, "HTTP Flood"),
  UDP_FLOOD(772, 768, "UDP Flood"),
  DNS_FLOOD(773, 768, "DNS Flood"),
  DENIAL_SERVICE_CUSTOM(1022, 768, "拒绝服务用户自定义"),
  OTHER_DENIAL_OF_SERVICE(1023, 768, "其他拒绝服务"),

  /** 4 攻击利用 */
  ATTACK_UTILIZATION(1024, -1, "攻击利用"),
  SQL_INJECTION(1025, 1024, "SQL注入"),
  URL_JUMP(1026, 1024, "URL跳转"),
  CODE_EXECUTION(1027, 1024, "代码执行"),
  UNAUTHORIZED_ACCESS(1028, 1024, "非授权访问/权限绕过"),
  XSS(1029, 1024, "跨站脚本攻击XSS"),
  CSRF(1030, 1024, "跨站请求伪造CSRF"),
  LOGIC_ERROR(1031, 1024, "逻辑/设计错误"),
  COMMAND_EXECUTION(1032, 1024, "命令执行"),
  DIRECTORY_TRAVERSAL(1033, 1024, "目录遍历"),
  CONFIGURATION_ERROR(1034, 1024, "配置不当/错误"),
  WEAK_PASSWORD(1035, 1024, "弱口令"),
  FILE_CONTAINS(1036, 1024, "文件包含"),
  FILE_READ(1037, 1024, "文件读取"),
  FILE_UPLOAD(1038, 1024, "文件上传"),
  DOCUMENT_DOWNLOAD(1039, 1024, "文件下载"),
  FILE_WRITE(1040, 1024, "文件写入"),
  OVERFLOW_ATTACK(1041, 1024, "溢出攻击"),
  INFORMATION_DISCLOSURE(1042, 1024, "信息泄露"),
  BROWSER_HIJACKING(1043, 1024, "浏览器劫持"),
  VIOLENT_GUESS(1044, 1024, "暴力猜解"),
  PHISHING(1045, 1024, "网络钓鱼"),
  WEBSHELL_UPLOAD(1046, 1024, "webshell上传"),
  BACK_DOOR(1047, 1024, "后门程序"),
  CONCEALED_TUNNEL(1048, 1024, "隐蔽隧道"),
  PROXY_TOOL(1049, 1024, "代理工具"),
  SUSPICIOUS_FILE_NAME(1050, 1024, "可疑文件名"),
  ABNORMAL_LOGIN(1051, 1024, "异常登录"),
  OS_COMMAND_INJECTION(1052, 1024, "OS命令注入"),
  WEBSHELL_UTILIZATION(1053, 1024, "WebShell利用"),
  PROTOCOL_EXCEPTION(1054, 1024, "协议异常"),
  ATTACK_UTILIZATION_CUSTOM(1278, 1024, "攻击利用用户自定义"),
  ATTACK_UTILIZATION_OTHER(1279, 1024, "其他攻击利用"),

  /** 恶意域名 */
  DNS_ALERT(1280, -1, "恶意域名"),
  DNS_DGA_DOMAIN(1281, 1280, "DGA静态域名"),
  DNS_DGA_RULE(1282, 1280, "DGA动态规则"),
  DNS_FAST_FLUX(1283, 1280, "FastFlux"),
  DNS_REBIND(1284, 1280, "DNS重绑定"),
  DNS_REFLECT(1285, 1280, "DNS反射放大攻击"),
  DNS_IDN(1286, 1280, "IDN可疑域名"),
  DNS_SINKHOLE(1287, 1280, "Sinkhole"),
  DNS_RING_ADDRESS(1288, 1280, "DNS隐蔽隧道"),
  DNS_SUS_DYN_DOMAIN(1289, 1280, "环路地址"),
  DNS_SUSPICIOUS_DYNAMIC_DOMAIN(1290, 1280, "可疑动态域名"),
  DNS_HEARTBEAT_DOMAIN(1291, 1280, "心跳域名"),
  DNS_HEART_BEAT_DOMAIN(1534, 1280, "恶意域名用户自定义"),
  DNS_OTHER(1535, 1280, "其他恶意域名"),

  /** 元数据-恶意软件 */
  METADATA_MALICIOUS(1536, -1, "威胁情报"),
  METADATA_BACKDOOR_PROGRAM(1537, 1536, "漏洞利用程序"),
  METADATA_BOTNET(1538, 1536, "僵尸网络"),
  METADATA_TROJAN(1539, 1536, "特洛伊木马"),
  METADATA_VIRUS(1540, 1536, "电脑病毒"),
  METADATA_SPYWARE(1541, 1536, "间谍软件"),
  METADATA_MALICIOUS_ADVERTISING(1542, 1536, "恶意广告"),
  METADATA_REMOTE_CONTROL_TROJAN(1543, 1536, "远控木马"),
  METADATA_KEY_LOGGER(1544, 1536, "键盘记录"),
  METADATA_STEALING_TROJAN(1545, 1536, "窃密木马"),
  METADATA_NETWORK_WORM(1546, 1536, "网络蠕虫"),
  METADATA_RANSOMWARE(1547, 1536, "勒索软件"),
  METADATA_BLACK_MARKET_TOOL(1548, 1536, "黑市工具"),
  METADATA_ROGUE_PROMOTION(1549, 1536, "流氓推广"),
  METADATA_MALICIOUS_DOWNLOAD(1550, 1536, "恶意下载"),
  METADATA_INFECTIOUS_VIRUS(1551, 1536, "感染型病毒"),
  METADATA_MINING_CUSTOM(1552, 1536, "挖矿病毒"),
  METADATA_CUSTOM(1790, 1536, "恶意软件用户自定义"),
  METADATA_MALICIOUS_OTHER(1791, 1536, "其他恶意软件"),

  /** 文件-恶意软件 */
  MALICIOUS(1792, -1, "恶意文件"),
  BACKDOOR_PROGRAM(1793, 1792, "后门程序"),
  KEY_LOGGER(1794, 1792, "键盘记录"),
  TROJAN(1795, 1792, "特洛伊木马"),
  REMOTE_CONTROL_TROJAN(1796, 1792, "远控木马"),
  STEALING_TROJAN(1797, 1792, "窃密木马"),
  VIRUS(1798, 1792, "电脑病毒"),
  BOTNET(1799, 1792, "僵尸网络"),
  NETWORK_WORM(1800, 1792, "网络蠕虫"),
  SPYWARE(1801, 1792, "间谍软件"),
  MALICIOUS_ADVERTISING(1802, 1792, "恶意广告"),
  ROGUE_PROMOTION(1803, 1792, "流氓推广"),
  RANSOMWARE(1804, 1792, "勒索软件"),
  BLACK_MARKET_TOOL(1805, 1792, "黑市工具"),
  MALICIOUS_DOWNLOAD(1806, 1792, "攻击利用套件"),
  INFECTIOUS_VIRUS(1807, 1792, "漏洞利用程序"),
  SUSPECT_MALICIOUS(2045, 1792, "疑似恶意软件"),
  MALICIOUS_CUSTOM(2046, 1792, "恶意软件用户自定义"),
  MALICIOUS_OTHER(2047, 1792, "其他恶意软件");

  private static Map<Integer, String> mapChild2Parent;

  private static final Map<Integer, AttackType> codeEnumMap = new HashMap<>();

  /** key: 父节点 value: 所有的子节点 */
  private static Map<Integer, List<Integer>> parentToChildrenMap = new HashMap<>(16);

  /** 类型编号 */
  @EnumValue private final Integer code;

  /** 父级类型 */
  @Getter private final Integer parent;

  /** 名称 */
  @Getter private final String msg;

  AttackType(Integer code, Integer parent, String msg) {
    this.code = code;
    this.parent = parent;
    this.msg = msg;
  }

  static {
    for (AttackType e : AttackType.values()) {
      codeEnumMap.put(e.getCode(), e);
    }
  }

  static {
    // 所有父节点信息
    List<Integer> parentCode = new ArrayList<>(16);

    for (AttackType e : AttackType.values()) {
      if (e.getParent() == -1) {
        parentCode.add(e.getCode());
      }
    }

    // 提前将 children 引用放入到 parentToChildrenMap
    for (Integer parent : parentCode) {
      List<Integer> children = new ArrayList<>(16);
      parentToChildrenMap.put(parent, children);
    }

    // 再次遍历，填充子节点
    for (AttackType e : AttackType.values()) {
      boolean contains = parentCode.contains(e.getParent());
      if (contains) {
        parentToChildrenMap.get(e.getParent()).add(e.getCode());
      }
    }
  }

  public static Map<Integer, List<Integer>> getParentToChildrenMap() {
    return parentToChildrenMap;
  }

  public static String getMsgByCode(Integer code) {
    AttackType getEnum = codeEnumMap.get(code);
    return getEnum != null ? getEnum.getMsg() : "";
  }

  public static AttackType get(Integer code) {
    return codeEnumMap.get(code);
  }

  public static String getMsgByCode(Set<String> codeSet, String delimiter) {
    Set<String> set = new HashSet<>();
    for (String str : codeSet) {
      Integer code = NumberUtils.createInteger(str);
      set.add(getMsgByCode(code));
    }
    return String.join(delimiter, set);
  }

  public static String getParentFull(Set<String> codeSet, String delimiter) {
    Set<String> set = new HashSet<>();
    for (String str : codeSet) {
      Integer code = NumberUtils.createInteger(str);
      set.add(getParentFull(code));
    }
    return String.join(delimiter, set);
  }

  public static String getParentCodes(Set<String> codeSet, String delimiter) {
    Set<String> set = new HashSet<>();
    for (String str : codeSet) {

      AttackType attackType = AttackType.get(Integer.valueOf(str));
      if (attackType == null) {
        continue;
      }
      if (attackType.getParent() != -1) {
        set.add(String.valueOf(attackType.getParent()));
      } else {
        set.add(str);
      }
    }
    return String.join(delimiter, set);
  }

  public static String getParent(Integer child) {
    if (mapChild2Parent == null) {
      synchronized (AttackType.class) {
        if (mapChild2Parent == null) {
          mapChild2Parent = new HashMap<>(64);
          for (AttackType e : AttackType.values()) {
            Integer pa = e.getParent();
            if (pa == -1) {
              mapChild2Parent.put(e.code, "");
              continue;
            }
            for (AttackType e1 : AttackType.values()) {
              if (e1.getCode().equals(pa)) {
                mapChild2Parent.put(e.code, e1.msg);
              }
            }
          }
        }
      }
    }
    return mapChild2Parent.get(child);
  }

  public static String getParentFull(Integer child) {
    if (mapChild2Parent == null) {
      synchronized (AttackType.class) {
        if (mapChild2Parent == null) {
          mapChild2Parent = new HashMap<>(64);
          for (AttackType e : AttackType.values()) {
            Integer pa = e.getParent();
            if (pa == -1) {
              mapChild2Parent.put(e.code, e.msg);
              continue;
            }
            for (AttackType e1 : AttackType.values()) {
              if (e1.getCode().equals(pa)) {
                mapChild2Parent.put(e.code, e1.msg);
              }
            }
          }
        }
      }
    }
    return mapChild2Parent.get(child);
  }

  public static List<Integer> getSecondLevel(Integer code) {
    List<Integer> secondLevel = new ArrayList<>();
    secondLevel.add(code);
    for (AttackType e : AttackType.values()) {
      if (e.getParent().equals(code)) {
        secondLevel.add(e.getCode());
      }
    }
    return secondLevel;
  }

  /**
   * 枚举查询 根据 code 查询message信息
   *
   * @param code code值
   * @return message
   */
  public static String getEnumMessage(Integer code) {
    AttackType attackType = AttackType.get(code);
    return Objects.isNull(attackType) ? null : attackType.getMsg();
  }

  /**
   * 枚举查询 根据 code 查询父类的message信息 如果该节点已经是1级节点（威胁类型），那么就直接返回该节点信息 如果该节点是二级节点（威胁事件），那么返回它父节点威胁类型的信息
   *
   * @param code code值
   * @return 父类的message信息
   */
  public static String getEnumParentMsg(Integer code) {
    AttackType attackType = AttackType.get(code);
    if (Objects.nonNull(attackType)) {
      Integer parent = attackType.getParent();
      if (parent == -1) {
        return attackType.getMsg();
      }
      AttackType attackTypeParent = AttackType.get(parent);
      return Objects.isNull(attackTypeParent) ? null : attackTypeParent.getMsg();
    }
    return null;
  }

  public static String getMsgFull(Integer code, String delimiter) {
    AttackType attackType = AttackType.get(code);
    if (attackType == null) {
      return "";
    }
    if (attackType.getParent() == -1) {
      return attackType.getMsg();
    }

    AttackType parent = AttackType.get(attackType.getParent());
    return String.join(delimiter, parent.getMsg(), attackType.getMsg());
  }

  public static String getCodeFull(Integer code, String delimiter) {
    AttackType attackType = AttackType.get(code);
    if (attackType == null) {
      return "";
    }
    if (attackType.getParent() == -1) {
      return attackType.getCode().toString();
    }

    AttackType parent = AttackType.get(attackType.getParent());
    return String.join(delimiter, parent.getCode().toString(), attackType.getCode().toString());
  }

  public static List<Integer> getDnsCodes() {
    List<Integer> codes = new ArrayList<>();
    for (AttackType value : AttackType.values()) {
      if (value.toString().startsWith("DNS_")) {
        codes.add(value.getCode());
      }
    }
    return codes;
  }

  /**
   * 获取所有的一级code
   *
   * @return
   */
  public static List<Integer> getFirstLevelCodes() {
    List<Integer> codes = new ArrayList<>();
    for (AttackType value : AttackType.values()) {
      if (value.getParent() == -1) {
        codes.add(value.getCode());
      }
    }
    return codes;
  }

  @JsonValue
  public Integer getCode() {
    return code;
  }

  @JsonCreator
  public static AttackType fromCode(Integer code) {
    AttackType attackType = codeEnumMap.get(code);
    if (attackType == null) {
      throw new IllegalArgumentException("Invalid EventType code: " + code);
    }
    return attackType;
  }
}
