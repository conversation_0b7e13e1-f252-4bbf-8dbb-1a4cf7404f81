package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum EventStatus {
  DELETED(0, "已删除"),
  PENDING(1, "未处置"),
  IGNORED(2, "已忽略"),
  RESOLVED(3, "已处置"),
  FOCUSED(4, "重点关注");

  @EnumValue private final int code;
  private final String desc;

  EventStatus(int code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  @JsonValue
  public int getCode() {
    return code;
  }

  @JsonCreator
  public static EventStatus fromCode(int code) {
    for (EventStatus status : values()) {
      if (status.getCode() == code) {
        return status;
      }
    }
    throw new IllegalArgumentException("Invalid EventStatus code: " + code);
  }
}
