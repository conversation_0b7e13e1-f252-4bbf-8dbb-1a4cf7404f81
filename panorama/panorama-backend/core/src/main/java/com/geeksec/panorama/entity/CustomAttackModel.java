package com.geeksec.panorama.entity;

import com.baomidou.mybatisplus.annotation.*;
import java.time.LocalDateTime;
import lombok.Data;

/** 自定义攻击模型实体类 */
@TableName(value = "custom_attack_model", autoResultMap = true)
@KeySequence(value = "pk_custom_attack_model", dbType = DbType.POSTGRE_SQL)
@Data
public class CustomAttackModel extends BaseAttackModel {

  /** 版本号 */
  @TableField(value = "version")
  private Integer version;

  /** 图模式对应的nGQL语句 */
  private String ngql;

  /** 运行状态 */
  private boolean running;

  /** 创建者 */
  @TableField(value = "created_by")
  private String creator;

  /** 访问权限标识符 */
  private String authTag;

  /** 最近运行时间 */
  private LocalDateTime lastRunTime;

  /** 是否为当前版本 */
  private boolean current;

  /** 是否已删除（逻辑删除） */
  @TableLogic(value = "false", delval = "true")
  private boolean deleted;
}
