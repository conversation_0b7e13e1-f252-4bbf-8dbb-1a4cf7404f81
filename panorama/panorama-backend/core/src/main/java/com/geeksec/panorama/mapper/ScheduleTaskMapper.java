package com.geeksec.panorama.mapper;

import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.model.ScheduleTaskVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ScheduleTaskMapper {
    ScheduleTaskMapper INSTANCE = Mappers.getMapper(ScheduleTaskMapper.class);

    ScheduleTaskVo toVO(ScheduleTask entity);

    ScheduleTask toEntity(ScheduleTaskVo vo);
}
