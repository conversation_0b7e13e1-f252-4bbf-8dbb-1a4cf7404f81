package com.geeksec.panorama.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.geeksec.panorama.entity.handler.JsonbTypeHandler;
import com.geeksec.panorama.enums.AttackType;
import com.geeksec.panorama.enums.ClassType;
import com.geeksec.panorama.enums.Severity;
import com.geeksec.panorama.model.GraphPattern;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public abstract class BaseAttackModel implements Serializable {
  @TableField(exist = false)
  private static final long serialVersionUID = 1L;

  /** 模型ID */
  @TableId(type = IdType.ASSIGN_UUID)
  private String id;

  /** 模型名称 */
  private String name;

  /** 事件级别 */
  private Severity severity;

  /** 事件分类 */
  private ClassType type;

  /** 事件模型描述 */
  private String description;

  /** 图模式 */
  @TableField(typeHandler = JsonbTypeHandler.class)
  private GraphPattern pattern;

  @TableField(value = "ngql")
  private String nGQL;

  /** 最近运行时间 */
  private LocalDateTime lastRunTime;

  /** 模型创建时间 */
  @TableField(fill = FieldFill.INSERT)
  private LocalDateTime createTime;

  /** 模型更新时间 */
  @TableField(fill = FieldFill.INSERT_UPDATE)
  private LocalDateTime updateTime;
}
