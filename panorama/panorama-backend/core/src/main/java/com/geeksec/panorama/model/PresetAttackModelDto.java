package com.geeksec.panorama.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.geeksec.panorama.enums.AttackType;
import com.geeksec.panorama.enums.ClassType;
import com.geeksec.panorama.enums.Severity;
import com.geeksec.panorama.enums.TimeUnit;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * @author: jerryzhou
 * @date: 2024/7/28 14:56 @Description: 预置模型展示VO
 */
@Data
public class PresetAttackModelDto {

  /** 模型ID */
  @JsonProperty("id")
  private String id;

  /** 模型名称 */
  @NotBlank(message = "Model name cannot be blank")
  @JsonProperty("name")
  private String name;

  /** 事件严重等级 */
  @NotNull(message = "Event severity must be specified")
  @JsonProperty("severity")
  private Severity severity;

  /** 事件分类 */
  @NotNull(message = "Event class must be specified")
  @JsonProperty("type")
  private ClassType type;

  /** 模型描述 */
  @JsonProperty("description")
  private String description;

  /** 模型执行间隔 */
  @JsonProperty("interval")
  private int interval;

  /** 间隔时间单位 */
  @JsonProperty("unit")
  private TimeUnit unit;

  /** 图模式 */
  @JsonProperty("pattern")
  private GraphPattern pattern;

  @JsonProperty("ngql")
  private String nGQL;

  /** 模型参数 */
  @JsonProperty("params")
  private List<ModelParameter> params;

  /** 模型激活状态 */
  @JsonProperty("is_active")
  private boolean isActive;

  /** 最后运行时间 */
  @JsonProperty("last_run_time")
  private LocalDateTime lastRunTime;
}
