package com.geeksec.panorama.service.impl;

import com.geeksec.panorama.enums.*;
import com.geeksec.panorama.service.DictionaryService;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author: jerryzhou
 * @date: 2024/3/6 15:26 @Description:
 */
@Slf4j
@Service
public class DictionaryServiceImpl implements DictionaryService {

  @Override
  public Map<String, Object> getDict() {
    Map<String, Object> dictionary = new HashMap<>();
    dictionary.put("vertices", generateVertexTypeMap());
    dictionary.put("edges", generateEdgeTypeMap());
    dictionary.put("eventSeverity", generateEventSeverityMap());
    dictionary.put("classType",generateClassTypeMap());
    dictionary.put("eventTypes", generateEventTypeMap());
    dictionary.put("operator", generateOperatorMap());
    dictionary.put("timeUnit", generateTimeUnitMap());
    return dictionary;
  }

    private Map<Integer, Object> generateVertexTypeMap() {
    return Stream.of(VertexType.values())
        .collect(
            Collectors.toMap(
                VertexType::getCode,
                vertexType -> {
                  Map<String, Object> vertexInfo = new HashMap<>();
                  vertexInfo.put("name", vertexType.getDesc());
                  vertexInfo.put("properties", mapVertexProperties(vertexType));
                  return vertexInfo;
                }));
  }

  private List<Map<String, Object>> mapVertexProperties(VertexType vertexType) {
    return vertexType.getProperties().stream()
        .map(
            prop -> {
              Map<String, Object> propMap = new HashMap<>();
              propMap.put("key", prop.getKey());
              propMap.put("name", prop.getName());
              propMap.put("type", prop.getType());
              propMap.put("queryable", prop.isQueryable());
              return propMap;
            })
        .collect(Collectors.toList());
  }

  private Map<Integer, Object> generateEdgeTypeMap() {
    return Stream.of(EdgeType.values())
        .collect(
            Collectors.toMap(
                EdgeType::getCode,
                edgeType -> {
                  Map<String, Object> edgeInfo = new HashMap<>();
                  edgeInfo.put("name", edgeType.getDesc());
                  edgeInfo.put("sourceType", edgeType.getSource().getCode());
                  edgeInfo.put("destinationType", edgeType.getDestination().getCode());
                  edgeInfo.put("properties", mapEdgeProperties(edgeType));
                  return edgeInfo;
                }));
  }

  private List<Map<String, Object>> mapEdgeProperties(EdgeType edgeType) {
    return edgeType.getProperties().stream()
        .map(
            prop -> {
              Map<String, Object> propMap = new HashMap<>();
              propMap.put("key", prop.getKey());
              propMap.put("name", prop.getName());
              propMap.put("type", prop.getType());
              propMap.put("queryable", prop.isQueryable());
              return propMap;
            })
        .collect(Collectors.toList());
  }

  private Map<Integer, String> generateEventSeverityMap() {
    return Stream.of(Severity.values())
        .collect(Collectors.toMap(Severity::getCode, Severity::getDesc));
  }

  private Map<Integer, String> generateTimeUnitMap() {
    return Stream.of(TimeUnit.values())
        .collect(Collectors.toMap(TimeUnit::getCode, TimeUnit::getDesc));
  }

  private Map<Integer, String> generateEventTypeMap() {
    return Stream.of(AttackType.values())
        .collect(Collectors.toMap(AttackType::getCode, AttackType::getMsg));
  }

    private Map<Integer, String> generateClassTypeMap() {
      return Stream.of(ClassType.values())
              .collect(Collectors.toMap(ClassType::getCode,ClassType::getClassName));
    }


    private Map<Integer, Object> generateOperatorMap() {
    return Stream.of(Operator.values())
        .collect(
            Collectors.toMap(
                Operator::getCode,
                operator -> {
                  Map<String, Object> operatorInfo = new HashMap<>();
                  operatorInfo.put("name", operator.getDesc());
                  operatorInfo.put("supportedTypes", operator.getSupportedTypes());
                  return operatorInfo;
                }));
  }
}
