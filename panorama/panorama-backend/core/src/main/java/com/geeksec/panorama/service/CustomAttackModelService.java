package com.geeksec.panorama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.model.CustomAttackModelDto;
import com.geeksec.panorama.model.PageResult;
import java.util.concurrent.CompletableFuture;
import javax.validation.Valid;

public interface CustomAttackModelService extends IService<CustomAttackModel> {

  /**
   * 获取自定义攻击模型的分页结果
   *
   * @param page 当前页码
   * @param pageSize 每页大小
   * @return 包含自定义攻击模型的分页结果
   */
  PageResult<CustomAttackModel> getCustomModelPage(int page, int pageSize);

  /**
   * 根据ID和版本号获取自定义攻击模型
   *
   * @param id 模型ID
   * @param version 模型版本号
   * @return 匹配的自定义攻击模型，如果不存在则返回null
   */
  CustomAttackModel getByIdAndVersion(String id, Integer version);

  /** 新建自定义攻击模型 */
  void createCustomModel(@Valid CustomAttackModelDto modelDto);

  /**
   * 更新自定义攻击模型
   *
   * @param id 要更新的模型ID
   * @param modelDto 包含更新信息的DTO对象
   */
  void updateCustomModel(String id, CustomAttackModelDto modelDto);

  /**
   * 删除自定义事件模型
   *
   * @param id
   */
  void deleteCustomModel(String id);

  /**
   * 运行自定义事件模型
   *
   * @param id
   * @return
   */
  CompletableFuture<Boolean> run(String id, String taskId);
}
