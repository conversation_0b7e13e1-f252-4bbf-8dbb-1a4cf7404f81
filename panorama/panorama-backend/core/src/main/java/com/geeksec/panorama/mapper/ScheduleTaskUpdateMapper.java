package com.geeksec.panorama.mapper;

import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.model.ScheduleTaskUpdate;
import com.geeksec.panorama.model.ScheduleTaskVo;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
        componentModel = "spring",
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ScheduleTaskUpdateMapper {
    ScheduleTaskUpdateMapper INSTANCE = Mappers.getMapper(ScheduleTaskUpdateMapper.class);

    ScheduleTask convertScheduleTask(ScheduleTaskUpdate entity);
}
