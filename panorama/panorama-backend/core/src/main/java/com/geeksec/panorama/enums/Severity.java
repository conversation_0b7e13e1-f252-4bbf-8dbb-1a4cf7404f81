package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum Severity {
  LOW(1, "低"),
  MEDIUM(2, "中"),
  HIGH(3, "高");

  @EnumValue private final int code;
  private final String desc;

  Severity(int code, String desc) {
    this.code = code;
    this.desc = desc;
  }

  @JsonValue
  public int getCode() {
    return code;
  }

  @JsonCreator
  public static Severity fromCode(int code) {
    for (Severity severity : values()) {
      if (severity.code == code) {
        return severity;
      }
    }
    throw new IllegalArgumentException("Invalid EventSeverity code: " + code);
  }
}
