package com.geeksec.panorama.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.model.ScheduleTaskVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

@Mapper
public interface ScheduleTaskDao extends BaseMapper<ScheduleTask> {
    // 查询所有定时任务
    @Select("<script>" +
            "SELECT st.task_id, \n" +
            "st.task_name, \n" +
            "st.execution_type, \n" +
            "st.interval_unit, \n" +
            "st.interval_value, \n" +
            "st.cron_expression, \n" +
            "st.task_status, \n" +
            "st.create_time, \n" +
            "st.update_time, \n" +
            "st.last_run_time, \n" +
            "st.model_id, \n" +
            "st.created_by, cam.name as model_name \n" +
            "FROM schedule_task st LEFT JOIN custom_attack_model cam ON st.model_id = cam.id " +
            "<where>"+
            "<if test='task_name != null'> and st.task_name = #{task_name} </if>" +
            "<if test='model_name != null'> and cam.name = #{model_name} </if>" +
            "<if test='user_id != null'> and st.created_by = #{user_id} </if>" +
            "   <if test='start_time != null'>" +
            "       AND st.create_time >= #{start_time}" +
            "   </if>" +
            "   <if test='end_time != null'>" +
            "       AND st.create_time &lt;= #{end_time}" +
            "   </if>" +
            "</where>" +
            "order by st.create_time" +
            "<choose>" +
                "<when test='sort_order != null'> ${sort_order} </when>" +
                "<otherwise> desc </otherwise>" +
            "</choose>" +
            "</script>")
    Page<ScheduleTaskVo> getAllScheduleTasks(IPage<ScheduleTaskVo> page, @Param("user_id") String userId, @Param("task_name") String taskName,
                                             @Param("model_name") String modelName, @Param("sort_order") String sortOrder,
                                             @Param("start_time") LocalDateTime startTime, @Param("end_time") LocalDateTime endTime);
}
