package com.geeksec.panorama.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.geeksec.panorama.entity.ScheduleTask;
import com.geeksec.panorama.model.PageResult;
import com.geeksec.panorama.model.ScheduleTaskUpdate;
import com.geeksec.panorama.model.ScheduleTaskVo;
import com.geeksec.panorama.model.TaskPageCondition;
import org.quartz.SchedulerException;

import java.util.List;
import java.util.Map;

public interface ScheduleTaskService extends IService<ScheduleTask> {
    String insertTask(ScheduleTask entity);

    boolean del(String taskId);

    boolean update(ScheduleTaskUpdate entity);

    PageResult<ScheduleTaskVo> list(TaskPageCondition condition);

    boolean updateSetting(ScheduleTaskUpdate entity);

    List<Map<String, Object>> getAllJobs() throws SchedulerException;
}
