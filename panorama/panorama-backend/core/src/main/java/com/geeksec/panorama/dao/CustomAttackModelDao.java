package com.geeksec.panorama.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.geeksec.panorama.entity.CustomAttackModel;
import com.geeksec.panorama.model.TaskModelDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 自定义事件图模型Mapper接口
 *
 * <AUTHOR>
 * @date 2024/7/31
 */
@Mapper
public interface CustomAttackModelDao extends BaseMapper<CustomAttackModel> {
    /**
     * 查询自定义事件图模型列表
     *
     * @return 自定义事件图模型
     */
    @Select("SELECT id, name FROM custom_attack_model where current and created_by = #{createdBy}")
    List<TaskModelDto> selectModels(@Param("createdBy")String userId);
}
