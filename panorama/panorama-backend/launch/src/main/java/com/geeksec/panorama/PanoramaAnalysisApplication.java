package com.geeksec.panorama;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @author: jerry<PERSON>
 * @date: 2024/3/6 10:30 @Description:
 */
@SpringBootApplication(
    scanBasePackages = {"com.geeksec", "org.nebula.contrib"},
    exclude = {MultipartAutoConfiguration.class})
@EnableTransactionManagement // 启用事务管理
@MapperScan("com.geeksec.panorama.dao")
public class PanoramaAnalysisApplication {
  public static void main(String[] args) {
    SpringApplication.run(PanoramaAnalysisApplication.class, args);
  }
}
