<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <Property name="LOG_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} %highlight{[%t] %-5level %logger{36}} - %msg%n</Property>
        <Property name="DEV_LOG_DIR">logs/dev</Property>
    </Properties>
    <Appenders>
        <!-- 控制台输出 -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}" disableAnsi="false">
                <highlight-pattern>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}</pattern>
                    <style color="cyan"/>
                </highlight-pattern>
                <highlight-pattern>
                    <pattern>\[.*?\]</pattern>
                    <style color="magenta"/>
                </highlight-pattern>
            </PatternLayout>
        </Console>
        
        <!-- 开发环境日志文件 -->
        <RollingFile name="RollingFile" fileName="${DEV_LOG_DIR}/panorama.log"
                     filePattern="${DEV_LOG_DIR}/panorama-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
        </RollingFile>
        
        <!-- 错误日志单独记录 -->
        <RollingFile name="ErrorFile" fileName="${DEV_LOG_DIR}/error.log"
                     filePattern="${DEV_LOG_DIR}/error-%d{yyyy-MM-dd}-%i.log">
            <PatternLayout pattern="${LOG_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="10 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="10"/>
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </RollingFile>
    </Appenders>
    
    <Loggers>
        <!-- 应用日志级别 -->
        <Logger name="com.geeksec" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>
        
        <!-- 第三方库日志级别 -->
        <Logger name="org.springframework" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <Logger name="org.nebula.contrib" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Logger>
        
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>
    </Loggers>
</Configuration>
