<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.geeksec</groupId>
        <artifactId>panorama-backend</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>launch</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>web</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>core</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.geeksec</groupId>
            <artifactId>ngbatis</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>PanoramaAnalysisApplication</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.7.18</version>
                <configuration>
                    <!-- 指定该Main Class为全局的唯一入口 -->
                    <mainClass>com.geeksec.panorama.PanoramaAnalysisApplication</mainClass>
                    <layout>ZIP</layout>
                    <executable>true</executable>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal><!--可以把依赖的包都打包到生成的Jar包中-->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
