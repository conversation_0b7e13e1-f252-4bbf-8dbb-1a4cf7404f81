package com.geeksec.panorama.nebula.convert;

import cn.hutool.core.map.MapUtil;
import com.vesoft.nebula.client.graph.data.*;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.function.Function;

/**
 * @author: jerryzhou
 * @date: 2024/8/22 11:43
 * @Description: Nebula Result 返回结果识别工具
 **/
public class NebulaResultConverter {

    public static final Map<String, Function<Map<String, Object>, String>> EXTRACTORS = new HashMap<>();

    static {
        EXTRACTORS.put("IP", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "ip_addr");
            }
        });
        EXTRACTORS.put("DOMAIN", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "domain_addr");
            }
        });
        EXTRACTORS.put("ORG", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "org_name");
            }
        });
        EXTRACTORS.put("UA", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "ua_str");
            }
        });
        EXTRACTORS.put("CERT", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "finger_print");
            }
        });
        EXTRACTORS.put("OS", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return map.get("id").toString();
            }
        });
        EXTRACTORS.put("DEVICE", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return map.get("id").toString();
            }
        });
        EXTRACTORS.put("APT_GROUP", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "apt_name");
            }
        });
        EXTRACTORS.put("ATTACK", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "attack_type_msg");
            }
        });
        EXTRACTORS.put("MAIL", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "subject");
            }
        });
        EXTRACTORS.put("EMAIL", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "email_addr");
            }
        });
        EXTRACTORS.put("ATTACH_FILE", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "file_name");
            }
        });
        EXTRACTORS.put("MALICIOUS_FAMILY", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> map) {
                return MapUtil.getStr(map, "malicious_family");
            }
        });
        EXTRACTORS.put("LABEL", new Function<Map<String, Object>, String>() {
            @Override
            public String apply(Map<String, Object> stringObjectMap) {
                return "威胁标签";
            }
        });
    }

    public static List<NebulaResult> convertResultSet(ResultSet resultSet) throws UnsupportedEncodingException {
        List<NebulaResult> results = new ArrayList<>();

        for (int i = 0; i < resultSet.rowsSize(); i++) {
            ResultSet.Record record = resultSet.rowValues(i);
            NebulaResult result = new NebulaResult();

            for (ValueWrapper value : record.values()) {
                if (value.isLong()) {
                    result.addLong(value.asLong());
                } else if (value.isBoolean()) {
                    result.addBoolean(value.asBoolean());
                } else if (value.isDouble()) {
                    result.addDouble(value.asDouble());
                } else if (value.isString()) {
                    result.addString(value.asString());
                } else if (value.isTime()) {
                    result.addTime(value.asTime());
                } else if (value.isDate()) {
                    result.addDate(value.asDate());
                } else if (value.isDateTime()) {
                    result.addDateTime(value.asDateTime());
                } else if (value.isVertex()) {
                    result.addNode(value.asNode());
                } else if (value.isEdge()) {
                    result.addRelationship(value.asRelationship());
                } else if (value.isPath()) {
                    result.addPath((List<?>) value.asPath());
                } else if (value.isList()) {
                    result.addList(value.asList());
                } else if (value.isSet()) {
                    result.addSet(value.asSet());
                } else if (value.isMap()) {
                    result.addMap(value.asMap());
                }
            }

            results.add(result);
        }

        return results;
    }

    public static Map<String, Object> convertNodePropertiesToMap(Node node, String vertexType) throws UnsupportedEncodingException {
        Map<String, Object> properties = new HashMap<>();
        List<ValueWrapper> wrappers = node.values(vertexType);
        List<String> propertyNames = node.keys(vertexType);

        for (int i = 0; i < wrappers.size(); i++) {
            ValueWrapper wrapper = wrappers.get(i);
            String propertyName = propertyNames.get(i);
            Object value = convertValueWrapperToObject(wrapper);
            properties.put(propertyName, value);
        }

        return properties;
    }

    public static Map<String, Object> convertRelationshipPropertiesToMap(Relationship relationship) throws UnsupportedEncodingException {
        Map<String, Object> properties = new HashMap<>();
        List<ValueWrapper> wrappers = relationship.values();
        List<String> propertyNames = relationship.keys();

        for (int i = 0; i < wrappers.size(); i++) {
            ValueWrapper wrapper = wrappers.get(i);
            String propertyName = propertyNames.get(i);
            Object value = convertValueWrapperToObject(wrapper);
            properties.put(propertyName, value);
        }

        return properties;
    }

    public static Object convertValueWrapperToObject(ValueWrapper wrapper) throws UnsupportedEncodingException {
        if (wrapper.isNull()) {
            return null;
        } else if (wrapper.isBoolean()) {
            return wrapper.asBoolean();
        } else if (wrapper.isLong()) {
            return wrapper.asLong();
        } else if (wrapper.isDouble()) {
            return wrapper.asDouble();
        } else if (wrapper.isString()) {
            return wrapper.asString();
        } else if (wrapper.isDate()) {
            return wrapper.asDate();
        } else if (wrapper.isTime()) {
            return wrapper.asTime();
        } else if (wrapper.isDateTime()) {
            return wrapper.asDateTime();
        } else if (wrapper.isList()) {
            return wrapper.asList();
        } else if (wrapper.isSet()) {
            return wrapper.asSet();
        } else if (wrapper.isMap()) {
            return wrapper.asMap();
        }
        // 如果是其他类型，可以根据需要添加更多的转换逻辑
        return wrapper.toString();
    }

    public static class NebulaResult {
        private List<Long> longs = new ArrayList<>();
        private List<Boolean> booleans = new ArrayList<>();
        private List<Double> doubles = new ArrayList<>();
        private List<String> strings = new ArrayList<>();
        private List<TimeWrapper> times = new ArrayList<TimeWrapper>();
        private List<DateWrapper> dates = new ArrayList<DateWrapper>();
        private List<DateTimeWrapper> dateTimes = new ArrayList<DateTimeWrapper>();
        private List<Node> nodes = new ArrayList<>();
        private List<Relationship> relationships = new ArrayList<>();
        private List<List<?>> paths = new ArrayList<>();
        private List<List<?>> lists = new ArrayList<>();
        private List<Set<?>> sets = new ArrayList<>();
        private List<Map<?, ?>> maps = new ArrayList<>();

        // Add methods
        public void addLong(Long value) {
            longs.add(value);
        }

        public void addBoolean(Boolean value) {
            booleans.add(value);
        }

        public void addDouble(Double value) {
            doubles.add(value);
        }

        public void addString(String value) {
            strings.add(value);
        }

        public void addTime(TimeWrapper value) {
            times.add(value);
        }

        public void addDate(DateWrapper value) {
            dates.add(value);
        }

        public void addDateTime(DateTimeWrapper value) {
            dateTimes.add(value);
        }

        public void addNode(Node value) {
            nodes.add(value);
        }

        public void addRelationship(Relationship value) {
            relationships.add(value);
        }

        public void addPath(List<?> value) {
            paths.add(value);
        }

        public void addList(List<?> value) {
            lists.add(value);
        }

        public void addSet(HashSet<ValueWrapper> value) {
            sets.add(value);
        }

        public void addMap(HashMap<String, ValueWrapper> value) {
            maps.add(value);
        }

        // Getter methods
        public List<Long> getLongs() {
            return longs;
        }

        public List<Boolean> getBooleans() {
            return booleans;
        }

        public List<Double> getDoubles() {
            return doubles;
        }

        public List<String> getStrings() {
            return strings;
        }

        public List<TimeWrapper> getTimes() {
            return times;
        }

        public List<DateWrapper> getDates() {
            return dates;
        }

        public List<DateTimeWrapper> getDateTimes() {
            return dateTimes;
        }

        public List<Node> getNodes() {
            return nodes;
        }

        public List<Relationship> getRelationships() {
            return relationships;
        }

        public List<List<?>> getPaths() {
            return paths;
        }

        public List<List<?>> getLists() {
            return lists;
        }

        public List<Set<?>> getSets() {
            return sets;
        }

        public List<Map<?, ?>> getMaps() {
            return maps;
        }
    }
}
