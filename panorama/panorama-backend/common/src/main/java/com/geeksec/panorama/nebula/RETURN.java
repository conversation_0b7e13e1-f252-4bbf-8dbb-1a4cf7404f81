package com.geeksec.panorama.nebula;

public class RETURN extends  VLBase {
    public RETURN(String ReturnSQL) {
        Name = ReturnSQL ;


    }
    public  SqlParse Parse(SqlParse PSql ) {
        //String alname = PSql.GetAlias(Name);
        //System.out.ut.println("RETURN  ========= Parse");
        String alname = Name;
        if (alname == null) {
            return null ;
        }else {
            PSql.SQL += " "+alname ;
        }
        return PSql;
    }


}
