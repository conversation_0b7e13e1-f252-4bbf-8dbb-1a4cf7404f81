package com.geeksec.panorama.nebula;

public class Order extends  VLBase  {
    private  String sField = null;
    private  boolean desc = true;
    Order(String TableName  ,String Field ,  Boolean  dDesc  ) {
        Name = TableName;
        sField = Field;
        Type = "O";
        desc = dDesc;
    }
    public Order(String Field, Boolean dDesc) {
        Name = null;
        sField = Field;
        Type = "O";
        desc = dDesc;
    }
    public  SqlParse Parse(SqlParse PSql) {
        String sql = "";

        if  (Name !=  null) {
            PSql.SQL += " "+Name+"."+ sField + " ";
        } else {
            PSql.SQL += " "+ sField + " ";
        }
        if (desc) {
            PSql.SQL +="DESC ";
        } else {
            PSql.SQL += "ASC ";
        }
        return PSql;
    }
}