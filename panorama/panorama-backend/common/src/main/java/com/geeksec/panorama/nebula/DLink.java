package com.geeksec.panorama.nebula;

public class DLink extends  VLBase  {
    DLink(String LName  ) {
        Name = LName;

        Type = "L";
    }
    public  SqlParse Parse(SqlParse PSql) {
        if (PSql.LastVType.equals("V")) {
            if (Name.equals("D") ) {
                PSql.SQL += "-";
            } else if  (Name.equals("R") ) {
                PSql.SQL += "->";
            } else if  (Name.equals("L") ) {
                PSql.SQL += "<-";
            }
        }
        return PSql;
    }
}
