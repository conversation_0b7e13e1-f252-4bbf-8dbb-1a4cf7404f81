package com.geeksec.panorama.nebula;

import java.util.ArrayList;
import java.util.List;

public class MATCH extends VLBase {
    List<VLBase> VLBaseList = new ArrayList<>();
    List<Where> WhereList = new ArrayList<>();
    List<RETURN> ReturnList = new ArrayList<>();
    List<Order> OrderList = new ArrayList<>();
    String strSql = "";
    private SqlParse PSql = null ;

    public MATCH AddVLBase(VLBase node) {
        VLBaseList.add(node);
        return this;
    }
    public MATCH AddVLWhere(Where tes) {
        WhereList.add(tes);
        return this;
    }
    public  MATCH AddVLReturn(RETURN tes) {
        ReturnList.add(tes);
        return this;
    }
    public  MATCH AddOrder(Order e) {

        OrderList.add(e);
        return this;
    }
    public SqlParse Parse() {
        PSql = new SqlParse();
        PSql.LastVType = "M";
        PSql.SQL = "MATCH ";
        for  (VLBase tmp : VLBaseList) {
            tmp.TParse(PSql);
        }
        //  WHERE
        if (WhereList.isEmpty() == false) {
            PSql.SQL += " WHERE ";
            PSql.LastVType = "W";
            for(Where tmp : WhereList)  {
                tmp.Parse(PSql);
            }
        }
        if (ReturnList.isEmpty() == false) {
            PSql.SQL += " RETURN ";
            PSql.LastVType = "W";
            int num = 0;
            for ( RETURN tmp : ReturnList) {
                if (num > 0) {
                    PSql.SQL +=",";
                }
                tmp.Parse(PSql);
                num ++;
            }

        }
        if (OrderList.isEmpty() == false ) {
            PSql.SQL += " ORDER BY  ";
            int num = 0;
            for ( Order tmp : OrderList) {
                if (num > 0) {
                    PSql.SQL +=",";
                }
                tmp.Parse(PSql);
                num ++;
            }
        }

        return PSql  ;
    }

}