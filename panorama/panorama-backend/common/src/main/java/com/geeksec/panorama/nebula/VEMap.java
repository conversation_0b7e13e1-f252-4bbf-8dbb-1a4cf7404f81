package com.geeksec.panorama.nebula;

import cn.hutool.core.util.ObjectUtil;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

//  对nanubal 查询的数据 整理成图的结构
// V(id , type , status  ,  value )
// V_Vname_id
// E( from: to:   value: )
//
//  解析配置
public class VEMap {
    private Set<String> VSet = new HashSet<>();
    private Set<String> ESet = new HashSet<>();
    private HashMap<String, Set<String>> labelMap = new HashMap<String, Set<String>>();

    private void Print(Set<String> T) {
        //System.out.ut.println("********* Print ******************");
        for (String L : T) {
            //System.out.ut.println(L);
        }
        //System.out.ut.println("********* End ******************");
    }

    public boolean AddVertice(Map<String, Object> VMap) {
        if (ObjectUtil.isEmpty(VMap.get("id"))) {
            VMap.put("id", "-");
        }
        String Key = VMap.get("id").toString() + "_" + VMap.get("type").toString();
        //System.out.ut.println("AddVertice  key ===== "+Key);
        // Print(VSet);
        if (VSet.contains(Key)) {
            Print(VSet);
            return false;
        } else {
            VSet.add(Key);
            return true;
        }

    }

    public boolean AddEdge(Map<String, Object> EMap) {

        if (ObjectUtil.isEmpty(EMap.get("from"))) {
            EMap.put("from", "-");
        }
        if (ObjectUtil.isEmpty(EMap.get("to"))) {
            EMap.put("to", "-");
        }
        // 如果存在两个点相同的情况，但边类型不一样时，做特殊判断
        String Key = EMap.get("from").toString() + "_" + EMap.get("to").toString();
        String label = EMap.get("label").toString();

        // 判断当前边Key-边类型是否存在
        if (!labelMap.containsKey(Key)) {
            Set<String> labelSet = new HashSet<>();
            labelSet.add(label);
            labelMap.put(Key, labelSet);
            return true;
        } else {
            Set<String> labelSet = labelMap.get(Key);
            if (labelSet.contains(label)) {
                return false;
            } else {
                return true;
            }
        }

        //if (ESet.contains(Key) ) {
        //    return false;
        //} else {
        //    ESet.add(Key);
        //    return true;
        //}
    }
}

