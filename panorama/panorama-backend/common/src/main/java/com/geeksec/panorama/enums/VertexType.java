package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.geeksec.panorama.entity.Property;
import com.geeksec.panorama.entity.PropertyHolder;
import lombok.Getter;

import java.awt.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Getter
public enum VertexType implements PropertyHolder {
    IP(
            1,
            "IP",
            "IP",
            Arrays.asList(
                    new Property("ip_addr", "IP地址", "string", true),
                    new Property("version", "版本", "string", true),
                    new Property("city", "所属城市", "string", true),
                    new Property("stat", "所属省份（州）", "string", true),
                    new Property("country", "所属国家", "string", true),
                    new Property("longitude", "地址经度", "double", false),
                    new Property("latitude", "地理纬度", "double", false),
                    new Property("ISP", "互联网服务提供商", "string", false),
                    new Property("asn", "AS信息", "string", false),
                    new Property("org", "所属机构名称", "string", false))),
    DOMAIN(
            2,
            "DOMAIN",
            "域名",
            Collections.singletonList(new Property("domain_addr", "域名地址", "string", true))),
    CERT(
            3, "CERT", "证书", Arrays.asList(
            new Property("cert_hash", "证书哈希值", "string", true),
            new Property("algorithm_id", "证书签名算法", "string", false),
            new Property("fp_alg", "指纹算法", "string", false),
            new Property("issuer", "证书颁发者", "string", true),
            new Property("subject", "证书使用者", "string", true),
            new Property("version", "证书版本号", "int", false),
            new Property("not_before", "证书生效日期", "long", false),
            new Property("not_after", "证书失效日期", "long", false))),
    ATTACK(
            4, "ATTACK", "攻击行为", Arrays.asList(
            new Property("attack_id", "告警ID", "string", true),
            new Property("attack_time", "告警时间", "datetime", true),
            new Property("aip_aport_app_vport_vip", "会话五元组信息", "string", false),
            new Property("threat_type", "威胁类型", "string", true),
            new Property("kill_chain", "杀伤链标签", "string", true),
            new Property("confidence", "置信度", "string", true),
            new Property("vendor_id","供应商ID","string",true),
            new Property("detect_type","检测类型","string",true),
            new Property("pcap_file","原始文件路径","string",true),
            new Property("labels","关联标签ID","string",false),
            new Property("pcap_name","原始文件名称","string",false)
    )),
    URL(
            5, "URL", "远程资源路径", Arrays.asList(
            new Property("url_path", "URL路径", "string", true),
            new Property("uri","uri信息","string",true)

    )),
    ORG(
            6, "ORG", "组织机构", Arrays.asList(
            new Property("org_name", "组织机构名称", "string", true))),
    UA(
            7, "User-Agent", "用户代理", Arrays.asList(
            new Property("ua_str", "User-Agent字符串", "string", false),
            new Property("os_name", "操作系统", "string", true),
            new Property("device_name", "设备名称", "string", true)
    )),
    APT_GROUP(
            8,
            "APT_GROUP",
            "APT组织",
            Arrays.asList(
                    new Property("apt_name", "APT组织名称", "string", true),
                    new Property("apt_desc", "APT组织描述", "string", true),
                    new Property("apt_country", "APT组织所属国家", "string", true),
                    new Property("apt_alias", "APT组织别名", "string", true))),
    FILE(9,
            "FILE",
            "附带文件",
            Arrays.asList(
                    new Property("file_md5", "文件MD5", "string", true),
                    new Property("file_sha1", "文件SHA1", "string", true),
                    new Property("file_sha256", "文件SHA256", "string", true),
                    new Property("file_sha512", "文件SHA512", "string", true),
                    new Property("file_crc32", "文件CRC32", "string", true),
                    new Property("file_size", "文件大小", "int64", false),
                    new Property("file_path", "文件路径", "string", true))),
    EMAIL(
            10,
            "EMAIL",
            "电子邮箱",
            Arrays.asList(
                    new Property("email_addr", "邮箱地址", "string", true),
                    new Property("user_name", "用户名称", "string", true))),
    MAIL(
            11,
            "MAIL",
            "邮件",
            Arrays.asList(
                    new Property("subject", "主题", "string", true),
                    new Property("industry", "所属行业", "string", false),
                    new Property("intents", "邮件意图", "string", false))),
    LABEL(
            12,
            "LABEL",
            "标签",
            Arrays.asList(
                    new Property("label_id", "标签ID", "string", true),
                    new Property("label_name", "标签名称", "string", true))),
    SSLFINGER(
            13,
            "SSLFINGER",
            "指纹", Arrays.asList(
            new Property("ja3_hash", "指纹哈希", "string", true)
    )),
    APP(
            14,
            "APP",
            "APP应用", Arrays.asList(
            new Property("app_md5", "应用名称", "string", true),
            new Property("app_type_id", "应用类型ID", "INT64", false),
            new Property("app_type", "应用类型", "string", true),
            new Property("app_class_id", "应用分类ID", "INT64", false),
            new Property("app_class", "应用分类", "string", true),
            new Property("is_encrypted", "是否加密", "bool", true)
    ));

    @EnumValue
    private final int code;
    private final String name;
    private final String desc;
    private final List<Property> properties;

    VertexType(int code, String name, String desc, List<Property> properties) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.properties = properties;
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    @JsonCreator
    public static VertexType fromCode(int code) {
        for (VertexType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid VertexType code: " + code);
    }

    public static VertexType fromName(String name) {
        for (VertexType type : values()) {
            if (Objects.equals(type.name, name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid VertexType name: " + name);
    }
}
