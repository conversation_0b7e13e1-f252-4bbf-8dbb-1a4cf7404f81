package com.geeksec.panorama.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: jerryzhou
 * @date: 2024/7/31 22:45
 * @Description: 事件类别枚举
 **/
public enum EventCategory {

    HARMFUL(1,"有害程序事件"),
    INFILTRATION(2,"渗透入侵事件"),
    NETWORK_ATTACK(3,"网络攻击事件");

    /**
     * 值
     */
    private Integer code;

    /**
     * 类型
     */
    private String msg;

    EventCategory(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    private static Map<Integer, EventCategory> codeEnumMap = new HashMap<>();


    static {
        for (EventCategory e : EventCategory.values()) {
            codeEnumMap.put(e.getCode(), e);
        }
    }

    public static String getMsgByCode(Integer code) {
        EventCategory getEnum = codeEnumMap.get(code);
        return getEnum != null ? getEnum.getMsg() : "";
    }
}
