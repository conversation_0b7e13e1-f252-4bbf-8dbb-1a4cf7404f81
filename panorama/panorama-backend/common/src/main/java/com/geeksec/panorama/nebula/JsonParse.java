package com.geeksec.panorama.nebula;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;
import java.util.List;

public class JsonParse {
    private static JacksonHandle instance=null;
    public JsonNode hJsonParse(String str)
    {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(str);
            return root;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null;
    }
    public JsonNode getObjValue(JsonNode obj  , String Path)
    {
        try {
            List<String> result = Arrays.asList(Path.split("/"));
            JsonNode obj_L = obj;
            for (int i = 0; i < result.size(); i++) {
                // 判断有没有符号
                String Filed = result.get(i);
                int Nt1 = Filed.indexOf("[");
                int Nt2 = Filed.indexOf("]");
                if(Nt1 != -1 && Nt2 != -1)
                {
                    String sArrFiled = Filed.substring(0,Nt1-1);
                    int Num = Integer.parseInt(Filed.substring(Nt1+1,Nt2-1));
                    obj_L = obj_L.get(Filed).get(Num);
                }
                else {
                    if (obj_L == null) {
                        return null;
                    }
                    obj_L = obj_L.get(Filed);
                }
            }
            return obj_L;
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return null ;
    }
}
