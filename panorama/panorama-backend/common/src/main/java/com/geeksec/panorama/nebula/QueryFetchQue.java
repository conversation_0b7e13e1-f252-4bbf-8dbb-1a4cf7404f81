package com.geeksec.panorama.nebula;

import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.Map;

class QueryFetchQue {
    private String TempSql = "";

    private  ChartReslutHandle  CResrt = new ChartReslutHandle();
    // 值转换
    private  String   Value2Str(Object param)
    {

        if (param instanceof Integer) {
            return String.valueOf((Integer)param);

        } else if (param instanceof String) {
            return "'"+(String)param+"'";

        } else if (param instanceof Float) {
            return String.valueOf((Float)param);
        } else if (param instanceof Double) {
            return String.valueOf((Double)param);
        }
        return "";
    }
    public String SqlCmd(Map<String , Object > Reqmap) {
        String sql = new String(TempSql) ;
        for (Map.Entry<String, Object> entry : Reqmap.entrySet()) {
            // //System.out.ut.print(entry.getKey() + "-" + entry.getValue() + "	");
            sql = sql.replace("@"+entry.getKey() , Value2Str(entry.getValue()));
        }
        return sql;
    }
    public  void Parse(JSONObject obj) {
        TempSql = obj.get("sql").toString();
        JSONObject RespJson  = obj.getJSONObject("resp");
        CResrt.Parse(RespJson);
    }
    public void ChartByResp(List<Map<String,Object>> reslet ,List<Object> VLsit ,List<Object> EList ,VEMap pVEMAap) {
        CResrt.Handle(reslet,VLsit,EList,pVEMAap);
    }
    //

}
