package com.geeksec.panorama.nebula;

import com.alibaba.fastjson.JSONObject;

import java.util.Map;

class NodePrase{
    public  String Key = "" ;
    public  String Type = "string" ; // string , int
    public  String Text = "";
    NodePrase(JSONObject map) {
        //Map<String, Object> map = new BeanMap(obj);
        Key = map.get("key").toString();
        if (map.containsKey("type")) {
            Type = map.get("type").toString();
        }
        if (map.containsKey("text")) {
            Text = map.get("text").toString();
        }

    }
    public  Object Handle( Map<String,Object> src ) {
        if ("".equals(Key) == false ) {
            Object v  = src.get(Key);
            return  v;
        } else {
            return Text;
        }

    }
}

