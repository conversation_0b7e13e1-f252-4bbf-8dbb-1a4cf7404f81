package com.geeksec.panorama.nebula;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

// {"V":[] , "E":[]}
public class ChartReslutHandle {
    private List<List<ChartNode>> VVDefList = new ArrayList<>();
    private List<List<ChartNode>> EEDefList = new ArrayList<>();

    public void Parse(JSONObject obj) {
        JSONArray VertexDef = obj.getJSONArray("vertex");
        JSONArray EdgeDef = obj.getJSONArray("edge");
        for (int i = 0; i < VertexDef.size(); i++) {
            JSONArray vobj = VertexDef.getJSONArray(i);
            List<ChartNode> VDefList = new ArrayList<>();
            for (int ii = 0; ii < vobj.size(); ii++) {
                JSONObject vvobj = vobj.getJSONObject(ii);
                VDefList.add(new ChartNode(vvobj));
            }
            VVDefList.add(VDefList);

        }
        for (int i = 0; i < EdgeDef.size(); i++) {
            JSONArray eobj = EdgeDef.getJSONArray(i);
            List<ChartNode> EDefList = new ArrayList<>();
            for (int ii = 0; ii < eobj.size(); ii++) {
                JSONObject eeobj = eobj.getJSONObject(ii);
                // 去重
                EDefList.add(new ChartNode(eeobj));
            }
            EEDefList.add(EDefList);
        }
    }

    public void Handle(List<Map<String, Object>> relust, List<Object> VLsit, List<Object> EList, VEMap pVEMAap) {
        for (Map<String, Object> src : relust) {

            for (List<ChartNode> VMList : VVDefList) {
                Map<String, Object> VT = new HashMap<>();
                for (ChartNode V : VMList) {
                    V.Handle(src, VT);
                }
                // 处理 特殊的V
                String vertexType = (String) VT.get("type");
                //VT = handleSpecialVertex(vertexType,VT);
                if (pVEMAap.AddVertice(VT)) {
                    VLsit.add(VT);
                }
            }

            for (List<ChartNode> EMList : EEDefList) {
                Map<String, Object> ET = new HashMap<>();
                for (ChartNode E : EMList) {
                    E.Handle(src, ET);
                }
                if (pVEMAap.AddEdge(ET)) {
                    //System.out.ut.println("EEEEEEEEEE" );
                    EList.add(ET);
                }
            }

        }
    }

    private Map<String, Object> handleSpecialVertex(String vertexType, Map<String, Object> VT) {
        switch (vertexType) {
            case "APPSERVICE":
                String ip = ((String) VT.get("label")).split("_")[0];
                VT.put("label", ip + "_" + vertexType);
                return VT;
            default:
                return VT;
        }
    }

}
