package com.geeksec.panorama.nebula;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections.map.HashedMap;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NebulaChart {

    //private PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    //private String hbaseHost = propertiesLoader.getProperty("hbase.zookeeper.host");
    private  Map<String ,List<QueryFetchQue> > ReqTypeMap = new HashedMap(); //
    public NebulaChart(String str) {
        JSONArray array = JSON.parseArray(str);
        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = (JSONObject)array.get(i);
            String type = obj.getString("type");
            List<QueryFetchQue> hList = new ArrayList<>();
            JSONArray handle_list = obj.getJSONArray("handle");
            for (int ii = 0 ; ii < handle_list.size() ; ii++) {
                QueryFetchQue  p = new QueryFetchQue();
                p.Parse((JSONObject) handle_list.get(ii));
                hList.add(p);
            }
            ReqTypeMap.put(type , hList);
        }
    }

    public  Map<String ,Object> Handle(String type, Map<String ,Object > req, NebulaDB db) {
        if (this.ReqTypeMap.containsKey(type)) {
            Map<String ,Object> RM = new HashMap<>();
            List<Object> VLsit =  new ArrayList<>( ) ;
            List<Object> EList  =  new ArrayList<>();
            VEMap pVEMAap = new VEMap();
            List<QueryFetchQue> QueryList = this.ReqTypeMap.get(type);
            for (QueryFetchQue Query : QueryList) {
                String sql = Query.SqlCmd(req);
                Query.ChartByResp(db.Select(sql),VLsit,EList,pVEMAap);
            }
            RM.put("vertex",VLsit);
            RM.put("edge",EList);
            return RM;
        }
        return null ;
    }

}
