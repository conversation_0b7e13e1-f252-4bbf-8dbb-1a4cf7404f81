package com.geeksec.panorama.nebula;

public class Vertex extends VLBase {
    public Vertex(String Vname) {
        Name = Vname;
        Type = "V";
    }
    public Vertex(String Vname,String type) {
        Name = Vname;
        Type = type;
    }
    public  SqlParse Parse( SqlParse PSql) {
        if (PSql.LastVType.equals("V")) {
            PSql.SQL +=  "-[]-";
        } else if (PSql.LastVType.equals("E")) {
            PSql.SQL += "-";
        }else if(Type.equals("Q")) {
            PSql.SQL +=  "-[]->";
        }
        //String VAliseName = PSql.GetVateAlias(Name);
        String VAliseName =  Name;
        PSql.SQL += "(" + VAliseName+":"+Name +")";
        return PSql;
    }
}
