package com.geeksec.panorama.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * JZ权限认证系统返回的用户信息实体类
 */
@Data
public class UserInfo {
    @JsonProperty(value = "token")
    private String token;

    @JsonProperty(value = "userID")
    private String userID;

    @JsonProperty(value = "userName")
    private String userName;

    @JsonProperty(value = "unitID")
    private String unitID;
    private String loginPointId;
    private String loginSysFrom;

    @JsonProperty(value = "lastLoginTime")
    private Long lastLoginTime;
    private Integer unitGrade;

    @JsonProperty(required = false)
    private Object isUserCustomMenu;

    private List<String> ssysfromList;
    private Map<String,Object> hwnodenoMap;
    private String unitName;
    private String tk;
    private Boolean admin;
    private String startUnitId;
    private boolean unitManager;
    private boolean sysManager;
    private List<Object> maxUnitRoleUnitID;


}
