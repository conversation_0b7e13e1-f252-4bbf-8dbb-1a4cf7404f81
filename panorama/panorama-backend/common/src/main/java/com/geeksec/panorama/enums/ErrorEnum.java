package com.geeksec.panorama.enums;

/**
 * @author: heeexy
 * @date: 2017/10/24 10:16
 */
public enum ErrorEnum {
    /*
     * 错误信息
     * */

    /**
     * 系统型错误
     */
    E_400("400", "请求处理异常"),
    E_408("408", "请求超时"),
    E_500("500", "请求方式有误,请检查 GET/POST"),
    E_501("501", "请求路径不存在"),
    E_502("502", "权限不足"),

    /**
     * 用户权限错误
     */
    E_10001("10001", "新增角色信息为空"),
    E_10002("10002", "修改角色信息为空"),
    E_10003("10003", "修改用户信息失败"),
    E_10004("10004", "获取角色所拥有权限失败"),
    E_10005("10005", "当前修改角色不存在"),
    E_10008("10008", "用户删除失败"),
    E_10009("10009", "账户已存在,请勿重复创建"),
    E_10010("10010", "账号/密码错误"),
    E_10011("10011", "角色已存在，请勿重复创建"),
    E_10012("10012", "远程登录失败，检查请求参数"),

    /**
     * 业务型错误
     */
    E_20011("20011", "登陆已过期,请重新登陆"),
    E_30001("30001", "角色不拥有此功能权限"),
    E_40001("40001", "排序字段有误"),
    E_40002("40002", "请求参数异常，请检查"),
    E_40003("40003", "json格式异常，请检查"),
    E_40004("40004", "数据冲突，请检查"),
    E_40005("40005", "文件读取失败"),
    E_40006("40006", "ES查询异常"),
    E_40007("40007", "数据处理异常"),
    E_40008("40008", "ES聚合，查询数量超过阀值"),
    E_40009("40009", "ES查询数据为空"),
    E_40010("40010", "文件内容格式非法"),
    E_40011("40011", "es查询页码超过限制"),
    E_40012("40012", "Nebula查询异常"),
    E_40013("40013", "Nebula查询数据为空"),
    E_40014("40014","Redis查询异常"),
    E_40015("40015","ES聚合查询失败"),

    /**
     * 系统规则型错误
     */
    E_50001("50001", "ip字段异常"),
    E_50002("50002", "ruleId生成失败，请重试"),
    E_50003("50003","数据库磁盘模式字段读取错误"),

    // 任务相关错误
    E_60001("60001", "创建任务失败"),
    E_60002("60002", "删除任务失败"),
    E_60003("60003", "修改任务状态失败"),
    E_60004("60004", "修改任务参数失败"),
    E_60005("60005", "查询任务失败"),


    /**
     * 常规错误
     */
    E_90003("90003", "缺少必填参数"),
    E_90004("90004", "临时文件夹位置未配置，请联系管理员"),
    E_90005("90005", "文件数量过大"),
    E_90006("90006", "规则同步url未配置"),
    E_90007("90007", "探针规则同步请求异常，请稍后尝试"),
    E_90008("90008", "文件创建失败"),
    E_90009("90009", "当前内网IP网段信息已存在"),
    E_90010("90010", "Nebula文件未初始化，请联系管理员"),
    E_90011("90011", "MySQL数据库操作失败"),
    E_90012("90012", "文件缺少必填项"),
    E_90013("90013", "MySQL查询数据为空"),
    E_90014("90014", "下载文件准备失败"),
    E_90015("90015", "会话条数过大");

    private final String errorCode;

    private final String errorMsg;

    ErrorEnum(String errorCode, String errorMsg) {
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    public Integer getErrorCode() {
        return Integer.valueOf(errorCode);
    }

    public String getErrorMsg() {
        return errorMsg;
    }
}
