package com.geeksec.panorama.entity;

import com.alibaba.fastjson.JSONObject;
import com.geeksec.panorama.enums.GkError;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import lombok.Data;

@Data
public class Result<T> implements Serializable {

  private T data;

  private Integer code;

  private String msg;

  public Result(T data, Integer code, String msg) {
    this.data = data;
    this.code = code;
    this.msg = msg;
  }

  public Result(T data) {
    this.data = data;
    this.code = 200;
    this.msg = "成功";
  }

  public Result(Integer code, String msg) {
    this.code = code;
    this.msg = msg;
  }

  /**
   * 返回对象的suc
   *
   * @param <T>
   * @return
   */
  public static <T> Result<T> success(T data) {
    return new Result<>(data);
  }

  /**
   * 返回对象的suc
   *
   * @param <T>
   * @return
   */
  public static <T> Result<T> successMsg(String msg) {
    return new Result<>(200, msg);
  }

  public static Result authFailReturnUrl(String url){
    Map<String,String> urlMap = new HashMap<>();
    urlMap.put("returnRUrl",url);
    return new Result(urlMap,401,"Token已失效，请重新登录");
  }

  /**
   * 失败信息
   *
   * @param <T>
   * @return
   */
  public static <T> Result<T> fail(Integer code, String msg) {
    return (Result<T>) new Result<>(new JSONObject(), code, msg);
  }
}
