package com.geeksec.panorama.config.filter;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.geeksec.panorama.enums.GkError;
import com.geeksec.panorama.exception.GkException;
import com.geeksec.panorama.entity.UserInfo;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Aspect
@Component
@Order(2)
@Slf4j
public class LoginStatusCheckFilter {

    @Value("${url.login_status_check}")
    private String LOGIN_STATUS_CHECK_URL;

    @Value("${enabled.login_check}")
    private Boolean LOGIN_CHECK_SWITCH;

    @Before(
            "@annotation(org.springframework.web.bind.annotation.PostMapping)" +
            "||@annotation(org.springframework.web.bind.annotation.GetMapping)" +
            "||@annotation(org.springframework.web.bind.annotation.PutMapping)" +
            "||@annotation(org.springframework.web.bind.annotation.DeleteMapping)")
    public void before(JoinPoint joinPoint) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        // 判断是否需要登录校验
        if (!LOGIN_CHECK_SWITCH) {
            MDC.put("token", "geeksec-static-token");
            return;
        }

        String token = request.getHeader("ut");
        if (StrUtil.isEmpty(token)) {
            throw new GkException(GkError.UNAUTHORIZED);
        }
        log.debug("进行第三方权限校验登陆...token:{}", token);

        // 组装校验参数
        JSONObject authParam = new JSONObject();
        authParam.put("token", token);
        authParam.put("version", "1.0");
        log.info("向第三方校验系统发起校验请求,authParam->{}", authParam);
        JSONObject resultJson;
        String resultStr = StrUtil.EMPTY_JSON;
        try {
            resultStr = HttpUtil.post(LOGIN_STATUS_CHECK_URL, authParam.toString());
            log.info("校验系统返回结果：{}", resultStr);
            // 返回的resultStr是一个json字符串
            resultJson = JSONUtil.parseObj(resultStr);
        } catch (Exception e) {
            log.error("第三方权限校验平台请求异常,result->{},URL->{}", resultStr, LOGIN_CHECK_SWITCH);
            throw new GkException(GkError.LOGIN_STATUS_CHECK_REQUEST_FAIL);
        }

        Integer code = resultJson.getInt("code");
        switch (code) {
            case 200:
                // 校验成功逻辑
                handleSuccessfulValidation(resultJson, token);
                break;
            case 500:
                handleFailedValidation(resultJson);
                break;
            default:
                throw new GkException(GkError.LOGIN_STATUS_CHECK_ERROR);
        }
    }

    private void handleFailedValidation(JSONObject resultJson) {
        Map<String, Object> dataMap = MapUtil.get(resultJson, "data", Map.class);
        String loginUrl = (String) dataMap.get("returnRUrl");
        throw new GkException(GkError.UNAUTHORIZED, loginUrl);
    }

    private void handleSuccessfulValidation(JSONObject resultJson, String token) {
        JSONObject dataJson = resultJson.getJSONObject("data").getJSONObject("user");
        UserInfo userInfo = JSONUtil.toBean(dataJson, UserInfo.class);
        if (ObjectUtil.isEmpty(userInfo)) {
            throw new GkException(GkError.LOGIN_STATUS_CHECK_USER_EMPTY);
        }
        String userName = userInfo.getUserName();
        String userId = userInfo.getUserID();
        if (StrUtil.isNotEmpty(userName) && StrUtil.isNotEmpty(userId)) {
            // 获取到用户数据，进行同步系统同步
            MDC.put("token", token);
            MDC.put("user_name",userName);
            MDC.put("user_id",userId);
        } else {
            throw new GkException(GkError.LOGIN_STATUS_CHECK_USER_EMPTY);
        }
    }
}