package com.geeksec.panorama.nebula;

public class Edge extends  VLBase  {
    public Edge(String EName) {
        Name = EName;
        Type = "E";
    }
    public  SqlParse Parse( SqlParse PSql) {
        if (PSql.LastVType.equals("V") == true) {
            PSql.SQL += "-";
        }else if (PSql.LastVType.equals("E") == true) {
            PSql.SQL += "-";
        }
        //String VAliseName = PSql.GetEdgeAlias(Name);
        PSql.SQL += "[" +Name +"]";
        return PSql;

    }
}
