package com.geeksec.panorama.nebula;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Description： 告警研判展示点尸体
 */
@Data
public class AlarmJudgeVertexEntity {

    @JsonProperty(value = "id")
    private String vid;

    @JsonProperty(value = "label")
    private String label;

    @JsonProperty(value = "lv")
    private Integer level;

    @JsonProperty(value = "num")
    private String num;

    @JsonProperty(value = "status")
    private String status;

    @JsonProperty(value = "type")
    private String type;

    @JsonProperty(value = "tag_list")
    private List<String> tagList;

    /**
     * 身份 attacker & victim
     */
    @JsonProperty(value = "identity")
    private String identity;


    ///**
    // * 点信息详情
    // */
    //@JsonProperty(value = "v_info")
    //private Map<String,Object> vertexDetail;
}
