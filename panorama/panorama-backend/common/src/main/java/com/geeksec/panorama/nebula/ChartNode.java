package com.geeksec.panorama.nebula;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

class ChartNode {

    Map<String,NodePrase> RespMap =  new HashMap<>();
    public   ChartNode(JSONObject jsonObject) {

        for (Map.Entry entry : jsonObject.entrySet()) {
            RespMap.put(entry.getKey().toString(),new NodePrase((JSONObject) entry.getValue()));
        }
        /*for(Map.Entry<String, J> entry : jsonObject.entrySet()) {
            ////System.out.ut.println(entry.getKey()+entry.getValue());
            RespMap.put(entry.getKey(),new NodePrase(entry.getValue()));
        }*/
    }
    public  Map<String,Object > Handle(Map<String,Object > src,Map <String,Object > V ) {


        for (Map.Entry<String, NodePrase> entry : RespMap.entrySet())
        {
            V.put(entry.getKey(),entry.getValue().Handle(src));
        }
        return V;
    }
}

