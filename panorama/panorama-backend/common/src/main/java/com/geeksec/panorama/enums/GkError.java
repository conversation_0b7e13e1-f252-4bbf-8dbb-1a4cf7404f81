package com.geeksec.panorama.enums;

import lombok.Getter;

/**
 * @description: 业务异常提示
 * @author: shiwenxu
 * @createtime: 2023/8/30 09:46
 */
@Getter
public enum GkError {
  /**
   * 系统类异常
   */
  FAIL(400, "系统异常，请联系管理员！"),
  UNAUTHORIZED(401, "用户登录失效，请重新登录"),
  PARAM_LACK_ERROR(402, "缺少必填参数，请检查！"),
  REQUEST_OVERTIME(408, "请求超时"),
  REQUEST_ERROR(409, "请求方式有误，请检查"),
  REQUEST_PATH_ERROR(501, "请求路径不存在"),
  REQUEST_AUTH_ERROR(502, "权限不足"),
  LOGIN_PASSWORD_FAILED(503, "登录密码错误"),
  LOGOUT_ERROR(504, "用户登出失败"),
  MYSQL_EXECUTE_ERROR(600, "数据库执行异常"),
  SYSTEM_SHUTDOWN_ERROR(700, "服务器关机失败"),
  SYSTEM_REBOOT_ERROR(800, "服务器重启失败"),
  OLD_PASSWORD_ERROR(900, "修改密码失败：原密码输入错误"),
  OLD_PASSWORD_REPEAT(901, "修改密码失败：新密码与原始密码相同"),
  MODIFY_USER_PASSWORD_ERROR(902, "修改密码失败"),
  USER_NOT_EXIST(903, "用户不存在"),
  USER_NOT_LOGIN(904, "用户未登录"),
  USER_LOGIN_ERROR(905, "用户登录失败，请确认用户名密码"),

  /**
   * 用户权限参数类异常
   */
  TOKEN_EMPTY(1001, "token为空,请重新登录"),
  LOGIN_EXPIRE(1002, "登陆已过期,请重新登录"),
  REQUEST_PARAM_ERROR(1003, "请求参数格式异常,请检查"),
  REQUEST_PARAM_EMPTY(1004, "请求参数为空,请检查"),
  REQUEST_PARAM_LEAK(1005, "缺少必填参数"),
  QUERY_IP_PARAM_ERROR(1006, "IP类型查询参数格式异常,请检查"),
  IP_FORMAT_ERROR(1007, "IP地址格式错误，请检查"),
  LOGIN_STATUS_CHECK_FAIL(1008, "用户登录状态失效，请重新登录"),
  LOGIN_STATUS_CHECK_ERROR(1009, "用户登录状态校验异常"),
  LOGIN_STATUS_CHECK_PARAM_LEAK(1010, "用户登录状态校验参数缺失"),
  LOGIN_STATUS_CHECK_USER_EMPTY(1011, "当前获取到的用户信息为空"),
  LOGIN_STATUS_CHECK_REQUEST_FAIL(1014,"权限校验系统请求失败"),
  // 图数据库相关错误 (5xxx)
  GRAPH_DB_QUERY_ERROR(5000, "图数据库查询异常"),
  GRAPH_DB_PARSE_ERROR(5001, "图数据库解析异常"),
  GRAPH_DB_QUERY_EMPTY(5002, "图数据库查询结果为空"),
  GRAPH_PATH_QUERY_ERROR(5003, "子图路径查询失败"),
  GRAPH_EDGE_DIRECTION_ERROR(5004, "图边方向错误"),

  // 模型相关错误 (6xxx)
  MODEL_NOT_FOUND(6000, "模型不存在"),
  MODEL_RUN_RECORD_SAVE_FAILED(6001, "保存模型运行记录失败"),
  MODEL_RUN_NOT_FOUND(6002, "模型运行记录不存在"),
  MODEL_NAME_DUPLICATE(6003, "模型名称重复"),
  MODEL_RUN_RECORDS_QUERY(6004,"模型记录查询失败"),
  MODEL_NOT_CURRENT(6005,"模型不是可用版本"),

  // 自定义模型错误 (61xx)
  CUSTOM_MODEL_CREATE_FAILED(6100, "创建自定义攻击模型失败"),
  CUSTOM_MODEL_UPDATE_FAILED(6101, "更新自定义攻击模型失败"),
  CUSTOM_MODEL_DELETE_FAILED(6102, "删除自定义攻击模型失败"),
  CUSTOM_MODEL_NAME_DUPLICATE(6103, "自定义攻击模型名称重复"),
  CUSTOM_MODEL_BUSY(6104, "自定义攻击模型正在运行中"),
  CUSTOM_MODEL_INVALID(6105, "自定义攻击模型无效"),
  CUSTOM_MODEL_EXECUTE_FAILED(6106, "自定义攻击模型执行失败"),
  NGQL_EXECUTE_FAILED(6107, "执行nGQL语句失败"),

  // 预置模型错误 (62xx)
  PRESET_MODEL_UPDATE_FAILED(6200, "更新预置攻击模型失败"),
  PRESET_MODEL_TOGGLE_FAILED(6201, "切换预置攻击模型状态失败"),
  PRESET_MODEL_LIST_FAILED(6202, "查询预置攻击模型列表失败"),
  PRESET_MODEL_RESCHEDULE_FAILED(6203, "修改预置攻击模型执行间隔失败"),

  // 字典相关错误 (63xx)
  NODE_DICT_FETCH_FAILED(6300, "获取攻击模型节点字典失败"),
  EDGE_DICT_FETCH_FAILED(6301, "获取攻击模型关联边字典失败"),

  TASK_CREATE_FAILED(6400, "创建任务失败"),
  TASK_DELETE_FAILED(6401, "删除任务失败"),
  TASK_STATUS_UPDATE_FAILED(6402, "修改任务状态失败"),
  TASK_SETTING_UPDATE_FAILED(6403, "修改任务参数失败"),
  TASK_QUERY_FAILED(6404, "查询任务失败"),
  TASK_NOT_FOUND_FAILED(6405, "任务不存在"),
  TASK_STATUS_NOT_EXIST(6406, "任务状态不可用"),
  TASK_TYPE_NOT_EXIST(6407, "任务类型不可用"),
  JOB_CREATE_FAILED(6408, "创建模型作业失败"),
  UNKNOWN_TIME_UNIT(6409, "不支持的时间间隔设置类型"),
  TASK_STATUS_NOT_CHANGED(6410, "任务状态无需更新"),
  JOB_NOT_FOUND_FAILED(6411, "任务无对应作业"),
  JOB_UPDATE_FAILED(6412, "更新作业参数失败"),
  JOB_SETTING_FAILED(6413, "作业参数设置失败"),
  // 攻击事件相关错误 (7xxx)
  ATTACK_EVENT_SAVE_FAILED(7000, "保存攻击事件失败"),
  ATTACK_EVENT_NOT_FOUND(7001, "攻击事件不存在"),
  // 参数校验
  EXTRA_PARAMETER_ERROR(9001, "单次任务不需要额外参数设置"),
  LOOP_PARAMETER_ERROR(9002, "循环任务必需指定正确的间隔时间和单位"),
  SCHEDULED_PARAMETER_ERROR(9003, "定时任务cron表达式缺失或校验错误"),
  // 其他业务错误 (8xxx)
  MODEL_SWITCH_TYPE_ERROR(8000, "模型开关修改失败：模型类型错误"),
  QUERYABLE_STATE_NOT_FOUND(8001, "查询告警统计结果失败"),
  JOB_NOT_FOUND(8002, "Flink任务未找到"),
  IOCTYPE_NOT_SUPPORT(8003, "不支持查询的IoC类型"),
  APT_NOT_FOUND(8004, "APT情报为空"),
  INIT_LABEL_INFO_ERROR(8005, "初始化图类标签失败"),
  TASK_OWNER_NOT_MATCH(6414, "当前用户不允许操作此任务"),
  TASK_EXECUTION_TYPE_EMPTY(6415, "修改时需指定任务类型"),
  TASK_NAME_INVALID(6416, "任务名称不可用");

  private final Integer code;
  private final String message;

  GkError(Integer code, String message) {
    this.code = code;
    this.message = message;
  }
}
