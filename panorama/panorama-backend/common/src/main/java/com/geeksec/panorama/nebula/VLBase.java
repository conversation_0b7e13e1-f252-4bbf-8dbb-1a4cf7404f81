package com.geeksec.panorama.nebula;

import java.util.HashMap;
import java.util.Map;

class SqlParse {

    public String SQL = "";
    public Map<String, String> AliasVMap = new HashMap<>();  //  别+
    private int Vnum = 1;
    private int Enum = 1;

    public String GetVateAlias(String SName) {
        if (AliasVMap.containsKey(SName)) {
            return AliasVMap.get(SName);
        } else {
            String AliasVName = "v" + String.valueOf(Vnum);
            Vnum++;
            AliasVMap.put(SName, AliasVName);
            return AliasVName;
        }
    }

    public String GetEdgeAlias(String SName) {
        if (AliasVMap.containsKey(SName)) {
            return AliasVMap.get(SName);
        } else {
            String AliasVName = "e" + String.valueOf(Enum);
            Vnum++;
            AliasVMap.put(SName, AliasVName);
            return AliasVName;
        }
    }

    public String GetAlias(String name) {
        if (AliasVMap.contains<PERSON>ey(name)) {
            return AliasVMap.get(name);
        }
        return null;
    }

    public String LastVType = "";
}

public class VLBase {
    public String Name = "";
    public String Type = "";

    public SqlParse Parse(SqlParse PSql) {
        return PSql;

    }

    public String getType() {
        return Type;
    }

    public void setType(String type) {
        this.Type = type;
    }

    public SqlParse TParse(SqlParse PSql) {

        Parse(PSql);
        PSql.LastVType = Type;
        return PSql;
    }

}
