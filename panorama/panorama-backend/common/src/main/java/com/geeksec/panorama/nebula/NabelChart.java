package com.geeksec.panorama.nebula;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class NabelChart {
    //private PropertiesLoader propertiesLoader = new PropertiesLoader("classpath:config.properties");
    //private String hbaseHost = propertiesLoader.getProperty("hbase.zookeeper.host");
    private Map<String, List<QueryFetchQue>> ReqTypeMap = new HashedMap(); //
    private Map<String, QueryFetchQue> ReqVEMap = new HashedMap(); //

    private static List<String> IP_END_EDGE_LIST = Lists.newArrayList("parse_to", "cname_result");
    private static List<String> MAC_END_EDGE_LIST = Lists.newArrayList("src_bind", "dst_bind");
    private static List<String> DOMAIN_END_EDGE_LIST = Lists.newArrayList("client_query_domain", "dns_server_domain", "client_ssl_connect_domain", "server_ssl_connect_domain", "client_http_connect_domain", "server_http_connect_domain", "sslfinger_connect_domain", "sslfinger_connect_cert");
    private static List<String> CERT_END_EDGE_LIST = Lists.newArrayList("sslfinger_connect_cert", "server_use_cert", "client_use_cert", "client_connect_cert", "sni_bind");
    private static List<String> ORG_END_EDGE_LIST = Lists.newArrayList("ip_belong_to_org", "domain_belong_to_org", "cert_belong_to_org");
    private static List<String> URL_END_EDGE_LIST = Lists.newArrayList("ip_url_related", "domain_url_related", "cert_url_related");

    public NabelChart(String str) {
        JSONArray array = JSON.parseArray(str);
        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = (JSONObject) array.get(i);
            String type = obj.getString("type");
            List<QueryFetchQue> hList = new ArrayList<>();
            JSONArray handle_list = obj.getJSONArray("handle");
            for (int ii = 0; ii < handle_list.size(); ii++) {
                QueryFetchQue p = new QueryFetchQue();
                p.Parse((JSONObject) handle_list.get(ii));
                hList.add(p);
            }
            ReqTypeMap.put(type, hList);
        }
        JSONArray array2 = JSON.parseArray(str);
        for (int i = 0; i < array2.size(); i++) {
            JSONObject obj = (JSONObject) array2.get(i);
            String type = obj.getString("type");
            JSONArray handle_list = obj.getJSONArray("handle");
            for (int ii = 0; ii < handle_list.size(); ii++) {

                QueryFetchQue p = new QueryFetchQue();
                JSONObject ht = (JSONObject) handle_list.get(ii);
                String Key = type + "@" + ht.getString("edge");
                p.Parse(ht);
                ReqVEMap.put(Key, p);
            }

        }
    }

    //private String hbasePort = propertiesLoader.getProperty("hbase.zookeeper.port");
    private JSONArray ConfParse(String Filenaname) {
        try {
            File file = new File(Filenaname);
            FileReader fileReader = new FileReader(file);
            Reader reader = new InputStreamReader(new FileInputStream(file), "Utf-8");
            int ch = 0;
            StringBuffer sb = new StringBuffer();
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            fileReader.close();
            reader.close();
            String jsonStr = sb.toString();
            return JSONArray.parseArray(jsonStr);
        } catch (FileNotFoundException | UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public List<Map<String, Object>> SqlHandle(String type, Map<String, Object> req, NebulaDB db) {
        List<Map<String, Object>> reslList = new ArrayList<>();
        if (this.ReqTypeMap.containsKey(type)) {
            List<QueryFetchQue> QueryList = this.ReqTypeMap.get(type);
            for (QueryFetchQue Query : QueryList) {
                String sql = Query.SqlCmd(req);
                reslList.addAll(db.Select(sql));
            }
            return reslList;
        }
        return null;
    }

    public Map<String, Object> Handle(String type, Map<String, Object> req, NebulaDB db) {
        if (this.ReqTypeMap.containsKey(type)) {
            Map<String, Object> RM = new HashMap<>();
            List<Object> VLsit = new ArrayList<>();
            List<Object> EList = new ArrayList<>();
            VEMap pVEMAap = new VEMap();
            List<QueryFetchQue> QueryList = this.ReqTypeMap.get(type);
            for (QueryFetchQue Query : QueryList) {
                String sql = Query.SqlCmd(req);
                Query.ChartByResp(db.Select(sql), VLsit, EList, pVEMAap);
            }
            RM.put("vertex", VLsit);
            RM.put("edge", EList);
            return RM;
        }
        return null;
    }

    public void EVHandle(String Vet, String Tbv, Map<String, Object> req, NebulaDB db, List<Object> VLsit, List<Object> EList, VEMap pVEMAap) {
        String type = Vet + "_" + Tbv;
        if (this.ReqTypeMap.containsKey(type)) {
            List<QueryFetchQue> QueryList = this.ReqTypeMap.get(type);
            for (QueryFetchQue Query : QueryList) {
                String sql = Query.SqlCmd(req);
                sql = handleWeightLimit(sql, Vet, Tbv, req);
                Query.ChartByResp(db.Select(sql), VLsit, EList, pVEMAap);
            }

        }

    }


    // Vet  查询的类型 IP/MAC 等 ， reqMap< 关系 ，值类型(包括 类型值 和 limit) > reqMap 连接
    public Map<String, Object> EVHandleList(String Vet, Map<String, Map<String, Object>> reqMap, NebulaDB db) {
        List<Object> VLsit = new ArrayList<>();
        List<Object> EList = new ArrayList<>();

        VEMap pVEMAap = new VEMap();
        for (String key : reqMap.keySet()) {
            EVHandle(Vet, key, reqMap.get(key), db, VLsit, EList, pVEMAap);
        }
        Map<String, Object> RM = new HashMap<>();
        RM.put("vertex", VLsit);
        RM.put("edge", EList);
        return RM;
    }


    /**
     * 处理黑名单权重Where拼接参数
     *
     * @param sql
     * @param originType
     * @param edgeType
     * @param req
     * @return
     */
    private String handleWeightLimit(String sql, String originType, String edgeType, Map<String, Object> req) {
        Integer[] weightLimit = (Integer[]) req.get("weight_limit");
        Integer blackStart = weightLimit[0];
        Integer blackEnd = weightLimit[1];
        if (blackStart == 0 && blackEnd == 100) {
            // 若权值范围为0~100，则查询的为全部关联节点
            return sql;
        }

        // 设置通配黑名单权值查询语句(最后留空格)
        String wildcard = "AND #{vid}.#{tag}.black_list >= " + blackStart.toString() + " AND #{vid}.#{tag}.black_list <= " + blackEnd.toString() + " ";

        StringBuilder sqlBuileder = new StringBuilder(sql);
        int insertIndex = sqlBuileder.indexOf("RETURN");

        // 根据起始节点，对其语句进行权值条件添加处理
        switch (originType) {
            // 以IP作为起始节点
            case "IP":
                if (MAC_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为MAC
                    wildcard = wildcard.replace("#{vid}", "MAC").replace("#{tag}", "MAC");
                } else if (DOMAIN_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为DOMAIN
                    wildcard = wildcard.replace("#{vid}", "DOMAIN").replace("#{tag}", "DOMAIN");
                } else if (CERT_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为CERT
                    wildcard = wildcard.replace("#{vid}", "CERT").replace("#{tag}", "CERT");
                } else if (ORG_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为ORG
                    wildcard = wildcard.replace("#{vid}", "ORG").replace("#{tag}", "ORG");
                } else if (URL_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为URL
                    wildcard = wildcard.replace("#{vid}", "URL").replace("#{tag}", "URL");
                } else if ("connect".equals(edgeType) || "r_connect".equals(edgeType) || "client_query_dns_server".equals(edgeType) || "r_client_query_dns_server".equals(edgeType)) {
                    // 关联终点为IP本身
                    wildcard = wildcard.replace("#{vid}", "IP1").replace("#{tag}", "IP");
                } else if (IP_END_EDGE_LIST.equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "IP").replace("#{tag}", "IP");
                } else {
                    wildcard = StringUtils.EMPTY;
                }
                break;
            case "MAC":
                if (MAC_END_EDGE_LIST.contains(edgeType)) {
                    // 反向查询IP节点
                    wildcard = wildcard.replace("{vid}", "IP").replace("#{tag}", "IP");
                } else if ("connect_mac".equals(edgeType) || "r_connect_mac".equals(edgeType)) {
                    // MAC之间通信
                    wildcard = wildcard.replace("#{vid}", "MAC1").replace("#{tag}", "MAC");
                }
                break;
            case "DOMAIN":
                if (IP_END_EDGE_LIST.contains(edgeType)) {
                    // 关联节点为IP
                    wildcard = wildcard.replace("#{vid}", "IP").replace("#{tag}", "IP");
                } else if (ORG_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为ORG
                    wildcard = wildcard.replace("#{vid}", "ORG").replace("#{tag}", "ORG");
                } else if (CERT_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为CERT
                    wildcard = wildcard.replace("#{vid}", "CERT").replace("#{tag}", "CERT");
                } else if ("cname".equals(edgeType) || "r_canme".equals(edgeType)) {
                    // 关联节点为DOMAIN本身
                    wildcard = wildcard.replace("#{vid}", "DOMAIN1").replace("#{tag}", "DOMAIN");
                } else if (URL_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为URL
                    wildcard = wildcard.replace("#{vid}", "URL").replace("#{tag}", "URL");
                } else if ("client_query_domain".equals(edgeType) || "dns_server_domain".equals(edgeType) || "client_ssl_connect_domain".equals(edgeType) || "server_ssl_connect_domain".equals(edgeType) || "client_http_connect_domain".equals(edgeType) || "server_http_connect_domain".equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "IP").replace("#{tag}", "IP");
                } else if ("sslfinger_connect_domain".equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "SSLFINGER").replace("#{tag}", "SSLFINGER");
                } else {
                    wildcard = StringUtils.EMPTY;
                }
                break;
            case "SSLFINGER":
                if (DOMAIN_END_EDGE_LIST.contains(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "DOMAIN").replace("#{tag}", "DOMAIN");
                } else if (CERT_END_EDGE_LIST.contains(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "CERT").replace("#{tag}", "CERT");
                } else {
                    wildcard = StringUtils.EMPTY;
                }
                break;
            case "CERT":
                if (URL_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为URL
                    wildcard = wildcard.replace("#{vid}", "URL").replace("#{tag}", "URL");
                } else if (ORG_END_EDGE_LIST.contains(edgeType)) {
                    // 关联终点为ORG
                    wildcard = wildcard.replace("#{vid}", "ORG").replace("#{tag}", "ORG");
                } else if ("client_use_cert".equals(edgeType) || "client_connect_cert".equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "IP").replace("#{tag}", "IP");
                } else if ("sni_bind".equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "DOMAIN").replace("#{tag}", "DOMAIN");
                } else if ("sslfinger_connect_cert".equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "SSLFINGER").replace("#{tag}", "SSLFINGER");
                } else {
                    wildcard = StringUtils.EMPTY;
                }
                break;
            case "ORG":
                // 只能为反向查询,统一替换关联类型
                String tagType = edgeType.split("_")[0].toUpperCase();
                wildcard = wildcard.replace("#{vid}", tagType).replace("#{tag}", tagType);
                break;
            case "FDOMAIN":
                wildcard = wildcard.replace("#{vid}", "DOMAIN").replace("#{tag}", "DOMAIN");
                break;
            case "UA":
                if ("ua_connect_domain".equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "DOMAIN").replace("#{tag}", "DOMAIN");
                } else if ("client_use_ua".equals(edgeType)) {
                    wildcard = wildcard.replace("#{vid}", "IP").replace("#{tag}", "IP");
                } else {
                    wildcard = StringUtils.EMPTY;
                }
                break;
            default:
                wildcard = StringUtils.EMPTY;
        }

        sqlBuileder.insert(insertIndex, wildcard);
        return sqlBuileder.toString();
    }


}
