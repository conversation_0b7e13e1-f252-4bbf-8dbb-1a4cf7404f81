package com.geeksec.panorama.nebula;

import com.vesoft.nebula.client.graph.NebulaPoolConfig;
import com.vesoft.nebula.client.graph.data.HostAddress;
import com.vesoft.nebula.client.graph.data.ResultSet;
import com.vesoft.nebula.client.graph.data.ValueWrapper;
import com.vesoft.nebula.client.graph.exception.InvalidValueException;
import com.vesoft.nebula.client.graph.net.NebulaPool;
import com.vesoft.nebula.client.graph.net.Session;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;

// 生成


// nabuel   json 转换成语句 ####
@Configuration
@EnableConfigurationProperties(NebulaProperties.class)
@Component
public class NebulaDB {

    private static final Logger logger = LoggerFactory.getLogger(NebulaDB.class);

    private NebulaPool pool;
    private NebulaPoolConfig nebulaPoolConfig;
    @Autowired
    private NebulaProperties nebulaProperties;

    // 模拟连接池，使用队列进行控制
    private static transient LinkedBlockingQueue<Session> sessionQueue = new LinkedBlockingQueue();
    private static Integer sessionQueueSize = 20;

    @Value("${enabled.nebula}")
    private Boolean enabledNebula;

    public NebulaDB() {

    }

//    @Bean(name = "nebulaDBOb")
    public NebulaPool nebulaDBFactory(NebulaProperties nebulaProperties) {
        try {
            if (enabledNebula) {
                pool = new NebulaPool();
                nebulaPoolConfig = new NebulaPoolConfig();
                DBConnect(nebulaProperties.getIp(), nebulaProperties.getPort(), nebulaProperties.getUsername(), nebulaProperties.getPassword(), nebulaProperties.getSpaceName());
                initSessionQueue();
                logger.info("初始化NebulaPool成功", pool.getIdleConnNum());
            }
        } catch (Exception e) {
            logger.error("初始化Nebula连接池失败！error->", e);
            e.printStackTrace();
        }
        return pool;
    }

    // 每一个小时执行一次
    @Scheduled(cron = "0 0/30 * * * ?")
    private void testSessionLiving() {
        try {
            // 是否重新
            Boolean isVaild = true;
            logger.info("定时检测Nebula连接是否生效,当前时间-->{}",new Date());
            // 检测session队列中的session是否有效
            for (Session session : sessionQueue) {
                if (!session.ping()) {
                    session.release();
                    sessionQueue.remove(session);
                    sessionQueue.put(pool.getSession(nebulaProperties.getUsername(), nebulaProperties.getPassword(), false));
                    continue;
                }
                session.execute("USE " + nebulaProperties.getSpaceName() + ";");
                ResultSet resp = session.execute("YIELD 1");
                if (!resp.isSucceeded()) {
                    logger.info("session查询已失效，重新新建session");
                    // 移除失效session，生成新session至队列中
                    session.release();
                    sessionQueue.remove(session);
                    sessionQueue.put(pool.getSession(nebulaProperties.getUsername(), nebulaProperties.getPassword(), false));
                    isVaild = false;
                } else {
                    continue;
                }
            }
        } catch (Exception e) {
            logger.error("检测session队列中session可用性失败,error->", e);
            e.printStackTrace();
        }
    }

    private void initSessionQueue() {
        // 初始化Nebula 连接Session队列
        try {
            sessionQueue.clear();
            for (int num = 0; num < sessionQueueSize; num++) {
                sessionQueue.put(pool.getSession(nebulaProperties.getUsername(), nebulaProperties.getPassword(), false));
            }
            logger.info("初始化Session Queue成功，队列长度:{}", sessionQueue.size());
        } catch (Exception e) {
            logger.error("初始化Session Queue队列失败！error:", e);
            e.printStackTrace();
        }
    }

    private void DBConnect(String ip, int port, String UserName, String Passwd, String SpaceName) {

        nebulaPoolConfig.setMaxConnSize(200);
        nebulaPoolConfig.setMinConnSize(10);
        nebulaPoolConfig.setIdleTime(604800);
        nebulaPoolConfig.setTimeout(300000);

        List<HostAddress> addresses = Arrays.asList(new HostAddress(ip, port));
        try {
            Boolean initResult = pool.init(addresses, nebulaPoolConfig);
            if (!initResult) {
                System.out.println("pool init failed.");
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<Map<String, Object>> Do(String sql) {
        List<Map<String, Object>> ResultList = null;
        if (StringUtils.isNotBlank(sql)) {
            ResultList = Select(sql);
        }
        return ResultList;
    }


    private static List<Map<String, Object>> ResultToJson(ResultSet resultSet) throws UnsupportedEncodingException {
        List<String> colNames = resultSet.keys();
        List<Map<String, Object>> ResultMapList = new ArrayList<>();
        //System.out.println();
        for (int i = 0; i < resultSet.rowsSize(); i++) {
            ResultSet.Record record = resultSet.rowValues(i);
            Map<String, Object> ResultMap = new HashMap<>();
            int num = 0;
            for (ValueWrapper value : record.values()) {
                if (value.isLong()) {
                    //System.out.printf("%15s |", value.asLong());
                    ResultMap.put(colNames.get(num), value.asLong());
                }
                if (value.isBoolean()) {
                    //System.out.printf("%15s |", value.asBoolean());
                    ResultMap.put(colNames.get(num), value.asBoolean());
                }
                if (value.isDouble()) {
                    //System.out.printf("%15s |", value.asDouble());
                    ResultMap.put(colNames.get(num), value.asDouble());
                }
                if (value.isString()) {
                    //System.out.printf("%15s |", value.asString());
                    ResultMap.put(colNames.get(num), value.asString());
                }
                if (value.isTime()) {
                    //System.out.printf("%15s |", value.asTime());
                    ResultMap.put(colNames.get(num), value.asTime());
                }
                if (value.isDate()) {
                    //System.out.printf("%15s |", value.asDate());
                    ResultMap.put(colNames.get(num), value.asDate());
                }
                if (value.isDateTime()) {
                    //System.out.printf("%15s |", value.asDateTime());
                    ResultMap.put(colNames.get(num), value.asDateTime());
                }
                if (value.isVertex()) {
                    //System.out.printf("%15s |", value.asNode());
                    ResultMap.put(colNames.get(num), value.asNode());
                }
                if (value.isEdge()) {
                    //System.out.printf("%15s |", value.asRelationship());
                    ResultMap.put(colNames.get(num), value.asRelationship());
                }
                if (value.isPath()) {
                    //System.out.printf("%15s |", value.asPath());
                    ResultMap.put(colNames.get(num), value.asPath());
                }
                if (value.isList()) {
                    //System.out.printf("%15s |", value.asList());
                    ResultMap.put(colNames.get(num), value.asList());
                }
                if (value.isSet()) {
                    //System.out.printf("%15s |", value.asSet());
                    ResultMap.put(colNames.get(num), value.asSet());
                }
                if (value.isMap()) {
                    //System.out.printf("%15s |", value.asMap());

                    ResultMap.put(colNames.get(num), asMap(value.getValue()));
                }
                num++;
            }
            //System.out.println();
            ResultMapList.add(ResultMap);
        }
        return ResultMapList;
    }

    private static String decodeType = "utf-8";
    private static int timezoneOffset = 0;

    public static HashMap<String, Object> asMap(com.vesoft.nebula.Value value) throws InvalidValueException, UnsupportedEncodingException {

        if (value.getSetField() != com.vesoft.nebula.Value.MVAL) {
            throw new InvalidValueException("Cannot get field type `set' because value's type is " + descType(value));
        }
        HashMap<String, Object> kvs = new HashMap<>();
        Map<byte[], com.vesoft.nebula.Value> inValues = value.getMVal().getKvs();
        for (byte[] key : inValues.keySet()) {
            String k1 = new String(key, decodeType);
            com.vesoft.nebula.Value q_v = inValues.get(key);
            ValueWrapper v1 = new ValueWrapper(q_v, decodeType, timezoneOffset);
            if (v1.isString()) {
                String s = v1.toString();
                s = s.substring("\"".length());
                s = s.substring(0, s.length() - "\"".length());
                kvs.put(k1, s);
            } else {
                kvs.put(k1, v1.toString());
            }

        }
        return kvs;
    }

    private static String descType(com.vesoft.nebula.Value value) {
        switch (value.getSetField()) {
            case com.vesoft.nebula.Value.NVAL:
                return "NULL";
            case com.vesoft.nebula.Value.BVAL:
                return "BOOLEAN";
            case com.vesoft.nebula.Value.IVAL:
                return "INT";
            case com.vesoft.nebula.Value.FVAL:
                return "FLOAT";
            case com.vesoft.nebula.Value.SVAL:
                return "STRING";
            case com.vesoft.nebula.Value.DVAL:
                return "DATE";
            case com.vesoft.nebula.Value.TVAL:
                return "TIME";
            case com.vesoft.nebula.Value.DTVAL:
                return "DATETIME";
            case com.vesoft.nebula.Value.VVAL:
                return "VERTEX";
            case com.vesoft.nebula.Value.EVAL:
                return "EDGE";
            case com.vesoft.nebula.Value.PVAL:
                return "PATH";
            case com.vesoft.nebula.Value.LVAL:
                return "LIST";
            case com.vesoft.nebula.Value.MVAL:
                return "MAP";
            case com.vesoft.nebula.Value.UVAL:
                return "SET";
            case com.vesoft.nebula.Value.GVAL:
                return "DATASET";
            case com.vesoft.nebula.Value.GGVAL:
                return "GEOGRAPHY";
            case com.vesoft.nebula.Value.DUVAL:
                return "DURATION";
            default:
                throw new IllegalArgumentException("Unknown field id " + value.getSetField());
        }
    }

    public List<Map<String, Object>> Select(String query) {
        Session session = null;
        try {
            session = sessionQueue.take();
            session.execute("USE " + nebulaProperties.getSpaceName() + ";");
            ResultSet resp = session.execute(query);
            if (!resp.isSucceeded()) {
                System.out.println(String.format("Execute: `%s', failed: %s", query, resp.getErrorMessage()));
                //System.exit(1);
            }
            List<Map<String, Object>> maps = ResultToJson(resp);
            sessionQueue.put(session);
            return maps;
        } catch (Exception e) {
            e.printStackTrace();
            return null;

        }
    }

}
