/**
 * Copyright (c) 2016-2019 人人开源 All rights reserved.
 *
 * <p>https://www.renren.io
 *
 * <p>版权所有，侵权必究！
 */
package com.geeksec.panorama.exception;

import com.geeksec.panorama.enums.GkError;
import lombok.Data;

/**
 * @description: 自定义异常
 * @author: shiwenxu
 * @createtime: 2023/8/30 09:46
 */
@Data
public class GkException extends RuntimeException {

  /** 错误码 */
  private Integer code;

  /** 错误信息 */
  private String msg;

  /** 异常枚举 */
  private GkError error;

  /** 登录失败返回URL */
  private String url;

  public GkException(GkError error) {
    super(error.getMessage());
    this.error = error;
    this.code = error.getCode();
    this.msg = error.getMessage();
  }

  public GkException(GkError error, String url) {
    super(error.getMessage());
    this.error = error;
    this.code = error.getCode();
    this.msg = error.getMessage();
    this.url = url;
  }
}
