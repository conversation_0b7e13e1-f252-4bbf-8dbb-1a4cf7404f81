package com.geeksec.panorama.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.geeksec.panorama.entity.Property;
import com.geeksec.panorama.entity.PropertyHolder;
import com.geeksec.panorama.enums.VertexType;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Getter
public enum EdgeType implements PropertyHolder {
    MAKE_ATTACK(
            1, "make_attack", "发起攻击", VertexType.IP, VertexType.ATTACK, new ArrayList<>()),
    ATTACK_TO(
            2, "attack_to", "攻击目标", VertexType.ATTACK, VertexType.IP, new ArrayList<>()),
    IP_BELONG_TO_APT(
            3, "ip_belong_to_apt", "IP关联APT组织", VertexType.IP, VertexType.APT_GROUP, new ArrayList<>()),
    IP_BELONG_TO_ORG(
            4, "ip_belong_to_org", "IP归属机构", VertexType.IP, VertexType.APT_GROUP, new ArrayList<>()),
    DOMAIN_BELONG_TO_APT(
            5, "domain_belong_to_apt", "域名关联APT组织", VertexType.DOMAIN, VertexType.APT_GROUP, new ArrayList<>()),
    APT_JUDGE_IP(
            6, "apt_judge_ip", "模型研判关联IP", VertexType.IP, VertexType.APT_GROUP, Arrays.asList(
            new Property("model_name", "模型名称", "set", false)
    )),
    APT_JUDGE_DOMAIN(
            7, "apt_judge_domain", "模型研判关联域名", VertexType.DOMAIN, VertexType.APT_GROUP, Arrays.asList(
            new Property("model_name", "模型名称", "set", false)
    )),
    HTTP_CONNECTION(
            8, "http_connect", "HTTP连接请求", VertexType.IP, VertexType.IP, Arrays.asList(
            new Property("uri", "资源标识符", "string", true),
            new Property("host", "主站名称", "string", true),
            new Property("cookie", "Cookie", "string", false),
            new Property("agent", "用户代理", "string", true),
            new Property("referer", "来源地址", "string", false),
            new Property("xff", "代理头", "string", false),
            new Property("req_data", "请求数据", "string", false),
            new Property("rsp_data", "响应数据", "string", false),
            new Property("method", "请求方法", "string", true),
            new Property("status", "状态码", "int", true),
            new Property("content_type", "内容类型", "string", false))),

    CLIENT_USE_UA(
            9, "client_use_ua", "客户端使用代理", VertexType.IP, VertexType.UA, new ArrayList<>()),
    UA_CONNECT_DOMAIN(
            10, "ua_connect_domain", "代理访问域名", VertexType.UA, VertexType.DOMAIN, new ArrayList<>()),
    CLIENT_HTTP_CONNECT_DOMAIN(
            11, "client_http_connect_domain", "客户端访问域名", VertexType.IP, VertexType.DOMAIN, new ArrayList<>()),
    SERVER_HTTP_CONNECT_DOMAIN(
            12, "server_http_connect_domain", "服务端部署HTTP服务", VertexType.IP, VertexType.DOMAIN, new ArrayList<>()),
    CLIENT_QUERY_DOMAIN(
            13, "client_query_domain", "客户端请求域名解析", VertexType.IP, VertexType.DOMAIN, Arrays.asList(
            new Property("dns_type", "DNS类型", "string", true),
            new Property("answer_type", "回答类型", "string", true))),
    CLIENT_QUERY_DNS_SERVER(
            14, "client_query_dns_server", "客户端请求DNS服务器", VertexType.IP, VertexType.IP, Arrays.asList()),
    DNS_SERVER_RESOLVES_DOMAIN(
            15, "dns_server_resolves_domain", "DNS服务器解析域名", VertexType.IP, VertexType.DOMAIN, Arrays.asList(
            new Property("dns_type", "DNS类型", "string", false),
            new Property("answer_type", "回答类型", "string", false))),
    PARSE_TO(
            16, "parse_to", "域名解析到IP", VertexType.DOMAIN, VertexType.IP, Arrays.asList(
            new Property("dns_server", "DNS服务器", "string", true),
            new Property("final_parse", "最终解析", "boolean", true))),
    CLIENT_USE_CERT(
            17, "client_use_cert", "客户端使用证书", VertexType.IP, VertexType.CERT, new ArrayList<>()
    ),
    SERVER_USE_CERT(
            18, "server_use_cert", "服务端使用证书", VertexType.IP, VertexType.CERT, new ArrayList<>()
    ),
    CERT_CONNECT_ORG(
            19, "cert_belong_to_org", "证书关联机构", VertexType.CERT, VertexType.ORG, new ArrayList<>()
    ),
    CERT_CONNECT_SNI(
            20, "cert_connect_sni", "证书关联SNI", VertexType.CERT, VertexType.DOMAIN, new ArrayList<>()
    ),
    CERT_CONNECT_SSLFINGER(
            21, "cert_connect_sslfinger", "证书关联SSL指纹", VertexType.CERT, VertexType.SSLFINGER, new ArrayList<>()
    ),
    CLIENT_USE_SSLFINGER(
            22, "client_use_sslfinger", "客户端使用SSL指纹", VertexType.IP, VertexType.SSLFINGER, new ArrayList<>()
    ),
    SERVER_USE_SSLFINGER(
            23, "server_use_sslfinger", "服务端使用SSL指纹", VertexType.IP, VertexType.SSLFINGER, new ArrayList<>()
    ),
    SSLFINGER_CONNECT_SNI(
            24, "sslfinger_connect_sni", "指纹关联SNI", VertexType.SSLFINGER, VertexType.DOMAIN, new ArrayList<>()
    ),
    SENDS_MAIL(
            25, "send_mail", "发送邮件", VertexType.EMAIL, VertexType.MAIL, new ArrayList<>()),

    RECEIVES_MAIL(
            26, "receive_mail", "接收邮件", VertexType.MAIL, VertexType.EMAIL, new ArrayList<>()),
    INCLUDE_FILE(
            27, "include_file", "邮件附带文件", VertexType.MAIL, VertexType.FILE, Arrays.asList(
            new Property("file_name", "文件名称", "string", true)
    )),
    SEND_FILE(
            28, "send_file", "攻击者传输文件", VertexType.IP, VertexType.FILE, Arrays.asList()),
    RECEIVES_FILE(
            29, "receives_file", "沙箱系统接收文件", VertexType.FILE, VertexType.IP, Arrays.asList()
    ),
    FILE_CONNECT_IP(
            30,"file_connect_ip","恶意文件关联IOC-IP",VertexType.FILE,VertexType.IP,Arrays.asList()
    ),
    FILE_CONNECT_DOMAIN(
            31,"file_connect_ip","恶意文件关联IOC-域名",VertexType.FILE,VertexType.DOMAIN,Arrays.asList()
    ),
    FILE_CONNECT_URL(
            32,"file_connect_ip","恶意文件关联IOC-URL",VertexType.FILE,VertexType.URL,Arrays.asList()
    ),
    ATTACK_BELONG_TO_APT_GROUP(
            33,"attack_belong_to_apt_group","攻击归属APT组织",VertexType.ATTACK,VertexType.APT_GROUP,Arrays.asList()
    ),
    APP_CONNECT_CERT(
            34,"app_connect_cert","应用关联证书",VertexType.APP,VertexType.CERT,Arrays.asList()),
    CLIENT_USE_APP(
            35,"client_use_app","客户端使用APP",VertexType.APP,VertexType.CERT,Arrays.asList()),
    APP_BELONG_TO_SERVER(
            36,"app_belong_to_server","APP归属于服务端IP",VertexType.APP,VertexType.CERT,Arrays.asList()),
    RELATED_TO_LABEL(
            37, "has_label", "关联标签", VertexType.IP, VertexType.LABEL, new ArrayList<>());

    @EnumValue
    private final int code;
    private final String name;
    private final String desc;
    private final VertexType source;
    private final VertexType destination;
    private final List<Property> properties;

    EdgeType(
            int code,
            String name,
            String desc,
            VertexType source,
            VertexType destination,
            List<Property> properties) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.source = source;
        this.destination = destination;
        this.properties = properties;
    }

    @JsonValue
    public int getCode() {
        return code;
    }

    @JsonCreator
    public static EdgeType fromCode(int code) {
        for (EdgeType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid EdgeType code: " + code);
    }

    public static EdgeType fromName(String name) {
        for (EdgeType type : values()) {
            if (Objects.equals(type.name, name)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid EdgeType name: " + name);
    }
}
