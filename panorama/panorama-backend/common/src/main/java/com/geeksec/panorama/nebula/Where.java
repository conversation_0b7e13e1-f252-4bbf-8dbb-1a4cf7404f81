package com.geeksec.panorama.nebula;

import java.util.ArrayList;
import java.util.List;

public class Where {
    private String VLName;  // 节点名称
    private String Flied = "id"; // 字段名称 。默认是ID
    private String Opr;
    private String v = "";
    private Integer Val = 0;
    private Long LVal = 0L;
    private String sVal = "";
    private List<Object> sValueList = new ArrayList<>();

    public Where(String sVLName, Object... sValueMap) {
        VLName = sVLName;

        for (Object e : sValueMap) {
            sValueList.add(e);
        }
        ////System.out.ut.println("where data type === int");
    }

    private String Value2Str(Object param) {
        ////System.out.ut.println("v=== "+v);
        if (param instanceof Integer) {
            return String.valueOf((Integer) param);

        } else if (param instanceof String) {
            return "'" + (String) param + "'";

        } else if (param instanceof Float) {
            return String.valueOf((Float) param);
        } else if (param instanceof Double) {
            return String.valueOf((Double) param);
        }
        return "";
    }

    public SqlParse Parse(SqlParse PSql) {
        String par = VLName;
        for (Object e : sValueList) {
            String t = Value2Str(e);
            par = par.replace("?", t);
        }
        PSql.SQL += par;
        return PSql;
    }

}
