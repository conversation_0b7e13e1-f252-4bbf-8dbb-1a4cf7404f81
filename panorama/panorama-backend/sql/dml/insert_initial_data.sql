INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('43cb706d-c5b6-4acf-9a4b-f87d9a0aa8fe', '侦查攻击聚合', 2, 4, '侦查攻击聚合是一种网络安全分析技术，它将多个来源的侦查活动（如扫描打点、端口探测、漏洞扫描等）的数据汇总并进行关联分析。这种方法通过整合和分析大量的侦查尝试，帮助安全团队识别出潜在的攻击模式、攻击者偏好和正在被针对的系统弱点。通过聚合这些数据，安全人员可以更全面地了解威胁态势，提前发现可能的攻击准备活动，从而采取更有效的防御措施，提高整体网络安全防护能力', 'f', 30, 1, '{"edges": [{"id": "e0", "type": 14, "dstId": "a1", "srcId": "aip", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e1", "type": 15, "dstId": "vip1", "srcId": "a1", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e2", "type": 14, "dstId": "a2", "srcId": "vip1", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e3", "type": 15, "dstId": "vip2", "srcId": "a2", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e4", "type": 14, "dstId": "a3", "srcId": "vip2", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e4", "type": 15, "dstId": "vip3", "srcId": "a3", "dstType": 1, "filters": [], "srcType": 10}], "limit": 10, "vertices": [{"id": "aip", "type": 1, "filters": []}, {"id": "a1", "type": 10, "filters": [{"value": "远控木马", "operator": 1, "property": "attack_type_msg"}]}, {"id": "vip1", "type": 1, "filters": []}, {"id": "a2", "type": 10, "filters": []}, {"id": "vip2", "type": 1, "filters": []}, {"id": "a3", "type": 10, "filters": []}, {"id": "vip3", "type": 1, "filters": []}]}', '[{"type": "limit", "param": "limit", "value": "10"}]', '2024-08-20 11:43:57.09083', '2024-08-20 11:43:57.09083', '2024-10-21 17:28:02.32', NULL, 'check_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('cd69a5b1-0423-4bd3-85da-498e80b300f0', '攻击意图聚合模型', 2, 4, '攻击意图聚合模型是一种安全分析框架，它通过整合和分析多种攻击指标来推断和分类攻击者的意图。这个模型综合考虑攻击技术、目标选择、持续时间、攻击强度等因素，利用机器学习或人工智能算法来识别共同的攻击模式和潜在目的。通过聚合相似的攻击意图，安全团队可以更好地理解威胁格局，预测可能的攻击路径，并制定更有针对性的防御策略。这种模型有助于提高安全响应的效率，优化资源分配，并增强对新型和复杂攻击的防御能力。', 'f', 3, 2, '{"edges": [{"id": "e72517412", "type": 1, "dstId": "v57315272", "srcId": "v80903007", "dstType": 4, "filters": [], "srcType": 1}, {"id": "e69300299", "type": 14, "dstId": "v37626553", "srcId": "v80903007", "dstType": 1, "filters": [], "srcType": 1}], "limit": 10, "vertices": [{"id": "v37626553", "type": 1, "filters": []}, {"id": "v80903007", "type": 1, "filters": []}, {"id": "v57315272", "type": 4, "filters": []}]}', '[{"type": "attack_type_msg", "param": "attack_type_msg", "value": "文件写入"}]', '2024-08-30 14:45:29.58856', '2024-08-30 14:45:29.58856', '2024-10-21 15:58:04.802', 'MATCH (v80903007:IP)-[e72517412:ip_belong_to_org]->(v57315272:ORG) MATCH (v80903007:IP)-[e69300299:make_attack_to]->(v37626553:IP) RETURN v80903007, id(v80903007), v57315272, id(v57315272), v37626553, id(v37626553), e72517412, properties(e72517412), e69300299, properties(e69300299) LIMIT 10', 'attack_chains');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('9942a700-0c87-48b3-9dae-7ed58b1e7d4b', '资产风险聚合模型', 2, 4, '资产风险聚合模型是一种综合性风险评估框架，用于汇总和分析组织内所有资产的安全风险。这个模型整合了各个资产的脆弱性、威胁暴露程度、潜在影响和现有防护措施等因素，通过算法计算得出整体风险评分。它考虑资产间的相互依赖关系，识别风险集中areas、关键节点和潜在的连锁反应。通过这种聚合分析，组织可以全面了解其风险状况，优先分配资源到高风险领域，制定更有效的风险缓解策略，并提高整体安全态势。这个模型支持动态风险评估，能随时间和环境变化不断更新，为安全决策提供数据支持。', 'f', 2, 2, '{"edges": [{"id": "e0", "type": 14, "dstId": "a1", "srcId": "aip", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e1", "type": 15, "dstId": "vip1", "srcId": "a1", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e2", "type": 14, "dstId": "a2", "srcId": "vip1", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e3", "type": 15, "dstId": "vip2", "srcId": "a2", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e4", "type": 14, "dstId": "a3", "srcId": "vip2", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e4", "type": 15, "dstId": "vip3", "srcId": "a3", "dstType": 1, "filters": [], "srcType": 10}], "limit": 10, "vertices": [{"id": "aip", "type": 1, "filters": []}, {"id": "a1", "type": 10, "filters": [{"value": "远控木马", "operator": 1, "property": "attack_type_msg"}]}, {"id": "vip1", "type": 1, "filters": []}, {"id": "a2", "type": 10, "filters": []}, {"id": "vip2", "type": 1, "filters": []}, {"id": "a3", "type": 10, "filters": []}, {"id": "vip3", "type": 1, "filters": []}]}', '[]', '2024-08-30 14:45:29.58856', '2024-08-30 14:45:29.58856', '2024-10-21 15:10:26.86', NULL, 'asset_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('d30b33cf-7697-4c97-bc0b-67a7d43c90e4', '基于域名关联的APT拓线', 2, 4, '基于域名关联的APT拓线是一种网络安全分析方法，通过研究和关联与高级持续性威胁（APT）相关的域名来追踪攻击者的活动。这种技术利用域名注册信息、DNS记录、IP地址关联等数据，构建攻击者基础设施的关系图。通过分析这些关联，安全研究人员可以识别出APT组织使用的域名模式、基础设施复用情况，以及潜在的攻击路径。这种方法有助于揭示APT攻击的范围、持续时间和演变过程，为防御策略提供关键洞察。', 'f', 1, 3, '{"edges": [{"id": "e42547831", "type": 10, "dstId": "v52958473", "srcId": "v08870948", "dstType": 2, "filters": [], "srcType": 1}, {"id": "e01645009", "type": 13, "dstId": "v70885397", "srcId": "v52958473", "dstType": 1, "filters": [], "srcType": 2}], "limit": 10, "vertices": [{"id": "v08870948", "type": 1, "filters": []}, {"id": "v52958473", "type": 2, "filters": []}, {"id": "v70885397", "type": 1, "filters": []}]}', '[]', '2024-08-30 14:45:29.58856', '2024-08-30 14:45:29.58856', NULL, 'MATCH (v08870948:IP)-[e42547831:client_query_domain]->(v52958473:DOMAIN) MATCH (v52958473:DOMAIN)-[e01645009:parse_to]->(v70885397:IP) RETURN v08870948, id(v08870948), v52958473, id(v52958473), v70885397, id(v70885397), e42547831, properties(e42547831), e01645009, properties(e01645009)', 'dns_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('816d3a18-92bc-4a14-a9ba-004cd6afded2', '模糊图结构攻击聚合', 2, 4, '模糊图结构攻击是一种针对图数据库或使用图结构的系统的复杂攻击手法。攻击者通过引入噪声或修改图的结构来混淆原有的数据关系，从而干扰基于图的算法和分析。这种攻击可能导致错误的数据推断、降低系统性能，或是绕过基于图结构的安全机制。其目的可能包括破坏数据完整性、隐藏恶意活动，或是干扰依赖图结构的决策系统。防御这类攻击需要强大的图数据验证机制和异常检测算法。', 'f', 3, 3, '{"edges": [{"id": "e0", "type": 14, "dstId": "a1", "srcId": "aip", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e1", "type": 15, "dstId": "vip1", "srcId": "a1", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e2", "type": 14, "dstId": "a2", "srcId": "vip1", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e3", "type": 15, "dstId": "vip2", "srcId": "a2", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e4", "type": 14, "dstId": "a3", "srcId": "vip2", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e4", "type": 15, "dstId": "vip3", "srcId": "a3", "dstType": 1, "filters": [], "srcType": 10}], "limit": 10, "vertices": [{"id": "aip", "type": 1, "filters": []}, {"id": "a1", "type": 10, "filters": [{"value": "远控木马", "operator": 1, "property": "attack_type_msg"}]}, {"id": "vip1", "type": 1, "filters": []}, {"id": "a2", "type": 10, "filters": []}, {"id": "vip2", "type": 1, "filters": []}, {"id": "a3", "type": 10, "filters": []}, {"id": "vip3", "type": 1, "filters": []}]}', '[{"type": "limit", "param": "limit", "value": "10"}]', '2024-08-30 14:45:29.58856', '2024-08-30 14:45:29.58856', '2024-10-21 15:03:37.837', 'MATCH (v60991411:IP)-[e92701962:make_attack_to]->(v24503896:IP) WHERE e92701962.attack_type_msg == ''端口扫描'' MATCH (v60991411:IP)-[e83740350:ip_belong_to_org]->(v85605785:ORG) RETURN v24503896, id(v24503896), v85605785, id(v85605785), v60991411, id(v60991411), e92701962, properties(e92701962), e83740350, properties(e83740350) LIMIT 10', 'graph_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('98676646-489a-40fb-9380-bb1fac4deba2', '基于通信关系的线索拓展模型', 2, 4, '基于通信关系的线索拓展模型是一种安全分析框架，通过研究网络通信模式和关系来发现和扩展与安全事件相关的线索。这个模型分析网络流量、日志数据和通信元数据，构建实体（如IP地址、域名、设备）之间的关系图。通过图分析算法，它能识别异常的通信模式、隐藏的连接和潜在的恶意网络。模型可以从已知的威胁指标出发，自动扩展相关的通信实体，揭示更广泛的攻击基础设施。这种方法有助于安全分析师快速理解攻击范围，发现新的威胁指标，并追踪复杂的攻击链。它为事件响应和威胁狩猎提供了强大的支持，提高了对高级持续性威胁的检测和分析能力。', 'f', 5, 1, '{"edges": [{"id": "e23736425", "type": 13, "dstId": "v55997832", "srcId": "v04537502", "dstType": 1, "filters": [], "srcType": 2}, {"id": "e35122486", "type": 14, "dstId": "v62341082", "srcId": "v55997832", "dstType": 1, "filters": [], "srcType": 1}], "limit": 10, "vertices": [{"id": "v62341082", "type": 1, "filters": []}, {"id": "v55997832", "type": 1, "filters": []}, {"id": "v04537502", "type": 2, "filters": []}]}', '[]', '2024-08-30 14:45:29.58856', '2024-08-30 14:45:29.58856', '2024-10-21 15:11:05.076', 'MATCH (v04537502:DOMAIN)-[e23736425:parse_to]->(v55997832:IP) MATCH (v55997832:IP)-[e35122486:make_attack_to]->(v62341082:IP) RETURN v62341082, id(v62341082), v55997832, id(v55997832), v04537502, id(v04537502), e23736425, properties(e23736425), e35122486, properties(e35122486)', 'commu_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('a68f6798-39d1-4f08-af87-9e0d06fbc768', '基于攻击负载的相似性聚合', 2, 4, '基于攻击负载的相似性聚合是一种安全分析方法，通过比较和分组相似的攻击载荷（payload）来识别共同的攻击模式和来源。这种技术分析攻击中使用的代码、脚本、或数据包的内容和结构，利用机器学习或其他算法来计算相似度，并将相关的攻击分类聚合。这有助于安全团队快速识别新的攻击变种、追踪攻击来源，并开发更有效的防御策略。通过聚合相似攻击，分析师可以更好地理解攻击趋势，优化响应措施，提高整体安全防御效率。', 'f', 6, 3, '{"edges": [{"id": "e08418619", "type": 6, "dstId": "v07335576", "srcId": "v68896358", "dstType": 5, "filters": [], "srcType": 1}, {"id": "e73144135", "type": 14, "dstId": "v20739244", "srcId": "v68896358", "dstType": 1, "filters": [], "srcType": 1}], "limit": 10, "vertices": [{"id": "v07335576", "type": 5, "filters": []}, {"id": "v68896358", "type": 1, "filters": []}, {"id": "v20739244", "type": 1, "filters": []}]}', '[{"type": "attack_type_msg", "param": "attack_type_msg", "value": "文件写入"}]', '2024-08-30 14:45:29.58856', '2024-08-30 14:45:29.58856', '2024-10-21 15:10:33.029', NULL, 'payload_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('37097f0b-555e-41e5-8bbb-e7d5edc7b9f4', '隧道穿越聚合', 3, 4, '隧道穿越是一种将一种网络协议封装在另一种网络协议中进行传输的技术。它通常用于绕过网络限制、提高安全性或实现跨网络通信。这种方法创建了一个虚拟的"隧道"，使得原本可能被阻挡或无法直接传输的数据能够顺利通过网络barriers。常见应用包括VPN、IPv6over IPv4、SSH隧道等。虽然隧道穿越可以提高网络灵活性和安全性，但也可能被滥用来绕过安全措施，因此网络管理员需要谨慎监控和管理隧道流量。', 'f', 30, 1, '{"edges": [{"id": "e01539060", "type": 14, "dstId": "v45982464", "srcId": "v94445869", "dstType": 1, "filters": [], "srcType": 1}, {"id": "e59635369", "type": 14, "dstId": "v23843198", "srcId": "v45982464", "dstType": 1, "filters": [], "srcType": 1}, {"id": "e52403293", "type": 14, "dstId": "v04636404", "srcId": "v23843198", "dstType": 1, "filters": [], "srcType": 1}], "limit": 10, "vertices": [{"id": "v94445869", "type": 1, "filters": []}, {"id": "v45982464", "type": 1, "filters": []}, {"id": "v23843198", "type": 1, "filters": []}, {"id": "v04636404", "type": 1, "filters": []}]}', '[{"type": "limit", "param": "limit", "value": "10"}]', '2024-08-20 11:43:57.09083', '2024-08-20 11:43:57.09083', '2024-10-21 16:58:58.786', 'MATCH (v94445869:IP)-[e01539060:make_attack_to]->(v45982464:IP) MATCH (v45982464:IP)-[e59635369:make_attack_to]->(v23843198:IP) MATCH (v23843198:IP)-[e52403293:make_attack_to]->(v04636404:IP) RETURN v94445869, id(v94445869), v45982464, id(v45982464), v23843198, id(v23843198), v04636404, id(v04636404), e01539060, properties(e01539060), e59635369, properties(e59635369), e52403293, properties(e52403293) LIMIT 10', 'tunnel_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('118a2c27-47c2-48fd-b4de-be33362d864d', '扫描打点攻击聚合', 1, 4, '扫描打点攻击是一种网络侦察技术，攻击者通过系统地探测目标网络或系统的开放端口、服务和潜在漏洞来收集信息。这种方法通常作为更大规模攻击的前奏，使用自动化工具快速扫描大量IP地址和端口，目的是识别可能的弱点和入口点。虽然扫描本身通常不直接造成破坏，但它为攻击者提供了宝贵的信息，可能导致更严重的后续攻击，因此及时发现和防范这种活动对网络安全至关重要。', 'f', 30, 1, '{"edges": [{"id": "e92701962", "type": 14, "dstId": "v24503896", "srcId": "v60991411", "dstType": 1, "filters": [{"value": "端口扫描", "operator": 1, "property": "attack_type_msg"}], "srcType": 1}, {"id": "e83740350", "type": 1, "dstId": "v85605785", "srcId": "v60991411", "dstType": 4, "filters": [], "srcType": 1}], "limit": 10, "vertices": [{"id": "v24503896", "type": 1, "filters": []}, {"id": "v60991411", "type": 1, "filters": []}, {"id": "v85605785", "type": 4, "filters": []}]}', '[{"type": "limit", "param": "limit", "value": "1"}]', '2024-08-20 11:43:57.09083', '2024-08-20 11:43:57.09083', '2024-10-21 17:13:46.065', NULL, 'scan_cluster');
INSERT INTO "preset_attack_model" ("id", "name", "severity", "type", "description", "active", "interval", "unit", "pattern", "params", "create_time", "update_time", "last_run_time", "ngql", "script_name") VALUES ('227d05eb-87e8-4ced-b899-11a940ed92c4', '远控穿透聚合模型', 2, 4, '远控穿透聚合模型是一种专门用于检测和分析远程控制木马（RAT）和其他远程访问工具的安全框架。这个模型通过聚合和关联来自网络流量、终端行为和日志数据的多种指标，识别可能的远控活动。它分析通信模式、命令结构和数据外流特征，利用机器学习算法来检测已知和未知的远控变种。通过聚合多个来源的数据，该模型能够更准确地识别复杂的远控攻击，追踪攻击者的活动路径，并提供全面的攻击链视图。这有助于安全团队快速响应远控入侵，制定有效的防御策略，并提高对高级持续性威胁（APT）的检测能力。', 'f', 30, 1, '{"edges": [{"id": "e0", "type": 14, "dstId": "a1", "srcId": "aip", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e1", "type": 15, "dstId": "vip1", "srcId": "a1", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e2", "type": 14, "dstId": "a2", "srcId": "vip1", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e3", "type": 15, "dstId": "vip2", "srcId": "a2", "dstType": 1, "filters": [], "srcType": 10}, {"id": "e4", "type": 14, "dstId": "a3", "srcId": "vip2", "dstType": 10, "filters": [], "srcType": 1}, {"id": "e4", "type": 15, "dstId": "vip3", "srcId": "a3", "dstType": 1, "filters": [], "srcType": 10}], "limit": 10, "vertices": [{"id": "aip", "type": 1, "filters": []}, {"id": "a1", "type": 10, "filters": [{"value": "远控木马", "operator": 1, "property": "attack_type_msg"}]}, {"id": "vip1", "type": 1, "filters": []}, {"id": "a2", "type": 10, "filters": []}, {"id": "vip2", "type": 1, "filters": []}, {"id": "a3", "type": 10, "filters": []}, {"id": "vip3", "type": 1, "filters": []}]}', '[{"type": "attack_type_msg", "param": "attack_type_msg", "value": "文件写入"}]', '2024-08-30 14:45:29.58856', '2024-08-30 14:45:29.58856', '2024-10-21 17:24:49.526', NULL, 'rat_cluster');
