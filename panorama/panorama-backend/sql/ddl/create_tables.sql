CREATE TABLE IF NOT EXISTS custom_attack_model
(
    id            VARCHAR(36)                           NOT NULL,
    version       INTEGER     DEFAULT 1                 NOT NULL,
    name          VARCHAR(255)                          NOT NULL,
    severity      SMALLINT                              NOT NULL
        CONSTRAINT ck_severity
            CHECK ((severity >= 1) AND (severity <= 3)),
    type          SMALLINT                              NOT NULL,
    description   TEXT,
    pattern       JSONB,
    ngql          TEXT,
    running       BOOLEAN     DEFAULT FALSE             NOT NULL,
    created_by    VARCHAR(20)                           NOT NULL,
    create_time   TIMESTAMP   DEFAULT CURRENT_TIMESTAMP NOT NULL,
    update_time   TIMESTAMP   DEFAULT CURRENT_TIMESTAMP NOT NULL,
    last_run_time TIMESTAMP,
    auth_tag      VARCHAR(20) DEFAULT ''::CHARACTER VARYING,
    current       BOOLEAN     DEFAULT TRUE              NOT NULL,
    deleted       BOOLEAN     DEFAULT FALSE             NOT NULL,
    CONSTRAINT pk_custom_attack_model PRIMARY KEY (id, version)
);

COMMENT ON TABLE custom_attack_model IS '自定义攻击模型表';

COMMENT ON COLUMN custom_attack_model.id IS '模型ID';
COMMENT ON COLUMN custom_attack_model.version IS '版本号';
COMMENT ON COLUMN custom_attack_model.name IS '模型名称';
COMMENT ON COLUMN custom_attack_model.severity IS '严重程度';
COMMENT ON COLUMN custom_attack_model.type IS '事件类型';
COMMENT ON COLUMN custom_attack_model.description IS '模型描述';
COMMENT ON COLUMN custom_attack_model.pattern IS '图模式';
COMMENT ON COLUMN custom_attack_model.ngql IS '图模式对应的nGQL语句';
COMMENT ON COLUMN custom_attack_model.running IS '是否正在运行';
COMMENT ON COLUMN custom_attack_model.create_time IS '模型创建时间';
COMMENT ON COLUMN custom_attack_model.update_time IS '模型更新时间';
COMMENT ON COLUMN custom_attack_model.last_run_time IS '最近运行时间';
COMMENT ON COLUMN custom_attack_model.current IS '是否为当前版本';
COMMENT ON COLUMN custom_attack_model.deleted IS '是否已删除（逻辑删除）';

alter table custom_attack_model
    owner to agg_judgment;

CREATE INDEX IF NOT EXISTS idx_custom_event_model_name ON custom_attack_model (name);
CREATE INDEX IF NOT EXISTS idx_custom_event_model_severity ON custom_attack_model (severity);
CREATE INDEX IF NOT EXISTS idx_custom_event_model_type ON custom_attack_model (type);
CREATE INDEX IF NOT EXISTS idx_custom_event_model_creator ON custom_attack_model (created_by);
CREATE INDEX IF NOT EXISTS idx_custom_event_model_current ON custom_attack_model (current);
CREATE INDEX IF NOT EXISTS idx_custom_event_model_deleted ON custom_attack_model (deleted);

create table if not exists preset_attack_model
(
    id            varchar(36)                         not null
        primary key,
    name          varchar(255)                        not null,
    severity      smallint                            not null
        constraint ck_severity
            check ((severity >= 1) AND (severity <= 3)),
    type          smallint                            not null,
    description   text,
    active        boolean   default false             not null,
    interval      integer,
    unit          smallint,
    pattern       jsonb,
    params        jsonb,
    create_time   timestamp default CURRENT_TIMESTAMP not null,
    update_time   timestamp default CURRENT_TIMESTAMP not null,
    last_run_time timestamp,
    ngql          TEXT,
    script_name   varchar(255)
);

comment on table preset_attack_model is '预置（内嵌）攻击模型表';
comment on column preset_attack_model.id is '模型ID';
comment on column preset_attack_model.name is '模型名称';
comment on column preset_attack_model.severity is '严重等级';
comment on column preset_attack_model.type is '攻击类型';
comment on column preset_attack_model.description is '模型描述';
comment on column preset_attack_model.active is '模型激活状态';
comment on column preset_attack_model.interval is '模型执行间隔';
comment on column preset_attack_model.unit is '间隔时间单位';
comment on column preset_attack_model.pattern is '图模式';
comment on column preset_attack_model.params is '模型参数';
comment on column preset_attack_model.create_time is '模型创建时间';
comment on column preset_attack_model.update_time is '模型更新时间';
comment on column preset_attack_model.last_run_time is '最近运行时间';
COMMENT ON COLUMN preset_attack_model.ngql IS '图模式对应的nGQL语句';
comment on column preset_attack_model.script_name is '模型对应的脚本名称';

alter table preset_attack_model
    owner to agg_judgment;

CREATE TABLE IF NOT EXISTS model_run
(
    id                  VARCHAR(36)         NOT NULL PRIMARY KEY,
    model_id            VARCHAR(36)         NOT NULL,
    model_version       INTEGER,
    model_type          SMALLINT            NOT NULL,
    status              SMALLINT  DEFAULT 1 NOT NULL,
    alarm_count         INTEGER,
    unique_victim_count INTEGER,
    source_distribution JSONB,
    earliest_event_time TIMESTAMP,
    latest_event_time   TIMESTAMP,
    create_time         TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE model_run IS '模型运行记录表';

COMMENT ON COLUMN model_run.id IS '主键ID';
COMMENT ON COLUMN model_run.model_id IS '关联的模型ID';
COMMENT ON COLUMN model_run.model_version IS '模型版本号';
COMMENT ON COLUMN model_run.model_type IS '模型类型（预置/自定义）';
COMMENT ON COLUMN model_run.status IS '状态：0-已删除，1-未处置，2-已忽略，3-已处置，4-重点关注';
COMMENT ON COLUMN model_run.alarm_count IS '涉及的告警日志总数';
COMMENT ON COLUMN model_run.unique_victim_count IS '独立受害者IP数量';
COMMENT ON COLUMN model_run.source_distribution IS '各数据源的告警日志数量分布';
COMMENT ON COLUMN model_run.earliest_event_time IS '发现的最早攻击事件时间';
COMMENT ON COLUMN model_run.latest_event_time IS '发现的最晚攻击事件时间';
COMMENT ON COLUMN model_run.create_time IS '运行时间';

CREATE INDEX IF NOT EXISTS idx_model_run_model_id ON model_run (model_id);
CREATE INDEX IF NOT EXISTS idx_model_run_model_version ON model_run (model_version);

ALTER TABLE model_run
    OWNER TO agg_judgment;

CREATE TABLE IF NOT EXISTS attack_event
(
    id          VARCHAR(36)                         NOT NULL PRIMARY KEY,
    run_id      VARCHAR(36)                         NOT NULL,
    event_graph JSONB,
    start_time  TIMESTAMP,
    end_time    TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

COMMENT ON TABLE attack_event IS '攻击事件表';

COMMENT ON COLUMN attack_event.id IS '攻击事件ID';
COMMENT ON COLUMN attack_event.run_id IS '关联的模型运行记录ID';
COMMENT ON COLUMN attack_event.event_graph IS '攻击事件图';
COMMENT ON COLUMN attack_event.start_time IS '攻击开始时间';
COMMENT ON COLUMN attack_event.end_time IS '攻击结束时间';
COMMENT ON COLUMN attack_event.create_time IS '事件入库时间';

ALTER TABLE attack_event
    OWNER TO agg_judgment;

CREATE OR REPLACE VIEW event_log_view AS
SELECT el.event_id,
       el.model_id,
       el.event_name,
       el.event_class,
       el.event_type,
       el.event_level,
       el.event_state,
       el.model_type,
       el.describe,
       el.start_time,
       el.end_time,
       el.alert_cnt,
       el.vip_cnt,
       el.vip_cnt_info,
       el.aip_cnt,
       el.aip_cnt_info,
       el.vendor_id_cnt_info,
       el.threat_level_cnt_info,
       el.kill_chain_cnt,
       el.kill_chain_cnt_info,
       el.ioa_tag_cnt_info,
       el.vuln_cnt,
       el.vuln_cnt_info,
       el.file_cnt,
       el.file_cnt_info,
       el.query_cnt,
       el.query_cnt_info,
       el.create_by,
       el.update_time,
       el.create_time
FROM event_log el
UNION ALL
SELECT mr.id                           AS event_id,
       mr.model_id,
       CASE mr.model_type
           WHEN 0 THEN pam.name
           WHEN 1 THEN cam.name
           END                         AS event_name,
       NULL                            AS event_class,
       CASE mr.model_type
           WHEN 0 THEN pam.type::varchar(255)
           WHEN 1 THEN cam.type::varchar(255)
           END                         AS event_type,
       CASE mr.model_type
           WHEN 0 THEN pam.severity
           WHEN 1 THEN cam.severity
           END                         AS event_level,
       COALESCE(mr.status::integer, 0) AS event_state,
       2                               AS model_type,
       CASE mr.model_type
           WHEN 0 THEN pam.description
           WHEN 1 THEN cam.description
           END                         AS describe,
       mr.earliest_event_time          AS start_time,
       mr.latest_event_time            AS end_time,
       mr.alarm_count                  AS alert_cnt,
       mr.unique_victim_count          AS vip_cnt,
       NULL                            AS vip_cnt_info,
       NULL                            AS aip_cnt,
       NULL                            AS aip_cnt_info,
       mr.source_distribution          AS vendor_id_cnt_info,
       NULL                            AS threat_level_cnt_info,
       NULL                            AS kill_chain_cnt,
       NULL                            AS kill_chain_cnt_info,
       NULL                            AS ioa_tag_cnt_info,
       NULL                            AS vuln_cnt,
       NULL                            AS vuln_cnt_info,
       NULL                            AS file_cnt,
       NULL                            AS file_cnt_info,
       NULL                            AS query_cnt,
       NULL                            AS query_cnt_info,
       CASE mr.model_type
           WHEN 0 THEN NULL
           WHEN 1 THEN cam.created_by
           END                         AS create_by,
       NULL                            AS update_time,
       mr.create_time
FROM model_run mr
         LEFT JOIN custom_attack_model cam
                   ON mr.model_id = cam.id AND mr.model_version = cam.version AND
                      mr.model_type = 1
         LEFT JOIN preset_attack_model pam ON mr.model_id = pam.id AND mr.model_type = 0;