create table if not exists attack_event
(
    id          varchar(36)                            not null
        primary key,
    run_id      varchar(36)                            not null,
    event_graph jsonb,
    start_time  timestamp(6),
    end_time    timestamp(6),
    create_time timestamp(6) default CURRENT_TIMESTAMP not null
);

comment on table attack_event is '攻击事件表';

comment on column attack_event.id is '攻击事件ID';

comment on column attack_event.run_id is '关联的模型运行记录ID';

comment on column attack_event.event_graph is '攻击事件图';

comment on column attack_event.start_time is '攻击开始时间';

comment on column attack_event.end_time is '攻击结束时间';

comment on column attack_event.create_time is '事件入库时间';

alter table attack_event
    owner to postgres;

create table if not exists custom_attack_model
(
    id            varchar(36)                            not null,
    version       integer      default 1                 not null,
    name          varchar(255)                           not null,
    severity      smallint                               not null
        constraint ck_severity
            check ((severity >= 1) AND (severity <= 3)),
    type          smallint                               not null,
    description   text,
    pattern       jsonb,
    ngql          text,
    running       boolean      default false             not null,
    created_by    varchar(20)                            not null,
    create_time   timestamp(6) default CURRENT_TIMESTAMP not null,
    update_time   timestamp(6) default CURRENT_TIMESTAMP not null,
    last_run_time timestamp(6),
    auth_tag      varchar(20)  default ''::character varying,
    current       boolean      default true              not null,
    deleted       boolean      default false             not null,
    constraint pk_custom_attack_model
        primary key (id, version)
);

comment on table custom_attack_model is '自定义攻击模型表';

comment on column custom_attack_model.id is '模型ID';

comment on column custom_attack_model.version is '版本号';

comment on column custom_attack_model.name is '模型名称';

comment on column custom_attack_model.severity is '严重程度';

comment on column custom_attack_model.type is '事件类型';

comment on column custom_attack_model.description is '模型描述';

comment on column custom_attack_model.pattern is '图模式';

comment on column custom_attack_model.ngql is '图模式对应的nGQL语句';

comment on column custom_attack_model.running is '是否正在运行';

comment on column custom_attack_model.create_time is '模型创建时间';

comment on column custom_attack_model.update_time is '模型更新时间';

comment on column custom_attack_model.last_run_time is '最近运行时间';

comment on column custom_attack_model.current is '是否为当前版本';

comment on column custom_attack_model.deleted is '是否已删除（逻辑删除）';

alter table custom_attack_model
    owner to postgres;

create index if not exists idx_custom_event_model_creator
    on custom_attack_model (created_by);

create index if not exists idx_custom_event_model_current
    on custom_attack_model (current);

create index if not exists idx_custom_event_model_deleted
    on custom_attack_model (deleted);

create index if not exists idx_custom_event_model_name
    on custom_attack_model (name);

create index if not exists idx_custom_event_model_severity
    on custom_attack_model (severity);

create index if not exists idx_custom_event_model_type
    on custom_attack_model (type);

create table if not exists event_log
(
    event_id              varchar(255)                           not null
        constraint event_log_pk
            primary key,
    model_id              varchar(255),
    event_name            varchar(255),
    event_class           varchar(255),
    event_type            varchar(255),
    event_level           integer,
    event_state           integer,
    model_type            integer,
    describe              text,
    start_time            timestamp(6),
    end_time              timestamp(6),
    alert_cnt             bigint       default 0,
    vip_cnt               bigint       default 0,
    vip_cnt_info          jsonb,
    aip_cnt               bigint       default 0,
    aip_cnt_info          jsonb,
    vendor_id_cnt_info    jsonb,
    threat_level_cnt_info jsonb,
    kill_chain_cnt        bigint       default 0,
    kill_chain_cnt_info   jsonb,
    ioa_tag_cnt_info      jsonb,
    vuln_cnt              bigint       default 0,
    vuln_cnt_info         jsonb,
    file_cnt              bigint       default 0,
    file_cnt_info         jsonb,
    query_cnt             integer      default 0                 not null,
    query_cnt_info        jsonb,
    update_time           timestamp(6) default CURRENT_TIMESTAMP not null,
    create_time           timestamp(6) default CURRENT_TIMESTAMP not null,
    create_by             varchar(255)
);

comment on column event_log.event_id is '事件ID';

comment on column event_log.model_id is '事件来源的模型ID';

comment on column event_log.event_name is '事件名称';

comment on column event_log.event_class is '事件分类';

comment on column event_log.event_type is '事件类型';

comment on column event_log.event_level is '事件等级';

comment on column event_log.event_state is '事件状态';

comment on column event_log.model_type is '事件来源的模型类型, 1: 序列化模型。2: 图形化模型。';

comment on column event_log.describe is '事件描述';

comment on column event_log.start_time is '事件归并窗口开始时间';

comment on column event_log.end_time is '事件归并窗口结束时间';

comment on column event_log.alert_cnt is '告警总数';

comment on column event_log.vip_cnt is '关联受害者，受害者IP的个数';

comment on column event_log.vip_cnt_info is '关联受害者，受害者分布信息';

comment on column event_log.aip_cnt is '关联攻击者，攻击者IP的个数';

comment on column event_log.aip_cnt_info is '关联攻击者，攻击者分布信息；及关键线索，攻击ip线索';

comment on column event_log.vendor_id_cnt_info is '数据来源分布信息';

comment on column event_log.threat_level_cnt_info is '威胁等级分布信息';

comment on column event_log.kill_chain_cnt is '攻击链分布中，攻击链个数';

comment on column event_log.kill_chain_cnt_info is '攻击链分布信息';

comment on column event_log.ioa_tag_cnt_info is '技战术分布信息';

comment on column event_log.vuln_cnt is '关联漏洞中，漏洞总数；及关键线索中漏洞线索';

comment on column event_log.vuln_cnt_info is '关联漏洞中，漏洞分布信息；及关键线索中漏洞线索';

comment on column event_log.file_cnt is '关键线索，文件的个数';

comment on column event_log.file_cnt_info is '关键线索，后门文件线索';

comment on column event_log.query_cnt is '关键线索，可疑域名数量';

comment on column event_log.query_cnt_info is '关键线索，可疑域名信息';

comment on column event_log.update_time is '事件的更新时间';

comment on column event_log.create_time is '事件记录的创建事件';

comment on column event_log.create_by is '事件创建者';

alter table event_log
    owner to postgres;

create table if not exists model_run
(
    id                  varchar(36)            not null
        primary key,
    model_id            varchar(36)            not null,
    model_version       integer,
    model_type          smallint               not null,
    status              smallint     default 1 not null,
    alarm_count         integer,
    unique_victim_count integer,
    source_distribution jsonb,
    earliest_event_time timestamp(6),
    latest_event_time   timestamp(6),
    create_time         timestamp(6) default CURRENT_TIMESTAMP,
    task_id             varchar(255)
);

comment on table model_run is '模型运行记录表';

comment on column model_run.id is '主键ID';

comment on column model_run.model_id is '关联的模型ID';

comment on column model_run.model_version is '模型版本号';

comment on column model_run.model_type is '模型类型（预置/自定义）';

comment on column model_run.status is '状态：0-已删除，1-未处置，2-已忽略，3-已处置，4-重点关注';

comment on column model_run.alarm_count is '涉及的告警日志总数';

comment on column model_run.unique_victim_count is '独立受害者IP数量';

comment on column model_run.source_distribution is '各数据源的告警日志数量分布';

comment on column model_run.earliest_event_time is '发现的最早攻击事件时间';

comment on column model_run.latest_event_time is '发现的最晚攻击事件时间';

comment on column model_run.create_time is '运行时间';

comment on column model_run.task_id is '管理任务id';

alter table model_run
    owner to postgres;

create index if not exists idx_model_run_model_id
    on model_run (model_id);

create index if not exists idx_model_run_model_version
    on model_run (model_version);

create table if not exists preset_attack_model
(
    id            varchar(36)                            not null
        primary key,
    name          varchar(255)                           not null,
    severity      smallint                               not null
        constraint ck_severity
            check ((severity >= 1) AND (severity <= 3)),
    type          smallint                               not null,
    description   text,
    active        boolean      default false             not null,
    interval      integer,
    unit          smallint,
    pattern       jsonb,
    params        jsonb,
    create_time   timestamp(6) default CURRENT_TIMESTAMP not null,
    update_time   timestamp(6) default CURRENT_TIMESTAMP not null,
    last_run_time timestamp(6),
    ngql          text,
    script_name   varchar(255)
);

comment on table preset_attack_model is '预置（内嵌）攻击模型表';

comment on column preset_attack_model.id is '模型ID';

comment on column preset_attack_model.name is '模型名称';

comment on column preset_attack_model.severity is '严重等级';

comment on column preset_attack_model.type is '攻击类型';

comment on column preset_attack_model.description is '模型描述';

comment on column preset_attack_model.active is '模型激活状态';

comment on column preset_attack_model.interval is '模型执行间隔';

comment on column preset_attack_model.unit is '间隔时间单位';

comment on column preset_attack_model.pattern is '图模式';

comment on column preset_attack_model.params is '模型参数';

comment on column preset_attack_model.create_time is '模型创建时间';

comment on column preset_attack_model.update_time is '模型更新时间';

comment on column preset_attack_model.last_run_time is '最近运行时间';

comment on column preset_attack_model.ngql is '图模式对应的nGQL语句';

comment on column preset_attack_model.script_name is '模型对应的脚本名称';

alter table preset_attack_model
    owner to postgres;

create table if not exists t_compute_job
(
    id               text,
    job_name         text,
    group_id         text,
    job_status       text         default 'stop'::text,
    job_describe     text,
    schedule_data    json,
    advanced_setting json,
    trans_ids        text,
    plugin_list      json,
    canvas_data      json,
    create_type      numeric(10)  default 0,
    creater_id       text,
    create_time      timestamp(6) default CURRENT_TIMESTAMP,
    update_time      timestamp(6) default CURRENT_TIMESTAMP
);

alter table t_compute_job
    owner to postgres;

create table if not exists schedule_task
(
    task_id         varchar(36)                                      not null
        primary key,
    task_name       varchar(255)                                     not null,
    model_id        varchar(36)                                      not null,
    execution_type  varchar(50)                                      not null,
    interval_unit   varchar(50),
    interval_value  integer,
    cron_expression varchar(50),
    task_status     varchar(50) default 'PENDING'::character varying not null,
    create_time     timestamp   default now()                        not null,
    update_time     timestamp   default now()                        not null,
    last_run_time   timestamp   default now()                        not null,
    created_by      varchar(20)                                      not null
);

comment on column schedule_task.task_id is '任务uuid';

comment on column schedule_task.task_name is '任务名';

comment on column schedule_task.model_id is '模型id';

comment on column schedule_task.execution_type is '执行类型，分为SINGLE, LOOP, SCHEDULED';

comment on column schedule_task.interval_unit is 'LOOP任务参数，运行间隔时间单位';

comment on column schedule_task.interval_value is 'LOOP任务参数，运行间隔时间大小';

comment on column schedule_task.cron_expression is 'SCHEDULED任务参数，cron表达式';

comment on column schedule_task.task_status is '任务状态，取值为PENDING, RUNNING, COMPELED, PAUSE, FAILED';

comment on column schedule_task.create_time is '任务创建时间';

comment on column schedule_task.update_time is '任务更新时间';

comment on column schedule_task.last_run_time is '任务最后运行时间';

comment on column schedule_task.created_by is '任务创建者id';

alter table schedule_task
    owner to postgres;

create view event_log_view
            (event_id, model_id, event_name, event_class, event_type, event_level, event_state, model_type, describe,
             start_time, end_time, alert_cnt, vip_cnt, vip_cnt_info, aip_cnt, aip_cnt_info, vendor_id_cnt_info,
             threat_level_cnt_info, kill_chain_cnt, kill_chain_cnt_info, ioa_tag_cnt_info, vuln_cnt, vuln_cnt_info,
             file_cnt, file_cnt_info, query_cnt, query_cnt_info, create_by, update_time, create_time)
as
SELECT el.event_id,
       el.model_id,
       el.event_name,
       el.event_class,
       el.event_type,
       el.event_level,
       el.event_state,
       el.model_type,
       el.describe,
       el.start_time,
       el.end_time,
       el.alert_cnt,
       el.vip_cnt,
       el.vip_cnt_info,
       el.aip_cnt,
       el.aip_cnt_info,
       el.vendor_id_cnt_info,
       el.threat_level_cnt_info,
       el.kill_chain_cnt,
       el.kill_chain_cnt_info,
       el.ioa_tag_cnt_info,
       el.vuln_cnt,
       el.vuln_cnt_info,
       el.file_cnt,
       el.file_cnt_info,
       el.query_cnt,
       el.query_cnt_info,
       el.create_by,
       el.update_time,
       el.create_time
FROM event_log el
UNION ALL
SELECT mr.id                             AS event_id,
       mr.model_id,
       CASE mr.model_type
           WHEN 0 THEN pam.name
           WHEN 1 THEN cam.name
           ELSE NULL::character varying
           END                           AS event_name,
       NULL::character varying           AS event_class,
       CASE mr.model_type
           WHEN 0 THEN pam.type::character varying(255)
           WHEN 1 THEN cam.type::character varying(255)
           ELSE NULL::character varying
           END                           AS event_type,
       CASE mr.model_type
           WHEN 0 THEN pam.severity
           WHEN 1 THEN cam.severity
           ELSE NULL::smallint
           END                           AS event_level,
       COALESCE(mr.status::integer, 0)   AS event_state,
       2                                 AS model_type,
       CASE mr.model_type
           WHEN 0 THEN pam.description
           WHEN 1 THEN cam.description
           ELSE NULL::text
           END                           AS describe,
    mr.earliest_event_time            AS start_time,
    mr.latest_event_time              AS end_time,
    mr.alarm_count                    AS alert_cnt,
    mr.unique_victim_count            AS vip_cnt,
    NULL::jsonb                       AS vip_cnt_info,
    NULL::bigint                      AS aip_cnt,
    NULL::jsonb                       AS aip_cnt_info,
    mr.source_distribution            AS vendor_id_cnt_info,
    NULL::jsonb                       AS threat_level_cnt_info,
    NULL::bigint                      AS kill_chain_cnt,
    NULL::jsonb                       AS kill_chain_cnt_info,
    NULL::jsonb                       AS ioa_tag_cnt_info,
    NULL::bigint                      AS vuln_cnt,
    NULL::jsonb                       AS vuln_cnt_info,
    NULL::bigint                      AS file_cnt,
    NULL::jsonb                       AS file_cnt_info,
    NULL::integer                     AS query_cnt,
    NULL::jsonb                       AS query_cnt_info,
    CASE mr.model_type
    WHEN 0 THEN NULL::character varying
    WHEN 1 THEN cam.created_by
    ELSE NULL::character varying
    END                           AS create_by,
    NULL::timestamp without time zone AS update_time,
    mr.create_time
    FROM model_run mr
    LEFT JOIN custom_attack_model cam
    ON mr.model_id::text = cam.id::text AND mr.model_version = cam.version AND mr.model_type = 1
    LEFT JOIN preset_attack_model pam ON mr.model_id::text = pam.id::text AND mr.model_type = 0;

alter table event_log_view
    owner to postgres;

