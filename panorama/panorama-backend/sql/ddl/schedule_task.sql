CREATE TABLE schedule_task (
       task_id VARCHAR(36) PRIMARY KEY NOT NULL,
       task_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
       model_id VARCHAR(36) NOT NULL,
       execution_type VARCHAR(50) NOT NULL,
       interval_unit VARCHAR(50),
       interval_value INTEGER,
       cron_expression VARCHAR(50),
       task_status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
       create_time TIMESTAMP NOT NULL DEFAULT NOW(),
       update_time TIMESTAMP NOT NULL DEFAULT NOW(),
       last_run_time TIMESTAMP NOT NULL DEFAULT NOW(),
       created_by VA<PERSON>HAR(20) NOT NULL
);