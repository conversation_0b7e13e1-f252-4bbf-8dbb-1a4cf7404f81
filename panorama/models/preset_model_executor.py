import subprocess
import sys
import json
import ast
import time
from configparser import ConfigParser
from datetime import datetime
import pg_operator

from kafka import KafkaConsumer

KAFKA_CONFIG = {}


def initialize_kafka_config(filename='./env_config.ini', section='kafka'):
    # create a parser
    parser = ConfigParser()
    # read config file
    parser.read(filename)

    # get section, default to postgresql
    if parser.has_section(section):
        params = parser.items(section)
        for param in params:
            key, value = param
            if key == 'bootstrap_servers':
                # 使用 ast.literal_eval 安全地解析字符串为列表
                try:
                    KAFKA_CONFIG[key] = ast.literal_eval(value)
                except (ValueError, SyntaxError):
                    # 如果解析失败，就将其作为单个元素的列表
                    KAFKA_CONFIG[key] = [value.strip('[]')]
            else:
                KAFKA_CONFIG[key] = value
    else:
        raise Exception(f'Section {section} not found in the {filename} file')


class Consumer(object):
    def __init__(self, config):
        self._kwargs = {
            "bootstrap_servers": config['bootstrap_servers'],
            "client_id": "PRESET_MODEL_EXECUTOR",
            "group_id": config['group_id'],
            'auto_offset_reset': 'latest',
            "enable_auto_commit": True,
            "auto_commit_interval_ms": 3000,
            "key_deserializer": self.safe_decode,
            "value_deserializer": self.safe_decode,
        }
        try:
            # 创建消费者
            self._consumer = KafkaConsumer(**self._kwargs)
            topics = [config['topic']]
            self._consumer.subscribe(topics=topics)

            # 等待分区分配
            while not self._consumer.assignment():
                self._consumer.poll(timeout_ms=100)
                time.sleep(0.1)

            print("预置模型执行消费者监听程序启动成功：", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        except Exception as err:
            print(f"Failed to create Kafka consumer: {err}")

    @staticmethod
    def safe_decode(msg):
        if msg is None:
            return None
        try:
            return json.loads(msg.decode('utf-8'))
        except Exception as err:
            return msg

    def consumeMsg(self):
        try:
            # 将消费者指针移到最新位置
            self._consumer.seek_to_end()

            # 获取当前位置
            end_offsets = self._consumer.end_offsets(self._consumer.assignment())

            while True:
                # 只消费新消息
                messages = self._consumer.poll(timeout_ms=1000)
                for tp, records in messages.items():
                    for record in records:
                        if record.offset >= end_offsets[tp] - 1:
                            message = {
                                "Topic": record.topic,
                                "Partition": record.partition,
                                "Offset": record.offset,
                                "Key": record.key,
                                "Value": record.value
                            }
                            print("收到预置模型调用消息:", json.dumps(message['Value'], indent=4))
                            model_id = message['Value']['modelId']
                            run_id = message['Value']['runId']
                            model_params = message['Value']['params']
                            success = execute_model(model_id, run_id, model_params)
                            if success:
                                print(f"预置模型调用成功:model_id -> {model_id},run_id -> {run_id} ")
                            else:
                                print(f"预置模型调用失败,model_id -> {model_id},run_id -> {run_id}")

                # 更新 end_offsets
                end_offsets = self._consumer.end_offsets(self._consumer.assignment())

                self._consumer.commit()
        except Exception as err:
            print(err)


def execute_model(model_id, run_id, model_params):
    script_name = pg_operator.get_preset_model_script_path(model_id)
    script_path = f"model_scripts.{script_name}"

    # 构建脚本调用命令
    model_params_json = json.dumps(model_params)
    args = [run_id, model_params_json]
    command = ["python3","-m", script_path] + args
    print(command)

    try:
        result = subprocess.run(command, capture_output=True, text=True)

        if result.returncode != 0:
            print(f"Script exited with error code {result.returncode}")
            return False
        return True
    except Exception as err:
        print(f"Failed to execute script: {err}")
        return False


def main():
    try:
        attack_model_executor_consumer = Consumer(KAFKA_CONFIG)
        attack_model_executor_consumer.consumeMsg()
    except Exception as err:
        print(err)


# 创建消费者
if __name__ == '__main__':
    try:
        # 初始化kafka配置
        initialize_kafka_config()
        main()
    finally:
        sys.exit(0)
