## 20240926更新

nebula_sql:
create tag index if not exists type_of_attack on ATTACK(attack_type_msg)
rebuild tag index type_of_attack

数据更新 insert_initial_data.sql

测试QA：
如果  基于域名关联的APT拓线 没有检测结果，很可能是没有解析到相同ip的请求，可以修改 dns_cluster.py 中
def check_cluster_algo(final_querys,ip_map):
    # todo add node2vec model here
    min_domain_cnt = 1
    # debug mode 
    # min_domain_cnt = 0
将min_domain_cnt 设置为0，或者修改测试数据


未加入
check_cluster   | 侦查攻击聚合
rat_cluster     | 远控穿透聚合模型
graph_cluster   | 模糊图结构攻击聚合
asset_cluster    | 


测试数据中缺少端口扫描数据
## 全景聚合预置模型调度应用介绍

enums 节点实体&关联关系枚举

**model_scripts 预置模型脚本**（核心部分）

nebula 图数据库相关操作工具模块

- attack_event_handler.py 事件结果处理模块
- pg_operator.py 数据库操作模块
- preset_model_executor.py 服务启动项（本质是Kafka消费者）
- env_config.ini 环境服务配置文件
- requriements.txt 当前服务所需安装依赖项

##### 使用原理：

启动preset_model_executor.py脚本应用，开始监听kafka中preset-attack-model-topic这个topic传来的内容，message中信息包含“模型ID”、“模型运行记录ID”、“模型运行参数”：

```json
{
  "modelId": "227d05eb-87e8-4ced-b899-11a940ed92c4",
  "runId": "f8c245e2-0846-48d1-b75f-d1ca8d99a054",
  "params": [
    {
      "type": "string",
      "param": "attack_type_msg",
      "value": "文件写入"
    }
  ]
}
```

收到对应的message后，根据modelId去查询当前模型的脚本名称并带入参数执行，执行后的结果会将ResultSet类转义为JSON格式，经过处理存储事件结果表中，一条查询记录存为一个事件。

通过model_id + run_id 关联事件与模型执行结果





