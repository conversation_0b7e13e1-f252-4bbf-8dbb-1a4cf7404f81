from enums import VertexType
from enums import EdgeType
from nebula.nebula_result_converter import NebulaResultConverter
import pg_operator

# 返回节点实体类
class VertexAssociation:
    num = ""
    lv = 0
    main = ""
    label = ""
    id = ""
    type = ""
    status = ""
    v_info = {}

    def __init__(self, num, lv, main, label, id, type, status, v_info):
        self.num = num
        self.lv = lv
        self.main = main
        self.label = label
        self.id = id
        self.type = type
        self.status = status
        self.v_info = v_info


# 返回关联关系实体类
class EdgeAssociation:
    num = ""
    from_ = ""
    lv = 0
    to = ""
    label = ""
    id = ""
    type = ""
    status = ""
    edge_info = {}

    def __init__(self, num, from_, lv, to, label, id, type, status, edge_info):
        self.num = num
        self.from_ = from_
        self.lv = lv
        self.to = to
        self.label = label
        self.id = id
        self.type = type
        self.status = status
        self.edge_info = edge_info

    def get_edge_info(self):
        return self.edge_info


# 处理单个事件查询结果，生成记录存储到postgreSQL
def createAttackEvent(result, run_id, victim_ip_list, alert_list, vendor_map, start_time,
                      end_time):
    result_dict = result.dict_for_vis()
    vertex_list = []
    edge_list = []
    nodes_count = result_dict['nodes_count']
    edges_count = result_dict['edges_count']
    print(f"查询事件结果 node_count:{nodes_count}, edge_count:{edges_count}")
    processNodes(vertex_list, result_dict['nodes'])
    start_time, end_time = processRelationships(edge_list, result_dict['edges'], victim_ip_list, alert_list, vendor_map,
                                                start_time, end_time)

    # 通过事件生成的结果，更新model_run的数据
    event_graph = dict()
    event_graph['edge'] = edge_list
    event_graph['vertex'] = vertex_list
    pg_operator.create_attack_event(event_graph, run_id)
    return start_time, end_time


# 处理节点集合
def processNodes(vertex_list, nodes):
    for node in nodes:
        vid = node['id']
        vertex_type_name = node['labels'][0]
        vertex_type_code = VertexType.from_name(vertex_type_name).code
        v_info = node['props']
        labels = NebulaResultConverter.get_label(vertex_type_name, node['props'])
        vertex = VertexAssociation(num="", lv=0, main="", label=labels, id=vid, type=vertex_type_code,
                                   status="", v_info=v_info)
        vertex_list.append(vertex)
    return vertex_list


# 处理关联关系集合
def processRelationships(edge_list, relationships, victim_ip_list, alert_list, vendor_map, start_time, end_time):
    for relationship in relationships:
        from_ = relationship['src']
        to = relationship['dst']
        edge_info = relationship['props']
        rank = edge_info['rank'] if "rank" in edge_info else 0
        labels = EdgeType.from_name(relationship['name']).desc
        edge_type_code = EdgeType.from_name(relationship['name']).code
        edge_type_name = EdgeType.from_name(relationship['name']).edge_name
        edge = EdgeAssociation(num="", from_=from_, lv=0, to=to, label=labels, id=rank, type=edge_type_code,
                               status="", edge_info=edge_info)
        edge_list.append(edge)

        # 通过攻击边处理事件相关信息
        if edge_type_name == "make_attack_to":
            vip = edge.from_
            attack_id = edge_info['attack_id']
            victim_ip_list.append(vip)
            alert_list.append(attack_id)
            vendor_id = edge_info['vendor_id']
            if vendor_id in vendor_map:
                vendor_map[vendor_id] += 1
            else:
                vendor_map[vendor_id] = 1
        # 获取最大最小时间
        if 'attack_time' in edge_info and edge_info['attack_time']:
            attack_time = int(edge_info['attack_time'])
            if start_time == 0 and end_time == 0:
                start_time = int(attack_time)
                end_time = int(attack_time)
            else:
                if attack_time < start_time:
                    start_time = int(attack_time)
                if attack_time > end_time:
                    end_time = int(attack_time)
    return start_time, end_time
