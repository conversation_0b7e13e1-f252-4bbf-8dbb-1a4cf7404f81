from enum import Enum
from typing import List, NamedTuple


class Property(NamedTuple):
    name: str
    desc: str
    type: str
    required: bool


class VertexType(Enum):
    IP = 1
    DOMAIN = 2
    CERT = 3
    ORG = 4
    UA = 5
    DEVICE = 6
    OS = 7
    APT_GROUP = 8
    ATTACH_FILE = 9
    EMAIL = 10
    MAIL = 11
    MALICIOUS_FAMILY = 12
    LABEL = 13


class EdgeType(Enum):
    IP_BELONG_TO_ORG = (1, "ip_belong_to_org", "IP归属机构", VertexType.IP, VertexType.ORG, [])
    UA_BELONG_TO_ORG = (2, "ua_belong_to_org", "UA归属机构", VertexType.UA, VertexType.ORG, [])
    UA_BELONG_TO_DEVICE = (3, "ua_belong_to_device", "UA归属设备", VertexType.UA, VertexType.DEVICE, [])
    DOMAIN_BELONG_TO_APT = (4, "domain_belong_to_apt", "域名关联APT组织", VertexType.DOMAIN, VertexType.APT_GROUP, [])
    HTTP_CONNECTION = (5, "http_connect", "HTTP连接", VertexType.IP, VertexType.IP, [
        Property("uri", "请求URI", "string", True),
        Property("host", "主机", "string", True),
        Property("cookie", "Cookie", "string", False),
        Property("agent", "用户代理", "string", True),
        Property("referer", "来源地址", "string", False),
        Property("xff", "代理头", "string", False),
        Property("req_data", "请求数据", "string", False),
        Property("rsp_data", "响应数据", "string", False),
        Property("method", "请求方法", "string", False),
        Property("status", "状态码", "int", True),
        Property("setcookie", "设置Cookie", "string", False),
        Property("content_type", "内容类型", "string", False),
        Property("accept_language", "接收语言", "string", False)
    ])
    CLIENT_USES_USER_AGENT = (6, "client_use_ua", "使用用户代理", VertexType.IP, VertexType.UA, [])
    USER_AGENT_ACCESSES_DOMAIN = (7, "ua_connect_domain", "用户代理访问域名", VertexType.UA, VertexType.DOMAIN, [])
    CLIENT_ACCESSES_DOMAIN = (8, "client_http_connect_domain", "客户端访问域名", VertexType.IP, VertexType.DOMAIN, [])
    SERVER_ACCESSES_DOMAIN = (9, "server_http_connect_domain", "服务端访问域名", VertexType.IP, VertexType.DOMAIN, [])
    CLIENT_QUERIES_DOMAIN = (10, "client_query_domain", "客户端DNS查询", VertexType.IP, VertexType.DOMAIN, [
        Property("dns_type", "DNS类型", "string", False),
        Property("answer_type", "回答类型", "string", False)
    ])
    CLIENT_QUERIES_DNS_SERVER = (11, "client_query_dns_server", "客户端DNS服务器查询", VertexType.IP, VertexType.IP, [
        Property("dns_type", "DNS类型", "string", False),
        Property("answer_type", "回答类型", "string", False)
    ])
    DNS_SERVER_RESOLVES_DOMAIN = (12, "dns_server_domain", "DNS服务器解析域名", VertexType.IP, VertexType.DOMAIN, [
        Property("dns_type", "DNS类型", "string", False),
        Property("answer_type", "回答类型", "string", False)
    ])
    DOMAIN_RESOLVES_TO_IP = (13, "parse_to", "域名解析到IP", VertexType.DOMAIN, VertexType.IP, [
        Property("dns_server", "DNS服务器", "string", True),
        Property("final_parse", "最终解析", "boolean", True),
        Property("max_ttl", "最大TTL", "int", False),
        Property("min_ttl", "最小TTL", "int", False)
    ])
    ATTACKS = (14, "make_attack_to", "发起攻击行为", VertexType.IP, VertexType.IP, [
        Property("attack_time", "攻击时间", "long", True),
        Property("attack_type_msg", "攻击类型", "string", True),
        Property("attack_type_parent_msg", "攻击父类型", "string", True),
        Property("kill_chain", "攻击链", "string", False),
        Property("malicious_type", "恶意类型", "string", True),
        Property("apt_activity_name", "所属APT组织名称", "string", True),
        Property("malicious_family_type", "恶意家族类型", "string", True),
        Property("malicious_family_name", "恶意家族名称", "string", True)
    ])
    SENDS_EMAIL = (15, "write_mail", "发送邮件", VertexType.EMAIL, VertexType.MAIL, [])
    RECEIVES_EMAIL = (16, "mail_to", "接收邮件", VertexType.MAIL, VertexType.EMAIL, [])
    CONTAINS_ATTACHMENT = (17, "include_file", "包含附件", VertexType.MAIL, VertexType.ATTACH_FILE, [
        Property("attach_name", "附件名称", "string", True),
        Property("attach_md5", "附件MD5", "string", True)
    ])
    IP_RELATED_TO_LABEL = (18, "has_label", "关联标签", VertexType.IP, VertexType.LABEL, [])
    CLIENT_USE_CERT = (19, "client_use_cert", "客户端使用证书", VertexType.IP, VertexType.CERT, [])
    RELATED_TO_ISSUER = (20, "cert_issuer_to", "证书颁发者归属", VertexType.CERT, VertexType.ORG, [])
    RELATED_TO_SUBJECT = (21, "cert_subject_to", "证书使用者归属", VertexType.CERT, VertexType.ORG, [])

    def __init__(self, code, edge_name, desc, source, destination, properties):
        self.code = code
        self.edge_name = edge_name
        self.desc = desc
        self.source = source
        self.destination = destination
        self.properties = properties

    @classmethod
    def from_code(cls, code):
        for edge_type in cls:
            if edge_type.code == code:
                return edge_type
        raise ValueError(f"Invalid EdgeType code: {code}")

    @classmethod
    def from_name(cls, name):
        for edge_type in cls:
            if edge_type.edge_name == name:
                return edge_type
        raise ValueError(f"Invalid EdgeType name: {name}")
