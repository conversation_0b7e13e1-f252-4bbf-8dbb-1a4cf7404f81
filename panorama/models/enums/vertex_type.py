# panorama/models/enums/vertex_type.py

from enum import Enum
from typing import List, NamedTuple


class Property(NamedTuple):
    name: str
    desc: str
    type: str
    required: bool


class VertexType(Enum):
    IP = (1, "IP", "IP地址", [
        Property("ip_addr", "IP地址", "string", True),
        Property("version", "IP版本", "string", True),
        Property("city", "IP归属城市", "string", True),
        Property("country", "IP归属国家", "string", True),
        Property("latitude", "地址纬度", "double", False),
        Property("longitude", "地址经度", "double", False),
        Property("ISP", "互联网服务提供商", "string", False),
        Property("AS_ZZY", "自治域", "string", False)
    ])
    DOMAIN = (2, "DOMAIN", "域名", [Property("domain_addr", "域名地址", "string", True)])
    CERT = (3, "CERT", "证书", [
        Property("finger_print", "证书指纹", "string", True),
        Property("serial_number", "证书序列号", "string", True),
        Property("algorithm_id", "证书签名算法ID", "string", False),
        Property("issuer_cn", "颁发者通用名称", "string", True),
        Property("issuer_on", "颁发者组织名称", "string", True),
        Property("subject_cn", "使用者通用名称", "string", True),
        Property("subject_on", "使用者组织名称", "string", True),
        Property("version", "证书版本号", "int", False),
        Property("not_before", "证书有效期开始时间", "long", False),
        Property("not_after", "证书有效期结束时间", "long", False)
    ])
    ORG = (4, "ORG", "组织机构", [Property("org_name", "组织机构名称", "string", True)])
    UA = (5, "UA", "用户代理", [Property("ua_str", "User-Agent字符串", "string", False)])
    DEVICE = (6, "DEVICE", "操作设备", [])
    OS = (7, "OS", "操作系统", [])
    APT_GROUP = (8, "APT_GROUP", "APT组织", [
        Property("apt_name", "APT组织名称", "string", True),
        Property("apt_desc", "APT组织描述", "string", True),
        Property("apt_country", "APT组织所属国家", "string", True),
        Property("apt_method", "APT组织攻击手段", "string", True)
    ])
    ATTACH_FILE = (9, "ATTACH_FILE", "附带文件", [
        Property("file_name", "文件名称", "string", True),
        Property("file_type", "文件类型", "string", True),
        Property("file_size", "文件大小（字节数）", "string", False),
        Property("first_md5", "文件MD5", "string", True),
        Property("malicious_family", "恶意家族名称", "string", True),
        Property("campaign", "APT组织名称", "string", True)
    ])
    EMAIL = (10, "EMAIL", "邮箱", [
        Property("email_addr", "邮箱地址", "string", True),
        Property("user_name", "用户名", "string", True)
    ])
    MAIL = (11, "MAIL", "邮件", [
        Property("mail_id", "邮件ID", "string", False),
        Property("subject", "主题", "string", True)
    ])
    MALICIOUS_FAMILY = (12, "MALICIOUS_FAMILY", "恶意家族信息", [
        Property("malicious_family", "恶意家族名称", "string", True),
        Property("risk", "风险等级", "string", True),
        Property("description", "恶意软件描述", "string", False),
        Property("reference", "参考链接", "string", False)
    ])
    LABEL = (13, "LABEL", "标签", [
        Property("ioc_tag", "威胁标签", "string", True),
        Property("ioa_tag", "ATT&标签", "string", True),
        Property("iob_tag", "异常标签", "string", True),
        Property("user_tag", "用户标签", "string", True)
    ])

    def __init__(self, code: int, enum_name: str, desc: str, properties: List[Property]):
        self._code = code
        self._enum_name = enum_name
        self._desc = desc
        self._properties = properties

    @property
    def code(self):
        return self._code

    @property
    def enum_name(self):
        return self._enum_name

    @property
    def desc(self):
        return self._desc

    @property
    def properties(self):
        return self._properties

    @classmethod
    def from_code(cls, code: int):
        for vertex_type in cls:
            if vertex_type.code == code:
                return vertex_type
        raise ValueError(f"Invalid VertexType code: {code}")

    @classmethod
    def from_name(cls, name: str):
        for vertex_type in cls:
            if vertex_type.enum_name == name:
                return vertex_type
        raise ValueError(f"Invalid VertexType name: {name}")


# 辅助函数
def get_all_vertex_types():
    return list(VertexType)


def get_vertex_type_names():
    return [vt.enum_name for vt in VertexType]


def get_vertex_type_codes():
    return [vt.code for vt in VertexType]
