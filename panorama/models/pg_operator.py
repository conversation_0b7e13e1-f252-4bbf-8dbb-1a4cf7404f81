import json
import uuid
from datetime import datetime
import psycopg2
from configparser import ConfigParser

PG_CONFIG = {}


def initialize_pgdb_connection(filename='./env_config.ini', section='postgresql'):
    # create a parser
    parser = ConfigParser()
    # read config file
    parser.read(filename)

    # get section, default to postgresql
    if parser.has_section(section):
        params = parser.items(section)
        for param in params:
            PG_CONFIG[param[0]] = param[1]
    else:
        raise Exception('Section {0} not found in the {1} file'.format(section, filename))


# 初始化链接使用
initialize_pgdb_connection()


# 创建单条事件结果
def create_attack_event(event_graph, run_id):
    with psycopg2.connect(**PG_CONFIG) as conn:
        with conn.cursor() as cur:
            insert_attack_event_sql = ("INSERT INTO attack_event "
                                       "(id, event_graph, run_id, start_time, end_time, create_time) "
                                       "VALUES (%s, %s::jsonb, %s, %s, %s, %s)")

            min_attack_time = None
            max_attack_time = None
            edges = event_graph['edge']
            for edge in edges:
                e = edge.get_edge_info()
                if "attack_time" not in e:
                    continue
                attack_time_str = e['attack_time']
                try:
                    attack_time = datetime.fromtimestamp(float(attack_time_str))
                    if min_attack_time is None or attack_time < min_attack_time:
                        min_attack_time = attack_time
                    if max_attack_time is None or attack_time > max_attack_time:
                        max_attack_time = attack_time
                except (ValueError, TypeError, OverflowError):
                    print(f"Warning: Invalid attack_time value: {attack_time_str}")

            # 将 event_graph 转换为 JSON 字符串
            event_graph_json = json.dumps(event_graph, default=custom_json_serializer)
            event_graph_json = event_graph_json.replace("from_", "from")
            cur.execute(insert_attack_event_sql,
                        (str(uuid.uuid4()),
                         event_graph_json,
                         run_id,
                         min_attack_time,
                         max_attack_time,
                         datetime.now()))
            conn.commit()


# 创建单次模型执行结果集合
def updateModelRun(run_id, alert_num, victim_num, vendor_info_list, start_time, end_time):
    with psycopg2.connect(**PG_CONFIG) as conn:
        with conn.cursor() as cur:
            # 将 Unix 时间戳转换为 datetime 对象
            start_datetime = datetime.fromtimestamp(start_time)
            end_datetime = datetime.fromtimestamp(end_time)

            # 更新 model_run 表
            update_model_run_sql = ("UPDATE model_run SET "
                                    "alarm_count = %s, unique_victim_count = %s, source_distribution = %s::jsonb, "
                                    "earliest_event_time = %s, latest_event_time = %s, create_time = %s,status = 1 "
                                    "WHERE id = %s")

            cur.execute(update_model_run_sql,
                        (alert_num, victim_num, json.dumps(vendor_info_list),
                         start_datetime, end_datetime, datetime.now(), run_id))
            conn.commit()


# 通过模型ID获取当前模型的脚本名称
def get_preset_model_script_path(model_id):
    with psycopg2.connect(**PG_CONFIG) as conn:
        with conn.cursor() as cur:
            sql = "SELECT script_name from preset_attack_model where id = %s"
            cur.execute(sql, (model_id,))
            conn.commit()
            result = cur.fetchone()
            if result:
                # 获取元组中的第一个（也是唯一的）元素
                return result[0] if isinstance(result[0], str) else str(result[0])
            else:
                return ""
def custom_json_serializer(obj):
    """自定义 JSON 序列化器，处理不能直接转换为 JSON 的对象"""
    if hasattr(obj, '__dict__'):
        return obj.__dict__
    elif isinstance(obj, datetime):
        return obj.isoformat()
    else:
        return str(obj)
