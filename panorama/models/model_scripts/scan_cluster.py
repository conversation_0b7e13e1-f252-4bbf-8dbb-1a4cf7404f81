
import time

def add_to_dictlist(d,key,value):
    if key not in d:
        d[key] = set()
    d[key].add(value)

def cluster_scan_attack(connection_pool,space_name,max_offset=3000):
    final_querys = []

    with connection_pool.session_context('root', 'nebula') as session:
        session.execute(f'USE {space_name}')

        offset = 0
        pagesize = 1000

        ts = time.time()        
        org_map = {}
        while True:
            t0 = time.time()
            print("当前模型执行时间:", t0)
            print("=" * 80)            
            first_query = '''
                MATCH  (a:IP)-[e:make_attack_to]-(v:IP)-[e1:ip_belong_to_org]-(o:ORG)
                WHERE e.attack_type_msg == \'端口扫描\'
                RETURN a.IP.ip_addr,id(o) skip %d limit %d;
            ''' %(offset, pagesize)
            rs = session.execute(first_query)
            for r in rs.rows():
                org = r.values[1].get_sVal().decode("utf-8")
                ip = r.values[0].get_sVal().decode("utf-8")
                add_to_dictlist(org_map,org,ip)
            # 遍历 domain 与 client
            offset += pagesize
            print(first_query)
            print(f"load: {len(rs.rows())}")            

            if len(rs.rows())==0 or offset>=max_offset:
                break
        check_cluster_algo(final_querys,org_map)
    return final_querys

def check_cluster_algo(final_querys,org_map):
    # todo add node2vec model here
    for org,ips in org_map.items():
        final_query = f"""
        MATCH  (a:IP)-[e:make_attack_to]-(v:IP)-[e1:ip_belong_to_org]-(o:ORG)
        where id(o)=="{org}" and e.attack_type_msg == \'端口扫描\'
        RETURN DISTINCT a,v,o,e,e1"""
        final_querys.append(final_query)
    return final_querys
            
def set_payload_clustrer_info(result):    
    # 受害者IP信息
    victim_ip_list = []
    # 当前事件中所有攻击IP信息
    alert_list = []
    # 当前事件中所有的传感器信息
    vendor_map = {}

    for r in result.rows():
        vip = r.values[1].get_vVal().tags[0].props[b'ip_addr'].get_sVal().decode("utf-8")
        aip = r.values[0].get_vVal().tags[0].props[b'ip_addr'].get_sVal().decode("utf-8")
        victim_ip_list.append(vip)
        alert_list.append(aip)
        # todo add vendor_map, make_attack_to lost from vip to aip
    return victim_ip_list, alert_list, vendor_map


from model_scripts.base_model import initialize_nebula_connection,SPACE_NAME,finish_events
import sys

def main():
    args = sys.argv[1:]
    run_id = args[0]
    model_params_json = args[1]

    # 初始化Nebula连接池
    nebula_pool = initialize_nebula_connection()
    try:
        # 执行Nebula查询
        final_querys = cluster_scan_attack(nebula_pool,SPACE_NAME)
        finish_events(nebula_pool, run_id, final_querys, set_payload_clustrer_info)

    finally:
        # 关闭连接
        nebula_pool.close()


if __name__ == "__main__":
    main()
