import json
import sys
import time
import pg_operator

import attack_event_handler
from configparser import ConfigParser

from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config

# 全局变量
SPACE_NAME = "panorama_analysis_graph"
PRESET_MODEL_ID = "cd69a5b1-0423-4bd3-85da-498e80b300f0"


def initialize_nebula_connection(filename='./env_config.ini', section='nebula'):
    # create a parser
    parser = ConfigParser()
    # read config file
    parser.read(filename)

    # get section,default to nebula
    graph_config = {}
    if parser.has_section(section):
        params = parser.items(section)
        for param in params:
            graph_config[param[0]] = param[1]
    else:
        raise Exception('Section {0} not found in the {1} file'.format(section, filename))

    config = Config()
    config.max_connection_pool_size = 30
    connection_pool = ConnectionPool()
    ok = connection_pool.init([(graph_config['host'], 9669)], config)
    if not ok:
        raise Exception("Failed to initialize Nebula connection pool")
    return connection_pool


# 执行当前预置模型图空间查询语句
def execute_nebula_queries(connection_pool, run_id, model_params,max_offset=3000):
    # 解析模型参数
    param = model_params[0]
    attack_type_msg = param['value']

    # 受害者IP信息
    victim_ip_list = []
    # 当前事件中所有攻击IP信息
    alert_list = []
    # 当前事件中所有的传感器信息
    vendor_map = {}
    # 本次模型执行的最大最小时间
    start_time = int()
    end_time = int()

    with connection_pool.session_context('root', 'nebula') as session:
        session.execute(f'USE {SPACE_NAME}')
        offset = 0
        pagesize = 1000

        ts = time.time()
        final_querys = set()
        while True:
            t0 = time.time()
            print("当前模型执行时间:", t0)
            print("=" * 80)
            first_query =  """MATCH (aip:IP)-[e0:make_attack_to]->(vip1:IP)-[e1:make_attack_to]->(vip2:IP)
                              WHERE e0.attack_type_msg in [\'弱口令\',\'webshell上传\']
                              AND e1.attack_type_msg in [\'端口扫描\',\'网络扫描\']
                              RETURN DISTINCT e0.attack_type_msg,e1.attack_type_msg,aip.IP.ip_addr,vip1.IP.ip_addr,vip2.IP.ip_addr 
                              skip %d limit %d;"""% ( offset, pagesize)
            # debug WHEN lost test data  todo 
            first_query =  """MATCH (aip:IP)-[e0:make_attack_to]->(vip1:IP)-[e1:make_attack_to]->(vip2:IP)
                              RETURN DISTINCT e0.attack_type_msg,e1.attack_type_msg,aip.IP.ip_addr,vip1.IP.ip_addr,vip2.IP.ip_addr 
                              skip %d limit %d;"""% ( offset, pagesize)
            print("预查询语句NGQL: ", first_query)
            rs = session.execute(first_query)
            for r in rs.rows():
                aip = r.values[3].get_sVal().decode("utf-8")
                final_query = (
                                  'MATCH (aip:IP{ip_addr:"%s"})-[e0:make_attack_to]->(vip1:IP)-[e1:make_attack_to]->(vip2:IP) '
                                  'RETURN DISTINCT aip,vip1,vip2,e0,e1') % aip
                final_querys.add(final_query)
            offset += pagesize
            if offset>=max_offset:                
                break

        # 此处执行多个查询语句 每一个语句生成单次的模型查询结果attack_event
        if len(final_querys) == 0 :
            print("当前事件没有可执行的查询语句,模型执行完毕")
            pg_operator.updateModelRun(run_id, 0, 0, [], 0, 0)
            sys.exit(0)
        for final_query in list(final_querys)[:23]: # todo
            print("=" * 80)
            print("当前事件查询语句NGQL:", final_query)
            result = session.execute(final_query)
            # 进入事件处理逻辑(传入受害者IP列表，告警列表，传感器列表)
            start_time, end_time = attack_event_handler.createAttackEvent(result, run_id,
                                                                          victim_ip_list, alert_list, vendor_map,
                                                                          start_time,
                                                                          end_time)
        # 创建模型执行结果
        vendor_info_list = []
        for key, value in vendor_map.items():
            item = {
                "key": key,
                "cnt": value
            }
            vendor_info_list.append(item)
        # 模型事件处理完毕后，更新模型执行结果
        pg_operator.updateModelRun(run_id, alert_list.__len__(), victim_ip_list.__len__(), vendor_info_list, start_time,
                                  end_time)


def main():
    args = sys.argv[1:]
    run_id = args[0]
    model_params_json = args[1]

    # 初始化Nebula连接池
    nebula_pool = initialize_nebula_connection()
    print(model_params_json)
    try:
        # 执行Nebula查询
        execute_nebula_queries(nebula_pool, run_id, json.loads(model_params_json))
    finally:
        # 关闭连接
        nebula_pool.close()


if __name__ == "__main__":
    main()
