
import time

def add_to_dictlist(d,key,value):
    if key not in d:
        d[key] = set()
    d[key].add(value)

# 基于域名关联的APT拓线
def cluster_payload_attack(connection_pool,space_name,max_offset=3000):
    final_querys = []

    with connection_pool.session_context('root', 'nebula') as session:
        session.execute(f'USE {space_name}')

        offset = 0
        pagesize = 1000

        ts = time.time()        
        ua_map = {}
        while True:
            t0 = time.time()
            print("当前模型执行时间:", t0)
            print("=" * 80)            
            first_query = '''
                MATCH  (a:IP)-[e:client_use_ua]-(v:UA) 
                RETURN a.IP.ip_addr,id(v) skip %d limit %d;
            ''' %(offset, pagesize)
            rs = session.execute(first_query)
            for r in rs.rows():
                ua = r.values[1].get_sVal().decode("utf-8")
                ip = r.values[0].get_sVal().decode("utf-8")
                add_to_dictlist(ua_map,ua,ip)
            # 遍历 domain 与 client
            offset += pagesize
            print(first_query)
            print(f"load: {len(rs.rows())}")            

            if len(rs.rows())==0 or offset>=max_offset:
                break
        check_cluster_algo(final_querys,ua_map)
    return final_querys

def check_cluster_algo(final_querys,ua_map):
    # todo add node2vec model here
    for ua,ips in ua_map.items():
        if len(ips) > 1 and len(ips)<100:
            final_query = f"""
            MATCH (u:UA)-[e:client_use_ua]-(a:IP)-[e2:make_attack_to]->(v:IP)
            where id(u)=="{ua}"
            RETURN DISTINCT u,v,a,e,e2"""
            final_querys.append(final_query)
    return final_querys
            
def set_payload_clustrer_info(result):    
    # 受害者IP信息
    victim_ip_list = []
    # 当前事件中所有攻击IP信息
    alert_list = []
    # 当前事件中所有的传感器信息
    vendor_map = {}

    for r in result.rows():
        vip = r.values[1].get_vVal().tags[0].props[b'ip_addr'].get_sVal().decode("utf-8")
        aip = r.values[2].get_vVal().tags[0].props[b'ip_addr'].get_sVal().decode("utf-8")
        victim_ip_list.append(vip)
        alert_list.append(aip)
        # todo add vendor_map, make_attack_to lost from vip to aip
    return victim_ip_list, alert_list, vendor_map


from model_scripts.base_model import initialize_nebula_connection,SPACE_NAME,finish_events
import sys

def main():
    args = sys.argv[1:]
    run_id = args[0]
    model_params_json = args[1]

    # 初始化Nebula连接池
    nebula_pool = initialize_nebula_connection()
    try:
        # 执行Nebula查询
        final_querys = cluster_payload_attack(nebula_pool,SPACE_NAME)
        finish_events(nebula_pool, run_id, final_querys, set_payload_clustrer_info)

    finally:
        # 关闭连接
        nebula_pool.close()


if __name__ == "__main__":
    main()
