# nebula_sql:

# create tag index if not exists type_of_attack on ATTACK(attack_type_msg)
# rebuild tag index type_of_attack

import json
import sys
import time
import pg_operator

import attack_event_handler
from configparser import ConfigParser

from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config

model_map = {
    "816d3a18-92bc-4a14-a9ba-004cd6afded2": "模糊图结构攻击聚合",
    "37097f0b-555e-41e5-8bbb-e7d5edc7b9f4": "隧道穿越聚合",
    "227d05eb-87e8-4ced-b899-11a940ed92c4": "远控穿透聚合模型",
    "118a2c27-47c2-48fd-b4de-be33362d864d": "扫描打点攻击聚合",
    "cd69a5b1-0423-4bd3-85da-498e80b300f0": "攻击意图聚合模型",
    "a68f6798-39d1-4f08-af87-9e0d06fbc768": "基于攻击负载的相似性聚合",
    "9942a700-0c87-48b3-9dae-7ed58b1e7d4b": "资产风险聚合模型",
    "d30b33cf-7697-4c97-bc0b-67a7d43c90e4": "基于域名关联的APT拓线",
    "98676646-489a-40fb-9380-bb1fac4deba2": "基于通信关系的线索拓展模型",
    "43cb706d-c5b6-4acf-9a4b-f87d9a0aa8fe": "侦查攻击聚合",
}


# 扫描打点攻击聚合模型
def cluster_scan_attack():
    sql = '''
    MATCH (v:ATTACK{attack_type_msg:"端口扫描"})
    -[e1:make_attack_to]->(vip:IP)
    -[e2:ip_belong_to_org]->(o:ORG)   
    RETURN v  limit 10;
    '''


SPACE_NAME = "panorama_analysis_graph"


def initialize_nebula_connection(filename='./env_config.ini', section='nebula'):
    # create a parser
    parser = ConfigParser()
    # read config file
    parser.read(filename)

    # get section,default to nebula
    graph_config = {}
    if parser.has_section(section):
        params = parser.items(section)
        for param in params:
            graph_config[param[0]] = param[1]
    else:
        raise Exception('Section {0} not found in the {1} file'.format(section, filename))

    config = Config()
    config.max_connection_pool_size = 30
    connection_pool = ConnectionPool()
    ok = connection_pool.init([(graph_config['host'], 9669)], config)
    if not ok:
        raise Exception("Failed to initialize Nebula connection pool")
    return connection_pool


def finish_events(connection_pool, run_id, final_querys, set_alarm_info):
    with connection_pool.session_context('root', 'nebula') as session:
        session.execute(f'USE {SPACE_NAME}')
        if len(final_querys) == 0:
            print("当前事件没有可执行的查询语句,模型执行完毕")
            pg_operator.updateModelRun(run_id, 0, 0, [], 0, 0)
        for final_query in final_querys:
            print("=" * 80)
            print("当前事件查询语句NGQL:", final_query)
            result = session.execute(final_query)
            victim_ip_list, alert_list, vendor_map = set_alarm_info(result)
            start_time = int()
            end_time = int()
            # # 进入事件处理逻辑(传入受害者IP列表，告警列表，传感器列表)
            start_time, end_time = attack_event_handler.createAttackEvent(
                result, run_id, victim_ip_list, alert_list, vendor_map, start_time, end_time)

            # 创建模型执行结果
            vendor_info_list = []
            for key, value in vendor_map.items():
                item = {
                    "key": key,
                    "cnt": value
                }
                vendor_info_list.append(item)
            # 模型事件处理完毕后，更新模型执行结果
            pg_operator.updateModelRun(run_id, alert_list.__len__(), victim_ip_list.__len__(), vendor_info_list,
                                       start_time,
                                       end_time)
