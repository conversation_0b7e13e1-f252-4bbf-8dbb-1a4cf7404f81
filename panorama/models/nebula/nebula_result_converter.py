from typing import Dict, Any


class NebulaResultConverter:
    @staticmethod
    def get_label(vertex_type: str, props: Dict[str, Any]) -> str:
        extractors = {
            "IP": lambda p: p.get("ip_addr", ""),
            "DOMAIN": lambda p: p.get("domain_addr", ""),
            "ORG": lambda p: p.get("org_name", ""),
            "UA": lambda p: p.get("ua_str", ""),
            "OS": lambda p: str(p.get("id", "")),
            "DEVICE": lambda p: str(p.get("id", "")),
            "APT_GROUP": lambda p: p.get("apt_name", ""),
            "ATTACK": lambda p: p.get("attack_type_msg", ""),
            "MAIL": lambda p: p.get("subject", ""),
            "EMAIL": lambda p: p.get("email_addr", ""),
            "ATTACH_FILE": lambda p: p.get("file_name", ""),
            "MALICIOUS_FAMILY": lambda p: p.get("malicious_family", ""),
        }

        extractor = extractors.get(vertex_type)
        if extractor:
            return extractor(props)
        return ""