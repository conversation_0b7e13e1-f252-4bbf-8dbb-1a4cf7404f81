# Panorama 全景分析平台

Panorama是一个安全威胁分析平台，用于处理、分析和可视化安全告警数据，支持图数据库关联分析和攻击模型检测。

## 项目架构

项目由以下主要模块组成：

### 后端模块 (panorama-backend)

- **launch**: 应用程序启动模块，包含主应用入口和配置
- **common**: 通用工具类和常量定义
- **web**: Web接口和控制器
- **core**: 核心业务逻辑和服务实现
- **ngbatis**: Nebula图数据库ORM映射模块

### ETL模块 (panorama-etl)

- **panorama-etl-fake-filter**: 虚警研判过滤ETL应用
- **panorama-etl-sink-nebula**: 告警日志图数据库实体&关系抽取ETL应用

### 前端模块 (frontend)

基于Vue.js的前端应用，提供用户界面和交互功能。

### 模型模块 (models)

包含预置攻击模型脚本和图数据库操作工具。

## 技术栈

- **后端**: Spring Boot, MyBatis-Plus, Kafka, Quartz
- **数据库**: PostgreSQL, Nebula Graph (图数据库)
- **ETL**: Apache Flink
- **前端**: Vue.js, Element UI
- **消息队列**: Kafka

## 环境要求

- JDK 1.8+
- Maven 3.6+
- Node.js 12+
- PostgreSQL 12+
- Nebula Graph 3.x
- Apache Flink 1.14+
- Kafka 2.x+

## 构建与部署

### 后端构建

```bash
# 在项目根目录下执行
mvn clean package -DskipTests
```

### 前端构建

```bash
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 开发环境构建
npm run dev

# 生产环境构建
npm run prod
```

## 启动方式

### SpringBoot后端启动

使用外部配置文件启动SpringBoot应用：

```bash
# 使用外部配置文件启动
java -jar panorama-backend/launch/target/PanoramaAnalysisApplication.jar --spring.config.location=file:/path/to/external/application.yml
```

详细的SpringBoot启动指南请参考 [SPRINGBOOT_STARTUP.md](SPRINGBOOT_STARTUP.md)。

### ETL应用启动

#### 虚警研判过滤ETL应用

```bash
flink run -c com.geeksec.task.PanoramaFakeAlertFilterTask panorama-etl-fake-filter-1.0.0-SNAPSHOT.jar --config_path ./config.properties
```

#### 告警日志图数据库实体&关系抽取ETL应用

```bash
flink run -c com.geeksec.task.PanoramaAlertKafka2Nebula panorama-etl-sink-nebula-1.0.0-SNAPSHOT.jar --config_path ./config.properties
```

### 前端部署

```bash
# 1. 打包项目
npm run prod

# 2. 将打包好的文件放到服务器上
# 例如: 
cp -r dist/ /opt/GeekSec/panorama/web/

# 3. 配置Nginx
# 修改Nginx配置文件中的root目录指向前端构建文件
# 例如: /usr/local/nginx/conf/nginx.conf

# 4. 重启Nginx
/usr/local/nginx/sbin/nginx -s reload
```

## 配置文件说明

### 后端配置文件

主要配置文件位于`panorama-backend/launch/src/main/resources/config/`目录下：

- **application.yml**: 应用主配置文件
- **config.properties**: 环境变量配置文件

### 外部配置文件示例

创建外部配置文件`application.yml`：

```yaml
# 应用服务基础配置
server:
  address: 0.0.0.0
  port: ${SERVER_PORT}
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# 日志配置
logging:
  config: classpath:config/log4j2.xml
  level:
    org.nebula.contrib: DEBUG

spring:
  config:
    import: optional:./config.properties
  # 数据库配置
  datasource:
    url: ${PG_DB_URL}
    username: ${PG_DB_USER}
    password: ${PG_DB_PASSWORD}
    driver-class-name: org.postgresql.Driver
  # Kafka配置
  kafka:
    bootstrap-servers: ${KAFKA_BROKERS}
  # 其他配置...

# Nebula图数据库配置
nebula:
  ngbatis:
    use-session-pool: true
  hosts: ${NEBULA_ADDRESS}
  username: ${NEBULA_USER}
  password: ${NEBULA_PASSWORD}
  space: ${NEBULA_SPACE_NAME}
  # 其他配置...
```

创建外部配置文件`config.properties`：

```properties
# 应用访问端口
SERVER_PORT=10009

# PostgreSQL数据库连接信息
PG_DB_URL=*********************************************************************
PG_DB_USER=your-username
PG_DB_PASSWORD=your-password

# Kakfa配置
KAFKA_BROKERS=your-kafka-host:9092

# Nebula配置
NEBULA_ADDRESS=your-nebula-host:9669
NEBULA_USER=root
NEBULA_PASSWORD=nebula
NEBULA_SPACE_NAME=your-space-name

# 其他配置...
```

## 功能模块

- 告警数据处理与分析
- 图数据库关联分析
- 攻击模型检测
- 威胁情报分析
- 可视化展示

### 预置攻击模型

系统内置了多种攻击模型，包括：

- 模糊图结构攻击聚合
- 隧道穿越聚合
- 远控穿透聚合模型
- 扫描打点攻击聚合
- 攻击意图聚合模型
- 基于攻击负载的相似性聚合
- 资产风险聚合模型
- 基于域名关联的APT拓线
- 基于通信关系的线索拓展模型
- 侦查攻击聚合

## 系统架构图

```
+------------------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|  安全设备/传感器  +--->+  Kafka消息队列   +--->+  ETL处理模块     |
|                  |    |                  |    |                  |
+------------------+    +------------------+    +--------+---------+
                                                         |
                                                         v
+------------------+    +------------------+    +------------------+
|                  |    |                  |    |                  |
|  前端展示        +<---+  后端API服务     +<---+  Nebula图数据库  |
|                  |    |                  |    |                  |
+------------------+    +------------------+    +--------+---------+
                                                         |
                                                         v
                                               +------------------+
                                               |                  |
                                               |  PostgreSQL数据库|
                                               |                  |
                                               +------------------+
```

## 开发指南

### 添加新的攻击模型

1. 在`models/model_scripts`目录下创建新的模型脚本
2. 在数据库中注册模型信息
3. 实现模型的NGQL查询逻辑

### 扩展ETL功能

1. 在相应的ETL模块中添加新的处理函数
2. 注册新的数据流处理逻辑
3. 更新配置文件

## 常见问题

### 如何修改应用端口？

在外部配置文件`config.properties`中修改`SERVER_PORT`参数。

### 如何连接不同的数据库？

在外部配置文件`config.properties`中修改相应的数据库连接参数。

### 如何添加新的预置模型？

1. 在`models/model_scripts`目录下创建新的模型脚本
2. 在`models/model_scripts/base_model.py`中注册模型ID和名称
3. 实现模型的查询逻辑

## 许可证

版权所有 © GeekSec
